lockfileVersion: 5.3

specifiers:
  '@babel/generator': ^7.22.10
  '@babel/parser': ^7.22.10
  '@babel/plugin-proposal-nullish-coalescing-operator': ^7.16.7
  '@babel/plugin-proposal-optional-chaining': ^7.16.7
  '@babel/preset-env': ^7.18.9
  '@babel/traverse': ^7.22.10
  '@chenfengyuan/vue-qrcode': ^1.0.2
  '@emmetio/codemirror-plugin': ^1.2.4
  '@haizhi/monitor': ^1.0.1
  '@riophae/vue-treeselect': ^0.4.0
  '@tinymce/tinymce-vue': ^3.2.8
  '@vue/babel-helper-vue-jsx-merge-props': ^1.4.0
  '@vue/babel-preset-jsx': ^1.4.0
  '@vue/cli-plugin-babel': ~4.5.0
  '@vue/cli-plugin-eslint': ~4.5.0
  '@vue/cli-plugin-router': ~4.5.0
  '@vue/cli-plugin-vuex': ~4.5.0
  '@vue/cli-service': ~4.5.0
  '@vue/eslint-config-standard': ^5.1.2
  ace: ^1.3.0
  animate.css: ^4.1.1
  axios: ^0.21.0
  babel-eslint: ^10.1.0
  babel-loader: ^8.2.3
  babel-plugin-component: ^1.1.1
  babel-polyfill: ^6.26.0
  babel-preset-es2015: ^6.24.1
  brace: ^0.11.1
  codemirror: ^5.39.2
  compression-webpack-plugin: ^5.0.0
  core-js: ^3.6.5
  cropperjs: ^1.5.12
  crypto-js: ^4.1.1
  csslint: ^1.0.5
  echarts: ^4.8.0
  element-ui: ^2.15.3
  eslint: ^6.7.2
  eslint-plugin-import: ^2.20.2
  eslint-plugin-node: ^11.1.0
  eslint-plugin-promise: ^4.2.1
  eslint-plugin-standard: ^4.0.0
  eslint-plugin-vue: ^6.2.2
  fastclick: ^1.0.6
  fingerprintjs2: ^2.1.4
  html2canvas: ^1.0.0-rc.7
  htmlhint: ^0.14.2
  hz-message: ^1.2.0
  hz-product-list: ^1.1.10
  hz-quark: ^2.0.6
  hz-user: ^1.1.7
  js-audio-recorder: ^1.0.7
  js-base64: ^3.6.0
  jshint: ^2.13.4
  jsoneditor: ^9.4.0
  jsonlint: ^1.6.3
  keymaster: ^1.6.2
  lint-staged: ^9.5.0
  lodash: ^4.17.20
  mitt: ^2.1.0
  mobile-drag-drop: ^2.2.0
  particles.vue: ^2.16.3
  pinyin-match: ^1.2.2
  qrcode: ^1.4.4
  qs: ^6.9.4
  sass: ^1.26.5
  sass-loader: ^8.0.2
  script-loader: ^0.7.2
  socket.io-client: ^2.1.1
  svg-sprite-loader: ^5.2.1
  swiper: 5.x
  tinymce: ^5.8.1
  uuid: ^8.3.1
  v-contextmenu: ^2.9.0
  v-region: 2.2.2
  vant: ^2.12.48
  vue: ^2.6.11
  vue-awesome-swiper: ^4.1.1
  vue-clipboard2: ^0.3.3
  vue-codemirror: ^4.0.6
  vue-grid-layout: ^2.3.12
  vue-lazyload: ^1.3.3
  vue-pdf: ^4.3.0
  vue-ref: ^2.0.0
  vue-router: ^3.2.0
  vue-socket.io: ^3.0.10
  vue-template-compiler: ^2.6.11
  vuedraggable: ^2.24.3
  vuex: ^3.4.0

dependencies:
  '@chenfengyuan/vue-qrcode': 1.0.2_vue@2.7.14
  '@emmetio/codemirror-plugin': 1.2.4
  '@haizhi/monitor': 1.0.1
  '@riophae/vue-treeselect': 0.4.0_vue@2.7.14
  '@tinymce/tinymce-vue': 3.2.8_vue@2.7.14
  '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
  '@vue/babel-preset-jsx': 1.4.0_vue@2.7.14
  ace: 1.3.0
  animate.css: 4.1.1
  axios: 0.21.4
  babel-polyfill: 6.26.0
  brace: 0.11.1
  codemirror: 5.65.14
  core-js: 3.32.0
  cropperjs: 1.5.13
  crypto-js: 4.1.1
  csslint: 1.0.5
  echarts: 4.9.0
  element-ui: 2.15.13_vue@2.7.14
  fastclick: 1.0.6
  fingerprintjs2: 2.1.4
  html2canvas: 1.4.1
  htmlhint: 0.14.2
  hz-message: 1.2.0
  hz-product-list: 1.1.11
  hz-quark: 2.0.8
  hz-user: 1.1.8
  js-audio-recorder: 1.0.7
  js-base64: 3.7.5
  jshint: 2.13.6
  jsoneditor: 9.10.2
  jsonlint: 1.6.3
  keymaster: 1.6.2
  lodash: 4.17.21
  mitt: 2.1.0
  mobile-drag-drop: 2.2.0
  particles.vue: 2.43.1_vue@2.7.14
  pinyin-match: 1.2.4
  qrcode: 1.5.3
  qs: 6.11.2
  socket.io-client: 2.5.0
  swiper: 5.4.5
  tinymce: 5.10.7
  uuid: 8.3.2
  v-contextmenu: 2.9.0_vue@2.7.14
  v-region: 2.2.2
  vant: 2.12.54_vue@2.7.14
  vue: 2.7.14
  vue-awesome-swiper: 4.1.1_swiper@5.4.5+vue@2.7.14
  vue-clipboard2: 0.3.3
  vue-codemirror: 4.0.6
  vue-grid-layout: 2.4.0
  vue-lazyload: 1.3.5
  vue-pdf: 4.3.0
  vue-ref: 2.0.0
  vue-router: 3.6.5
  vue-socket.io: 3.0.10
  vuedraggable: 2.24.3
  vuex: 3.6.2_vue@2.7.14

devDependencies:
  '@babel/generator': 7.22.10
  '@babel/parser': 7.22.10
  '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6
  '@babel/plugin-proposal-optional-chaining': 7.21.0
  '@babel/preset-env': 7.22.10
  '@babel/traverse': 7.22.10
  '@vue/cli-plugin-babel': 4.5.19_2dacb0cea8cfbd89239ebc716832e7d4
  '@vue/cli-plugin-eslint': 4.5.19_291eed2a4b15311c68aabdde8b2cf70f
  '@vue/cli-plugin-router': 4.5.19_@vue+cli-service@4.5.19
  '@vue/cli-plugin-vuex': 4.5.19_@vue+cli-service@4.5.19
  '@vue/cli-service': 4.5.19_8f023ad5f6aa6a67fdfe810224770a2b
  '@vue/eslint-config-standard': 5.1.2_4e9acc927bc5f54b3d304a6e7610b158
  babel-eslint: 10.1.0_eslint@6.8.0
  babel-loader: 8.3.0
  babel-plugin-component: 1.1.1
  babel-preset-es2015: 6.24.1
  compression-webpack-plugin: 5.0.2
  eslint: 6.8.0
  eslint-plugin-import: 2.28.0_eslint@6.8.0
  eslint-plugin-node: 11.1.0_eslint@6.8.0
  eslint-plugin-promise: 4.3.1
  eslint-plugin-standard: 4.1.0_eslint@6.8.0
  eslint-plugin-vue: 6.2.2_eslint@6.8.0
  lint-staged: 9.5.0
  sass: 1.65.1
  sass-loader: 8.0.2_sass@1.65.1
  script-loader: 0.7.2
  svg-sprite-loader: 5.2.1
  vue-template-compiler: 2.7.14

packages:

  /@achrinza/node-ipc/9.2.2:
    resolution: {integrity: sha1-rhtdPWqTYgNO6mDI2Ua5OJPC5Ow=}
    engines: {node: 8 || 10 || 12 || 14 || 16 || 17}
    dependencies:
      '@node-ipc/js-queue': 2.0.3
      event-pubsub: 4.3.0
      js-message: 1.0.7
    dev: true

  /@ampproject/remapping/2.2.1:
    resolution: {integrity: sha1-mejhGFESi4cCzVfDNoTx0PJgtjA=}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.19
    dev: true

  /@babel/code-frame/7.22.10:
    resolution: {integrity: sha1-HCDmErdo/vp19ukNbsuGMpJH8KM=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.22.10
      chalk: 2.4.2
    dev: true

  /@babel/compat-data/7.22.9:
    resolution: {integrity: sha1-cc2wChzjoynOTL7DpE+f7zVmlzA=}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/core/7.22.10:
    resolution: {integrity: sha1-qtRCx7zRWCJSy0V2dHrONbwSLzU=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@babel/code-frame': 7.22.10
      '@babel/generator': 7.22.10
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-module-transforms': 7.22.9_@babel+core@7.22.10
      '@babel/helpers': 7.22.10
      '@babel/parser': 7.22.10
      '@babel/template': 7.22.5
      '@babel/traverse': 7.22.10
      '@babel/types': 7.22.10
      convert-source-map: 1.9.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/generator/7.22.10:
    resolution: {integrity: sha1-ySJUNh85jhYGRaxYgxBpcHOCtyI=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.19
      jsesc: 2.5.2
    dev: true

  /@babel/helper-annotate-as-pure/7.22.5:
    resolution: {integrity: sha1-5/BnN7GX1YCgHt912X4si+mdOII=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: true

  /@babel/helper-builder-binary-assignment-operator-visitor/7.22.10:
    resolution: {integrity: sha1-Vz5zWTfpnqdeoweItX61L6t0aMk=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: true

  /@babel/helper-compilation-targets/7.22.10:
    resolution: {integrity: sha1-AdZIu8Jd2I9RPYYu4N8nt9TmcCQ=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': 7.22.9
      '@babel/helper-validator-option': 7.22.5
      browserslist: 4.21.10
      lru-cache: 5.1.1
      semver: 6.3.1
    dev: true

  /@babel/helper-create-class-features-plugin/7.22.10:
    resolution: {integrity: sha1-3SYS1Z6sRViAIaw9b6l20I9OlaM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-function-name': 7.22.5
      '@babel/helper-member-expression-to-functions': 7.22.5
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-replace-supers': 7.22.9
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      semver: 6.3.1
    dev: true

  /@babel/helper-create-class-features-plugin/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-3SYS1Z6sRViAIaw9b6l20I9OlaM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-function-name': 7.22.5
      '@babel/helper-member-expression-to-functions': 7.22.5
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-replace-supers': 7.22.9_@babel+core@7.22.10
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      semver: 6.3.1
    dev: true

  /@babel/helper-create-regexp-features-plugin/7.22.9:
    resolution: {integrity: sha1-nY5hqNk2b+ZhmPV8QFZWY94IJfY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-annotate-as-pure': 7.22.5
      regexpu-core: 5.3.2
      semver: 6.3.1
    dev: true

  /@babel/helper-create-regexp-features-plugin/7.22.9_@babel+core@7.22.10:
    resolution: {integrity: sha1-nY5hqNk2b+ZhmPV8QFZWY94IJfY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-annotate-as-pure': 7.22.5
      regexpu-core: 5.3.2
      semver: 6.3.1
    dev: true

  /@babel/helper-define-polyfill-provider/0.4.2:
    resolution: {integrity: sha1-gsglyt7u7nqtI3YY676PoXEAFdc=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      debug: 4.3.4
      lodash.debounce: 4.0.8
      resolve: 1.22.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-define-polyfill-provider/0.4.2_@babel+core@7.22.10:
    resolution: {integrity: sha1-gsglyt7u7nqtI3YY676PoXEAFdc=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      debug: 4.3.4
      lodash.debounce: 4.0.8
      resolve: 1.22.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-environment-visitor/7.22.5:
    resolution: {integrity: sha1-8G3UG3wfROH42mxAVbQas6Cafpg=}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-function-name/7.22.5:
    resolution: {integrity: sha1-7eMAgokFuxXlgsA3Fi+Z1Rg68b4=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.22.5
      '@babel/types': 7.22.10
    dev: true

  /@babel/helper-hoist-variables/7.22.5:
    resolution: {integrity: sha1-wBoAfawFwIWRTo+2UrM521DYI7s=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: true

  /@babel/helper-member-expression-to-functions/7.22.5:
    resolution: {integrity: sha1-CnxWEXytM3L7+NL7S/j41koedrI=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: true

  /@babel/helper-module-imports/7.0.0-beta.35:
    resolution: {integrity: sha1-MI41DnMXUs200PBY3x1wSSXGTgo=}
    dependencies:
      '@babel/types': 7.0.0-beta.35
      lodash: 4.17.21
    dev: true

  /@babel/helper-module-imports/7.22.5:
    resolution: {integrity: sha1-Go9Mn0An0j9SC9drNk1EQ0pyZgw=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10

  /@babel/helper-module-transforms/7.22.9:
    resolution: {integrity: sha1-kt/LH7uyvGJSkCT3LZQqjJcUISk=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-module-imports': 7.22.5
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.5
    dev: true

  /@babel/helper-module-transforms/7.22.9_@babel+core@7.22.10:
    resolution: {integrity: sha1-kt/LH7uyvGJSkCT3LZQqjJcUISk=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-module-imports': 7.22.5
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.5
    dev: true

  /@babel/helper-optimise-call-expression/7.22.5:
    resolution: {integrity: sha1-8hUxqcy/9kT90Va0B3wW/ww/YJ4=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: true

  /@babel/helper-plugin-utils/7.22.5:
    resolution: {integrity: sha1-3X7jc16KMTufewWnc9iS6I5tcpU=}
    engines: {node: '>=6.9.0'}

  /@babel/helper-remap-async-to-generator/7.22.9:
    resolution: {integrity: sha1-U6JbdITnItfvucNQx1wDLUYo3oI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-wrap-function': 7.22.10
    dev: true

  /@babel/helper-remap-async-to-generator/7.22.9_@babel+core@7.22.10:
    resolution: {integrity: sha1-U6JbdITnItfvucNQx1wDLUYo3oI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-wrap-function': 7.22.10
    dev: true

  /@babel/helper-replace-supers/7.22.9:
    resolution: {integrity: sha1-y9wn1tjRjNIsga5Ck3ZaXZr9B3k=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-member-expression-to-functions': 7.22.5
      '@babel/helper-optimise-call-expression': 7.22.5
    dev: true

  /@babel/helper-replace-supers/7.22.9_@babel+core@7.22.10:
    resolution: {integrity: sha1-y9wn1tjRjNIsga5Ck3ZaXZr9B3k=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-member-expression-to-functions': 7.22.5
      '@babel/helper-optimise-call-expression': 7.22.5
    dev: true

  /@babel/helper-simple-access/7.22.5:
    resolution: {integrity: sha1-STg1fcfXgrgO1tuwOg+6PSKx1d4=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: true

  /@babel/helper-skip-transparent-expression-wrappers/7.22.5:
    resolution: {integrity: sha1-AH8VJAtXUcU3xA53q7TonuqqiEc=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: true

  /@babel/helper-split-export-declaration/7.22.6:
    resolution: {integrity: sha1-MixhtzEMCZf+TDI5VWZ/GPzvuRw=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: true

  /@babel/helper-string-parser/7.22.5:
    resolution: {integrity: sha1-Uz82RXolgUzx32SIUjrVR9eEqZ8=}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-identifier/7.22.5:
    resolution: {integrity: sha1-lUTvajOZk0PIdA+lE1DzDuqq8ZM=}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-option/7.22.5:
    resolution: {integrity: sha1-3lIAChWhd0E8gjT6Oor07oEC0Kw=}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-wrap-function/7.22.10:
    resolution: {integrity: sha1-2EXgQ4gO0LjBi9GUoSAFyxbS9hQ=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-function-name': 7.22.5
      '@babel/template': 7.22.5
      '@babel/types': 7.22.10
    dev: true

  /@babel/helpers/7.22.10:
    resolution: {integrity: sha1-rmAFxTnfvLXNcftRv8ilK6Y7w3o=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.22.5
      '@babel/traverse': 7.22.10
      '@babel/types': 7.22.10
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/highlight/7.22.10:
    resolution: {integrity: sha1-AqP22MHLRSGy/Qqw2o9HOZNhN9c=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.22.5
      chalk: 2.4.2
      js-tokens: 4.0.0
    dev: true

  /@babel/parser/7.22.10:
    resolution: {integrity: sha1-43Y0+aEqFxYTbERiTvVCg8q9P1U=}
    engines: {node: '>=6.0.0'}
    hasBin: true

  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.22.5:
    resolution: {integrity: sha1-hyRaIc1ppzsLgbzamNRD1t8I8F4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-hyRaIc1ppzsLgbzamNRD1t8I8F4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.22.5:
    resolution: {integrity: sha1-/vCflJmx8ckw2ooMQZ20IWfXkso=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-transform-optional-chaining': 7.22.10
    dev: true

  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-/vCflJmx8ckw2ooMQZ20IWfXkso=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-transform-optional-chaining': 7.22.10_@babel+core@7.22.10
    dev: true

  /@babel/plugin-proposal-class-properties/7.18.6_@babel+core@7.22.10:
    resolution: {integrity: sha1-sRD1l0GJX37CGm//aW7EYmXERqM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-class-features-plugin': 7.22.10_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-proposal-decorators/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-1qjDqQGOGxPmZH+GnF6lb/K1hdQ=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-class-features-plugin': 7.22.10_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-replace-supers': 7.22.9_@babel+core@7.22.10
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/plugin-syntax-decorators': 7.22.10_@babel+core@7.22.10
    dev: true

  /@babel/plugin-proposal-nullish-coalescing-operator/7.18.6:
    resolution: {integrity: sha1-/dlAqZp0Dld9bHU6tvu0P9uUZ+E=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3
    dev: true

  /@babel/plugin-proposal-optional-chaining/7.21.0:
    resolution: {integrity: sha1-iG9ciXjet9MPZ4suJDRrKHI00+o=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-syntax-optional-chaining': 7.8.3
    dev: true

  /@babel/plugin-proposal-private-property-in-object/7.21.0-placeholder-for-preset-env.2:
    resolution: {integrity: sha1-eET5KJVG76n+usLeTP41igUL1wM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dev: true

  /@babel/plugin-proposal-private-property-in-object/7.21.0-placeholder-for-preset-env.2_@babel+core@7.22.10:
    resolution: {integrity: sha1-eET5KJVG76n+usLeTP41igUL1wM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
    dev: true

  /@babel/plugin-syntax-async-generators/7.8.4:
    resolution: {integrity: sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-async-generators/7.8.4_@babel+core@7.22.10:
    resolution: {integrity: sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-class-properties/7.12.13:
    resolution: {integrity: sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-class-properties/7.12.13_@babel+core@7.22.10:
    resolution: {integrity: sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-class-static-block/7.14.5:
    resolution: {integrity: sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-class-static-block/7.14.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-decorators/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-fYPqBNiTxEK3jr9MPLrFmnIR3v8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-dynamic-import/7.8.3:
    resolution: {integrity: sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-dynamic-import/7.8.3_@babel+core@7.22.10:
    resolution: {integrity: sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-export-namespace-from/7.8.3:
    resolution: {integrity: sha1-AolkqbqA28CUyRXEh618TnpmRlo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-export-namespace-from/7.8.3_@babel+core@7.22.10:
    resolution: {integrity: sha1-AolkqbqA28CUyRXEh618TnpmRlo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-import-assertions/7.22.5:
    resolution: {integrity: sha1-B9JS4qoLxhJVZ/dCzVhhnLFNzpg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-import-assertions/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-B9JS4qoLxhJVZ/dCzVhhnLFNzpg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-import-attributes/7.22.5:
    resolution: {integrity: sha1-q4QCSNg0QQuCn1afUmK55RdVXss=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-import-attributes/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-q4QCSNg0QQuCn1afUmK55RdVXss=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-import-meta/7.10.4:
    resolution: {integrity: sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-import-meta/7.10.4_@babel+core@7.22.10:
    resolution: {integrity: sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-json-strings/7.8.3:
    resolution: {integrity: sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-json-strings/7.8.3_@babel+core@7.22.10:
    resolution: {integrity: sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-jsx/7.22.5:
    resolution: {integrity: sha1-praOhPt251n8O5PpAYdv+rvh2Rg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: false

  /@babel/plugin-syntax-jsx/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-praOhPt251n8O5PpAYdv+rvh2Rg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-logical-assignment-operators/7.10.4:
    resolution: {integrity: sha1-ypHvRjA1MESLkGZSusLp/plB9pk=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-logical-assignment-operators/7.10.4_@babel+core@7.22.10:
    resolution: {integrity: sha1-ypHvRjA1MESLkGZSusLp/plB9pk=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3:
    resolution: {integrity: sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3_@babel+core@7.22.10:
    resolution: {integrity: sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-numeric-separator/7.10.4:
    resolution: {integrity: sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-numeric-separator/7.10.4_@babel+core@7.22.10:
    resolution: {integrity: sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-object-rest-spread/7.8.3:
    resolution: {integrity: sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-object-rest-spread/7.8.3_@babel+core@7.22.10:
    resolution: {integrity: sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-optional-catch-binding/7.8.3:
    resolution: {integrity: sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-optional-catch-binding/7.8.3_@babel+core@7.22.10:
    resolution: {integrity: sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-optional-chaining/7.8.3:
    resolution: {integrity: sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-optional-chaining/7.8.3_@babel+core@7.22.10:
    resolution: {integrity: sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-private-property-in-object/7.14.5:
    resolution: {integrity: sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-private-property-in-object/7.14.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-top-level-await/7.14.5:
    resolution: {integrity: sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-top-level-await/7.14.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-unicode-sets-regex/7.18.6:
    resolution: {integrity: sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-create-regexp-features-plugin': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-syntax-unicode-sets-regex/7.18.6_@babel+core@7.22.10:
    resolution: {integrity: sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-regexp-features-plugin': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-arrow-functions/7.22.5:
    resolution: {integrity: sha1-5bpWbQxYpbK6Kot5VFBkGVC3GVg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-arrow-functions/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-5bpWbQxYpbK6Kot5VFBkGVC3GVg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-async-generator-functions/7.22.10:
    resolution: {integrity: sha1-RZRs0X+RWxDmXCm47RigpQ/GSMg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-remap-async-to-generator': 7.22.9
      '@babel/plugin-syntax-async-generators': 7.8.4
    dev: true

  /@babel/plugin-transform-async-generator-functions/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-RZRs0X+RWxDmXCm47RigpQ/GSMg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-remap-async-to-generator': 7.22.9_@babel+core@7.22.10
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-async-to-generator/7.22.5:
    resolution: {integrity: sha1-x6hfRORviVL20n/lfC7TzAhMN3U=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-module-imports': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-remap-async-to-generator': 7.22.9
    dev: true

  /@babel/plugin-transform-async-to-generator/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-x6hfRORviVL20n/lfC7TzAhMN3U=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-module-imports': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-remap-async-to-generator': 7.22.9_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-block-scoped-functions/7.22.5:
    resolution: {integrity: sha1-J5eAdb+uufpYbTy2Oj0wwd5YACQ=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-block-scoped-functions/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-J5eAdb+uufpYbTy2Oj0wwd5YACQ=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-block-scoping/7.22.10:
    resolution: {integrity: sha1-iKHczDODiZ615mBTSnaiLs7mT6o=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-block-scoping/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-iKHczDODiZ615mBTSnaiLs7mT6o=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-class-properties/7.22.5:
    resolution: {integrity: sha1-l6VuMa2MncBqCzcQzngD1aSMync=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-create-class-features-plugin': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-class-properties/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-l6VuMa2MncBqCzcQzngD1aSMync=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-class-features-plugin': 7.22.10_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-class-static-block/7.22.5:
    resolution: {integrity: sha1-PkDEbwSEA0ctb0GDEW1eRrG/9bo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
    dependencies:
      '@babel/helper-create-class-features-plugin': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-class-static-block': 7.14.5
    dev: true

  /@babel/plugin-transform-class-static-block/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-PkDEbwSEA0ctb0GDEW1eRrG/9bo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-class-features-plugin': 7.22.10_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-class-static-block': 7.14.5_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-classes/7.22.6:
    resolution: {integrity: sha1-4E19gE7VuFATESk9Gg5tQ+lMM2M=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-function-name': 7.22.5
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-replace-supers': 7.22.9
      '@babel/helper-split-export-declaration': 7.22.6
      globals: 11.12.0
    dev: true

  /@babel/plugin-transform-classes/7.22.6_@babel+core@7.22.10:
    resolution: {integrity: sha1-4E19gE7VuFATESk9Gg5tQ+lMM2M=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-function-name': 7.22.5
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-replace-supers': 7.22.9_@babel+core@7.22.10
      '@babel/helper-split-export-declaration': 7.22.6
      globals: 11.12.0
    dev: true

  /@babel/plugin-transform-computed-properties/7.22.5:
    resolution: {integrity: sha1-zR6ZS/nzFr0cLa/NAgY+wmG7OGk=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/template': 7.22.5
    dev: true

  /@babel/plugin-transform-computed-properties/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-zR6ZS/nzFr0cLa/NAgY+wmG7OGk=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/template': 7.22.5
    dev: true

  /@babel/plugin-transform-destructuring/7.22.10:
    resolution: {integrity: sha1-OOInOBSljIELbDTqKTvklzxOteI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-destructuring/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-OOInOBSljIELbDTqKTvklzxOteI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-dotall-regex/7.22.5:
    resolution: {integrity: sha1-27Tw5Fdm61ROGT+wDmWh3TsqQWU=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-create-regexp-features-plugin': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-dotall-regex/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-27Tw5Fdm61ROGT+wDmWh3TsqQWU=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-regexp-features-plugin': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-duplicate-keys/7.22.5:
    resolution: {integrity: sha1-tuZCjZQW9fC7oZxw0ebn4LiKsoU=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-duplicate-keys/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-tuZCjZQW9fC7oZxw0ebn4LiKsoU=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-dynamic-import/7.22.5:
    resolution: {integrity: sha1-1pCKiRaoEEaMTt/3O1t1vaatOT4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-dynamic-import': 7.8.3
    dev: true

  /@babel/plugin-transform-dynamic-import/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-1pCKiRaoEEaMTt/3O1t1vaatOT4=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-exponentiation-operator/7.22.5:
    resolution: {integrity: sha1-QCQyrVRKH5pIDahl/aJr5lPkj2o=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-exponentiation-operator/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-QCQyrVRKH5pIDahl/aJr5lPkj2o=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-export-namespace-from/7.22.5:
    resolution: {integrity: sha1-V8QcsdBhPSL1SP3diyiO7bmXOls=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-export-namespace-from': 7.8.3
    dev: true

  /@babel/plugin-transform-export-namespace-from/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-V8QcsdBhPSL1SP3diyiO7bmXOls=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-export-namespace-from': 7.8.3_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-for-of/7.22.5:
    resolution: {integrity: sha1-qxuKIAqPmQE3r/mghPjeQJmrFz8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-for-of/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-qxuKIAqPmQE3r/mghPjeQJmrFz8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-function-name/7.22.5:
    resolution: {integrity: sha1-k1GJr2iwGJjg1tmWWNtrFkIFwUM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-function-name': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-function-name/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-k1GJr2iwGJjg1tmWWNtrFkIFwUM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-function-name': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-json-strings/7.22.5:
    resolution: {integrity: sha1-FLZDUv334fc37taN4aFGi9KnfsA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-json-strings': 7.8.3
    dev: true

  /@babel/plugin-transform-json-strings/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-FLZDUv334fc37taN4aFGi9KnfsA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-literals/7.22.5:
    resolution: {integrity: sha1-6TQfS1oWeVJXbiPbjUNYSbHdeSA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-literals/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-6TQfS1oWeVJXbiPbjUNYSbHdeSA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-logical-assignment-operators/7.22.5:
    resolution: {integrity: sha1-Zq5fBo/VqaXcVw3xb1bCqEYqnWw=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4
    dev: true

  /@babel/plugin-transform-logical-assignment-operators/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-Zq5fBo/VqaXcVw3xb1bCqEYqnWw=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-member-expression-literals/7.22.5:
    resolution: {integrity: sha1-T8yQUO3tmBpGg0fdN0U57T4Fje8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-member-expression-literals/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-T8yQUO3tmBpGg0fdN0U57T4Fje8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-modules-amd/7.22.5:
    resolution: {integrity: sha1-TgRfVdz5iv0A+FaRpo/AeAcE9SY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-module-transforms': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-modules-amd/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-TgRfVdz5iv0A+FaRpo/AeAcE9SY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-module-transforms': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-modules-commonjs/7.22.5:
    resolution: {integrity: sha1-fZh1kI0ZuMBTYIWvewU/1b1lG/o=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-module-transforms': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-simple-access': 7.22.5
    dev: true

  /@babel/plugin-transform-modules-commonjs/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-fZh1kI0ZuMBTYIWvewU/1b1lG/o=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-module-transforms': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-simple-access': 7.22.5
    dev: true

  /@babel/plugin-transform-modules-systemjs/7.22.5:
    resolution: {integrity: sha1-GMMUELXleaAJJjj5XIlsKpil1JY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-module-transforms': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-validator-identifier': 7.22.5
    dev: true

  /@babel/plugin-transform-modules-systemjs/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-GMMUELXleaAJJjj5XIlsKpil1JY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-module-transforms': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-validator-identifier': 7.22.5
    dev: true

  /@babel/plugin-transform-modules-umd/7.22.5:
    resolution: {integrity: sha1-RpSuQKh7F0Xjd1tqf+lkADFdT5g=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-module-transforms': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-modules-umd/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-RpSuQKh7F0Xjd1tqf+lkADFdT5g=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-module-transforms': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-named-capturing-groups-regex/7.22.5:
    resolution: {integrity: sha1-Z/4Y7ozgLVfIVRheJ+PclZsumR8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-create-regexp-features-plugin': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-named-capturing-groups-regex/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-Z/4Y7ozgLVfIVRheJ+PclZsumR8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-regexp-features-plugin': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-new-target/7.22.5:
    resolution: {integrity: sha1-GySKzqVM5E6gbf03JHugifz5dY0=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-new-target/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-GySKzqVM5E6gbf03JHugifz5dY0=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-nullish-coalescing-operator/7.22.5:
    resolution: {integrity: sha1-+IcsZXduC1UuCEnXWWzd1BbD44E=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3
    dev: true

  /@babel/plugin-transform-nullish-coalescing-operator/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-+IcsZXduC1UuCEnXWWzd1BbD44E=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-numeric-separator/7.22.5:
    resolution: {integrity: sha1-VyJqLtnlErm0RlF6tvotF6u4P1g=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-numeric-separator': 7.10.4
    dev: true

  /@babel/plugin-transform-numeric-separator/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-VyJqLtnlErm0RlF6tvotF6u4P1g=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-object-rest-spread/7.22.5:
    resolution: {integrity: sha1-lobcNEffR1OwsqL65+i8M83B8uE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.22.9
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-object-rest-spread': 7.8.3
      '@babel/plugin-transform-parameters': 7.22.5
    dev: true

  /@babel/plugin-transform-object-rest-spread/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-lobcNEffR1OwsqL65+i8M83B8uE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.22.9
      '@babel/core': 7.22.10
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.22.10
      '@babel/plugin-transform-parameters': 7.22.5_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-object-super/7.22.5:
    resolution: {integrity: sha1-eUqNL8tdCDWvciFzwanXBPROIYw=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-replace-supers': 7.22.9
    dev: true

  /@babel/plugin-transform-object-super/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-eUqNL8tdCDWvciFzwanXBPROIYw=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-replace-supers': 7.22.9_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-optional-catch-binding/7.22.5:
    resolution: {integrity: sha1-hCCAvjB2cDvg6vMurWrIF07e4zM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3
    dev: true

  /@babel/plugin-transform-optional-catch-binding/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-hCCAvjB2cDvg6vMurWrIF07e4zM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-optional-chaining/7.22.10:
    resolution: {integrity: sha1-B20op+B0OS6EDUrlh9g0RbrANyo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-syntax-optional-chaining': 7.8.3
    dev: true

  /@babel/plugin-transform-optional-chaining/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-B20op+B0OS6EDUrlh9g0RbrANyo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-parameters/7.22.5:
    resolution: {integrity: sha1-w1Qt08ObQsgGmTbkhxeo0XnWOhg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-parameters/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-w1Qt08ObQsgGmTbkhxeo0XnWOhg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-private-methods/7.22.5:
    resolution: {integrity: sha1-IciveR92Z0QgoUeuYumTXXkPhyI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-create-class-features-plugin': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-private-methods/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-IciveR92Z0QgoUeuYumTXXkPhyI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-class-features-plugin': 7.22.10_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-private-property-in-object/7.22.5:
    resolution: {integrity: sha1-B6d/KMuyUVRqQ9F1od2kzz74PjI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-create-class-features-plugin': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-private-property-in-object': 7.14.5
    dev: true

  /@babel/plugin-transform-private-property-in-object/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-B6d/KMuyUVRqQ9F1od2kzz74PjI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-create-class-features-plugin': 7.22.10_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-private-property-in-object': 7.14.5_@babel+core@7.22.10
    dev: true

  /@babel/plugin-transform-property-literals/7.22.5:
    resolution: {integrity: sha1-td2r1zpPfybNDiD120gpC4hzJ2Y=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-property-literals/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-td2r1zpPfybNDiD120gpC4hzJ2Y=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-regenerator/7.22.10:
    resolution: {integrity: sha1-jO7zvXN1xNt2UoeLAkGyvl0MPMo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      regenerator-transform: 0.15.2
    dev: true

  /@babel/plugin-transform-regenerator/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-jO7zvXN1xNt2UoeLAkGyvl0MPMo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      regenerator-transform: 0.15.2
    dev: true

  /@babel/plugin-transform-reserved-words/7.22.5:
    resolution: {integrity: sha1-gyzTW4HCh8S80JzgPiIZlkH5ZPs=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-reserved-words/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-gyzTW4HCh8S80JzgPiIZlkH5ZPs=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-runtime/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-ie2m2vHTr282+zaHZlUwVMjXzUY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-module-imports': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
      babel-plugin-polyfill-corejs2: 0.4.5_@babel+core@7.22.10
      babel-plugin-polyfill-corejs3: 0.8.3_@babel+core@7.22.10
      babel-plugin-polyfill-regenerator: 0.5.2_@babel+core@7.22.10
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-shorthand-properties/7.22.5:
    resolution: {integrity: sha1-bid2VL6CtVWfxLn1gIhQfCTwxiQ=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-shorthand-properties/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-bid2VL6CtVWfxLn1gIhQfCTwxiQ=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-spread/7.22.5:
    resolution: {integrity: sha1-ZIf9KfIpyV4oS6bJjWXq+4k/6ms=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
    dev: true

  /@babel/plugin-transform-spread/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-ZIf9KfIpyV4oS6bJjWXq+4k/6ms=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
    dev: true

  /@babel/plugin-transform-sticky-regex/7.22.5:
    resolution: {integrity: sha1-KVq6FZW/yBl6vQLq5fwojA3rJqo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-sticky-regex/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-KVq6FZW/yBl6vQLq5fwojA3rJqo=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-template-literals/7.22.5:
    resolution: {integrity: sha1-jzjPKR5feo5g6fczGT8LzBCQm/8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-template-literals/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-jzjPKR5feo5g6fczGT8LzBCQm/8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-typeof-symbol/7.22.5:
    resolution: {integrity: sha1-XiukeNpLYDr4Zz/3xU91qXtxazQ=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-typeof-symbol/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-XiukeNpLYDr4Zz/3xU91qXtxazQ=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-unicode-escapes/7.22.10:
    resolution: {integrity: sha1-xyPzgPQKKy9Xpi3yTJAFg0yGFtk=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-unicode-escapes/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-xyPzgPQKKy9Xpi3yTJAFg0yGFtk=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-unicode-property-regex/7.22.5:
    resolution: {integrity: sha1-CYiY901cHoZmDcESBXstESJ/HIE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-create-regexp-features-plugin': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-unicode-property-regex/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-CYiY901cHoZmDcESBXstESJ/HIE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-regexp-features-plugin': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-unicode-regex/7.22.5:
    resolution: {integrity: sha1-zn57s+8gjE/2fgKiKBZlYlbXoYM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-create-regexp-features-plugin': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-unicode-regex/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-zn57s+8gjE/2fgKiKBZlYlbXoYM=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-regexp-features-plugin': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-unicode-sets-regex/7.22.5:
    resolution: {integrity: sha1-d3iAYOURtwj/x9Qv37xbN8MATpE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-create-regexp-features-plugin': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/plugin-transform-unicode-sets-regex/7.22.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-d3iAYOURtwj/x9Qv37xbN8MATpE=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-create-regexp-features-plugin': 7.22.9_@babel+core@7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: true

  /@babel/preset-env/7.22.10:
    resolution: {integrity: sha1-MmO5/iyII9GR0o5h6sYKefnOig8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.22.9
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-validator-option': 7.22.5
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.22.5
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.22.5
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2
      '@babel/plugin-syntax-async-generators': 7.8.4
      '@babel/plugin-syntax-class-properties': 7.12.13
      '@babel/plugin-syntax-class-static-block': 7.14.5
      '@babel/plugin-syntax-dynamic-import': 7.8.3
      '@babel/plugin-syntax-export-namespace-from': 7.8.3
      '@babel/plugin-syntax-import-assertions': 7.22.5
      '@babel/plugin-syntax-import-attributes': 7.22.5
      '@babel/plugin-syntax-import-meta': 7.10.4
      '@babel/plugin-syntax-json-strings': 7.8.3
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3
      '@babel/plugin-syntax-numeric-separator': 7.10.4
      '@babel/plugin-syntax-object-rest-spread': 7.8.3
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3
      '@babel/plugin-syntax-optional-chaining': 7.8.3
      '@babel/plugin-syntax-private-property-in-object': 7.14.5
      '@babel/plugin-syntax-top-level-await': 7.14.5
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6
      '@babel/plugin-transform-arrow-functions': 7.22.5
      '@babel/plugin-transform-async-generator-functions': 7.22.10
      '@babel/plugin-transform-async-to-generator': 7.22.5
      '@babel/plugin-transform-block-scoped-functions': 7.22.5
      '@babel/plugin-transform-block-scoping': 7.22.10
      '@babel/plugin-transform-class-properties': 7.22.5
      '@babel/plugin-transform-class-static-block': 7.22.5
      '@babel/plugin-transform-classes': 7.22.6
      '@babel/plugin-transform-computed-properties': 7.22.5
      '@babel/plugin-transform-destructuring': 7.22.10
      '@babel/plugin-transform-dotall-regex': 7.22.5
      '@babel/plugin-transform-duplicate-keys': 7.22.5
      '@babel/plugin-transform-dynamic-import': 7.22.5
      '@babel/plugin-transform-exponentiation-operator': 7.22.5
      '@babel/plugin-transform-export-namespace-from': 7.22.5
      '@babel/plugin-transform-for-of': 7.22.5
      '@babel/plugin-transform-function-name': 7.22.5
      '@babel/plugin-transform-json-strings': 7.22.5
      '@babel/plugin-transform-literals': 7.22.5
      '@babel/plugin-transform-logical-assignment-operators': 7.22.5
      '@babel/plugin-transform-member-expression-literals': 7.22.5
      '@babel/plugin-transform-modules-amd': 7.22.5
      '@babel/plugin-transform-modules-commonjs': 7.22.5
      '@babel/plugin-transform-modules-systemjs': 7.22.5
      '@babel/plugin-transform-modules-umd': 7.22.5
      '@babel/plugin-transform-named-capturing-groups-regex': 7.22.5
      '@babel/plugin-transform-new-target': 7.22.5
      '@babel/plugin-transform-nullish-coalescing-operator': 7.22.5
      '@babel/plugin-transform-numeric-separator': 7.22.5
      '@babel/plugin-transform-object-rest-spread': 7.22.5
      '@babel/plugin-transform-object-super': 7.22.5
      '@babel/plugin-transform-optional-catch-binding': 7.22.5
      '@babel/plugin-transform-optional-chaining': 7.22.10
      '@babel/plugin-transform-parameters': 7.22.5
      '@babel/plugin-transform-private-methods': 7.22.5
      '@babel/plugin-transform-private-property-in-object': 7.22.5
      '@babel/plugin-transform-property-literals': 7.22.5
      '@babel/plugin-transform-regenerator': 7.22.10
      '@babel/plugin-transform-reserved-words': 7.22.5
      '@babel/plugin-transform-shorthand-properties': 7.22.5
      '@babel/plugin-transform-spread': 7.22.5
      '@babel/plugin-transform-sticky-regex': 7.22.5
      '@babel/plugin-transform-template-literals': 7.22.5
      '@babel/plugin-transform-typeof-symbol': 7.22.5
      '@babel/plugin-transform-unicode-escapes': 7.22.10
      '@babel/plugin-transform-unicode-property-regex': 7.22.5
      '@babel/plugin-transform-unicode-regex': 7.22.5
      '@babel/plugin-transform-unicode-sets-regex': 7.22.5
      '@babel/preset-modules': 0.1.6-no-external-plugins
      '@babel/types': 7.22.10
      babel-plugin-polyfill-corejs2: 0.4.5
      babel-plugin-polyfill-corejs3: 0.8.3
      babel-plugin-polyfill-regenerator: 0.5.2
      core-js-compat: 3.32.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/preset-env/7.22.10_@babel+core@7.22.10:
    resolution: {integrity: sha1-MmO5/iyII9GR0o5h6sYKefnOig8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.22.9
      '@babel/core': 7.22.10
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-validator-option': 7.22.5
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2_@babel+core@7.22.10
      '@babel/plugin-syntax-async-generators': 7.8.4_@babel+core@7.22.10
      '@babel/plugin-syntax-class-properties': 7.12.13_@babel+core@7.22.10
      '@babel/plugin-syntax-class-static-block': 7.14.5_@babel+core@7.22.10
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.22.10
      '@babel/plugin-syntax-export-namespace-from': 7.8.3_@babel+core@7.22.10
      '@babel/plugin-syntax-import-assertions': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-syntax-import-attributes': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-syntax-import-meta': 7.10.4_@babel+core@7.22.10
      '@babel/plugin-syntax-json-strings': 7.8.3_@babel+core@7.22.10
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4_@babel+core@7.22.10
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.22.10
      '@babel/plugin-syntax-numeric-separator': 7.10.4_@babel+core@7.22.10
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.22.10
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3_@babel+core@7.22.10
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.22.10
      '@babel/plugin-syntax-private-property-in-object': 7.14.5_@babel+core@7.22.10
      '@babel/plugin-syntax-top-level-await': 7.14.5_@babel+core@7.22.10
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6_@babel+core@7.22.10
      '@babel/plugin-transform-arrow-functions': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-async-generator-functions': 7.22.10_@babel+core@7.22.10
      '@babel/plugin-transform-async-to-generator': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-block-scoped-functions': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-block-scoping': 7.22.10_@babel+core@7.22.10
      '@babel/plugin-transform-class-properties': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-class-static-block': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-classes': 7.22.6_@babel+core@7.22.10
      '@babel/plugin-transform-computed-properties': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-destructuring': 7.22.10_@babel+core@7.22.10
      '@babel/plugin-transform-dotall-regex': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-duplicate-keys': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-dynamic-import': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-exponentiation-operator': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-export-namespace-from': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-for-of': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-function-name': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-json-strings': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-literals': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-logical-assignment-operators': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-member-expression-literals': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-modules-amd': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-modules-commonjs': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-modules-systemjs': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-modules-umd': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-named-capturing-groups-regex': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-new-target': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-nullish-coalescing-operator': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-numeric-separator': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-object-rest-spread': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-object-super': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-optional-catch-binding': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-optional-chaining': 7.22.10_@babel+core@7.22.10
      '@babel/plugin-transform-parameters': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-private-methods': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-private-property-in-object': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-property-literals': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-regenerator': 7.22.10_@babel+core@7.22.10
      '@babel/plugin-transform-reserved-words': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-shorthand-properties': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-spread': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-sticky-regex': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-template-literals': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-typeof-symbol': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-unicode-escapes': 7.22.10_@babel+core@7.22.10
      '@babel/plugin-transform-unicode-property-regex': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-unicode-regex': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-unicode-sets-regex': 7.22.5_@babel+core@7.22.10
      '@babel/preset-modules': 0.1.6-no-external-plugins_@babel+core@7.22.10
      '@babel/types': 7.22.10
      babel-plugin-polyfill-corejs2: 0.4.5_@babel+core@7.22.10
      babel-plugin-polyfill-corejs3: 0.8.3_@babel+core@7.22.10
      babel-plugin-polyfill-regenerator: 0.5.2_@babel+core@7.22.10
      core-js-compat: 3.32.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/preset-modules/0.1.6-no-external-plugins:
    resolution: {integrity: sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/types': 7.22.10
      esutils: 2.0.3
    dev: true

  /@babel/preset-modules/0.1.6-no-external-plugins_@babel+core@7.22.10:
    resolution: {integrity: sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/types': 7.22.10
      esutils: 2.0.3
    dev: true

  /@babel/regjsgen/0.8.0:
    resolution: {integrity: sha1-8LppsHXh8F+yglt/rZkeetuxgxA=}
    dev: true

  /@babel/runtime/7.22.10:
    resolution: {integrity: sha1-rj6WMf2UfLfjYQ0+nY/vX3ZpZoI=}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.0

  /@babel/template/7.22.5:
    resolution: {integrity: sha1-DIxNlEUJh1hJvQNE/wBQdW7vxuw=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.22.10
      '@babel/parser': 7.22.10
      '@babel/types': 7.22.10
    dev: true

  /@babel/traverse/7.22.10:
    resolution: {integrity: sha1-ICUqyyQOdG0nwugrRITxmc+BQao=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.22.10
      '@babel/generator': 7.22.10
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-function-name': 7.22.5
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/parser': 7.22.10
      '@babel/types': 7.22.10
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/types/7.0.0-beta.35:
    resolution: {integrity: sha1-z5M6mpo4SEynJLM1uI2Dcm1auWA=}
    dependencies:
      esutils: 2.0.3
      lodash: 4.17.21
      to-fast-properties: 2.0.0
    dev: true

  /@babel/types/7.22.10:
    resolution: {integrity: sha1-Sp52RGBI8sZpgtGpid0SuKLS3AM=}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.22.5
      '@babel/helper-validator-identifier': 7.22.5
      to-fast-properties: 2.0.0

  /@chenfengyuan/vue-qrcode/1.0.2_vue@2.7.14:
    resolution: {integrity: sha1-N9cZAuFm4a5YF2vWy5xAkFwbCUk=}
    peerDependencies:
      vue: ^2.6.0
    dependencies:
      qrcode: 1.5.3
      vue: 2.7.14
    dev: false

  /@emmetio/codemirror-plugin/1.2.4:
    resolution: {integrity: sha1-+ICjYJhTb2VVdH6t7Om3zC31l+M=}
    dev: false

  /@gar/promisify/1.1.3:
    resolution: {integrity: sha1-VVGTqy47s7atw9VRycAw2ehg2vY=}
    dev: true

  /@haizhi/monitor/1.0.1:
    resolution: {integrity: sha1-d8ld9XF6NY3Y3umShHgQikTrhtw=, tarball: '@haizhi/monitor/-/@haizhi/monitor-1.0.1.tgz'}
    dev: false

  /@hapi/address/2.1.4:
    resolution: {integrity: sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=}
    deprecated: Moved to 'npm install @sideway/address'
    dev: true

  /@hapi/bourne/1.3.2:
    resolution: {integrity: sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=}
    deprecated: This version has been deprecated and is no longer supported or maintained
    dev: true

  /@hapi/hoek/8.5.1:
    resolution: {integrity: sha1-/elgZMpEbeyMVajC8TCVewcMbgY=}
    deprecated: This version has been deprecated and is no longer supported or maintained
    dev: true

  /@hapi/joi/15.1.1:
    resolution: {integrity: sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=}
    deprecated: Switch to 'npm install joi'
    dependencies:
      '@hapi/address': 2.1.4
      '@hapi/bourne': 1.3.2
      '@hapi/hoek': 8.5.1
      '@hapi/topo': 3.1.6
    dev: true

  /@hapi/topo/3.1.6:
    resolution: {integrity: sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=}
    deprecated: This version has been deprecated and is no longer supported or maintained
    dependencies:
      '@hapi/hoek': 8.5.1
    dev: true

  /@interactjs/actions/1.10.2:
    resolution: {integrity: sha1-BQrhxAt703c6eaZcX7FzC7YRHnw=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/actions/1.10.2_760d4ab2919a973169eb3c271689265e:
    resolution: {integrity: sha1-BQrhxAt703c6eaZcX7FzC7YRHnw=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/core': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/arrange/1.10.2:
    resolution: {integrity: sha1-5tk8XAEmHRMG1GQSCVKJZx4b5NI=}
    dev: false

  /@interactjs/auto-scroll/1.10.2:
    resolution: {integrity: sha1-RuMv4KZ+Jmqx91jbHqbdFSiEA/A=}
    peerDependencies:
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/auto-scroll/1.10.2_@interactjs+utils@1.10.2:
    resolution: {integrity: sha1-RuMv4KZ+Jmqx91jbHqbdFSiEA/A=}
    peerDependencies:
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/auto-start/1.10.2:
    resolution: {integrity: sha1-v5Ya5UHN98sPA8t7qJtN70Ri+rA=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/auto-start/1.10.2_760d4ab2919a973169eb3c271689265e:
    resolution: {integrity: sha1-v5Ya5UHN98sPA8t7qJtN70Ri+rA=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/core': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/clone/1.10.2:
    resolution: {integrity: sha1-sRCh2c/SBGhMQq93aEez+ay7rlM=}
    dev: false

  /@interactjs/core/1.10.2_@interactjs+utils@1.10.2:
    resolution: {integrity: sha1-ALycnqn1DB40Wrlt63UIgef/Dmo=}
    peerDependencies:
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/utils': 1.10.2
    dev: false

  /@interactjs/dev-tools/1.10.2:
    resolution: {integrity: sha1-zeQTq70R3ZHIdJkQXhYJHm6zW2c=}
    dependencies:
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/feedback/1.10.2:
    resolution: {integrity: sha1-2W3B0xl8825gEEeSU8HPnbiVLNs=}
    dev: false

  /@interactjs/inertia/1.10.2_100eacedd1bbd176f89fe8a2e4773d5f:
    resolution: {integrity: sha1-ykj+6tbr/KHKRD9xqAvAzfr4qgU=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/modifiers': 1.10.2
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/core': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/modifiers': 1.10.2_760d4ab2919a973169eb3c271689265e
      '@interactjs/offset': 1.10.2_760d4ab2919a973169eb3c271689265e
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/interact/1.10.2:
    resolution: {integrity: sha1-BQvM0J989c7VMu+RxUkucuEw/aM=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/interact/-/interact-1.10.2.tgz}
    dependencies:
      '@interactjs/core': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/types': 1.10.2
      '@interactjs/utils': 1.10.2
    dev: false

  /@interactjs/interactjs/1.10.2:
    resolution: {integrity: sha1-r5bkf8Y3ypbZR3/o19GFjMrvKPg=}
    dependencies:
      '@interactjs/actions': 1.10.2_760d4ab2919a973169eb3c271689265e
      '@interactjs/arrange': 1.10.2
      '@interactjs/auto-scroll': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/auto-start': 1.10.2_760d4ab2919a973169eb3c271689265e
      '@interactjs/clone': 1.10.2
      '@interactjs/core': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/dev-tools': 1.10.2
      '@interactjs/feedback': 1.10.2
      '@interactjs/inertia': 1.10.2_100eacedd1bbd176f89fe8a2e4773d5f
      '@interactjs/interact': 1.10.2
      '@interactjs/modifiers': 1.10.2_760d4ab2919a973169eb3c271689265e
      '@interactjs/multi-target': 1.10.2
      '@interactjs/offset': 1.10.2_760d4ab2919a973169eb3c271689265e
      '@interactjs/pointer-events': 1.10.2_760d4ab2919a973169eb3c271689265e
      '@interactjs/react': 1.10.2
      '@interactjs/reflow': 1.10.2_760d4ab2919a973169eb3c271689265e
      '@interactjs/utils': 1.10.2
      '@interactjs/vue': 1.10.2
    dev: false

  /@interactjs/modifiers/1.10.2:
    resolution: {integrity: sha1-/elB0fB9iLGcNR13yrPHZjUcj54=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/snappers': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/modifiers/1.10.2_760d4ab2919a973169eb3c271689265e:
    resolution: {integrity: sha1-/elB0fB9iLGcNR13yrPHZjUcj54=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/core': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/snappers': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/multi-target/1.10.2:
    resolution: {integrity: sha1-m5vCZRSwQcrhkw17aUvku1sLbq0=}
    dev: false

  /@interactjs/offset/1.10.2_760d4ab2919a973169eb3c271689265e:
    resolution: {integrity: sha1-v15PisF2xdJElprtkSyoOhhciPE=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/core': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/pointer-events/1.10.2_760d4ab2919a973169eb3c271689265e:
    resolution: {integrity: sha1-Ai/v+FHmhcBHhiBMHHOTYhB409U=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/core': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/react/1.10.2:
    resolution: {integrity: sha1-Kj1CUC82pTG9DfxFZwENPsk2b8s=}
    dev: false

  /@interactjs/reflow/1.10.2_760d4ab2919a973169eb3c271689265e:
    resolution: {integrity: sha1-e1tbN0Cq2RSaeS1vHtnXveJuO1s=}
    peerDependencies:
      '@interactjs/core': 1.10.2
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/core': 1.10.2_@interactjs+utils@1.10.2
      '@interactjs/utils': 1.10.2
    optionalDependencies:
      '@interactjs/interact': 1.10.2
    dev: false

  /@interactjs/snappers/1.10.2:
    resolution: {integrity: sha1-w48a3iSfnKauLkCMa/ROemOa5bA=}
    peerDependencies:
      '@interactjs/utils': 1.10.2
    dev: false

  /@interactjs/snappers/1.10.2_@interactjs+utils@1.10.2:
    resolution: {integrity: sha1-w48a3iSfnKauLkCMa/ROemOa5bA=}
    peerDependencies:
      '@interactjs/utils': 1.10.2
    dependencies:
      '@interactjs/utils': 1.10.2
    dev: false

  /@interactjs/types/1.10.2:
    resolution: {integrity: sha1-RadNAZ+bPo/M08zCiLVbz1EBBrw=}
    dev: false

  /@interactjs/utils/1.10.2:
    resolution: {integrity: sha1-1o3UXJtBuqsRrPDulobeIH11S2M=}
    dev: false

  /@interactjs/vue/1.10.2:
    resolution: {integrity: sha1-SKzQiD/HnkLKQfqd+ZUkaK83svk=}
    dev: false

  /@intervolga/optimize-cssnano-plugin/1.0.6_webpack@4.46.0:
    resolution: {integrity: sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg=}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      cssnano: 4.1.11
      cssnano-preset-default: 4.0.8
      postcss: 7.0.39
      webpack: 4.46.0
    dev: true

  /@jridgewell/gen-mapping/0.3.3:
    resolution: {integrity: sha1-fgLm6135AartsIUUIDsJZhQCQJg=}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.19
    dev: true

  /@jridgewell/resolve-uri/3.1.1:
    resolution: {integrity: sha1-wIZ5Bj8nlhWjMmWDujqQ0dgsxyE=}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/set-array/1.1.2:
    resolution: {integrity: sha1-fGz5mNbSC5FMClWpGuko/yWWXnI=}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/sourcemap-codec/1.4.15:
    resolution: {integrity: sha1-18bmdVx4VnqVHgSrUu8P0m3lnzI=}
    dev: true

  /@jridgewell/trace-mapping/0.3.19:
    resolution: {integrity: sha1-+KMkmGL5G+SNMSfDz+mS95tLiBE=}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.1
      '@jridgewell/sourcemap-codec': 1.4.15
    dev: true

  /@mrmlnc/readdir-enhanced/2.2.1:
    resolution: {integrity: sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=}
    engines: {node: '>=4'}
    dependencies:
      call-me-maybe: 1.0.2
      glob-to-regexp: 0.3.0
    dev: true

  /@node-ipc/js-queue/2.0.3:
    resolution: {integrity: sha1-rH/jPXZvpT4jPvj+2vNEOgHFpM0=}
    engines: {node: '>=1.0.0'}
    dependencies:
      easy-stack: 1.0.1
    dev: true

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat/1.1.3:
    resolution: {integrity: sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=}
    engines: {node: '>= 6'}
    dev: true

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.15.0
    dev: true

  /@npmcli/fs/1.1.1:
    resolution: {integrity: sha1-cvcZ/pNeaHxWpPrs88A9BrpZMlc=}
    dependencies:
      '@gar/promisify': 1.1.3
      semver: 7.5.4
    dev: true

  /@npmcli/move-file/1.1.2:
    resolution: {integrity: sha1-GoLD43L3yuklPrZtclQ9a4aFxnQ=}
    engines: {node: '>=10'}
    deprecated: This functionality has been moved to @npmcli/fs
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2
    dev: true

  /@riophae/vue-treeselect/0.4.0_vue@2.7.14:
    resolution: {integrity: sha1-C67Vp5TP/FgLY1kfNcEl5RwN8kE=}
    peerDependencies:
      vue: ^2.2.0
    dependencies:
      '@babel/runtime': 7.22.10
      babel-helper-vue-jsx-merge-props: 2.0.3
      easings-css: 1.0.0
      fuzzysearch: 1.0.3
      is-promise: 2.2.2
      lodash: 4.17.21
      material-colors: 1.2.6
      vue: 2.7.14
      watch-size: 2.0.0
    dev: false

  /@samverschueren/stream-to-observable/0.3.1_rxjs@6.6.7:
    resolution: {integrity: sha1-ohEXsZ7pvnDDeewYd1N+8uHGMwE=}
    engines: {node: '>=6'}
    peerDependencies:
      rxjs: '*'
      zen-observable: '*'
    peerDependenciesMeta:
      rxjs:
        optional: true
      zen-observable:
        optional: true
    dependencies:
      any-observable: 0.3.0
      rxjs: 6.6.7
    dev: true

  /@soda/friendly-errors-webpack-plugin/1.8.1_webpack@4.46.0:
    resolution: {integrity: sha1-TU+7EQiZOqo2IRYkfD0YGIosbIU=}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      chalk: 3.0.0
      error-stack-parser: 2.1.4
      string-width: 4.2.3
      strip-ansi: 6.0.1
      webpack: 4.46.0
    dev: true

  /@soda/get-current-script/1.0.2:
    resolution: {integrity: sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc=}
    dev: true

  /@sphinxxxx/color-conversion/2.2.2:
    resolution: {integrity: sha1-A+zCknnjwMgy9hhaW/o0l4WKyMo=}
    dev: false

  /@tinymce/tinymce-vue/3.2.8_vue@2.7.14:
    resolution: {integrity: sha1-AUVxtS7I+oNmWn+oh79lFAIH3nE=}
    peerDependencies:
      vue: ^2.4.3
    dependencies:
      vue: 2.7.14
    dev: false

  /@types/body-parser/1.19.2:
    resolution: {integrity: sha1-rqIFnii3ZYY5CBNHrE+rPeFm5vA=}
    dependencies:
      '@types/connect': 3.4.35
      '@types/node': 20.5.0
    dev: true

  /@types/connect-history-api-fallback/1.5.0:
    resolution: {integrity: sha1-n9ILOXS9wrzUrGVn4uD2iFyyz0E=}
    dependencies:
      '@types/express-serve-static-core': 4.17.35
      '@types/node': 20.5.0
    dev: true

  /@types/connect/3.4.35:
    resolution: {integrity: sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=}
    dependencies:
      '@types/node': 20.5.0
    dev: true

  /@types/express-serve-static-core/4.17.35:
    resolution: {integrity: sha1-yV3UQk8NMuUl0jgSqoq45NOQbE8=}
    dependencies:
      '@types/node': 20.5.0
      '@types/qs': 6.9.7
      '@types/range-parser': 1.2.4
      '@types/send': 0.17.1
    dev: true

  /@types/express/4.17.17:
    resolution: {integrity: sha1-AdVDf275z6hmjmFuE8LyrJpJGuQ=}
    dependencies:
      '@types/body-parser': 1.19.2
      '@types/express-serve-static-core': 4.17.35
      '@types/qs': 6.9.7
      '@types/serve-static': 1.15.2
    dev: true

  /@types/glob/7.2.0:
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=}
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 20.5.0
    dev: true

  /@types/http-errors/2.0.1:
    resolution: {integrity: sha1-IBcvlXiyJfbH2mNEb1bUzhCNWmU=}
    dev: true

  /@types/http-proxy/1.17.11:
    resolution: {integrity: sha1-DKIZSaVYjVWsK2WbaQNchL1dopM=}
    dependencies:
      '@types/node': 20.5.0
    dev: true

  /@types/json-schema/7.0.12:
    resolution: {integrity: sha1-1w+rpwOdX8pUyDx9urQQUdK29ss=}

  /@types/json5/0.0.29:
    resolution: {integrity: sha1-7ihweulOEdK4J7y+UnC86n8+ce4=}
    dev: true

  /@types/mime/1.3.2:
    resolution: {integrity: sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o=}
    dev: true

  /@types/mime/3.0.1:
    resolution: {integrity: sha1-X48rygpYY8tpvAsKzYjJbLHUrhA=}
    dev: true

  /@types/minimatch/5.1.2:
    resolution: {integrity: sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=}
    dev: true

  /@types/minimist/1.2.2:
    resolution: {integrity: sha1-7nceK6Sz3Fs3KTXVSf2WF780W4w=}
    dev: true

  /@types/node/20.5.0:
    resolution: {integrity: sha1-f8hjbV8aqjsh5iRel9Vrf1ZwIxM=}
    dev: true

  /@types/normalize-package-data/2.4.1:
    resolution: {integrity: sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE=}
    dev: true

  /@types/q/1.5.5:
    resolution: {integrity: sha1-daKo59irSyMEFFBdkjNdHctTpt8=}
    dev: true

  /@types/qs/6.9.7:
    resolution: {integrity: sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss=}
    dev: true

  /@types/range-parser/1.2.4:
    resolution: {integrity: sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw=}
    dev: true

  /@types/send/0.17.1:
    resolution: {integrity: sha1-7UkyuKKoBfH+Nipw9OYtCsmU4wE=}
    dependencies:
      '@types/mime': 1.3.2
      '@types/node': 20.5.0
    dev: true

  /@types/serve-static/1.15.2:
    resolution: {integrity: sha1-PlQZ7NHkDnQF00CT8QvvtD9jOBo=}
    dependencies:
      '@types/http-errors': 2.0.1
      '@types/mime': 3.0.1
      '@types/node': 20.5.0
    dev: true

  /@types/source-list-map/0.1.2:
    resolution: {integrity: sha1-AHiDYGP/rxdBI0m7o2QIfgrALsk=}
    dev: true

  /@types/tapable/1.0.8:
    resolution: {integrity: sha1-uUpDkchWZse3Mpn9OtedT6pDUxA=}
    dev: true

  /@types/uglify-js/3.17.1:
    resolution: {integrity: sha1-4P/O91ZHZBDlvOLLAThO2HihlbU=}
    dependencies:
      source-map: 0.6.1
    dev: true

  /@types/webpack-dev-server/3.11.6_debug@4.3.4:
    resolution: {integrity: sha1-2IiM/S8GMCA+E9PteDOk0RuKNNw=}
    dependencies:
      '@types/connect-history-api-fallback': 1.5.0
      '@types/express': 4.17.17
      '@types/serve-static': 1.15.2
      '@types/webpack': 4.41.33
      http-proxy-middleware: 1.3.1_debug@4.3.4
    transitivePeerDependencies:
      - debug
    dev: true

  /@types/webpack-sources/3.2.0:
    resolution: {integrity: sha1-FtdZuglsKJA0smVT0t8b9FJI04s=}
    dependencies:
      '@types/node': 20.5.0
      '@types/source-list-map': 0.1.2
      source-map: 0.7.4
    dev: true

  /@types/webpack/4.41.33:
    resolution: {integrity: sha1-FhZIRaW+ajBry+VUqOZ/nKwhX/w=}
    dependencies:
      '@types/node': 20.5.0
      '@types/tapable': 1.0.8
      '@types/uglify-js': 3.17.1
      '@types/webpack-sources': 3.2.0
      anymatch: 3.1.3
      source-map: 0.6.1
    dev: true

  /@vant/icons/1.8.0:
    resolution: {integrity: sha1-NrE/LmKLUz9lI6k6FozwLwcFZnQ=}
    dev: false

  /@vant/popperjs/1.3.0:
    resolution: {integrity: sha1-4O/wFxJLWyNS7zs2pt8GJ39EAPI=}
    dev: false

  /@vue/babel-helper-vue-jsx-merge-props/1.4.0:
    resolution: {integrity: sha1-jVOh4hNH247b5U0zmQJYMXbeCfI=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz}

  /@vue/babel-helper-vue-transform-on/1.1.5:
    resolution: {integrity: sha1-qXZIayHhCOVFUk/kH/4/ybvCjH8=}
    dev: true

  /@vue/babel-plugin-jsx/1.1.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-UIi659u4NTHZTfN0L/ZQwS/VSXM=}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-module-imports': 7.22.5
      '@babel/plugin-syntax-jsx': 7.22.5_@babel+core@7.22.10
      '@babel/template': 7.22.5
      '@babel/traverse': 7.22.10
      '@babel/types': 7.22.10
      '@vue/babel-helper-vue-transform-on': 1.1.5
      camelcase: 6.3.0
      html-tags: 3.3.1
      svg-tags: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vue/babel-plugin-transform-vue-jsx/1.4.0:
    resolution: {integrity: sha1-TUs9RqOepit0Z91uJs5H986vsv4=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-module-imports': 7.22.5
      '@babel/plugin-syntax-jsx': 7.22.5
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      html-tags: 2.0.0
      lodash.kebabcase: 4.1.1
      svg-tags: 1.0.0
    dev: false

  /@vue/babel-plugin-transform-vue-jsx/1.4.0_@babel+core@7.22.10:
    resolution: {integrity: sha1-TUs9RqOepit0Z91uJs5H986vsv4=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-module-imports': 7.22.5
      '@babel/plugin-syntax-jsx': 7.22.5_@babel+core@7.22.10
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      html-tags: 2.0.0
      lodash.kebabcase: 4.1.1
      svg-tags: 1.0.0
    dev: true

  /@vue/babel-preset-app/4.5.19_vue@2.7.14:
    resolution: {integrity: sha1-uu5FfaAGXAFvdPrEFJ98l2Mbpac=}
    peerDependencies:
      vue: ^2 || ^3.0.0-0
    peerDependenciesMeta:
      core-js:
        optional: true
      vue:
        optional: true
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-module-imports': 7.22.5
      '@babel/plugin-proposal-class-properties': 7.18.6_@babel+core@7.22.10
      '@babel/plugin-proposal-decorators': 7.22.10_@babel+core@7.22.10
      '@babel/plugin-syntax-dynamic-import': 7.8.3_@babel+core@7.22.10
      '@babel/plugin-syntax-jsx': 7.22.5_@babel+core@7.22.10
      '@babel/plugin-transform-runtime': 7.22.10_@babel+core@7.22.10
      '@babel/preset-env': 7.22.10_@babel+core@7.22.10
      '@babel/runtime': 7.22.10
      '@vue/babel-plugin-jsx': 1.1.5_@babel+core@7.22.10
      '@vue/babel-preset-jsx': 1.4.0_@babel+core@7.22.10+vue@2.7.14
      babel-plugin-dynamic-import-node: 2.3.3
      core-js: 3.32.0
      core-js-compat: 3.32.0
      semver: 6.3.1
      vue: 2.7.14
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@vue/babel-preset-jsx/1.4.0_@babel+core@7.22.10+vue@2.7.14:
    resolution: {integrity: sha1-9JFLoxQjWrCXvENy7WdHPAeAv8w=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-preset-jsx/-/babel-preset-jsx-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
      vue: '*'
    peerDependenciesMeta:
      vue:
        optional: true
    dependencies:
      '@babel/core': 7.22.10
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0_@babel+core@7.22.10
      '@vue/babel-sugar-composition-api-inject-h': 1.4.0_@babel+core@7.22.10
      '@vue/babel-sugar-composition-api-render-instance': 1.4.0_@babel+core@7.22.10
      '@vue/babel-sugar-functional-vue': 1.4.0_@babel+core@7.22.10
      '@vue/babel-sugar-inject-h': 1.4.0_@babel+core@7.22.10
      '@vue/babel-sugar-v-model': 1.4.0_@babel+core@7.22.10
      '@vue/babel-sugar-v-on': 1.4.0_@babel+core@7.22.10
      vue: 2.7.14
    dev: true

  /@vue/babel-preset-jsx/1.4.0_vue@2.7.14:
    resolution: {integrity: sha1-9JFLoxQjWrCXvENy7WdHPAeAv8w=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-preset-jsx/-/babel-preset-jsx-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
      vue: '*'
    peerDependenciesMeta:
      vue:
        optional: true
    dependencies:
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0
      '@vue/babel-sugar-composition-api-inject-h': 1.4.0
      '@vue/babel-sugar-composition-api-render-instance': 1.4.0
      '@vue/babel-sugar-functional-vue': 1.4.0
      '@vue/babel-sugar-inject-h': 1.4.0
      '@vue/babel-sugar-v-model': 1.4.0
      '@vue/babel-sugar-v-on': 1.4.0
      vue: 2.7.14
    dev: false

  /@vue/babel-sugar-composition-api-inject-h/1.4.0:
    resolution: {integrity: sha1-GH4TifiHHYns50O7UK7XE76dbIU=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-composition-api-inject-h/-/babel-sugar-composition-api-inject-h-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/plugin-syntax-jsx': 7.22.5
    dev: false

  /@vue/babel-sugar-composition-api-inject-h/1.4.0_@babel+core@7.22.10:
    resolution: {integrity: sha1-GH4TifiHHYns50O7UK7XE76dbIU=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-composition-api-inject-h/-/babel-sugar-composition-api-inject-h-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/plugin-syntax-jsx': 7.22.5_@babel+core@7.22.10
    dev: true

  /@vue/babel-sugar-composition-api-render-instance/1.4.0:
    resolution: {integrity: sha1-LBYHrm3/2rR+eFvAH6Rbp1bpksE=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-composition-api-render-instance/-/babel-sugar-composition-api-render-instance-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/plugin-syntax-jsx': 7.22.5
    dev: false

  /@vue/babel-sugar-composition-api-render-instance/1.4.0_@babel+core@7.22.10:
    resolution: {integrity: sha1-LBYHrm3/2rR+eFvAH6Rbp1bpksE=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-composition-api-render-instance/-/babel-sugar-composition-api-render-instance-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/plugin-syntax-jsx': 7.22.5_@babel+core@7.22.10
    dev: true

  /@vue/babel-sugar-functional-vue/1.4.0:
    resolution: {integrity: sha1-YNoxBoVnCCKHxzN8Zu9N8E4KECk=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/plugin-syntax-jsx': 7.22.5
    dev: false

  /@vue/babel-sugar-functional-vue/1.4.0_@babel+core@7.22.10:
    resolution: {integrity: sha1-YNoxBoVnCCKHxzN8Zu9N8E4KECk=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/plugin-syntax-jsx': 7.22.5_@babel+core@7.22.10
    dev: true

  /@vue/babel-sugar-inject-h/1.4.0:
    resolution: {integrity: sha1-vzmqZjH7HQOZscSbTFnhyImbQ2M=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/plugin-syntax-jsx': 7.22.5
    dev: false

  /@vue/babel-sugar-inject-h/1.4.0_@babel+core@7.22.10:
    resolution: {integrity: sha1-vzmqZjH7HQOZscSbTFnhyImbQ2M=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/plugin-syntax-jsx': 7.22.5_@babel+core@7.22.10
    dev: true

  /@vue/babel-sugar-v-model/1.4.0:
    resolution: {integrity: sha1-pR2YZgn0MMT3Cto6k8xWCilw9yA=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/plugin-syntax-jsx': 7.22.5
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0
      camelcase: 5.3.1
      html-tags: 2.0.0
      svg-tags: 1.0.0
    dev: false

  /@vue/babel-sugar-v-model/1.4.0_@babel+core@7.22.10:
    resolution: {integrity: sha1-pR2YZgn0MMT3Cto6k8xWCilw9yA=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/plugin-syntax-jsx': 7.22.5_@babel+core@7.22.10
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0_@babel+core@7.22.10
      camelcase: 5.3.1
      html-tags: 2.0.0
      svg-tags: 1.0.0
    dev: true

  /@vue/babel-sugar-v-on/1.4.0:
    resolution: {integrity: sha1-Q7cQapZy2Mvu/A64r+HTdu3GFm4=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/plugin-syntax-jsx': 7.22.5
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0
      camelcase: 5.3.1
    dev: false

  /@vue/babel-sugar-v-on/1.4.0_@babel+core@7.22.10:
    resolution: {integrity: sha1-Q7cQapZy2Mvu/A64r+HTdu3GFm4=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/plugin-syntax-jsx': 7.22.5_@babel+core@7.22.10
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0_@babel+core@7.22.10
      camelcase: 5.3.1
    dev: true

  /@vue/cli-overlay/4.5.19:
    resolution: {integrity: sha1-0SBveAK8uh2cMHaVtUCR35ltuAQ=}
    dev: true

  /@vue/cli-plugin-babel/4.5.19_2dacb0cea8cfbd89239ebc716832e7d4:
    resolution: {integrity: sha1-KIsy5p8BkadzaeiPBxwM2ANu36c=}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@vue/babel-preset-app': 4.5.19_vue@2.7.14
      '@vue/cli-service': 4.5.19_8f023ad5f6aa6a67fdfe810224770a2b
      '@vue/cli-shared-utils': 4.5.19
      babel-loader: 8.3.0_a894a531581cfdbb33a5fa8f06028c80
      cache-loader: 4.1.0_webpack@4.46.0
      thread-loader: 2.1.3_webpack@4.46.0
      webpack: 4.46.0
    transitivePeerDependencies:
      - supports-color
      - vue
      - webpack-cli
      - webpack-command
    dev: true

  /@vue/cli-plugin-eslint/4.5.19_291eed2a4b15311c68aabdde8b2cf70f:
    resolution: {integrity: sha1-0fkItdB58pAtwjMBKQ5N2BdvIEw=}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0-0
      eslint: '>= 1.6.0 < 7.0.0'
    dependencies:
      '@vue/cli-service': 4.5.19_8f023ad5f6aa6a67fdfe810224770a2b
      '@vue/cli-shared-utils': 4.5.19
      eslint: 6.8.0
      eslint-loader: 2.2.1_eslint@6.8.0+webpack@4.46.0
      globby: 9.2.0
      inquirer: 7.3.3
      webpack: 4.46.0
      yorkie: 2.0.0
    transitivePeerDependencies:
      - webpack-cli
      - webpack-command
    dev: true

  /@vue/cli-plugin-router/4.5.19_@vue+cli-service@4.5.19:
    resolution: {integrity: sha1-p/7qcCS4Ogr3f8lA0WN9POL5Lh8=}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0-0
    dependencies:
      '@vue/cli-service': 4.5.19_8f023ad5f6aa6a67fdfe810224770a2b
      '@vue/cli-shared-utils': 4.5.19
    dev: true

  /@vue/cli-plugin-vuex/4.5.19_@vue+cli-service@4.5.19:
    resolution: {integrity: sha1-JFLeWOtm7Yc4Ur6kXm4GtX2EK0c=}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0-0
    dependencies:
      '@vue/cli-service': 4.5.19_8f023ad5f6aa6a67fdfe810224770a2b
    dev: true

  /@vue/cli-service/4.5.19_8f023ad5f6aa6a67fdfe810224770a2b:
    resolution: {integrity: sha1-X2UTEo9Ca+Dumn0DFVwjpvI/jUI=}
    engines: {node: '>=8'}
    hasBin: true
    peerDependencies:
      '@vue/compiler-sfc': ^3.0.0-beta.14
      less-loader: '*'
      pug-plain-loader: '*'
      raw-loader: '*'
      sass-loader: '*'
      stylus-loader: '*'
      vue-template-compiler: ^2.0.0
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true
      less-loader:
        optional: true
      pug-plain-loader:
        optional: true
      raw-loader:
        optional: true
      sass-loader:
        optional: true
      stylus-loader:
        optional: true
      vue-template-compiler:
        optional: true
    dependencies:
      '@intervolga/optimize-cssnano-plugin': 1.0.6_webpack@4.46.0
      '@soda/friendly-errors-webpack-plugin': 1.8.1_webpack@4.46.0
      '@soda/get-current-script': 1.0.2
      '@types/minimist': 1.2.2
      '@types/webpack': 4.41.33
      '@types/webpack-dev-server': 3.11.6_debug@4.3.4
      '@vue/cli-overlay': 4.5.19
      '@vue/cli-plugin-router': 4.5.19_@vue+cli-service@4.5.19
      '@vue/cli-plugin-vuex': 4.5.19_@vue+cli-service@4.5.19
      '@vue/cli-shared-utils': 4.5.19
      '@vue/component-compiler-utils': 3.3.0
      '@vue/preload-webpack-plugin': 1.1.2_502c618fc8a7d35df07e93275324a2d0
      '@vue/web-component-wrapper': 1.3.0
      acorn: 7.4.1
      acorn-walk: 7.2.0
      address: 1.2.2
      autoprefixer: 9.8.8
      browserslist: 4.21.10
      cache-loader: 4.1.0_webpack@4.46.0
      case-sensitive-paths-webpack-plugin: 2.4.0
      cli-highlight: 2.1.11
      clipboardy: 2.3.0
      cliui: 6.0.0
      copy-webpack-plugin: 5.1.2_webpack@4.46.0
      css-loader: 3.6.0_webpack@4.46.0
      cssnano: 4.1.11
      debug: 4.3.4
      default-gateway: 5.0.5
      dotenv: 8.6.0
      dotenv-expand: 5.1.0
      file-loader: 4.3.0_webpack@4.46.0
      fs-extra: 7.0.1
      globby: 9.2.0
      hash-sum: 2.0.0
      html-webpack-plugin: 3.2.0_webpack@4.46.0
      launch-editor-middleware: 2.6.0
      lodash.defaultsdeep: 4.6.1
      lodash.mapvalues: 4.6.0
      lodash.transform: 4.6.0
      mini-css-extract-plugin: 0.9.0_webpack@4.46.0
      minimist: 1.2.8
      pnp-webpack-plugin: 1.7.0
      portfinder: 1.0.32
      postcss-loader: 3.0.0
      sass-loader: 8.0.2_sass@1.65.1
      ssri: 8.0.1
      terser-webpack-plugin: 1.4.5_webpack@4.46.0
      thread-loader: 2.1.3_webpack@4.46.0
      url-loader: 2.3.0_file-loader@4.3.0+webpack@4.46.0
      vue-loader: 15.10.1_942d38e42bf4b987e783635c88cde9fa
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.7.14
      webpack: 4.46.0
      webpack-bundle-analyzer: 3.9.0
      webpack-chain: 6.5.1
      webpack-dev-server: 3.11.3_webpack@4.46.0
      webpack-merge: 4.2.2
    optionalDependencies:
      vue-loader-v16: /vue-loader/16.8.3_webpack@4.46.0
    transitivePeerDependencies:
      - supports-color
      - typescript
      - webpack-cli
      - webpack-command
    dev: true

  /@vue/cli-shared-utils/4.5.19:
    resolution: {integrity: sha1-zDibHeGwUHOATA/ptLCDuSjvYTA=}
    dependencies:
      '@achrinza/node-ipc': 9.2.2
      '@hapi/joi': 15.1.1
      chalk: 2.4.2
      execa: 1.0.0
      launch-editor: 2.6.0
      lru-cache: 5.1.1
      open: 6.4.0
      ora: 3.4.0
      read-pkg: 5.2.0
      request: 2.88.2
      semver: 6.3.1
      strip-ansi: 6.0.1
    dev: true

  /@vue/compiler-sfc/2.7.14:
    resolution: {integrity: sha1-NEb9L7tnDXCSd/w/+ojvxeEChP0=}
    dependencies:
      '@babel/parser': 7.22.10
      postcss: 8.4.28
      source-map: 0.6.1
    dev: false

  /@vue/component-compiler-utils/3.3.0:
    resolution: {integrity: sha1-+fX7U0ZLDDeyyNLz+/5E32D2Hck=}
    dependencies:
      consolidate: 0.15.1
      hash-sum: 1.0.2
      lru-cache: 4.1.5
      merge-source-map: 1.1.0
      postcss: 7.0.39
      postcss-selector-parser: 6.0.13
      source-map: 0.6.1
      vue-template-es2015-compiler: 1.9.1
    optionalDependencies:
      prettier: 2.8.8
    dev: true

  /@vue/eslint-config-standard/5.1.2_4e9acc927bc5f54b3d304a6e7610b158:
    resolution: {integrity: sha1-xdVa+JSjriO2Wxr0pCV3esAXC0I=}
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0-0
      eslint: '>=6.2.2'
      eslint-plugin-import: '>= 2.18.0'
      eslint-plugin-node: '>= 9.1.0'
      eslint-plugin-promise: '>= 4.2.1'
      eslint-plugin-standard: '>= 4.0.0'
      eslint-plugin-vue: '>= 6.1.2'
    peerDependenciesMeta:
      '@vue/cli-service':
        optional: true
    dependencies:
      '@vue/cli-service': 4.5.19_8f023ad5f6aa6a67fdfe810224770a2b
      eslint: 6.8.0
      eslint-config-standard: 14.1.1_098f0143802d3bc4a66be70e1fb701e2
      eslint-import-resolver-node: 0.3.9
      eslint-import-resolver-webpack: 0.12.2_eslint-plugin-import@2.28.0
      eslint-plugin-import: 2.28.0_eslint@6.8.0
      eslint-plugin-node: 11.1.0_eslint@6.8.0
      eslint-plugin-promise: 4.3.1
      eslint-plugin-standard: 4.1.0_eslint@6.8.0
      eslint-plugin-vue: 6.2.2_eslint@6.8.0
    transitivePeerDependencies:
      - webpack
    dev: true

  /@vue/preload-webpack-plugin/1.1.2_502c618fc8a7d35df07e93275324a2d0:
    resolution: {integrity: sha1-zrkktOyzucQ4ccekKaAvhCPmIas=}
    engines: {node: '>=6.0.0'}
    peerDependencies:
      html-webpack-plugin: '>=2.26.0'
      webpack: '>=4.0.0'
    dependencies:
      html-webpack-plugin: 3.2.0_webpack@4.46.0
      webpack: 4.46.0
    dev: true

  /@vue/web-component-wrapper/1.3.0:
    resolution: {integrity: sha1-trQKdiVCnSvXwigd26YB7QXcfxo=}
    dev: true

  /@webassemblyjs/ast/1.9.0:
    resolution: {integrity: sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=}
    dependencies:
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
    dev: true

  /@webassemblyjs/floating-point-hex-parser/1.9.0:
    resolution: {integrity: sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=}
    dev: true

  /@webassemblyjs/helper-api-error/1.9.0:
    resolution: {integrity: sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=}
    dev: true

  /@webassemblyjs/helper-buffer/1.9.0:
    resolution: {integrity: sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=}
    dev: true

  /@webassemblyjs/helper-code-frame/1.9.0:
    resolution: {integrity: sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=}
    dependencies:
      '@webassemblyjs/wast-printer': 1.9.0
    dev: true

  /@webassemblyjs/helper-fsm/1.9.0:
    resolution: {integrity: sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=}
    dev: true

  /@webassemblyjs/helper-module-context/1.9.0:
    resolution: {integrity: sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
    dev: true

  /@webassemblyjs/helper-wasm-bytecode/1.9.0:
    resolution: {integrity: sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=}
    dev: true

  /@webassemblyjs/helper-wasm-section/1.9.0:
    resolution: {integrity: sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
    dev: true

  /@webassemblyjs/ieee754/1.9.0:
    resolution: {integrity: sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=}
    dependencies:
      '@xtuc/ieee754': 1.2.0
    dev: true

  /@webassemblyjs/leb128/1.9.0:
    resolution: {integrity: sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=}
    dependencies:
      '@xtuc/long': 4.2.2
    dev: true

  /@webassemblyjs/utf8/1.9.0:
    resolution: {integrity: sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=}
    dev: true

  /@webassemblyjs/wasm-edit/1.9.0:
    resolution: {integrity: sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/helper-wasm-section': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-opt': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      '@webassemblyjs/wast-printer': 1.9.0
    dev: true

  /@webassemblyjs/wasm-gen/1.9.0:
    resolution: {integrity: sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0
    dev: true

  /@webassemblyjs/wasm-opt/1.9.0:
    resolution: {integrity: sha1-IhEYHlsxMmRDzIES658LkChyGmE=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
    dev: true

  /@webassemblyjs/wasm-parser/1.9.0:
    resolution: {integrity: sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0
    dev: true

  /@webassemblyjs/wast-parser/1.9.0:
    resolution: {integrity: sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/floating-point-hex-parser': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-code-frame': 1.9.0
      '@webassemblyjs/helper-fsm': 1.9.0
      '@xtuc/long': 4.2.2
    dev: true

  /@webassemblyjs/wast-printer/1.9.0:
    resolution: {integrity: sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
      '@xtuc/long': 4.2.2
    dev: true

  /@xtuc/ieee754/1.2.0:
    resolution: {integrity: sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=}
    dev: true

  /@xtuc/long/4.2.2:
    resolution: {integrity: sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=}
    dev: true

  /JSV/4.0.2:
    resolution: {integrity: sha1-0Hf2glVx+CEy+d/67Vh7QCn+/1c=}
    dev: false

  /accepts/1.3.8:
    resolution: {integrity: sha1-C/C+EltnAUrcsLCSHmLbe//hay4=}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3
    dev: true

  /ace-builds/1.24.1:
    resolution: {integrity: sha1-kMkp2eMbsQkq6p3rMnZ79R93sDQ=}
    dev: false

  /ace/1.3.0:
    resolution: {integrity: sha1-WDW3mcyhx5ZHCQ+8VIf5tUNMoGY=}
    hasBin: true
    dependencies:
      archy: 1.0.0
      async-retry: 0.1.1
      babel-runtime: 6.6.1
      debug: 2.2.0
      easy-table: 1.0.0
      es6-promisify: 4.0.0
      fs-promise: 0.5.0
      got: 5.3.1
      gunzip-maybe: 1.2.1
      https-proxy-agent: 1.0.0
      init-package-json: 1.9.1
      minimist: 1.2.0
      mkdirp-then: 1.2.0
      node-uuid: 1.4.3
      npm-package-arg: 4.1.0
      readdir-scoped-modules: 1.0.2
      semver: 5.0.1
      tar: 2.2.1
    dev: false

  /acorn-jsx/5.3.2_acorn@7.4.1:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 7.4.1
    dev: true

  /acorn-walk/7.2.0:
    resolution: {integrity: sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=}
    engines: {node: '>=0.4.0'}
    dev: true

  /acorn/6.4.2:
    resolution: {integrity: sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /acorn/7.4.1:
    resolution: {integrity: sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /address/1.2.2:
    resolution: {integrity: sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=}
    engines: {node: '>= 10.0.0'}
    dev: true

  /after/0.8.2:
    resolution: {integrity: sha1-/ts5T58OAqqXaOcCvaI7UF+ufh8=}
    dev: false

  /agent-base/2.1.1:
    resolution: {integrity: sha1-1t4Q1a9hMtW9aSQn1G/FOFOQlMc=}
    dependencies:
      extend: 3.0.2
      semver: 5.0.1
    dev: false

  /aggregate-error/3.1.0:
    resolution: {integrity: sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=}
    engines: {node: '>=8'}
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: true

  /ajv-errors/1.0.1_ajv@6.12.6:
    resolution: {integrity: sha1-81mGrOuRr63sQQL72FAUlQzvpk0=}
    peerDependencies:
      ajv: '>=5.0.0'
    dependencies:
      ajv: 6.12.6
    dev: true

  /ajv-keywords/3.5.2_ajv@6.12.6:
    resolution: {integrity: sha1-MfKdpatuANHC0yms97WSlhTVAU0=}
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: 6.12.6

  /ajv/6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  /alphanum-sort/1.0.2:
    resolution: {integrity: sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=}
    dev: true

  /animate.css/4.1.1:
    resolution: {integrity: sha1-YU7FqBEx1+TcNipYFD90BqvWgHU=}
    dev: false

  /ansi-colors/3.2.4:
    resolution: {integrity: sha1-46PaS/uubIapwoViXeEkojQCb78=}
    engines: {node: '>=6'}
    dev: true

  /ansi-escapes/3.2.0:
    resolution: {integrity: sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=}
    engines: {node: '>=4'}
    dev: true

  /ansi-escapes/4.3.2:
    resolution: {integrity: sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: true

  /ansi-html-community/0.0.8:
    resolution: {integrity: sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=}
    engines: {'0': node >= 0.8.0}
    hasBin: true
    dev: true

  /ansi-regex/2.1.1:
    resolution: {integrity: sha1-w7M6te42DYbg5ijwRorn7yfWVN8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-regex/3.0.1:
    resolution: {integrity: sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=}
    engines: {node: '>=4'}
    dev: true

  /ansi-regex/4.1.1:
    resolution: {integrity: sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=}
    engines: {node: '>=6'}
    dev: true

  /ansi-regex/5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=}
    engines: {node: '>=8'}

  /ansi-styles/1.0.0:
    resolution: {integrity: sha1-yxAt8cVvUSPquLZ817mAJ6AnkXg=}
    engines: {node: '>=0.8.0'}
    dev: false

  /ansi-styles/2.2.1:
    resolution: {integrity: sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /ansi-styles/3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: true

  /ansi-styles/4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1

  /any-observable/0.3.0:
    resolution: {integrity: sha1-r5M0deWAamfQ198JDdXovvZdEZs=}
    engines: {node: '>=6'}
    dev: true

  /any-promise/1.3.0:
    resolution: {integrity: sha1-q8av7tzqUugJzcA3au0845Y10X8=}

  /anymatch/2.0.0:
    resolution: {integrity: sha1-vLJLTzeTTZqnrBe0ra+J58du8us=}
    dependencies:
      micromatch: 3.1.10
      normalize-path: 2.1.1
    dev: true

  /anymatch/3.1.3:
    resolution: {integrity: sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    dev: true

  /aproba/1.2.0:
    resolution: {integrity: sha1-aALmJk79GMeQobDVF/DyYnvyyUo=}
    dev: true

  /arch/2.2.0:
    resolution: {integrity: sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE=}
    dev: true

  /archy/1.0.0:
    resolution: {integrity: sha1-+cjBN1fMHde8N5rHeyxipcKGjEA=}
    dev: false

  /argparse/1.0.10:
    resolution: {integrity: sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=}
    dependencies:
      sprintf-js: 1.0.3
    dev: true

  /arr-diff/4.0.0:
    resolution: {integrity: sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=}
    engines: {node: '>=0.10.0'}
    dev: true

  /arr-flatten/1.1.0:
    resolution: {integrity: sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=}
    engines: {node: '>=0.10.0'}
    dev: true

  /arr-union/3.1.0:
    resolution: {integrity: sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /array-buffer-byte-length/1.0.0:
    resolution: {integrity: sha1-+r6LwZP+qGXzF/54Bwhe4N7lrq0=}
    dependencies:
      call-bind: 1.0.2
      is-array-buffer: 3.0.2
    dev: true

  /array-find/1.0.0:
    resolution: {integrity: sha1-bI4obRHtdoMn+OYuzuhzU8o+eLg=}
    dev: true

  /array-flatten/1.1.1:
    resolution: {integrity: sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=}
    dev: true

  /array-flatten/2.1.2:
    resolution: {integrity: sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=}
    dev: true

  /array-includes/3.1.6:
    resolution: {integrity: sha1-np5yDhlPGYJmup4Ywp5qmw5LIl8=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      get-intrinsic: 1.2.1
      is-string: 1.0.7
    dev: true

  /array-union/1.0.2:
    resolution: {integrity: sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-uniq: 1.0.3
    dev: true

  /array-union/2.1.0:
    resolution: {integrity: sha1-t5hCCtvrHego2ErNii4j0+/oXo0=}
    engines: {node: '>=8'}
    dev: true

  /array-uniq/1.0.3:
    resolution: {integrity: sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=}
    engines: {node: '>=0.10.0'}
    dev: true

  /array-unique/0.3.2:
    resolution: {integrity: sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=}
    engines: {node: '>=0.10.0'}
    dev: true

  /array.prototype.findlastindex/1.2.2:
    resolution: {integrity: sha1-vCKa75j2vQUzorxh/5UgmHVSbJs=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      es-shim-unscopables: 1.0.0
      get-intrinsic: 1.2.1
    dev: true

  /array.prototype.flat/1.3.1:
    resolution: {integrity: sha1-/8ZXanyj78L0ahQ7nR3am0s89eI=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      es-shim-unscopables: 1.0.0
    dev: true

  /array.prototype.flatmap/1.3.1:
    resolution: {integrity: sha1-Gq55A8IQBDPLgmHNTtMQqrXEoYM=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      es-shim-unscopables: 1.0.0
    dev: true

  /array.prototype.reduce/1.0.5:
    resolution: {integrity: sha1-ayCw2qnZc03WvH6ma1u845VHHqw=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      es-array-method-boxes-properly: 1.0.0
      is-string: 1.0.7
    dev: true

  /arraybuffer.prototype.slice/1.0.1:
    resolution: {integrity: sha1-m16jhopu68MCc9pXfriIOBwARLs=}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.0
      call-bind: 1.0.2
      define-properties: 1.2.0
      get-intrinsic: 1.2.1
      is-array-buffer: 3.0.2
      is-shared-array-buffer: 1.0.2
    dev: true

  /arraybuffer.slice/0.0.7:
    resolution: {integrity: sha1-O7xCdd1YTMGxCAm4nU6LY6aednU=}
    dev: false

  /asap/2.0.6:
    resolution: {integrity: sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=}
    dev: false

  /asn1.js/5.4.1:
    resolution: {integrity: sha1-EamAuE67kXgc41sP3C7ilON4Pwc=}
    dependencies:
      bn.js: 4.12.0
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      safer-buffer: 2.1.2
    dev: true

  /asn1/0.2.6:
    resolution: {integrity: sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=}
    dependencies:
      safer-buffer: 2.1.2

  /assert-plus/1.0.0:
    resolution: {integrity: sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=}
    engines: {node: '>=0.8'}

  /assert/1.5.0:
    resolution: {integrity: sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=}
    dependencies:
      object-assign: 4.1.1
      util: 0.10.3
    dev: true

  /assign-symbols/1.0.0:
    resolution: {integrity: sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=}
    engines: {node: '>=0.10.0'}
    dev: true

  /astral-regex/1.0.0:
    resolution: {integrity: sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=}
    engines: {node: '>=4'}
    dev: true

  /async-each/1.0.6:
    resolution: {integrity: sha1-UvHZQDgYwXm3Vh4RpdG3frIWDnc=}
    dev: true

  /async-limiter/1.0.1:
    resolution: {integrity: sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=}
    dev: true

  /async-retry/0.1.1:
    resolution: {integrity: sha1-ZBo8xuQOpuR+j/SLrZr68TdV+2A=}
    dependencies:
      babel-runtime: 6.5.0
      retry: 0.9.0
    dev: false

  /async-validator/1.8.5:
    resolution: {integrity: sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A=}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /async/2.6.4:
    resolution: {integrity: sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=}
    dependencies:
      lodash: 4.17.21
    dev: true

  /async/3.2.0:
    resolution: {integrity: sha1-s6JoXF67ZB094C0WEALGD8n4VyA=}
    dev: false

  /asynckit/0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=}

  /atob/2.1.2:
    resolution: {integrity: sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=}
    engines: {node: '>= 4.5.0'}
    hasBin: true
    dev: true

  /autoprefixer/9.8.8:
    resolution: {integrity: sha1-/UvUWVOF+m8GWZ3nSaTV96R0lXo=}
    hasBin: true
    dependencies:
      browserslist: 4.21.10
      caniuse-lite: 1.0.30001521
      normalize-range: 0.1.2
      num2fraction: 1.2.2
      picocolors: 0.2.1
      postcss: 7.0.39
      postcss-value-parser: 4.2.0
    dev: true

  /available-typed-arrays/1.0.5:
    resolution: {integrity: sha1-kvlWFlAQadB9EO2y/DfT4cZRI7c=}
    engines: {node: '>= 0.4'}
    dev: true

  /aws-sign2/0.7.0:
    resolution: {integrity: sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=}

  /aws4/1.12.0:
    resolution: {integrity: sha1-zhydFDOJZ54lOzFCQeqapc7JgNM=}

  /axios/0.21.4:
    resolution: {integrity: sha1-xnuQ3AVo5cHPKwuFjEO6KOLtpXU=}
    dependencies:
      follow-redirects: 1.15.2
    transitivePeerDependencies:
      - debug
    dev: false

  /babel-code-frame/6.26.0:
    resolution: {integrity: sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=}
    dependencies:
      chalk: 1.1.3
      esutils: 2.0.3
      js-tokens: 3.0.2
    dev: true

  /babel-eslint/10.1.0_eslint@6.8.0:
    resolution: {integrity: sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=}
    engines: {node: '>=6'}
    deprecated: babel-eslint is now @babel/eslint-parser. This package will no longer receive updates.
    peerDependencies:
      eslint: '>= 4.12.1'
    dependencies:
      '@babel/code-frame': 7.22.10
      '@babel/parser': 7.22.10
      '@babel/traverse': 7.22.10
      '@babel/types': 7.22.10
      eslint: 6.8.0
      eslint-visitor-keys: 1.3.0
      resolve: 1.22.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-helper-call-delegate/6.24.1:
    resolution: {integrity: sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340=}
    dependencies:
      babel-helper-hoist-variables: 6.24.1
      babel-runtime: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-helper-define-map/6.26.0:
    resolution: {integrity: sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8=}
    dependencies:
      babel-helper-function-name: 6.24.1
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      lodash: 4.17.21
    dev: true

  /babel-helper-function-name/6.24.1:
    resolution: {integrity: sha1-00dbjAPtmCQqJbSDUasYOZ01gKk=}
    dependencies:
      babel-helper-get-function-arity: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-helper-get-function-arity/6.24.1:
    resolution: {integrity: sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-helper-hoist-variables/6.24.1:
    resolution: {integrity: sha1-HssnaJydJVE+rbyZFKc/VAi+enY=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-helper-optimise-call-expression/6.24.1:
    resolution: {integrity: sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-helper-regex/6.26.0:
    resolution: {integrity: sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      lodash: 4.17.21
    dev: true

  /babel-helper-replace-supers/6.24.1:
    resolution: {integrity: sha1-v22/5Dk40XNpohPKiov3S2qQqxo=}
    dependencies:
      babel-helper-optimise-call-expression: 6.24.1
      babel-messages: 6.23.0
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-helper-vue-jsx-merge-props/2.0.3:
    resolution: {integrity: sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY=}
    dev: false

  /babel-loader/8.3.0:
    resolution: {integrity: sha1-Ekk26EG6T+gXZ4bW/yit0fE01qg=}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
    dev: true

  /babel-loader/8.3.0_a894a531581cfdbb33a5fa8f06028c80:
    resolution: {integrity: sha1-Ekk26EG6T+gXZ4bW/yit0fE01qg=}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      '@babel/core': 7.22.10
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 4.46.0
    dev: true

  /babel-messages/6.23.0:
    resolution: {integrity: sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-check-es2015-constants/6.22.0:
    resolution: {integrity: sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-component/1.1.1:
    resolution: {integrity: sha1-mwI6I/9cmq4P1WxaGLnKuMTUXuo=}
    dependencies:
      '@babel/helper-module-imports': 7.0.0-beta.35
    dev: true

  /babel-plugin-dynamic-import-node/2.3.3:
    resolution: {integrity: sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=}
    dependencies:
      object.assign: 4.1.4
    dev: true

  /babel-plugin-polyfill-corejs2/0.4.5:
    resolution: {integrity: sha1-gJe0y0r1tkodETMrb7cu9eZKBUw=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/compat-data': 7.22.9
      '@babel/helper-define-polyfill-provider': 0.4.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-corejs2/0.4.5_@babel+core@7.22.10:
    resolution: {integrity: sha1-gJe0y0r1tkodETMrb7cu9eZKBUw=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/compat-data': 7.22.9
      '@babel/core': 7.22.10
      '@babel/helper-define-polyfill-provider': 0.4.2_@babel+core@7.22.10
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-corejs3/0.8.3:
    resolution: {integrity: sha1-tPcZ0K2buODCPj5jDAyOxt16HFI=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/helper-define-polyfill-provider': 0.4.2
      core-js-compat: 3.32.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-corejs3/0.8.3_@babel+core@7.22.10:
    resolution: {integrity: sha1-tPcZ0K2buODCPj5jDAyOxt16HFI=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-define-polyfill-provider': 0.4.2_@babel+core@7.22.10
      core-js-compat: 3.32.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-regenerator/0.5.2:
    resolution: {integrity: sha1-gNDz4QmMCAyLWmX0HpQnr2ktwyY=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/helper-define-polyfill-provider': 0.4.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-polyfill-regenerator/0.5.2_@babel+core@7.22.10:
    resolution: {integrity: sha1-gNDz4QmMCAyLWmX0HpQnr2ktwyY=}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-define-polyfill-provider': 0.4.2_@babel+core@7.22.10
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-syntax-dynamic-import/6.18.0:
    resolution: {integrity: sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo=}
    dev: false

  /babel-plugin-transform-es2015-arrow-functions/6.22.0:
    resolution: {integrity: sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-block-scoped-functions/6.22.0:
    resolution: {integrity: sha1-u8UbSflk1wy42OC5ToICRs46YUE=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-block-scoping/6.26.0:
    resolution: {integrity: sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8=}
    dependencies:
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
      lodash: 4.17.21
    dev: true

  /babel-plugin-transform-es2015-classes/6.24.1:
    resolution: {integrity: sha1-WkxYpQyclGHlZLSyo7+ryXolhNs=}
    dependencies:
      babel-helper-define-map: 6.26.0
      babel-helper-function-name: 6.24.1
      babel-helper-optimise-call-expression: 6.24.1
      babel-helper-replace-supers: 6.24.1
      babel-messages: 6.23.0
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-computed-properties/6.24.1:
    resolution: {integrity: sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM=}
    dependencies:
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-destructuring/6.23.0:
    resolution: {integrity: sha1-mXux8auWf2gtKwh2/jWNYOdlxW0=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-duplicate-keys/6.24.1:
    resolution: {integrity: sha1-c+s9MQypaePvnskcU3QabxV2Qj4=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-for-of/6.23.0:
    resolution: {integrity: sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-function-name/6.24.1:
    resolution: {integrity: sha1-g0yJhTvDaxrw86TF26qU/Y6sqos=}
    dependencies:
      babel-helper-function-name: 6.24.1
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-literals/6.22.0:
    resolution: {integrity: sha1-T1SgLWzWbPkVKAAZox0xklN3yi4=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-modules-amd/6.24.1:
    resolution: {integrity: sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ=}
    dependencies:
      babel-plugin-transform-es2015-modules-commonjs: 6.26.2
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-modules-commonjs/6.26.2:
    resolution: {integrity: sha1-WKeThjqefKhwvcWogRF/+sJ9tvM=}
    dependencies:
      babel-plugin-transform-strict-mode: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-modules-systemjs/6.24.1:
    resolution: {integrity: sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM=}
    dependencies:
      babel-helper-hoist-variables: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-modules-umd/6.24.1:
    resolution: {integrity: sha1-rJl+YoXNGO1hdq22B9YCNErThGg=}
    dependencies:
      babel-plugin-transform-es2015-modules-amd: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-object-super/6.24.1:
    resolution: {integrity: sha1-JM72muIcuDp/hgPa0CH1cusnj40=}
    dependencies:
      babel-helper-replace-supers: 6.24.1
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-parameters/6.24.1:
    resolution: {integrity: sha1-V6w1GrScrxSpfNE7CfZv3wpiXys=}
    dependencies:
      babel-helper-call-delegate: 6.24.1
      babel-helper-get-function-arity: 6.24.1
      babel-runtime: 6.26.0
      babel-template: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-shorthand-properties/6.24.1:
    resolution: {integrity: sha1-JPh11nIch2YbvZmkYi5R8U3jiqA=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-spread/6.22.0:
    resolution: {integrity: sha1-1taKmfia7cRTbIGlQujdnxdG+NE=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-sticky-regex/6.24.1:
    resolution: {integrity: sha1-AMHNsaynERLN8M9hJsLta0V8zbw=}
    dependencies:
      babel-helper-regex: 6.26.0
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-template-literals/6.22.0:
    resolution: {integrity: sha1-qEs0UPfp+PH2g51taH2oS7EjbY0=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-typeof-symbol/6.23.0:
    resolution: {integrity: sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I=}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-plugin-transform-es2015-unicode-regex/6.24.1:
    resolution: {integrity: sha1-04sS9C6nMj9yk4fxinxa4frrNek=}
    dependencies:
      babel-helper-regex: 6.26.0
      babel-runtime: 6.26.0
      regexpu-core: 2.0.0
    dev: true

  /babel-plugin-transform-regenerator/6.26.0:
    resolution: {integrity: sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8=}
    dependencies:
      regenerator-transform: 0.10.1
    dev: true

  /babel-plugin-transform-strict-mode/6.24.1:
    resolution: {integrity: sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
    dev: true

  /babel-polyfill/6.26.0:
    resolution: {integrity: sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM=}
    dependencies:
      babel-runtime: 6.26.0
      core-js: 2.6.12
      regenerator-runtime: 0.10.5
    dev: false

  /babel-preset-es2015/6.24.1:
    resolution: {integrity: sha1-1EBQ1rwsn+6nAqrzjXJ6AhBTiTk=}
    deprecated: '🙌  Thanks for using Babel: we recommend using babel-preset-env now: please read https://babeljs.io/env to update!'
    dependencies:
      babel-plugin-check-es2015-constants: 6.22.0
      babel-plugin-transform-es2015-arrow-functions: 6.22.0
      babel-plugin-transform-es2015-block-scoped-functions: 6.22.0
      babel-plugin-transform-es2015-block-scoping: 6.26.0
      babel-plugin-transform-es2015-classes: 6.24.1
      babel-plugin-transform-es2015-computed-properties: 6.24.1
      babel-plugin-transform-es2015-destructuring: 6.23.0
      babel-plugin-transform-es2015-duplicate-keys: 6.24.1
      babel-plugin-transform-es2015-for-of: 6.23.0
      babel-plugin-transform-es2015-function-name: 6.24.1
      babel-plugin-transform-es2015-literals: 6.22.0
      babel-plugin-transform-es2015-modules-amd: 6.24.1
      babel-plugin-transform-es2015-modules-commonjs: 6.26.2
      babel-plugin-transform-es2015-modules-systemjs: 6.24.1
      babel-plugin-transform-es2015-modules-umd: 6.24.1
      babel-plugin-transform-es2015-object-super: 6.24.1
      babel-plugin-transform-es2015-parameters: 6.24.1
      babel-plugin-transform-es2015-shorthand-properties: 6.24.1
      babel-plugin-transform-es2015-spread: 6.22.0
      babel-plugin-transform-es2015-sticky-regex: 6.24.1
      babel-plugin-transform-es2015-template-literals: 6.22.0
      babel-plugin-transform-es2015-typeof-symbol: 6.23.0
      babel-plugin-transform-es2015-unicode-regex: 6.24.1
      babel-plugin-transform-regenerator: 6.26.0
    dev: true

  /babel-runtime/6.26.0:
    resolution: {integrity: sha1-llxwWGaOgrVde/4E/yM3vItWR/4=}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1

  /babel-runtime/6.5.0:
    resolution: {integrity: sha1-+3wYiQab2fggAjipvGrVteirW/s=}
    dependencies:
      core-js: 1.2.7
    dev: false

  /babel-runtime/6.6.1:
    resolution: {integrity: sha1-eIuUtvY04luRvWxd9y1GdFevsAA=}
    dependencies:
      core-js: 2.6.12
    dev: false

  /babel-template/6.26.0:
    resolution: {integrity: sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=}
    dependencies:
      babel-runtime: 6.26.0
      babel-traverse: 6.26.0
      babel-types: 6.26.0
      babylon: 6.18.0
      lodash: 4.17.21
    dev: true

  /babel-traverse/6.26.0:
    resolution: {integrity: sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=}
    dependencies:
      babel-code-frame: 6.26.0
      babel-messages: 6.23.0
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      babylon: 6.18.0
      debug: 2.6.9
      globals: 9.18.0
      invariant: 2.2.4
      lodash: 4.17.21
    dev: true

  /babel-types/6.26.0:
    resolution: {integrity: sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=}
    dependencies:
      babel-runtime: 6.26.0
      esutils: 2.0.3
      lodash: 4.17.21
      to-fast-properties: 1.0.3
    dev: true

  /babylon/6.18.0:
    resolution: {integrity: sha1-ry87iPpvXB5MY00aD46sT1WzleM=}
    hasBin: true
    dev: true

  /backo2/1.0.2:
    resolution: {integrity: sha1-MasayLEpNjRj41s+u2n038+6eUc=}
    dev: false

  /balanced-match/1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=}

  /base/0.11.2:
    resolution: {integrity: sha1-e95c7RRbbVUakNuH+DxVi060io8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.0
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1
    dev: true

  /base64-arraybuffer/0.1.4:
    resolution: {integrity: sha1-mBjHngWbE1X5fgQooBfIOOkLqBI=}
    engines: {node: '>= 0.6.0'}
    dev: false

  /base64-arraybuffer/1.0.2:
    resolution: {integrity: sha1-HDdYmnxLB0bjS9H+uVHaLfAcG9w=}
    engines: {node: '>= 0.6.0'}
    dev: false

  /base64-js/1.5.1:
    resolution: {integrity: sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=}
    dev: true

  /batch-processor/1.0.0:
    resolution: {integrity: sha1-dclcMrdI4IUNEMKxaPa9vpiRrOg=}
    dev: false

  /batch/0.6.1:
    resolution: {integrity: sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=}
    dev: true

  /bcrypt-pbkdf/1.0.2:
    resolution: {integrity: sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=}
    dependencies:
      tweetnacl: 0.14.5

  /bfj/6.1.2:
    resolution: {integrity: sha1-MlyGGoIryzWKQceKM7jm4ght3n8=}
    engines: {node: '>= 6.0.0'}
    dependencies:
      bluebird: 3.7.2
      check-types: 8.0.3
      hoopy: 0.1.4
      tryer: 1.0.1
    dev: true

  /big.js/3.2.0:
    resolution: {integrity: sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=}
    dev: true

  /big.js/5.2.2:
    resolution: {integrity: sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=}

  /binary-extensions/1.13.1:
    resolution: {integrity: sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=}
    engines: {node: '>=0.10.0'}
    dev: true

  /binary-extensions/2.2.0:
    resolution: {integrity: sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=}
    engines: {node: '>=8'}
    dev: true

  /bindings/1.5.0:
    resolution: {integrity: sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=}
    dependencies:
      file-uri-to-path: 1.0.0
    dev: true
    optional: true

  /blob/0.0.5:
    resolution: {integrity: sha1-1oDu7yX4zZGtUz9bAe7UjmTK9oM=}
    dev: false

  /block-stream/0.0.9:
    resolution: {integrity: sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=}
    engines: {node: 0.4 || >=0.5.8}
    dependencies:
      inherits: 2.0.4
    dev: false

  /bluebird/3.7.2:
    resolution: {integrity: sha1-nyKcFb4nJFT/qXOs4NvueaGww28=}
    dev: true

  /bn.js/4.12.0:
    resolution: {integrity: sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=}
    dev: true

  /bn.js/5.2.1:
    resolution: {integrity: sha1-C8UnpqDRjQqo1bBTjOSnfcz6e3A=}
    dev: true

  /body-parser/1.20.1:
    resolution: {integrity: sha1-sYEqiRLBlc03Gj7l5m+qIzilxmg=}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.11.0
      raw-body: 2.5.1
      type-is: 1.6.18
      unpipe: 1.0.0
    dev: true

  /bonjour/3.5.0:
    resolution: {integrity: sha1-jokKGD2O6aI5OzhExpGkK897yfU=}
    dependencies:
      array-flatten: 2.1.2
      deep-equal: 1.1.1
      dns-equal: 1.0.0
      dns-txt: 2.0.2
      multicast-dns: 6.2.3
      multicast-dns-service-types: 1.1.0
    dev: true

  /boolbase/1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=}
    dev: true

  /brace-expansion/1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  /brace/0.11.1:
    resolution: {integrity: sha1-SJb8ydVE7vRfS7dmDbMg07N5/lg=}
    dev: false

  /braces/2.3.2:
    resolution: {integrity: sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    dev: true

  /braces/3.0.2:
    resolution: {integrity: sha1-NFThpGLujVmeI23zNs2epPiv4Qc=}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1
    dev: true

  /brorand/1.1.0:
    resolution: {integrity: sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=}
    dev: true

  /browserify-aes/1.2.0:
    resolution: {integrity: sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=}
    dependencies:
      buffer-xor: 1.0.3
      cipher-base: 1.0.4
      create-hash: 1.2.0
      evp_bytestokey: 1.0.3
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /browserify-cipher/1.0.1:
    resolution: {integrity: sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=}
    dependencies:
      browserify-aes: 1.2.0
      browserify-des: 1.0.2
      evp_bytestokey: 1.0.3
    dev: true

  /browserify-des/1.0.2:
    resolution: {integrity: sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=}
    dependencies:
      cipher-base: 1.0.4
      des.js: 1.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /browserify-rsa/4.1.0:
    resolution: {integrity: sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=}
    dependencies:
      bn.js: 5.2.1
      randombytes: 2.1.0
    dev: true

  /browserify-sign/4.2.1:
    resolution: {integrity: sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM=}
    dependencies:
      bn.js: 5.2.1
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      create-hmac: 1.1.7
      elliptic: 6.5.4
      inherits: 2.0.4
      parse-asn1: 5.1.6
      readable-stream: 3.6.2
      safe-buffer: 5.2.1
    dev: true

  /browserify-zlib/0.1.4:
    resolution: {integrity: sha1-uzX4pRn2AOD6a4SFJByXnQFB+y0=}
    dependencies:
      pako: 0.2.9
    dev: false

  /browserify-zlib/0.2.0:
    resolution: {integrity: sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=}
    dependencies:
      pako: 1.0.11
    dev: true

  /browserslist/4.21.10:
    resolution: {integrity: sha1-27rFdmKME9OyIxMyyy7FpG4BW7A=}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001521
      electron-to-chromium: 1.4.494
      node-releases: 2.0.13
      update-browserslist-db: 1.0.11_browserslist@4.21.10
    dev: true

  /buffer-from/1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=}

  /buffer-indexof/1.1.1:
    resolution: {integrity: sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=}
    dev: true

  /buffer-json/2.0.0:
    resolution: {integrity: sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM=}
    dev: true

  /buffer-xor/1.0.3:
    resolution: {integrity: sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=}
    dev: true

  /buffer/4.9.2:
    resolution: {integrity: sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
      isarray: 1.0.0
    dev: true

  /builtin-status-codes/3.0.0:
    resolution: {integrity: sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=}
    dev: true

  /builtins/0.0.7:
    resolution: {integrity: sha1-NVIZzWzxjb58Acx/0tznZc/cVJo=}
    dev: false

  /bytes/3.0.0:
    resolution: {integrity: sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=}
    engines: {node: '>= 0.8'}
    dev: true

  /bytes/3.1.2:
    resolution: {integrity: sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=}
    engines: {node: '>= 0.8'}
    dev: true

  /cacache/12.0.4:
    resolution: {integrity: sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=}
    dependencies:
      bluebird: 3.7.2
      chownr: 1.1.4
      figgy-pudding: 3.5.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      infer-owner: 1.0.4
      lru-cache: 5.1.1
      mississippi: 3.0.0
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      promise-inflight: 1.0.1
      rimraf: 2.7.1
      ssri: 6.0.2
      unique-filename: 1.1.1
      y18n: 4.0.3
    dev: true

  /cacache/15.3.0:
    resolution: {integrity: sha1-3IU4D7L1Vv492kxxm/oOyHWn8es=}
    engines: {node: '>= 10'}
    dependencies:
      '@npmcli/fs': 1.1.1
      '@npmcli/move-file': 1.1.2
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 7.2.3
      infer-owner: 1.0.4
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1
      rimraf: 3.0.2
      ssri: 8.0.1
      tar: 6.1.15
      unique-filename: 1.1.1
    dev: true

  /cache-base/1.0.1:
    resolution: {integrity: sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.0
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0
    dev: true

  /cache-loader/4.1.0_webpack@4.46.0:
    resolution: {integrity: sha1-mUjK41OuwKH8ser9ojAIFuyFOH4=}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      buffer-json: 2.0.0
      find-cache-dir: 3.3.2
      loader-utils: 1.4.2
      mkdirp: 0.5.6
      neo-async: 2.6.2
      schema-utils: 2.7.1
      webpack: 4.46.0
    dev: true

  /call-bind/1.0.2:
    resolution: {integrity: sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=}
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.2.1

  /call-me-maybe/1.0.2:
    resolution: {integrity: sha1-A/lk8ZUiumQ7GwaTrLkVL+IHS6o=}
    dev: true

  /caller-callsite/2.0.0:
    resolution: {integrity: sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=}
    engines: {node: '>=4'}
    dependencies:
      callsites: 2.0.0
    dev: true

  /caller-path/2.0.0:
    resolution: {integrity: sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=}
    engines: {node: '>=4'}
    dependencies:
      caller-callsite: 2.0.0
    dev: true

  /callsites/2.0.0:
    resolution: {integrity: sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=}
    engines: {node: '>=4'}
    dev: true

  /callsites/3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=}
    engines: {node: '>=6'}
    dev: true

  /camel-case/3.0.0:
    resolution: {integrity: sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=}
    dependencies:
      no-case: 2.3.2
      upper-case: 1.1.3
    dev: true

  /camelcase/5.3.1:
    resolution: {integrity: sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=}
    engines: {node: '>=6'}

  /camelcase/6.3.0:
    resolution: {integrity: sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=}
    engines: {node: '>=10'}
    dev: true

  /caniuse-api/3.0.0:
    resolution: {integrity: sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=}
    dependencies:
      browserslist: 4.21.10
      caniuse-lite: 1.0.30001521
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0
    dev: true

  /caniuse-lite/1.0.30001521:
    resolution: {integrity: sha1-6ZMM9Jn3wegDNLbB+8pS4A2InlY=}
    dev: true

  /capture-stack-trace/1.0.2:
    resolution: {integrity: sha1-HEP2sFnUJJ5/P4ck8V8Ei5J9Ooo=}
    engines: {node: '>=0.10.0'}
    dev: false

  /case-sensitive-paths-webpack-plugin/2.4.0:
    resolution: {integrity: sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ=}
    engines: {node: '>=4'}
    dev: true

  /caseless/0.12.0:
    resolution: {integrity: sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=}

  /chalk/0.4.0:
    resolution: {integrity: sha1-UZmj3c0MHv4jvAjBsCewYXbgxk8=}
    engines: {node: '>=0.8.0'}
    dependencies:
      ansi-styles: 1.0.0
      has-color: 0.1.7
      strip-ansi: 0.1.1
    dev: false

  /chalk/1.1.3:
    resolution: {integrity: sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0
    dev: true

  /chalk/2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: true

  /chalk/3.0.0:
    resolution: {integrity: sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chalk/4.1.0:
    resolution: {integrity: sha1-ThSHCmGNni7dl92DRf2dncMVZGo=}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: false

  /chalk/4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chardet/0.7.0:
    resolution: {integrity: sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=}
    dev: true

  /check-types/8.0.3:
    resolution: {integrity: sha1-M1bMoZyIlUTy16le1JzlCKDs9VI=}
    dev: true

  /chokidar/2.1.8:
    resolution: {integrity: sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=}
    deprecated: Chokidar 2 does not receive security updates since 2019. Upgrade to chokidar 3 with 15x fewer dependencies
    dependencies:
      anymatch: 2.0.0
      async-each: 1.0.6
      braces: 2.3.2
      glob-parent: 3.1.0
      inherits: 2.0.4
      is-binary-path: 1.0.1
      is-glob: 4.0.3
      normalize-path: 3.0.0
      path-is-absolute: 1.0.1
      readdirp: 2.2.1
      upath: 1.2.0
    optionalDependencies:
      fsevents: 1.2.13
    dev: true

  /chokidar/3.5.3:
    resolution: {integrity: sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70=}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.2
    dev: true

  /chownr/1.1.4:
    resolution: {integrity: sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=}
    dev: true

  /chownr/2.0.0:
    resolution: {integrity: sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=}
    engines: {node: '>=10'}
    dev: true

  /chrome-trace-event/1.0.3:
    resolution: {integrity: sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw=}
    engines: {node: '>=6.0'}
    dev: true

  /ci-info/1.6.0:
    resolution: {integrity: sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=}
    dev: true

  /cipher-base/1.0.4:
    resolution: {integrity: sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /class-utils/0.3.6:
    resolution: {integrity: sha1-+TNprouafOAv1B+q0MqDAzGQxGM=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2
    dev: true

  /clean-css/4.2.4:
    resolution: {integrity: sha1-czv0brpOYHxokepXwkqYk1aDEXg=}
    engines: {node: '>= 4.0'}
    dependencies:
      source-map: 0.6.1
    dev: true

  /clean-stack/2.2.0:
    resolution: {integrity: sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=}
    engines: {node: '>=6'}
    dev: true

  /cli-cursor/2.1.0:
    resolution: {integrity: sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=}
    engines: {node: '>=4'}
    dependencies:
      restore-cursor: 2.0.0
    dev: true

  /cli-cursor/3.1.0:
    resolution: {integrity: sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: true

  /cli-highlight/2.1.11:
    resolution: {integrity: sha1-SXNvpFLwqvT65YDjCssmgo0twb8=}
    engines: {node: '>=8.0.0', npm: '>=5.0.0'}
    hasBin: true
    dependencies:
      chalk: 4.1.2
      highlight.js: 10.7.3
      mz: 2.7.0
      parse5: 5.1.1
      parse5-htmlparser2-tree-adapter: 6.0.1
      yargs: 16.2.0
    dev: true

  /cli-spinners/2.9.0:
    resolution: {integrity: sha1-WIHQrZY4HhF7vgetkfIAj+b/2Ns=}
    engines: {node: '>=6'}
    dev: true

  /cli-truncate/0.2.1:
    resolution: {integrity: sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      slice-ansi: 0.0.4
      string-width: 1.0.2
    dev: true

  /cli-width/3.0.0:
    resolution: {integrity: sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=}
    engines: {node: '>= 10'}
    dev: true

  /cli/1.0.1:
    resolution: {integrity: sha1-IoF1NPJL+klQw01TLUjsvGIbjBQ=}
    engines: {node: '>=0.2.5'}
    dependencies:
      exit: 0.1.2
      glob: 7.2.3
    dev: false

  /clipboard/2.0.11:
    resolution: {integrity: sha1-YhgDYLl91mi2s6hOwiaXV2KnC+U=}
    dependencies:
      good-listener: 1.2.2
      select: 1.1.2
      tiny-emitter: 2.1.0
    dev: false

  /clipboardy/2.3.0:
    resolution: {integrity: sha1-PCkDZQxo5GqRs4iYW8J3QofbopA=}
    engines: {node: '>=8'}
    dependencies:
      arch: 2.2.0
      execa: 1.0.0
      is-wsl: 2.2.0
    dev: true

  /cliui/5.0.0:
    resolution: {integrity: sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=}
    dependencies:
      string-width: 3.1.0
      strip-ansi: 5.2.0
      wrap-ansi: 5.1.0
    dev: true

  /cliui/6.0.0:
    resolution: {integrity: sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0

  /cliui/7.0.4:
    resolution: {integrity: sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: true

  /clone-deep/4.0.1:
    resolution: {integrity: sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=}
    engines: {node: '>=6'}
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1
    dev: true

  /clone/1.0.4:
    resolution: {integrity: sha1-2jCcwmPfFZlMaIypAheco8fNfH4=}
    engines: {node: '>=0.8'}
    dev: true

  /clone/2.1.2:
    resolution: {integrity: sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=}
    engines: {node: '>=0.8'}

  /coa/2.0.2:
    resolution: {integrity: sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=}
    engines: {node: '>= 4.0'}
    dependencies:
      '@types/q': 1.5.5
      chalk: 2.4.2
      q: 1.5.1
    dev: true

  /code-point-at/1.1.0:
    resolution: {integrity: sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=}
    engines: {node: '>=0.10.0'}
    dev: true

  /codemirror/5.65.14:
    resolution: {integrity: sha1-51+8ckdFPxuqcUY8M7Uq26fkGyo=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/codemirror/-/codemirror-5.65.14.tgz}
    dev: false

  /collection-visit/1.0.0:
    resolution: {integrity: sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1
    dev: true

  /color-convert/1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=}
    dependencies:
      color-name: 1.1.3
    dev: true

  /color-convert/2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4

  /color-name/1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=}
    dev: true

  /color-name/1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=}

  /color-string/1.9.1:
    resolution: {integrity: sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=}
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    dev: true

  /color/3.2.1:
    resolution: {integrity: sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=}
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1
    dev: true

  /combined-stream/1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0

  /commander/2.17.1:
    resolution: {integrity: sha1-vXerfebelCBc6sxy8XFtKfIKd78=}
    dev: true

  /commander/2.19.0:
    resolution: {integrity: sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=}
    dev: true

  /commander/2.20.3:
    resolution: {integrity: sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=}
    dev: true

  /commander/5.1.0:
    resolution: {integrity: sha1-Rqu9FlL44Fm92u+Zu9yyrZzxea4=}
    engines: {node: '>= 6'}
    dev: false

  /commondir/1.0.1:
    resolution: {integrity: sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=}
    dev: true

  /component-bind/1.0.0:
    resolution: {integrity: sha1-AMYIq33Nk4l8AAllGx06jh5zu9E=}
    dev: false

  /component-emitter/1.3.0:
    resolution: {integrity: sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=}

  /component-inherit/0.0.3:
    resolution: {integrity: sha1-ZF/ErfWLcrZJ1crmUTVhnbJv8UM=}
    dev: false

  /compressible/2.0.18:
    resolution: {integrity: sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: true

  /compression-webpack-plugin/5.0.2:
    resolution: {integrity: sha1-34Tmgs+h+yojDnHPg9UMMj1TacI=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cacache: 15.3.0
      find-cache-dir: 3.3.2
      schema-utils: 2.7.1
      serialize-javascript: 4.0.0
      webpack-sources: 1.4.3
    dev: true

  /compression/1.7.4:
    resolution: {integrity: sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: 1.3.8
      bytes: 3.0.0
      compressible: 2.0.18
      debug: 2.6.9
      on-headers: 1.0.2
      safe-buffer: 5.1.2
      vary: 1.1.2
    dev: true

  /concat-map/0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}

  /concat-stream/1.6.2:
    resolution: {integrity: sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=}
    engines: {'0': node >= 0.8}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.8
      typedarray: 0.0.6
    dev: true

  /connect-history-api-fallback/1.6.0:
    resolution: {integrity: sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=}
    engines: {node: '>=0.8'}
    dev: true

  /console-browserify/1.1.0:
    resolution: {integrity: sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=}
    dependencies:
      date-now: 0.1.4
    dev: false

  /console-browserify/1.2.0:
    resolution: {integrity: sha1-ZwY871fOts9Jk6KrOlWECujEkzY=}
    dev: true

  /consolidate/0.15.1:
    resolution: {integrity: sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=}
    engines: {node: '>= 0.10.0'}
    deprecated: Please upgrade to consolidate v1.0.0+ as it has been modernized with several long-awaited fixes implemented. Maintenance is supported by Forward Email at https://forwardemail.net ; follow/watch https://github.com/ladjs/consolidate for updates and release changelog
    dependencies:
      bluebird: 3.7.2
    dev: true

  /constants-browserify/1.0.0:
    resolution: {integrity: sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=}
    dev: true

  /content-disposition/0.5.4:
    resolution: {integrity: sha1-i4K076yCUSoCuwsdzsnSxejrW/4=}
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /content-type/1.0.5:
    resolution: {integrity: sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=}
    engines: {node: '>= 0.6'}
    dev: true

  /convert-source-map/1.9.0:
    resolution: {integrity: sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=}
    dev: true

  /cookie-signature/1.0.6:
    resolution: {integrity: sha1-4wOogrNCzD7oylE6eZmXNNqzriw=}
    dev: true

  /cookie/0.5.0:
    resolution: {integrity: sha1-0fXXGt7GVYxY84mYfDZqpH6ZT4s=}
    engines: {node: '>= 0.6'}
    dev: true

  /copy-concurrently/1.0.5:
    resolution: {integrity: sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=}
    dependencies:
      aproba: 1.2.0
      fs-write-stream-atomic: 1.0.10
      iferr: 0.1.5
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: true

  /copy-descriptor/0.1.1:
    resolution: {integrity: sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=}
    engines: {node: '>=0.10.0'}
    dev: true

  /copy-webpack-plugin/5.1.2_webpack@4.46.0:
    resolution: {integrity: sha1-ioieHcr6bJHGzUvhrRWPHTgjuuI=}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      glob-parent: 3.1.0
      globby: 7.1.1
      is-glob: 4.0.3
      loader-utils: 1.4.2
      minimatch: 3.1.2
      normalize-path: 3.0.0
      p-limit: 2.3.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      webpack: 4.46.0
      webpack-log: 2.0.0
    dev: true

  /core-js-compat/3.32.0:
    resolution: {integrity: sha1-9BV0tok6sV3bCsFpNoG9VshVCpA=}
    dependencies:
      browserslist: 4.21.10
    dev: true

  /core-js/1.2.7:
    resolution: {integrity: sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY=}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    dev: false

  /core-js/2.6.12:
    resolution: {integrity: sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    requiresBuild: true

  /core-js/3.32.0:
    resolution: {integrity: sha1-dkPTU9iZdHqx+LA9KAOwMSoPs7Y=}
    requiresBuild: true

  /core-util-is/1.0.2:
    resolution: {integrity: sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=}

  /core-util-is/1.0.3:
    resolution: {integrity: sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=}

  /cosmiconfig/5.2.1:
    resolution: {integrity: sha1-BA9yaAnFked6F8CjYmykW08Wixo=}
    engines: {node: '>=4'}
    dependencies:
      import-fresh: 2.0.0
      is-directory: 0.3.1
      js-yaml: 3.14.1
      parse-json: 4.0.0
    dev: true

  /create-ecdh/4.0.4:
    resolution: {integrity: sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=}
    dependencies:
      bn.js: 4.12.0
      elliptic: 6.5.4
    dev: true

  /create-error-class/2.0.1:
    resolution: {integrity: sha1-qHWe1cjSFKRh6B0Y5wqssz3WPJw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      capture-stack-trace: 1.0.2
      inherits: 2.0.4
    dev: false

  /create-hash/1.2.0:
    resolution: {integrity: sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=}
    dependencies:
      cipher-base: 1.0.4
      inherits: 2.0.4
      md5.js: 1.3.5
      ripemd160: 2.0.2
      sha.js: 2.4.11
    dev: true

  /create-hmac/1.1.7:
    resolution: {integrity: sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=}
    dependencies:
      cipher-base: 1.0.4
      create-hash: 1.2.0
      inherits: 2.0.4
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: true

  /cropperjs/1.5.13:
    resolution: {integrity: sha1-6xaC8B0Xxw7VJEMXCR10XJoknvg=}
    dev: false

  /cross-spawn/5.1.0:
    resolution: {integrity: sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=}
    dependencies:
      lru-cache: 4.1.5
      shebang-command: 1.2.0
      which: 1.3.1
    dev: true

  /cross-spawn/6.0.5:
    resolution: {integrity: sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=}
    engines: {node: '>=4.8'}
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.2
      shebang-command: 1.2.0
      which: 1.3.1
    dev: true

  /cross-spawn/7.0.3:
    resolution: {integrity: sha1-9zqFudXUHQRVUcF34ogtSshXKKY=}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /crypto-browserify/3.12.0:
    resolution: {integrity: sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=}
    dependencies:
      browserify-cipher: 1.0.1
      browserify-sign: 4.2.1
      create-ecdh: 4.0.4
      create-hash: 1.2.0
      create-hmac: 1.1.7
      diffie-hellman: 5.0.3
      inherits: 2.0.4
      pbkdf2: 3.1.2
      public-encrypt: 4.0.3
      randombytes: 2.1.0
      randomfill: 1.0.4
    dev: true

  /crypto-js/4.1.1:
    resolution: {integrity: sha1-nkhbzwNSEEG9hYRHhrg/t2GXNs8=}
    dev: false

  /css-color-names/0.0.4:
    resolution: {integrity: sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=}
    dev: true

  /css-declaration-sorter/4.0.1:
    resolution: {integrity: sha1-wZiUD2OnbX42wecQGLABchBUyyI=}
    engines: {node: '>4'}
    dependencies:
      postcss: 7.0.39
      timsort: 0.3.0
    dev: true

  /css-line-break/2.1.0:
    resolution: {integrity: sha1-v+9mDfpvU5fqVBFrs8tIc+28T6A=}
    dependencies:
      utrie: 1.0.2
    dev: false

  /css-loader/3.6.0_webpack@4.46.0:
    resolution: {integrity: sha1-Lkssfm4tJ/jI8o9hv/zS5ske9kU=}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      camelcase: 5.3.1
      cssesc: 3.0.0
      icss-utils: 4.1.1
      loader-utils: 1.4.2
      normalize-path: 3.0.0
      postcss: 7.0.39
      postcss-modules-extract-imports: 2.0.0
      postcss-modules-local-by-default: 3.0.3
      postcss-modules-scope: 2.2.0
      postcss-modules-values: 3.0.0
      postcss-value-parser: 4.2.0
      schema-utils: 2.7.1
      semver: 6.3.1
      webpack: 4.46.0
    dev: true

  /css-select-base-adapter/0.1.1:
    resolution: {integrity: sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=}
    dev: true

  /css-select/2.1.0:
    resolution: {integrity: sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=}
    dependencies:
      boolbase: 1.0.0
      css-what: 3.4.2
      domutils: 1.7.0
      nth-check: 1.0.2
    dev: true

  /css-select/4.3.0:
    resolution: {integrity: sha1-23EpsoRmYv2GKM/ElquytZ5BUps=}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1
    dev: true

  /css-tree/1.0.0-alpha.37:
    resolution: {integrity: sha1-mL69YsTB2flg7DQM+fdSLjBwmiI=}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.4
      source-map: 0.6.1
    dev: true

  /css-tree/1.1.3:
    resolution: {integrity: sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1
    dev: true

  /css-what/3.4.2:
    resolution: {integrity: sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=}
    engines: {node: '>= 6'}
    dev: true

  /css-what/6.1.0:
    resolution: {integrity: sha1-+17/z3bx3eosgb36pN5E55uscPQ=}
    engines: {node: '>= 6'}
    dev: true

  /cssesc/3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /csslint/1.0.5:
    resolution: {integrity: sha1-Gcw+2jIhYP0/cjKvHLKjYOiYouk=}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      clone: 2.1.2
      parserlib: 1.1.1
    dev: false

  /cssnano-preset-default/4.0.8:
    resolution: {integrity: sha1-kgYisfwelaNOiDggPxOXpQTy0/8=}
    engines: {node: '>=6.9.0'}
    dependencies:
      css-declaration-sorter: 4.0.1
      cssnano-util-raw-cache: 4.0.1
      postcss: 7.0.39
      postcss-calc: 7.0.5
      postcss-colormin: 4.0.3
      postcss-convert-values: 4.0.1
      postcss-discard-comments: 4.0.2
      postcss-discard-duplicates: 4.0.2
      postcss-discard-empty: 4.0.1
      postcss-discard-overridden: 4.0.1
      postcss-merge-longhand: 4.0.11
      postcss-merge-rules: 4.0.3
      postcss-minify-font-values: 4.0.2
      postcss-minify-gradients: 4.0.2
      postcss-minify-params: 4.0.2
      postcss-minify-selectors: 4.0.2
      postcss-normalize-charset: 4.0.1
      postcss-normalize-display-values: 4.0.2
      postcss-normalize-positions: 4.0.2
      postcss-normalize-repeat-style: 4.0.2
      postcss-normalize-string: 4.0.2
      postcss-normalize-timing-functions: 4.0.2
      postcss-normalize-unicode: 4.0.1
      postcss-normalize-url: 4.0.1
      postcss-normalize-whitespace: 4.0.2
      postcss-ordered-values: 4.1.2
      postcss-reduce-initial: 4.0.3
      postcss-reduce-transforms: 4.0.2
      postcss-svgo: 4.0.3
      postcss-unique-selectors: 4.0.1
    dev: true

  /cssnano-util-get-arguments/4.0.0:
    resolution: {integrity: sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=}
    engines: {node: '>=6.9.0'}
    dev: true

  /cssnano-util-get-match/4.0.0:
    resolution: {integrity: sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=}
    engines: {node: '>=6.9.0'}
    dev: true

  /cssnano-util-raw-cache/4.0.1:
    resolution: {integrity: sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /cssnano-util-same-parent/4.0.1:
    resolution: {integrity: sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=}
    engines: {node: '>=6.9.0'}
    dev: true

  /cssnano/4.1.11:
    resolution: {integrity: sha1-x7X1uB2iacsf2YLLlgwSAJEMmpk=}
    engines: {node: '>=6.9.0'}
    dependencies:
      cosmiconfig: 5.2.1
      cssnano-preset-default: 4.0.8
      is-resolvable: 1.1.0
      postcss: 7.0.39
    dev: true

  /csso/4.2.0:
    resolution: {integrity: sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-tree: 1.1.3
    dev: true

  /csstype/3.1.2:
    resolution: {integrity: sha1-HUv51XLxHBQDHwQ24cELwfVx9Qs=}
    dev: false

  /cyclist/1.0.2:
    resolution: {integrity: sha1-ZztfIzvzTY5gK5SUKfgXHZEhvqM=}
    dev: true

  /dashdash/1.14.1:
    resolution: {integrity: sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=}
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: 1.0.0

  /date-fns/1.30.1:
    resolution: {integrity: sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw=}
    dev: true

  /date-now/0.1.4:
    resolution: {integrity: sha1-6vQ5/U1ISK105cx9vvIAZyueNFs=}
    dev: false

  /de-indent/1.0.2:
    resolution: {integrity: sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=}
    dev: true

  /debug/2.2.0:
    resolution: {integrity: sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=}
    dependencies:
      ms: 0.7.1
    dev: false

  /debug/2.6.9:
    resolution: {integrity: sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=}
    dependencies:
      ms: 2.0.0
    dev: true

  /debug/3.1.0:
    resolution: {integrity: sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=}
    dependencies:
      ms: 2.0.0
    dev: false

  /debug/3.2.7:
    resolution: {integrity: sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=}
    dependencies:
      ms: 2.1.3
    dev: true

  /debug/4.3.4:
    resolution: {integrity: sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /debug/4.3.4_supports-color@6.1.0:
    resolution: {integrity: sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
      supports-color: 6.1.0
    dev: true

  /debuglog/1.0.1:
    resolution: {integrity: sha1-qiT/uaw9+aI1GDfPstJ5NgzXhJI=}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    dev: false

  /decamelize/1.2.0:
    resolution: {integrity: sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=}
    engines: {node: '>=0.10.0'}

  /decode-uri-component/0.2.2:
    resolution: {integrity: sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek=}
    engines: {node: '>=0.10'}
    dev: true

  /dedent/0.7.0:
    resolution: {integrity: sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=}
    dev: true

  /deep-equal/1.1.1:
    resolution: {integrity: sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=}
    dependencies:
      is-arguments: 1.1.1
      is-date-object: 1.0.5
      is-regex: 1.1.4
      object-is: 1.1.5
      object-keys: 1.1.1
      regexp.prototype.flags: 1.5.0
    dev: true

  /deep-is/0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=}
    dev: true

  /deepmerge/1.3.2:
    resolution: {integrity: sha1-FmNpFinU2/42T6EqKk8KqGqjoFA=}
    engines: {node: '>=0.10.0'}
    dev: true

  /deepmerge/1.5.2:
    resolution: {integrity: sha1-EEmdhohEza1P7ghC34x/bwyVp1M=}
    engines: {node: '>=0.10.0'}

  /default-gateway/4.2.0:
    resolution: {integrity: sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=}
    engines: {node: '>=6'}
    dependencies:
      execa: 1.0.0
      ip-regex: 2.1.0
    dev: true

  /default-gateway/5.0.5:
    resolution: {integrity: sha1-T9a9XShV05s0zFpZUFSG6ar8mxA=}
    engines: {node: ^8.12.0 || >=9.7.0}
    dependencies:
      execa: 3.4.0
    dev: true

  /defaults/1.0.4:
    resolution: {integrity: sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=}
    dependencies:
      clone: 1.0.4
    dev: true

  /define-properties/1.2.0:
    resolution: {integrity: sha1-UpiFcGcMnqzt2AZPSpkPJAWEm9U=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-property-descriptors: 1.0.0
      object-keys: 1.1.1
    dev: true

  /define-property/0.2.5:
    resolution: {integrity: sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 0.1.6
    dev: true

  /define-property/1.0.0:
    resolution: {integrity: sha1-dp66rz9KY6rTr56NMEybvnm/sOY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2
    dev: true

  /define-property/2.0.2:
    resolution: {integrity: sha1-1Flono1lS6d+AqgX+HENcCyxbp0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.2
      isobject: 3.0.1
    dev: true

  /del/4.1.1:
    resolution: {integrity: sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=}
    engines: {node: '>=6'}
    dependencies:
      '@types/glob': 7.2.0
      globby: 6.1.0
      is-path-cwd: 2.2.0
      is-path-in-cwd: 2.1.0
      p-map: 2.1.0
      pify: 4.0.1
      rimraf: 2.7.1
    dev: true

  /del/5.1.0:
    resolution: {integrity: sha1-2Uh8lONnQQ5u/ykl7ljAyEp1s6c=}
    engines: {node: '>=8'}
    dependencies:
      globby: 10.0.2
      graceful-fs: 4.2.11
      is-glob: 4.0.3
      is-path-cwd: 2.2.0
      is-path-inside: 3.0.3
      p-map: 3.0.0
      rimraf: 3.0.2
      slash: 3.0.0
    dev: true

  /delayed-stream/1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=}
    engines: {node: '>=0.4.0'}

  /delegate/3.2.0:
    resolution: {integrity: sha1-tmtxwxWFIuirV0T3INjKDCr1kWY=}
    dev: false

  /depd/1.1.2:
    resolution: {integrity: sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=}
    engines: {node: '>= 0.6'}
    dev: true

  /depd/2.0.0:
    resolution: {integrity: sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=}
    engines: {node: '>= 0.8'}
    dev: true

  /des.js/1.1.0:
    resolution: {integrity: sha1-HTf1dm87v/Tuljjocah2jBc7gdo=}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: true

  /destroy/1.2.0:
    resolution: {integrity: sha1-SANzVQmti+VSk0xn32FPlOZvoBU=}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dev: true

  /detect-node/2.1.0:
    resolution: {integrity: sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=}
    dev: true

  /dezalgo/1.0.4:
    resolution: {integrity: sha1-dRI1JgRpCEwTIVffqFfzhtTDPYE=}
    dependencies:
      asap: 2.0.6
      wrappy: 1.0.2
    dev: false

  /diff-match-patch/1.0.5:
    resolution: {integrity: sha1-q7WE1fEM0Rlt/FWqA3AVkq4/ezc=}
    dev: false

  /diffie-hellman/5.0.3:
    resolution: {integrity: sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=}
    dependencies:
      bn.js: 4.12.0
      miller-rabin: 4.0.1
      randombytes: 2.1.0
    dev: true

  /dijkstrajs/1.0.3:
    resolution: {integrity: sha1-TI296h8PZHi/+U2cSceE1iPk/CM=}
    dev: false

  /dir-glob/2.2.2:
    resolution: {integrity: sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=}
    engines: {node: '>=4'}
    dependencies:
      path-type: 3.0.0
    dev: true

  /dir-glob/3.0.1:
    resolution: {integrity: sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: true

  /dns-equal/1.0.0:
    resolution: {integrity: sha1-s55/HabrCnW6nBcySzR1PEfgZU0=}
    dev: true

  /dns-packet/1.3.4:
    resolution: {integrity: sha1-40VQZYJKJQe6iGxVqJljuxB97G8=}
    dependencies:
      ip: 1.1.8
      safe-buffer: 5.2.1
    dev: true

  /dns-txt/2.0.2:
    resolution: {integrity: sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=}
    dependencies:
      buffer-indexof: 1.1.1
    dev: true

  /doctrine/2.1.0:
    resolution: {integrity: sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /doctrine/3.0.0:
    resolution: {integrity: sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /dom-converter/0.2.0:
    resolution: {integrity: sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=}
    dependencies:
      utila: 0.4.0
    dev: true

  /dom-serializer/0.2.2:
    resolution: {integrity: sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=}
    dependencies:
      domelementtype: 2.3.0
      entities: 2.2.0

  /dom-serializer/1.4.1:
    resolution: {integrity: sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0
    dev: true

  /dom7/2.1.5:
    resolution: {integrity: sha1-p5QRAXgAsx2EAAcM2uu/ySwfY3c=}
    dependencies:
      ssr-window: 2.0.0
    dev: false

  /domain-browser/1.2.0:
    resolution: {integrity: sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=}
    engines: {node: '>=0.4', npm: '>=1.2'}
    dev: true

  /domelementtype/1.3.1:
    resolution: {integrity: sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=}

  /domelementtype/2.3.0:
    resolution: {integrity: sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=}

  /domhandler/2.3.0:
    resolution: {integrity: sha1-LeWaCCLVAn+r/28DLCsloqir5zg=}
    dependencies:
      domelementtype: 1.3.1
    dev: false

  /domhandler/2.4.2:
    resolution: {integrity: sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=}
    dependencies:
      domelementtype: 1.3.1
    dev: true

  /domhandler/4.3.1:
    resolution: {integrity: sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: true

  /domready/1.0.8:
    resolution: {integrity: sha1-kfJS5Ze2Wvd+dFriTdAYXV4m1Yw=}
    dev: true

  /domutils/1.5.1:
    resolution: {integrity: sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=}
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1
    dev: false

  /domutils/1.7.0:
    resolution: {integrity: sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=}
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1
    dev: true

  /domutils/2.8.0:
    resolution: {integrity: sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=}
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1
    dev: true

  /dot-prop/5.3.0:
    resolution: {integrity: sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=}
    engines: {node: '>=8'}
    dependencies:
      is-obj: 2.0.0
    dev: true

  /dotenv-expand/5.1.0:
    resolution: {integrity: sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=}
    dev: true

  /dotenv/8.6.0:
    resolution: {integrity: sha1-Bhr2ZNGff02PxuT/m1hM4jety4s=}
    engines: {node: '>=10'}
    dev: true

  /duplexer/0.1.2:
    resolution: {integrity: sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=}
    dev: true

  /duplexer2/0.1.4:
    resolution: {integrity: sha1-ixLauHjA1p4+eJEFFmKjL8a93ME=}
    dependencies:
      readable-stream: 2.3.8
    dev: false

  /duplexify/3.7.1:
    resolution: {integrity: sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=}
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 2.3.8
      stream-shift: 1.0.1

  /easings-css/1.0.0:
    resolution: {integrity: sha1-3eVpADu3pKDAt3h49ds+C+VnnIE=}
    dev: false

  /easy-stack/1.0.1:
    resolution: {integrity: sha1-iv5CZGJpiMq7EfPHBMzQyDVBEGY=}
    engines: {node: '>=6.0.0'}
    dev: true

  /easy-table/1.0.0:
    resolution: {integrity: sha1-KdstCFXTYxbkOC5aPYXZy1/JMhY=}
    dev: false

  /ecc-jsbn/0.1.2:
    resolution: {integrity: sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=}
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  /echarts/4.9.0:
    resolution: {integrity: sha1-qbm6oD8Doqcx5jQMVb77V6nhNH0=}
    dependencies:
      zrender: 4.3.2
    dev: false

  /ee-first/1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=}
    dev: true

  /ejs/2.7.4:
    resolution: {integrity: sha1-SGYSh1c9zFPjZsehrlLDoSDuybo=}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dev: true

  /electron-to-chromium/1.4.494:
    resolution: {integrity: sha1-WI96PRnTKjHzp+BdgbYdldJbFVU=}
    dev: true

  /elegant-spinner/1.0.1:
    resolution: {integrity: sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4=}
    engines: {node: '>=0.10.0'}
    dev: true

  /element-resize-detector/1.2.4:
    resolution: {integrity: sha1-PmxZgt13UItfp+bVwCFw4mMlybE=}
    dependencies:
      batch-processor: 1.0.0
    dev: false

  /element-ui/2.15.13_vue@2.7.14:
    resolution: {integrity: sha1-OA8BnufRWxgRBVh7Qf1ZFMMIoUM=}
    peerDependencies:
      vue: ^2.5.17
    dependencies:
      async-validator: 1.8.5
      babel-helper-vue-jsx-merge-props: 2.0.3
      deepmerge: 1.5.2
      normalize-wheel: 1.0.1
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 1.1.0
      vue: 2.7.14
    dev: false

  /elliptic/6.5.4:
    resolution: {integrity: sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s=}
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
      hash.js: 1.1.7
      hmac-drbg: 1.0.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: true

  /emoji-regex/7.0.3:
    resolution: {integrity: sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=}
    dev: true

  /emoji-regex/8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=}

  /emojis-list/2.1.0:
    resolution: {integrity: sha1-TapNnbAPmBmIDHn6RXrlsJof04k=}
    engines: {node: '>= 0.10'}
    dev: true

  /emojis-list/3.0.0:
    resolution: {integrity: sha1-VXBmIEatKeLpFucariYKvf9Pang=}
    engines: {node: '>= 4'}

  /encode-utf8/1.0.3:
    resolution: {integrity: sha1-8w/dMdoH+1lvKBvrL2sCeFGZTNo=}
    dev: false

  /encodeurl/1.0.2:
    resolution: {integrity: sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=}
    engines: {node: '>= 0.8'}
    dev: true

  /end-of-stream/1.4.4:
    resolution: {integrity: sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=}
    dependencies:
      once: 1.4.0

  /engine.io-client/3.5.3:
    resolution: {integrity: sha1-MlT2H9vVNQPcmm+dRqUlKIccoNc=}
    dependencies:
      component-emitter: 1.3.0
      component-inherit: 0.0.3
      debug: 3.1.0
      engine.io-parser: 2.2.1
      has-cors: 1.1.0
      indexof: 0.0.1
      parseqs: 0.0.6
      parseuri: 0.0.6
      ws: 7.4.6
      xmlhttprequest-ssl: 1.6.3
      yeast: 0.1.2
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /engine.io-parser/2.2.1:
    resolution: {integrity: sha1-V85WEdk3DulPmWQbWJ+UyX5PXac=}
    dependencies:
      after: 0.8.2
      arraybuffer.slice: 0.0.7
      base64-arraybuffer: 0.1.4
      blob: 0.0.5
      has-binary2: 1.0.3
    dev: false

  /enhanced-resolve/0.9.1:
    resolution: {integrity: sha1-TW5omzcl+GCQknzMhs2fFjW4ni4=}
    engines: {node: '>=0.6'}
    dependencies:
      graceful-fs: 4.2.11
      memory-fs: 0.2.0
      tapable: 0.1.10
    dev: true

  /enhanced-resolve/4.5.0:
    resolution: {integrity: sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=}
    engines: {node: '>=6.9.0'}
    dependencies:
      graceful-fs: 4.2.11
      memory-fs: 0.5.0
      tapable: 1.1.3
    dev: true

  /entities/1.0.0:
    resolution: {integrity: sha1-sph6o4ITR/zeZCsk/fyeT7cSvyY=}
    dev: false

  /entities/1.1.2:
    resolution: {integrity: sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=}
    dev: true

  /entities/2.2.0:
    resolution: {integrity: sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=}

  /errno/0.1.8:
    resolution: {integrity: sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=}
    hasBin: true
    dependencies:
      prr: 1.0.1
    dev: true

  /error-ex/1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=}
    dependencies:
      is-arrayish: 0.2.1

  /error-stack-parser/2.1.4:
    resolution: {integrity: sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY=}
    dependencies:
      stackframe: 1.3.4
    dev: true

  /es-abstract/1.22.1:
    resolution: {integrity: sha1-i05fxc79fxZg8PjhpSkA37ydnMw=}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.0
      arraybuffer.prototype.slice: 1.0.1
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      es-set-tostringtag: 2.0.1
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.5
      get-intrinsic: 1.2.1
      get-symbol-description: 1.0.0
      globalthis: 1.0.3
      gopd: 1.0.1
      has: 1.0.3
      has-property-descriptors: 1.0.0
      has-proto: 1.0.1
      has-symbols: 1.0.3
      internal-slot: 1.0.5
      is-array-buffer: 3.0.2
      is-callable: 1.2.7
      is-negative-zero: 2.0.2
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.2
      is-string: 1.0.7
      is-typed-array: 1.1.12
      is-weakref: 1.0.2
      object-inspect: 1.12.3
      object-keys: 1.1.1
      object.assign: 4.1.4
      regexp.prototype.flags: 1.5.0
      safe-array-concat: 1.0.0
      safe-regex-test: 1.0.0
      string.prototype.trim: 1.2.7
      string.prototype.trimend: 1.0.6
      string.prototype.trimstart: 1.0.6
      typed-array-buffer: 1.0.0
      typed-array-byte-length: 1.0.0
      typed-array-byte-offset: 1.0.0
      typed-array-length: 1.0.4
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.11
    dev: true

  /es-array-method-boxes-properly/1.0.0:
    resolution: {integrity: sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4=}
    dev: true

  /es-set-tostringtag/2.0.1:
    resolution: {integrity: sha1-M41QL29nQwHXELgMhZLeihXwnNg=}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.1
      has: 1.0.3
      has-tostringtag: 1.0.0
    dev: true

  /es-shim-unscopables/1.0.0:
    resolution: {integrity: sha1-cC5jIZMgHj7fhxNjXQg9N45RAkE=}
    dependencies:
      has: 1.0.3
    dev: true

  /es-to-primitive/1.2.1:
    resolution: {integrity: sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4
    dev: true

  /es6-promise/3.3.1:
    resolution: {integrity: sha1-oIzd6EzNvzTQJ6FFG8kdS80ophM=}
    dev: false

  /es6-promisify/4.0.0:
    resolution: {integrity: sha1-eMH/zYSM4jP9lin+qlNxym3N9Lg=}
    dependencies:
      es6-promise: 3.3.1
    dev: false

  /escalade/3.1.1:
    resolution: {integrity: sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=}
    engines: {node: '>=6'}
    dev: true

  /escape-html/1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=}
    dev: true

  /escape-string-regexp/1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=}
    engines: {node: '>=0.8.0'}
    dev: true

  /eslint-config-standard/14.1.1_098f0143802d3bc4a66be70e1fb701e2:
    resolution: {integrity: sha1-gwqOROeu995nRkl5rQa0BgJsVuo=}
    peerDependencies:
      eslint: '>=6.2.2'
      eslint-plugin-import: '>=2.18.0'
      eslint-plugin-node: '>=9.1.0'
      eslint-plugin-promise: '>=4.2.1'
      eslint-plugin-standard: '>=4.0.0'
    dependencies:
      eslint: 6.8.0
      eslint-plugin-import: 2.28.0_eslint@6.8.0
      eslint-plugin-node: 11.1.0_eslint@6.8.0
      eslint-plugin-promise: 4.3.1
      eslint-plugin-standard: 4.1.0_eslint@6.8.0
    dev: true

  /eslint-import-resolver-node/0.3.9:
    resolution: {integrity: sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=}
    dependencies:
      debug: 3.2.7
      is-core-module: 2.13.0
      resolve: 1.22.4
    dev: true

  /eslint-import-resolver-webpack/0.12.2_eslint-plugin-import@2.28.0:
    resolution: {integrity: sha1-dp6GzQx1KhU2wZhV69kKoUzjhO4=}
    peerDependencies:
      eslint-plugin-import: '>=1.4.0'
      webpack: '>=1.11.0'
    dependencies:
      array-find: 1.0.0
      debug: 2.6.9
      enhanced-resolve: 0.9.1
      eslint-plugin-import: 2.28.0_eslint@6.8.0
      find-root: 1.1.0
      has: 1.0.3
      interpret: 1.4.0
      lodash: 4.17.21
      node-libs-browser: 2.2.1
      resolve: 1.22.4
      semver: 5.7.2
    dev: true

  /eslint-loader/2.2.1_eslint@6.8.0+webpack@4.46.0:
    resolution: {integrity: sha1-KLnBLaVAV68IReKmEScBova/gzc=}
    deprecated: This loader has been deprecated. Please use eslint-webpack-plugin
    peerDependencies:
      eslint: '>=1.6.0 <7.0.0'
      webpack: '>=2.0.0 <5.0.0'
    dependencies:
      eslint: 6.8.0
      loader-fs-cache: 1.0.3
      loader-utils: 1.4.2
      object-assign: 4.1.1
      object-hash: 1.3.1
      rimraf: 2.7.1
      webpack: 4.46.0
    dev: true

  /eslint-module-utils/2.8.0_eslint@6.8.0:
    resolution: {integrity: sha1-5Dn+5l/DP2u6Yw/2Ie/DjsA3XEk=}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: '*'
    peerDependenciesMeta:
      eslint:
        optional: true
    dependencies:
      debug: 3.2.7
      eslint: 6.8.0
    dev: true

  /eslint-plugin-es/3.0.1_eslint@6.8.0:
    resolution: {integrity: sha1-dafN/czdwFiZNK7rOEF18iHFeJM=}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=4.19.1'
    dependencies:
      eslint: 6.8.0
      eslint-utils: 2.1.0
      regexpp: 3.2.0
    dev: true

  /eslint-plugin-import/2.28.0_eslint@6.8.0:
    resolution: {integrity: sha1-jWbWklEXsGxAGNSRroRGnrPLEAU=}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
    dependencies:
      array-includes: 3.1.6
      array.prototype.findlastindex: 1.2.2
      array.prototype.flat: 1.3.1
      array.prototype.flatmap: 1.3.1
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 6.8.0
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.8.0_eslint@6.8.0
      has: 1.0.3
      is-core-module: 2.13.0
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.6
      object.groupby: 1.0.0
      object.values: 1.1.6
      resolve: 1.22.4
      semver: 6.3.1
      tsconfig-paths: 3.14.2
    dev: true

  /eslint-plugin-node/11.1.0_eslint@6.8.0:
    resolution: {integrity: sha1-yVVEQW7kraJnQKMEdO78VALcZx0=}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=5.16.0'
    dependencies:
      eslint: 6.8.0
      eslint-plugin-es: 3.0.1_eslint@6.8.0
      eslint-utils: 2.1.0
      ignore: 5.2.4
      minimatch: 3.1.2
      resolve: 1.22.4
      semver: 6.3.1
    dev: true

  /eslint-plugin-promise/4.3.1:
    resolution: {integrity: sha1-YUhd8qNZ4DFJ/a/AposOAwrSrEU=}
    engines: {node: '>=6'}
    dev: true

  /eslint-plugin-standard/4.1.0_eslint@6.8.0:
    resolution: {integrity: sha1-DDvzpn6FP4u7xYD7SUX78W9Bt8U=}
    peerDependencies:
      eslint: '>=5.0.0'
    dependencies:
      eslint: 6.8.0
    dev: true

  /eslint-plugin-vue/6.2.2_eslint@6.8.0:
    resolution: {integrity: sha1-J/7NmjokeJsPER7N1UCp5WGY4P4=}
    engines: {node: '>=8.10'}
    peerDependencies:
      eslint: ^5.0.0 || ^6.0.0
    dependencies:
      eslint: 6.8.0
      natural-compare: 1.4.0
      semver: 5.7.2
      vue-eslint-parser: 7.11.0_eslint@6.8.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-scope/4.0.3:
    resolution: {integrity: sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=}
    engines: {node: '>=4.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: true

  /eslint-scope/5.1.1:
    resolution: {integrity: sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=}
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: true

  /eslint-utils/1.4.3:
    resolution: {integrity: sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: true

  /eslint-utils/2.1.0:
    resolution: {integrity: sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: true

  /eslint-visitor-keys/1.3.0:
    resolution: {integrity: sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=}
    engines: {node: '>=4'}
    dev: true

  /eslint/6.8.0:
    resolution: {integrity: sha1-YiYtZylzn5J1cjgkMC+yJ8jJP/s=}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    hasBin: true
    dependencies:
      '@babel/code-frame': 7.22.10
      ajv: 6.12.6
      chalk: 2.4.2
      cross-spawn: 6.0.5
      debug: 4.3.4
      doctrine: 3.0.0
      eslint-scope: 5.1.1
      eslint-utils: 1.4.3
      eslint-visitor-keys: 1.3.0
      espree: 6.2.1
      esquery: 1.5.0
      esutils: 2.0.3
      file-entry-cache: 5.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 5.1.2
      globals: 12.4.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      inquirer: 7.3.3
      is-glob: 4.0.3
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.3.0
      lodash: 4.17.21
      minimatch: 3.1.2
      mkdirp: 0.5.6
      natural-compare: 1.4.0
      optionator: 0.8.3
      progress: 2.0.3
      regexpp: 2.0.1
      semver: 6.3.1
      strip-ansi: 5.2.0
      strip-json-comments: 3.1.1
      table: 5.4.6
      text-table: 0.2.0
      v8-compile-cache: 2.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /espree/6.2.1:
    resolution: {integrity: sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=}
    engines: {node: '>=6.0.0'}
    dependencies:
      acorn: 7.4.1
      acorn-jsx: 5.3.2_acorn@7.4.1
      eslint-visitor-keys: 1.3.0
    dev: true

  /esprima/4.0.1:
    resolution: {integrity: sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /esquery/1.5.0:
    resolution: {integrity: sha1-bOF3ON6Fd2lO3XNhxXGCrIyw2ws=}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse/4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /estraverse/4.3.0:
    resolution: {integrity: sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=}
    engines: {node: '>=4.0'}
    dev: true

  /estraverse/5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=}
    engines: {node: '>=4.0'}
    dev: true

  /esutils/2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=}
    engines: {node: '>=0.10.0'}
    dev: true

  /etag/1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=}
    engines: {node: '>= 0.6'}
    dev: true

  /event-pubsub/4.3.0:
    resolution: {integrity: sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=}
    engines: {node: '>=4.0.0'}
    dev: true

  /eventemitter3/4.0.7:
    resolution: {integrity: sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=}
    dev: true

  /events/3.3.0:
    resolution: {integrity: sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=}
    engines: {node: '>=0.8.x'}
    dev: true

  /eventsource/2.0.2:
    resolution: {integrity: sha1-dt/MApMPsv8zlSC20pDaVzqehQg=}
    engines: {node: '>=12.0.0'}
    dev: true

  /evp_bytestokey/1.0.3:
    resolution: {integrity: sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=}
    dependencies:
      md5.js: 1.3.5
      safe-buffer: 5.2.1
    dev: true

  /execa/0.8.0:
    resolution: {integrity: sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=}
    engines: {node: '>=4'}
    dependencies:
      cross-spawn: 5.1.0
      get-stream: 3.0.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0
    dev: true

  /execa/1.0.0:
    resolution: {integrity: sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=}
    engines: {node: '>=6'}
    dependencies:
      cross-spawn: 6.0.5
      get-stream: 4.1.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0
    dev: true

  /execa/2.1.0:
    resolution: {integrity: sha1-5dPs2DfSpg7FDz2nj9OXZ3R7vpk=}
    engines: {node: ^8.12.0 || >=9.7.0}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 5.2.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 3.1.0
      onetime: 5.1.2
      p-finally: 2.0.1
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /execa/3.4.0:
    resolution: {integrity: sha1-wI7UVQ72XYWPrCaf/IVyRG8364k=}
    engines: {node: ^8.12.0 || >=9.7.0}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 5.2.0
      human-signals: 1.1.1
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      p-finally: 2.0.1
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /exit/0.1.2:
    resolution: {integrity: sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=}
    engines: {node: '>= 0.8.0'}
    dev: false

  /expand-brackets/2.1.4:
    resolution: {integrity: sha1-t3c14xXOMPa27/D4OwQVGiJEliI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /express/4.18.2:
    resolution: {integrity: sha1-P6vggpbpMMeWwZ48UWl5OGup/Vk=}
    engines: {node: '>= 0.10.0'}
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.1
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.5.0
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.2.0
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.1
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.7
      proxy-addr: 2.0.7
      qs: 6.11.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.18.0
      serve-static: 1.15.0
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    dev: true

  /extend-shallow/2.0.1:
    resolution: {integrity: sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1
    dev: true

  /extend-shallow/3.0.2:
    resolution: {integrity: sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1
    dev: true

  /extend/3.0.2:
    resolution: {integrity: sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=}

  /external-editor/3.1.0:
    resolution: {integrity: sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=}
    engines: {node: '>=4'}
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33
    dev: true

  /extglob/2.0.4:
    resolution: {integrity: sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /extsprintf/1.3.0:
    resolution: {integrity: sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=}
    engines: {'0': node >=0.6.0}

  /fast-deep-equal/3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=}

  /fast-glob/2.2.7:
    resolution: {integrity: sha1-aVOFfDr6R1//ku5gFdUtpwpM050=}
    engines: {node: '>=4.0.0'}
    dependencies:
      '@mrmlnc/readdir-enhanced': 2.2.1
      '@nodelib/fs.stat': 1.1.3
      glob-parent: 3.1.0
      is-glob: 4.0.3
      merge2: 1.4.1
      micromatch: 3.1.10
    dev: true

  /fast-glob/3.3.1:
    resolution: {integrity: sha1-eEtOiXNA89u+8XQTs/EazwPIdMQ=}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5
    dev: true

  /fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=}

  /fast-levenshtein/2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=}
    dev: true

  /fastclick/1.0.6:
    resolution: {integrity: sha1-FhYlsnsaWAZAWTa9qaLBkm0Gvmo=}
    dev: false

  /fastq/1.15.0:
    resolution: {integrity: sha1-0E0HxqKmj+RZn+qNLhA6k3+uazo=}
    dependencies:
      reusify: 1.0.4
    dev: true

  /faye-websocket/0.11.4:
    resolution: {integrity: sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=}
    engines: {node: '>=0.8.0'}
    dependencies:
      websocket-driver: 0.7.4
    dev: true

  /figgy-pudding/3.5.2:
    resolution: {integrity: sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=}
    dev: true

  /figures/1.7.0:
    resolution: {integrity: sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      escape-string-regexp: 1.0.5
      object-assign: 4.1.1
    dev: true

  /figures/2.0.0:
    resolution: {integrity: sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=}
    engines: {node: '>=4'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /figures/3.2.0:
    resolution: {integrity: sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /file-entry-cache/5.0.1:
    resolution: {integrity: sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=}
    engines: {node: '>=4'}
    dependencies:
      flat-cache: 2.0.1
    dev: true

  /file-loader/4.3.0_webpack@4.46.0:
    resolution: {integrity: sha1-eA8ED3KbPRgBnyBgX3I+hEuKWK8=}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      loader-utils: 1.4.2
      schema-utils: 2.7.1
      webpack: 4.46.0
    dev: true

  /file-uri-to-path/1.0.0:
    resolution: {integrity: sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=}
    dev: true
    optional: true

  /filesize/3.6.1:
    resolution: {integrity: sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=}
    engines: {node: '>= 0.4.0'}
    dev: true

  /fill-range/4.0.0:
    resolution: {integrity: sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1
    dev: true

  /fill-range/7.0.1:
    resolution: {integrity: sha1-GRmmp8df44ssfHflGYU12prN2kA=}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: true

  /finalhandler/1.2.0:
    resolution: {integrity: sha1-fSP+VzGyB7RkDk/NAK7B+SB6ezI=}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    dev: true

  /find-cache-dir/0.1.1:
    resolution: {integrity: sha1-yN765XyKUqinhPnjHFfHQumToLk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      commondir: 1.0.1
      mkdirp: 0.5.6
      pkg-dir: 1.0.0
    dev: true

  /find-cache-dir/2.1.0:
    resolution: {integrity: sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=}
    engines: {node: '>=6'}
    dependencies:
      commondir: 1.0.1
      make-dir: 2.1.0
      pkg-dir: 3.0.0
    dev: true

  /find-cache-dir/3.3.2:
    resolution: {integrity: sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=}
    engines: {node: '>=8'}
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0
    dev: true

  /find-root/1.1.0:
    resolution: {integrity: sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=}
    dev: true

  /find-up/1.1.2:
    resolution: {integrity: sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      path-exists: 2.1.0
      pinkie-promise: 2.0.1
    dev: true

  /find-up/3.0.0:
    resolution: {integrity: sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=}
    engines: {node: '>=6'}
    dependencies:
      locate-path: 3.0.0
    dev: true

  /find-up/4.1.0:
    resolution: {integrity: sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  /fingerprintjs2/2.1.4:
    resolution: {integrity: sha1-o53rlHqhh8CYMGoLXdQc6qLhX8U=}
    deprecated: Package has been renamed to @fingerprintjs/fingerprintjs. Install @fingerprintjs/fingerprintjs to get updates.
    dev: false

  /flat-cache/2.0.1:
    resolution: {integrity: sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=}
    engines: {node: '>=4'}
    dependencies:
      flatted: 2.0.2
      rimraf: 2.6.3
      write: 1.0.3
    dev: true

  /flatted/2.0.2:
    resolution: {integrity: sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=}
    dev: true

  /flush-write-stream/1.1.1:
    resolution: {integrity: sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: true

  /follow-redirects/1.15.2:
    resolution: {integrity: sha1-tGCGQUS6Y/JoEJbydMTlcCbaLBM=}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  /for-each/0.3.3:
    resolution: {integrity: sha1-abRH6IoKXTLD5whPPxcQA0shN24=}
    dependencies:
      is-callable: 1.2.7
    dev: true

  /for-in/1.0.2:
    resolution: {integrity: sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=}
    engines: {node: '>=0.10.0'}
    dev: true

  /forever-agent/0.6.1:
    resolution: {integrity: sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=}

  /form-data/2.3.3:
    resolution: {integrity: sha1-3M5SwF9kTymManq5Nr1yTO/786Y=}
    engines: {node: '>= 0.12'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  /forwarded/0.2.0:
    resolution: {integrity: sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=}
    engines: {node: '>= 0.6'}
    dev: true

  /fragment-cache/0.2.1:
    resolution: {integrity: sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-cache: 0.2.2
    dev: true

  /fresh/0.5.2:
    resolution: {integrity: sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=}
    engines: {node: '>= 0.6'}
    dev: true

  /from2/2.3.0:
    resolution: {integrity: sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: true

  /fs-extra/0.26.7:
    resolution: {integrity: sha1-muH92UiXeY7at20JGM9C0MMYT6k=}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 2.4.0
      klaw: 1.3.1
      path-is-absolute: 1.0.1
      rimraf: 2.7.1
    dev: false

  /fs-extra/7.0.1:
    resolution: {integrity: sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=}
    engines: {node: '>=6 <7 || >=8'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2
    dev: true

  /fs-minipass/2.1.0:
    resolution: {integrity: sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /fs-promise/0.5.0:
    resolution: {integrity: sha1-Q0fWv2JGVacGGkMZITw5MnatPvM=}
    deprecated: Use mz or fs-extra^3.0 with Promise Support
    dependencies:
      any-promise: 1.3.0
      fs-extra: 0.26.7
      mz: 2.7.0
      thenify-all: 1.6.0
    dev: false

  /fs-write-stream-atomic/1.0.10:
    resolution: {integrity: sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=}
    dependencies:
      graceful-fs: 4.2.11
      iferr: 0.1.5
      imurmurhash: 0.1.4
      readable-stream: 2.3.8
    dev: true

  /fs.realpath/1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=}

  /fsevents/1.2.13:
    resolution: {integrity: sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/fsevents/-/fsevents-1.2.13.tgz}
    engines: {node: '>= 4.0'}
    os: [darwin]
    deprecated: The v1 package contains DANGEROUS / INSECURE binaries. Upgrade to safe fsevents v2
    requiresBuild: true
    dependencies:
      bindings: 1.5.0
      nan: 2.17.0
    dev: true
    optional: true

  /fsevents/2.3.2:
    resolution: {integrity: sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/fsevents/-/fsevents-2.3.2.tgz}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /fstream/1.0.12:
    resolution: {integrity: sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU=}
    engines: {node: '>=0.6'}
    dependencies:
      graceful-fs: 4.2.11
      inherits: 2.0.4
      mkdirp: 0.5.6
      rimraf: 2.7.1
    dev: false

  /function-bind/1.1.1:
    resolution: {integrity: sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=}

  /function.prototype.name/1.1.5:
    resolution: {integrity: sha1-zOBQX+H/uAUD5vnkbMZORqEqliE=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      functions-have-names: 1.2.3
    dev: true

  /functional-red-black-tree/1.0.1:
    resolution: {integrity: sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=}
    dev: true

  /functions-have-names/1.2.3:
    resolution: {integrity: sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=}
    dev: true

  /fuzzysearch/1.0.3:
    resolution: {integrity: sha1-3/yA9tawQiPyImqnndGUIxCW0Ag=}
    dev: false

  /gensync/1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=}
    engines: {node: '>=6.9.0'}
    dev: true

  /get-caller-file/2.0.5:
    resolution: {integrity: sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=}
    engines: {node: 6.* || 8.* || >= 10.*}

  /get-intrinsic/1.2.1:
    resolution: {integrity: sha1-0pVkT+1FBfyc3pUsN+4StHeoPYI=}
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-proto: 1.0.1
      has-symbols: 1.0.3

  /get-own-enumerable-property-symbols/3.0.2:
    resolution: {integrity: sha1-tf3nfyLL4185C04ImSLFC85u9mQ=}
    dev: true

  /get-stream/3.0.0:
    resolution: {integrity: sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=}
    engines: {node: '>=4'}
    dev: true

  /get-stream/4.1.0:
    resolution: {integrity: sha1-wbJVV189wh1Zv8ec09K0axw6VLU=}
    engines: {node: '>=6'}
    dependencies:
      pump: 3.0.0
    dev: true

  /get-stream/5.2.0:
    resolution: {integrity: sha1-SWaheV7lrOZecGxLe+txJX1uItM=}
    engines: {node: '>=8'}
    dependencies:
      pump: 3.0.0
    dev: true

  /get-symbol-description/1.0.0:
    resolution: {integrity: sha1-f9uByQAQH71WTdXxowr1qtweWNY=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
    dev: true

  /get-value/2.0.6:
    resolution: {integrity: sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=}
    engines: {node: '>=0.10.0'}
    dev: true

  /getpass/0.1.7:
    resolution: {integrity: sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=}
    dependencies:
      assert-plus: 1.0.0

  /glob-base/0.3.0:
    resolution: {integrity: sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=}
    engines: {node: '>=0.10.0'}
    dependencies:
      glob-parent: 2.0.0
      is-glob: 2.0.1
    dev: false

  /glob-parent/2.0.0:
    resolution: {integrity: sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=}
    dependencies:
      is-glob: 2.0.1
    dev: false

  /glob-parent/3.1.0:
    resolution: {integrity: sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=}
    dependencies:
      is-glob: 3.1.0
      path-dirname: 1.0.2
    dev: true

  /glob-parent/5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob-to-regexp/0.3.0:
    resolution: {integrity: sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=}
    dev: true

  /glob/5.0.15:
    resolution: {integrity: sha1-G8k2ueAvSmA/zCIuz3Yz0wuLk7E=}
    dependencies:
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: false

  /glob/7.1.6:
    resolution: {integrity: sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: false

  /glob/7.2.3:
    resolution: {integrity: sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /globals/11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=}
    engines: {node: '>=4'}
    dev: true

  /globals/12.4.0:
    resolution: {integrity: sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.8.1
    dev: true

  /globals/9.18.0:
    resolution: {integrity: sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo=}
    engines: {node: '>=0.10.0'}
    dev: true

  /globalthis/1.0.3:
    resolution: {integrity: sha1-WFKIKlK4DcMBsGYCc+HtCC8LbM8=}
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: 1.2.0
    dev: true

  /globby/10.0.2:
    resolution: {integrity: sha1-J3WT50WsqkZGw6tBEonsR6A5JUM=}
    engines: {node: '>=8'}
    dependencies:
      '@types/glob': 7.2.0
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.1
      glob: 7.2.3
      ignore: 5.2.4
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /globby/6.1.0:
    resolution: {integrity: sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-union: 1.0.2
      glob: 7.2.3
      object-assign: 4.1.1
      pify: 2.3.0
      pinkie-promise: 2.0.1
    dev: true

  /globby/7.1.1:
    resolution: {integrity: sha1-+yzP+UAfhgCUXfral0QMypcrhoA=}
    engines: {node: '>=4'}
    dependencies:
      array-union: 1.0.2
      dir-glob: 2.2.2
      glob: 7.2.3
      ignore: 3.3.10
      pify: 3.0.0
      slash: 1.0.0
    dev: true

  /globby/9.2.0:
    resolution: {integrity: sha1-/QKacGxwPSm90XD0tts6P3p8tj0=}
    engines: {node: '>=6'}
    dependencies:
      '@types/glob': 7.2.0
      array-union: 1.0.2
      dir-glob: 2.2.2
      fast-glob: 2.2.7
      glob: 7.2.3
      ignore: 4.0.6
      pify: 4.0.1
      slash: 2.0.0
    dev: true

  /good-listener/1.2.2:
    resolution: {integrity: sha1-1TswzfkxPf+33JoNR3CWqm0UXFA=}
    dependencies:
      delegate: 3.2.0
    dev: false

  /gopd/1.0.1:
    resolution: {integrity: sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=}
    dependencies:
      get-intrinsic: 1.2.1
    dev: true

  /got/5.3.1:
    resolution: {integrity: sha1-det5autZdyav3aVyE0ywKtlvXYs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      create-error-class: 2.0.1
      duplexer2: 0.1.4
      is-plain-obj: 1.1.0
      is-redirect: 1.0.0
      is-stream: 1.1.0
      lowercase-keys: 1.0.1
      node-status-codes: 1.0.0
      object-assign: 4.1.1
      parse-json: 2.2.0
      pinkie-promise: 2.0.1
      read-all-stream: 3.1.0
      readable-stream: 2.3.8
      timed-out: 2.0.0
      unzip-response: 1.0.2
      url-parse-lax: 1.0.0
    dev: false

  /graceful-fs/4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=}

  /gunzip-maybe/1.2.1:
    resolution: {integrity: sha1-mqPc5TwnJRbECyphCiVSm/ZO4bw=}
    hasBin: true
    dependencies:
      browserify-zlib: 0.1.4
      peek-stream: 1.1.3
      pumpify: 1.5.1
      through2: 0.4.2
    dev: false

  /gzip-size/5.1.1:
    resolution: {integrity: sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ=}
    engines: {node: '>=6'}
    dependencies:
      duplexer: 0.1.2
      pify: 4.0.1
    dev: true

  /handle-thing/2.0.1:
    resolution: {integrity: sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=}
    dev: true

  /har-schema/2.0.0:
    resolution: {integrity: sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=}
    engines: {node: '>=4'}

  /har-validator/5.1.5:
    resolution: {integrity: sha1-HwgDufjLIMD6E4It8ezds2veHv0=}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  /has-ansi/2.0.0:
    resolution: {integrity: sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /has-bigints/1.0.2:
    resolution: {integrity: sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=}
    dev: true

  /has-binary2/1.0.3:
    resolution: {integrity: sha1-d3asYn8+p3JQz8My2rfd9eT10R0=}
    dependencies:
      isarray: 2.0.1
    dev: false

  /has-color/0.1.7:
    resolution: {integrity: sha1-ZxRKUmDDT8PMpnfQQdr1L+e3iy8=}
    engines: {node: '>=0.10.0'}
    dev: false

  /has-cors/1.1.0:
    resolution: {integrity: sha1-XkdHk/fqmEPRu5nCPu9J/xJv/zk=}
    dev: false

  /has-flag/1.0.0:
    resolution: {integrity: sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=}
    engines: {node: '>=0.10.0'}
    dev: true

  /has-flag/3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=}
    engines: {node: '>=4'}
    dev: true

  /has-flag/4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=}
    engines: {node: '>=8'}

  /has-property-descriptors/1.0.0:
    resolution: {integrity: sha1-YQcIYAYG02lh7QTBlhk7amB/qGE=}
    dependencies:
      get-intrinsic: 1.2.1
    dev: true

  /has-proto/1.0.1:
    resolution: {integrity: sha1-GIXBMFU4lYr/Rp/vN5N8InlUCOA=}
    engines: {node: '>= 0.4'}

  /has-symbols/1.0.3:
    resolution: {integrity: sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=}
    engines: {node: '>= 0.4'}

  /has-tostringtag/1.0.0:
    resolution: {integrity: sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: true

  /has-value/0.3.1:
    resolution: {integrity: sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0
    dev: true

  /has-value/1.0.0:
    resolution: {integrity: sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1
    dev: true

  /has-values/0.1.4:
    resolution: {integrity: sha1-bWHeldkd/Km5oCCJrThL/49it3E=}
    engines: {node: '>=0.10.0'}
    dev: true

  /has-values/1.0.0:
    resolution: {integrity: sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0
    dev: true

  /has/1.0.3:
    resolution: {integrity: sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1

  /hash-base/3.1.0:
    resolution: {integrity: sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=}
    engines: {node: '>=4'}
    dependencies:
      inherits: 2.0.4
      readable-stream: 3.6.2
      safe-buffer: 5.2.1
    dev: true

  /hash-sum/1.0.2:
    resolution: {integrity: sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=}
    dev: true

  /hash-sum/2.0.0:
    resolution: {integrity: sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=}
    dev: true

  /hash.js/1.1.7:
    resolution: {integrity: sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: true

  /he/1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=}
    hasBin: true
    dev: true

  /hex-color-regex/1.1.0:
    resolution: {integrity: sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=}
    dev: true

  /highlight.js/10.7.3:
    resolution: {integrity: sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE=}
    dev: true

  /hmac-drbg/1.0.1:
    resolution: {integrity: sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=}
    dependencies:
      hash.js: 1.1.7
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: true

  /hoopy/0.1.4:
    resolution: {integrity: sha1-YJIH1mEQADOpqUAq096mdzgcGx0=}
    engines: {node: '>= 6.0.0'}
    dev: true

  /hosted-git-info/2.8.9:
    resolution: {integrity: sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=}

  /hpack.js/2.1.6:
    resolution: {integrity: sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=}
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.8
      wbuf: 1.7.3
    dev: true

  /hsl-regex/1.0.0:
    resolution: {integrity: sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=}
    dev: true

  /hsla-regex/1.0.0:
    resolution: {integrity: sha1-wc56MWjIxmFAM6S194d/OyJfnDg=}
    dev: true

  /html-entities/1.4.0:
    resolution: {integrity: sha1-z70bAdKvr5rcobEK59/6uYxx0tw=}
    dev: true

  /html-minifier/3.5.21:
    resolution: {integrity: sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=}
    engines: {node: '>=4'}
    hasBin: true
    dependencies:
      camel-case: 3.0.0
      clean-css: 4.2.4
      commander: 2.17.1
      he: 1.2.0
      param-case: 2.1.1
      relateurl: 0.2.7
      uglify-js: 3.4.10
    dev: true

  /html-tags/2.0.0:
    resolution: {integrity: sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/html-tags/-/html-tags-2.0.0.tgz}
    engines: {node: '>=4'}

  /html-tags/3.3.1:
    resolution: {integrity: sha1-oEAmoYyILku6igGj05z+Rl1Atc4=}
    engines: {node: '>=8'}
    dev: true

  /html-webpack-plugin/3.2.0_webpack@4.46.0:
    resolution: {integrity: sha1-sBq71yOsqqeze2r0SS69oD2d03s=}
    engines: {node: '>=6.9'}
    deprecated: 3.x is no longer supported
    peerDependencies:
      webpack: ^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0
    dependencies:
      html-minifier: 3.5.21
      loader-utils: 0.2.17
      lodash: 4.17.21
      pretty-error: 2.1.2
      tapable: 1.1.3
      toposort: 1.0.7
      util.promisify: 1.0.0
      webpack: 4.46.0
    dev: true

  /html2canvas/1.4.1:
    resolution: {integrity: sha1-fO8YiDEbUBHVB3lKBmBBsUZppUM=}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-line-break: 2.1.0
      text-segmentation: 1.0.3
    dev: false

  /htmlhint/0.14.2:
    resolution: {integrity: sha1-jFW4U5C6ENUSvVk687F1raBvXqg=}
    hasBin: true
    dependencies:
      async: 3.2.0
      chalk: 4.1.0
      commander: 5.1.0
      glob: 7.1.6
      parse-glob: 3.0.4
      request: 2.88.2
      strip-json-comments: 3.1.0
      xml: 1.0.1
    dev: false

  /htmlparser2/3.10.1:
    resolution: {integrity: sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=}
    dependencies:
      domelementtype: 1.3.1
      domhandler: 2.4.2
      domutils: 1.7.0
      entities: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.2
    dev: true

  /htmlparser2/3.8.3:
    resolution: {integrity: sha1-mWwosZFRaovoZQGn15dX5ccMEGg=}
    dependencies:
      domelementtype: 1.3.1
      domhandler: 2.3.0
      domutils: 1.5.1
      entities: 1.0.0
      readable-stream: 1.1.14
    dev: false

  /htmlparser2/6.1.0:
    resolution: {integrity: sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0
    dev: true

  /http-deceiver/1.2.7:
    resolution: {integrity: sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=}
    dev: true

  /http-errors/1.6.3:
    resolution: {integrity: sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=}
    engines: {node: '>= 0.6'}
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0
    dev: true

  /http-errors/2.0.0:
    resolution: {integrity: sha1-t3dKFIbvc892Z6ya4IWMASxXudM=}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1
    dev: true

  /http-parser-js/0.5.8:
    resolution: {integrity: sha1-ryMJDZrE4kVz3m9q7MnYSki/IOM=}
    dev: true

  /http-proxy-middleware/0.19.1_debug@4.3.4:
    resolution: {integrity: sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=}
    engines: {node: '>=4.0.0'}
    dependencies:
      http-proxy: 1.18.1_debug@4.3.4
      is-glob: 4.0.3
      lodash: 4.17.21
      micromatch: 3.1.10
    transitivePeerDependencies:
      - debug
    dev: true

  /http-proxy-middleware/1.3.1_debug@4.3.4:
    resolution: {integrity: sha1-Q3ANbZ7st0Gb8IahKND3IF2etmU=}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@types/http-proxy': 1.17.11
      http-proxy: 1.18.1_debug@4.3.4
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.5
    transitivePeerDependencies:
      - debug
    dev: true

  /http-proxy/1.18.1_debug@4.3.4:
    resolution: {integrity: sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=}
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.2
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug
    dev: true

  /http-signature/1.2.0:
    resolution: {integrity: sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=}
    engines: {node: '>=0.8', npm: '>=1.3.7'}
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.17.0

  /https-browserify/1.0.0:
    resolution: {integrity: sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=}
    dev: true

  /https-proxy-agent/1.0.0:
    resolution: {integrity: sha1-NffabEjOTdv6JkiRrFk+5f+GceY=}
    dependencies:
      agent-base: 2.1.1
      debug: 2.2.0
      extend: 3.0.2
    dev: false

  /human-signals/1.1.1:
    resolution: {integrity: sha1-xbHNFPUK6uCatsWf5jujOV/k36M=}
    engines: {node: '>=8.12.0'}
    dev: true

  /hz-message/1.2.0:
    resolution: {integrity: sha1-fBtaoYJBanaVhhFmKZZD3yTFJ+I=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/hz-message/-/hz-message-1.2.0.tgz}
    dev: false

  /hz-product-list/1.1.11:
    resolution: {integrity: sha1-+yXu6UmRZqgllGywNGmRkWuLkNk=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/hz-product-list/-/hz-product-list-1.1.11.tgz}
    dev: false

  /hz-quark/2.0.8:
    resolution: {integrity: sha1-htPmXqFPuRYYRsgU6OluqkujWOM=}
    dev: false

  /hz-user/1.1.8:
    resolution: {integrity: sha1-OQ/MeadyfTbF7AJkWRfbtNgssPM=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/hz-user/-/hz-user-1.1.8.tgz}
    dev: false

  /iconv-lite/0.4.24:
    resolution: {integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: true

  /icss-utils/4.1.1:
    resolution: {integrity: sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc=}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /ieee754/1.2.1:
    resolution: {integrity: sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=}
    dev: true

  /iferr/0.1.5:
    resolution: {integrity: sha1-xg7taebY/bazEEofy8ocGS3FtQE=}
    dev: true

  /ignore/3.3.10:
    resolution: {integrity: sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=}
    dev: true

  /ignore/4.0.6:
    resolution: {integrity: sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=}
    engines: {node: '>= 4'}
    dev: true

  /ignore/5.2.4:
    resolution: {integrity: sha1-opHAxheP8blgvv5H/N7DAWdKYyQ=}
    engines: {node: '>= 4'}
    dev: true

  /image-size/0.5.5:
    resolution: {integrity: sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dev: true

  /immutable/4.3.2:
    resolution: {integrity: sha1-+J2RD437bhXAOyyuL6r4wfZkVf4=}
    dev: true

  /import-cwd/2.1.0:
    resolution: {integrity: sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=}
    engines: {node: '>=4'}
    dependencies:
      import-from: 2.1.0
    dev: true

  /import-fresh/2.0.0:
    resolution: {integrity: sha1-2BNVwVYS04bGH53dOSLUMEgipUY=}
    engines: {node: '>=4'}
    dependencies:
      caller-path: 2.0.0
      resolve-from: 3.0.0
    dev: true

  /import-fresh/3.3.0:
    resolution: {integrity: sha1-NxYsJfy566oublPVtNiM4X2eDCs=}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /import-from/2.1.0:
    resolution: {integrity: sha1-M1238qev/VOqpHHUuAId7ja387E=}
    engines: {node: '>=4'}
    dependencies:
      resolve-from: 3.0.0
    dev: true

  /import-local/2.0.0:
    resolution: {integrity: sha1-VQcL44pZk88Y72236WH1vuXFoJ0=}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      pkg-dir: 3.0.0
      resolve-cwd: 2.0.0
    dev: true

  /imurmurhash/0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=}
    engines: {node: '>=0.8.19'}
    dev: true

  /indent-string/3.2.0:
    resolution: {integrity: sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=}
    engines: {node: '>=4'}
    dev: true

  /indent-string/4.0.0:
    resolution: {integrity: sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=}
    engines: {node: '>=8'}
    dev: true

  /indexes-of/1.0.1:
    resolution: {integrity: sha1-8w9xbI4r00bHtn0985FVZqfAVgc=}
    dev: true

  /indexof/0.0.1:
    resolution: {integrity: sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10=}
    dev: false

  /infer-owner/1.0.4:
    resolution: {integrity: sha1-xM78qo5RBRwqQLos6KPScpWvlGc=}
    dev: true

  /inflight/1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  /inherits/2.0.1:
    resolution: {integrity: sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=}
    dev: true

  /inherits/2.0.3:
    resolution: {integrity: sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=}
    dev: true

  /inherits/2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=}

  /init-package-json/1.9.1:
    resolution: {integrity: sha1-oo4FtbrrM2PNRz32jTDTqAUjoxw=}
    dependencies:
      glob: 5.0.15
      npm-package-arg: 4.1.0
      promzard: 0.3.0
      read: 1.0.7
      read-package-json: 2.1.2
      semver: 5.0.1
      validate-npm-package-license: 3.0.4
      validate-npm-package-name: 2.2.2
    dev: false

  /inquirer/7.3.3:
    resolution: {integrity: sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=}
    engines: {node: '>=8.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
    dev: true

  /internal-ip/4.3.0:
    resolution: {integrity: sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=}
    engines: {node: '>=6'}
    dependencies:
      default-gateway: 4.2.0
      ipaddr.js: 1.9.1
    dev: true

  /internal-slot/1.0.5:
    resolution: {integrity: sha1-8qLuIfZo+GJ6RmfzCdwPT7ZnSYY=}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.1
      has: 1.0.3
      side-channel: 1.0.4
    dev: true

  /interpret/1.4.0:
    resolution: {integrity: sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=}
    engines: {node: '>= 0.10'}
    dev: true

  /invariant/2.2.4:
    resolution: {integrity: sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=}
    dependencies:
      loose-envify: 1.4.0
    dev: true

  /ip-regex/2.1.0:
    resolution: {integrity: sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=}
    engines: {node: '>=4'}
    dev: true

  /ip/1.1.8:
    resolution: {integrity: sha1-rgWUj2sHVDXtMweszgRinajNv0g=}
    dev: true

  /ipaddr.js/1.9.1:
    resolution: {integrity: sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=}
    engines: {node: '>= 0.10'}
    dev: true

  /is-absolute-url/2.1.0:
    resolution: {integrity: sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-absolute-url/3.0.3:
    resolution: {integrity: sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=}
    engines: {node: '>=8'}
    dev: true

  /is-accessor-descriptor/0.1.6:
    resolution: {integrity: sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /is-accessor-descriptor/1.0.0:
    resolution: {integrity: sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3
    dev: true

  /is-arguments/1.1.1:
    resolution: {integrity: sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: true

  /is-array-buffer/3.0.2:
    resolution: {integrity: sha1-8mU87YQSCBY47LDrvQxBxuCuy74=}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      is-typed-array: 1.1.12
    dev: true

  /is-arrayish/0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=}

  /is-arrayish/0.3.2:
    resolution: {integrity: sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=}
    dev: true

  /is-bigint/1.0.4:
    resolution: {integrity: sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=}
    dependencies:
      has-bigints: 1.0.2
    dev: true

  /is-binary-path/1.0.1:
    resolution: {integrity: sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      binary-extensions: 1.13.1
    dev: true

  /is-binary-path/2.1.0:
    resolution: {integrity: sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0
    dev: true

  /is-boolean-object/1.1.2:
    resolution: {integrity: sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: true

  /is-buffer/1.1.6:
    resolution: {integrity: sha1-76ouqdqg16suoTqXsritUf776L4=}
    dev: true

  /is-callable/1.2.7:
    resolution: {integrity: sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=}
    engines: {node: '>= 0.4'}
    dev: true

  /is-ci/1.2.1:
    resolution: {integrity: sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=}
    hasBin: true
    dependencies:
      ci-info: 1.6.0
    dev: true

  /is-color-stop/1.1.0:
    resolution: {integrity: sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=}
    dependencies:
      css-color-names: 0.0.4
      hex-color-regex: 1.1.0
      hsl-regex: 1.0.0
      hsla-regex: 1.0.0
      rgb-regex: 1.0.1
      rgba-regex: 1.0.0
    dev: true

  /is-core-module/2.13.0:
    resolution: {integrity: sha1-u1Kqbiy9SaMMK6aMQr80Nbpgcts=}
    dependencies:
      has: 1.0.3

  /is-data-descriptor/0.1.4:
    resolution: {integrity: sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /is-data-descriptor/1.0.0:
    resolution: {integrity: sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 6.0.3
    dev: true

  /is-date-object/1.0.5:
    resolution: {integrity: sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: true

  /is-descriptor/0.1.6:
    resolution: {integrity: sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 0.1.6
      is-data-descriptor: 0.1.4
      kind-of: 5.1.0
    dev: true

  /is-descriptor/1.0.2:
    resolution: {integrity: sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-accessor-descriptor: 1.0.0
      is-data-descriptor: 1.0.0
      kind-of: 6.0.3
    dev: true

  /is-directory/0.3.1:
    resolution: {integrity: sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-docker/2.2.1:
    resolution: {integrity: sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=}
    engines: {node: '>=8'}
    hasBin: true
    dev: true

  /is-dotfile/1.0.3:
    resolution: {integrity: sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-extendable/0.1.1:
    resolution: {integrity: sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-extendable/1.0.1:
    resolution: {integrity: sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-object: 2.0.4
    dev: true

  /is-extglob/1.0.0:
    resolution: {integrity: sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-extglob/2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-fullwidth-code-point/1.0.0:
    resolution: {integrity: sha1-754xOG8DGn8NZDr4L95QxFfvAMs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      number-is-nan: 1.0.1
    dev: true

  /is-fullwidth-code-point/2.0.0:
    resolution: {integrity: sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=}
    engines: {node: '>=4'}
    dev: true

  /is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=}
    engines: {node: '>=8'}

  /is-glob/2.0.1:
    resolution: {integrity: sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 1.0.0
    dev: false

  /is-glob/3.1.0:
    resolution: {integrity: sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-glob/4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-negative-zero/2.0.2:
    resolution: {integrity: sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA=}
    engines: {node: '>= 0.4'}
    dev: true

  /is-number-object/1.0.7:
    resolution: {integrity: sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: true

  /is-number/3.0.0:
    resolution: {integrity: sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /is-number/7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=}
    engines: {node: '>=0.12.0'}
    dev: true

  /is-obj/1.0.1:
    resolution: {integrity: sha1-PkcprB9f3gJc19g6iW2rn09n2w8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-obj/2.0.0:
    resolution: {integrity: sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=}
    engines: {node: '>=8'}
    dev: true

  /is-observable/1.1.0:
    resolution: {integrity: sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14=}
    engines: {node: '>=4'}
    dependencies:
      symbol-observable: 1.2.0
    dev: true

  /is-path-cwd/2.2.0:
    resolution: {integrity: sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=}
    engines: {node: '>=6'}
    dev: true

  /is-path-in-cwd/2.1.0:
    resolution: {integrity: sha1-v+Lcomxp85cmWkAJljYCk1oFOss=}
    engines: {node: '>=6'}
    dependencies:
      is-path-inside: 2.1.0
    dev: true

  /is-path-inside/2.1.0:
    resolution: {integrity: sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=}
    engines: {node: '>=6'}
    dependencies:
      path-is-inside: 1.0.2
    dev: true

  /is-path-inside/3.0.3:
    resolution: {integrity: sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=}
    engines: {node: '>=8'}
    dev: true

  /is-plain-obj/1.1.0:
    resolution: {integrity: sha1-caUMhCnfync8kqOQpKA7OfzVHT4=}
    engines: {node: '>=0.10.0'}

  /is-plain-obj/3.0.0:
    resolution: {integrity: sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=}
    engines: {node: '>=10'}
    dev: true

  /is-plain-object/2.0.4:
    resolution: {integrity: sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: true

  /is-promise/2.2.2:
    resolution: {integrity: sha1-OauVnMv5p3TPB597QMeib3YxNfE=}

  /is-redirect/1.0.0:
    resolution: {integrity: sha1-HQPd7VO9jbDzDCbk+V02/HyH3CQ=}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-regex/1.1.4:
    resolution: {integrity: sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: true

  /is-regexp/1.0.0:
    resolution: {integrity: sha1-/S2INUXEa6xaYz57mgnof6LLUGk=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-resolvable/1.1.0:
    resolution: {integrity: sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=}
    dev: true

  /is-shared-array-buffer/1.0.2:
    resolution: {integrity: sha1-jyWcVztgtqMtQFihoHQwwKc0THk=}
    dependencies:
      call-bind: 1.0.2
    dev: true

  /is-stream/1.1.0:
    resolution: {integrity: sha1-EtSj3U5o4Lec6428hBc66A2RykQ=}
    engines: {node: '>=0.10.0'}

  /is-stream/2.0.1:
    resolution: {integrity: sha1-+sHj1TuXrVqdCunO8jifWBClwHc=}
    engines: {node: '>=8'}
    dev: true

  /is-string/1.0.7:
    resolution: {integrity: sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: true

  /is-symbol/1.0.4:
    resolution: {integrity: sha1-ptrJO2NbBjymhyI23oiRClevE5w=}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: true

  /is-typed-array/1.1.12:
    resolution: {integrity: sha1-0Lq1aG70p296cwl7lUcKsZnFfUo=}
    engines: {node: '>= 0.4'}
    dependencies:
      which-typed-array: 1.1.11
    dev: true

  /is-typedarray/1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=}

  /is-weakref/1.0.2:
    resolution: {integrity: sha1-lSnzg6kzggXol2XgOS78LxAPBvI=}
    dependencies:
      call-bind: 1.0.2
    dev: true

  /is-windows/1.0.2:
    resolution: {integrity: sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-wsl/1.1.0:
    resolution: {integrity: sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=}
    engines: {node: '>=4'}
    dev: true

  /is-wsl/2.2.0:
    resolution: {integrity: sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=}
    engines: {node: '>=8'}
    dependencies:
      is-docker: 2.2.1
    dev: true

  /isarray/0.0.1:
    resolution: {integrity: sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=}
    dev: false

  /isarray/1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=}

  /isarray/2.0.1:
    resolution: {integrity: sha1-o32U7ZzaLVmGXJ92/llu4fM4dB4=}
    dev: false

  /isarray/2.0.5:
    resolution: {integrity: sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=}
    dev: true

  /isexe/2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=}
    dev: true

  /isobject/2.1.0:
    resolution: {integrity: sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isarray: 1.0.0
    dev: true

  /isobject/3.0.1:
    resolution: {integrity: sha1-TkMekrEalzFjaqH5yNHMvP2reN8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /isstream/0.1.2:
    resolution: {integrity: sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=}

  /javascript-natural-sort/0.7.1:
    resolution: {integrity: sha1-+eIwPUUH9tdDVac2ZNFED7Wg71k=}
    dev: false

  /javascript-stringify/2.1.0:
    resolution: {integrity: sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk=}
    dev: true

  /jmespath/0.16.0:
    resolution: {integrity: sha1-sVsKhd/U2TDUPmntYFlDyAJ4UHY=}
    engines: {node: '>= 0.6.0'}
    dev: false

  /js-audio-recorder/1.0.7:
    resolution: {integrity: sha1-AVAcuesUPVeZS0K9Wul5dP2hiQg=}
    dev: false

  /js-base64/2.6.4:
    resolution: {integrity: sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ=}
    dev: true

  /js-base64/3.7.5:
    resolution: {integrity: sha1-IeJM9riG921vXxZb/NacxVueP8o=}
    dev: false

  /js-message/1.0.7:
    resolution: {integrity: sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc=}
    engines: {node: '>=0.6.0'}
    dev: true

  /js-tokens/3.0.2:
    resolution: {integrity: sha1-mGbfOVECEw449/mWvOtlRDIJwls=}
    dev: true

  /js-tokens/4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=}
    dev: true

  /js-yaml/3.14.1:
    resolution: {integrity: sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1
    dev: true

  /jsbn/0.1.1:
    resolution: {integrity: sha1-peZUwuWi3rXyAdls77yoDA7y9RM=}

  /jsesc/0.5.0:
    resolution: {integrity: sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=}
    hasBin: true
    dev: true

  /jsesc/2.5.2:
    resolution: {integrity: sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /jshint/2.13.6:
    resolution: {integrity: sha1-NnmiaHowZvqQNO+F2MMFYTox7sY=}
    hasBin: true
    dependencies:
      cli: 1.0.1
      console-browserify: 1.1.0
      exit: 0.1.2
      htmlparser2: 3.8.3
      lodash: 4.17.21
      minimatch: 3.0.8
      strip-json-comments: 1.0.4
    dev: false

  /json-parse-better-errors/1.0.2:
    resolution: {integrity: sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=}
    dev: true

  /json-parse-even-better-errors/2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=}

  /json-schema-traverse/0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=}

  /json-schema/0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=}

  /json-source-map/0.6.1:
    resolution: {integrity: sha1-4LH29M4Tqa1X4q4WWiTQbmLHmg8=}
    dev: false

  /json-stable-stringify-without-jsonify/1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=}
    dev: true

  /json-stringify-safe/5.0.1:
    resolution: {integrity: sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=}

  /json5/0.5.1:
    resolution: {integrity: sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=}
    hasBin: true
    dev: true

  /json5/1.0.2:
    resolution: {integrity: sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=}
    hasBin: true
    dependencies:
      minimist: 1.2.8

  /json5/2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=}
    engines: {node: '>=6'}
    hasBin: true

  /jsoneditor/9.10.2:
    resolution: {integrity: sha1-yMOstZ1nSTGXHwWffHAVxS5edGA=}
    dependencies:
      ace-builds: 1.24.1
      ajv: 6.12.6
      javascript-natural-sort: 0.7.1
      jmespath: 0.16.0
      json-source-map: 0.6.1
      jsonrepair: 3.2.0
      mobius1-selectr: 2.4.13
      picomodal: 3.0.0
      vanilla-picker: 2.12.1
    dev: false

  /jsonfile/2.4.0:
    resolution: {integrity: sha1-NzaitCi4e72gzIO1P6PWM6NcKug=}
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: false

  /jsonfile/4.0.0:
    resolution: {integrity: sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=}
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: true

  /jsonlint/1.6.3:
    resolution: {integrity: sha1-y14x78C3gpHQ2GL77wWQCt8hKYg=}
    engines: {node: '>= 0.6'}
    hasBin: true
    dependencies:
      JSV: 4.0.2
      nomnom: 1.8.1
    dev: false

  /jsonrepair/3.2.0:
    resolution: {integrity: sha1-yhtv3SOudruMgnQveldzp87z1lE=}
    hasBin: true
    dev: false

  /jsprim/1.4.2:
    resolution: {integrity: sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=}
    engines: {node: '>=0.6.0'}
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  /keymaster/1.6.2:
    resolution: {integrity: sha1-4a5U0OqUiPn2C2a2aPAumhlGxus=}
    dev: false

  /killable/1.0.1:
    resolution: {integrity: sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=}
    dev: true

  /kind-of/3.2.2:
    resolution: {integrity: sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: true

  /kind-of/4.0.0:
    resolution: {integrity: sha1-IIE989cSkosgc3hpGkUGb65y3Vc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: true

  /kind-of/5.1.0:
    resolution: {integrity: sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /kind-of/6.0.3:
    resolution: {integrity: sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /klaw/1.3.1:
    resolution: {integrity: sha1-QIhDO0azsbolnXh4XY6W9zugJDk=}
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: false

  /launch-editor-middleware/2.6.0:
    resolution: {integrity: sha1-K6T+S2ldf+PUTe6GttRtV7gzLf0=}
    dependencies:
      launch-editor: 2.6.0
    dev: true

  /launch-editor/2.6.0:
    resolution: {integrity: sha1-TAwaasEmxXK9n/mjDaHSyuZt79c=}
    dependencies:
      picocolors: 1.0.0
      shell-quote: 1.8.1
    dev: true

  /levn/0.3.0:
    resolution: {integrity: sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2
    dev: true

  /lines-and-columns/1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=}
    dev: true

  /lint-staged/9.5.0:
    resolution: {integrity: sha1-KQ7GBSUq9kbZt01zoPoRg2KwWjM=}
    hasBin: true
    dependencies:
      chalk: 2.4.2
      commander: 2.20.3
      cosmiconfig: 5.2.1
      debug: 4.3.4
      dedent: 0.7.0
      del: 5.1.0
      execa: 2.1.0
      listr: 0.14.3
      log-symbols: 3.0.0
      micromatch: 4.0.5
      normalize-path: 3.0.0
      please-upgrade-node: 3.2.0
      string-argv: 0.3.2
      stringify-object: 3.3.0
    transitivePeerDependencies:
      - supports-color
      - zen-observable
    dev: true

  /listr-silent-renderer/1.1.1:
    resolution: {integrity: sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4=}
    engines: {node: '>=4'}
    dev: true

  /listr-update-renderer/0.5.0_listr@0.14.3:
    resolution: {integrity: sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI=}
    engines: {node: '>=6'}
    peerDependencies:
      listr: ^0.14.2
    dependencies:
      chalk: 1.1.3
      cli-truncate: 0.2.1
      elegant-spinner: 1.0.1
      figures: 1.7.0
      indent-string: 3.2.0
      listr: 0.14.3
      log-symbols: 1.0.2
      log-update: 2.3.0
      strip-ansi: 3.0.1
    dev: true

  /listr-verbose-renderer/0.5.0:
    resolution: {integrity: sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s=}
    engines: {node: '>=4'}
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      date-fns: 1.30.1
      figures: 2.0.0
    dev: true

  /listr/0.14.3:
    resolution: {integrity: sha1-L+qQlgTkNL5GTFC926DUlpKPpYY=}
    engines: {node: '>=6'}
    dependencies:
      '@samverschueren/stream-to-observable': 0.3.1_rxjs@6.6.7
      is-observable: 1.1.0
      is-promise: 2.2.2
      is-stream: 1.1.0
      listr-silent-renderer: 1.1.1
      listr-update-renderer: 0.5.0_listr@0.14.3
      listr-verbose-renderer: 0.5.0
      p-map: 2.1.0
      rxjs: 6.6.7
    transitivePeerDependencies:
      - zen-observable
    dev: true

  /loader-fs-cache/1.0.3:
    resolution: {integrity: sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k=}
    dependencies:
      find-cache-dir: 0.1.1
      mkdirp: 0.5.6
    dev: true

  /loader-runner/2.4.0:
    resolution: {integrity: sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dev: true

  /loader-utils/0.2.17:
    resolution: {integrity: sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=}
    dependencies:
      big.js: 3.2.0
      emojis-list: 2.1.0
      json5: 0.5.1
      object-assign: 4.1.1
    dev: true

  /loader-utils/1.4.2:
    resolution: {integrity: sha1-KalX86Y5c4g+toTxD/09FR/sAaM=}
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.2

  /loader-utils/2.0.4:
    resolution: {integrity: sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=}
    engines: {node: '>=8.9.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3

  /locate-path/3.0.0:
    resolution: {integrity: sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=}
    engines: {node: '>=6'}
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0
    dev: true

  /locate-path/5.0.0:
    resolution: {integrity: sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0

  /lodash.debounce/4.0.8:
    resolution: {integrity: sha1-gteb/zCmfEAF/9XiUVMArZyk168=}
    dev: true

  /lodash.defaultsdeep/4.6.1:
    resolution: {integrity: sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY=}
    dev: true

  /lodash.kebabcase/4.1.1:
    resolution: {integrity: sha1-hImxyw0p/4gZXM7KRI/21swpXDY=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz}

  /lodash.mapvalues/4.6.0:
    resolution: {integrity: sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=}
    dev: true

  /lodash.memoize/4.1.2:
    resolution: {integrity: sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=}
    dev: true

  /lodash.transform/4.6.0:
    resolution: {integrity: sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A=}
    dev: true

  /lodash.uniq/4.5.0:
    resolution: {integrity: sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=}
    dev: true

  /lodash/4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=}

  /log-symbols/1.0.2:
    resolution: {integrity: sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      chalk: 1.1.3
    dev: true

  /log-symbols/2.2.0:
    resolution: {integrity: sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=}
    engines: {node: '>=4'}
    dependencies:
      chalk: 2.4.2
    dev: true

  /log-symbols/3.0.0:
    resolution: {integrity: sha1-86CFFqXeqJMzan3uFNGKHP2rd8Q=}
    engines: {node: '>=8'}
    dependencies:
      chalk: 2.4.2
    dev: true

  /log-update/2.3.0:
    resolution: {integrity: sha1-iDKP19HOeTiykoN0bwsbwSayRwg=}
    engines: {node: '>=4'}
    dependencies:
      ansi-escapes: 3.2.0
      cli-cursor: 2.1.0
      wrap-ansi: 3.0.1
    dev: true

  /loglevel/1.8.1:
    resolution: {integrity: sha1-XGIfg9W0jFSuk7YVY1P1VZYzd7Q=}
    engines: {node: '>= 0.6.0'}
    dev: true

  /loose-envify/1.4.0:
    resolution: {integrity: sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: true

  /lower-case/1.1.4:
    resolution: {integrity: sha1-miyr0bno4K6ZOkv31YdcOcQujqw=}
    dev: true

  /lowercase-keys/1.0.1:
    resolution: {integrity: sha1-b54wtHCE2XGnyCD/FabFFnt0wm8=}
    engines: {node: '>=0.10.0'}
    dev: false

  /lru-cache/4.1.5:
    resolution: {integrity: sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=}
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2
    dev: true

  /lru-cache/5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=}
    dependencies:
      yallist: 3.1.1
    dev: true

  /lru-cache/6.0.0:
    resolution: {integrity: sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /make-dir/2.1.0:
    resolution: {integrity: sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=}
    engines: {node: '>=6'}
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    dev: true

  /make-dir/3.1.0:
    resolution: {integrity: sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=}
    engines: {node: '>=8'}
    dependencies:
      semver: 6.3.1
    dev: true

  /map-cache/0.2.2:
    resolution: {integrity: sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /map-visit/1.0.0:
    resolution: {integrity: sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-visit: 1.0.1
    dev: true

  /material-colors/1.2.6:
    resolution: {integrity: sha1-bRlYhxEmmSzuzHL0vMTY8BCGX0Y=}
    dev: false

  /md5.js/1.3.5:
    resolution: {integrity: sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /mdn-data/2.0.14:
    resolution: {integrity: sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=}
    dev: true

  /mdn-data/2.0.4:
    resolution: {integrity: sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=}
    dev: true

  /media-typer/0.3.0:
    resolution: {integrity: sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=}
    engines: {node: '>= 0.6'}
    dev: true

  /memory-fs/0.2.0:
    resolution: {integrity: sha1-8rslNovBIeORwlIN6Slpyu4KApA=}
    dev: true

  /memory-fs/0.4.1:
    resolution: {integrity: sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8
    dev: true

  /memory-fs/0.5.0:
    resolution: {integrity: sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8
    dev: true

  /merge-descriptors/1.0.1:
    resolution: {integrity: sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=}
    dev: true

  /merge-options/1.0.1:
    resolution: {integrity: sha1-KmSyRFe+zU5NxggoMkfpTOWJqjI=}
    engines: {node: '>=4'}
    dependencies:
      is-plain-obj: 1.1.0
    dev: true

  /merge-source-map/1.1.0:
    resolution: {integrity: sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=}
    dependencies:
      source-map: 0.6.1
    dev: true

  /merge-stream/2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=}
    dev: true

  /merge2/1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=}
    engines: {node: '>= 8'}
    dev: true

  /methods/1.1.2:
    resolution: {integrity: sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=}
    engines: {node: '>= 0.6'}
    dev: true

  /micromatch/3.1.0:
    resolution: {integrity: sha1-UQLU6vILaZfWAI46z+HESj+oFeI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 1.0.0
      extend-shallow: 2.0.1
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 5.1.0
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /micromatch/3.1.10:
    resolution: {integrity: sha1-cIWbyVyYQJUvNZoGij/En57PrCM=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /micromatch/4.0.5:
    resolution: {integrity: sha1-vImZp8u/d83InxMvbkZwUbSQkMY=}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1
    dev: true

  /miller-rabin/4.0.1:
    resolution: {integrity: sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=}
    hasBin: true
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
    dev: true

  /mime-db/1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=}
    engines: {node: '>= 0.6'}

  /mime-types/2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0

  /mime/1.6.0:
    resolution: {integrity: sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /mime/2.6.0:
    resolution: {integrity: sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=}
    engines: {node: '>=4.0.0'}
    hasBin: true
    dev: true

  /mimic-fn/1.2.0:
    resolution: {integrity: sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=}
    engines: {node: '>=4'}
    dev: true

  /mimic-fn/2.1.0:
    resolution: {integrity: sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=}
    engines: {node: '>=6'}
    dev: true

  /mini-css-extract-plugin/0.9.0_webpack@4.46.0:
    resolution: {integrity: sha1-R/LPB6oWWrNXM7H8l9TEbAVkM54=}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.4.0
    dependencies:
      loader-utils: 1.4.2
      normalize-url: 1.9.1
      schema-utils: 1.0.0
      webpack: 4.46.0
      webpack-sources: 1.4.3
    dev: true

  /minimalistic-assert/1.0.1:
    resolution: {integrity: sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=}
    dev: true

  /minimalistic-crypto-utils/1.0.1:
    resolution: {integrity: sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=}
    dev: true

  /minimatch/3.0.8:
    resolution: {integrity: sha1-XmpZvRHiqw3hz7hD6y2C5UbDIcE=}
    dependencies:
      brace-expansion: 1.1.11
    dev: false

  /minimatch/3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=}
    dependencies:
      brace-expansion: 1.1.11

  /minimist/1.2.0:
    resolution: {integrity: sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=}
    dev: false

  /minimist/1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=}

  /minipass-collect/1.0.2:
    resolution: {integrity: sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc=}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-flush/1.0.5:
    resolution: {integrity: sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M=}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-pipeline/1.2.4:
    resolution: {integrity: sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw=}
    engines: {node: '>=8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass/3.3.6:
    resolution: {integrity: sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=}
    engines: {node: '>=8'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /minipass/5.0.0:
    resolution: {integrity: sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0=}
    engines: {node: '>=8'}
    dev: true

  /minizlib/2.1.2:
    resolution: {integrity: sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0
    dev: true

  /mississippi/3.0.0:
    resolution: {integrity: sha1-6goykfl+C16HdrNj1fChLZTGcCI=}
    engines: {node: '>=4.0.0'}
    dependencies:
      concat-stream: 1.6.2
      duplexify: 3.7.1
      end-of-stream: 1.4.4
      flush-write-stream: 1.1.1
      from2: 2.3.0
      parallel-transform: 1.2.0
      pump: 3.0.0
      pumpify: 1.5.1
      stream-each: 1.2.3
      through2: 2.0.5
    dev: true

  /mitt/1.1.2:
    resolution: {integrity: sha1-OA5hSA1qYVtmDwertg1R4KTkvtY=}
    dev: true

  /mitt/2.1.0:
    resolution: {integrity: sha1-90BXfCMXbGIFsSGylzUU6t4bIjA=}
    dev: false

  /mixin-deep/1.3.2:
    resolution: {integrity: sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1
    dev: true

  /mkdirp-then/1.2.0:
    resolution: {integrity: sha1-pJLIecpNhz9e5FAI+PVf0BUN48U=}
    dependencies:
      any-promise: 1.3.0
      mkdirp: 0.5.6
    dev: false

  /mkdirp/0.5.6:
    resolution: {integrity: sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=}
    hasBin: true
    dependencies:
      minimist: 1.2.8

  /mkdirp/1.0.4:
    resolution: {integrity: sha1-PrXtYmInVteaXw4qIh3+utdcL34=}
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  /mobile-drag-drop/2.2.0:
    resolution: {integrity: sha1-2eAEQWQ24yXz0Q5RVw8gcTltzDo=}
    dev: false

  /mobius1-selectr/2.4.13:
    resolution: {integrity: sha1-ABnf2fmEhA1uQPcGg6s+x4zjtd8=}
    dev: false

  /move-concurrently/1.0.1:
    resolution: {integrity: sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=}
    dependencies:
      aproba: 1.2.0
      copy-concurrently: 1.0.5
      fs-write-stream-atomic: 1.0.10
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: true

  /ms/0.7.1:
    resolution: {integrity: sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=}
    dev: false

  /ms/2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=}

  /ms/2.1.2:
    resolution: {integrity: sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=}
    dev: true

  /ms/2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=}
    dev: true

  /multicast-dns-service-types/1.1.0:
    resolution: {integrity: sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=}
    dev: true

  /multicast-dns/6.2.3:
    resolution: {integrity: sha1-oOx72QVcQoL3kMPIL04o2zsxsik=}
    hasBin: true
    dependencies:
      dns-packet: 1.3.4
      thunky: 1.1.0
    dev: true

  /mute-stream/0.0.8:
    resolution: {integrity: sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=}

  /mz/2.7.0:
    resolution: {integrity: sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=}
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  /nan/2.17.0:
    resolution: {integrity: sha1-wBUKI2ihgvAz6apRlex26kGhmcs=}
    dev: true
    optional: true

  /nanoid/3.3.6:
    resolution: {integrity: sha1-RDOAyFbW6fmCQmfZYLQjatWD6kw=}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: false

  /nanomatch/1.2.13:
    resolution: {integrity: sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    dev: true

  /natural-compare/1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=}
    dev: true

  /negotiator/0.6.3:
    resolution: {integrity: sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=}
    engines: {node: '>= 0.6'}
    dev: true

  /neo-async/2.6.2:
    resolution: {integrity: sha1-tKr7k+OustgXTKU88WOrfXMIMF8=}
    dev: true

  /nice-try/1.0.5:
    resolution: {integrity: sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=}
    dev: true

  /no-case/2.3.2:
    resolution: {integrity: sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=}
    dependencies:
      lower-case: 1.1.4
    dev: true

  /node-forge/0.10.0:
    resolution: {integrity: sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=}
    engines: {node: '>= 6.0.0'}
    dev: true

  /node-libs-browser/2.2.1:
    resolution: {integrity: sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=}
    dependencies:
      assert: 1.5.0
      browserify-zlib: 0.2.0
      buffer: 4.9.2
      console-browserify: 1.2.0
      constants-browserify: 1.0.0
      crypto-browserify: 3.12.0
      domain-browser: 1.2.0
      events: 3.3.0
      https-browserify: 1.0.0
      os-browserify: 0.3.0
      path-browserify: 0.0.1
      process: 0.11.10
      punycode: 1.4.1
      querystring-es3: 0.2.1
      readable-stream: 2.3.8
      stream-browserify: 2.0.2
      stream-http: 2.8.3
      string_decoder: 1.3.0
      timers-browserify: 2.0.12
      tty-browserify: 0.0.0
      url: 0.11.1
      util: 0.11.1
      vm-browserify: 1.1.2
    dev: true

  /node-releases/2.0.13:
    resolution: {integrity: sha1-1e0WJ8I+NGHoGbAuV7deSJmxyB0=}
    dev: true

  /node-status-codes/1.0.0:
    resolution: {integrity: sha1-WuVUHQJGRdMqWPzdyc7s6nrjrC8=}
    engines: {node: '>=0.10.0'}
    dev: false

  /node-uuid/1.4.3:
    resolution: {integrity: sha1-MZu3pW58tj8AtcDNeFHNS03fHfk=}
    deprecated: Use uuid module instead
    hasBin: true
    dev: false

  /nomnom/1.8.1:
    resolution: {integrity: sha1-IVH3Ikcrp55Qp2/BJbuMjy5Nwqc=}
    deprecated: Package no longer supported. Contact <EMAIL> for more info.
    dependencies:
      chalk: 0.4.0
      underscore: 1.6.0
    dev: false

  /normalize-package-data/2.5.0:
    resolution: {integrity: sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=}
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.4
      semver: 5.7.2
      validate-npm-package-license: 3.0.4

  /normalize-path/1.0.0:
    resolution: {integrity: sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=}
    engines: {node: '>=0.10.0'}
    dev: true

  /normalize-path/2.1.1:
    resolution: {integrity: sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      remove-trailing-separator: 1.1.0
    dev: true

  /normalize-path/3.0.0:
    resolution: {integrity: sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=}
    engines: {node: '>=0.10.0'}
    dev: true

  /normalize-range/0.1.2:
    resolution: {integrity: sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=}
    engines: {node: '>=0.10.0'}
    dev: true

  /normalize-url/1.9.1:
    resolution: {integrity: sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=}
    engines: {node: '>=4'}
    dependencies:
      object-assign: 4.1.1
      prepend-http: 1.0.4
      query-string: 4.3.4
      sort-keys: 1.1.2
    dev: true

  /normalize-url/3.3.0:
    resolution: {integrity: sha1-suHE3E98bVd0PfczpPWXjRhlBVk=}
    engines: {node: '>=6'}
    dev: true

  /normalize-wheel/1.0.1:
    resolution: {integrity: sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU=}
    dev: false

  /npm-normalize-package-bin/1.0.1:
    resolution: {integrity: sha1-bnmkHyP9I1wGIyGCKNp9nCO49uI=}
    dev: false

  /npm-package-arg/4.1.0:
    resolution: {integrity: sha1-LgFfisAHN8uX+ZfJy/BZ9Cp0Un0=}
    dependencies:
      hosted-git-info: 2.8.9
      semver: 5.0.1
    dev: false

  /npm-run-path/2.0.2:
    resolution: {integrity: sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=}
    engines: {node: '>=4'}
    dependencies:
      path-key: 2.0.1
    dev: true

  /npm-run-path/3.1.0:
    resolution: {integrity: sha1-f5G+MX9qRm7+08nymArYpO6LD6U=}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: true

  /npm-run-path/4.0.1:
    resolution: {integrity: sha1-t+zR5e1T2o43pV4cImnguX7XSOo=}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: true

  /nth-check/1.0.2:
    resolution: {integrity: sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=}
    dependencies:
      boolbase: 1.0.0
    dev: true

  /nth-check/2.1.1:
    resolution: {integrity: sha1-yeq0KO/842zWuSySS9sADvHx7R0=}
    dependencies:
      boolbase: 1.0.0
    dev: true

  /num2fraction/1.2.2:
    resolution: {integrity: sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=}
    dev: true

  /number-is-nan/1.0.1:
    resolution: {integrity: sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=}
    engines: {node: '>=0.10.0'}
    dev: true

  /oauth-sign/0.9.0:
    resolution: {integrity: sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=}

  /object-assign/4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=}
    engines: {node: '>=0.10.0'}

  /object-copy/0.1.0:
    resolution: {integrity: sha1-fn2Fi3gb18mRpBupde04EnVOmYw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2
    dev: true

  /object-hash/1.3.1:
    resolution: {integrity: sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=}
    engines: {node: '>= 0.10.0'}
    dev: true

  /object-inspect/1.12.3:
    resolution: {integrity: sha1-umLf/WfuJWyMCG365p4BbNHxmLk=}

  /object-is/1.1.5:
    resolution: {integrity: sha1-ud7qpfx/GEag+uzc7sE45XePU6w=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
    dev: true

  /object-keys/0.4.0:
    resolution: {integrity: sha1-KKaq50KN0sOpLz2V8hM13SBOAzY=}
    dev: false

  /object-keys/1.1.1:
    resolution: {integrity: sha1-HEfyct8nfzsdrwYWd9nILiMixg4=}
    engines: {node: '>= 0.4'}
    dev: true

  /object-visit/1.0.1:
    resolution: {integrity: sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: true

  /object.assign/4.1.4:
    resolution: {integrity: sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      has-symbols: 1.0.3
      object-keys: 1.1.1
    dev: true

  /object.fromentries/2.0.6:
    resolution: {integrity: sha1-zbBNoIxTnP+pEtzTaLiG4JBL+nM=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: true

  /object.getownpropertydescriptors/2.1.6:
    resolution: {integrity: sha1-Xlw4TdIJ+k7//q0546BRJ3DMwxI=}
    engines: {node: '>= 0.8'}
    dependencies:
      array.prototype.reduce: 1.0.5
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      safe-array-concat: 1.0.0
    dev: true

  /object.groupby/1.0.0:
    resolution: {integrity: sha1-yyklnPkPN+e6xkN2hsHqjJFtEqk=}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      get-intrinsic: 1.2.1
    dev: true

  /object.pick/1.3.0:
    resolution: {integrity: sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: true

  /object.values/1.1.6:
    resolution: {integrity: sha1-SruqceukfWNYnUAoVvkIJD7qmx0=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: true

  /obuf/1.1.2:
    resolution: {integrity: sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=}
    dev: true

  /on-finished/2.4.1:
    resolution: {integrity: sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1
    dev: true

  /on-headers/1.0.2:
    resolution: {integrity: sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=}
    engines: {node: '>= 0.8'}
    dev: true

  /once/1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=}
    dependencies:
      wrappy: 1.0.2

  /onetime/2.0.1:
    resolution: {integrity: sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=}
    engines: {node: '>=4'}
    dependencies:
      mimic-fn: 1.2.0
    dev: true

  /onetime/5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: true

  /open/6.4.0:
    resolution: {integrity: sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=}
    engines: {node: '>=8'}
    dependencies:
      is-wsl: 1.1.0
    dev: true

  /opener/1.5.2:
    resolution: {integrity: sha1-XTfh81B3udysQwE3InGv3rKhNZg=}
    hasBin: true
    dev: true

  /opn/5.5.0:
    resolution: {integrity: sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=}
    engines: {node: '>=4'}
    dependencies:
      is-wsl: 1.1.0
    dev: true

  /optionator/0.8.3:
    resolution: {integrity: sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.5
    dev: true

  /ora/3.4.0:
    resolution: {integrity: sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=}
    engines: {node: '>=6'}
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-spinners: 2.9.0
      log-symbols: 2.2.0
      strip-ansi: 5.2.0
      wcwidth: 1.0.1
    dev: true

  /os-browserify/0.3.0:
    resolution: {integrity: sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=}
    dev: true

  /os-tmpdir/1.0.2:
    resolution: {integrity: sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /p-finally/1.0.0:
    resolution: {integrity: sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=}
    engines: {node: '>=4'}
    dev: true

  /p-finally/2.0.1:
    resolution: {integrity: sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE=}
    engines: {node: '>=8'}
    dev: true

  /p-limit/2.3.0:
    resolution: {integrity: sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0

  /p-locate/3.0.0:
    resolution: {integrity: sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=}
    engines: {node: '>=6'}
    dependencies:
      p-limit: 2.3.0
    dev: true

  /p-locate/4.1.0:
    resolution: {integrity: sha1-o0KLtwiLOmApL2aRkni3wpetTwc=}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0

  /p-map/2.1.0:
    resolution: {integrity: sha1-MQko/u+cnsxltosXaTAYpmXOoXU=}
    engines: {node: '>=6'}
    dev: true

  /p-map/3.0.0:
    resolution: {integrity: sha1-1wTZr4orpoTiYA2aIVmD1BQal50=}
    engines: {node: '>=8'}
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /p-map/4.0.0:
    resolution: {integrity: sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=}
    engines: {node: '>=10'}
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /p-retry/3.0.1:
    resolution: {integrity: sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=}
    engines: {node: '>=6'}
    dependencies:
      retry: 0.12.0
    dev: true

  /p-try/2.2.0:
    resolution: {integrity: sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=}
    engines: {node: '>=6'}

  /pako/0.2.9:
    resolution: {integrity: sha1-8/dSL073gjSNqBYbrZ7P1Rv4OnU=}
    dev: false

  /pako/1.0.11:
    resolution: {integrity: sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=}
    dev: true

  /parallel-transform/1.2.0:
    resolution: {integrity: sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=}
    dependencies:
      cyclist: 1.0.2
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: true

  /param-case/2.1.1:
    resolution: {integrity: sha1-35T9jPZTHs915r75oIWPvHK+Ikc=}
    dependencies:
      no-case: 2.3.2
    dev: true

  /parent-module/1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse-asn1/5.1.6:
    resolution: {integrity: sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=}
    dependencies:
      asn1.js: 5.4.1
      browserify-aes: 1.2.0
      evp_bytestokey: 1.0.3
      pbkdf2: 3.1.2
      safe-buffer: 5.2.1
    dev: true

  /parse-glob/3.0.4:
    resolution: {integrity: sha1-ssN2z7EfNVE7rdFz7wu246OIORw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      glob-base: 0.3.0
      is-dotfile: 1.0.3
      is-extglob: 1.0.0
      is-glob: 2.0.1
    dev: false

  /parse-json/2.2.0:
    resolution: {integrity: sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=}
    engines: {node: '>=0.10.0'}
    dependencies:
      error-ex: 1.3.2
    dev: false

  /parse-json/4.0.0:
    resolution: {integrity: sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=}
    engines: {node: '>=4'}
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2
    dev: true

  /parse-json/5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.22.10
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4
    dev: true

  /parse5-htmlparser2-tree-adapter/6.0.1:
    resolution: {integrity: sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=}
    dependencies:
      parse5: 6.0.1
    dev: true

  /parse5/5.1.1:
    resolution: {integrity: sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=}
    dev: true

  /parse5/6.0.1:
    resolution: {integrity: sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=}
    dev: true

  /parseqs/0.0.6:
    resolution: {integrity: sha1-jku1oZ0c3IRKCKyXTTTic6+mcNU=}
    dev: false

  /parserlib/1.1.1:
    resolution: {integrity: sha1-pkz6ckBiQ0/fw1HJpOwtkrlMBvQ=}
    dev: false

  /parseuri/0.0.6:
    resolution: {integrity: sha1-4Ulugp46wv9H85pN0ESzKCPEolo=}
    dev: false

  /parseurl/1.3.3:
    resolution: {integrity: sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=}
    engines: {node: '>= 0.8'}
    dev: true

  /particles.vue/2.43.1_vue@2.7.14:
    resolution: {integrity: sha1-IoyA2OmVdx74MvJ5yi48WdoZOeo=}
    deprecated: Version 2.x is the current version, v1 is obsolete now
    peerDependencies:
      vue: <3
    dependencies:
      tsparticles: 1.43.1
      vue: 2.7.14
      vue-class-component: 7.2.6_vue@2.7.14
      vue-property-decorator: 9.1.2_4cf18c60478cc4559d7fa95f13b081de
    dev: false

  /pascalcase/0.1.1:
    resolution: {integrity: sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-browserify/0.0.1:
    resolution: {integrity: sha1-5sTd1+06onxoogzE5Q4aTug7vEo=}
    dev: true

  /path-dirname/1.0.2:
    resolution: {integrity: sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=}
    dev: true

  /path-exists/2.1.0:
    resolution: {integrity: sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=}
    engines: {node: '>=0.10.0'}
    dependencies:
      pinkie-promise: 2.0.1
    dev: true

  /path-exists/3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=}
    engines: {node: '>=4'}
    dev: true

  /path-exists/4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=}
    engines: {node: '>=8'}

  /path-is-absolute/1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=}
    engines: {node: '>=0.10.0'}

  /path-is-inside/1.0.2:
    resolution: {integrity: sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=}
    dev: true

  /path-key/2.0.1:
    resolution: {integrity: sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=}
    engines: {node: '>=4'}
    dev: true

  /path-key/3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=}
    engines: {node: '>=8'}
    dev: true

  /path-parse/1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=}

  /path-to-regexp/0.1.7:
    resolution: {integrity: sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=}
    dev: true

  /path-type/3.0.0:
    resolution: {integrity: sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=}
    engines: {node: '>=4'}
    dependencies:
      pify: 3.0.0
    dev: true

  /path-type/4.0.0:
    resolution: {integrity: sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=}
    engines: {node: '>=8'}
    dev: true

  /pbkdf2/3.1.2:
    resolution: {integrity: sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=}
    engines: {node: '>=0.12'}
    dependencies:
      create-hash: 1.2.0
      create-hmac: 1.1.7
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: true

  /pdfjs-dist/2.6.347:
    resolution: {integrity: sha1-8lftZug76QDND9KFJKIYf7niXNU=}
    dev: false

  /peek-stream/1.1.3:
    resolution: {integrity: sha1-OzXYS3zLvSYv/zHcENpWhW6tbWc=}
    dependencies:
      buffer-from: 1.1.2
      duplexify: 3.7.1
      through2: 2.0.5
    dev: false

  /performance-now/2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=}

  /picocolors/0.2.1:
    resolution: {integrity: sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=}
    dev: true

  /picocolors/1.0.0:
    resolution: {integrity: sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=}

  /picomatch/2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=}
    engines: {node: '>=8.6'}
    dev: true

  /picomodal/3.0.0:
    resolution: {integrity: sha1-+s0w9PvzSoCcHgTqUl8ATzmcC4I=}
    dev: false

  /pify/2.3.0:
    resolution: {integrity: sha1-7RQaasBDqEnqWISY59yosVMw6Qw=}
    engines: {node: '>=0.10.0'}
    dev: true

  /pify/3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=}
    engines: {node: '>=4'}
    dev: true

  /pify/4.0.1:
    resolution: {integrity: sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=}
    engines: {node: '>=6'}
    dev: true

  /pinkie-promise/2.0.1:
    resolution: {integrity: sha1-ITXW36ejWMBprJsXh3YogihFD/o=}
    engines: {node: '>=0.10.0'}
    dependencies:
      pinkie: 2.0.4

  /pinkie/2.0.4:
    resolution: {integrity: sha1-clVrgM+g1IqXToDnckjoDtT3+HA=}
    engines: {node: '>=0.10.0'}

  /pinyin-match/1.2.4:
    resolution: {integrity: sha1-lDgbtlAc/Twk+SP+NsgXENjEm8w=}
    dev: false

  /pkg-dir/1.0.0:
    resolution: {integrity: sha1-ektQio1bstYp1EcFb/TpyTFM89Q=}
    engines: {node: '>=0.10.0'}
    dependencies:
      find-up: 1.1.2
    dev: true

  /pkg-dir/3.0.0:
    resolution: {integrity: sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
    dev: true

  /pkg-dir/4.2.0:
    resolution: {integrity: sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
    dev: true

  /please-upgrade-node/3.2.0:
    resolution: {integrity: sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=}
    dependencies:
      semver-compare: 1.0.0
    dev: true

  /pngjs/5.0.0:
    resolution: {integrity: sha1-553SshV2f9nARWHAEjbflgvOf7s=}
    engines: {node: '>=10.13.0'}
    dev: false

  /pnp-webpack-plugin/1.7.0:
    resolution: {integrity: sha1-ZXQThPbYBW824iVajWf/wghm9ck=}
    engines: {node: '>=6'}
    dependencies:
      ts-pnp: 1.2.0
    transitivePeerDependencies:
      - typescript
    dev: true

  /portfinder/1.0.32:
    resolution: {integrity: sha1-L+G55YOJcSQp3CvqW+shRhRsf4E=}
    engines: {node: '>= 0.12.0'}
    dependencies:
      async: 2.6.4
      debug: 3.2.7
      mkdirp: 0.5.6
    dev: true

  /posix-character-classes/0.1.1:
    resolution: {integrity: sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=}
    engines: {node: '>=0.10.0'}
    dev: true

  /postcss-calc/7.0.5:
    resolution: {integrity: sha1-+KbpnxLmGcLrwjz2xIb9wVhgkz4=}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.0.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-colormin/4.0.3:
    resolution: {integrity: sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.10
      color: 3.2.1
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-convert-values/4.0.1:
    resolution: {integrity: sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-discard-comments/4.0.2:
    resolution: {integrity: sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-discard-duplicates/4.0.2:
    resolution: {integrity: sha1-P+EzzTyCKC5VD8myORdqkge3hOs=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-discard-empty/4.0.1:
    resolution: {integrity: sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-discard-overridden/4.0.1:
    resolution: {integrity: sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-load-config/2.1.2:
    resolution: {integrity: sha1-xepQTyxK7zPHNZo03jVzdyrXUCo=}
    engines: {node: '>= 4'}
    dependencies:
      cosmiconfig: 5.2.1
      import-cwd: 2.1.0
    dev: true

  /postcss-loader/3.0.0:
    resolution: {integrity: sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0=}
    engines: {node: '>= 6'}
    dependencies:
      loader-utils: 1.4.2
      postcss: 7.0.39
      postcss-load-config: 2.1.2
      schema-utils: 1.0.0
    dev: true

  /postcss-merge-longhand/4.0.11:
    resolution: {integrity: sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=}
    engines: {node: '>=6.9.0'}
    dependencies:
      css-color-names: 0.0.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      stylehacks: 4.0.3
    dev: true

  /postcss-merge-rules/4.0.3:
    resolution: {integrity: sha1-NivqT/Wh+Y5AdacTxsslrv75plA=}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.10
      caniuse-api: 3.0.0
      cssnano-util-same-parent: 4.0.1
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
      vendors: 1.0.4
    dev: true

  /postcss-minify-font-values/4.0.2:
    resolution: {integrity: sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-minify-gradients/4.0.2:
    resolution: {integrity: sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      is-color-stop: 1.1.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-minify-params/4.0.2:
    resolution: {integrity: sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=}
    engines: {node: '>=6.9.0'}
    dependencies:
      alphanum-sort: 1.0.2
      browserslist: 4.21.10
      cssnano-util-get-arguments: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      uniqs: 2.0.0
    dev: true

  /postcss-minify-selectors/4.0.2:
    resolution: {integrity: sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=}
    engines: {node: '>=6.9.0'}
    dependencies:
      alphanum-sort: 1.0.2
      has: 1.0.3
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
    dev: true

  /postcss-modules-extract-imports/2.0.0:
    resolution: {integrity: sha1-gYcZoa4doyX5gyRGsBE27rSTzX4=}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-modules-local-by-default/3.0.3:
    resolution: {integrity: sha1-uxTgzHgnnVBNvcv9fgyiiZP/u7A=}
    engines: {node: '>= 6'}
    dependencies:
      icss-utils: 4.1.1
      postcss: 7.0.39
      postcss-selector-parser: 6.0.13
      postcss-value-parser: 4.2.0
    dev: true

  /postcss-modules-scope/2.2.0:
    resolution: {integrity: sha1-OFyuATzHdD9afXYC0Qc6iequYu4=}
    engines: {node: '>= 6'}
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.0.13
    dev: true

  /postcss-modules-values/3.0.0:
    resolution: {integrity: sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA=}
    dependencies:
      icss-utils: 4.1.1
      postcss: 7.0.39
    dev: true

  /postcss-normalize-charset/4.0.1:
    resolution: {integrity: sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
    dev: true

  /postcss-normalize-display-values/4.0.2:
    resolution: {integrity: sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-positions/4.0.2:
    resolution: {integrity: sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-repeat-style/4.0.2:
    resolution: {integrity: sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-string/4.0.2:
    resolution: {integrity: sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=}
    engines: {node: '>=6.9.0'}
    dependencies:
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-timing-functions/4.0.2:
    resolution: {integrity: sha1-jgCcoqOUnNr4rSPmtquZy159KNk=}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-unicode/4.0.1:
    resolution: {integrity: sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.10
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-url/4.0.1:
    resolution: {integrity: sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=}
    engines: {node: '>=6.9.0'}
    dependencies:
      is-absolute-url: 2.1.0
      normalize-url: 3.3.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-normalize-whitespace/4.0.2:
    resolution: {integrity: sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-ordered-values/4.1.2:
    resolution: {integrity: sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-prefix-selector/1.16.0_postcss@5.2.18:
    resolution: {integrity: sha1-rVtW+ac6LAkMpxYQSWMsnYm8tAQ=}
    peerDependencies:
      postcss: '>4 <9'
    dependencies:
      postcss: 5.2.18
    dev: true

  /postcss-reduce-initial/4.0.3:
    resolution: {integrity: sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.10
      caniuse-api: 3.0.0
      has: 1.0.3
      postcss: 7.0.39
    dev: true

  /postcss-reduce-transforms/4.0.2:
    resolution: {integrity: sha1-F++kBerMbge+NBSlyi0QdGgdTik=}
    engines: {node: '>=6.9.0'}
    dependencies:
      cssnano-util-get-match: 4.0.0
      has: 1.0.3
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
    dev: true

  /postcss-selector-parser/3.1.2:
    resolution: {integrity: sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=}
    engines: {node: '>=8'}
    dependencies:
      dot-prop: 5.3.0
      indexes-of: 1.0.1
      uniq: 1.0.1
    dev: true

  /postcss-selector-parser/6.0.13:
    resolution: {integrity: sha1-0F2NdrHo4XMlfvnWC3Bqjl6Zvxs=}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-svgo/4.0.3:
    resolution: {integrity: sha1-NDos26yVBdQWJD1Jb3JPOIlMlB4=}
    engines: {node: '>=6.9.0'}
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      svgo: 1.3.2
    dev: true

  /postcss-unique-selectors/4.0.1:
    resolution: {integrity: sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=}
    engines: {node: '>=6.9.0'}
    dependencies:
      alphanum-sort: 1.0.2
      postcss: 7.0.39
      uniqs: 2.0.0
    dev: true

  /postcss-value-parser/3.3.1:
    resolution: {integrity: sha1-n/giVH4okyE88cMO+lGsX9G6goE=}
    dev: true

  /postcss-value-parser/4.2.0:
    resolution: {integrity: sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=}
    dev: true

  /postcss/5.2.18:
    resolution: {integrity: sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U=}
    engines: {node: '>=0.12'}
    dependencies:
      chalk: 1.1.3
      js-base64: 2.6.4
      source-map: 0.5.7
      supports-color: 3.2.3
    dev: true

  /postcss/7.0.39:
    resolution: {integrity: sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=}
    engines: {node: '>=6.0.0'}
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1
    dev: true

  /postcss/8.4.28:
    resolution: {integrity: sha1-xsxoHtABCQcoFuFVf4ie9Rz5UKU=}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.6
      picocolors: 1.0.0
      source-map-js: 1.0.2
    dev: false

  /posthtml-parser/0.2.1:
    resolution: {integrity: sha1-NdUw3jhnQMK6JP8usvrznM3ycd0=}
    dependencies:
      htmlparser2: 3.10.1
      isobject: 2.1.0
    dev: true

  /posthtml-rename-id/1.0.12:
    resolution: {integrity: sha1-z39us3FGvxr6wx5o8YxswZrmFDM=}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /posthtml-render/1.4.0:
    resolution: {integrity: sha1-QBFAcMRYgcrLkzR9rj7/U6+8/xM=}
    engines: {node: '>=10'}
    dev: true

  /posthtml-svg-mode/1.0.3:
    resolution: {integrity: sha1-q9VU+s6BIjyrDLNn4Y5O/SpOdLA=}
    dependencies:
      merge-options: 1.0.1
      posthtml: 0.9.2
      posthtml-parser: 0.2.1
      posthtml-render: 1.4.0
    dev: true

  /posthtml/0.9.2:
    resolution: {integrity: sha1-9MBtufZ7Yf0XxOJW5+PZUVv3Jv0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      posthtml-parser: 0.2.1
      posthtml-render: 1.4.0
    dev: true

  /prelude-ls/1.1.2:
    resolution: {integrity: sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=}
    engines: {node: '>= 0.8.0'}
    dev: true

  /prepend-http/1.0.4:
    resolution: {integrity: sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=}
    engines: {node: '>=0.10.0'}

  /prettier/2.8.8:
    resolution: {integrity: sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/prettier/-/prettier-2.8.8.tgz}
    engines: {node: '>=10.13.0'}
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  /pretty-error/2.1.2:
    resolution: {integrity: sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y=}
    dependencies:
      lodash: 4.17.21
      renderkid: 2.0.7
    dev: true

  /private/0.1.8:
    resolution: {integrity: sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8=}
    engines: {node: '>= 0.6'}
    dev: true

  /process-nextick-args/2.0.1:
    resolution: {integrity: sha1-eCDZsWEgzFXKmud5JoCufbptf+I=}

  /process/0.11.10:
    resolution: {integrity: sha1-czIwDoQBYb2j5podHZGn1LwW8YI=}
    engines: {node: '>= 0.6.0'}
    dev: true

  /progress/2.0.3:
    resolution: {integrity: sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=}
    engines: {node: '>=0.4.0'}
    dev: true

  /promise-inflight/1.0.1:
    resolution: {integrity: sha1-mEcocL8igTL8vdhoEputEsPAKeM=}
    dev: true

  /promzard/0.3.0:
    resolution: {integrity: sha1-JqXW7ox97kyxIggwWs+5O6OCqe4=}
    dependencies:
      read: 1.0.7
    dev: false

  /proxy-addr/2.0.7:
    resolution: {integrity: sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=}
    engines: {node: '>= 0.10'}
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1
    dev: true

  /prr/1.0.1:
    resolution: {integrity: sha1-0/wRS6BplaRexok/SEzrHXj19HY=}
    dev: true

  /pseudomap/1.0.2:
    resolution: {integrity: sha1-8FKijacOYYkX7wqKw0wa5aaChrM=}
    dev: true

  /psl/1.9.0:
    resolution: {integrity: sha1-0N8qE38AeUVl/K87LADNCfjVpac=}

  /public-encrypt/4.0.3:
    resolution: {integrity: sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=}
    dependencies:
      bn.js: 4.12.0
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      parse-asn1: 5.1.6
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: true

  /pump/2.0.1:
    resolution: {integrity: sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  /pump/3.0.0:
    resolution: {integrity: sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: true

  /pumpify/1.5.1:
    resolution: {integrity: sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=}
    dependencies:
      duplexify: 3.7.1
      inherits: 2.0.4
      pump: 2.0.1

  /punycode/1.4.1:
    resolution: {integrity: sha1-wNWmOycYgArY4esPpSachN1BhF4=}
    dev: true

  /punycode/2.3.0:
    resolution: {integrity: sha1-9n+mfJTaj00M//mBruQRgGQZm48=}
    engines: {node: '>=6'}

  /q/1.5.1:
    resolution: {integrity: sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=}
    engines: {node: '>=0.6.0', teleport: '>=0.2.0'}
    dev: true

  /qrcode/1.5.3:
    resolution: {integrity: sha1-A6+oCRLA3M8SvJP2FaU1qtEGYXA=}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      dijkstrajs: 1.0.3
      encode-utf8: 1.0.3
      pngjs: 5.0.0
      yargs: 15.4.1
    dev: false

  /qs/6.11.0:
    resolution: {integrity: sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.4
    dev: true

  /qs/6.11.2:
    resolution: {integrity: sha1-ZL6lHxLB9dobwBSW9I/8/3xp19k=}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.4

  /qs/6.5.3:
    resolution: {integrity: sha1-Ou7/yRln7241wOSI70b7KWq3aq0=}
    engines: {node: '>=0.6'}

  /query-string/4.3.4:
    resolution: {integrity: sha1-u7aTucqRXCMlFbIosaArYJBD2+s=}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-assign: 4.1.1
      strict-uri-encode: 1.1.0
    dev: true

  /querystring-es3/0.2.1:
    resolution: {integrity: sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=}
    engines: {node: '>=0.4.x'}
    dev: true

  /querystringify/2.2.0:
    resolution: {integrity: sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=}
    dev: true

  /queue-microtask/1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=}
    dev: true

  /randombytes/2.1.0:
    resolution: {integrity: sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /randomfill/1.0.4:
    resolution: {integrity: sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=}
    dependencies:
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: true

  /range-parser/1.2.1:
    resolution: {integrity: sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=}
    engines: {node: '>= 0.6'}
    dev: true

  /raw-body/2.5.1:
    resolution: {integrity: sha1-/hsWKLGBtwAhXl/UI4n5i3E5KFc=}
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0
    dev: true

  /raw-loader/0.5.1:
    resolution: {integrity: sha1-DD0L6u2KAclm2Xh793goElKpeao=}
    dev: true

  /raw-loader/4.0.2:
    resolution: {integrity: sha1-GqxrfRrRUB5m79rBUixz5ZpYTrY=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.3.0
    dev: false

  /read-all-stream/3.1.0:
    resolution: {integrity: sha1-NcPhd/IHjveJ7kv6+kNzB06u9Po=}
    engines: {node: '>=0.10.0'}
    dependencies:
      pinkie-promise: 2.0.1
      readable-stream: 2.3.8
    dev: false

  /read-package-json/2.1.2:
    resolution: {integrity: sha1-aZKytmxxdyWf646qxzw6zSi5Iio=}
    dependencies:
      glob: 7.2.3
      json-parse-even-better-errors: 2.3.1
      normalize-package-data: 2.5.0
      npm-normalize-package-bin: 1.0.1
    dev: false

  /read-pkg/5.2.0:
    resolution: {integrity: sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=}
    engines: {node: '>=8'}
    dependencies:
      '@types/normalize-package-data': 2.4.1
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0
    dev: true

  /read/1.0.7:
    resolution: {integrity: sha1-s9oZvQUkMal2cdRKQmNK33ELQMQ=}
    engines: {node: '>=0.8'}
    dependencies:
      mute-stream: 0.0.8
    dev: false

  /readable-stream/1.0.34:
    resolution: {integrity: sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31
    dev: false

  /readable-stream/1.1.14:
    resolution: {integrity: sha1-fPTFTvZI44EwhMY23SB54WbAgdk=}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31
    dev: false

  /readable-stream/2.3.8:
    resolution: {integrity: sha1-kRJegEK7obmIf0k0X2J3Anzovps=}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  /readable-stream/3.6.2:
    resolution: {integrity: sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2
    dev: true

  /readdir-scoped-modules/1.0.2:
    resolution: {integrity: sha1-n6+jfShr5dksuuve4DDcm19AZ0c=}
    dependencies:
      debuglog: 1.0.1
      dezalgo: 1.0.4
      graceful-fs: 4.2.11
      once: 1.4.0
    dev: false

  /readdirp/2.2.1:
    resolution: {integrity: sha1-DodiKjMlqjPokihcr4tOhGUppSU=}
    engines: {node: '>=0.10'}
    dependencies:
      graceful-fs: 4.2.11
      micromatch: 3.1.10
      readable-stream: 2.3.8
    dev: true

  /readdirp/3.6.0:
    resolution: {integrity: sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1
    dev: true

  /regenerate-unicode-properties/10.1.0:
    resolution: {integrity: sha1-fDGSyrbdJOIctEYeXd190k+oN0w=}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
    dev: true

  /regenerate/1.4.2:
    resolution: {integrity: sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=}
    dev: true

  /regenerator-runtime/0.10.5:
    resolution: {integrity: sha1-M2w+/BIgrc7dosn6tntaeVWjNlg=}
    dev: false

  /regenerator-runtime/0.11.1:
    resolution: {integrity: sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=}

  /regenerator-runtime/0.14.0:
    resolution: {integrity: sha1-XhnWjrEtSG95fhWjxqkY987F60U=}

  /regenerator-transform/0.10.1:
    resolution: {integrity: sha1-HkmWg3Ix2ot/PPQRTXG1aRoGgN0=}
    dependencies:
      babel-runtime: 6.26.0
      babel-types: 6.26.0
      private: 0.1.8
    dev: true

  /regenerator-transform/0.15.2:
    resolution: {integrity: sha1-W7rli1IgmOvfCbyi+Dg4kpABx6Q=}
    dependencies:
      '@babel/runtime': 7.22.10
    dev: true

  /regex-not/1.0.2:
    resolution: {integrity: sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0
    dev: true

  /regexp.prototype.flags/1.5.0:
    resolution: {integrity: sha1-/nziXn5Myo2ze2Y0yKLHAJGZucs=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      functions-have-names: 1.2.3
    dev: true

  /regexpp/2.0.1:
    resolution: {integrity: sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=}
    engines: {node: '>=6.5.0'}
    dev: true

  /regexpp/3.2.0:
    resolution: {integrity: sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=}
    engines: {node: '>=8'}
    dev: true

  /regexpu-core/2.0.0:
    resolution: {integrity: sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA=}
    dependencies:
      regenerate: 1.4.2
      regjsgen: 0.2.0
      regjsparser: 0.1.5
    dev: true

  /regexpu-core/5.3.2:
    resolution: {integrity: sha1-EaKwaITzUnrsPpPbv0o7lYqVVGs=}
    engines: {node: '>=4'}
    dependencies:
      '@babel/regjsgen': 0.8.0
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.1.0
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.1.0
    dev: true

  /regjsgen/0.2.0:
    resolution: {integrity: sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=}
    dev: true

  /regjsparser/0.1.5:
    resolution: {integrity: sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=}
    hasBin: true
    dependencies:
      jsesc: 0.5.0
    dev: true

  /regjsparser/0.9.1:
    resolution: {integrity: sha1-Jy0FqhDHwfZwlbH/Ct2uhEL8Vwk=}
    hasBin: true
    dependencies:
      jsesc: 0.5.0
    dev: true

  /relateurl/0.2.7:
    resolution: {integrity: sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=}
    engines: {node: '>= 0.10'}
    dev: true

  /remove-trailing-separator/1.1.0:
    resolution: {integrity: sha1-wkvOKig62tW8P1jg1IJJuSN52O8=}
    dev: true

  /renderkid/2.0.7:
    resolution: {integrity: sha1-Rk8namvc7mBvShWZP5sp/HTKhgk=}
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 3.0.1
    dev: true

  /repeat-element/1.1.4:
    resolution: {integrity: sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=}
    engines: {node: '>=0.10.0'}
    dev: true

  /repeat-string/1.6.1:
    resolution: {integrity: sha1-jcrkcOHIirwtYA//Sndihtp15jc=}
    engines: {node: '>=0.10'}
    dev: true

  /request/2.88.2:
    resolution: {integrity: sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.12.0
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  /require-directory/2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=}
    engines: {node: '>=0.10.0'}

  /require-main-filename/2.0.0:
    resolution: {integrity: sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=}

  /requires-port/1.0.0:
    resolution: {integrity: sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=}
    dev: true

  /resize-observer-polyfill/1.5.1:
    resolution: {integrity: sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=}
    dev: false

  /resolve-cwd/2.0.0:
    resolution: {integrity: sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=}
    engines: {node: '>=4'}
    dependencies:
      resolve-from: 3.0.0
    dev: true

  /resolve-from/3.0.0:
    resolution: {integrity: sha1-six699nWiBvItuZTM17rywoYh0g=}
    engines: {node: '>=4'}
    dev: true

  /resolve-from/4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=}
    engines: {node: '>=4'}
    dev: true

  /resolve-url/0.2.1:
    resolution: {integrity: sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=}
    deprecated: https://github.com/lydell/resolve-url#deprecated
    dev: true

  /resolve/1.22.4:
    resolution: {integrity: sha1-HcQN9GVUza+JSKSGoQ9roeICbDQ=}
    hasBin: true
    dependencies:
      is-core-module: 2.13.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /restore-cursor/2.0.0:
    resolution: {integrity: sha1-n37ih/gv0ybU/RYpI9YhKe7g368=}
    engines: {node: '>=4'}
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.7
    dev: true

  /restore-cursor/3.1.0:
    resolution: {integrity: sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: true

  /ret/0.1.15:
    resolution: {integrity: sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=}
    engines: {node: '>=0.12'}
    dev: true

  /retry/0.12.0:
    resolution: {integrity: sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=}
    engines: {node: '>= 4'}
    dev: true

  /retry/0.9.0:
    resolution: {integrity: sha1-b2l+UKDk3cjI9/tUeptg3q1DZ40=}
    dev: false

  /reusify/1.0.4:
    resolution: {integrity: sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rgb-regex/1.0.1:
    resolution: {integrity: sha1-wODWiC3w4jviVKR16O3UGRX+rrE=}
    dev: true

  /rgba-regex/1.0.0:
    resolution: {integrity: sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=}
    dev: true

  /rimraf/2.6.3:
    resolution: {integrity: sha1-stEE/g2Psnz54KHNqCYt04M8bKs=}
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /rimraf/2.7.1:
    resolution: {integrity: sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=}
    hasBin: true
    dependencies:
      glob: 7.2.3

  /rimraf/3.0.2:
    resolution: {integrity: sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=}
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /ripemd160/2.0.2:
    resolution: {integrity: sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4
    dev: true

  /run-async/2.4.1:
    resolution: {integrity: sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=}
    engines: {node: '>=0.12.0'}
    dev: true

  /run-parallel/1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /run-queue/1.0.3:
    resolution: {integrity: sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=}
    dependencies:
      aproba: 1.2.0
    dev: true

  /rxjs/6.6.7:
    resolution: {integrity: sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=}
    engines: {npm: '>=2.0.0'}
    dependencies:
      tslib: 1.14.1
    dev: true

  /safe-array-concat/1.0.0:
    resolution: {integrity: sha1-IGQiPLo8CNLuBRSO7bxWPNbYQGA=}
    engines: {node: '>=0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      has-symbols: 1.0.3
      isarray: 2.0.5
    dev: true

  /safe-buffer/5.1.2:
    resolution: {integrity: sha1-mR7GnSluAxN0fVm9/St0XDX4go0=}

  /safe-buffer/5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=}

  /safe-regex-test/1.0.0:
    resolution: {integrity: sha1-eTuHTVJOs2QNGHOq0DWW2y1PIpU=}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      is-regex: 1.1.4
    dev: true

  /safe-regex/1.1.0:
    resolution: {integrity: sha1-QKNmnzsHfR6UPURinhV91IAjvy4=}
    dependencies:
      ret: 0.1.15
    dev: true

  /safer-buffer/2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=}

  /sass-loader/8.0.2_sass@1.65.1:
    resolution: {integrity: sha1-3r7NjDziQ8dkVPLoKQSCFQOACQ0=}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0
      sass: ^1.3.0
      webpack: ^4.36.0 || ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
    dependencies:
      clone-deep: 4.0.1
      loader-utils: 1.4.2
      neo-async: 2.6.2
      sass: 1.65.1
      schema-utils: 2.7.1
      semver: 6.3.1
    dev: true

  /sass/1.65.1:
    resolution: {integrity: sha1-jyg7DCYzWogkakSNIuE0K6LqFDI=}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      chokidar: 3.5.3
      immutable: 4.3.2
      source-map-js: 1.0.2
    dev: true

  /sax/1.2.4:
    resolution: {integrity: sha1-KBYjTiN4vdxOU1T6tcqold9xANk=}
    dev: true

  /schema-utils/0.4.7:
    resolution: {integrity: sha1-unT1l9K+LqiAExdG7hfQoJPGgYc=}
    engines: {node: '>= 4'}
    dependencies:
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: false

  /schema-utils/1.0.0:
    resolution: {integrity: sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=}
    engines: {node: '>= 4'}
    dependencies:
      ajv: 6.12.6
      ajv-errors: 1.0.1_ajv@6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: true

  /schema-utils/2.7.1:
    resolution: {integrity: sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=}
    engines: {node: '>= 8.9.0'}
    dependencies:
      '@types/json-schema': 7.0.12
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: true

  /schema-utils/3.3.0:
    resolution: {integrity: sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.12
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
    dev: false

  /script-loader/0.7.2:
    resolution: {integrity: sha1-IBbbb4byX1z1baOJFdgzeLsWa6c=}
    dependencies:
      raw-loader: 0.5.1
    dev: true

  /select-hose/2.0.0:
    resolution: {integrity: sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=}
    dev: true

  /select/1.1.2:
    resolution: {integrity: sha1-DnNQrN7ICxEIUoeG7B1EGNEbOW0=}
    dev: false

  /selfsigned/1.10.14:
    resolution: {integrity: sha1-7lHYTZ3OzGHgfkq6NPIpq1JcFXQ=}
    dependencies:
      node-forge: 0.10.0
    dev: true

  /semver-compare/1.0.0:
    resolution: {integrity: sha1-De4hahyUGrN+nvsXiPavxf9VN/w=}
    dev: true

  /semver/5.0.1:
    resolution: {integrity: sha1-n7P0AE+QDYPEeWj+QvdYPgWDLMk=}
    hasBin: true
    dev: false

  /semver/5.7.2:
    resolution: {integrity: sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=}
    hasBin: true

  /semver/6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=}
    hasBin: true
    dev: true

  /semver/7.5.4:
    resolution: {integrity: sha1-SDmG7E7TjhxsSMNIlKkYLb/2im4=}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /send/0.18.0:
    resolution: {integrity: sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    dev: true

  /serialize-javascript/4.0.0:
    resolution: {integrity: sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=}
    dependencies:
      randombytes: 2.1.0
    dev: true

  /serve-index/1.9.1:
    resolution: {integrity: sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    dev: true

  /serve-static/1.15.0:
    resolution: {integrity: sha1-+q7wjP/goaYvYMrQxOUTz/CslUA=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      encodeurl: 1.0.2
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.18.0
    dev: true

  /set-blocking/2.0.0:
    resolution: {integrity: sha1-BF+XgtARrppoA93TgrJDkrPYkPc=}

  /set-value/2.0.1:
    resolution: {integrity: sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0
    dev: true

  /setimmediate/1.0.5:
    resolution: {integrity: sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=}
    dev: true

  /setprototypeof/1.1.0:
    resolution: {integrity: sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=}
    dev: true

  /setprototypeof/1.2.0:
    resolution: {integrity: sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=}
    dev: true

  /sha.js/2.4.11:
    resolution: {integrity: sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=}
    hasBin: true
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /shallow-clone/3.0.1:
    resolution: {integrity: sha1-jymBrZJTH1UDWwH7IwdppA4C76M=}
    engines: {node: '>=8'}
    dependencies:
      kind-of: 6.0.3
    dev: true

  /shebang-command/1.2.0:
    resolution: {integrity: sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=}
    engines: {node: '>=0.10.0'}
    dependencies:
      shebang-regex: 1.0.0
    dev: true

  /shebang-command/2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex/1.0.0:
    resolution: {integrity: sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=}
    engines: {node: '>=0.10.0'}
    dev: true

  /shebang-regex/3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=}
    engines: {node: '>=8'}
    dev: true

  /shell-quote/1.8.1:
    resolution: {integrity: sha1-bb9Nt1UVrVusY7TxiUw6FUx2ZoA=}
    dev: true

  /side-channel/1.0.4:
    resolution: {integrity: sha1-785cj9wQTudRslxY1CkAEfpeos8=}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      object-inspect: 1.12.3

  /signal-exit/3.0.7:
    resolution: {integrity: sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=}
    dev: true

  /simple-swizzle/0.2.2:
    resolution: {integrity: sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=}
    dependencies:
      is-arrayish: 0.3.2
    dev: true

  /slash/1.0.0:
    resolution: {integrity: sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=}
    engines: {node: '>=0.10.0'}
    dev: true

  /slash/2.0.0:
    resolution: {integrity: sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=}
    engines: {node: '>=6'}
    dev: true

  /slash/3.0.0:
    resolution: {integrity: sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=}
    engines: {node: '>=8'}
    dev: true

  /slice-ansi/0.0.4:
    resolution: {integrity: sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=}
    engines: {node: '>=0.10.0'}
    dev: true

  /slice-ansi/2.1.0:
    resolution: {integrity: sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=}
    engines: {node: '>=6'}
    dependencies:
      ansi-styles: 3.2.1
      astral-regex: 1.0.0
      is-fullwidth-code-point: 2.0.0
    dev: true

  /snapdragon-node/2.1.1:
    resolution: {integrity: sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1
    dev: true

  /snapdragon-util/3.0.1:
    resolution: {integrity: sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /snapdragon/0.8.2:
    resolution: {integrity: sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      base: 0.11.2
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    dev: true

  /socket.io-client/2.5.0:
    resolution: {integrity: sha1-NPSG82QN3pwiEfzohawnRvm69cs=}
    dependencies:
      backo2: 1.0.2
      component-bind: 1.0.0
      component-emitter: 1.3.0
      debug: 3.1.0
      engine.io-client: 3.5.3
      has-binary2: 1.0.3
      indexof: 0.0.1
      parseqs: 0.0.6
      parseuri: 0.0.6
      socket.io-parser: 3.3.3
      to-array: 0.1.4
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /socket.io-parser/3.3.3:
    resolution: {integrity: sha1-OouEgj66h/P3Yk5kqKqrbWMYpy8=}
    dependencies:
      component-emitter: 1.3.0
      debug: 3.1.0
      isarray: 2.0.1
    dev: false

  /sockjs-client/1.6.1:
    resolution: {integrity: sha1-NQuO2kLW1S3cAww5lDNkwR3K2AY=}
    engines: {node: '>=12'}
    dependencies:
      debug: 3.2.7
      eventsource: 2.0.2
      faye-websocket: 0.11.4
      inherits: 2.0.4
      url-parse: 1.5.10
    dev: true

  /sockjs/0.3.24:
    resolution: {integrity: sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=}
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4
    dev: true

  /sort-keys/1.1.2:
    resolution: {integrity: sha1-RBttTTRnmPG05J6JIK37oOVD+a0=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-obj: 1.1.0
    dev: true

  /sortablejs/1.10.2:
    resolution: {integrity: sha1-bkA2TZE/mLhaFPZnj5K1wSIfUpA=}
    dev: false

  /source-list-map/2.0.1:
    resolution: {integrity: sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=}
    dev: true

  /source-map-js/1.0.2:
    resolution: {integrity: sha1-rbw2HZxi3zgBJefxYfccgm8eSQw=}
    engines: {node: '>=0.10.0'}

  /source-map-resolve/0.5.3:
    resolution: {integrity: sha1-GQhmvs51U+H48mei7oLGBrVQmho=}
    deprecated: See https://github.com/lydell/source-map-resolve#deprecated
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.2
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0
    dev: true

  /source-map-support/0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: true

  /source-map-url/0.4.1:
    resolution: {integrity: sha1-CvZmBadFpaL5HPG7+KevvCg97FY=}
    deprecated: See https://github.com/lydell/source-map-url#deprecated
    dev: true

  /source-map/0.5.7:
    resolution: {integrity: sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map/0.6.1:
    resolution: {integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=}
    engines: {node: '>=0.10.0'}

  /source-map/0.7.4:
    resolution: {integrity: sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=}
    engines: {node: '>= 8'}
    dev: true

  /spdx-correct/3.2.0:
    resolution: {integrity: sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=}
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.13

  /spdx-exceptions/2.3.0:
    resolution: {integrity: sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=}

  /spdx-expression-parse/3.0.1:
    resolution: {integrity: sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=}
    dependencies:
      spdx-exceptions: 2.3.0
      spdx-license-ids: 3.0.13

  /spdx-license-ids/3.0.13:
    resolution: {integrity: sha1-cYmkdMRvjUfHsNpLmHu0XpCL0tU=}

  /spdy-transport/3.0.0_supports-color@6.1.0:
    resolution: {integrity: sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=}
    dependencies:
      debug: 4.3.4_supports-color@6.1.0
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.2
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /spdy/4.0.2_supports-color@6.1.0:
    resolution: {integrity: sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=}
    engines: {node: '>=6.0.0'}
    dependencies:
      debug: 4.3.4_supports-color@6.1.0
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0_supports-color@6.1.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /split-string/3.1.0:
    resolution: {integrity: sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
    dev: true

  /sprintf-js/1.0.3:
    resolution: {integrity: sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=}
    dev: true

  /sshpk/1.17.0:
    resolution: {integrity: sha1-V4CC2S1P5hKxMAdJblQ/oPvL5MU=}
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  /ssr-window/2.0.0:
    resolution: {integrity: sha1-mMMBrvmVIzF/jWlhjwAQeRCW78Q=}
    dev: false

  /ssri/6.0.2:
    resolution: {integrity: sha1-FXk5E08gRk5zAd26PpD/qPdyisU=}
    dependencies:
      figgy-pudding: 3.5.2
    dev: true

  /ssri/8.0.1:
    resolution: {integrity: sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: true

  /stable/0.1.8:
    resolution: {integrity: sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=}
    deprecated: 'Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility'
    dev: true

  /stackframe/1.3.4:
    resolution: {integrity: sha1-uIGgBMjBSaXo7+831RsW5BKUMxA=}
    dev: true

  /static-extend/0.1.2:
    resolution: {integrity: sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0
    dev: true

  /statuses/1.5.0:
    resolution: {integrity: sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=}
    engines: {node: '>= 0.6'}
    dev: true

  /statuses/2.0.1:
    resolution: {integrity: sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=}
    engines: {node: '>= 0.8'}
    dev: true

  /stream-browserify/2.0.2:
    resolution: {integrity: sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: true

  /stream-each/1.2.3:
    resolution: {integrity: sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=}
    dependencies:
      end-of-stream: 1.4.4
      stream-shift: 1.0.1
    dev: true

  /stream-http/2.8.3:
    resolution: {integrity: sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=}
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.8
      to-arraybuffer: 1.0.1
      xtend: 4.0.2
    dev: true

  /stream-shift/1.0.1:
    resolution: {integrity: sha1-1wiCgVWasneEJCebCHfaPDktWj0=}

  /strict-uri-encode/1.1.0:
    resolution: {integrity: sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=}
    engines: {node: '>=0.10.0'}
    dev: true

  /string-argv/0.3.2:
    resolution: {integrity: sha1-K20O8ktlYnTZV9VOCku/YVPcArY=}
    engines: {node: '>=0.6.19'}
    dev: true

  /string-width/1.0.2:
    resolution: {integrity: sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=}
    engines: {node: '>=0.10.0'}
    dependencies:
      code-point-at: 1.1.0
      is-fullwidth-code-point: 1.0.0
      strip-ansi: 3.0.1
    dev: true

  /string-width/2.1.1:
    resolution: {integrity: sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=}
    engines: {node: '>=4'}
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0
    dev: true

  /string-width/3.1.0:
    resolution: {integrity: sha1-InZ74htirxCBV0MG9prFG2IgOWE=}
    engines: {node: '>=6'}
    dependencies:
      emoji-regex: 7.0.3
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 5.2.0
    dev: true

  /string-width/4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  /string.prototype.trim/1.2.7:
    resolution: {integrity: sha1-poNSdAhZ9ok/FM4+8bswN/epBTM=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: true

  /string.prototype.trimend/1.0.6:
    resolution: {integrity: sha1-xKJ/oCbZedecBPFzl/JQpGKURTM=}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: true

  /string.prototype.trimstart/1.0.6:
    resolution: {integrity: sha1-6Qq2aqjkAH2S71kbvzzUIsVr3PQ=}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: true

  /string_decoder/0.10.31:
    resolution: {integrity: sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=}
    dev: false

  /string_decoder/1.1.1:
    resolution: {integrity: sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=}
    dependencies:
      safe-buffer: 5.1.2

  /string_decoder/1.3.0:
    resolution: {integrity: sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /stringify-object/3.3.0:
    resolution: {integrity: sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=}
    engines: {node: '>=4'}
    dependencies:
      get-own-enumerable-property-symbols: 3.0.2
      is-obj: 1.0.1
      is-regexp: 1.0.0
    dev: true

  /strip-ansi/0.1.1:
    resolution: {integrity: sha1-OeipjQRNFQZgq+SmgIrPcLt7yZE=}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dev: false

  /strip-ansi/3.0.1:
    resolution: {integrity: sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: true

  /strip-ansi/4.0.0:
    resolution: {integrity: sha1-qEeQIusaw2iocTibY1JixQXuNo8=}
    engines: {node: '>=4'}
    dependencies:
      ansi-regex: 3.0.1
    dev: true

  /strip-ansi/5.2.0:
    resolution: {integrity: sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=}
    engines: {node: '>=6'}
    dependencies:
      ansi-regex: 4.1.1
    dev: true

  /strip-ansi/6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1

  /strip-bom/3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=}
    engines: {node: '>=4'}
    dev: true

  /strip-eof/1.0.0:
    resolution: {integrity: sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /strip-final-newline/2.0.0:
    resolution: {integrity: sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=}
    engines: {node: '>=6'}
    dev: true

  /strip-indent/2.0.0:
    resolution: {integrity: sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=}
    engines: {node: '>=4'}
    dev: true

  /strip-json-comments/1.0.4:
    resolution: {integrity: sha1-HhX7ysl9Pumb8tc7TGVrCCu6+5E=}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dev: false

  /strip-json-comments/3.1.0:
    resolution: {integrity: sha1-djjTFCISns9EV0QACfugP5+awYA=}
    engines: {node: '>=8'}
    dev: false

  /strip-json-comments/3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=}
    engines: {node: '>=8'}
    dev: true

  /stylehacks/4.0.3:
    resolution: {integrity: sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=}
    engines: {node: '>=6.9.0'}
    dependencies:
      browserslist: 4.21.10
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
    dev: true

  /supports-color/2.0.0:
    resolution: {integrity: sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=}
    engines: {node: '>=0.8.0'}
    dev: true

  /supports-color/3.2.3:
    resolution: {integrity: sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=}
    engines: {node: '>=0.8.0'}
    dependencies:
      has-flag: 1.0.0
    dev: true

  /supports-color/5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-color/6.1.0:
    resolution: {integrity: sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=}
    engines: {node: '>=6'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-color/7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=}
    engines: {node: '>= 0.4'}

  /svg-baker-runtime/1.4.7:
    resolution: {integrity: sha1-9HIGN/W2IC7vY3jYHx/q0IFfik4=}
    dependencies:
      deepmerge: 1.3.2
      mitt: 1.1.2
      svg-baker: 1.7.0
    dev: true

  /svg-baker/1.7.0:
    resolution: {integrity: sha1-g2f3jYdVUMUv5HVvcwPVxdfC6ac=}
    dependencies:
      bluebird: 3.7.2
      clone: 2.1.2
      he: 1.2.0
      image-size: 0.5.5
      loader-utils: 1.4.2
      merge-options: 1.0.1
      micromatch: 3.1.0
      postcss: 5.2.18
      postcss-prefix-selector: 1.16.0_postcss@5.2.18
      posthtml-rename-id: 1.0.12
      posthtml-svg-mode: 1.0.3
      query-string: 4.3.4
      traverse: 0.6.7
    dev: true

  /svg-sprite-loader/5.2.1:
    resolution: {integrity: sha1-8MN+dmZd3996vI/269HaNc3SEtk=}
    engines: {node: '>=6'}
    dependencies:
      bluebird: 3.7.2
      deepmerge: 1.3.2
      domready: 1.0.8
      escape-string-regexp: 1.0.5
      loader-utils: 1.4.2
      svg-baker: 1.7.0
      svg-baker-runtime: 1.4.7
      url-slug: 2.0.0
    dev: true

  /svg-tags/1.0.0:
    resolution: {integrity: sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=}

  /svgo/1.3.2:
    resolution: {integrity: sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc=}
    engines: {node: '>=4.0.0'}
    deprecated: This SVGO version is no longer supported. Upgrade to v2.x.x.
    hasBin: true
    dependencies:
      chalk: 2.4.2
      coa: 2.0.2
      css-select: 2.1.0
      css-select-base-adapter: 0.1.1
      css-tree: 1.0.0-alpha.37
      csso: 4.2.0
      js-yaml: 3.14.1
      mkdirp: 0.5.6
      object.values: 1.1.6
      sax: 1.2.4
      stable: 0.1.8
      unquote: 1.1.1
      util.promisify: 1.0.1
    dev: true

  /swiper/5.4.5:
    resolution: {integrity: sha1-o1D2VL9oQm27ZReTgkklUS0iPA8=}
    engines: {node: '>= 4.7.0'}
    requiresBuild: true
    dependencies:
      dom7: 2.1.5
      ssr-window: 2.0.0
    dev: false

  /symbol-observable/1.2.0:
    resolution: {integrity: sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /table/5.4.6:
    resolution: {integrity: sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=}
    engines: {node: '>=6.0.0'}
    dependencies:
      ajv: 6.12.6
      lodash: 4.17.21
      slice-ansi: 2.1.0
      string-width: 3.1.0
    dev: true

  /tapable/0.1.10:
    resolution: {integrity: sha1-KcNXB8K3DlDQdIK10gLo7URtr9Q=}
    engines: {node: '>=0.6'}
    dev: true

  /tapable/1.1.3:
    resolution: {integrity: sha1-ofzMBrWNth/XpF2i2kT186Pme6I=}
    engines: {node: '>=6'}
    dev: true

  /tar/2.2.1:
    resolution: {integrity: sha1-jk0qJWwOIYXGsYrWlK7JaLg8sdE=}
    deprecated: This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.
    dependencies:
      block-stream: 0.0.9
      fstream: 1.0.12
      inherits: 2.0.4
    dev: false

  /tar/6.1.15:
    resolution: {integrity: sha1-yXOLC5iEWjs0TTNLj6MEGqulOmk=}
    engines: {node: '>=10'}
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0
    dev: true

  /terser-webpack-plugin/1.4.5_webpack@4.46.0:
    resolution: {integrity: sha1-oheu+uozDnNP+sthIOwfoxLWBAs=}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      is-wsl: 1.1.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 4.46.0
      webpack-sources: 1.4.3
      worker-farm: 1.7.0
    dev: true

  /terser/4.8.1:
    resolution: {integrity: sha1-oA5WNFYt4iOf1ATGSQUb9vwhFE8=}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      commander: 2.20.3
      source-map: 0.6.1
      source-map-support: 0.5.21
    dev: true

  /text-segmentation/1.0.3:
    resolution: {integrity: sha1-UqOIFZ7//nRrJKY7oxG2rJ8teUM=}
    dependencies:
      utrie: 1.0.2
    dev: false

  /text-table/0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=}
    dev: true

  /thenify-all/1.6.0:
    resolution: {integrity: sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=}
    engines: {node: '>=0.8'}
    dependencies:
      thenify: 3.3.1

  /thenify/3.3.1:
    resolution: {integrity: sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=}
    dependencies:
      any-promise: 1.3.0

  /thread-loader/2.1.3_webpack@4.46.0:
    resolution: {integrity: sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo=}
    engines: {node: '>= 6.9.0 <7.0.0 || >= 8.9.0'}
    peerDependencies:
      webpack: ^2.0.0 || ^3.0.0 || ^4.0.0
    dependencies:
      loader-runner: 2.4.0
      loader-utils: 1.4.2
      neo-async: 2.6.2
      webpack: 4.46.0
    dev: true

  /throttle-debounce/1.1.0:
    resolution: {integrity: sha1-UYU9o3vmihVctugns1FKPEIuic0=}
    engines: {node: '>=4'}
    dev: false

  /through/2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=}
    dev: true

  /through2/0.4.2:
    resolution: {integrity: sha1-2/WGYDEVHsg1K7bE22SiKSqEC5s=}
    dependencies:
      readable-stream: 1.0.34
      xtend: 2.1.2
    dev: false

  /through2/2.0.5:
    resolution: {integrity: sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=}
    dependencies:
      readable-stream: 2.3.8
      xtend: 4.0.2

  /thunky/1.1.0:
    resolution: {integrity: sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=}
    dev: true

  /timed-out/2.0.0:
    resolution: {integrity: sha1-84sK6B03R9YoAB9B2vxlKs5nHAo=}
    engines: {node: '>=0.10.0'}
    dev: false

  /timers-browserify/2.0.12:
    resolution: {integrity: sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=}
    engines: {node: '>=0.6.0'}
    dependencies:
      setimmediate: 1.0.5
    dev: true

  /timsort/0.3.0:
    resolution: {integrity: sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=}
    dev: true

  /tiny-emitter/2.1.0:
    resolution: {integrity: sha1-HRpW7fxRxD6GPLtTgqcjMONVVCM=}
    dev: false

  /tinymce/5.10.7:
    resolution: {integrity: sha1-2J1Ebxli8qHfaytwAYzkdex/+4A=}
    dev: false

  /tmp/0.0.33:
    resolution: {integrity: sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=}
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: 1.0.2
    dev: true

  /to-array/0.1.4:
    resolution: {integrity: sha1-F+bBH3PdTz10zaek/zI46a2b+JA=}
    dev: false

  /to-arraybuffer/1.0.1:
    resolution: {integrity: sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=}
    dev: true

  /to-fast-properties/1.0.3:
    resolution: {integrity: sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=}
    engines: {node: '>=0.10.0'}
    dev: true

  /to-fast-properties/2.0.0:
    resolution: {integrity: sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=}
    engines: {node: '>=4'}

  /to-object-path/0.3.0:
    resolution: {integrity: sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: true

  /to-regex-range/2.1.1:
    resolution: {integrity: sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1
    dev: true

  /to-regex-range/5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: true

  /to-regex/3.0.2:
    resolution: {integrity: sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0
    dev: true

  /toidentifier/1.0.1:
    resolution: {integrity: sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=}
    engines: {node: '>=0.6'}
    dev: true

  /toposort/1.0.7:
    resolution: {integrity: sha1-LmhELZ9k7HILjMieZEOsbKqVACk=}
    dev: true

  /tough-cookie/2.5.0:
    resolution: {integrity: sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=}
    engines: {node: '>=0.8'}
    dependencies:
      psl: 1.9.0
      punycode: 2.3.0

  /traverse/0.6.7:
    resolution: {integrity: sha1-RpYc0tV92HBsNmZKzeBqJI8Rc/4=}
    dev: true

  /tryer/1.0.1:
    resolution: {integrity: sha1-8shUBoALmw90yfdGW4HqrSQSUvg=}
    dev: true

  /ts-pnp/1.2.0:
    resolution: {integrity: sha1-pQCtCEsHmPHDBxrzkeZZEshrypI=}
    engines: {node: '>=6'}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dev: true

  /tsconfig-paths/3.14.2:
    resolution: {integrity: sha1-bjLx95QS3s0mH5LWM6ncHPqZ8Ig=}
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0
    dev: true

  /tslib/1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=}
    dev: true

  /tsparticles/1.43.1:
    resolution: {integrity: sha1-H2s37kG2qwQ9ZqDiC/4IVaGuoAY=}
    deprecated: tsParticles 2.6.0 is out, please update
    requiresBuild: true
    dev: false

  /tty-browserify/0.0.0:
    resolution: {integrity: sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=}
    dev: true

  /tunnel-agent/0.6.0:
    resolution: {integrity: sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=}
    dependencies:
      safe-buffer: 5.2.1

  /tweetnacl/0.14.5:
    resolution: {integrity: sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=}

  /type-check/0.3.2:
    resolution: {integrity: sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
    dev: true

  /type-fest/0.21.3:
    resolution: {integrity: sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=}
    engines: {node: '>=10'}
    dev: true

  /type-fest/0.6.0:
    resolution: {integrity: sha1-jSojcNPfiG61yQraHFv2GIrPg4s=}
    engines: {node: '>=8'}
    dev: true

  /type-fest/0.8.1:
    resolution: {integrity: sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=}
    engines: {node: '>=8'}
    dev: true

  /type-is/1.6.18:
    resolution: {integrity: sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=}
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35
    dev: true

  /typed-array-buffer/1.0.0:
    resolution: {integrity: sha1-GN4+fteXSwpynT/uy5QzjRRyzWA=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      is-typed-array: 1.1.12
    dev: true

  /typed-array-byte-length/1.0.0:
    resolution: {integrity: sha1-14eiSplXEWEfsrh6QFJ5lReyMNA=}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      for-each: 0.3.3
      has-proto: 1.0.1
      is-typed-array: 1.1.12
    dev: true

  /typed-array-byte-offset/1.0.0:
    resolution: {integrity: sha1-y76JtR/e+c1qrwetRwc0CrvE6gs=}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      for-each: 0.3.3
      has-proto: 1.0.1
      is-typed-array: 1.1.12
    dev: true

  /typed-array-length/1.0.4:
    resolution: {integrity: sha1-idg3heXECYvscuCLMZZR8OrJwbs=}
    dependencies:
      call-bind: 1.0.2
      for-each: 0.3.3
      is-typed-array: 1.1.12
    dev: true

  /typedarray/0.0.6:
    resolution: {integrity: sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=}
    dev: true

  /uglify-js/3.4.10:
    resolution: {integrity: sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dependencies:
      commander: 2.19.0
      source-map: 0.6.1
    dev: true

  /unbox-primitive/1.0.2:
    resolution: {integrity: sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=}
    dependencies:
      call-bind: 1.0.2
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2
    dev: true

  /underscore/1.6.0:
    resolution: {integrity: sha1-izixDKze9jM3uLJOT/htRa6lKag=}
    dev: false

  /unicode-canonical-property-names-ecmascript/2.0.0:
    resolution: {integrity: sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=}
    engines: {node: '>=4'}
    dev: true

  /unicode-match-property-ecmascript/2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=}
    engines: {node: '>=4'}
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.1.0
    dev: true

  /unicode-match-property-value-ecmascript/2.1.0:
    resolution: {integrity: sha1-y1//3NFqBRJPWksL98N3Agisu+A=}
    engines: {node: '>=4'}
    dev: true

  /unicode-property-aliases-ecmascript/2.1.0:
    resolution: {integrity: sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=}
    engines: {node: '>=4'}
    dev: true

  /unidecode/0.1.8:
    resolution: {integrity: sha1-77swFTi8RSRqmsjFWdcvAVMFBT4=}
    engines: {node: '>= 0.4.12'}
    dev: true

  /union-value/1.0.1:
    resolution: {integrity: sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1
    dev: true

  /uniq/1.0.1:
    resolution: {integrity: sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=}
    dev: true

  /uniqs/2.0.0:
    resolution: {integrity: sha1-/+3ks2slKQaW5uFl1KWe25mOawI=}
    dev: true

  /unique-filename/1.1.1:
    resolution: {integrity: sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=}
    dependencies:
      unique-slug: 2.0.2
    dev: true

  /unique-slug/2.0.2:
    resolution: {integrity: sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=}
    dependencies:
      imurmurhash: 0.1.4
    dev: true

  /universalify/0.1.2:
    resolution: {integrity: sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=}
    engines: {node: '>= 4.0.0'}
    dev: true

  /unpipe/1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=}
    engines: {node: '>= 0.8'}
    dev: true

  /unquote/1.1.1:
    resolution: {integrity: sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=}
    dev: true

  /unset-value/1.0.0:
    resolution: {integrity: sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=}
    engines: {node: '>=0.10.0'}
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1
    dev: true

  /unzip-response/1.0.2:
    resolution: {integrity: sha1-uYTwh3/AqJwsdzzB73tbIytbBv4=}
    engines: {node: '>=0.10'}
    dev: false

  /upath/1.2.0:
    resolution: {integrity: sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=}
    engines: {node: '>=4'}
    dev: true

  /update-browserslist-db/1.0.11_browserslist@4.21.10:
    resolution: {integrity: sha1-mipkGtKQeuezYWUG9Ll3hR21uUA=}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.21.10
      escalade: 3.1.1
      picocolors: 1.0.0
    dev: true

  /upper-case/1.1.3:
    resolution: {integrity: sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=}
    dev: true

  /uri-js/4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=}
    dependencies:
      punycode: 2.3.0

  /urix/0.1.0:
    resolution: {integrity: sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=}
    deprecated: Please see https://github.com/lydell/urix#deprecated
    dev: true

  /url-loader/2.3.0_file-loader@4.3.0+webpack@4.46.0:
    resolution: {integrity: sha1-4OLvZY8APvuMpBsPP/v3a6uIZYs=}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      file-loader: '*'
      webpack: ^4.0.0
    peerDependenciesMeta:
      file-loader:
        optional: true
    dependencies:
      file-loader: 4.3.0_webpack@4.46.0
      loader-utils: 1.4.2
      mime: 2.6.0
      schema-utils: 2.7.1
      webpack: 4.46.0
    dev: true

  /url-parse-lax/1.0.0:
    resolution: {integrity: sha1-evjzA2Rem9eaJy56FKxovAYJ2nM=}
    engines: {node: '>=0.10.0'}
    dependencies:
      prepend-http: 1.0.4
    dev: false

  /url-parse/1.5.10:
    resolution: {integrity: sha1-nTwvc2wddd070r5QfcwRHx4uqcE=}
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0
    dev: true

  /url-slug/2.0.0:
    resolution: {integrity: sha1-p4nVrtSZXA2VrzM3etHVxo1NcCc=}
    dependencies:
      unidecode: 0.1.8
    dev: true

  /url/0.11.1:
    resolution: {integrity: sha1-JvkPYVQn7KG59NaigojBR+IwKjI=}
    dependencies:
      punycode: 1.4.1
      qs: 6.11.2
    dev: true

  /use/3.1.1:
    resolution: {integrity: sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=}
    engines: {node: '>=0.10.0'}
    dev: true

  /util-deprecate/1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=}

  /util.promisify/1.0.0:
    resolution: {integrity: sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=}
    dependencies:
      define-properties: 1.2.0
      object.getownpropertydescriptors: 2.1.6
    dev: true

  /util.promisify/1.0.1:
    resolution: {integrity: sha1-a693dLgO6w91INi4HQeYKlmruu4=}
    dependencies:
      define-properties: 1.2.0
      es-abstract: 1.22.1
      has-symbols: 1.0.3
      object.getownpropertydescriptors: 2.1.6
    dev: true

  /util/0.10.3:
    resolution: {integrity: sha1-evsa/lCAUkZInj23/g7TeTNqwPk=}
    dependencies:
      inherits: 2.0.1
    dev: true

  /util/0.11.1:
    resolution: {integrity: sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=}
    dependencies:
      inherits: 2.0.3
    dev: true

  /utila/0.4.0:
    resolution: {integrity: sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=}
    dev: true

  /utils-merge/1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=}
    engines: {node: '>= 0.4.0'}
    dev: true

  /utrie/1.0.2:
    resolution: {integrity: sha1-1C/kTem8ARnCXef1ZKbtGyyHpkU=}
    dependencies:
      base64-arraybuffer: 1.0.2
    dev: false

  /uuid/3.4.0:
    resolution: {integrity: sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  /uuid/8.3.2:
    resolution: {integrity: sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=}
    hasBin: true

  /v-contextmenu/2.9.0_vue@2.7.14:
    resolution: {integrity: sha1-F1Onr8QgUfHtXLdcIEmKFv1+wzI=}
    engines: {node: '>= 4.0.0', npm: '>= 3.0.0'}
    peerDependencies:
      vue: ^2.5.0
    dependencies:
      vue: 2.7.14
    dev: false

  /v-dropdown/2.1.1:
    resolution: {integrity: sha1-XhpJ1hwpE6ar3aPMtGLiZH+dae0=}
    dev: false

  /v-region/2.2.2:
    resolution: {integrity: sha1-WrswXysE8P/ZoCW4TACCHaJzCKU=}
    dependencies:
      v-dropdown: 2.1.1
    dev: false

  /v8-compile-cache/2.4.0:
    resolution: {integrity: sha1-za2ovsYeFYZfBdCXxfT9MOlNwSg=}
    dev: true

  /validate-npm-package-license/3.0.4:
    resolution: {integrity: sha1-/JH2uce6FchX9MssXe/uw51PQQo=}
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  /validate-npm-package-name/2.2.2:
    resolution: {integrity: sha1-9laVsi9zJEQgGaPH+jmm5/0pkIU=}
    dependencies:
      builtins: 0.0.7
    dev: false

  /vanilla-picker/2.12.1:
    resolution: {integrity: sha1-bmGe7PVTiRuNLQQrdFojyR8Z80w=}
    dependencies:
      '@sphinxxxx/color-conversion': 2.2.2
    dev: false

  /vant/2.12.54_vue@2.7.14:
    resolution: {integrity: sha1-C8UtgEFEIph825t+fBAaZtNkfY0=}
    peerDependencies:
      vue: '>= 2.6.0'
    dependencies:
      '@babel/runtime': 7.22.10
      '@vant/icons': 1.8.0
      '@vant/popperjs': 1.3.0
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      vue: 2.7.14
      vue-lazyload: 1.2.3
    dev: false

  /vary/1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=}
    engines: {node: '>= 0.8'}
    dev: true

  /vendors/1.0.4:
    resolution: {integrity: sha1-4rgApT56Kbk1BsPPQRANFsTErY4=}
    dev: true

  /verror/1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=}
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  /vm-browserify/1.1.2:
    resolution: {integrity: sha1-eGQcSIuObKkadfUR56OzKobl3aA=}
    dev: true

  /vue-awesome-swiper/4.1.1_swiper@5.4.5+vue@2.7.14:
    resolution: {integrity: sha1-j3qyIa0AMCHXVrhqphj0KZJJAP4=}
    engines: {node: '>=8'}
    peerDependencies:
      swiper: ^5.2.0
      vue: 2.x
    dependencies:
      swiper: 5.4.5
      vue: 2.7.14
    dev: false

  /vue-class-component/7.2.6_vue@2.7.14:
    resolution: {integrity: sha1-hHHgN7jkdi9aRkaG4Z5a/HCFAuQ=}
    peerDependencies:
      vue: ^2.0.0
    dependencies:
      vue: 2.7.14
    dev: false

  /vue-clipboard2/0.3.3:
    resolution: {integrity: sha1-Mx/shfnU8XXrDU/q7013M4VirzY=}
    dependencies:
      clipboard: 2.0.11
    dev: false

  /vue-codemirror/4.0.6:
    resolution: {integrity: sha1-t4a7gNjXYqk6q45G95qBAG8EN8Q=}
    engines: {node: '>= 4.0.0', npm: '>= 3.0.0'}
    dependencies:
      codemirror: 5.65.14
      diff-match-patch: 1.0.5
    dev: false

  /vue-eslint-parser/7.11.0_eslint@6.8.0:
    resolution: {integrity: sha1-IUtd6pYQB/z/su5luJEjB2KNDa8=}
    engines: {node: '>=8.10'}
    peerDependencies:
      eslint: '>=5.0.0'
    dependencies:
      debug: 4.3.4
      eslint: 6.8.0
      eslint-scope: 5.1.1
      eslint-visitor-keys: 1.3.0
      espree: 6.2.1
      esquery: 1.5.0
      lodash: 4.17.21
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vue-grid-layout/2.4.0:
    resolution: {integrity: sha1-i2BOKYMmZrmLOhhqbql7PR7VIxA=}
    dependencies:
      '@interactjs/actions': 1.10.2
      '@interactjs/auto-scroll': 1.10.2
      '@interactjs/auto-start': 1.10.2
      '@interactjs/dev-tools': 1.10.2
      '@interactjs/interactjs': 1.10.2
      '@interactjs/modifiers': 1.10.2
      element-resize-detector: 1.2.4
    transitivePeerDependencies:
      - '@interactjs/core'
      - '@interactjs/utils'
    dev: false

  /vue-hot-reload-api/2.3.4:
    resolution: {integrity: sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=}
    dev: true

  /vue-lazyload/1.2.3:
    resolution: {integrity: sha1-kB+ewVx+bKeHgaK65KNDaGve2yw=}
    dev: false

  /vue-lazyload/1.3.5:
    resolution: {integrity: sha1-6zbSmaUZFn2Yf98Ov9ycbdG/HvA=}
    dev: false

  /vue-loader/15.10.1_942d38e42bf4b987e783635c88cde9fa:
    resolution: {integrity: sha1-xFHEzQWpEarntdu7wJ+5E/s8yhg=}
    peerDependencies:
      cache-loader: '*'
      css-loader: '*'
      vue-template-compiler: '*'
      webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      cache-loader:
        optional: true
      vue-template-compiler:
        optional: true
    dependencies:
      '@vue/component-compiler-utils': 3.3.0
      cache-loader: 4.1.0_webpack@4.46.0
      css-loader: 3.6.0_webpack@4.46.0
      hash-sum: 1.0.2
      loader-utils: 1.4.2
      vue-hot-reload-api: 2.3.4
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.7.14
      webpack: 4.46.0
    dev: true

  /vue-loader/16.8.3_webpack@4.46.0:
    resolution: {integrity: sha1-1D5nXe9bqTRdbH8FkUwT2GGZcIc=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/vue-loader/-/vue-loader-16.8.3.tgz}
    requiresBuild: true
    peerDependencies:
      webpack: ^4.1.0 || ^5.0.0-0
    dependencies:
      chalk: 4.1.2
      hash-sum: 2.0.0
      loader-utils: 2.0.4
      webpack: 4.46.0
    dev: true
    optional: true

  /vue-pdf/4.3.0:
    resolution: {integrity: sha1-1feQ7nln57eqkIm5exGrFo4Z29A=}
    dependencies:
      babel-plugin-syntax-dynamic-import: 6.18.0
      loader-utils: 1.4.2
      pdfjs-dist: 2.6.347
      raw-loader: 4.0.2
      vue-resize-sensor: 2.0.0
      worker-loader: 2.0.0
    transitivePeerDependencies:
      - webpack
    dev: false

  /vue-property-decorator/9.1.2_4cf18c60478cc4559d7fa95f13b081de:
    resolution: {integrity: sha1-JmourGG6ZSfi5oppM8+5j92rVFc=}
    peerDependencies:
      vue: '*'
      vue-class-component: '*'
    dependencies:
      vue: 2.7.14
      vue-class-component: 7.2.6_vue@2.7.14
    dev: false

  /vue-ref/2.0.0:
    resolution: {integrity: sha1-SDCE1zKr7RHaeWd4qCZqOvDqGpw=}
    dev: false

  /vue-resize-sensor/2.0.0:
    resolution: {integrity: sha1-Olh/1oAuFohwnPLFqtrnoAdZUr8=}
    dev: false

  /vue-router/3.6.5:
    resolution: {integrity: sha1-lYR9Urmn4/E2HLYFyOZEHyAq+tg=}
    dev: false

  /vue-socket.io/3.0.10:
    resolution: {integrity: sha1-dhNb9QQ7r0hMZIY2p6mCK0wRwIQ=}
    dependencies:
      socket.io-client: 2.5.0
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /vue-style-loader/4.1.3:
    resolution: {integrity: sha1-bVWGOlH6dXqyTonZNxRlByqnvDU=}
    dependencies:
      hash-sum: 1.0.2
      loader-utils: 1.4.2
    dev: true

  /vue-template-compiler/2.7.14:
    resolution: {integrity: sha1-RUW337iAkHRMFXeuWsP5ZOYWNLE=}
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0
    dev: true

  /vue-template-es2015-compiler/1.9.1:
    resolution: {integrity: sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=}
    dev: true

  /vue/2.7.14:
    resolution: {integrity: sha1-N0Pc0kj9OjTUIa5Fa4ZKAka6+xc=}
    dependencies:
      '@vue/compiler-sfc': 2.7.14
      csstype: 3.1.2
    dev: false

  /vuedraggable/2.24.3:
    resolution: {integrity: sha1-Q8k4SbdGokzlA+Ej1bJZxwG6DRk=}
    dependencies:
      sortablejs: 1.10.2
    dev: false

  /vuex/3.6.2_vue@2.7.14:
    resolution: {integrity: sha1-I2vAhqhww655lG8QfxbeWdWJXnE=}
    peerDependencies:
      vue: ^2.0.0
    dependencies:
      vue: 2.7.14
    dev: false

  /watch-size/2.0.0:
    resolution: {integrity: sha1-CW7ijQNlvX6gPZyL8fL1CnO+FHQ=}
    dev: false

  /watchpack-chokidar2/2.0.1:
    resolution: {integrity: sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=, tarball: https://art.haizhi.com:443/artifactory/api/npm/npm/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz}
    requiresBuild: true
    dependencies:
      chokidar: 2.1.8
    dev: true
    optional: true

  /watchpack/1.7.5:
    resolution: {integrity: sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=}
    dependencies:
      graceful-fs: 4.2.11
      neo-async: 2.6.2
    optionalDependencies:
      chokidar: 3.5.3
      watchpack-chokidar2: 2.0.1
    dev: true

  /wbuf/1.7.3:
    resolution: {integrity: sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=}
    dependencies:
      minimalistic-assert: 1.0.1
    dev: true

  /wcwidth/1.0.1:
    resolution: {integrity: sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=}
    dependencies:
      defaults: 1.0.4
    dev: true

  /webpack-bundle-analyzer/3.9.0:
    resolution: {integrity: sha1-9vlNsQj7V05BWtMT3kGicH0z7zw=}
    engines: {node: '>= 6.14.4'}
    hasBin: true
    dependencies:
      acorn: 7.4.1
      acorn-walk: 7.2.0
      bfj: 6.1.2
      chalk: 2.4.2
      commander: 2.20.3
      ejs: 2.7.4
      express: 4.18.2
      filesize: 3.6.1
      gzip-size: 5.1.1
      lodash: 4.17.21
      mkdirp: 0.5.6
      opener: 1.5.2
      ws: 6.2.2
    dev: true

  /webpack-chain/6.5.1:
    resolution: {integrity: sha1-TycoTLu2N+PI+970Pu9YjU2GEgY=}
    engines: {node: '>=8'}
    dependencies:
      deepmerge: 1.5.2
      javascript-stringify: 2.1.0
    dev: true

  /webpack-dev-middleware/3.7.3_webpack@4.46.0:
    resolution: {integrity: sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU=}
    engines: {node: '>= 6'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      memory-fs: 0.4.1
      mime: 2.6.0
      mkdirp: 0.5.6
      range-parser: 1.2.1
      webpack: 4.46.0
      webpack-log: 2.0.0
    dev: true

  /webpack-dev-server/3.11.3_webpack@4.46.0:
    resolution: {integrity: sha1-jIa50oEr8TXTybzm8HtxjjD3w9M=}
    engines: {node: '>= 6.11.5'}
    hasBin: true
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      ansi-html-community: 0.0.8
      bonjour: 3.5.0
      chokidar: 2.1.8
      compression: 1.7.4
      connect-history-api-fallback: 1.6.0
      debug: 4.3.4_supports-color@6.1.0
      del: 4.1.1
      express: 4.18.2
      html-entities: 1.4.0
      http-proxy-middleware: 0.19.1_debug@4.3.4
      import-local: 2.0.0
      internal-ip: 4.3.0
      ip: 1.1.8
      is-absolute-url: 3.0.3
      killable: 1.0.1
      loglevel: 1.8.1
      opn: 5.5.0
      p-retry: 3.0.1
      portfinder: 1.0.32
      schema-utils: 1.0.0
      selfsigned: 1.10.14
      semver: 6.3.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      sockjs-client: 1.6.1
      spdy: 4.0.2_supports-color@6.1.0
      strip-ansi: 3.0.1
      supports-color: 6.1.0
      url: 0.11.1
      webpack: 4.46.0
      webpack-dev-middleware: 3.7.3_webpack@4.46.0
      webpack-log: 2.0.0
      ws: 6.2.2
      yargs: 13.3.2
    dev: true

  /webpack-log/2.0.0:
    resolution: {integrity: sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=}
    engines: {node: '>= 6'}
    dependencies:
      ansi-colors: 3.2.4
      uuid: 3.4.0
    dev: true

  /webpack-merge/4.2.2:
    resolution: {integrity: sha1-onxS6ng9E5iv0gh/VH17nS9DY00=}
    dependencies:
      lodash: 4.17.21
    dev: true

  /webpack-sources/1.4.3:
    resolution: {integrity: sha1-7t2OwLko+/HL/plOItLYkPMwqTM=}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: true

  /webpack/4.46.0:
    resolution: {integrity: sha1-v5tEBOogoHNgXgoBHRiNd8tq1UI=}
    engines: {node: '>=6.11.5'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
      webpack-command: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
      webpack-command:
        optional: true
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/wasm-edit': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      acorn: 6.4.2
      ajv: 6.12.6
      ajv-keywords: 3.5.2_ajv@6.12.6
      chrome-trace-event: 1.0.3
      enhanced-resolve: 4.5.0
      eslint-scope: 4.0.3
      json-parse-better-errors: 1.0.2
      loader-runner: 2.4.0
      loader-utils: 1.4.2
      memory-fs: 0.4.1
      micromatch: 3.1.10
      mkdirp: 0.5.6
      neo-async: 2.6.2
      node-libs-browser: 2.2.1
      schema-utils: 1.0.0
      tapable: 1.1.3
      terser-webpack-plugin: 1.4.5_webpack@4.46.0
      watchpack: 1.7.5
      webpack-sources: 1.4.3
    dev: true

  /websocket-driver/0.7.4:
    resolution: {integrity: sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=}
    engines: {node: '>=0.8.0'}
    dependencies:
      http-parser-js: 0.5.8
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4
    dev: true

  /websocket-extensions/0.1.4:
    resolution: {integrity: sha1-f4RzvIOd/YdgituV1+sHUhFXikI=}
    engines: {node: '>=0.8.0'}
    dev: true

  /which-boxed-primitive/1.0.2:
    resolution: {integrity: sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=}
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4
    dev: true

  /which-module/2.0.1:
    resolution: {integrity: sha1-d2sf412Qrr6Z6KwV6yQJM4mkpAk=}

  /which-typed-array/1.1.11:
    resolution: {integrity: sha1-mdaR8jxyqrZ2hoCAWicbaXYe1ho=}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.0
    dev: true

  /which/1.3.1:
    resolution: {integrity: sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /which/2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /word-wrap/1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=}
    engines: {node: '>=0.10.0'}
    dev: true

  /worker-farm/1.7.0:
    resolution: {integrity: sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=}
    dependencies:
      errno: 0.1.8
    dev: true

  /worker-loader/2.0.0:
    resolution: {integrity: sha1-Rf2j73asqBV3GokQc5nuQRm0MKw=}
    engines: {node: '>= 6.9.0 || >= 8.9.0'}
    peerDependencies:
      webpack: ^3.0.0 || ^4.0.0-alpha.0 || ^4.0.0
    dependencies:
      loader-utils: 1.4.2
      schema-utils: 0.4.7
    dev: false

  /wrap-ansi/3.0.1:
    resolution: {integrity: sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=}
    engines: {node: '>=4'}
    dependencies:
      string-width: 2.1.1
      strip-ansi: 4.0.0
    dev: true

  /wrap-ansi/5.1.0:
    resolution: {integrity: sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=}
    engines: {node: '>=6'}
    dependencies:
      ansi-styles: 3.2.1
      string-width: 3.1.0
      strip-ansi: 5.2.0
    dev: true

  /wrap-ansi/6.2.0:
    resolution: {integrity: sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  /wrap-ansi/7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrappy/1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=}

  /write/1.0.3:
    resolution: {integrity: sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=}
    engines: {node: '>=4'}
    dependencies:
      mkdirp: 0.5.6
    dev: true

  /ws/6.2.2:
    resolution: {integrity: sha1-3Vzb1XqZeZFgl2UtePHMX66gwy4=}
    dependencies:
      async-limiter: 1.0.1
    dev: true

  /ws/7.4.6:
    resolution: {integrity: sha1-VlTKjs3u5HwzqaS/bSjivimAN3w=}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dev: false

  /xml/1.0.1:
    resolution: {integrity: sha1-eLpyAgApxbyHuKgaPPzXS0ovweU=}
    dev: false

  /xmlhttprequest-ssl/1.6.3:
    resolution: {integrity: sha1-A7cThzsBZZ36LBxdBWBlsn3cLeY=}
    engines: {node: '>=0.4.0'}
    dev: false

  /xtend/2.1.2:
    resolution: {integrity: sha1-bv7MKk2tjmlixJAbM3znuoe10os=}
    engines: {node: '>=0.4'}
    dependencies:
      object-keys: 0.4.0
    dev: false

  /xtend/4.0.2:
    resolution: {integrity: sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=}
    engines: {node: '>=0.4'}

  /y18n/4.0.3:
    resolution: {integrity: sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=}

  /y18n/5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=}
    engines: {node: '>=10'}
    dev: true

  /yallist/2.1.2:
    resolution: {integrity: sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=}
    dev: true

  /yallist/3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=}
    dev: true

  /yallist/4.0.0:
    resolution: {integrity: sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=}
    dev: true

  /yargs-parser/13.1.2:
    resolution: {integrity: sha1-Ew8JcC667vJlDVTObj5XBvek+zg=}
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0
    dev: true

  /yargs-parser/18.1.3:
    resolution: {integrity: sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=}
    engines: {node: '>=6'}
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0
    dev: false

  /yargs-parser/20.2.9:
    resolution: {integrity: sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=}
    engines: {node: '>=10'}
    dev: true

  /yargs/13.3.2:
    resolution: {integrity: sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=}
    dependencies:
      cliui: 5.0.0
      find-up: 3.0.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 3.1.0
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 13.1.2
    dev: true

  /yargs/15.4.1:
    resolution: {integrity: sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=}
    engines: {node: '>=8'}
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3
    dev: false

  /yargs/16.2.0:
    resolution: {integrity: sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=}
    engines: {node: '>=10'}
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9
    dev: true

  /yeast/0.1.2:
    resolution: {integrity: sha1-AI4G2AlDIMNy28L47XagymyKxBk=}
    dev: false

  /yorkie/2.0.0:
    resolution: {integrity: sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=}
    engines: {node: '>=4'}
    requiresBuild: true
    dependencies:
      execa: 0.8.0
      is-ci: 1.2.1
      normalize-path: 1.0.0
      strip-indent: 2.0.0
    dev: true

  /zrender/4.3.2:
    resolution: {integrity: sha1-7HQy+UFcgsc1hLa3uMR+GwFiCcY=}
    dev: false
