START_TIMER=$((`date '+%s'`*1000));
#删除build-prod目录
rm -rf build-prod
rm public/build.txt
# . /home/<USER>/.nvm/nvm.sh
# . /home/<USER>/.nvm/bash_completion
unset npm_config_prefix
# source $HOME/nvm/nvm.sh
nvm use v14.16.0
echo node -v
# git submodule init
# git submodule update 
npm install --registry=https://art.haizhi.com/artifactory/api/npm/npm/
npm rebuild node-sass

#执行node打包脚本
vue-cli-service build --mode production --dir build-prod
echo '打包脚本执行完毕';
#执行node打包脚本
# cross-env NODE_ENV=testing vue-cli-service build
# echo '打包脚本执行完毕';

# mkdir build-pre

#输出打包信息
sh config/build-info.sh --dest=build-prod

END_TIMER=$((`date '+%s'`*1000))
TIMER=$((($END_TIMER - $START_TIMER)/1000/60));
SECOND=$((($END_TIMER - $START_TIMER)/1000%60));
echo '构建时间：'$TIMER'分钟'$SECOND'秒';
echo 'ALL BUILD DONE';
