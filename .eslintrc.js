module.exports = {
  root: true,
  env: {
    browser: true,
    node: true
  },
  extends: [
    'plugin:vue/essential',
    '@vue/standard'
  ],
  parserOptions: {
    parser: 'babel-eslint'
  },
  globals: {
    '_': 'readonly'
  },
  rules: {
    // "off" 或 0 - 关闭规则; "warn" 或 1 - 警告。"error" 或 2 - 错误。
    // 数组格式，第一项总是规则的严重程度，后面是规则参数
    // 配置定义在插件中的一个规则的时候，你必须使用 插件名/规则ID 的形式
    'no-console': ["error", { allow: ["warn", "error"] }],
    'no-debugger': "error",
    semi: 'off',
    'no-useless-escape': 'off',
  }
}
