# 控件配置规范

主要思想：json 配置 -> 视图 -> 更新 -> 还原成 json。

## 配置规则

单个控件配置 = 通用配置 + 私有配置

## 通用配置

| 字段名 | 含义 | 类型 | 	是否必选 | 备注 |
| :---: | :---: | :---: | :---: | :---: |
| `name` | 显示名 | string | 是 | 示例值："文本框" |
| `type` | 类型 | string | 是 | 示例值："text"。当设置为hidden时，将不对组件进⾏渲染。|
| `default` | 默认值 | string、number、object、array | 否 |  无 |
| `showInPanel` | 配置项是否在⾯板显示 | Boolean | 否 | 可定义配置项的显隐规则。不配置时，显示配置项；配置时，按照配置规则显示，配置规则请参见showInPanel配置规则。|
| `caption` | 配置项标注 | string | 否 | 不配置时，将不显示标注。(????) |
| `description` | 配置项描述 | string | 否 | 描述文字 |
| `handler` | 处理函数名 | string | 否 | 无。(????)|
| `col` | 控件主体部分所占栅格数 | number | 否 | 采⽤24栅格系统。(????)|
| `valuePath` | 配置项对应值的路径 | string | 否 | 配置规则请参见valuePath配置规则。|
| `show` | 自定义配置项的显示与隐藏	| object | 否 | ??? |

## 控件配置

参照 [datav控件配置](https://help.aliyun.com/document_detail/155354.html?spm=a2c4g.11186623.2.18.457b6e1fV4oyHo#concept-2427732) 实现。

TODO: 后亮亮完善。

## 技术实现

核心类：`/src/lib/ConfigTree.js` 用于解析配置项结构以及生成路径和配置值对象信息。

在 vuex 中声明 `chartConfig` 的 state，用于存储配置项值，通过 `updateChartConfig` 和 `updateChartConfigValue` 分别初始化和更新值。

通过 mixns 将配置值和结点信息传递给每个小组件。文件`src/mixins/config.js`。

小组件通过 `this.node.data` 访问配置信息。通过 `this.value` 获取和设置值。

## 参考文档

* [DataV控件概览](https://help.aliyun.com/document_detail/155352.html?spm=a2c4g.11186623.6.957.2fe12f222eVs50)