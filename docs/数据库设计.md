# 大屏数据库设计

## 用户表(user)

```json
{
  "id": 2323,
  "name": "admin",
  "password": "xxxxx",
  "email": "<EMAIL>",
  "phone": "12345325675",
  "createdAt": "创建时间",
  "updatedAt": "更新时间"
}
```

## 角色表(role)

```json
{
  "id": 313,
  "name": "角色名",
  "description": "角色描述",
  "createdAt": "创建时间",
  "updatedAt": "更新时间"
}
```

## 功能表(function)

```json
{
  "id": 211,
  "name": "功能英文名",
  "alias": "功能中文别名",
  "description": "功能描述"
}
```

## 工作空间表(workspace)

```json
{
  "id": 1234,
  "name": "工作空间名称（不可重名）",
  "screenCount": 100
}
```

字段说明：
- `screenCount`: 支持的大屏最大数量

## 项目表(project)

```json
{
  "id": 1234,
  "workspaceId": 456,
  "name": "项目名",
  "type": 1
}
```

字段说明：
- `type`: 项目类型，0 或 1，0表示未分组，1表示自己创建的。默认会有一个 0 作为未分组的显示。

## 大屏表(screen)

```json
{
  "id": 1234,
  "workspaceId": 56,
  "projectId": 12,
  "name": "大屏名称",
  "type": "pc",
  "templateId": 10,
  "createdAt": "创建时间",
  "updatedAt": "更新时间",
  "config": {
    "width": 1920,
    "height": 1080,
    "backgroundColor": "rgba(0,0,0,0)",
    "backgroundImage": "",
    "scaleType": "full_screen",
    "gridSpace": 1,
    "thumbnail": ""
  }
}
```

字段说明：
- `type`:	大屏类型，枚举: pc,mobile。
- `templateId`: 大屏基于的模板 id。
- `config`: 大屏配置项。
- `config.scaleType`: 缩放方式，枚举: full_screen,full_width,full_height,full_height_scroll,no_scale。

## 组件包表(packages)

```json
{
  "id": 123,
  "userId": 456,
  "name": "mlh-test-1",
  "version": "1.0.0",
  "alias": "fasfasfd",
  "icon": "",
  "type": "custom",
  "parent": null,
  "children": null,
  "createdAt": "创建时间",
  "updatedAt": "更新时间",
  "config": "{\"cn_name\":\"fasfasfd\",\"type\":\"custom\",\"icon\":\"\",\"show\":true,\"width\":400,\"height\":200,\"children\":null,\"parent\":null,\"data\":{\"source\":[{\"x\":\"普货\",\"y\":5},{\"x\":\"普货\",\"y\":22},{\"x\":\"泡货\",\"y\":22},{\"x\":\"设备\",\"y\":14},{\"x\":\"矿产\",\"y\":15},{\"x\":\"钢铁\",\"y\":15},{\"x\":\"建材\",\"y\":12},{\"x\":\"食品\",\"y\":12},{\"x\":\"粮食\",\"y\":28}],\"fields\":[{\"name\":\"x\",\"type\":\"string\",\"description\":\"类目\"},{\"name\":\"y\",\"type\":\"number\",\"description\":\"数值\"}]},\"config\":{\"size\":{\"name\":\"字号\",\"type\":\"number\",\"default\":22,\"range\":[10,100]},\"color\":{\"name\":\"颜色\",\"type\":\"fill\",\"default\":\"#fff\"},\"bgColor\":{\"name\":\"背景颜色\",\"type\":\"fill\",\"default\":\"#fff\"}},\"events\":[{\"name\":\"click\",\"description\":\"点击时\",\"params\":[{\"name\":\"x\",\"type\":\"string\",\"description\":\"类目\"},{\"name\":\"y\",\"type\":\"number\",\"description\":\"数值\"}]},{\"name\":\"hover\",\"description\":\"悬浮时\",\"params\":[{\"name\":\"x\",\"type\":\"string\",\"description\":\"类目\"},{\"name\":\"y\",\"type\":\"number\",\"description\":\"数值\"}]}],\"actions\":[{\"name\":\"show\",\"description\":\"显示\",\"params\":[]},{\"name\":\"highlight\",\"description\":\"高亮显示\",\"params\":[{\"name\":\"x\",\"type\":\"string\",\"description\":\"柱子类目\"}]}]}"
}
```

## 组件实例表(components)

```json
{
  "id": 123,
  "screenId": 345,
  "type": "com",
  "comName": "组件英文名",
  "alias": "组件中文名",
  "version": "1.0.0",
  "comType": "custom",
  "parent": 123,
  "icon": "",
  "show": true,
  "config": {},
  "dataConfig": {},
  "staticData": [],
  "interactionConfig": {},
  "attr": {
    "w": 300,
    "h": 150,
    "x": 0,
    "y": 0,
    "deg": 0,
    "opacity": 0
  },
  "createdAt": "创建时间",
  "updatedAt": "更新时间"
}
```

字段说明：
- `type`: 枚举: com,subCom,layer。组件、子组件、组。
- `parent`: 父组件id。
- `show`: 组件是否显示在大屏上。
- `config`: 控件配置解析后的值。
- `dataConfig`: 数据面板配置。
- `staticData`: 静态数据。
- `interactionConfig`: 交互面板配置。
- `attr`: 通用属性。

## 过滤器表(filters)

```json
{
  "id": 12,
  "screenId": 23,
  "name": "过滤器名称",
  "code": "过滤器代码",
  "originCode": "原始代码",
  "createdAt": "创建时间",
  "updatedAt": "更新时间",
  "isDelete": 0
}
```