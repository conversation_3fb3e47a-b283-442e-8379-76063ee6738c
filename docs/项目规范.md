# 项目规范

项目采用 Vue-Cli 脚手架搭建，大部分规范遵循[Vue 组件规范](https://cn.vuejs.org/v2/style-guide)设计。
现整理以下代码规范，分为必须的和推荐的。

## 命名规范（必须的）

所有名字应使用常见且易于理解的英文命名，避免采用中文命名。
除了常用的英文缩写外，命名避免使用英文缩写，以免引起歧义。
**总之：命名应该让别人一眼能看懂文件含义，而不是只有自己能看懂。**

命名规范：

* `kebab-case`：短横线命名，所有字母小写，不同单词用短横线连接。
* `camelCase`：驼峰命名法。（本规范约定驼峰命名均默认为`lowerCamelCase`）
  * `lowerCamelCase`：小驼峰命名法：第一个单词首字母小写，其他大写。
  * `UpperCamelCase`：大驼峰命名法：第一个单词首字母大写，其他大写。等同于 `PascalCase` 命名法。
* `PascalCase`：所有单词首字母大写，没有连字符。

### 文件名、文件夹名

文件夹名一律采用 `kebab-case`命名。

属于类的`.js`文件，使用`PascalCase`风格。

`*.vue`文件命名规范: 除`index.vue`之外，其他`.vue`文件统一用`PascalCase`风格。

其他类型的文件，使用`kebab-case`风格。

### 组件命名

* 组件名为多个单词，避免使用 HTML 原始的标签名。

* 组件名应该以高级别的 (通常是一般化描述的) 单词开头，以描述性的修饰词结尾。

* 所有地方引入组件都采用`<MyComponent></MyComponent>` 或 `<MyComponent/>`写法。

* JS/JSX 中的组件名应该始终是 PascalCase 的。

```js
import MyComponent from './MyComponent.vue';
export default {
  name: 'MyComponent',
  // ...
}
```
* 组件名应该倾向于完整单词而不是缩写。

* 类名应遵循 BEM 命名规范。

### 组件 Prop

组件 Prop 定义应该尽量详细。

在声明 prop 的时候，其命名应该始终使用 camelCase，而在模板和 JSX 中应该始终使用 kebab-case。

```js
props: {
  greetingText: String
}
<MyComponent greetingText="hi"><MyComponent/>
```

### 多个 attribute 的元素

多个(超过三个) attribute 的元素应该分多行撰写，每个 attribute 一行。

```html
<MyComponent
  foo="a"
  bar="b"
  baz="c"
>
<MyComponent/>
```

### 组件配置项顺序书写规范

每个组件配置项之间应空出一行，首行前面不用空。

```js
{
  name: 'CompAbc',

  components: {
    MyComponent
  },

  directives: {},

  filters: {},

  extends: {},

  mixins: [],

  inheritAttrs: true,

  model: {},

  props: {},

  data() {
    return {};
  },

  computed: {},

  watch: {},

  beforeCreate() {},
  created() {},
  beforeMount() {},
  mounted() {},
  beforeUpdate() {},
  updated() {},
  activated() {},
  deactivated() {},
  beforeDestroy() {},
  destroyed() {},

  methods: {}
}
```

### 全局组件

写全局基础组件应做好抽象和封装。

组件名一律以`Seatom`做前缀。

### 完整的组件规范示例

```html
<template>
  <div class="container">
    <MyComponent
      name="aaa"
      config="bbb"
      @event="handleEvt"
    >
    </MyComponent>
  </div>
</template>

<script>
import MyComponent from '.../components/xxxx';
export default {
  name: 'CompAbc',

  components: {
    MyComponent
  },

  directives: {},

  filters: {},

  extends: {},

  mixins: [],

  inheritAttrs: true,

  model: {},

  props: {},

  data() {
    return {};
  },

  computed: {},

  watch: {},

  beforeCreate() {},
  created() {},
  beforeMount() {},
  mounted() {},
  beforeUpdate() {},
  updated() {},
  activated() {},
  deactivated() {},
  beforeDestroy() {},
  destroyed() {},

  methods: {}
}
</script>

```

## JS(ESlint) 规范

继承 eslint 标准规范，自定义以下规范：

* 使用分号结束语句。
* 多使用 ES6 原生语法，如 let、const 声明，不要用 var 声明变量。
* 数组、对象的一些操作尽量使用原生方法，如不能使用原生方法的，用 loadash 实现。
* js基础教程：[现代 JavaScript 教程](https://zh.javascript.info/)
* 链式调用
```js
fileName
  .split('/')
  .pop()
  .replace(/\.\w+$/, '')
```


## CSS 样式规范

* 类名采用[BEM命名法](https://www.w3cplus.com/css/css-architecture-1.html)。
  具体可做变通，状态量可以不加前面一串，如`--hide,--leavein`
* 使用全局样式，不要重复定义。
* 选择器用类名，不要写多余的元素名。
* 多使用 flex 和 css3 布局方式。
* z-index 能不用就不用。最大值限定在：1000。具体参考张鑫旭《css世界》。

## git 规范

分支管理：
master：分支为主分支，不要在 master 上提交个人代码，只在需要发版时先合 dev ，后将 dev 合到 master。
dev：分支为开发分支，为开发时团队成员共同合代码的分支。
个人分支：以个人名字命名的分支，个人开发功能用。

注：个人只能往个人分支上push代码，要将代码合并到 dev 分支，需要提 PR。

提交规范：
fix：修改问题和bug
add：新增功能和特性
improve：优化功能
doc：文档和代码注释
lint：代码格式修改
style：样式修改

如：
```text
add: 用户登录
fix: 主题色切换问题
```

## 多主题

主题文件放置在`public/themes`目录，使用[CSS3变量](https://www.cnblogs.com/jofun/p/11917401.html?utm_source=tuicool&utm_medium=referral)作为主题变量。

主题变量存储在 vuex 中，默认从本地 localStorage 中读取。组件通过 watch vuex 中 theme 参数变化，调用 `src/utils/theme.js`中`toggleTheme`函数来引入对应主题。

## 遇到问题？

遇到语言使用上的问题，应首先查官方文档。

* [MDN](https://developer.mozilla.org/zh-CN/)  -- 权威的html、css问题查询
* [ES6 入门教程](https://es6.ruanyifeng.com/) -- ES6问题
* [Vue](https://cn.vuejs.org/) -- vue文档

