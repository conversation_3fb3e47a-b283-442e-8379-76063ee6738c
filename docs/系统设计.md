# 系统设计

## 总体思想

基于构建前端低代码平台的设计思路，构建可视化大屏制作工具。

工具具备大屏生产制作、发布共享、定制开发、灵活配置、操作简便快速等特点。

**核心是组件，其次是编辑器。**

## 详细设计

### 页面路由设计

路由是一个嵌套的树结构

```js
const routes = {
  path: '/',
  name: '根路由',
  children: [
    {
      path: 'login',
      name: '登录'
    },
    {
      path: 'register',
      name: '注册'
    },
    {
      path: 'reset-password',
      name: '找回密码'
    },
    {
      path: 'workspace',
      name: '工作空间',
      children: [
        {
          path: '',
          name: '数据大屏'
        },
        {
          path: 'data',
          name: '我的数据'
        },
        {
          path: 'component',
          name: '我的组件'
        },
        {
          path: 'help',
          name: '帮助中心'
        },
        {
          path: 'create',
          name: '新建大屏'
        },
        {
          path: 'edit/:screenId',
          name: '编辑大屏'
        },
        {
          path: 'preview/:screenId',
          name: '预览大屏'
        }
      ]
    }
  ]
}
```

### 用户模块

不同用户登录系统看到的大屏不同，所有用户都可以创建、修改和删除自己的大屏。

未登录的用户不能访问系统和大屏页面。

### 编辑器模块

编辑器相当于组件的组装车间，应提供以下功能：

**图层列表：**

展示当前画布上的图层，顺序从上往下依次代表最顶部到最底部的图层。

图层列表可以对图层做排序(置顶、置底、上移、下移)、复制、删除等右键操作，后续考虑支持成组、取消成组操作。

图层列表操作应与画布图层操作同步。

技术实现：采用封装的 tree 数据结构（考虑后期的成组）操作。

**组件列表：**

包含了内置组件和用户自定义组件。可以按类型分组。

**画布：**

采用自由画布模式，支持：缩放、标尺度量、拖放、改变大小等功能。

鼠标点击画布之外时，配置项展示画布配置；点击组件时，配置项显示组件配置。

画布配置项：屏幕大小、背景颜色、背景图片、页面缩放方式、栅格间距。

**组件配置：**

读取组件配置文件`package.json`自动渲染成页面。

应支持正确渲染配置数据、配置项改变响应、读取配置结果等功能。

未选中组件时，显示大屏配置；选中组件时，显示组件配置。

### 发布预览模块

大屏最终产出的是一个 json 配置，前端可以通过读取这个配置还原出整块大屏。

### 组件设计细节

所有组件都抽象成一个 npm 包，包以 Vue 组件定义的方式导出该组件。

#### 组件配置项结构

主要参考 [datav-package.json规范](https://help.aliyun.com/document_detail/86882.html?spm=a2c4g.11174359.6.912.54e15dd3WJ8tCA)。本项目部分配置项做了修改。

```json
{
  "name": "bar", // npm 包名
  "version": "1.0.0", // npm 版本号
  "chartConfig": { // 组件配置
    "name": "柱状图", // 中文名称
    "en_name": "barchart", // 英文名称
    "type": "C100", // 组件类型，内置组件用。自定义组件一律为同一代码。具体参见组件类型表
    "group": "regular", // 所属分组，字符串或数组，字符串代表属于一个分组。
    "supportConfig": true, // 是否支持配置，如果为 false，则点击组件时，不弹出组件配置面板。
    "supportTheme": true, // 是否支持主题
    "view": { // 组件初始宽高，单位: pixel
      "width": 300,
      "height": 200,
      "minWidth": 100,
      "minHeight": 50
    },
    "icon": "", // 组件图标链接地址。
    "apis": {}, //组件接口，可以多个。
    "config": {},  //组件配置，给编辑器识别用。
    "api_data": {}, //接口数据，可以多个。
    "events": {}, // 组件事件配置
    "publicHandler": {} // 组件行为配置
  },
  "dependencies": {} // 依赖库
}
```

`group`分组字段：

```javascript
{
  regular: "常规图表",
  map: "地图",
  text: "文本",
  network: "关系网络",
  media: "媒体",
  decorate: "辅助图形",
  interact: "交互"
}
```

`config`控件配置

移步：`docs/控件配置.md`。

#### 组件注册流程

1. 编写组件 npm 包。
2. 注册 npm 包。
   读取组件注册表，若包名且版本号已有，则无法注册。
   读取组件依赖表，若包名且版本号已有，则无法注册。
   若包名与npm线上包名重复，也无法注册。
   其他情况则允许注册。

（在此推荐组件包名前统一加一个名称空间，以免重复。）

注册成功后，会将组件信息写入注册表，注册表用来维护用户注册的所有组件。

组件注册表结构：

```json
{
  "id": "xxxxx", // 唯一id
  "name": "bar", // 包名
  "version": "1.0.1", // 版本号
  "alias": "中文名", // 组件中文名
  "icon": "http://xxx", // 图标地址
  "config": "xxx" // JSON.stringify(packagejson.chartConfig)
}
```

组件依赖表结构：

```json
{
  "id": "xxxxx", // 唯一id
  "name": "jquery", // 包名
  "version": "1.12.1", // 版本号
}
```

组件资源存放路径：

```
|——packages
|   |——components
|   |  |——name1@version1
|   |  |——name2@version2
|   |  |——name3@version3
|   |——dependencies
|   |  |——name1@version1
|   |  |——name2@version2
|   |  |——name3@version3
```

最终生成一个总的 map 映射表供前端按需引入使用：

```json
{
  "name1@version1": "http://xxx.js",
  "name2@version2": "http://xxx.js",
  "name3@version3": "http://xxx.js",
  "name4@version4": "http://xxx.js"
}
```

#### 组件实例表结构

组件实例是指将左侧组件拖到画布中后生成的实例，它将会产生自己的一些属性信息。

```json
{
  "id": "xxxx", // 唯一 id
  "name": "bar", // 包名
  "version": "1.0.1", // 版本号
  "type": "C101", // 类型
  "alias": "xxxx", // 中文名
  "icon": "http://xxx", // 图标地址
  "config": "{}", // 原始配置项对象
  "configVal": "{}", // 配置项对象对应的值
  "attr": { // 通用属性
    "w": 200, // 宽
    "h": 150, // 高
    "x": 0, // x坐标
    "y": 0, // y坐标
    "deg": 0, // 旋转角度
    "opacity": 1 // 透明度
  }
}
```
#### 回调设计

回调用于组件间数据的交互。组件开发者在组件内部调用系统提供的函数更新回调数据，使用回调的组件在`数据配置`处配置回调。

[easyv 回调组件开发](https://dtstack.yuque.com/books/share/e4165ff9-8406-48a7-8036-f417d74aa091/biqsl3)
[回调的使用](https://www.yuque.com/zhouyanjiuyi/wdh5pn/eksx4b)

具体实现思路：
```js
class CallbackManager {
  constructor() {
    this.keyMapComp = {};
    this.compMapKey = {};
    this.compSubscribleKeys = {};
    this.state = {};
  }
  addCallbackValue(compId, key) {
    this.state[key] = null;
    (this.keyMapComp[key] || (this.keyMapComp[key] = new Set())).add(compId);
    (this.compMapKey[compId] || (this.compMapKey[compId] = new Set())).add(key);
  }
  removeCallbackValue(compId, key) {
    delete this.state[key];
    delete this.keyMapComp[key];
    if (this.compMapKey[compId]) {
      this.compMapKey[compId].delete(key);
    }
  }
  updateCallbackValue(params) {
    const updateKeys = [];
    Object.keys(params).forEach(key => {
      if (this.state.hasOwnProperty(key)) {
        this.state[key] = params[key];
        updateKeys.push(key);
      }
    });
    // callback
    const callbackObjs = updateKeys.reduce((res, key) => {
      this.compSubscribleKeys[key] && res.push(...this.compSubscribleKeys[key]);
      return res;
    }, []);
    const updateData = updateKeys.reduce((res, key) => {
      res[key] = this.state[key];
      return res;
    }, {});
    let called = {};
    callbackObjs.forEach(cbObj => {
      if (!called[cbObj.id]) {
        cbObj.cb.call(null, updateData);
        called[cbObj.id] = true;
      }
    });
  }
  subscribeCallbackValue(compId, keys, callback) {
    keys.forEach(key => {
      (this.compSubscribleKeys[key] || (this.compSubscribleKeys[key] = [])).push({
        id: compId,
        cb: callback
      });
    });
  }
}
```

## 技术栈

前端：

* [Vue.js](https://cn.vuejs.org/)

后端：

* [Egg.js](https://eggjs.org/zh-cn)

数据库：

* [mongoDB](https://www.mongodb.org.cn/)

## 参考文档

* [可视化搭建数据大屏系统的前端实现](https://www.zoo.team/article/data-visualization)
* [政采云前端团队-前端工程实践之可视化搭建系统](https://juejin.im/post/6844903950508883982)
* [你必须掌握的可视化大屏开发模式](https://juejin.im/post/6844904007878574094)
