const { execSync } = require('child_process');
const fs = require('fs');

const commitId = execSync('git log -n1 --format=format:"%H"').toString();
const branch = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
const commitTime = execSync('git log -n1 --pretty=format:"%ad" --date=iso').toString().substring(0, 19);

const info = `branch: ${branch}\ncommitId: ${commitId}\ntime: ${commitTime}`
fs.writeFileSync('./public/build.txt', info);

console.warn('========== Save git info done. ==========');
