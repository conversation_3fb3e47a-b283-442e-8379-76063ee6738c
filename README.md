
# seatom

## 分支说明

* dev  伏羲演示环境
* test  伏羲测试环境
* dev-ssr-use  基于dev分支，给伏羲服务端渲染用的，dev分支更新之后，要合并到该分支，该分支不要合并到dev分支，该分支主要是src/utils/http.js与dev分支有差异
* test-ssr-use  基于test分支，给伏羲服务端渲染用的，test分支更新之后，要合并到该分支，该分支不要合并到test分支，该分支主要是src/utils/http.js与test分支有差异，要不要这个分支取决于要不要在测试环境测试服务端渲染功能
* gd-yfb-new  数字广东可视化预发布、正式环境分支
* gd-test-new  数字广东可视化测试环境分支


## Project setup

```
npm install
```

### Compiles and hot-reloads for development

```
# 启动开发联调环境，连接开发环境(development)后台
npm run serve
# 启动演示环境，连接演示环境(preview)后台
npm run preview
# 启动测试环境，连接测试环境(test)后台
npm run test
```

### Compiles and minifies for production

```
# 构建开发联调环境
npm run build
# 构建生产环境
npm run build-prod
# 构建演示环境
npm run build-pre
# 构建测试环境
npm run build-test
```

### Lints and fixes files

```
npm run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

### 添加图标 icon 方式

```
1.添加icon的方式： 将svg图片引入 src/assets/img/iconSvg 中 后执行 npm run svgo
2.使用： <hz-icon name="icon的名字"></hz-icon>
```

### 添加无损 icon 方式 此方法导入不会改变 svg icon 原颜色

```
1.添加icon的方式： 将svg图片引入 src/assets/img/icon-svg-lossless 中 后执行 npm run svgo-lossless
2.使用： <hz-icon name="icon的名字"></hz-icon>
```
