# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@achrinza/node-ipc@9.2.2":
  "integrity" "sha1-rhtdPWqTYgNO6mDI2Ua5OJPC5Ow="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@achrinza/node-ipc/-/node-ipc-9.2.2.tgz"
  "version" "9.2.2"
  dependencies:
    "@node-ipc/js-queue" "2.0.3"
    "event-pubsub" "4.3.0"
    "js-message" "1.0.7"

"@ampproject/remapping@^2.1.0":
  "integrity" "sha1-VsEzgkeA3jF0rtWraDTzAmeQFU0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@ampproject/remapping/-/remapping-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.1.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6", "@babel/code-frame@^7.22.10", "@babel/code-frame@^7.22.5":
  "integrity" "sha1-HCDmErdo/vp19ukNbsuGMpJH8KM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.22.10.tgz"
  "version" "7.22.10"
  dependencies:
    "@babel/highlight" "^7.22.10"
    "chalk" "^2.4.2"

"@babel/compat-data@^7.17.7", "@babel/compat-data@^7.18.8", "@babel/compat-data@^7.19.1":
  "integrity" "sha1-ctZHtP9qT4KHjRhGEzU68d0CkPk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/compat-data/-/compat-data-7.19.1.tgz"
  "version" "7.19.1"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.11.0", "@babel/core@^7.12.0", "@babel/core@^7.13.0", "@babel/core@^7.4.0-0":
  "integrity" "sha1-yPphXF6I4nJWSs49QvvIsXv+sis="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/core/-/core-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.19.0"
    "@babel/helper-compilation-targets" "^7.19.1"
    "@babel/helper-module-transforms" "^7.19.0"
    "@babel/helpers" "^7.19.0"
    "@babel/parser" "^7.19.1"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.19.1"
    "@babel/types" "^7.19.0"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.1"
    "semver" "^6.3.0"

"@babel/generator@^7.19.0", "@babel/generator@^7.22.10":
  "integrity" "sha1-ySJUNh85jhYGRaxYgxBpcHOCtyI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/generator/-/generator-7.22.10.tgz"
  "version" "7.22.10"
  dependencies:
    "@babel/types" "^7.22.10"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    "jsesc" "^2.5.1"

"@babel/helper-annotate-as-pure@^7.18.6":
  "integrity" "sha1-6qSfb4DVoz+aXdInbm1uRRvgprs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6":
  "integrity" "sha1-rNTt/XpWbR1R6pdd/zj9UpBpgbs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.18.6"
    "@babel/types" "^7.18.9"

"@babel/helper-compilation-targets@^7.17.7", "@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.19.0", "@babel/helper-compilation-targets@^7.19.1", "@babel/helper-compilation-targets@^7.9.6":
  "integrity" "sha1-f2MJEdg7QIt2/lhIMcmOU5XXoXw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-compilation-targets/-/helper-compilation-targets-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@babel/compat-data" "^7.19.1"
    "@babel/helper-validator-option" "^7.18.6"
    "browserslist" "^4.21.3"
    "semver" "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.19.0":
  "integrity" "sha1-v9aQRiDfTkZHC65IUNZr4QVMQEs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.9"
    "@babel/helper-split-export-declaration" "^7.18.6"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.19.0":
  "integrity" "sha1-eXasphwJhCArrKc9hOIzelQkpBs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "regexpu-core" "^5.1.0"

"@babel/helper-define-polyfill-provider@^0.3.3":
  "integrity" "sha1-hhLlW+XVHwzR82tKWoOSTomIS3o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/helper-compilation-targets" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-environment-visitor@^7.18.9", "@babel/helper-environment-visitor@^7.22.5":
  "integrity" "sha1-8G3UG3wfROH42mxAVbQas6Cafpg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.5.tgz"
  "version" "7.22.5"

"@babel/helper-explode-assignable-expression@^7.18.6":
  "integrity" "sha1-QfgijvCm8aA2uN/f7HzpT5prwJY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-function-name@^7.18.9", "@babel/helper-function-name@^7.19.0", "@babel/helper-function-name@^7.22.5":
  "integrity" "sha1-7eMAgokFuxXlgsA3Fi+Z1Rg68b4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/template" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/helper-hoist-variables@^7.18.6", "@babel/helper-hoist-variables@^7.22.5":
  "integrity" "sha1-wBoAfawFwIWRTo+2UrM521DYI7s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-member-expression-to-functions@^7.18.9":
  "integrity" "sha1-FTFmHoN1r4Q603rGksEyhB4v2BU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/types" "^7.18.9"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.18.6", "@babel/helper-module-imports@^7.8.3":
  "integrity" "sha1-Hj69u9CKrRQ3tCjFAgTbE8Wjym4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-imports@7.0.0-beta.35":
  "integrity" "sha1-MI41DnMXUs200PBY3x1wSSXGTgo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.35.tgz"
  "version" "7.0.0-beta.35"
  dependencies:
    "@babel/types" "7.0.0-beta.35"
    "lodash" "^4.2.0"

"@babel/helper-module-transforms@^7.18.6", "@babel/helper-module-transforms@^7.19.0":
  "integrity" "sha1-MJsjDwTiLFjGosDAx+ULIW01DDA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-module-transforms/-/helper-module-transforms-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.18.6"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.19.0"
    "@babel/types" "^7.19.0"

"@babel/helper-optimise-call-expression@^7.18.6":
  "integrity" "sha1-k2mqlD7n2kftqyy06Dis8J0pD/4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha1-R5a7FJYVIfD4cVmQvuL7blHOIb8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.19.0.tgz"
  "version" "7.19.0"

"@babel/helper-remap-async-to-generator@^7.18.6", "@babel/helper-remap-async-to-generator@^7.18.9":
  "integrity" "sha1-mXRYoOM1cIDlTh157DR/iozShRk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-wrap-function" "^7.18.9"
    "@babel/types" "^7.18.9"

"@babel/helper-replace-supers@^7.18.6", "@babel/helper-replace-supers@^7.18.9", "@babel/helper-replace-supers@^7.19.1":
  "integrity" "sha1-4Vkqm0s2iqa9uHhKcR4Ly/BhK3g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/traverse" "^7.19.1"
    "@babel/types" "^7.19.0"

"@babel/helper-simple-access@^7.18.6":
  "integrity" "sha1-1tj1H0rCl4Bo35NLVp8I8peIx+o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-simple-access/-/helper-simple-access-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-skip-transparent-expression-wrappers@^7.18.9":
  "integrity" "sha1-d42Hs6dY2QtHHnuZGPNKmgLrWBg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/types" "^7.18.9"

"@babel/helper-split-export-declaration@^7.18.6", "@babel/helper-split-export-declaration@^7.22.6":
  "integrity" "sha1-MixhtzEMCZf+TDI5VWZ/GPzvuRw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz"
  "version" "7.22.6"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-string-parser@^7.22.5":
  "integrity" "sha1-Uz82RXolgUzx32SIUjrVR9eEqZ8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz"
  "version" "7.22.5"

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.22.5":
  "integrity" "sha1-lUTvajOZk0PIdA+lE1DzDuqq8ZM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz"
  "version" "7.22.5"

"@babel/helper-validator-option@^7.18.6":
  "integrity" "sha1-vw0rWlCbHzNgmeT/NuGmOqXbTbg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz"
  "version" "7.18.6"

"@babel/helper-wrap-function@^7.18.9":
  "integrity" "sha1-ifGDNc/xFSNzIi92pLN3mWNq6LE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helper-wrap-function/-/helper-wrap-function-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-function-name" "^7.19.0"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.19.0"
    "@babel/types" "^7.19.0"

"@babel/helpers@^7.19.0":
  "integrity" "sha1-8wU0ZX+vJGrpZVHYjdMenR+h/Bg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/helpers/-/helpers-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.19.0"
    "@babel/types" "^7.19.0"

"@babel/highlight@^7.22.10":
  "integrity" "sha1-AqP22MHLRSGy/Qqw2o9HOZNhN9c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.22.10.tgz"
  "version" "7.22.10"
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.5"
    "chalk" "^2.4.2"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.19.1", "@babel/parser@^7.22.10", "@babel/parser@^7.22.5", "@babel/parser@^7.7.0":
  "integrity" "sha1-43Y0+aEqFxYTbERiTvVCg8q9P1U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/parser/-/parser-7.22.10.tgz"
  "version" "7.22.10"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.18.6":
  "integrity" "sha1-2luPmlgKzfvlNJTbpF6jifsJpNI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.18.9":
  "integrity" "sha1-oRrxmqNz1o1WHwjgpXJCNQ7Q7FA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
    "@babel/plugin-proposal-optional-chaining" "^7.18.9"

"@babel/plugin-proposal-async-generator-functions@^7.19.1":
  "integrity" "sha1-NPb1F0tohSk0IojNJk+AyeqftKc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-remap-async-to-generator" "^7.18.9"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.18.6", "@babel/plugin-proposal-class-properties@^7.8.3":
  "integrity" "sha1-sRD1l0GJX37CGm//aW7EYmXERqM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-class-static-block@^7.18.6":
  "integrity" "sha1-iqgdQDq3LTli/AbCbiItrPybkCA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-decorators@^7.8.3":
  "integrity" "sha1-S6s+ev6JT9v0f/qGcBJmEE/Lbsw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.19.0"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-replace-supers" "^7.19.1"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/plugin-syntax-decorators" "^7.19.0"

"@babel/plugin-proposal-dynamic-import@^7.18.6":
  "integrity" "sha1-crz41Ah5n1R9dZKYw8J8fn+qTZQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.18.9":
  "integrity" "sha1-X3MTqzSM2xnVkBRfkkdUDpR2EgM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.18.6":
  "integrity" "sha1-foeIwYEcOTr/digX59vx69DAXws="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.18.9":
  "integrity" "sha1-gUjLs1BIO/YiCvBvpts2kOFLLiM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator@^7.18.6":
  "integrity" "sha1-/dlAqZp0Dld9bHU6tvu0P9uUZ+E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.18.6":
  "integrity" "sha1-iZsU+6/ofwU9LF/wWzYCnGLhPHU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.18.9":
  "integrity" "sha1-+UNPa+ssjK6d/Pl9KllBu7+a1Oc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/compat-data" "^7.18.8"
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.18.8"

"@babel/plugin-proposal-optional-catch-binding@^7.18.6":
  "integrity" "sha1-+UANDmo+qTup73CwnnLdbaY4oss="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.16.7", "@babel/plugin-proposal-optional-chaining@^7.18.9":
  "integrity" "sha1-6Oj+ByPyVjlg5L9elpCTNpGRWZM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.18.6":
  "integrity" "sha1-UgnefSE0V1SKmENvoogvUvS+a+o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-private-property-in-object@^7.18.6":
  "integrity" "sha1-pkE3sjLwrKNzOmfrGhRMGSOJxQM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.18.6", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha1-r2E9LNXmQ2Q7Zc3tZCB7Fchct44="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.19.0":
  "integrity" "sha1-XxPR2PzpaVG+oBoQQkRjyaWzpZk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha1-AolkqbqA28CUyRXEh618TnpmRlo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.18.6":
  "integrity" "sha1-zWGQUApPov4xmQqWP/q0tj5FBeQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.8.3":
  "integrity" "sha1-qP7vY7AQFQq9l/FknsKW6EmUPKA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha1-ypHvRjA1MESLkGZSusLp/plB9pk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  "integrity" "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.18.6":
  "integrity" "sha1-GQY/z4dx7Hsx10IznaxiQz0GEf4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-async-to-generator@^7.18.6":
  "integrity" "sha1-zNo9GrnVztUmX9sT8YgtVHbHFhU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-remap-async-to-generator" "^7.18.6"

"@babel/plugin-transform-block-scoped-functions@^7.18.6":
  "integrity" "sha1-kYe/S6MCY1udcNmGrXDwOHJiFqg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-block-scoping@^7.18.9":
  "integrity" "sha1-+bfgGKw/NzyBRS1q2ovVoYkokm0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-classes@^7.19.0":
  "integrity" "sha1-DmHsJX+6QJxBNyF158HmBtx5uyA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-classes/-/plugin-transform-classes-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-compilation-targets" "^7.19.0"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-replace-supers" "^7.18.9"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.18.9":
  "integrity" "sha1-I1eoIk1ALa1iPK9iWbYR5WrsdG4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-destructuring@^7.18.13":
  "integrity" "sha1-ngO8SpRHXWK39BFJOObFwzNyy/U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.18.13.tgz"
  "version" "7.18.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-dotall-regex@^7.18.6", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "integrity" "sha1-soaz56rmx7hh5FvtCi+v1rGk/vg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-duplicate-keys@^7.18.9":
  "integrity" "sha1-aH8V7jza1thRkesqNyxFKOqgrg4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-exponentiation-operator@^7.18.6":
  "integrity" "sha1-QhxwX0UhiIxl6R/dGvlRv+/U2s0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-for-of@^7.18.8":
  "integrity" "sha1-bvilCyROtqC9utDHxhh35OMAl8E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.18.8.tgz"
  "version" "7.18.8"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-function-name@^7.18.9":
  "integrity" "sha1-zDVPgjTmKWiUbGGkbWNlRA/HZOA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-literals@^7.18.9":
  "integrity" "sha1-cnlv2++A5W+6PGppnVTw3lV0RLw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-member-expression-literals@^7.18.6":
  "integrity" "sha1-rJ/cGhGGIKxJt+el0twXehv+6I4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-modules-amd@^7.18.6":
  "integrity" "sha1-jJH4xRFdIgLyd1SYSIdAJ9cXLSE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-module-transforms" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.18.6":
  "integrity" "sha1-r9JDr7oWbMppiS4kqP2MnyyoeIM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-module-transforms" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-simple-access" "^7.18.6"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.19.0":
  "integrity" "sha1-XyC0cShEMPAtnFBZ2bmhbUsIWh8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-module-transforms" "^7.19.0"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-validator-identifier" "^7.18.6"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.18.6":
  "integrity" "sha1-gdODLWA0t1tU5ighuljyjtCqtLk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-module-transforms" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-named-capturing-groups-regex@^7.19.1":
  "integrity" "sha1-7HRVurbNj7BcUlqUh29DWkgSiIg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.19.0"
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-transform-new-target@^7.18.6":
  "integrity" "sha1-0Sjzdq4gBHfzfE3fzHIqihsyRqg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-object-super@^7.18.6":
  "integrity" "sha1-+zxszdFZObb/eTmUS1GXHdw1kSw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.6"

"@babel/plugin-transform-parameters@^7.18.8":
  "integrity" "sha1-7p8aDObXivWNCVapN46jQnzMtIo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.18.8.tgz"
  "version" "7.18.8"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-property-literals@^7.18.6":
  "integrity" "sha1-4iSYkDpINEjpTgMum7ucXMv8k6M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-regenerator@^7.18.6":
  "integrity" "sha1-WFxmy4TUtL9yUZo0z852G4Z2ynM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "regenerator-transform" "^0.15.0"

"@babel/plugin-transform-reserved-words@^7.18.6":
  "integrity" "sha1-savY6/jtql9/5ru40hM9I7am92o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-runtime@^7.11.0":
  "integrity" "sha1-o98tcxLupiTHiJotzTf9Hf0lssY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    "babel-plugin-polyfill-corejs2" "^0.3.3"
    "babel-plugin-polyfill-corejs3" "^0.6.0"
    "babel-plugin-polyfill-regenerator" "^0.4.1"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.18.6":
  "integrity" "sha1-bW33mD1nsZUom+JJCePxKo9mTck="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-spread@^7.19.0":
  "integrity" "sha1-3WC0Ygwv7IBtYM+q42TsIYjVk7Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-spread/-/plugin-transform-spread-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"

"@babel/plugin-transform-sticky-regex@^7.18.6":
  "integrity" "sha1-xnBusrFSQCjjF3IDOVg60PRErcw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-template-literals@^7.18.9":
  "integrity" "sha1-BOxvEKzaqBhGaJ1j+uEX3ZwkOl4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-typeof-symbol@^7.18.9":
  "integrity" "sha1-yM6mgmPkWt3NavyQkUKfgJJXYsA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-unicode-escapes@^7.18.10":
  "integrity" "sha1-Hs+w7ag9CbvLd8CZcMLdVYMqokY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.18.10.tgz"
  "version" "7.18.10"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-unicode-regex@^7.18.6":
  "integrity" "sha1-GUMXIl2MIBu64QM2T/6eLOo2zco="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/preset-env@^7.11.0", "@babel/preset-env@^7.18.9":
  "integrity" "sha1-nwTJFvnAIFpI6+XMG+d2jrGYP2c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/preset-env/-/preset-env-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@babel/compat-data" "^7.19.1"
    "@babel/helper-compilation-targets" "^7.19.1"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-validator-option" "^7.18.6"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.18.6"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-async-generator-functions" "^7.19.1"
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-proposal-class-static-block" "^7.18.6"
    "@babel/plugin-proposal-dynamic-import" "^7.18.6"
    "@babel/plugin-proposal-export-namespace-from" "^7.18.9"
    "@babel/plugin-proposal-json-strings" "^7.18.6"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.18.9"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.18.6"
    "@babel/plugin-proposal-numeric-separator" "^7.18.6"
    "@babel/plugin-proposal-object-rest-spread" "^7.18.9"
    "@babel/plugin-proposal-optional-catch-binding" "^7.18.6"
    "@babel/plugin-proposal-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-private-methods" "^7.18.6"
    "@babel/plugin-proposal-private-property-in-object" "^7.18.6"
    "@babel/plugin-proposal-unicode-property-regex" "^7.18.6"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.18.6"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.18.6"
    "@babel/plugin-transform-async-to-generator" "^7.18.6"
    "@babel/plugin-transform-block-scoped-functions" "^7.18.6"
    "@babel/plugin-transform-block-scoping" "^7.18.9"
    "@babel/plugin-transform-classes" "^7.19.0"
    "@babel/plugin-transform-computed-properties" "^7.18.9"
    "@babel/plugin-transform-destructuring" "^7.18.13"
    "@babel/plugin-transform-dotall-regex" "^7.18.6"
    "@babel/plugin-transform-duplicate-keys" "^7.18.9"
    "@babel/plugin-transform-exponentiation-operator" "^7.18.6"
    "@babel/plugin-transform-for-of" "^7.18.8"
    "@babel/plugin-transform-function-name" "^7.18.9"
    "@babel/plugin-transform-literals" "^7.18.9"
    "@babel/plugin-transform-member-expression-literals" "^7.18.6"
    "@babel/plugin-transform-modules-amd" "^7.18.6"
    "@babel/plugin-transform-modules-commonjs" "^7.18.6"
    "@babel/plugin-transform-modules-systemjs" "^7.19.0"
    "@babel/plugin-transform-modules-umd" "^7.18.6"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.19.1"
    "@babel/plugin-transform-new-target" "^7.18.6"
    "@babel/plugin-transform-object-super" "^7.18.6"
    "@babel/plugin-transform-parameters" "^7.18.8"
    "@babel/plugin-transform-property-literals" "^7.18.6"
    "@babel/plugin-transform-regenerator" "^7.18.6"
    "@babel/plugin-transform-reserved-words" "^7.18.6"
    "@babel/plugin-transform-shorthand-properties" "^7.18.6"
    "@babel/plugin-transform-spread" "^7.19.0"
    "@babel/plugin-transform-sticky-regex" "^7.18.6"
    "@babel/plugin-transform-template-literals" "^7.18.9"
    "@babel/plugin-transform-typeof-symbol" "^7.18.9"
    "@babel/plugin-transform-unicode-escapes" "^7.18.10"
    "@babel/plugin-transform-unicode-regex" "^7.18.6"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.19.0"
    "babel-plugin-polyfill-corejs2" "^0.3.3"
    "babel-plugin-polyfill-corejs3" "^0.6.0"
    "babel-plugin-polyfill-regenerator" "^0.4.1"
    "core-js-compat" "^3.25.1"
    "semver" "^6.3.0"

"@babel/preset-modules@^0.1.5":
  "integrity" "sha1-75Odbn8miCfhhBY43G/5VRXhFdk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/preset-modules/-/preset-modules-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/runtime@^7.11.0", "@babel/runtime@^7.3.1", "@babel/runtime@^7.8.4", "@babel/runtime@7.x":
  "integrity" "sha1-IrEcA3sJTSeoolBOpNz/APUOIlk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/runtime/-/runtime-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/template@^7.0.0", "@babel/template@^7.18.10", "@babel/template@^7.22.5":
  "integrity" "sha1-DIxNlEUJh1hJvQNE/wBQdW7vxuw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/template/-/template-7.22.5.tgz"
  "version" "7.22.5"
  dependencies:
    "@babel/code-frame" "^7.22.5"
    "@babel/parser" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.19.0", "@babel/traverse@^7.19.1", "@babel/traverse@^7.22.10", "@babel/traverse@^7.7.0":
  "integrity" "sha1-ICUqyyQOdG0nwugrRITxmc+BQao="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.22.10.tgz"
  "version" "7.22.10"
  dependencies:
    "@babel/code-frame" "^7.22.10"
    "@babel/generator" "^7.22.10"
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/parser" "^7.22.10"
    "@babel/types" "^7.22.10"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.18.6", "@babel/types@^7.18.9", "@babel/types@^7.19.0", "@babel/types@^7.22.10", "@babel/types@^7.22.5", "@babel/types@^7.4.4", "@babel/types@^7.7.0":
  "integrity" "sha1-Sp52RGBI8sZpgtGpid0SuKLS3AM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/types/-/types-7.22.10.tgz"
  "version" "7.22.10"
  dependencies:
    "@babel/helper-string-parser" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.5"
    "to-fast-properties" "^2.0.0"

"@babel/types@7.0.0-beta.35":
  "integrity" "sha1-z5M6mpo4SEynJLM1uI2Dcm1auWA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@babel/types/-/types-7.0.0-beta.35.tgz"
  "version" "7.0.0-beta.35"
  dependencies:
    "esutils" "^2.0.2"
    "lodash" "^4.2.0"
    "to-fast-properties" "^2.0.0"

"@chenfengyuan/vue-qrcode@^1.0.2":
  "integrity" "sha1-N9cZAuFm4a5YF2vWy5xAkFwbCUk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@chenfengyuan/vue-qrcode/-/vue-qrcode-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "qrcode" "^1.4.4"

"@emmetio/codemirror-plugin@^1.2.4":
  "integrity" "sha1-+ICjYJhTb2VVdH6t7Om3zC31l+M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@emmetio/codemirror-plugin/-/codemirror-plugin-1.2.4.tgz"
  "version" "1.2.4"

"@gar/promisify@^1.0.1":
  "integrity" "sha1-VVGTqy47s7atw9VRycAw2ehg2vY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@gar/promisify/-/promisify-1.1.3.tgz"
  "version" "1.1.3"

"@haizhi/monitor@^1.0.1":
  "integrity" "sha1-d8ld9XF6NY3Y3umShHgQikTrhtw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@haizhi/monitor/-/@haizhi/monitor-1.0.1.tgz"
  "version" "1.0.1"

"@hapi/address@2.x.x":
  "integrity" "sha1-XWftQ/P9QaadS5/3tW58DR0KgeU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@hapi/address/-/address-2.1.4.tgz"
  "version" "2.1.4"

"@hapi/bourne@1.x.x":
  "integrity" "sha1-CnCVreoGckPOMoPhtWuKj0U7JCo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@hapi/bourne/-/bourne-1.3.2.tgz"
  "version" "1.3.2"

"@hapi/hoek@^8.3.0", "@hapi/hoek@8.x.x":
  "integrity" "sha1-/elgZMpEbeyMVajC8TCVewcMbgY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@hapi/hoek/-/hoek-8.5.1.tgz"
  "version" "8.5.1"

"@hapi/joi@^15.0.1":
  "integrity" "sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@hapi/joi/-/joi-15.1.1.tgz"
  "version" "15.1.1"
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  "integrity" "sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@hapi/topo/-/topo-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@interactjs/actions@1.10.2":
  "integrity" "sha1-BQrhxAt703c6eaZcX7FzC7YRHnw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/actions/-/actions-1.10.2.tgz"
  "version" "1.10.2"
  optionalDependencies:
    "@interactjs/interact" "1.10.2"

"@interactjs/arrange@1.10.2":
  "integrity" "sha1-5tk8XAEmHRMG1GQSCVKJZx4b5NI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/arrange/-/arrange-1.10.2.tgz"
  "version" "1.10.2"

"@interactjs/auto-scroll@1.10.2":
  "integrity" "sha1-RuMv4KZ+Jmqx91jbHqbdFSiEA/A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/auto-scroll/-/auto-scroll-1.10.2.tgz"
  "version" "1.10.2"
  optionalDependencies:
    "@interactjs/interact" "1.10.2"

"@interactjs/auto-start@1.10.2":
  "integrity" "sha1-v5Ya5UHN98sPA8t7qJtN70Ri+rA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/auto-start/-/auto-start-1.10.2.tgz"
  "version" "1.10.2"
  optionalDependencies:
    "@interactjs/interact" "1.10.2"

"@interactjs/clone@1.10.2":
  "integrity" "sha1-sRCh2c/SBGhMQq93aEez+ay7rlM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/clone/-/clone-1.10.2.tgz"
  "version" "1.10.2"

"@interactjs/core@1.10.2":
  "integrity" "sha1-ALycnqn1DB40Wrlt63UIgef/Dmo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/core/-/core-1.10.2.tgz"
  "version" "1.10.2"

"@interactjs/dev-tools@1.10.2":
  "integrity" "sha1-zeQTq70R3ZHIdJkQXhYJHm6zW2c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/dev-tools/-/dev-tools-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "@interactjs/utils" "1.10.2"
  optionalDependencies:
    "@interactjs/interact" "1.10.2"

"@interactjs/feedback@1.10.2":
  "integrity" "sha1-2W3B0xl8825gEEeSU8HPnbiVLNs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/feedback/-/feedback-1.10.2.tgz"
  "version" "1.10.2"

"@interactjs/inertia@1.10.2":
  "integrity" "sha1-ykj+6tbr/KHKRD9xqAvAzfr4qgU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/inertia/-/inertia-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "@interactjs/offset" "1.10.2"
  optionalDependencies:
    "@interactjs/interact" "1.10.2"

"@interactjs/interact@1.10.2":
  "integrity" "sha1-BQvM0J989c7VMu+RxUkucuEw/aM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/interact/-/interact-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "@interactjs/core" "1.10.2"
    "@interactjs/types" "1.10.2"
    "@interactjs/utils" "1.10.2"

"@interactjs/interactjs@1.10.2":
  "integrity" "sha1-r5bkf8Y3ypbZR3/o19GFjMrvKPg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/interactjs/-/interactjs-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "@interactjs/actions" "1.10.2"
    "@interactjs/arrange" "1.10.2"
    "@interactjs/auto-scroll" "1.10.2"
    "@interactjs/auto-start" "1.10.2"
    "@interactjs/clone" "1.10.2"
    "@interactjs/core" "1.10.2"
    "@interactjs/dev-tools" "1.10.2"
    "@interactjs/feedback" "1.10.2"
    "@interactjs/inertia" "1.10.2"
    "@interactjs/interact" "1.10.2"
    "@interactjs/modifiers" "1.10.2"
    "@interactjs/multi-target" "1.10.2"
    "@interactjs/offset" "1.10.2"
    "@interactjs/pointer-events" "1.10.2"
    "@interactjs/react" "1.10.2"
    "@interactjs/reflow" "1.10.2"
    "@interactjs/utils" "1.10.2"
    "@interactjs/vue" "1.10.2"

"@interactjs/modifiers@1.10.2":
  "integrity" "sha1-/elB0fB9iLGcNR13yrPHZjUcj54="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/modifiers/-/modifiers-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "@interactjs/snappers" "1.10.2"
  optionalDependencies:
    "@interactjs/interact" "1.10.2"

"@interactjs/multi-target@1.10.2":
  "integrity" "sha1-m5vCZRSwQcrhkw17aUvku1sLbq0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/multi-target/-/multi-target-1.10.2.tgz"
  "version" "1.10.2"

"@interactjs/offset@1.10.2":
  "integrity" "sha1-v15PisF2xdJElprtkSyoOhhciPE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/offset/-/offset-1.10.2.tgz"
  "version" "1.10.2"
  optionalDependencies:
    "@interactjs/interact" "1.10.2"

"@interactjs/pointer-events@1.10.2":
  "integrity" "sha1-Ai/v+FHmhcBHhiBMHHOTYhB409U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/pointer-events/-/pointer-events-1.10.2.tgz"
  "version" "1.10.2"
  optionalDependencies:
    "@interactjs/interact" "1.10.2"

"@interactjs/react@1.10.2":
  "integrity" "sha1-Kj1CUC82pTG9DfxFZwENPsk2b8s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/react/-/react-1.10.2.tgz"
  "version" "1.10.2"

"@interactjs/reflow@1.10.2":
  "integrity" "sha1-e1tbN0Cq2RSaeS1vHtnXveJuO1s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/reflow/-/reflow-1.10.2.tgz"
  "version" "1.10.2"
  optionalDependencies:
    "@interactjs/interact" "1.10.2"

"@interactjs/snappers@1.10.2":
  "integrity" "sha1-w48a3iSfnKauLkCMa/ROemOa5bA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/snappers/-/snappers-1.10.2.tgz"
  "version" "1.10.2"

"@interactjs/types@1.10.2":
  "integrity" "sha1-RadNAZ+bPo/M08zCiLVbz1EBBrw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/types/-/types-1.10.2.tgz"
  "version" "1.10.2"

"@interactjs/utils@1.10.2":
  "integrity" "sha1-1o3UXJtBuqsRrPDulobeIH11S2M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/utils/-/utils-1.10.2.tgz"
  "version" "1.10.2"

"@interactjs/vue@1.10.2":
  "integrity" "sha1-SKzQiD/HnkLKQfqd+ZUkaK83svk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@interactjs/vue/-/vue-1.10.2.tgz"
  "version" "1.10.2"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  "integrity" "sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@intervolga/optimize-cssnano-plugin/-/optimize-cssnano-plugin-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "cssnano" "^4.0.0"
    "cssnano-preset-default" "^4.0.0"
    "postcss" "^7.0.0"

"@jridgewell/gen-mapping@^0.1.0":
  "integrity" "sha1-5dLkUDBqlJHjvXfjI+ONev8xWZY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/gen-mapping@^0.3.2":
  "integrity" "sha1-wa7cYehT8rufXf5tRELTtWWyU7k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@^3.1.0":
  "integrity" "sha1-IgOxGMFXchrd/mnUe3BGVGMGbXg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz"
  "version" "3.1.0"

"@jridgewell/set-array@^1.0.0", "@jridgewell/set-array@^1.0.1":
  "integrity" "sha1-fGz5mNbSC5FMClWpGuko/yWWXnI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@jridgewell/set-array/-/set-array-1.1.2.tgz"
  "version" "1.1.2"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  "integrity" "sha1-rdTJjTQUcqKJGQtCTvvbCWmRuyQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz"
  "version" "1.4.14"

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  "integrity" "sha1-+KMkmGL5G+SNMSfDz+mS95tLiBE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@jridgewell/trace-mapping/-/trace-mapping-0.3.19.tgz"
  "version" "0.3.19"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@mrmlnc/readdir-enhanced@^2.2.1":
  "integrity" "sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@mrmlnc/readdir-enhanced/-/readdir-enhanced-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "call-me-maybe" "^1.0.1"
    "glob-to-regexp" "^0.3.0"

"@node-ipc/js-queue@2.0.3":
  "integrity" "sha1-rH/jPXZvpT4jPvj+2vNEOgHFpM0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@node-ipc/js-queue/-/js-queue-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "easy-stack" "1.0.1"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^1.1.2":
  "integrity" "sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@nodelib/fs.stat/-/fs.stat-1.1.3.tgz"
  "version" "1.1.3"

"@nodelib/fs.stat@^2.0.2":
  "integrity" "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.stat@2.0.5":
  "integrity" "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@npmcli/fs@^1.0.0":
  "integrity" "sha1-cvcZ/pNeaHxWpPrs88A9BrpZMlc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@npmcli/fs/-/fs-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@gar/promisify" "^1.0.1"
    "semver" "^7.3.5"

"@npmcli/move-file@^1.0.1":
  "integrity" "sha1-GoLD43L3yuklPrZtclQ9a4aFxnQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@npmcli/move-file/-/move-file-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "mkdirp" "^1.0.4"
    "rimraf" "^3.0.2"

"@picovoice/porcupine-web@^2.1.15":
  "integrity" "sha1-0TAkBTCbgiIQXCFtw9/B7zRgFfA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@picovoice/porcupine-web/-/porcupine-web-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "@picovoice/web-utils" "=1.3.1"

"@picovoice/web-utils@=1.3.1":
  "integrity" "sha1-1BfphgSmULVKjgNmkBXs+Ywjg+w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@picovoice/web-utils/-/web-utils-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "commander" "^9.2.0"

"@picovoice/web-voice-processor@^4.0.5":
  "integrity" "sha1-lSR6U5PKxNFkkKU/6w9BPJAu5fo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@picovoice/web-voice-processor/-/web-voice-processor-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "@picovoice/web-utils" "=1.3.1"

"@riophae/vue-treeselect@^0.4.0":
  "integrity" "sha1-C67Vp5TP/FgLY1kfNcEl5RwN8kE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@riophae/vue-treeselect/-/vue-treeselect-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "@babel/runtime" "^7.3.1"
    "babel-helper-vue-jsx-merge-props" "^2.0.3"
    "easings-css" "^1.0.0"
    "fuzzysearch" "^1.0.3"
    "is-promise" "^2.1.0"
    "lodash" "^4.0.0"
    "material-colors" "^1.2.6"
    "watch-size" "^2.0.0"

"@samverschueren/stream-to-observable@^0.3.0":
  "integrity" "sha1-ohEXsZ7pvnDDeewYd1N+8uHGMwE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@samverschueren/stream-to-observable/-/stream-to-observable-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "any-observable" "^0.3.0"

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  "integrity" "sha1-TU+7EQiZOqo2IRYkfD0YGIosbIU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@soda/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "chalk" "^3.0.0"
    "error-stack-parser" "^2.0.6"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"

"@soda/get-current-script@^1.0.0":
  "integrity" "sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@soda/get-current-script/-/get-current-script-1.0.2.tgz"
  "version" "1.0.2"

"@sphinxxxx/color-conversion@^2.2.2":
  "integrity" "sha1-A+zCknnjwMgy9hhaW/o0l4WKyMo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@sphinxxxx/color-conversion/-/color-conversion-2.2.2.tgz"
  "version" "2.2.2"

"@tensorflow-models/speech-commands@^0.5.4":
  "integrity" "sha1-0QZcItKoOh5kJHMY7y+D+cTIGCs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@tensorflow-models/speech-commands/-/speech-commands-0.5.4.tgz"
  "version" "0.5.4"

"@tensorflow/tfjs-backend-cpu@3.3.0":
  "integrity" "sha1-qgo+0sYjem4MFpZ4xb1LWoh2axw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@tensorflow/tfjs-backend-cpu/-/tfjs-backend-cpu-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "@types/seedrandom" "2.4.27"
    "seedrandom" "2.4.3"

"@tensorflow/tfjs-backend-webgl@3.3.0":
  "integrity" "sha1-Kd1mX2qFbJ3vy5EIFk+EXh/c0C4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@tensorflow/tfjs-backend-webgl/-/tfjs-backend-webgl-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "@tensorflow/tfjs-backend-cpu" "3.3.0"
    "@types/offscreencanvas" "~2019.3.0"
    "@types/seedrandom" "2.4.27"
    "@types/webgl-ext" "0.0.30"
    "@types/webgl2" "0.0.5"
    "seedrandom" "2.4.3"

"@tensorflow/tfjs-converter@3.3.0":
  "integrity" "sha1-2fL/0PvbtHwH1f18Pl3BgM/zF6o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@tensorflow/tfjs-converter/-/tfjs-converter-3.3.0.tgz"
  "version" "3.3.0"

"@tensorflow/tfjs-core@^3.0.0", "@tensorflow/tfjs-core@3.3.0":
  "integrity" "sha1-PSa9A8tY4Oz0bJbRGMOcSpC39e0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@tensorflow/tfjs-core/-/tfjs-core-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "@types/offscreencanvas" "~2019.3.0"
    "@types/seedrandom" "2.4.27"
    "@types/webgl-ext" "0.0.30"
    "node-fetch" "~2.6.1"
    "seedrandom" "2.4.3"

"@tensorflow/tfjs-data@^3.0.0", "@tensorflow/tfjs-data@3.3.0":
  "integrity" "sha1-upQ71qSG+kyzyjEsEmRupNz2zOQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@tensorflow/tfjs-data/-/tfjs-data-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "@types/node-fetch" "^2.1.2"
    "node-fetch" "~2.6.1"

"@tensorflow/tfjs-layers@^3.0.0", "@tensorflow/tfjs-layers@3.3.0":
  "integrity" "sha1-0gl8WyLsEuX9vkcKiMoKNKlcoR8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@tensorflow/tfjs-layers/-/tfjs-layers-3.3.0.tgz"
  "version" "3.3.0"

"@tensorflow/tfjs@3.3.0":
  "integrity" "sha1-25IJndSMDrHBZz9wUSXStXSWoaM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@tensorflow/tfjs/-/tfjs-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "@tensorflow/tfjs-backend-cpu" "3.3.0"
    "@tensorflow/tfjs-backend-webgl" "3.3.0"
    "@tensorflow/tfjs-converter" "3.3.0"
    "@tensorflow/tfjs-core" "3.3.0"
    "@tensorflow/tfjs-data" "3.3.0"
    "@tensorflow/tfjs-layers" "3.3.0"
    "argparse" "^1.0.10"
    "chalk" "^4.1.0"
    "core-js" "3"
    "regenerator-runtime" "^0.13.5"
    "yargs" "^16.0.3"

"@tinymce/tinymce-vue@^3.2.8":
  "integrity" "sha1-AUVxtS7I+oNmWn+oh79lFAIH3nE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@tinymce/tinymce-vue/-/tinymce-vue-3.2.8.tgz"
  "version" "3.2.8"

"@types/body-parser@*":
  "integrity" "sha1-rqIFnii3ZYY5CBNHrE+rPeFm5vA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/body-parser/-/body-parser-1.19.2.tgz"
  "version" "1.19.2"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/connect-history-api-fallback@*":
  "integrity" "sha1-0feooJ0O1aV67lrpwYq5uAMgXa4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  "integrity" "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/connect/-/connect-3.4.35.tgz"
  "version" "3.4.35"
  dependencies:
    "@types/node" "*"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
  "integrity" "sha1-oROe/qtOcyODS7AibmKsAZ9HSy8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/express-serve-static-core/-/express-serve-static-core-4.17.31.tgz"
  "version" "4.17.31"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  "integrity" "sha1-FD6gVXJJvBs7VPFdtMgcPU6zVpw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/express/-/express-4.17.14.tgz"
  "version" "4.17.14"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/glob@^7.1.1":
  "integrity" "sha1-vBtb86qS8lvV3TnzXFc2G9zlsus="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/glob/-/glob-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/http-proxy@^1.17.5":
  "integrity" "sha1-fw55MTQ3Ye/eHiv0jEDwLz91cFo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/http-proxy/-/http-proxy-1.17.9.tgz"
  "version" "1.17.9"
  dependencies:
    "@types/node" "*"

"@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8":
  "integrity" "sha1-1CG2xSejA398hEM/0sQingFoY9M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/json-schema/-/json-schema-7.0.11.tgz"
  "version" "7.0.11"

"@types/json5@^0.0.29":
  "integrity" "sha1-7ihweulOEdK4J7y+UnC86n8+ce4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/json5/-/json5-0.0.29.tgz"
  "version" "0.0.29"

"@types/mime@*":
  "integrity" "sha1-X48rygpYY8tpvAsKzYjJbLHUrhA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/mime/-/mime-3.0.1.tgz"
  "version" "3.0.1"

"@types/minimatch@*":
  "integrity" "sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/minimatch/-/minimatch-5.1.2.tgz"
  "version" "5.1.2"

"@types/minimist@^1.2.0":
  "integrity" "sha1-7nceK6Sz3Fs3KTXVSf2WF780W4w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/minimist/-/minimist-1.2.2.tgz"
  "version" "1.2.2"

"@types/node-fetch@^2.1.2":
  "integrity" "sha1-lydWqaD+NUsohr897+Zn3bTw0wo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/node-fetch/-/node-fetch-2.6.5.tgz"
  "version" "2.6.5"
  dependencies:
    "@types/node" "*"
    "form-data" "^4.0.0"

"@types/node@*":
  "integrity" "sha1-YzGE9VwyLk+whhIwfCdO5tXtMVQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/node/-/node-18.7.18.tgz"
  "version" "18.7.18"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz"
  "version" "2.4.1"

"@types/offscreencanvas@~2019.3.0":
  "integrity" "sha1-MzZCjsfpGAz0Vm3+pdoE61hqZVM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/offscreencanvas/-/offscreencanvas-2019.3.0.tgz"
  "version" "2019.3.0"

"@types/q@^1.5.1":
  "integrity" "sha1-daKo59irSyMEFFBdkjNdHctTpt8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/q/-/q-1.5.5.tgz"
  "version" "1.5.5"

"@types/qs@*":
  "integrity" "sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/qs/-/qs-6.9.7.tgz"
  "version" "6.9.7"

"@types/range-parser@*":
  "integrity" "sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/range-parser/-/range-parser-1.2.4.tgz"
  "version" "1.2.4"

"@types/seedrandom@2.4.27":
  "integrity" "sha1-nbVjk33YaRX2kJK8QyWdL0hXjkE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/seedrandom/-/seedrandom-2.4.27.tgz"
  "version" "2.4.27"

"@types/serve-static@*":
  "integrity" "sha1-x5MP9hr7M04SGp2ngKrA2bjzQVU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/serve-static/-/serve-static-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "@types/mime" "*"
    "@types/node" "*"

"@types/source-list-map@*":
  "integrity" "sha1-AHiDYGP/rxdBI0m7o2QIfgrALsk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/source-list-map/-/source-list-map-0.1.2.tgz"
  "version" "0.1.2"

"@types/tapable@^1":
  "integrity" "sha1-uUpDkchWZse3Mpn9OtedT6pDUxA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/tapable/-/tapable-1.0.8.tgz"
  "version" "1.0.8"

"@types/uglify-js@*":
  "integrity" "sha1-lSceer4L9wlMYChPdu5DIyrvQ7k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/uglify-js/-/uglify-js-3.17.0.tgz"
  "version" "3.17.0"
  dependencies:
    "source-map" "^0.6.1"

"@types/webgl-ext@0.0.30":
  "integrity" "sha1-DOSYwWpBoj0VKJ4LhE2UWyXw+50="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/webgl-ext/-/webgl-ext-0.0.30.tgz"
  "version" "0.0.30"

"@types/webgl2@0.0.5":
  "integrity" "sha1-3ZJeIKuKzoDrSx5G/aWxCcUI+w0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/webgl2/-/webgl2-0.0.5.tgz"
  "version" "0.0.5"

"@types/webpack-dev-server@^3.11.0":
  "integrity" "sha1-2IiM/S8GMCA+E9PteDOk0RuKNNw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/webpack-dev-server/-/webpack-dev-server-3.11.6.tgz"
  "version" "3.11.6"
  dependencies:
    "@types/connect-history-api-fallback" "*"
    "@types/express" "*"
    "@types/serve-static" "*"
    "@types/webpack" "^4"
    "http-proxy-middleware" "^1.0.0"

"@types/webpack-sources@*":
  "integrity" "sha1-FtdZuglsKJA0smVT0t8b9FJI04s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/webpack-sources/-/webpack-sources-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    "source-map" "^0.7.3"

"@types/webpack@^4", "@types/webpack@^4.0.0":
  "integrity" "sha1-p7qwO3KQQHAWKy8WlBVJIgnpQhI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@types/webpack/-/webpack-4.41.32.tgz"
  "version" "4.41.32"
  dependencies:
    "@types/node" "*"
    "@types/tapable" "^1"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    "anymatch" "^3.0.0"
    "source-map" "^0.6.0"

"@vant/icons@^1.7.1":
  "integrity" "sha1-NrE/LmKLUz9lI6k6FozwLwcFZnQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vant/icons/-/icons-1.8.0.tgz"
  "version" "1.8.0"

"@vant/popperjs@^1.1.0":
  "integrity" "sha1-4O/wFxJLWyNS7zs2pt8GJ39EAPI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vant/popperjs/-/popperjs-1.3.0.tgz"
  "version" "1.3.0"

"@vue/babel-helper-vue-jsx-merge-props@^1.0.0", "@vue/babel-helper-vue-jsx-merge-props@^1.4.0":
  "integrity" "sha1-jVOh4hNH247b5U0zmQJYMXbeCfI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz"
  "version" "1.4.0"

"@vue/babel-helper-vue-transform-on@^1.0.2":
  "integrity" "sha1-m5xpHNBvyFUiGiR1w8yDHXdLx9w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.0.2.tgz"
  "version" "1.0.2"

"@vue/babel-plugin-jsx@^1.0.3":
  "integrity" "sha1-DFusJ4gNI/iYlM0Daje1XvYd38E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "@vue/babel-helper-vue-transform-on" "^1.0.2"
    "camelcase" "^6.0.0"
    "html-tags" "^3.1.0"
    "svg-tags" "^1.0.0"

"@vue/babel-plugin-transform-vue-jsx@^1.4.0":
  "integrity" "sha1-TUs9RqOepit0Z91uJs5H986vsv4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "html-tags" "^2.0.0"
    "lodash.kebabcase" "^4.1.1"
    "svg-tags" "^1.0.0"

"@vue/babel-preset-app@^4.5.19":
  "integrity" "sha1-uu5FfaAGXAFvdPrEFJ98l2Mbpac="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-preset-app/-/babel-preset-app-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@babel/core" "^7.11.0"
    "@babel/helper-compilation-targets" "^7.9.6"
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/plugin-proposal-class-properties" "^7.8.3"
    "@babel/plugin-proposal-decorators" "^7.8.3"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.8.3"
    "@babel/plugin-transform-runtime" "^7.11.0"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.0"
    "@vue/babel-plugin-jsx" "^1.0.3"
    "@vue/babel-preset-jsx" "^1.2.4"
    "babel-plugin-dynamic-import-node" "^2.3.3"
    "core-js" "^3.6.5"
    "core-js-compat" "^3.6.5"
    "semver" "^6.1.0"

"@vue/babel-preset-jsx@^1.2.4", "@vue/babel-preset-jsx@^1.4.0":
  "integrity" "sha1-9JFLoxQjWrCXvENy7WdHPAeAv8w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-preset-jsx/-/babel-preset-jsx-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "@vue/babel-sugar-composition-api-inject-h" "^1.4.0"
    "@vue/babel-sugar-composition-api-render-instance" "^1.4.0"
    "@vue/babel-sugar-functional-vue" "^1.4.0"
    "@vue/babel-sugar-inject-h" "^1.4.0"
    "@vue/babel-sugar-v-model" "^1.4.0"
    "@vue/babel-sugar-v-on" "^1.4.0"

"@vue/babel-sugar-composition-api-inject-h@^1.4.0":
  "integrity" "sha1-GH4TifiHHYns50O7UK7XE76dbIU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-composition-api-inject-h/-/babel-sugar-composition-api-inject-h-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.4.0":
  "integrity" "sha1-LBYHrm3/2rR+eFvAH6Rbp1bpksE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-composition-api-render-instance/-/babel-sugar-composition-api-render-instance-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.4.0":
  "integrity" "sha1-YNoxBoVnCCKHxzN8Zu9N8E4KECk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.4.0":
  "integrity" "sha1-vzmqZjH7HQOZscSbTFnhyImbQ2M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.4.0":
  "integrity" "sha1-pR2YZgn0MMT3Cto6k8xWCilw9yA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "camelcase" "^5.0.0"
    "html-tags" "^2.0.0"
    "svg-tags" "^1.0.0"

"@vue/babel-sugar-v-on@^1.4.0":
  "integrity" "sha1-Q7cQapZy2Mvu/A64r+HTdu3GFm4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "camelcase" "^5.0.0"

"@vue/cli-overlay@^4.5.19":
  "integrity" "sha1-0SBveAK8uh2cMHaVtUCR35ltuAQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/cli-overlay/-/cli-overlay-4.5.19.tgz"
  "version" "4.5.19"

"@vue/cli-plugin-babel@~4.5.0":
  "integrity" "sha1-KIsy5p8BkadzaeiPBxwM2ANu36c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/cli-plugin-babel/-/cli-plugin-babel-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@babel/core" "^7.11.0"
    "@vue/babel-preset-app" "^4.5.19"
    "@vue/cli-shared-utils" "^4.5.19"
    "babel-loader" "^8.1.0"
    "cache-loader" "^4.1.0"
    "thread-loader" "^2.1.3"
    "webpack" "^4.0.0"

"@vue/cli-plugin-eslint@~4.5.0":
  "integrity" "sha1-0fkItdB58pAtwjMBKQ5N2BdvIEw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@vue/cli-shared-utils" "^4.5.19"
    "eslint-loader" "^2.2.1"
    "globby" "^9.2.0"
    "inquirer" "^7.1.0"
    "webpack" "^4.0.0"
    "yorkie" "^2.0.0"

"@vue/cli-plugin-router@^4.5.19", "@vue/cli-plugin-router@~4.5.0":
  "integrity" "sha1-p/7qcCS4Ogr3f8lA0WN9POL5Lh8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/cli-plugin-router/-/cli-plugin-router-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@vue/cli-shared-utils" "^4.5.19"

"@vue/cli-plugin-vuex@^4.5.19", "@vue/cli-plugin-vuex@~4.5.0":
  "integrity" "sha1-JFLeWOtm7Yc4Ur6kXm4GtX2EK0c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/cli-plugin-vuex/-/cli-plugin-vuex-4.5.19.tgz"
  "version" "4.5.19"

"@vue/cli-service@^3.0.0 || ^4.0.0-0", "@vue/cli-service@~4.5.0":
  "integrity" "sha1-X2UTEo9Ca+Dumn0DFVwjpvI/jUI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/cli-service/-/cli-service-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@soda/get-current-script" "^1.0.0"
    "@types/minimist" "^1.2.0"
    "@types/webpack" "^4.0.0"
    "@types/webpack-dev-server" "^3.11.0"
    "@vue/cli-overlay" "^4.5.19"
    "@vue/cli-plugin-router" "^4.5.19"
    "@vue/cli-plugin-vuex" "^4.5.19"
    "@vue/cli-shared-utils" "^4.5.19"
    "@vue/component-compiler-utils" "^3.1.2"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    "acorn" "^7.4.0"
    "acorn-walk" "^7.1.1"
    "address" "^1.1.2"
    "autoprefixer" "^9.8.6"
    "browserslist" "^4.12.0"
    "cache-loader" "^4.1.0"
    "case-sensitive-paths-webpack-plugin" "^2.3.0"
    "cli-highlight" "^2.1.4"
    "clipboardy" "^2.3.0"
    "cliui" "^6.0.0"
    "copy-webpack-plugin" "^5.1.1"
    "css-loader" "^3.5.3"
    "cssnano" "^4.1.10"
    "debug" "^4.1.1"
    "default-gateway" "^5.0.5"
    "dotenv" "^8.2.0"
    "dotenv-expand" "^5.1.0"
    "file-loader" "^4.2.0"
    "fs-extra" "^7.0.1"
    "globby" "^9.2.0"
    "hash-sum" "^2.0.0"
    "html-webpack-plugin" "^3.2.0"
    "launch-editor-middleware" "^2.2.1"
    "lodash.defaultsdeep" "^4.6.1"
    "lodash.mapvalues" "^4.6.0"
    "lodash.transform" "^4.6.0"
    "mini-css-extract-plugin" "^0.9.0"
    "minimist" "^1.2.5"
    "pnp-webpack-plugin" "^1.6.4"
    "portfinder" "^1.0.26"
    "postcss-loader" "^3.0.0"
    "ssri" "^8.0.1"
    "terser-webpack-plugin" "^1.4.4"
    "thread-loader" "^2.1.3"
    "url-loader" "^2.2.0"
    "vue-loader" "^15.9.2"
    "vue-style-loader" "^4.1.2"
    "webpack" "^4.0.0"
    "webpack-bundle-analyzer" "^3.8.0"
    "webpack-chain" "^6.4.0"
    "webpack-dev-server" "^3.11.0"
    "webpack-merge" "^4.2.2"
  optionalDependencies:
    "vue-loader-v16" "npm:vue-loader@^16.1.0"

"@vue/cli-shared-utils@^4.5.19":
  "integrity" "sha1-zDibHeGwUHOATA/ptLCDuSjvYTA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/cli-shared-utils/-/cli-shared-utils-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@achrinza/node-ipc" "9.2.2"
    "@hapi/joi" "^15.0.1"
    "chalk" "^2.4.2"
    "execa" "^1.0.0"
    "launch-editor" "^2.2.1"
    "lru-cache" "^5.1.1"
    "open" "^6.3.0"
    "ora" "^3.4.0"
    "read-pkg" "^5.1.1"
    "request" "^2.88.2"
    "semver" "^6.1.0"
    "strip-ansi" "^6.0.0"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.1.2":
  "integrity" "sha1-+fX7U0ZLDDeyyNLz+/5E32D2Hck="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/component-compiler-utils/-/component-compiler-utils-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "consolidate" "^0.15.1"
    "hash-sum" "^1.0.2"
    "lru-cache" "^4.1.2"
    "merge-source-map" "^1.1.0"
    "postcss" "^7.0.36"
    "postcss-selector-parser" "^6.0.2"
    "source-map" "~0.6.1"
    "vue-template-es2015-compiler" "^1.9.0"
  optionalDependencies:
    "prettier" "^1.18.2 || ^2.0.0"

"@vue/eslint-config-standard@^5.1.2":
  "integrity" "sha1-xdVa+JSjriO2Wxr0pCV3esAXC0I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/eslint-config-standard/-/eslint-config-standard-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "eslint-config-standard" "^14.1.0"
    "eslint-import-resolver-node" "^0.3.3"
    "eslint-import-resolver-webpack" "^0.12.1"

"@vue/preload-webpack-plugin@^1.1.0":
  "integrity" "sha1-zrkktOyzucQ4ccekKaAvhCPmIas="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/preload-webpack-plugin/-/preload-webpack-plugin-1.1.2.tgz"
  "version" "1.1.2"

"@vue/web-component-wrapper@^1.2.0":
  "integrity" "sha1-trQKdiVCnSvXwigd26YB7QXcfxo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@vue/web-component-wrapper/-/web-component-wrapper-1.3.0.tgz"
  "version" "1.3.0"

"@webassemblyjs/ast@1.9.0":
  "integrity" "sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/ast/-/ast-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  "integrity" "sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-api-error@1.9.0":
  "integrity" "sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-buffer@1.9.0":
  "integrity" "sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/helper-buffer/-/helper-buffer-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-code-frame@1.9.0":
  "integrity" "sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  "integrity" "sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-module-context@1.9.0":
  "integrity" "sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/helper-module-context/-/helper-module-context-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  "integrity" "sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-wasm-section@1.9.0":
  "integrity" "sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  "integrity" "sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/ieee754/-/ieee754-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  "integrity" "sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/leb128/-/leb128-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  "integrity" "sha1-BNM7Y2945qaBMifoJAL3Y3tiKas="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/utf8/-/utf8-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/wasm-edit@1.9.0":
  "integrity" "sha1-P+bXnT8PkiGDqoYALELdJWz+6c8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  "integrity" "sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  "integrity" "sha1-IhEYHlsxMmRDzIES658LkChyGmE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  "integrity" "sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  "integrity" "sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/wast-parser/-/wast-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  "integrity" "sha1-STXVTIX+9jewDOn1I3dFHQDUeJk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/@xtuc/long/-/long-4.2.2.tgz"
  "version" "4.2.2"

"accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.8":
  "integrity" "sha1-C/C+EltnAUrcsLCSHmLbe//hay4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/accepts/-/accepts-1.3.8.tgz"
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"ace-builds@^1.10.1":
  "integrity" "sha1-Z89zF0iOjWAp/xZ1Sn6ycx+H6Oc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ace-builds/-/ace-builds-1.11.0.tgz"
  "version" "1.11.0"

"ace@^1.3.0":
  "integrity" "sha1-WDW3mcyhx5ZHCQ+8VIf5tUNMoGY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ace/-/ace-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "archy" "1.0.0"
    "async-retry" "0.1.1"
    "babel-runtime" "6.6.1"
    "debug" "2.2.0"
    "easy-table" "1.0.0"
    "es6-promisify" "4.0.0"
    "fs-promise" "0.5.0"
    "got" "5.3.1"
    "gunzip-maybe" "1.2.1"
    "https-proxy-agent" "1.0.0"
    "init-package-json" "1.9.1"
    "minimist" "1.2.0"
    "mkdirp-then" "1.2.0"
    "node-uuid" "1.4.3"
    "npm-package-arg" "4.1.0"
    "readdir-scoped-modules" "1.0.2"
    "semver" "5.0.1"
    "tar" "2.2.1"

"acorn-jsx@^5.2.0":
  "integrity" "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-walk@^7.1.1":
  "integrity" "sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/acorn-walk/-/acorn-walk-7.2.0.tgz"
  "version" "7.2.0"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^6.4.1":
  "integrity" "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/acorn/-/acorn-6.4.2.tgz"
  "version" "6.4.2"

"acorn@^7.1.1":
  "integrity" "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^7.4.0":
  "integrity" "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"address@^1.1.2":
  "integrity" "sha1-JbthCVt1ItZbNXuqEbwFSS1Mis0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/address/-/address-1.2.1.tgz"
  "version" "1.2.1"

"after@0.8.2":
  "integrity" "sha1-/ts5T58OAqqXaOcCvaI7UF+ufh8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/after/-/after-0.8.2.tgz"
  "version" "0.8.2"

"agent-base@2":
  "integrity" "sha1-1t4Q1a9hMtW9aSQn1G/FOFOQlMc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/agent-base/-/agent-base-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "extend" "~3.0.0"
    "semver" "~5.0.1"

"aggregate-error@^3.0.0":
  "integrity" "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/aggregate-error/-/aggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv-errors@^1.0.0":
  "integrity" "sha1-81mGrOuRr63sQQL72FAUlQzvpk0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ajv-errors/-/ajv-errors-1.0.1.tgz"
  "version" "1.0.1"

"ajv-keywords@^3.1.0", "ajv-keywords@^3.4.1", "ajv-keywords@^3.5.2":
  "integrity" "sha1-MfKdpatuANHC0yms97WSlhTVAU0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv@^6.1.0", "ajv@^6.10.0", "ajv@^6.10.2", "ajv@^6.12.3", "ajv@^6.12.4", "ajv@^6.12.5", "ajv@^6.12.6", "ajv@^6.9.1", "ajv@>=5.0.0":
  "integrity" "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"alphanum-sort@^1.0.0":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/alphanum-sort/-/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"animate.css@^4.1.1":
  "integrity" "sha1-YU7FqBEx1+TcNipYFD90BqvWgHU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/animate.css/-/animate.css-4.1.1.tgz"
  "version" "4.1.1"

"ansi-colors@^3.0.0":
  "integrity" "sha1-46PaS/uubIapwoViXeEkojQCb78="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-colors/-/ansi-colors-3.2.4.tgz"
  "version" "3.2.4"

"ansi-escapes@^3.0.0":
  "integrity" "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-escapes/-/ansi-escapes-3.2.0.tgz"
  "version" "3.2.0"

"ansi-escapes@^4.2.1":
  "integrity" "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-html-community@0.0.8":
  "integrity" "sha1-afvE1sy+OD+XNpNK40w/gpDxv0E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
  "version" "0.0.8"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-regex/-/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha1-Ej1keekq1FrYl9QFTjx8p9tJROE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-regex/-/ansi-regex-3.0.1.tgz"
  "version" "3.0.1"

"ansi-regex@^4.1.0":
  "integrity" "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-regex/-/ansi-regex-4.1.1.tgz"
  "version" "4.1.1"

"ansi-regex@^5.0.1":
  "integrity" "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.0":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^3.2.1":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha1-7dgDYornHATIWuegkG7a00tkiTc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@~1.0.0":
  "integrity" "sha1-yxAt8cVvUSPquLZ817mAJ6AnkXg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-1.0.0.tgz"
  "version" "1.0.0"

"any-observable@^0.3.0":
  "integrity" "sha1-r5M0deWAamfQ198JDdXovvZdEZs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/any-observable/-/any-observable-0.3.0.tgz"
  "version" "0.3.0"

"any-promise@^1.0.0", "any-promise@^1.1.0":
  "integrity" "sha1-q8av7tzqUugJzcA3au0845Y10X8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/any-promise/-/any-promise-1.3.0.tgz"
  "version" "1.3.0"

"anymatch@^2.0.0":
  "integrity" "sha1-vLJLTzeTTZqnrBe0ra+J58du8us="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/anymatch/-/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@^3.0.0", "anymatch@~3.1.2":
  "integrity" "sha1-wFV8CWrzLxBhmPT04qODU343hxY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/anymatch/-/anymatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"aproba@^1.1.1":
  "integrity" "sha1-aALmJk79GMeQobDVF/DyYnvyyUo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/aproba/-/aproba-1.2.0.tgz"
  "version" "1.2.0"

"arch@^2.1.1":
  "integrity" "sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/arch/-/arch-2.2.0.tgz"
  "version" "2.2.0"

"archy@1.0.0":
  "integrity" "sha1-+cjBN1fMHde8N5rHeyxipcKGjEA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/archy/-/archy-1.0.0.tgz"
  "version" "1.0.0"

"argparse@^1.0.10", "argparse@^1.0.7":
  "integrity" "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/arr-diff/-/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/arr-flatten/-/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/arr-union/-/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-find@^1.0.0":
  "integrity" "sha1-bI4obRHtdoMn+OYuzuhzU8o+eLg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array-find/-/array-find-1.0.0.tgz"
  "version" "1.0.0"

"array-flatten@^2.1.0":
  "integrity" "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array-flatten/-/array-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array-flatten/-/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-includes@^3.1.4":
  "integrity" "sha1-LDIAENuNMQMf0qX2s7vUsarTG9s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array-includes/-/array-includes-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.19.5"
    "get-intrinsic" "^1.1.1"
    "is-string" "^1.0.7"

"array-union@^1.0.1", "array-union@^1.0.2":
  "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array-union/-/array-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-union@^2.1.0":
  "integrity" "sha1-t5hCCtvrHego2ErNii4j0+/oXo0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array-union/-/array-union-2.1.0.tgz"
  "version" "2.1.0"

"array-uniq@^1.0.1":
  "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array-uniq/-/array-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array-unique/-/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"array.prototype.flat@^1.2.5":
  "integrity" "sha1-CwwVZ79Xs4tWtMl7iqcqtF5K3Hs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array.prototype.flat/-/array.prototype.flat-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.2"
    "es-shim-unscopables" "^1.0.0"

"array.prototype.reduce@^1.0.4":
  "integrity" "sha1-gWfoAIn3i/9wqZ4gvUIB1GY7Cm8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/array.prototype.reduce/-/array.prototype.reduce-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.2"
    "es-array-method-boxes-properly" "^1.0.0"
    "is-string" "^1.0.7"

"arraybuffer.slice@~0.0.7":
  "integrity" "sha1-O7xCdd1YTMGxCAm4nU6LY6aednU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/arraybuffer.slice/-/arraybuffer.slice-0.0.7.tgz"
  "version" "0.0.7"

"asap@^2.0.0":
  "integrity" "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/asap/-/asap-2.0.6.tgz"
  "version" "2.0.6"

"asn1.js@^5.2.0":
  "integrity" "sha1-EamAuE67kXgc41sP3C7ilON4Pwc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/asn1.js/-/asn1.js-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "safer-buffer" "^2.1.0"

"asn1@~0.2.3":
  "integrity" "sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/asn1/-/asn1-0.2.6.tgz"
  "version" "0.2.6"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/assert-plus/-/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assert@^1.1.1":
  "integrity" "sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/assert/-/assert-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "object-assign" "^4.1.1"
    "util" "0.10.3"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/assign-symbols/-/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^1.0.0":
  "integrity" "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/astral-regex/-/astral-regex-1.0.0.tgz"
  "version" "1.0.0"

"async-each@^1.0.1":
  "integrity" "sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/async-each/-/async-each-1.0.3.tgz"
  "version" "1.0.3"

"async-limiter@~1.0.0":
  "integrity" "sha1-3TeelPDbgxCwgpH51kwyCXZmF/0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/async-limiter/-/async-limiter-1.0.1.tgz"
  "version" "1.0.1"

"async-retry@0.1.1":
  "integrity" "sha1-ZBo8xuQOpuR+j/SLrZr68TdV+2A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/async-retry/-/async-retry-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "babel-runtime" "6.5.0"
    "retry" "0.9.0"

"async-validator@~1.8.1":
  "integrity" "sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/async-validator/-/async-validator-1.8.5.tgz"
  "version" "1.8.5"
  dependencies:
    "babel-runtime" "6.x"

"async@^2.6.4":
  "integrity" "sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/async/-/async-2.6.4.tgz"
  "version" "2.6.4"
  dependencies:
    "lodash" "^4.17.14"

"async@3.2.0":
  "integrity" "sha1-s6JoXF67ZB094C0WEALGD8n4VyA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/async/-/async-3.2.0.tgz"
  "version" "3.2.0"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^9.8.6":
  "integrity" "sha1-/UvUWVOF+m8GWZ3nSaTV96R0lXo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/autoprefixer/-/autoprefixer-9.8.8.tgz"
  "version" "9.8.8"
  dependencies:
    "browserslist" "^4.12.0"
    "caniuse-lite" "^1.0.30001109"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "picocolors" "^0.2.1"
    "postcss" "^7.0.32"
    "postcss-value-parser" "^4.1.0"

"aws-sign2@~0.7.0":
  "integrity" "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/aws-sign2/-/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.8.0":
  "integrity" "sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/aws4/-/aws4-1.11.0.tgz"
  "version" "1.11.0"

"axios@^0.21.0":
  "integrity" "sha1-xnuQ3AVo5cHPKwuFjEO6KOLtpXU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/axios/-/axios-0.21.4.tgz"
  "version" "0.21.4"
  dependencies:
    "follow-redirects" "^1.14.0"

"babel-code-frame@^6.26.0":
  "integrity" "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-code-frame/-/babel-code-frame-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "chalk" "^1.1.3"
    "esutils" "^2.0.2"
    "js-tokens" "^3.0.2"

"babel-eslint@^10.1.0":
  "integrity" "sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-eslint/-/babel-eslint-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    "eslint-visitor-keys" "^1.0.0"
    "resolve" "^1.12.0"

"babel-helper-call-delegate@^6.24.1":
  "integrity" "sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-helper-call-delegate/-/babel-helper-call-delegate-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-define-map@^6.24.1":
  "integrity" "sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-helper-define-map/-/babel-helper-define-map-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-function-name@^6.24.1":
  "integrity" "sha1-00dbjAPtmCQqJbSDUasYOZ01gKk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-helper-function-name/-/babel-helper-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-get-function-arity@^6.24.1":
  "integrity" "sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-hoist-variables@^6.24.1":
  "integrity" "sha1-HssnaJydJVE+rbyZFKc/VAi+enY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-helper-hoist-variables/-/babel-helper-hoist-variables-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-optimise-call-expression@^6.24.1":
  "integrity" "sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-helper-optimise-call-expression/-/babel-helper-optimise-call-expression-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-helper-regex@^6.24.1":
  "integrity" "sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-helper-regex/-/babel-helper-regex-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-helper-replace-supers@^6.24.1":
  "integrity" "sha1-v22/5Dk40XNpohPKiov3S2qQqxo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-helper-replace-supers/-/babel-helper-replace-supers-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-helper-vue-jsx-merge-props@^2.0.0", "babel-helper-vue-jsx-merge-props@^2.0.3":
  "integrity" "sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  "version" "2.0.3"

"babel-loader@^8.1.0", "babel-loader@^8.2.3":
  "integrity" "sha1-1F9YXmVNWl2Q9TUKd512R8XtUS4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-loader/-/babel-loader-8.2.5.tgz"
  "version" "8.2.5"
  dependencies:
    "find-cache-dir" "^3.3.1"
    "loader-utils" "^2.0.0"
    "make-dir" "^3.1.0"
    "schema-utils" "^2.6.5"

"babel-messages@^6.23.0":
  "integrity" "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-messages/-/babel-messages-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-check-es2015-constants@^6.22.0":
  "integrity" "sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-check-es2015-constants/-/babel-plugin-check-es2015-constants-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-component@^1.1.1":
  "integrity" "sha1-mwI6I/9cmq4P1WxaGLnKuMTUXuo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-component/-/babel-plugin-component-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@babel/helper-module-imports" "7.0.0-beta.35"

"babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-polyfill-corejs2@^0.3.3":
  "integrity" "sha1-XRvTg20KGeG4S78tlkDMtvlRwSI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/compat-data" "^7.17.7"
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.6.0":
  "integrity" "sha1-Vq2II3E36t5IWnG1L3Lb7VfGIwo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    "core-js-compat" "^3.25.1"

"babel-plugin-polyfill-regenerator@^0.4.1":
  "integrity" "sha1-OQ+Rw42QRzWS7UM1HoAanT4P10c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"

"babel-plugin-syntax-dynamic-import@^6.18.0":
  "integrity" "sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-syntax-dynamic-import/-/babel-plugin-syntax-dynamic-import-6.18.0.tgz"
  "version" "6.18.0"

"babel-plugin-transform-es2015-arrow-functions@^6.22.0":
  "integrity" "sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-arrow-functions/-/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoped-functions@^6.22.0":
  "integrity" "sha1-u8UbSflk1wy42OC5ToICRs46YUE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-block-scoped-functions/-/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-block-scoping@^6.24.1":
  "integrity" "sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-block-scoping/-/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "lodash" "^4.17.4"

"babel-plugin-transform-es2015-classes@^6.24.1":
  "integrity" "sha1-WkxYpQyclGHlZLSyo7+ryXolhNs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-define-map" "^6.24.1"
    "babel-helper-function-name" "^6.24.1"
    "babel-helper-optimise-call-expression" "^6.24.1"
    "babel-helper-replace-supers" "^6.24.1"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-computed-properties@^6.24.1":
  "integrity" "sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-computed-properties/-/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-destructuring@^6.22.0":
  "integrity" "sha1-mXux8auWf2gtKwh2/jWNYOdlxW0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-duplicate-keys@^6.24.1":
  "integrity" "sha1-c+s9MQypaePvnskcU3QabxV2Qj4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-duplicate-keys/-/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-for-of@^6.22.0":
  "integrity" "sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-for-of/-/babel-plugin-transform-es2015-for-of-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-function-name@^6.24.1":
  "integrity" "sha1-g0yJhTvDaxrw86TF26qU/Y6sqos="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-function-name/-/babel-plugin-transform-es2015-function-name-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-function-name" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-literals@^6.22.0":
  "integrity" "sha1-T1SgLWzWbPkVKAAZox0xklN3yi4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-literals/-/babel-plugin-transform-es2015-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-modules-amd@^6.24.1":
  "integrity" "sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-commonjs" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-commonjs@^6.24.1":
  "integrity" "sha1-WKeThjqefKhwvcWogRF/+sJ9tvM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  "version" "6.26.2"
  dependencies:
    "babel-plugin-transform-strict-mode" "^6.24.1"
    "babel-runtime" "^6.26.0"
    "babel-template" "^6.26.0"
    "babel-types" "^6.26.0"

"babel-plugin-transform-es2015-modules-systemjs@^6.24.1":
  "integrity" "sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-hoist-variables" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-modules-umd@^6.24.1":
  "integrity" "sha1-rJl+YoXNGO1hdq22B9YCNErThGg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-transform-es2015-modules-amd" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"

"babel-plugin-transform-es2015-object-super@^6.24.1":
  "integrity" "sha1-JM72muIcuDp/hgPa0CH1cusnj40="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-object-super/-/babel-plugin-transform-es2015-object-super-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-replace-supers" "^6.24.1"
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-parameters@^6.24.1":
  "integrity" "sha1-V6w1GrScrxSpfNE7CfZv3wpiXys="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-call-delegate" "^6.24.1"
    "babel-helper-get-function-arity" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-template" "^6.24.1"
    "babel-traverse" "^6.24.1"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-shorthand-properties@^6.24.1":
  "integrity" "sha1-JPh11nIch2YbvZmkYi5R8U3jiqA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-shorthand-properties/-/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-spread@^6.22.0":
  "integrity" "sha1-1taKmfia7cRTbIGlQujdnxdG+NE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-spread/-/babel-plugin-transform-es2015-spread-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-sticky-regex@^6.24.1":
  "integrity" "sha1-AMHNsaynERLN8M9hJsLta0V8zbw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-sticky-regex/-/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-plugin-transform-es2015-template-literals@^6.22.0":
  "integrity" "sha1-qEs0UPfp+PH2g51taH2oS7EjbY0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-template-literals/-/babel-plugin-transform-es2015-template-literals-6.22.0.tgz"
  "version" "6.22.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-typeof-symbol@^6.22.0":
  "integrity" "sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-typeof-symbol/-/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz"
  "version" "6.23.0"
  dependencies:
    "babel-runtime" "^6.22.0"

"babel-plugin-transform-es2015-unicode-regex@^6.24.1":
  "integrity" "sha1-04sS9C6nMj9yk4fxinxa4frrNek="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-es2015-unicode-regex/-/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-helper-regex" "^6.24.1"
    "babel-runtime" "^6.22.0"
    "regexpu-core" "^2.0.0"

"babel-plugin-transform-regenerator@^6.24.1":
  "integrity" "sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-regenerator/-/babel-plugin-transform-regenerator-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "regenerator-transform" "^0.10.0"

"babel-plugin-transform-strict-mode@^6.24.1":
  "integrity" "sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-plugin-transform-strict-mode/-/babel-plugin-transform-strict-mode-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-runtime" "^6.22.0"
    "babel-types" "^6.24.1"

"babel-polyfill@^6.26.0":
  "integrity" "sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-polyfill/-/babel-polyfill-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "core-js" "^2.5.0"
    "regenerator-runtime" "^0.10.5"

"babel-preset-es2015@^6.24.1":
  "integrity" "sha1-1EBQ1rwsn+6nAqrzjXJ6AhBTiTk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-preset-es2015/-/babel-preset-es2015-6.24.1.tgz"
  "version" "6.24.1"
  dependencies:
    "babel-plugin-check-es2015-constants" "^6.22.0"
    "babel-plugin-transform-es2015-arrow-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoped-functions" "^6.22.0"
    "babel-plugin-transform-es2015-block-scoping" "^6.24.1"
    "babel-plugin-transform-es2015-classes" "^6.24.1"
    "babel-plugin-transform-es2015-computed-properties" "^6.24.1"
    "babel-plugin-transform-es2015-destructuring" "^6.22.0"
    "babel-plugin-transform-es2015-duplicate-keys" "^6.24.1"
    "babel-plugin-transform-es2015-for-of" "^6.22.0"
    "babel-plugin-transform-es2015-function-name" "^6.24.1"
    "babel-plugin-transform-es2015-literals" "^6.22.0"
    "babel-plugin-transform-es2015-modules-amd" "^6.24.1"
    "babel-plugin-transform-es2015-modules-commonjs" "^6.24.1"
    "babel-plugin-transform-es2015-modules-systemjs" "^6.24.1"
    "babel-plugin-transform-es2015-modules-umd" "^6.24.1"
    "babel-plugin-transform-es2015-object-super" "^6.24.1"
    "babel-plugin-transform-es2015-parameters" "^6.24.1"
    "babel-plugin-transform-es2015-shorthand-properties" "^6.24.1"
    "babel-plugin-transform-es2015-spread" "^6.22.0"
    "babel-plugin-transform-es2015-sticky-regex" "^6.24.1"
    "babel-plugin-transform-es2015-template-literals" "^6.22.0"
    "babel-plugin-transform-es2015-typeof-symbol" "^6.22.0"
    "babel-plugin-transform-es2015-unicode-regex" "^6.24.1"
    "babel-plugin-transform-regenerator" "^6.24.1"

"babel-runtime@^6.18.0":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"babel-runtime@^6.22.0":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"babel-runtime@^6.26.0":
  "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"babel-runtime@6.5.0":
  "integrity" "sha1-+3wYiQab2fggAjipvGrVteirW/s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-runtime/-/babel-runtime-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "core-js" "^1.2.0"

"babel-runtime@6.6.1", "babel-runtime@6.x":
  "integrity" "sha1-eIuUtvY04luRvWxd9y1GdFevsAA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-runtime/-/babel-runtime-6.6.1.tgz"
  "version" "6.6.1"
  dependencies:
    "core-js" "^2.1.0"

"babel-template@^6.24.1", "babel-template@^6.26.0":
  "integrity" "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-template/-/babel-template-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "babel-traverse" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "lodash" "^4.17.4"

"babel-traverse@^6.24.1", "babel-traverse@^6.26.0":
  "integrity" "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-traverse/-/babel-traverse-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-code-frame" "^6.26.0"
    "babel-messages" "^6.23.0"
    "babel-runtime" "^6.26.0"
    "babel-types" "^6.26.0"
    "babylon" "^6.18.0"
    "debug" "^2.6.8"
    "globals" "^9.18.0"
    "invariant" "^2.2.2"
    "lodash" "^4.17.4"

"babel-types@^6.19.0", "babel-types@^6.24.1", "babel-types@^6.26.0":
  "integrity" "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babel-types/-/babel-types-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "babel-runtime" "^6.26.0"
    "esutils" "^2.0.2"
    "lodash" "^4.17.4"
    "to-fast-properties" "^1.0.3"

"babylon@^6.18.0":
  "integrity" "sha1-ry87iPpvXB5MY00aD46sT1WzleM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/babylon/-/babylon-6.18.0.tgz"
  "version" "6.18.0"

"backo2@1.0.2":
  "integrity" "sha1-MasayLEpNjRj41s+u2n038+6eUc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/backo2/-/backo2-1.0.2.tgz"
  "version" "1.0.2"

"balanced-match@^1.0.0":
  "integrity" "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha1-e95c7RRbbVUakNuH+DxVi060io8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/base/-/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-arraybuffer@^1.0.2":
  "integrity" "sha1-HDdYmnxLB0bjS9H+uVHaLfAcG9w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  "version" "1.0.2"

"base64-arraybuffer@0.1.4":
  "integrity" "sha1-mBjHngWbE1X5fgQooBfIOOkLqBI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/base64-arraybuffer/-/base64-arraybuffer-0.1.4.tgz"
  "version" "0.1.4"

"base64-js@^1.0.2":
  "integrity" "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/base64-js/-/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"batch-processor@1.0.0":
  "integrity" "sha1-dclcMrdI4IUNEMKxaPa9vpiRrOg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/batch-processor/-/batch-processor-1.0.0.tgz"
  "version" "1.0.0"

"batch@0.6.1":
  "integrity" "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/batch/-/batch-0.6.1.tgz"
  "version" "0.6.1"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"bfj@^6.1.1":
  "integrity" "sha1-MlyGGoIryzWKQceKM7jm4ght3n8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bfj/-/bfj-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "bluebird" "^3.5.5"
    "check-types" "^8.0.3"
    "hoopy" "^0.1.4"
    "tryer" "^1.0.1"

"big.js@^3.1.3":
  "integrity" "sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/big.js/-/big.js-3.2.0.tgz"
  "version" "3.2.0"

"big.js@^5.2.2":
  "integrity" "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^1.0.0":
  "integrity" "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/binary-extensions/-/binary-extensions-1.13.1.tgz"
  "version" "1.13.1"

"binary-extensions@^2.0.0":
  "integrity" "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/binary-extensions/-/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"bindings@^1.5.0":
  "integrity" "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bindings/-/bindings-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "file-uri-to-path" "1.0.0"

"blob@0.0.5":
  "integrity" "sha1-1oDu7yX4zZGtUz9bAe7UjmTK9oM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/blob/-/blob-0.0.5.tgz"
  "version" "0.0.5"

"block-stream@*":
  "integrity" "sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/block-stream/-/block-stream-0.0.9.tgz"
  "version" "0.0.9"
  dependencies:
    "inherits" "~2.0.0"

"bluebird@^3.1.1", "bluebird@^3.5.0", "bluebird@^3.5.5":
  "integrity" "sha1-nyKcFb4nJFT/qXOs4NvueaGww28="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bluebird/-/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"bn.js@^4.0.0":
  "integrity" "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.1.0":
  "integrity" "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.11.9":
  "integrity" "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^5.0.0", "bn.js@^5.1.1":
  "integrity" "sha1-C8UnpqDRjQqo1bBTjOSnfcz6e3A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bn.js/-/bn.js-5.2.1.tgz"
  "version" "5.2.1"

"body-parser@1.20.0":
  "integrity" "sha1-Peab2JARwRVz17/uamTxG2vSfMU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/body-parser/-/body-parser-1.20.0.tgz"
  "version" "1.20.0"
  dependencies:
    "bytes" "3.1.2"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "on-finished" "2.4.1"
    "qs" "6.10.3"
    "raw-body" "2.5.1"
    "type-is" "~1.6.18"
    "unpipe" "1.0.0"

"bonjour@^3.5.0":
  "integrity" "sha1-jokKGD2O6aI5OzhExpGkK897yfU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bonjour/-/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "^2.1.0"
    "deep-equal" "^1.0.1"
    "dns-equal" "^1.0.0"
    "dns-txt" "^2.0.2"
    "multicast-dns" "^6.0.1"
    "multicast-dns-service-types" "^1.1.0"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"brace@^0.11.1":
  "integrity" "sha1-SJb8ydVE7vRfS7dmDbMg07N5/lg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/brace/-/brace-0.11.1.tgz"
  "version" "0.11.1"

"braces@^2.2.2", "braces@^2.3.1", "braces@^2.3.2":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/braces/-/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^3.0.2":
  "integrity" "sha1-NFThpGLujVmeI23zNs2epPiv4Qc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"braces@~3.0.2":
  "integrity" "sha1-NFThpGLujVmeI23zNs2epPiv4Qc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"brorand@^1.0.1", "brorand@^1.1.0":
  "integrity" "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/brorand/-/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/browserify-aes/-/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/browserify-cipher/-/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/browserify-des/-/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0", "browserify-rsa@^4.0.1":
  "integrity" "sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/browserify-rsa/-/browserify-rsa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "bn.js" "^5.0.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/browserify-sign/-/browserify-sign-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "bn.js" "^5.1.1"
    "browserify-rsa" "^4.0.1"
    "create-hash" "^1.2.0"
    "create-hmac" "^1.1.7"
    "elliptic" "^6.5.3"
    "inherits" "^2.0.4"
    "parse-asn1" "^5.1.5"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"browserify-zlib@^0.1.4":
  "integrity" "sha1-uzX4pRn2AOD6a4SFJByXnQFB+y0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/browserify-zlib/-/browserify-zlib-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "pako" "~0.2.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^4.0.0", "browserslist@^4.12.0", "browserslist@^4.21.3", "browserslist@^4.21.4", "browserslist@>= 4.21.0":
  "integrity" "sha1-50lrvGe5453Q+YVl/szcsNT/aYc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/browserslist/-/browserslist-4.21.4.tgz"
  "version" "4.21.4"
  dependencies:
    "caniuse-lite" "^1.0.30001400"
    "electron-to-chromium" "^1.4.251"
    "node-releases" "^2.0.6"
    "update-browserslist-db" "^1.0.9"

"buffer-from@^1.0.0":
  "integrity" "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"buffer-indexof@^1.0.0":
  "integrity" "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/buffer-indexof/-/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"buffer-json@^2.0.0":
  "integrity" "sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/buffer-json/-/buffer-json-2.0.0.tgz"
  "version" "2.0.0"

"buffer-xor@^1.0.3":
  "integrity" "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/buffer-xor/-/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/buffer/-/buffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"builtin-status-codes@^3.0.0":
  "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"builtins@0.0.7":
  "integrity" "sha1-NVIZzWzxjb58Acx/0tznZc/cVJo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/builtins/-/builtins-0.0.7.tgz"
  "version" "0.0.7"

"bumblebee-hotword@^0.2.1":
  "integrity" "sha1-JfasJnRdA5zsJRMtNYVLXfT93jo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bumblebee-hotword/-/bumblebee-hotword-0.2.1.tgz"
  "version" "0.2.1"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bytes/-/bytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.2":
  "integrity" "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/bytes/-/bytes-3.1.2.tgz"
  "version" "3.1.2"

"cacache@^12.0.2", "cacache@^12.0.3":
  "integrity" "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cacache/-/cacache-12.0.4.tgz"
  "version" "12.0.4"
  dependencies:
    "bluebird" "^3.5.5"
    "chownr" "^1.1.1"
    "figgy-pudding" "^3.5.1"
    "glob" "^7.1.4"
    "graceful-fs" "^4.1.15"
    "infer-owner" "^1.0.3"
    "lru-cache" "^5.1.1"
    "mississippi" "^3.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.3"
    "ssri" "^6.0.1"
    "unique-filename" "^1.1.1"
    "y18n" "^4.0.0"

"cacache@^15.0.5":
  "integrity" "sha1-3IU4D7L1Vv492kxxm/oOyHWn8es="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cacache/-/cacache-15.3.0.tgz"
  "version" "15.3.0"
  dependencies:
    "@npmcli/fs" "^1.0.0"
    "@npmcli/move-file" "^1.0.1"
    "chownr" "^2.0.0"
    "fs-minipass" "^2.0.0"
    "glob" "^7.1.4"
    "infer-owner" "^1.0.4"
    "lru-cache" "^6.0.0"
    "minipass" "^3.1.1"
    "minipass-collect" "^1.0.2"
    "minipass-flush" "^1.0.5"
    "minipass-pipeline" "^1.2.2"
    "mkdirp" "^1.0.3"
    "p-map" "^4.0.0"
    "promise-inflight" "^1.0.1"
    "rimraf" "^3.0.2"
    "ssri" "^8.0.1"
    "tar" "^6.0.2"
    "unique-filename" "^1.1.1"

"cache-base@^1.0.1":
  "integrity" "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cache-base/-/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"cache-loader@^4.1.0":
  "integrity" "sha1-mUjK41OuwKH8ser9ojAIFuyFOH4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cache-loader/-/cache-loader-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer-json" "^2.0.0"
    "find-cache-dir" "^3.0.0"
    "loader-utils" "^1.2.3"
    "mkdirp" "^0.5.1"
    "neo-async" "^2.6.1"
    "schema-utils" "^2.0.0"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"call-me-maybe@^1.0.1":
  "integrity" "sha1-JtII6onje1y95gJQoV8DHBak1ms="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/call-me-maybe/-/call-me-maybe-1.0.1.tgz"
  "version" "1.0.1"

"caller-callsite@^2.0.0":
  "integrity" "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/caller-callsite/-/caller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^2.0.0":
  "integrity" "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/caller-path/-/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsites@^2.0.0":
  "integrity" "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/callsites/-/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@^3.0.0":
  "integrity" "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@3.0.x":
  "integrity" "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/camel-case/-/camel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "^2.2.0"
    "upper-case" "^1.1.1"

"camelcase@^5.0.0", "camelcase@^5.3.1":
  "integrity" "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.0.0":
  "integrity" "sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/camelcase/-/camelcase-6.3.0.tgz"
  "version" "6.3.0"

"caniuse-api@^3.0.0":
  "integrity" "sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/caniuse-api/-/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30001109", "caniuse-lite@^1.0.30001400":
  "integrity" "sha1-YTXancqzTNl2HZzbEqaOZ0DF6W4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/caniuse-lite/-/caniuse-lite-1.0.30001409.tgz"
  "version" "1.0.30001409"

"capture-stack-trace@^1.0.0":
  "integrity" "sha1-psC74fOPOqC5Ijjstv9Cw0TUE10="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/capture-stack-trace/-/capture-stack-trace-1.0.1.tgz"
  "version" "1.0.1"

"case-sensitive-paths-webpack-plugin@^2.3.0":
  "integrity" "sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  "version" "2.4.0"

"caseless@~0.12.0":
  "integrity" "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/caseless/-/caseless-0.12.0.tgz"
  "version" "0.12.0"

"chalk@^1.0.0", "chalk@^1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.0.1":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.1.0":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.4.1":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.4.2":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^3.0.0":
  "integrity" "sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chalk/-/chalk-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.0.0", "chalk@^4.1.0", "chalk@4.1.0":
  "integrity" "sha1-ThSHCmGNni7dl92DRf2dncMVZGo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chalk/-/chalk-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@~0.4.0":
  "integrity" "sha1-UZmj3c0MHv4jvAjBsCewYXbgxk8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chalk/-/chalk-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "ansi-styles" "~1.0.0"
    "has-color" "~0.1.0"
    "strip-ansi" "~0.1.0"

"chardet@^0.7.0":
  "integrity" "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chardet/-/chardet-0.7.0.tgz"
  "version" "0.7.0"

"check-types@^8.0.3":
  "integrity" "sha1-M1bMoZyIlUTy16le1JzlCKDs9VI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/check-types/-/check-types-8.0.3.tgz"
  "version" "8.0.3"

"chokidar@^2.1.8":
  "integrity" "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chokidar/-/chokidar-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.1"
    "braces" "^2.3.2"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.3"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "normalize-path" "^3.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.2.1"
    "upath" "^1.1.1"
  optionalDependencies:
    "fsevents" "^1.2.7"

"chokidar@^3.4.1", "chokidar@>=3.0.0 <4.0.0":
  "integrity" "sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chokidar/-/chokidar-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chownr@^1.1.1":
  "integrity" "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chownr/-/chownr-1.1.4.tgz"
  "version" "1.1.4"

"chownr@^2.0.0":
  "integrity" "sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chownr/-/chownr-2.0.0.tgz"
  "version" "2.0.0"

"chrome-trace-event@^1.0.2":
  "integrity" "sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"
  "version" "1.0.3"

"ci-info@^1.5.0":
  "integrity" "sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ci-info/-/ci-info-1.6.0.tgz"
  "version" "1.6.0"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cipher-base/-/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"class-utils@^0.3.5":
  "integrity" "sha1-+TNprouafOAv1B+q0MqDAzGQxGM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/class-utils/-/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-css@4.2.x":
  "integrity" "sha1-czv0brpOYHxokepXwkqYk1aDEXg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/clean-css/-/clean-css-4.2.4.tgz"
  "version" "4.2.4"
  dependencies:
    "source-map" "~0.6.0"

"clean-stack@^2.0.0":
  "integrity" "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/clean-stack/-/clean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cli-cursor@^2.0.0", "cli-cursor@^2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cli-cursor/-/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-cursor@^3.1.0":
  "integrity" "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cli-cursor/-/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-highlight@^2.1.4":
  "integrity" "sha1-SXNvpFLwqvT65YDjCssmgo0twb8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cli-highlight/-/cli-highlight-2.1.11.tgz"
  "version" "2.1.11"
  dependencies:
    "chalk" "^4.0.0"
    "highlight.js" "^10.7.1"
    "mz" "^2.4.0"
    "parse5" "^5.1.1"
    "parse5-htmlparser2-tree-adapter" "^6.0.0"
    "yargs" "^16.0.0"

"cli-spinners@^2.0.0":
  "integrity" "sha1-+BX9MLX56qwC22BMeiMe18sveXo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cli-spinners/-/cli-spinners-2.7.0.tgz"
  "version" "2.7.0"

"cli-truncate@^0.2.1":
  "integrity" "sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cli-truncate/-/cli-truncate-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "slice-ansi" "0.0.4"
    "string-width" "^1.0.1"

"cli-width@^3.0.0":
  "integrity" "sha1-ovSEN6LKqaIkNueUvwceyeYc7fY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cli-width/-/cli-width-3.0.0.tgz"
  "version" "3.0.0"

"cli@~1.0.0":
  "integrity" "sha1-IoF1NPJL+klQw01TLUjsvGIbjBQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cli/-/cli-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "exit" "0.1.2"
    "glob" "^7.1.1"

"clipboard@^2.0.0":
  "integrity" "sha1-YhgDYLl91mi2s6hOwiaXV2KnC+U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/clipboard/-/clipboard-2.0.11.tgz"
  "version" "2.0.11"
  dependencies:
    "good-listener" "^1.2.2"
    "select" "^1.1.2"
    "tiny-emitter" "^2.0.0"

"clipboardy@^2.3.0":
  "integrity" "sha1-PCkDZQxo5GqRs4iYW8J3QofbopA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/clipboardy/-/clipboardy-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "arch" "^2.1.1"
    "execa" "^1.0.0"
    "is-wsl" "^2.1.1"

"cliui@^5.0.0":
  "integrity" "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cliui/-/cliui-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "string-width" "^3.1.0"
    "strip-ansi" "^5.2.0"
    "wrap-ansi" "^5.1.0"

"cliui@^6.0.0":
  "integrity" "sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cliui/-/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"cliui@^7.0.2":
  "integrity" "sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cliui/-/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clone-deep@^4.0.1":
  "integrity" "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/clone-deep/-/clone-deep-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"
    "kind-of" "^6.0.2"
    "shallow-clone" "^3.0.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1", "clone@~2.1.0":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"coa@^2.0.2":
  "integrity" "sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/coa/-/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    "chalk" "^2.4.1"
    "q" "^1.1.2"

"code-point-at@^1.0.0":
  "integrity" "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/code-point-at/-/code-point-at-1.1.0.tgz"
  "version" "1.1.0"

"codemirror@^5.39.2", "codemirror@^5.41.0":
  "integrity" "sha1-7HDJKqIG7kyYU9Xx58TtNWzatow="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/codemirror/-/codemirror-5.65.9.tgz"
  "version" "5.65.9"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/collection-visit/-/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0":
  "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^1.9.3":
  "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.0.0", "color-name@~1.1.4":
  "integrity" "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-string@^1.6.0":
  "integrity" "sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/color-string/-/color-string-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "color-name" "^1.0.0"
    "simple-swizzle" "^0.2.2"

"color@^3.0.0":
  "integrity" "sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/color/-/color-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.3"
    "color-string" "^1.6.0"

"combined-stream@^1.0.6", "combined-stream@^1.0.8", "combined-stream@~1.0.6":
  "integrity" "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.18.0":
  "integrity" "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^2.20.0":
  "integrity" "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^9.2.0":
  "integrity" "sha1-vAjR61zt98y3l6lhmdQce8PmDTA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/commander/-/commander-9.5.0.tgz"
  "version" "9.5.0"

"commander@~2.19.0":
  "integrity" "sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/commander/-/commander-2.19.0.tgz"
  "version" "2.19.0"

"commander@2.17.x":
  "integrity" "sha1-vXerfebelCBc6sxy8XFtKfIKd78="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/commander/-/commander-2.17.1.tgz"
  "version" "2.17.1"

"commander@5.1.0":
  "integrity" "sha1-Rqu9FlL44Fm92u+Zu9yyrZzxea4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/commander/-/commander-5.1.0.tgz"
  "version" "5.1.0"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/commondir/-/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-bind@1.0.0":
  "integrity" "sha1-AMYIq33Nk4l8AAllGx06jh5zu9E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/component-bind/-/component-bind-1.0.0.tgz"
  "version" "1.0.0"

"component-emitter@^1.2.1", "component-emitter@~1.3.0":
  "integrity" "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/component-emitter/-/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"component-inherit@0.0.3":
  "integrity" "sha1-ZF/ErfWLcrZJ1crmUTVhnbJv8UM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/component-inherit/-/component-inherit-0.0.3.tgz"
  "version" "0.0.3"

"compressible@~2.0.16":
  "integrity" "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/compressible/-/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression-webpack-plugin@^5.0.0":
  "integrity" "sha1-34Tmgs+h+yojDnHPg9UMMj1TacI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/compression-webpack-plugin/-/compression-webpack-plugin-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "cacache" "^15.0.5"
    "find-cache-dir" "^3.3.1"
    "schema-utils" "^2.7.0"
    "serialize-javascript" "^4.0.0"
    "webpack-sources" "^1.4.3"

"compression@^1.7.4":
  "integrity" "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/compression/-/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.5.0":
  "integrity" "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/concat-stream/-/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"connect-history-api-fallback@^1.6.0":
  "integrity" "sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"console-browserify@^1.1.0", "console-browserify@1.1.x":
  "integrity" "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/console-browserify/-/console-browserify-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "date-now" "^0.1.4"

"consolidate@^0.15.1":
  "integrity" "sha1-IasEMjXHGgfUXZqtmFk7DbpWurc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/consolidate/-/consolidate-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "bluebird" "^3.1.1"

"constants-browserify@^1.0.0":
  "integrity" "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/constants-browserify/-/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"content-disposition@0.5.4":
  "integrity" "sha1-i4K076yCUSoCuwsdzsnSxejrW/4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/content-disposition/-/content-disposition-0.5.4.tgz"
  "version" "0.5.4"
  dependencies:
    "safe-buffer" "5.2.1"

"content-type@~1.0.4":
  "integrity" "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/content-type/-/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.7.0":
  "integrity" "sha1-8zc8MtIbTXgN2ABFFGhPt5HKQ2k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/convert-source-map/-/convert-source-map-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cookie-signature/-/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.5.0":
  "integrity" "sha1-0fXXGt7GVYxY84mYfDZqpH6ZT4s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cookie/-/cookie-0.5.0.tgz"
  "version" "0.5.0"

"copy-concurrently@^1.0.0":
  "integrity" "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/copy-concurrently/-/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "^1.1.1"
    "fs-write-stream-atomic" "^1.0.8"
    "iferr" "^0.1.5"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-webpack-plugin@^5.1.1":
  "integrity" "sha1-ioieHcr6bJHGzUvhrRWPHTgjuuI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/copy-webpack-plugin/-/copy-webpack-plugin-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "cacache" "^12.0.3"
    "find-cache-dir" "^2.1.0"
    "glob-parent" "^3.1.0"
    "globby" "^7.1.1"
    "is-glob" "^4.0.1"
    "loader-utils" "^1.2.3"
    "minimatch" "^3.0.4"
    "normalize-path" "^3.0.0"
    "p-limit" "^2.2.1"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^4.0.0"
    "webpack-log" "^2.0.0"

"core-js-compat@^3.25.1", "core-js-compat@^3.6.5":
  "integrity" "sha1-eHVXNYaAmQnGngPvMQgQwZae4Tg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/core-js-compat/-/core-js-compat-3.25.2.tgz"
  "version" "3.25.2"
  dependencies:
    "browserslist" "^4.21.4"

"core-js@^1.2.0":
  "integrity" "sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/core-js/-/core-js-1.2.7.tgz"
  "version" "1.2.7"

"core-js@^2.1.0":
  "integrity" "sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/core-js/-/core-js-2.6.12.tgz"
  "version" "2.6.12"

"core-js@^2.4.0", "core-js@^2.5.0":
  "integrity" "sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/core-js/-/core-js-2.6.12.tgz"
  "version" "2.6.12"

"core-js@^3.6.5", "core-js@3":
  "integrity" "sha1-QP87QViLCRqu0ZyhqlyxEYA/qaY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/core-js/-/core-js-3.29.1.tgz"
  "version" "3.29.1"

"core-util-is@~1.0.0":
  "integrity" "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"core-util-is@1.0.2":
  "integrity" "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/core-util-is/-/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cosmiconfig@^5.0.0", "cosmiconfig@^5.2.1":
  "integrity" "sha1-BA9yaAnFked6F8CjYmykW08Wixo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cosmiconfig/-/cosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"create-ecdh@^4.0.0":
  "integrity" "sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/create-ecdh/-/create-ecdh-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.5.3"

"create-error-class@^2.0.0":
  "integrity" "sha1-qHWe1cjSFKRh6B0Y5wqssz3WPJw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/create-error-class/-/create-error-class-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "capture-stack-trace" "^1.0.0"
    "inherits" "^2.0.1"

"create-hash@^1.1.0", "create-hash@^1.1.2", "create-hash@^1.2.0":
  "integrity" "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/create-hash/-/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.4", "create-hmac@^1.1.7":
  "integrity" "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/create-hmac/-/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"cropperjs@^1.5.12":
  "integrity" "sha1-2cDbK/uMDXadUXOej5FrvEThD1A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cropperjs/-/cropperjs-1.5.12.tgz"
  "version" "1.5.12"

"cross-spawn@^5.0.1":
  "integrity" "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cross-spawn/-/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.0", "cross-spawn@^6.0.5":
  "integrity" "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cross-spawn/-/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.0":
  "integrity" "sha1-9zqFudXUHQRVUcF34ogtSshXKKY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypto-browserify@^3.11.0":
  "integrity" "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/crypto-browserify/-/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"crypto-js@^4.1.1":
  "integrity" "sha1-nkhbzwNSEEG9hYRHhrg/t2GXNs8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/crypto-js/-/crypto-js-4.1.1.tgz"
  "version" "4.1.1"

"css-color-names@^0.0.4", "css-color-names@0.0.4":
  "integrity" "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-color-names/-/css-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-declaration-sorter@^4.0.1":
  "integrity" "sha1-wZiUD2OnbX42wecQGLABchBUyyI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-declaration-sorter/-/css-declaration-sorter-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.1"
    "timsort" "^0.3.0"

"css-line-break@^2.1.0":
  "integrity" "sha1-v+9mDfpvU5fqVBFrs8tIc+28T6A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-line-break/-/css-line-break-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "utrie" "^1.0.2"

"css-loader@*", "css-loader@^3.5.3":
  "integrity" "sha1-Lkssfm4tJ/jI8o9hv/zS5ske9kU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-loader/-/css-loader-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "camelcase" "^5.3.1"
    "cssesc" "^3.0.0"
    "icss-utils" "^4.1.1"
    "loader-utils" "^1.2.3"
    "normalize-path" "^3.0.0"
    "postcss" "^7.0.32"
    "postcss-modules-extract-imports" "^2.0.0"
    "postcss-modules-local-by-default" "^3.0.2"
    "postcss-modules-scope" "^2.2.0"
    "postcss-modules-values" "^3.0.0"
    "postcss-value-parser" "^4.1.0"
    "schema-utils" "^2.7.0"
    "semver" "^6.3.0"

"css-select-base-adapter@^0.1.1":
  "integrity" "sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@^2.0.0":
  "integrity" "sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-select/-/css-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-select@^4.1.3":
  "integrity" "sha1-23EpsoRmYv2GKM/ElquytZ5BUps="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-select/-/css-select-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^6.0.1"
    "domhandler" "^4.3.1"
    "domutils" "^2.8.0"
    "nth-check" "^2.0.1"

"css-tree@^1.1.2":
  "integrity" "sha1-60hw+2/XcHMn7JXC/yqwm16NuR0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-tree/-/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha1-mL69YsTB2flg7DQM+fdSLjBwmiI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-tree/-/css-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "^0.6.1"

"css-what@^3.2.1":
  "integrity" "sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-what/-/css-what-3.4.2.tgz"
  "version" "3.4.2"

"css-what@^6.0.1":
  "integrity" "sha1-+17/z3bx3eosgb36pN5E55uscPQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/css-what/-/css-what-6.1.0.tgz"
  "version" "6.1.0"

"cssesc@^3.0.0":
  "integrity" "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"csslint@^1.0.5":
  "integrity" "sha1-Gcw+2jIhYP0/cjKvHLKjYOiYouk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/csslint/-/csslint-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "clone" "~2.1.0"
    "parserlib" "~1.1.1"

"cssnano-preset-default@^4.0.0", "cssnano-preset-default@^4.0.8":
  "integrity" "sha1-kgYisfwelaNOiDggPxOXpQTy0/8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cssnano-preset-default/-/cssnano-preset-default-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "css-declaration-sorter" "^4.0.1"
    "cssnano-util-raw-cache" "^4.0.1"
    "postcss" "^7.0.0"
    "postcss-calc" "^7.0.1"
    "postcss-colormin" "^4.0.3"
    "postcss-convert-values" "^4.0.1"
    "postcss-discard-comments" "^4.0.2"
    "postcss-discard-duplicates" "^4.0.2"
    "postcss-discard-empty" "^4.0.1"
    "postcss-discard-overridden" "^4.0.1"
    "postcss-merge-longhand" "^4.0.11"
    "postcss-merge-rules" "^4.0.3"
    "postcss-minify-font-values" "^4.0.2"
    "postcss-minify-gradients" "^4.0.2"
    "postcss-minify-params" "^4.0.2"
    "postcss-minify-selectors" "^4.0.2"
    "postcss-normalize-charset" "^4.0.1"
    "postcss-normalize-display-values" "^4.0.2"
    "postcss-normalize-positions" "^4.0.2"
    "postcss-normalize-repeat-style" "^4.0.2"
    "postcss-normalize-string" "^4.0.2"
    "postcss-normalize-timing-functions" "^4.0.2"
    "postcss-normalize-unicode" "^4.0.1"
    "postcss-normalize-url" "^4.0.1"
    "postcss-normalize-whitespace" "^4.0.2"
    "postcss-ordered-values" "^4.1.2"
    "postcss-reduce-initial" "^4.0.3"
    "postcss-reduce-transforms" "^4.0.2"
    "postcss-svgo" "^4.0.3"
    "postcss-unique-selectors" "^4.0.1"

"cssnano-util-get-arguments@^4.0.0":
  "integrity" "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cssnano-util-get-arguments/-/cssnano-util-get-arguments-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-get-match@^4.0.0":
  "integrity" "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cssnano-util-get-match/-/cssnano-util-get-match-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-raw-cache@^4.0.1":
  "integrity" "sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cssnano-util-raw-cache/-/cssnano-util-raw-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"cssnano-util-same-parent@^4.0.0":
  "integrity" "sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.1.tgz"
  "version" "4.0.1"

"cssnano@^4.0.0", "cssnano@^4.1.10":
  "integrity" "sha1-x7X1uB2iacsf2YLLlgwSAJEMmpk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cssnano/-/cssnano-4.1.11.tgz"
  "version" "4.1.11"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "cssnano-preset-default" "^4.0.8"
    "is-resolvable" "^1.0.0"
    "postcss" "^7.0.0"

"csso@^4.0.2":
  "integrity" "sha1-6jpWE0bo3J9UbW/r7dUBh884lSk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/csso/-/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"cyclist@^1.0.1":
  "integrity" "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/cyclist/-/cyclist-1.0.1.tgz"
  "version" "1.0.1"

"dashdash@^1.12.0":
  "integrity" "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dashdash/-/dashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"date-fns@^1.27.2":
  "integrity" "sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/date-fns/-/date-fns-1.30.1.tgz"
  "version" "1.30.1"

"date-now@^0.1.4":
  "integrity" "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/date-now/-/date-now-0.1.4.tgz"
  "version" "0.1.4"

"de-indent@^1.0.2":
  "integrity" "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/de-indent/-/de-indent-1.0.2.tgz"
  "version" "1.0.2"

"debug@^2.2.0", "debug@2", "debug@2.2.0":
  "integrity" "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "ms" "0.7.1"

"debug@^2.3.3":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.6.8":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.2.7":
  "integrity" "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.0.1":
  "integrity" "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debug@^4.1.0":
  "integrity" "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debug@^4.1.1":
  "integrity" "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debug@~3.1.0":
  "integrity" "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "ms" "2.0.0"

"debug@2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debuglog@^1.0.1":
  "integrity" "sha1-qiT/uaw9+aI1GDfPstJ5NgzXhJI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/debuglog/-/debuglog-1.0.1.tgz"
  "version" "1.0.1"

"decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/decode-uri-component/-/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"dedent@^0.7.0":
  "integrity" "sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dedent/-/dedent-0.7.0.tgz"
  "version" "0.7.0"

"deep-equal@^1.0.1":
  "integrity" "sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/deep-equal/-/deep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@~0.1.3":
  "integrity" "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^1.2.0", "deepmerge@^1.5.2":
  "integrity" "sha1-EEmdhohEza1P7ghC34x/bwyVp1M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/deepmerge/-/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"deepmerge@1.3.2":
  "integrity" "sha1-FmNpFinU2/42T6EqKk8KqGqjoFA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/deepmerge/-/deepmerge-1.3.2.tgz"
  "version" "1.3.2"

"default-gateway@^4.2.0":
  "integrity" "sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/default-gateway/-/default-gateway-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "execa" "^1.0.0"
    "ip-regex" "^2.1.0"

"default-gateway@^5.0.5":
  "integrity" "sha1-T9a9XShV05s0zFpZUFSG6ar8mxA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/default-gateway/-/default-gateway-5.0.5.tgz"
  "version" "5.0.5"
  dependencies:
    "execa" "^3.3.0"

"defaults@^1.0.3":
  "integrity" "sha1-xlYFHpgX2f8I7YgUd/P+QBnz730="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/defaults/-/defaults-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "clone" "^1.0.2"

"define-properties@^1.1.2", "define-properties@^1.1.3", "define-properties@^1.1.4":
  "integrity" "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "has-property-descriptors" "^1.0.0"
    "object-keys" "^1.1.1"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/define-property/-/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/define-property/-/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha1-1Flono1lS6d+AqgX+HENcCyxbp0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/define-property/-/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"del@^4.1.1":
  "integrity" "sha1-no8RciLqRKMf86FWwEm5kFKp8LQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/del/-/del-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "@types/glob" "^7.1.1"
    "globby" "^6.1.0"
    "is-path-cwd" "^2.0.0"
    "is-path-in-cwd" "^2.0.0"
    "p-map" "^2.0.0"
    "pify" "^4.0.1"
    "rimraf" "^2.6.3"

"del@^5.0.0":
  "integrity" "sha1-2Uh8lONnQQ5u/ykl7ljAyEp1s6c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/del/-/del-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "globby" "^10.0.1"
    "graceful-fs" "^4.2.2"
    "is-glob" "^4.0.1"
    "is-path-cwd" "^2.2.0"
    "is-path-inside" "^3.0.1"
    "p-map" "^3.0.0"
    "rimraf" "^3.0.0"
    "slash" "^3.0.0"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"delegate@^3.1.2":
  "integrity" "sha1-tmtxwxWFIuirV0T3INjKDCr1kWY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/delegate/-/delegate-3.2.0.tgz"
  "version" "3.2.0"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"depd@2.0.0":
  "integrity" "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/depd/-/depd-2.0.0.tgz"
  "version" "2.0.0"

"des.js@^1.0.0":
  "integrity" "sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/des.js/-/des.js-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"destroy@1.2.0":
  "integrity" "sha1-SANzVQmti+VSk0xn32FPlOZvoBU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/destroy/-/destroy-1.2.0.tgz"
  "version" "1.2.0"

"detect-node@^2.0.4":
  "integrity" "sha1-yccHdaScPQO8LAbZpzvlUPl4+LE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/detect-node/-/detect-node-2.1.0.tgz"
  "version" "2.1.0"

"dezalgo@^1.0.0":
  "integrity" "sha1-dRI1JgRpCEwTIVffqFfzhtTDPYE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dezalgo/-/dezalgo-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "asap" "^2.0.0"
    "wrappy" "1"

"diff-match-patch@^1.0.0":
  "integrity" "sha1-q7WE1fEM0Rlt/FWqA3AVkq4/ezc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/diff-match-patch/-/diff-match-patch-1.0.5.tgz"
  "version" "1.0.5"

"diffie-hellman@^5.0.0":
  "integrity" "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/diffie-hellman/-/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dijkstrajs@^1.0.1":
  "integrity" "sha1-LkjA07glRir+datK1egpyOzjYlc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dijkstrajs/-/dijkstrajs-1.0.2.tgz"
  "version" "1.0.2"

"dir-glob@^2.0.0", "dir-glob@^2.2.2":
  "integrity" "sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dir-glob/-/dir-glob-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "path-type" "^3.0.0"

"dir-glob@^3.0.1":
  "integrity" "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dir-glob/-/dir-glob-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "path-type" "^4.0.0"

"dns-equal@^1.0.0":
  "integrity" "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dns-equal/-/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^1.3.1":
  "integrity" "sha1-40VQZYJKJQe6iGxVqJljuxB97G8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dns-packet/-/dns-packet-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "ip" "^1.1.0"
    "safe-buffer" "^5.0.1"

"dns-txt@^2.0.2":
  "integrity" "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dns-txt/-/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "^1.0.0"

"doctrine@^2.1.0":
  "integrity" "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/doctrine/-/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@^3.0.0":
  "integrity" "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/doctrine/-/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-converter@^0.2.0":
  "integrity" "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dom-converter/-/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-serializer@^1.0.1":
  "integrity" "sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dom-serializer/-/dom-serializer-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.2.0"
    "entities" "^2.0.0"

"dom-serializer@0":
  "integrity" "sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dom-serializer/-/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"dom7@^2.1.5":
  "integrity" "sha1-p5QRAXgAsx2EAAcM2uu/ySwfY3c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dom7/-/dom7-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "ssr-window" "^2.0.0"

"domain-browser@^1.1.1":
  "integrity" "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/domain-browser/-/domain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@^2.0.1", "domelementtype@^2.2.0":
  "integrity" "sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/domelementtype/-/domelementtype-2.3.0.tgz"
  "version" "2.3.0"

"domelementtype@1":
  "integrity" "sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/domelementtype/-/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domhandler@^4.0.0", "domhandler@^4.2.0", "domhandler@^4.3.1":
  "integrity" "sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/domhandler/-/domhandler-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "domelementtype" "^2.2.0"

"domhandler@2.3":
  "integrity" "sha1-LeWaCCLVAn+r/28DLCsloqir5zg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/domhandler/-/domhandler-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "domelementtype" "1"

"domready@1.0.8":
  "integrity" "sha1-kfJS5Ze2Wvd+dFriTdAYXV4m1Yw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/domready/-/domready-1.0.8.tgz"
  "version" "1.0.8"

"domutils@^1.7.0":
  "integrity" "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/domutils/-/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@^2.5.2", "domutils@^2.8.0":
  "integrity" "sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/domutils/-/domutils-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"domutils@1.5":
  "integrity" "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/domutils/-/domutils-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"dot-prop@^5.2.0":
  "integrity" "sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dot-prop/-/dot-prop-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "is-obj" "^2.0.0"

"dotenv-expand@^5.1.0":
  "integrity" "sha1-P7rwIL/XlIhAcuomsel5HUWmKfA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
  "version" "5.1.0"

"dotenv@^8.2.0":
  "integrity" "sha1-Bhr2ZNGff02PxuT/m1hM4jety4s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/dotenv/-/dotenv-8.6.0.tgz"
  "version" "8.6.0"

"duplexer@^0.1.1":
  "integrity" "sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/duplexer/-/duplexer-0.1.2.tgz"
  "version" "0.1.2"

"duplexer2@^0.1.4":
  "integrity" "sha1-ixLauHjA1p4+eJEFFmKjL8a93ME="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/duplexer2/-/duplexer2-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "readable-stream" "^2.0.2"

"duplexify@^3.4.2", "duplexify@^3.5.0", "duplexify@^3.6.0":
  "integrity" "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/duplexify/-/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"easings-css@^1.0.0":
  "integrity" "sha1-3eVpADu3pKDAt3h49ds+C+VnnIE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/easings-css/-/easings-css-1.0.0.tgz"
  "version" "1.0.0"

"easy-stack@1.0.1":
  "integrity" "sha1-iv5CZGJpiMq7EfPHBMzQyDVBEGY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/easy-stack/-/easy-stack-1.0.1.tgz"
  "version" "1.0.1"

"easy-table@1.0.0":
  "integrity" "sha1-KdstCFXTYxbkOC5aPYXZy1/JMhY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/easy-table/-/easy-table-1.0.0.tgz"
  "version" "1.0.0"

"ecc-jsbn@~0.1.1":
  "integrity" "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"echarts@^4.8.0":
  "integrity" "sha1-qbm6oD8Doqcx5jQMVb77V6nhNH0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/echarts/-/echarts-4.9.0.tgz"
  "version" "4.9.0"
  dependencies:
    "zrender" "4.3.2"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^2.6.1":
  "integrity" "sha1-SGYSh1c9zFPjZsehrlLDoSDuybo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ejs/-/ejs-2.7.4.tgz"
  "version" "2.7.4"

"electron-to-chromium@^1.4.251":
  "integrity" "sha1-iV3HPGu1jRI13ICHnsvKC8upbiw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/electron-to-chromium/-/electron-to-chromium-1.4.257.tgz"
  "version" "1.4.257"

"elegant-spinner@^1.0.1":
  "integrity" "sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/elegant-spinner/-/elegant-spinner-1.0.1.tgz"
  "version" "1.0.1"

"element-resize-detector@^1.2.1":
  "integrity" "sha1-PmxZgt13UItfp+bVwCFw4mMlybE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/element-resize-detector/-/element-resize-detector-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "batch-processor" "1.0.0"

"element-ui@^2.15.3":
  "integrity" "sha1-/eD/XLTDDo6xZtYX+FkW3v5ZSPE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/element-ui/-/element-ui-2.15.10.tgz"
  "version" "2.15.10"
  dependencies:
    "async-validator" "~1.8.1"
    "babel-helper-vue-jsx-merge-props" "^2.0.0"
    "deepmerge" "^1.2.0"
    "normalize-wheel" "^1.0.1"
    "resize-observer-polyfill" "^1.5.0"
    "throttle-debounce" "^1.0.1"

"elliptic@^6.5.3":
  "integrity" "sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/elliptic/-/elliptic-6.5.4.tgz"
  "version" "6.5.4"
  dependencies:
    "bn.js" "^4.11.9"
    "brorand" "^1.1.0"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.1"
    "inherits" "^2.0.4"
    "minimalistic-assert" "^1.0.1"
    "minimalistic-crypto-utils" "^1.0.1"

"emoji-regex@^7.0.1":
  "integrity" "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/emoji-regex/-/emoji-regex-7.0.3.tgz"
  "version" "7.0.3"

"emoji-regex@^8.0.0":
  "integrity" "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^2.0.0":
  "integrity" "sha1-TapNnbAPmBmIDHn6RXrlsJof04k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/emojis-list/-/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"emojis-list@^3.0.0":
  "integrity" "sha1-VXBmIEatKeLpFucariYKvf9Pang="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encode-utf8@^1.0.3":
  "integrity" "sha1-8w/dMdoH+1lvKBvrL2sCeFGZTNo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/encode-utf8/-/encode-utf8-1.0.3.tgz"
  "version" "1.0.3"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0":
  "integrity" "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"engine.io-client@~3.5.0":
  "integrity" "sha1-MlT2H9vVNQPcmm+dRqUlKIccoNc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/engine.io-client/-/engine.io-client-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "component-emitter" "~1.3.0"
    "component-inherit" "0.0.3"
    "debug" "~3.1.0"
    "engine.io-parser" "~2.2.0"
    "has-cors" "1.1.0"
    "indexof" "0.0.1"
    "parseqs" "0.0.6"
    "parseuri" "0.0.6"
    "ws" "~7.4.2"
    "xmlhttprequest-ssl" "~1.6.2"
    "yeast" "0.1.2"

"engine.io-parser@~2.2.0":
  "integrity" "sha1-V85WEdk3DulPmWQbWJ+UyX5PXac="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/engine.io-parser/-/engine.io-parser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "after" "0.8.2"
    "arraybuffer.slice" "~0.0.7"
    "base64-arraybuffer" "0.1.4"
    "blob" "0.0.5"
    "has-binary2" "~1.0.2"

"enhanced-resolve@^0.9.1":
  "integrity" "sha1-TW5omzcl+GCQknzMhs2fFjW4ni4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/enhanced-resolve/-/enhanced-resolve-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.2.0"
    "tapable" "^0.1.8"

"enhanced-resolve@^4.5.0":
  "integrity" "sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.5.0"
    "tapable" "^1.0.0"

"entities@^2.0.0":
  "integrity" "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/entities/-/entities-2.2.0.tgz"
  "version" "2.2.0"

"entities@1.0":
  "integrity" "sha1-sph6o4ITR/zeZCsk/fyeT7cSvyY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/entities/-/entities-1.0.0.tgz"
  "version" "1.0.0"

"errno@^0.1.3", "errno@~0.1.7":
  "integrity" "sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/errno/-/errno-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.2.0", "error-ex@^1.3.1":
  "integrity" "sha1-tKxAZIEH/c3PriQvQovqihTU8b8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.6":
  "integrity" "sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/error-stack-parser/-/error-stack-parser-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "stackframe" "^1.3.4"

"es-abstract@^1.17.2", "es-abstract@^1.19.0", "es-abstract@^1.19.1", "es-abstract@^1.19.2", "es-abstract@^1.19.5", "es-abstract@^1.20.1":
  "integrity" "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz"
  "version" "1.20.2"
  dependencies:
    "call-bind" "^1.0.2"
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "function.prototype.name" "^1.1.5"
    "get-intrinsic" "^1.1.2"
    "get-symbol-description" "^1.0.0"
    "has" "^1.0.3"
    "has-property-descriptors" "^1.0.0"
    "has-symbols" "^1.0.3"
    "internal-slot" "^1.0.3"
    "is-callable" "^1.2.4"
    "is-negative-zero" "^2.0.2"
    "is-regex" "^1.1.4"
    "is-shared-array-buffer" "^1.0.2"
    "is-string" "^1.0.7"
    "is-weakref" "^1.0.2"
    "object-inspect" "^1.12.2"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.4"
    "regexp.prototype.flags" "^1.4.3"
    "string.prototype.trimend" "^1.0.5"
    "string.prototype.trimstart" "^1.0.5"
    "unbox-primitive" "^1.0.2"

"es-array-method-boxes-properly@^1.0.0":
  "integrity" "sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/es-array-method-boxes-properly/-/es-array-method-boxes-properly-1.0.0.tgz"
  "version" "1.0.0"

"es-shim-unscopables@^1.0.0":
  "integrity" "sha1-cC5jIZMgHj7fhxNjXQg9N45RAkE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/es-shim-unscopables/-/es-shim-unscopables-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has" "^1.0.3"

"es-to-primitive@^1.2.1":
  "integrity" "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"es6-promise@^3.1.2":
  "integrity" "sha1-oIzd6EzNvzTQJ6FFG8kdS80ophM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/es6-promise/-/es6-promise-3.3.1.tgz"
  "version" "3.3.1"

"es6-promisify@4.0.0":
  "integrity" "sha1-eMH/zYSM4jP9lin+qlNxym3N9Lg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/es6-promisify/-/es6-promisify-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "es6-promise" "^3.1.2"

"escalade@^3.1.1":
  "integrity" "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5", "escape-string-regexp@1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"eslint-config-standard@^14.1.0":
  "integrity" "sha1-gwqOROeu995nRkl5rQa0BgJsVuo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-config-standard/-/eslint-config-standard-14.1.1.tgz"
  "version" "14.1.1"

"eslint-import-resolver-node@^0.3.3", "eslint-import-resolver-node@^0.3.6":
  "integrity" "sha1-QEi5WDldqJZoJSAB29nsprg7rL0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "debug" "^3.2.7"
    "resolve" "^1.20.0"

"eslint-import-resolver-webpack@^0.12.1":
  "integrity" "sha1-dp6GzQx1KhU2wZhV69kKoUzjhO4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-import-resolver-webpack/-/eslint-import-resolver-webpack-0.12.2.tgz"
  "version" "0.12.2"
  dependencies:
    "array-find" "^1.0.0"
    "debug" "^2.6.9"
    "enhanced-resolve" "^0.9.1"
    "find-root" "^1.1.0"
    "has" "^1.0.3"
    "interpret" "^1.2.0"
    "lodash" "^4.17.15"
    "node-libs-browser" "^1.0.0 || ^2.0.0"
    "resolve" "^1.13.1"
    "semver" "^5.7.1"

"eslint-loader@^2.2.1":
  "integrity" "sha1-KLnBLaVAV68IReKmEScBova/gzc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-loader/-/eslint-loader-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "loader-fs-cache" "^1.0.0"
    "loader-utils" "^1.0.2"
    "object-assign" "^4.0.1"
    "object-hash" "^1.1.4"
    "rimraf" "^2.6.1"

"eslint-module-utils@^2.7.3":
  "integrity" "sha1-Tz5BEWqvE6IHkiYeYdOi5+BYOXQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-module-utils/-/eslint-module-utils-2.7.4.tgz"
  "version" "2.7.4"
  dependencies:
    "debug" "^3.2.7"

"eslint-plugin-es@^3.0.0":
  "integrity" "sha1-dafN/czdwFiZNK7rOEF18iHFeJM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-plugin-es/-/eslint-plugin-es-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "eslint-utils" "^2.0.0"
    "regexpp" "^3.0.0"

"eslint-plugin-import@^2.20.2", "eslint-plugin-import@>= 2.18.0", "eslint-plugin-import@>=1.4.0", "eslint-plugin-import@>=2.18.0":
  "integrity" "sha1-+BLcR75PK3K0eKAhYFpZ/G/ouIs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-plugin-import/-/eslint-plugin-import-2.26.0.tgz"
  "version" "2.26.0"
  dependencies:
    "array-includes" "^3.1.4"
    "array.prototype.flat" "^1.2.5"
    "debug" "^2.6.9"
    "doctrine" "^2.1.0"
    "eslint-import-resolver-node" "^0.3.6"
    "eslint-module-utils" "^2.7.3"
    "has" "^1.0.3"
    "is-core-module" "^2.8.1"
    "is-glob" "^4.0.3"
    "minimatch" "^3.1.2"
    "object.values" "^1.1.5"
    "resolve" "^1.22.0"
    "tsconfig-paths" "^3.14.1"

"eslint-plugin-node@^11.1.0", "eslint-plugin-node@>= 9.1.0", "eslint-plugin-node@>=9.1.0":
  "integrity" "sha1-yVVEQW7kraJnQKMEdO78VALcZx0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-plugin-node/-/eslint-plugin-node-11.1.0.tgz"
  "version" "11.1.0"
  dependencies:
    "eslint-plugin-es" "^3.0.0"
    "eslint-utils" "^2.0.0"
    "ignore" "^5.1.1"
    "minimatch" "^3.0.4"
    "resolve" "^1.10.1"
    "semver" "^6.1.0"

"eslint-plugin-promise@^4.2.1", "eslint-plugin-promise@>= 4.2.1", "eslint-plugin-promise@>=4.2.1":
  "integrity" "sha1-YUhd8qNZ4DFJ/a/AposOAwrSrEU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-plugin-promise/-/eslint-plugin-promise-4.3.1.tgz"
  "version" "4.3.1"

"eslint-plugin-standard@^4.0.0", "eslint-plugin-standard@>= 4.0.0", "eslint-plugin-standard@>=4.0.0":
  "integrity" "sha1-DDvzpn6FP4u7xYD7SUX78W9Bt8U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-plugin-standard/-/eslint-plugin-standard-4.1.0.tgz"
  "version" "4.1.0"

"eslint-plugin-vue@^6.2.2", "eslint-plugin-vue@>= 6.1.2":
  "integrity" "sha1-J/7NmjokeJsPER7N1UCp5WGY4P4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-plugin-vue/-/eslint-plugin-vue-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "natural-compare" "^1.4.0"
    "semver" "^5.6.0"
    "vue-eslint-parser" "^7.0.0"

"eslint-scope@^4.0.3":
  "integrity" "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-scope/-/eslint-scope-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-scope@^5.0.0":
  "integrity" "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-scope@^5.1.1":
  "integrity" "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-utils@^1.4.3":
  "integrity" "sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-utils/-/eslint-utils-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-utils@^2.0.0":
  "integrity" "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-utils/-/eslint-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-visitor-keys@^1.0.0", "eslint-visitor-keys@^1.1.0":
  "integrity" "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "eslint@^5.0.0 || ^6.0.0", "eslint@^6.7.2", "eslint@>= 1.6.0 < 7.0.0", "eslint@>= 4.12.1", "eslint@>=1.6.0 <7.0.0", "eslint@>=4.19.1", "eslint@>=5.0.0", "eslint@>=5.16.0", "eslint@>=6.2.2":
  "integrity" "sha1-YiYtZylzn5J1cjgkMC+yJ8jJP/s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eslint/-/eslint-6.8.0.tgz"
  "version" "6.8.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "ajv" "^6.10.0"
    "chalk" "^2.1.0"
    "cross-spawn" "^6.0.5"
    "debug" "^4.0.1"
    "doctrine" "^3.0.0"
    "eslint-scope" "^5.0.0"
    "eslint-utils" "^1.4.3"
    "eslint-visitor-keys" "^1.1.0"
    "espree" "^6.1.2"
    "esquery" "^1.0.1"
    "esutils" "^2.0.2"
    "file-entry-cache" "^5.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^5.0.0"
    "globals" "^12.1.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "inquirer" "^7.0.0"
    "is-glob" "^4.0.0"
    "js-yaml" "^3.13.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.3.0"
    "lodash" "^4.17.14"
    "minimatch" "^3.0.4"
    "mkdirp" "^0.5.1"
    "natural-compare" "^1.4.0"
    "optionator" "^0.8.3"
    "progress" "^2.0.0"
    "regexpp" "^2.0.1"
    "semver" "^6.1.2"
    "strip-ansi" "^5.2.0"
    "strip-json-comments" "^3.0.1"
    "table" "^5.2.3"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^6.1.2", "espree@^6.2.1":
  "integrity" "sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/espree/-/espree-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-jsx" "^5.2.0"
    "eslint-visitor-keys" "^1.1.0"

"esprima@^4.0.0":
  "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.0.1", "esquery@^1.4.0":
  "integrity" "sha1-IUj/w4uC6McFff7UhCWz5h8PJKU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/esquery/-/esquery-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.1.0", "esrecurse@^4.3.0":
  "integrity" "sha1-eteWTWeauyi+5yzsY3WLHF0smSE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0":
  "integrity" "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"estraverse@^5.2.0":
  "integrity" "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"esutils@^2.0.2":
  "integrity" "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-pubsub@4.3.0":
  "integrity" "sha1-9o2Ba8KfHsAsU53FjI3UDOcss24="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/event-pubsub/-/event-pubsub-4.3.0.tgz"
  "version" "4.3.0"

"eventemitter3@^4.0.0":
  "integrity" "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eventemitter3/-/eventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.0.0":
  "integrity" "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"eventsource@^2.0.2":
  "integrity" "sha1-dt/MApMPsv8zlSC20pDaVzqehQg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/eventsource/-/eventsource-2.0.2.tgz"
  "version" "2.0.2"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"execa@^0.8.0":
  "integrity" "sha1-2NdrvBtVIX7RkP1t1J08d07PyNo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/execa/-/execa-0.8.0.tgz"
  "version" "0.8.0"
  dependencies:
    "cross-spawn" "^5.0.1"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^1.0.0":
  "integrity" "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/execa/-/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^2.0.3":
  "integrity" "sha1-5dPs2DfSpg7FDz2nj9OXZ3R7vpk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/execa/-/execa-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^3.0.0"
    "onetime" "^5.1.0"
    "p-finally" "^2.0.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"execa@^3.3.0":
  "integrity" "sha1-wI7UVQ72XYWPrCaf/IVyRG8364k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/execa/-/execa-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "p-finally" "^2.0.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"exit@0.1.2", "exit@0.1.x":
  "integrity" "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/exit/-/exit-0.1.2.tgz"
  "version" "0.1.2"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/expand-brackets/-/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"express@^4.16.3", "express@^4.17.1":
  "integrity" "sha1-d5fei5xyyFe5zQ4Upe6oBmYmfK8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/express/-/express-4.18.1.tgz"
  "version" "4.18.1"
  dependencies:
    "accepts" "~1.3.8"
    "array-flatten" "1.1.1"
    "body-parser" "1.20.0"
    "content-disposition" "0.5.4"
    "content-type" "~1.0.4"
    "cookie" "0.5.0"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "1.2.0"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.7"
    "qs" "6.10.3"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.2.1"
    "send" "0.18.0"
    "serve-static" "1.15.0"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/extend-shallow/-/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@~3.0.0", "extend@~3.0.2", "extend@3":
  "integrity" "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"external-editor@^3.0.3":
  "integrity" "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/external-editor/-/external-editor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "chardet" "^0.7.0"
    "iconv-lite" "^0.4.24"
    "tmp" "^0.0.33"

"extglob@^2.0.2", "extglob@^2.0.4":
  "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/extglob/-/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extsprintf@^1.2.0", "extsprintf@1.3.0":
  "integrity" "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/extsprintf/-/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@^3.1.1":
  "integrity" "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-glob@^2.2.6":
  "integrity" "sha1-aVOFfDr6R1//ku5gFdUtpwpM050="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fast-glob/-/fast-glob-2.2.7.tgz"
  "version" "2.2.7"
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    "glob-parent" "^3.1.0"
    "is-glob" "^4.0.0"
    "merge2" "^1.2.3"
    "micromatch" "^3.1.10"

"fast-glob@^3.0.3":
  "integrity" "sha1-fznsmcLmqwMDNxQtqeDBjzevroA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fast-glob/-/fast-glob-3.2.12.tgz"
  "version" "3.2.12"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@~2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastclick@^1.0.6":
  "integrity" "sha1-FhYlsnsaWAZAWTa9qaLBkm0Gvmo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fastclick/-/fastclick-1.0.6.tgz"
  "version" "1.0.6"

"fastq@^1.6.0":
  "integrity" "sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fastq/-/fastq-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "reusify" "^1.0.4"

"faye-websocket@^0.11.3", "faye-websocket@^0.11.4":
  "integrity" "sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/faye-websocket/-/faye-websocket-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "websocket-driver" ">=0.5.1"

"figgy-pudding@^3.5.1":
  "integrity" "sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/figgy-pudding/-/figgy-pudding-3.5.2.tgz"
  "version" "3.5.2"

"figures@^1.7.0":
  "integrity" "sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/figures/-/figures-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"
    "object-assign" "^4.1.0"

"figures@^2.0.0":
  "integrity" "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/figures/-/figures-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"figures@^3.0.0":
  "integrity" "sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/figures/-/figures-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^5.0.1":
  "integrity" "sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/file-entry-cache/-/file-entry-cache-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "flat-cache" "^2.0.1"

"file-loader@*", "file-loader@^6.2.0":
  "integrity" "sha1-uu98+OGEDfMl5DkLRISHlIDuvk0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/file-loader/-/file-loader-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "loader-utils" "^2.0.0"
    "schema-utils" "^3.0.0"

"file-loader@^4.2.0":
  "integrity" "sha1-eA8ED3KbPRgBnyBgX3I+hEuKWK8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/file-loader/-/file-loader-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "loader-utils" "^1.2.3"
    "schema-utils" "^2.5.0"

"file-uri-to-path@1.0.0":
  "integrity" "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz"
  "version" "1.0.0"

"filesize@^3.6.1":
  "integrity" "sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/filesize/-/filesize-3.6.1.tgz"
  "version" "3.6.1"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fill-range/-/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha1-GRmmp8df44ssfHflGYU12prN2kA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@1.2.0":
  "integrity" "sha1-fSP+VzGyB7RkDk/NAK7B+SB6ezI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/finalhandler/-/finalhandler-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "statuses" "2.0.1"
    "unpipe" "~1.0.0"

"find-cache-dir@^0.1.1":
  "integrity" "sha1-yN765XyKUqinhPnjHFfHQumToLk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/find-cache-dir/-/find-cache-dir-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "commondir" "^1.0.1"
    "mkdirp" "^0.5.1"
    "pkg-dir" "^1.0.0"

"find-cache-dir@^2.1.0":
  "integrity" "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/find-cache-dir/-/find-cache-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^2.0.0"
    "pkg-dir" "^3.0.0"

"find-cache-dir@^3.0.0", "find-cache-dir@^3.3.1":
  "integrity" "sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/find-cache-dir/-/find-cache-dir-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-root@^1.1.0":
  "integrity" "sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/find-root/-/find-root-1.1.0.tgz"
  "version" "1.1.0"

"find-up@^1.0.0":
  "integrity" "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/find-up/-/find-up-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "path-exists" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"find-up@^3.0.0":
  "integrity" "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/find-up/-/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"find-up@^4.0.0", "find-up@^4.1.0":
  "integrity" "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"fingerprintjs2@^2.1.4":
  "integrity" "sha1-o53rlHqhh8CYMGoLXdQc6qLhX8U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fingerprintjs2/-/fingerprintjs2-2.1.4.tgz"
  "version" "2.1.4"

"flat-cache@^2.0.1":
  "integrity" "sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/flat-cache/-/flat-cache-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "flatted" "^2.0.0"
    "rimraf" "2.6.3"
    "write" "1.0.3"

"flatted@^2.0.0":
  "integrity" "sha1-RXWyHivO50NKqb5mL0t7X5wrUTg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/flatted/-/flatted-2.0.2.tgz"
  "version" "2.0.2"

"flush-write-stream@^1.0.0":
  "integrity" "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/flush-write-stream/-/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "readable-stream" "^2.3.6"

"follow-redirects@^1.0.0", "follow-redirects@^1.14.0":
  "integrity" "sha1-tGCGQUS6Y/JoEJbydMTlcCbaLBM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/follow-redirects/-/follow-redirects-1.15.2.tgz"
  "version" "1.15.2"

"for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/for-in/-/for-in-1.0.2.tgz"
  "version" "1.0.2"

"forever-agent@~0.6.1":
  "integrity" "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/forever-agent/-/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"form-data@^4.0.0":
  "integrity" "sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/form-data/-/form-data-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"form-data@~2.3.2":
  "integrity" "sha1-3M5SwF9kTymManq5Nr1yTO/786Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/form-data/-/form-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"forwarded@0.2.0":
  "integrity" "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/forwarded/-/forwarded-0.2.0.tgz"
  "version" "0.2.0"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fragment-cache/-/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"from2@^2.1.0":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/from2/-/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-extra@^0.26.5":
  "integrity" "sha1-muH92UiXeY7at20JGM9C0MMYT6k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fs-extra/-/fs-extra-0.26.7.tgz"
  "version" "0.26.7"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^2.1.0"
    "klaw" "^1.0.0"
    "path-is-absolute" "^1.0.0"
    "rimraf" "^2.2.8"

"fs-extra@^7.0.1":
  "integrity" "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fs-extra/-/fs-extra-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-minipass@^2.0.0":
  "integrity" "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fs-minipass/-/fs-minipass-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "minipass" "^3.0.0"

"fs-promise@0.5.0":
  "integrity" "sha1-Q0fWv2JGVacGGkMZITw5MnatPvM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fs-promise/-/fs-promise-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "any-promise" "^1.0.0"
    "fs-extra" "^0.26.5"
    "mz" "^2.3.1"
    "thenify-all" "^1.6.0"

"fs-write-stream-atomic@^1.0.8":
  "integrity" "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "^4.1.2"
    "iferr" "^0.1.5"
    "imurmurhash" "^0.1.4"
    "readable-stream" "1 || 2"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@^1.2.7":
  "integrity" "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fsevents/-/fsevents-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "bindings" "^1.5.0"
    "nan" "^2.12.1"

"fstream@^1.0.2":
  "integrity" "sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fstream/-/fstream-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "graceful-fs" "^4.1.2"
    "inherits" "~2.0.0"
    "mkdirp" ">=0.5 0"
    "rimraf" "2"

"function-bind@^1.1.1":
  "integrity" "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"function.prototype.name@^1.1.5":
  "integrity" "sha1-zOBQX+H/uAUD5vnkbMZORqEqliE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/function.prototype.name/-/function.prototype.name-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.0"
    "functions-have-names" "^1.2.2"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"functions-have-names@^1.2.2":
  "integrity" "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/functions-have-names/-/functions-have-names-1.2.3.tgz"
  "version" "1.2.3"

"fuzzysearch@^1.0.3":
  "integrity" "sha1-3/yA9tawQiPyImqnndGUIxCW0Ag="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/fuzzysearch/-/fuzzysearch-1.0.3.tgz"
  "version" "1.0.3"

"gensync@^1.0.0-beta.2":
  "integrity" "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1", "get-caller-file@^2.0.5":
  "integrity" "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.0", "get-intrinsic@^1.1.1", "get-intrinsic@^1.1.2":
  "integrity" "sha1-BjyEMprZPoOJPH9PJD72P/o1E4U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.3"

"get-own-enumerable-property-symbols@^3.0.0":
  "integrity" "sha1-tf3nfyLL4185C04ImSLFC85u9mQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz"
  "version" "3.0.2"

"get-stream@^3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/get-stream/-/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@^4.0.0":
  "integrity" "sha1-wbJVV189wh1Zv8ec09K0axw6VLU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/get-stream/-/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^5.0.0":
  "integrity" "sha1-SWaheV7lrOZecGxLe+txJX1uItM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/get-stream/-/get-stream-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "pump" "^3.0.0"

"get-symbol-description@^1.0.0":
  "integrity" "sha1-f9uByQAQH71WTdXxowr1qtweWNY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.1.1"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/get-value/-/get-value-2.0.6.tgz"
  "version" "2.0.6"

"getpass@^0.1.1":
  "integrity" "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/getpass/-/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-base@^0.3.0":
  "integrity" "sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob-base/-/glob-base-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "glob-parent" "^2.0.0"
    "is-glob" "^2.0.0"

"glob-parent@^2.0.0":
  "integrity" "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob-parent/-/glob-parent-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-glob" "^2.0.0"

"glob-parent@^3.1.0":
  "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob-parent/-/glob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob-parent@^5.0.0":
  "integrity" "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^5.1.2":
  "integrity" "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@~5.1.2":
  "integrity" "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-to-regexp@^0.3.0":
  "integrity" "sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob-to-regexp/-/glob-to-regexp-0.3.0.tgz"
  "version" "0.3.0"

"glob@^5.0.3":
  "integrity" "sha1-G8k2ueAvSmA/zCIuz3Yz0wuLk7E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob/-/glob-5.0.15.tgz"
  "version" "5.0.15"
  dependencies:
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "2 || 3"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@^7.0.3", "glob@^7.1.1", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4":
  "integrity" "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob/-/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@7.1.6":
  "integrity" "sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/glob/-/glob-7.1.6.tgz"
  "version" "7.1.6"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^11.1.0":
  "integrity" "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^12.1.0":
  "integrity" "sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/globals/-/globals-12.4.0.tgz"
  "version" "12.4.0"
  dependencies:
    "type-fest" "^0.8.1"

"globals@^9.18.0":
  "integrity" "sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/globals/-/globals-9.18.0.tgz"
  "version" "9.18.0"

"globby@^10.0.1":
  "integrity" "sha1-J3WT50WsqkZGw6tBEonsR6A5JUM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/globby/-/globby-10.0.2.tgz"
  "version" "10.0.2"
  dependencies:
    "@types/glob" "^7.1.1"
    "array-union" "^2.1.0"
    "dir-glob" "^3.0.1"
    "fast-glob" "^3.0.3"
    "glob" "^7.1.3"
    "ignore" "^5.1.1"
    "merge2" "^1.2.3"
    "slash" "^3.0.0"

"globby@^6.1.0":
  "integrity" "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/globby/-/globby-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "array-union" "^1.0.1"
    "glob" "^7.0.3"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"globby@^7.1.1":
  "integrity" "sha1-+yzP+UAfhgCUXfral0QMypcrhoA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/globby/-/globby-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "array-union" "^1.0.1"
    "dir-glob" "^2.0.0"
    "glob" "^7.1.2"
    "ignore" "^3.3.5"
    "pify" "^3.0.0"
    "slash" "^1.0.0"

"globby@^9.2.0":
  "integrity" "sha1-/QKacGxwPSm90XD0tts6P3p8tj0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/globby/-/globby-9.2.0.tgz"
  "version" "9.2.0"
  dependencies:
    "@types/glob" "^7.1.1"
    "array-union" "^1.0.2"
    "dir-glob" "^2.2.2"
    "fast-glob" "^2.2.6"
    "glob" "^7.1.3"
    "ignore" "^4.0.3"
    "pify" "^4.0.1"
    "slash" "^2.0.0"

"good-listener@^1.2.2":
  "integrity" "sha1-1TswzfkxPf+33JoNR3CWqm0UXFA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/good-listener/-/good-listener-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "delegate" "^3.1.2"

"got@5.3.1":
  "integrity" "sha1-det5autZdyav3aVyE0ywKtlvXYs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/got/-/got-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "create-error-class" "^2.0.0"
    "duplexer2" "^0.1.4"
    "is-plain-obj" "^1.0.0"
    "is-redirect" "^1.0.0"
    "is-stream" "^1.0.0"
    "lowercase-keys" "^1.0.0"
    "node-status-codes" "^1.0.0"
    "object-assign" "^4.0.1"
    "parse-json" "^2.1.0"
    "pinkie-promise" "^2.0.0"
    "read-all-stream" "^3.0.0"
    "readable-stream" "^2.0.5"
    "timed-out" "^2.0.0"
    "unzip-response" "^1.0.0"
    "url-parse-lax" "^1.0.0"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.15", "graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.1.9", "graceful-fs@^4.2.2":
  "integrity" "sha1-FH06AG2kyjzhRyjHrvwofDZ9emw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/graceful-fs/-/graceful-fs-4.2.10.tgz"
  "version" "4.2.10"

"gunzip-maybe@1.2.1":
  "integrity" "sha1-mqPc5TwnJRbECyphCiVSm/ZO4bw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/gunzip-maybe/-/gunzip-maybe-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "browserify-zlib" "^0.1.4"
    "peek-stream" "^1.1.0"
    "pumpify" "^1.3.3"
    "through2" "^0.4.1"

"gzip-size@^5.0.0":
  "integrity" "sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/gzip-size/-/gzip-size-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "duplexer" "^0.1.1"
    "pify" "^4.0.1"

"handle-thing@^2.0.0":
  "integrity" "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/handle-thing/-/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"har-schema@^2.0.0":
  "integrity" "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/har-schema/-/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~5.1.3":
  "integrity" "sha1-HwgDufjLIMD6E4It8ezds2veHv0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/har-validator/-/har-validator-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "ajv" "^6.12.3"
    "har-schema" "^2.0.0"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-ansi/-/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-bigints@^1.0.1", "has-bigints@^1.0.2":
  "integrity" "sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-bigints/-/has-bigints-1.0.2.tgz"
  "version" "1.0.2"

"has-binary2@~1.0.2":
  "integrity" "sha1-d3asYn8+p3JQz8My2rfd9eT10R0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-binary2/-/has-binary2-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "isarray" "2.0.1"

"has-color@~0.1.0":
  "integrity" "sha1-ZxRKUmDDT8PMpnfQQdr1L+e3iy8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-color/-/has-color-0.1.7.tgz"
  "version" "0.1.7"

"has-cors@1.1.0":
  "integrity" "sha1-XkdHk/fqmEPRu5nCPu9J/xJv/zk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-cors/-/has-cors-1.1.0.tgz"
  "version" "1.1.0"

"has-flag@^1.0.0":
  "integrity" "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-flag/-/has-flag-1.0.0.tgz"
  "version" "1.0.0"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-property-descriptors@^1.0.0":
  "integrity" "sha1-YQcIYAYG02lh7QTBlhk7amB/qGE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-intrinsic" "^1.1.1"

"has-symbols@^1.0.1", "has-symbols@^1.0.2", "has-symbols@^1.0.3":
  "integrity" "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz"
  "version" "1.0.3"

"has-tostringtag@^1.0.0":
  "integrity" "sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-value/-/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-value/-/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-values/-/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has-values/-/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.0", "has@^1.0.3":
  "integrity" "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hash-base/-/hash-base-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "inherits" "^2.0.4"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"hash-sum@^1.0.2":
  "integrity" "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hash-sum/-/hash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash-sum@^2.0.0":
  "integrity" "sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hash-sum/-/hash-sum-2.0.0.tgz"
  "version" "2.0.0"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hash.js/-/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"he@^1.1.0", "he@^1.1.1", "he@1.2.x":
  "integrity" "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/he/-/he-1.2.0.tgz"
  "version" "1.2.0"

"hex-color-regex@^1.1.0":
  "integrity" "sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hex-color-regex/-/hex-color-regex-1.1.0.tgz"
  "version" "1.1.0"

"highlight.js@^10.7.1":
  "integrity" "sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/highlight.js/-/highlight.js-10.7.3.tgz"
  "version" "10.7.3"

"hmac-drbg@^1.0.1":
  "integrity" "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hmac-drbg/-/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"hoopy@^0.1.4":
  "integrity" "sha1-YJIH1mEQADOpqUAq096mdzgcGx0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hoopy/-/hoopy-0.1.4.tgz"
  "version" "0.1.4"

"hosted-git-info@^2.1.4":
  "integrity" "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hpack.js@^2.1.6":
  "integrity" "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hpack.js/-/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"hsl-regex@^1.0.0":
  "integrity" "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hsl-regex/-/hsl-regex-1.0.0.tgz"
  "version" "1.0.0"

"hsla-regex@^1.0.0":
  "integrity" "sha1-wc56MWjIxmFAM6S194d/OyJfnDg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hsla-regex/-/hsla-regex-1.0.0.tgz"
  "version" "1.0.0"

"html-entities@^1.3.1":
  "integrity" "sha1-z70bAdKvr5rcobEK59/6uYxx0tw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/html-entities/-/html-entities-1.4.0.tgz"
  "version" "1.4.0"

"html-minifier@^3.2.3":
  "integrity" "sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/html-minifier/-/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.x"
    "clean-css" "4.2.x"
    "commander" "2.17.x"
    "he" "1.2.x"
    "param-case" "2.1.x"
    "relateurl" "0.2.x"
    "uglify-js" "3.4.x"

"html-tags@^2.0.0":
  "integrity" "sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/html-tags/-/html-tags-2.0.0.tgz"
  "version" "2.0.0"

"html-tags@^3.1.0":
  "integrity" "sha1-27NRjSC3JlJOTdQ945frCpVyaWE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/html-tags/-/html-tags-3.2.0.tgz"
  "version" "3.2.0"

"html-webpack-plugin@^3.2.0", "html-webpack-plugin@>=2.26.0":
  "integrity" "sha1-sBq71yOsqqeze2r0SS69oD2d03s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/html-webpack-plugin/-/html-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "html-minifier" "^3.2.3"
    "loader-utils" "^0.2.16"
    "lodash" "^4.17.3"
    "pretty-error" "^2.0.2"
    "tapable" "^1.0.0"
    "toposort" "^1.0.0"
    "util.promisify" "1.0.0"

"html2canvas@^1.0.0-rc.7":
  "integrity" "sha1-fO8YiDEbUBHVB3lKBmBBsUZppUM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/html2canvas/-/html2canvas-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "css-line-break" "^2.1.0"
    "text-segmentation" "^1.0.3"

"htmlhint@^0.14.2":
  "integrity" "sha1-jFW4U5C6ENUSvVk687F1raBvXqg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/htmlhint/-/htmlhint-0.14.2.tgz"
  "version" "0.14.2"
  dependencies:
    "async" "3.2.0"
    "chalk" "4.1.0"
    "commander" "5.1.0"
    "glob" "7.1.6"
    "parse-glob" "3.0.4"
    "request" "2.88.2"
    "strip-json-comments" "3.1.0"
    "xml" "1.0.1"

"htmlparser2@^3.8.3", "htmlparser2@3.8.x":
  "integrity" "sha1-mWwosZFRaovoZQGn15dX5ccMEGg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/htmlparser2/-/htmlparser2-3.8.3.tgz"
  "version" "3.8.3"
  dependencies:
    "domelementtype" "1"
    "domhandler" "2.3"
    "domutils" "1.5"
    "entities" "1.0"
    "readable-stream" "1.1"

"htmlparser2@^6.1.0":
  "integrity" "sha1-xNditsM3GgXb5l6UrkOp+EX7j7c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/htmlparser2/-/htmlparser2-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.0.0"
    "domutils" "^2.5.2"
    "entities" "^2.0.0"

"http-deceiver@^1.2.7":
  "integrity" "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/http-deceiver/-/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/http-errors/-/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@2.0.0":
  "integrity" "sha1-t3dKFIbvc892Z6ya4IWMASxXudM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/http-errors/-/http-errors-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "depd" "2.0.0"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "toidentifier" "1.0.1"

"http-parser-js@>=0.5.1":
  "integrity" "sha1-ryMJDZrE4kVz3m9q7MnYSki/IOM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/http-parser-js/-/http-parser-js-0.5.8.tgz"
  "version" "0.5.8"

"http-proxy-middleware@^1.0.0":
  "integrity" "sha1-Q3ANbZ7st0Gb8IahKND3IF2etmU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/http-proxy-middleware/-/http-proxy-middleware-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "@types/http-proxy" "^1.17.5"
    "http-proxy" "^1.18.1"
    "is-glob" "^4.0.1"
    "is-plain-obj" "^3.0.0"
    "micromatch" "^4.0.2"

"http-proxy-middleware@0.19.1":
  "integrity" "sha1-GDx9xKoUeRUDBkmMIQza+WCApDo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/http-proxy-middleware/-/http-proxy-middleware-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "http-proxy" "^1.17.0"
    "is-glob" "^4.0.0"
    "lodash" "^4.17.11"
    "micromatch" "^3.1.10"

"http-proxy@^1.17.0", "http-proxy@^1.18.1":
  "integrity" "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/http-proxy/-/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"http-signature@~1.2.0":
  "integrity" "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/http-signature/-/http-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"https-browserify@^1.0.0":
  "integrity" "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/https-browserify/-/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"https-proxy-agent@1.0.0":
  "integrity" "sha1-NffabEjOTdv6JkiRrFk+5f+GceY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/https-proxy-agent/-/https-proxy-agent-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "agent-base" "2"
    "debug" "2"
    "extend" "3"

"human-signals@^1.1.1":
  "integrity" "sha1-xbHNFPUK6uCatsWf5jujOV/k36M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/human-signals/-/human-signals-1.1.1.tgz"
  "version" "1.1.1"

"hz-message@^1.2.0":
  "integrity" "sha1-fBtaoYJBanaVhhFmKZZD3yTFJ+I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hz-message/-/hz-message-1.2.0.tgz"
  "version" "1.2.0"

"hz-product-list@^1.1.10":
  "integrity" "sha1-+yXu6UmRZqgllGywNGmRkWuLkNk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hz-product-list/-/hz-product-list-1.1.11.tgz"
  "version" "1.1.11"

"hz-quark@^2.0.6":
  "integrity" "sha1-8DGA7Zwf2LSMkUcenBBwOtQW2TY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hz-quark/-/hz-quark-2.0.6.tgz"
  "version" "2.0.6"

"hz-user@^1.1.7":
  "integrity" "sha1-/oiueJgLZ+fW5fOpyHX4ySik7Qc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/hz-user/-/hz-user-1.1.7.tgz"
  "version" "1.1.7"

"iconv-lite@^0.4.24", "iconv-lite@0.4.24":
  "integrity" "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-utils@^4.0.0", "icss-utils@^4.1.1":
  "integrity" "sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/icss-utils/-/icss-utils-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "postcss" "^7.0.14"

"ieee754@^1.1.4":
  "integrity" "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"iferr@^0.1.5":
  "integrity" "sha1-xg7taebY/bazEEofy8ocGS3FtQE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/iferr/-/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore@^3.3.5":
  "integrity" "sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ignore/-/ignore-3.3.10.tgz"
  "version" "3.3.10"

"ignore@^4.0.3", "ignore@^4.0.6":
  "integrity" "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ignore/-/ignore-4.0.6.tgz"
  "version" "4.0.6"

"ignore@^5.1.1":
  "integrity" "sha1-bTusj6f+DUXZ+b57rC/CeVd+NFo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ignore/-/ignore-5.2.0.tgz"
  "version" "5.2.0"

"image-size@^0.5.1":
  "integrity" "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"immutable@^4.0.0":
  "integrity" "sha1-95V4fw23gBgzB7nrIJH8rB9vr+8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/immutable/-/immutable-4.1.0.tgz"
  "version" "4.1.0"

"import-cwd@^2.0.0":
  "integrity" "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/import-cwd/-/import-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "import-from" "^2.1.0"

"import-fresh@^2.0.0":
  "integrity" "sha1-2BNVwVYS04bGH53dOSLUMEgipUY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/import-fresh/-/import-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-fresh@^3.0.0":
  "integrity" "sha1-NxYsJfy566oublPVtNiM4X2eDCs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-from@^2.1.0":
  "integrity" "sha1-M1238qev/VOqpHHUuAId7ja387E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/import-from/-/import-from-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "resolve-from" "^3.0.0"

"import-local@^2.0.0":
  "integrity" "sha1-VQcL44pZk88Y72236WH1vuXFoJ0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/import-local/-/import-local-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pkg-dir" "^3.0.0"
    "resolve-cwd" "^2.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^3.0.0":
  "integrity" "sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/indent-string/-/indent-string-3.2.0.tgz"
  "version" "3.2.0"

"indent-string@^4.0.0":
  "integrity" "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/indent-string/-/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"indexes-of@^1.0.1":
  "integrity" "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/indexes-of/-/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"indexof@0.0.1":
  "integrity" "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/indexof/-/indexof-0.0.1.tgz"
  "version" "0.0.1"

"infer-owner@^1.0.3", "infer-owner@^1.0.4":
  "integrity" "sha1-xM78qo5RBRwqQLos6KPScpWvlGc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/infer-owner/-/infer-owner-1.0.4.tgz"
  "version" "1.0.4"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.0", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.1":
  "integrity" "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/inherits/-/inherits-2.0.1.tgz"
  "version" "2.0.1"

"inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"init-package-json@1.9.1":
  "integrity" "sha1-oo4FtbrrM2PNRz32jTDTqAUjoxw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/init-package-json/-/init-package-json-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "glob" "^5.0.3"
    "npm-package-arg" "^4.0.0"
    "promzard" "^0.3.0"
    "read" "~1.0.1"
    "read-package-json" "1 || 2"
    "semver" "2.x || 3.x || 4 || 5"
    "validate-npm-package-license" "^3.0.1"
    "validate-npm-package-name" "^2.0.1"

"inquirer@^7.0.0", "inquirer@^7.1.0":
  "integrity" "sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/inquirer/-/inquirer-7.3.3.tgz"
  "version" "7.3.3"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-width" "^3.0.0"
    "external-editor" "^3.0.3"
    "figures" "^3.0.0"
    "lodash" "^4.17.19"
    "mute-stream" "0.0.8"
    "run-async" "^2.4.0"
    "rxjs" "^6.6.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "through" "^2.3.6"

"internal-ip@^4.3.0":
  "integrity" "sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/internal-ip/-/internal-ip-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "default-gateway" "^4.2.0"
    "ipaddr.js" "^1.9.0"

"internal-slot@^1.0.3":
  "integrity" "sha1-c0fjB97uovqsKsYgXUvH00ln9Zw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/internal-slot/-/internal-slot-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "get-intrinsic" "^1.1.0"
    "has" "^1.0.3"
    "side-channel" "^1.0.4"

"interpret@^1.2.0":
  "integrity" "sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/interpret/-/interpret-1.4.0.tgz"
  "version" "1.4.0"

"invariant@^2.2.2":
  "integrity" "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/invariant/-/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"ip-regex@^2.1.0":
  "integrity" "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ip-regex/-/ip-regex-2.1.0.tgz"
  "version" "2.1.0"

"ip@^1.1.0", "ip@^1.1.5":
  "integrity" "sha1-rgWUj2sHVDXtMweszgRinajNv0g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ip/-/ip-1.1.8.tgz"
  "version" "1.1.8"

"ipaddr.js@^1.9.0", "ipaddr.js@1.9.1":
  "integrity" "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-absolute-url@^2.0.0":
  "integrity" "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-absolute-url/-/is-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-absolute-url@^3.0.3":
  "integrity" "sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-absolute-url/-/is-absolute-url-3.0.3.tgz"
  "version" "3.0.3"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arguments@^1.0.4":
  "integrity" "sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-arguments/-/is-arguments-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@^0.3.1":
  "integrity" "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-arrayish/-/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-bigint@^1.0.1":
  "integrity" "sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-bigint/-/is-bigint-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-bigints" "^1.0.1"

"is-binary-path@^1.0.0":
  "integrity" "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-binary-path/-/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-binary-path@~2.1.0":
  "integrity" "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-boolean-object@^1.1.0":
  "integrity" "sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha1-76ouqdqg16suoTqXsritUf776L4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.4", "is-callable@^1.2.4":
  "integrity" "sha1-/WFwsLjH4sxz3jQu+ChKIgICPEQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.6.tgz"
  "version" "1.2.6"

"is-ci@^1.0.10":
  "integrity" "sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-ci/-/is-ci-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ci-info" "^1.5.0"

"is-color-stop@^1.0.0":
  "integrity" "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-color-stop/-/is-color-stop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-color-names" "^0.0.4"
    "hex-color-regex" "^1.1.0"
    "hsl-regex" "^1.0.0"
    "hsla-regex" "^1.0.0"
    "rgb-regex" "^1.0.1"
    "rgba-regex" "^1.0.0"

"is-core-module@^2.8.1", "is-core-module@^2.9.0":
  "integrity" "sha1-kBLt4KkcaVh+ZHUU4dUncBnnKO0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-core-module/-/is-core-module-2.10.0.tgz"
  "version" "2.10.0"
  dependencies:
    "has" "^1.0.3"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-date-object/-/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-descriptor@^0.1.0":
  "integrity" "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-descriptor/-/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-descriptor@^1.0.2":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-directory/-/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-docker@^2.0.0":
  "integrity" "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-docker/-/is-docker-2.2.1.tgz"
  "version" "2.2.1"

"is-dotfile@^1.0.0":
  "integrity" "sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-dotfile/-/is-dotfile-1.0.3.tgz"
  "version" "1.0.3"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-extendable/-/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^1.0.0":
  "integrity" "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-extglob/-/is-extglob-1.0.0.tgz"
  "version" "1.0.0"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^1.0.0":
  "integrity" "sha1-754xOG8DGn8NZDr4L95QxFfvAMs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^2.0.0":
  "integrity" "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-glob/-/is-glob-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extglob" "^1.0.0"

"is-glob@^3.1.0":
  "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-glob/-/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@~4.0.1":
  "integrity" "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-glob@^4.0.3":
  "integrity" "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-negative-zero@^2.0.2":
  "integrity" "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
  "version" "2.0.2"

"is-number-object@^1.0.4":
  "integrity" "sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-number-object/-/is-number-object-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-number/-/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^1.0.1":
  "integrity" "sha1-PkcprB9f3gJc19g6iW2rn09n2w8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-obj/-/is-obj-1.0.1.tgz"
  "version" "1.0.1"

"is-obj@^2.0.0":
  "integrity" "sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-obj/-/is-obj-2.0.0.tgz"
  "version" "2.0.0"

"is-observable@^1.1.0":
  "integrity" "sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-observable/-/is-observable-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "symbol-observable" "^1.1.0"

"is-path-cwd@^2.0.0", "is-path-cwd@^2.2.0":
  "integrity" "sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-path-cwd/-/is-path-cwd-2.2.0.tgz"
  "version" "2.2.0"

"is-path-in-cwd@^2.0.0":
  "integrity" "sha1-v+Lcomxp85cmWkAJljYCk1oFOss="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "is-path-inside" "^2.1.0"

"is-path-inside@^2.1.0":
  "integrity" "sha1-fJgQWH1lmkDSe8201WFuqwWUlLI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-path-inside/-/is-path-inside-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "path-is-inside" "^1.0.2"

"is-path-inside@^3.0.1":
  "integrity" "sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-path-inside/-/is-path-inside-3.0.3.tgz"
  "version" "3.0.3"

"is-plain-obj@^1.0.0", "is-plain-obj@^1.1":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-obj@^3.0.0":
  "integrity" "sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
  "version" "3.0.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-promise@^2.1.0":
  "integrity" "sha1-OauVnMv5p3TPB597QMeib3YxNfE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-promise/-/is-promise-2.2.2.tgz"
  "version" "2.2.2"

"is-redirect@^1.0.0":
  "integrity" "sha1-HQPd7VO9jbDzDCbk+V02/HyH3CQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-redirect/-/is-redirect-1.0.0.tgz"
  "version" "1.0.0"

"is-regex@^1.0.4", "is-regex@^1.1.4":
  "integrity" "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-regexp@^1.0.0":
  "integrity" "sha1-/S2INUXEa6xaYz57mgnof6LLUGk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-regexp/-/is-regexp-1.0.0.tgz"
  "version" "1.0.0"

"is-resolvable@^1.0.0":
  "integrity" "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-resolvable/-/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-shared-array-buffer@^1.0.2":
  "integrity" "sha1-jyWcVztgtqMtQFihoHQwwKc0THk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-stream@^1.0.0", "is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-stream/-/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha1-+sHj1TuXrVqdCunO8jifWBClwHc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-string@^1.0.5", "is-string@^1.0.7":
  "integrity" "sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-string/-/is-string-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha1-ptrJO2NbBjymhyI23oiRClevE5w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-symbol/-/is-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-typedarray@~1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-weakref@^1.0.2":
  "integrity" "sha1-lSnzg6kzggXol2XgOS78LxAPBvI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-weakref/-/is-weakref-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-windows@^1.0.2":
  "integrity" "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-windows/-/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-wsl/-/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"is-wsl@^2.1.1":
  "integrity" "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/is-wsl/-/is-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@^1.0.0", "isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isarray@0.0.1":
  "integrity" "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/isarray/-/isarray-0.0.1.tgz"
  "version" "0.0.1"

"isarray@2.0.1":
  "integrity" "sha1-o32U7ZzaLVmGXJ92/llu4fM4dB4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/isarray/-/isarray-2.0.1.tgz"
  "version" "2.0.1"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^2.1.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isstream@~0.1.2":
  "integrity" "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/isstream/-/isstream-0.1.2.tgz"
  "version" "0.1.2"

"javascript-natural-sort@^0.7.1":
  "integrity" "sha1-+eIwPUUH9tdDVac2ZNFED7Wg71k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/javascript-natural-sort/-/javascript-natural-sort-0.7.1.tgz"
  "version" "0.7.1"

"javascript-stringify@^2.0.1":
  "integrity" "sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/javascript-stringify/-/javascript-stringify-2.1.0.tgz"
  "version" "2.1.0"

"jmespath@^0.16.0":
  "integrity" "sha1-sVsKhd/U2TDUPmntYFlDyAJ4UHY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jmespath/-/jmespath-0.16.0.tgz"
  "version" "0.16.0"

"js-audio-recorder@^1.0.7":
  "integrity" "sha1-AVAcuesUPVeZS0K9Wul5dP2hiQg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/js-audio-recorder/-/js-audio-recorder-1.0.7.tgz"
  "version" "1.0.7"

"js-base64@^2.1.9":
  "integrity" "sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/js-base64/-/js-base64-2.6.4.tgz"
  "version" "2.6.4"

"js-base64@^3.6.0":
  "integrity" "sha1-gW0R2BqK/yQWA9Gc5XYeE+Qdd0U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/js-base64/-/js-base64-3.7.2.tgz"
  "version" "3.7.2"

"js-message@1.0.7":
  "integrity" "sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/js-message/-/js-message-1.0.7.tgz"
  "version" "1.0.7"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-tokens@^3.0.2":
  "integrity" "sha1-mGbfOVECEw449/mWvOtlRDIJwls="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/js-tokens/-/js-tokens-3.0.2.tgz"
  "version" "3.0.2"

"js-yaml@^3.13.1":
  "integrity" "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsbn@~0.1.0":
  "integrity" "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jsbn/-/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsesc@^2.5.1":
  "integrity" "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"jshint@^2.13.4":
  "integrity" "sha1-NGVLRDh+8RKznCBeLnC5k4U3ZXk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jshint/-/jshint-2.13.5.tgz"
  "version" "2.13.5"
  dependencies:
    "cli" "~1.0.0"
    "console-browserify" "1.1.x"
    "exit" "0.1.x"
    "htmlparser2" "3.8.x"
    "lodash" "~4.17.21"
    "minimatch" "~3.0.2"
    "strip-json-comments" "1.0.x"

"json-parse-better-errors@^1.0.1", "json-parse-better-errors@^1.0.2":
  "integrity" "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-parse-even-better-errors@^2.3.0":
  "integrity" "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema@0.4.0":
  "integrity" "sha1-995M9u+rg4666zI2R0y7paGTCrU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json-schema/-/json-schema-0.4.0.tgz"
  "version" "0.4.0"

"json-source-map@^0.6.1":
  "integrity" "sha1-4LH29M4Tqa1X4q4WWiTQbmLHmg8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json-source-map/-/json-source-map-0.6.1.tgz"
  "version" "0.6.1"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json-stringify-safe@~5.0.1":
  "integrity" "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json5@^0.5.0":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json5/-/json5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^1.0.1":
  "integrity" "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json5/-/json5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.2":
  "integrity" "sha1-ZV1Q7R5vla0aPKq6vSsO/aELOVw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json5/-/json5-2.2.1.tgz"
  "version" "2.2.1"

"json5@^2.2.1":
  "integrity" "sha1-ZV1Q7R5vla0aPKq6vSsO/aELOVw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/json5/-/json5-2.2.1.tgz"
  "version" "2.2.1"

"jsoneditor@^9.4.0":
  "integrity" "sha1-F2ES9xrL836Xeq6GVqF7wzp6Qmk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jsoneditor/-/jsoneditor-9.9.2.tgz"
  "version" "9.9.2"
  dependencies:
    "ace-builds" "^1.10.1"
    "ajv" "^6.12.6"
    "javascript-natural-sort" "^0.7.1"
    "jmespath" "^0.16.0"
    "json-source-map" "^0.6.1"
    "jsonrepair" "^2.2.1"
    "mobius1-selectr" "^2.4.13"
    "picomodal" "^3.0.0"
    "vanilla-picker" "^2.12.1"

"jsonfile@^2.1.0":
  "integrity" "sha1-NzaitCi4e72gzIO1P6PWM6NcKug="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jsonfile/-/jsonfile-2.4.0.tgz"
  "version" "2.4.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonfile@^4.0.0":
  "integrity" "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jsonfile/-/jsonfile-4.0.0.tgz"
  "version" "4.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonlint@^1.6.3":
  "integrity" "sha1-y14x78C3gpHQ2GL77wWQCt8hKYg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jsonlint/-/jsonlint-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "JSV" "^4.0.x"
    "nomnom" "^1.5.x"

"jsonrepair@^2.2.1":
  "integrity" "sha1-fGJXw2VQoxAVDEGrfV1Mq3GChFY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jsonrepair/-/jsonrepair-2.2.1.tgz"
  "version" "2.2.1"

"jsprim@^1.2.2":
  "integrity" "sha1-cSxlUzoVyHi6WentXw4m1bd8X+s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/jsprim/-/jsprim-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.4.0"
    "verror" "1.10.0"

"JSV@^4.0.x":
  "integrity" "sha1-0Hf2glVx+CEy+d/67Vh7QCn+/1c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/JSV/-/JSV-4.0.2.tgz"
  "version" "4.0.2"

"keymaster@^1.6.2":
  "integrity" "sha1-4a5U0OqUiPn2C2a2aPAumhlGxus="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/keymaster/-/keymaster-1.6.2.tgz"
  "version" "1.6.2"

"killable@^1.0.1":
  "integrity" "sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/killable/-/killable-1.0.1.tgz"
  "version" "1.0.1"

"kind-of@^3.0.2":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.0.3":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/kind-of/-/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/kind-of/-/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^5.0.2":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/kind-of/-/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"klaw@^1.0.0":
  "integrity" "sha1-QIhDO0azsbolnXh4XY6W9zugJDk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/klaw/-/klaw-1.3.1.tgz"
  "version" "1.3.1"
  optionalDependencies:
    "graceful-fs" "^4.1.9"

"launch-editor-middleware@^2.2.1":
  "integrity" "sha1-K6T+S2ldf+PUTe6GttRtV7gzLf0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/launch-editor-middleware/-/launch-editor-middleware-2.6.0.tgz"
  "version" "2.6.0"
  dependencies:
    "launch-editor" "^2.6.0"

"launch-editor@^2.2.1", "launch-editor@^2.6.0":
  "integrity" "sha1-TAwaasEmxXK9n/mjDaHSyuZt79c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/launch-editor/-/launch-editor-2.6.0.tgz"
  "version" "2.6.0"
  dependencies:
    "picocolors" "^1.0.0"
    "shell-quote" "^1.7.3"

"levn@^0.3.0", "levn@~0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/levn/-/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"lines-and-columns@^1.1.6":
  "integrity" "sha1-7KKE910pZQeTCdwK2SVauy68FjI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"lint-staged@^9.5.0":
  "integrity" "sha1-KQ7GBSUq9kbZt01zoPoRg2KwWjM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lint-staged/-/lint-staged-9.5.0.tgz"
  "version" "9.5.0"
  dependencies:
    "chalk" "^2.4.2"
    "commander" "^2.20.0"
    "cosmiconfig" "^5.2.1"
    "debug" "^4.1.1"
    "dedent" "^0.7.0"
    "del" "^5.0.0"
    "execa" "^2.0.3"
    "listr" "^0.14.3"
    "log-symbols" "^3.0.0"
    "micromatch" "^4.0.2"
    "normalize-path" "^3.0.0"
    "please-upgrade-node" "^3.1.1"
    "string-argv" "^0.3.0"
    "stringify-object" "^3.3.0"

"listr-silent-renderer@^1.1.1":
  "integrity" "sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/listr-silent-renderer/-/listr-silent-renderer-1.1.1.tgz"
  "version" "1.1.1"

"listr-update-renderer@^0.5.0":
  "integrity" "sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/listr-update-renderer/-/listr-update-renderer-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "chalk" "^1.1.3"
    "cli-truncate" "^0.2.1"
    "elegant-spinner" "^1.0.1"
    "figures" "^1.7.0"
    "indent-string" "^3.0.0"
    "log-symbols" "^1.0.2"
    "log-update" "^2.3.0"
    "strip-ansi" "^3.0.1"

"listr-verbose-renderer@^0.5.0":
  "integrity" "sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/listr-verbose-renderer/-/listr-verbose-renderer-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "chalk" "^2.4.1"
    "cli-cursor" "^2.1.0"
    "date-fns" "^1.27.2"
    "figures" "^2.0.0"

"listr@^0.14.2", "listr@^0.14.3":
  "integrity" "sha1-L+qQlgTkNL5GTFC926DUlpKPpYY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/listr/-/listr-0.14.3.tgz"
  "version" "0.14.3"
  dependencies:
    "@samverschueren/stream-to-observable" "^0.3.0"
    "is-observable" "^1.1.0"
    "is-promise" "^2.1.0"
    "is-stream" "^1.1.0"
    "listr-silent-renderer" "^1.1.1"
    "listr-update-renderer" "^0.5.0"
    "listr-verbose-renderer" "^0.5.0"
    "p-map" "^2.0.0"
    "rxjs" "^6.3.3"

"loader-fs-cache@^1.0.0":
  "integrity" "sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/loader-fs-cache/-/loader-fs-cache-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "find-cache-dir" "^0.1.1"
    "mkdirp" "^0.5.1"

"loader-runner@^2.3.1", "loader-runner@^2.4.0":
  "integrity" "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/loader-runner/-/loader-runner-2.4.0.tgz"
  "version" "2.4.0"

"loader-utils@^0.2.16":
  "integrity" "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/loader-utils/-/loader-utils-0.2.17.tgz"
  "version" "0.2.17"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"
    "object-assign" "^4.0.1"

"loader-utils@^1.0.0", "loader-utils@^1.0.2", "loader-utils@^1.1.0", "loader-utils@^1.2.3", "loader-utils@^1.4.0":
  "integrity" "sha1-xXm140yzSxp07cbB+za/o3HVphM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/loader-utils/-/loader-utils-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^2.0.0":
  "integrity" "sha1-1uO0+4GHByGuTghoqxHdY4NowSk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/loader-utils/-/loader-utils-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"locate-path@^3.0.0":
  "integrity" "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/locate-path/-/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"locate-path@^5.0.0":
  "integrity" "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.defaultsdeep@^4.6.1":
  "integrity" "sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lodash.defaultsdeep/-/lodash.defaultsdeep-4.6.1.tgz"
  "version" "4.6.1"

"lodash.kebabcase@^4.1.1":
  "integrity" "sha1-hImxyw0p/4gZXM7KRI/21swpXDY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.mapvalues@^4.6.0":
  "integrity" "sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lodash.mapvalues/-/lodash.mapvalues-4.6.0.tgz"
  "version" "4.6.0"

"lodash.memoize@^4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.transform@^4.6.0":
  "integrity" "sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lodash.transform/-/lodash.transform-4.6.0.tgz"
  "version" "4.6.0"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.0.0", "lodash@^4.17.11", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.19", "lodash@^4.17.20", "lodash@^4.17.21", "lodash@^4.17.3", "lodash@^4.17.4", "lodash@^4.2.0", "lodash@~4.17.21":
  "integrity" "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^1.0.2":
  "integrity" "sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/log-symbols/-/log-symbols-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "chalk" "^1.0.0"

"log-symbols@^2.2.0":
  "integrity" "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/log-symbols/-/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"log-symbols@^3.0.0":
  "integrity" "sha1-86CFFqXeqJMzan3uFNGKHP2rd8Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/log-symbols/-/log-symbols-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "chalk" "^2.4.2"

"log-update@^2.3.0":
  "integrity" "sha1-iDKP19HOeTiykoN0bwsbwSayRwg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/log-update/-/log-update-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "cli-cursor" "^2.0.0"
    "wrap-ansi" "^3.0.1"

"loglevel@^1.6.8":
  "integrity" "sha1-5+xzpX4ee0GctsasBr8FC2c1YRQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/loglevel/-/loglevel-1.8.0.tgz"
  "version" "1.8.0"

"loose-envify@^1.0.0":
  "integrity" "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lower-case@^1.1.1":
  "integrity" "sha1-miyr0bno4K6ZOkv31YdcOcQujqw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lower-case/-/lower-case-1.1.4.tgz"
  "version" "1.1.4"

"lowercase-keys@^1.0.0":
  "integrity" "sha1-b54wtHCE2XGnyCD/FabFFnt0wm8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lowercase-keys/-/lowercase-keys-1.0.1.tgz"
  "version" "1.0.1"

"lru-cache@^4.0.1":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lru-cache/-/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^4.1.2":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lru-cache/-/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^5.1.1":
  "integrity" "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lru-cache@^6.0.0":
  "integrity" "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"make-dir@^2.0.0":
  "integrity" "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^3.0.2", "make-dir@^3.1.0":
  "integrity" "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/make-dir/-/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/map-cache/-/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/map-visit/-/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"material-colors@^1.2.6":
  "integrity" "sha1-bRlYhxEmmSzuzHL0vMTY8BCGX0Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/material-colors/-/material-colors-1.2.6.tgz"
  "version" "1.2.6"

"md5.js@^1.3.4":
  "integrity" "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/md5.js/-/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"mdn-data@2.0.14":
  "integrity" "sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mdn-data/-/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"mdn-data@2.0.4":
  "integrity" "sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mdn-data/-/mdn-data-2.0.4.tgz"
  "version" "2.0.4"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memory-fs@^0.2.0":
  "integrity" "sha1-8rslNovBIeORwlIN6Slpyu4KApA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/memory-fs/-/memory-fs-0.2.0.tgz"
  "version" "0.2.0"

"memory-fs@^0.4.1":
  "integrity" "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/memory-fs/-/memory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"memory-fs@^0.5.0":
  "integrity" "sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/memory-fs/-/memory-fs-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"merge-options@1.0.1":
  "integrity" "sha1-KmSyRFe+zU5NxggoMkfpTOWJqjI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/merge-options/-/merge-options-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-obj" "^1.1"

"merge-source-map@^1.1.0":
  "integrity" "sha1-L93n5gIJOfcJBqaPLXrmheTIxkY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/merge-source-map/-/merge-source-map-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "source-map" "^0.6.1"

"merge-stream@^2.0.0":
  "integrity" "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.2.3", "merge2@^1.3.0":
  "integrity" "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^3.1.10", "micromatch@^3.1.4":
  "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/micromatch/-/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^4.0.2", "micromatch@^4.0.4":
  "integrity" "sha1-vImZp8u/d83InxMvbkZwUbSQkMY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/micromatch/-/micromatch-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "braces" "^3.0.2"
    "picomatch" "^2.3.1"

"micromatch@3.1.0":
  "integrity" "sha1-UQLU6vILaZfWAI46z+HESj+oFeI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/micromatch/-/micromatch-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.2.2"
    "define-property" "^1.0.0"
    "extend-shallow" "^2.0.1"
    "extglob" "^2.0.2"
    "fragment-cache" "^0.2.1"
    "kind-of" "^5.0.2"
    "nanomatch" "^1.2.1"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"miller-rabin@^4.0.0":
  "integrity" "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/miller-rabin/-/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@>= 1.43.0 < 2", "mime-db@1.52.0":
  "integrity" "sha1-u6vNwChZ9JhzAchW4zh85exDv3A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12", "mime-types@^2.1.27", "mime-types@~2.1.17", "mime-types@~2.1.19", "mime-types@~2.1.24", "mime-types@~2.1.34":
  "integrity" "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@^2.4.4":
  "integrity" "sha1-oqaCqVzU0MsdYlfij4PafjWAA2c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mime/-/mime-2.6.0.tgz"
  "version" "2.6.0"

"mime@1.6.0":
  "integrity" "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^1.0.0":
  "integrity" "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mimic-fn/-/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@^2.1.0":
  "integrity" "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"mini-css-extract-plugin@^0.9.0":
  "integrity" "sha1-R/LPB6oWWrNXM7H8l9TEbAVkM54="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mini-css-extract-plugin/-/mini-css-extract-plugin-0.9.0.tgz"
  "version" "0.9.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "normalize-url" "1.9.1"
    "schema-utils" "^1.0.0"
    "webpack-sources" "^1.1.0"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4", "minimatch@^3.1.1", "minimatch@^3.1.2", "minimatch@2 || 3":
  "integrity" "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@~3.0.2":
  "integrity" "sha1-XmpZvRHiqw3hz7hD6y2C5UbDIcE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minimatch/-/minimatch-3.0.8.tgz"
  "version" "3.0.8"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.2.0", "minimist@1.2.0":
  "integrity" "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minimist/-/minimist-1.2.0.tgz"
  "version" "1.2.0"

"minimist@^1.2.5":
  "integrity" "sha1-hjelt1nqDW6YcCz7OpKDMjyTr0Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minimist/-/minimist-1.2.6.tgz"
  "version" "1.2.6"

"minimist@^1.2.6":
  "integrity" "sha1-hjelt1nqDW6YcCz7OpKDMjyTr0Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minimist/-/minimist-1.2.6.tgz"
  "version" "1.2.6"

"minipass-collect@^1.0.2":
  "integrity" "sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minipass-collect/-/minipass-collect-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minipass" "^3.0.0"

"minipass-flush@^1.0.5":
  "integrity" "sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minipass-flush/-/minipass-flush-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "minipass" "^3.0.0"

"minipass-pipeline@^1.2.2":
  "integrity" "sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "minipass" "^3.0.0"

"minipass@^3.0.0", "minipass@^3.1.1":
  "integrity" "sha1-ypn5Xdd8Q8ena/UebSAAJe7g/64="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minipass/-/minipass-3.3.4.tgz"
  "version" "3.3.4"
  dependencies:
    "yallist" "^4.0.0"

"minizlib@^2.1.1":
  "integrity" "sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/minizlib/-/minizlib-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "minipass" "^3.0.0"
    "yallist" "^4.0.0"

"mississippi@^3.0.0":
  "integrity" "sha1-6goykfl+C16HdrNj1fChLZTGcCI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mississippi/-/mississippi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^3.0.0"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mitt@^2.1.0":
  "integrity" "sha1-90BXfCMXbGIFsSGylzUU6t4bIjA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mitt/-/mitt-2.1.0.tgz"
  "version" "2.1.0"

"mitt@1.1.2":
  "integrity" "sha1-OA5hSA1qYVtmDwertg1R4KTkvtY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mitt/-/mitt-1.1.2.tgz"
  "version" "1.1.2"

"mixin-deep@^1.2.0":
  "integrity" "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mixin-deep/-/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp-then@1.2.0":
  "integrity" "sha1-pJLIecpNhz9e5FAI+PVf0BUN48U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mkdirp-then/-/mkdirp-then-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "any-promise" "^1.1.0"
    "mkdirp" "^0.5.0"

"mkdirp@^0.5.0", "mkdirp@^0.5.1", "mkdirp@^0.5.3", "mkdirp@^0.5.6", "mkdirp@>=0.5 0", "mkdirp@~0.5.1":
  "integrity" "sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mkdirp/-/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"mkdirp@^1.0.3":
  "integrity" "sha1-PrXtYmInVteaXw4qIh3+utdcL34="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mkdirp/-/mkdirp-1.0.4.tgz"
  "version" "1.0.4"

"mkdirp@^1.0.4":
  "integrity" "sha1-PrXtYmInVteaXw4qIh3+utdcL34="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mkdirp/-/mkdirp-1.0.4.tgz"
  "version" "1.0.4"

"mobile-drag-drop@^2.2.0":
  "integrity" "sha1-2eAEQWQ24yXz0Q5RVw8gcTltzDo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mobile-drag-drop/-/mobile-drag-drop-2.2.0.tgz"
  "version" "2.2.0"

"mobius1-selectr@^2.4.13":
  "integrity" "sha1-ABnf2fmEhA1uQPcGg6s+x4zjtd8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mobius1-selectr/-/mobius1-selectr-2.4.13.tgz"
  "version" "2.4.13"

"move-concurrently@^1.0.1":
  "integrity" "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/move-concurrently/-/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "^1.1.1"
    "copy-concurrently" "^1.0.0"
    "fs-write-stream-atomic" "^1.0.8"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.3"

"ms@^2.1.1":
  "integrity" "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"ms@0.7.1":
  "integrity" "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ms/-/ms-0.7.1.tgz"
  "version" "0.7.1"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.2":
  "integrity" "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.1.3":
  "integrity" "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"multicast-dns-service-types@^1.1.0":
  "integrity" "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@^6.0.1":
  "integrity" "sha1-oOx72QVcQoL3kMPIL04o2zsxsik="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/multicast-dns/-/multicast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "^1.3.1"
    "thunky" "^1.0.2"

"mute-stream@~0.0.4", "mute-stream@0.0.8":
  "integrity" "sha1-FjDEKyJR/4HiooPelqVJfqkuXg0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mute-stream/-/mute-stream-0.0.8.tgz"
  "version" "0.0.8"

"mz@^2.3.1", "mz@^2.4.0":
  "integrity" "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/mz/-/mz-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "any-promise" "^1.0.0"
    "object-assign" "^4.0.1"
    "thenify-all" "^1.0.0"

"nan@^2.12.1":
  "integrity" "sha1-Zk9D5FRg+5j68A7coLsNe43OeRY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/nan/-/nan-2.16.0.tgz"
  "version" "2.16.0"

"nanomatch@^1.2.1", "nanomatch@^1.2.9":
  "integrity" "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/nanomatch/-/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.3":
  "integrity" "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/negotiator/-/negotiator-0.6.3.tgz"
  "version" "0.6.3"

"neo-async@^2.5.0", "neo-async@^2.6.0", "neo-async@^2.6.1":
  "integrity" "sha1-tKr7k+OustgXTKU88WOrfXMIMF8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"nice-try@^1.0.4":
  "integrity" "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/nice-try/-/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"no-case@^2.2.0":
  "integrity" "sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/no-case/-/no-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "^1.1.1"

"node-fetch@~2.6.1":
  "integrity" "sha1-ogrLvsc8Lgn5AH3lzaFxBBIuABA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/node-fetch/-/node-fetch-2.6.13.tgz"
  "version" "2.6.13"
  dependencies:
    "whatwg-url" "^5.0.0"

"node-forge@^0.10.0":
  "integrity" "sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/node-forge/-/node-forge-0.10.0.tgz"
  "version" "0.10.0"

"node-libs-browser@^1.0.0 || ^2.0.0", "node-libs-browser@^2.2.1":
  "integrity" "sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/node-libs-browser/-/node-libs-browser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^3.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.1"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.11.0"
    "vm-browserify" "^1.0.1"

"node-releases@^2.0.6":
  "integrity" "sha1-inCIxjpV5JOEVoPr88go2MUcVQM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/node-releases/-/node-releases-2.0.6.tgz"
  "version" "2.0.6"

"node-status-codes@^1.0.0":
  "integrity" "sha1-WuVUHQJGRdMqWPzdyc7s6nrjrC8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/node-status-codes/-/node-status-codes-1.0.0.tgz"
  "version" "1.0.0"

"node-uuid@1.4.3":
  "integrity" "sha1-MZu3pW58tj8AtcDNeFHNS03fHfk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/node-uuid/-/node-uuid-1.4.3.tgz"
  "version" "1.4.3"

"nomnom@^1.5.x":
  "integrity" "sha1-IVH3Ikcrp55Qp2/BJbuMjy5Nwqc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/nomnom/-/nomnom-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "chalk" "~0.4.0"
    "underscore" "~1.6.0"

"normalize-package-data@^2.0.0", "normalize-package-data@^2.5.0":
  "integrity" "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^1.0.0":
  "integrity" "sha1-MtDkcvkf80VwHBWoMRAY07CpA3k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/normalize-path/-/normalize-path-1.0.0.tgz"
  "version" "1.0.0"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/normalize-path/-/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^3.0.0":
  "integrity" "sha1-suHE3E98bVd0PfczpPWXjRhlBVk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/normalize-url/-/normalize-url-3.3.0.tgz"
  "version" "3.3.0"

"normalize-url@1.9.1":
  "integrity" "sha1-LMDWazHqIwNkWENuNiDYWVTGbDw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/normalize-url/-/normalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"normalize-wheel@^1.0.1":
  "integrity" "sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/normalize-wheel/-/normalize-wheel-1.0.1.tgz"
  "version" "1.0.1"

"npm-normalize-package-bin@^1.0.0":
  "integrity" "sha1-bnmkHyP9I1wGIyGCKNp9nCO49uI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.1.tgz"
  "version" "1.0.1"

"npm-package-arg@^4.0.0", "npm-package-arg@4.1.0":
  "integrity" "sha1-LgFfisAHN8uX+ZfJy/BZ9Cp0Un0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/npm-package-arg/-/npm-package-arg-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "semver" "4 || 5"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/npm-run-path/-/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^3.0.0":
  "integrity" "sha1-f5G+MX9qRm7+08nymArYpO6LD6U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/npm-run-path/-/npm-run-path-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "path-key" "^3.0.0"

"npm-run-path@^4.0.0":
  "integrity" "sha1-t+zR5e1T2o43pV4cImnguX7XSOo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nth-check@^1.0.2":
  "integrity" "sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/nth-check/-/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"nth-check@^2.0.1":
  "integrity" "sha1-yeq0KO/842zWuSySS9sADvHx7R0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/nth-check/-/nth-check-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "boolbase" "^1.0.0"

"num2fraction@^1.2.2":
  "integrity" "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/num2fraction/-/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"number-is-nan@^1.0.0":
  "integrity" "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/number-is-nan/-/number-is-nan-1.0.1.tgz"
  "version" "1.0.1"

"oauth-sign@~0.9.0":
  "integrity" "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/oauth-sign/-/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object-copy/-/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-hash@^1.1.4":
  "integrity" "sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object-hash/-/object-hash-1.3.1.tgz"
  "version" "1.3.1"

"object-inspect@^1.12.2", "object-inspect@^1.9.0":
  "integrity" "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz"
  "version" "1.12.2"

"object-is@^1.0.1":
  "integrity" "sha1-ud7qpfx/GEag+uzc7sE45XePU6w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object-is/-/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.1.1":
  "integrity" "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-keys@~0.4.0":
  "integrity" "sha1-KKaq50KN0sOpLz2V8hM13SBOAzY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object-keys/-/object-keys-0.4.0.tgz"
  "version" "0.4.0"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object-visit/-/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.1.0", "object.assign@^4.1.4":
  "integrity" "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz"
  "version" "4.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "has-symbols" "^1.0.3"
    "object-keys" "^1.1.1"

"object.getownpropertydescriptors@^2.0.3", "object.getownpropertydescriptors@^2.1.0":
  "integrity" "sha1-eWXmQ3pXJ4tYc4ODGpuClFWkvDc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "array.prototype.reduce" "^1.0.4"
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.1"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object.pick/-/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"object.values@^1.1.0", "object.values@^1.1.5":
  "integrity" "sha1-lZ9j486e8QhyAzMIITHkpFm3Fqw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/object.values/-/object.values-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/obuf/-/obuf-1.1.2.tgz"
  "version" "1.1.2"

"on-finished@2.4.1":
  "integrity" "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/on-finished/-/on-finished-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/on-headers/-/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/onetime/-/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"onetime@^5.1.0":
  "integrity" "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^6.3.0":
  "integrity" "sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/open/-/open-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "is-wsl" "^1.1.0"

"opener@^1.5.1":
  "integrity" "sha1-XTfh81B3udysQwE3InGv3rKhNZg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/opener/-/opener-1.5.2.tgz"
  "version" "1.5.2"

"opn@^5.5.0":
  "integrity" "sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/opn/-/opn-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optionator@^0.8.3":
  "integrity" "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/optionator/-/optionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.6"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "word-wrap" "~1.2.3"

"ora@^3.4.0":
  "integrity" "sha1-vwdSSRBZo+8+1MhQl1Md6f280xg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ora/-/ora-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "chalk" "^2.4.2"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^2.0.0"
    "log-symbols" "^2.2.0"
    "strip-ansi" "^5.2.0"
    "wcwidth" "^1.0.1"

"os-browserify@^0.3.0":
  "integrity" "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/os-browserify/-/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"os-tmpdir@~1.0.2":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-finally/-/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-finally@^2.0.0":
  "integrity" "sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-finally/-/p-finally-2.0.1.tgz"
  "version" "2.0.1"

"p-limit@^2.0.0", "p-limit@^2.2.0", "p-limit@^2.2.1":
  "integrity" "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^3.0.0":
  "integrity" "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-locate/-/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha1-o0KLtwiLOmApL2aRkni3wpetTwc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^2.0.0":
  "integrity" "sha1-MQko/u+cnsxltosXaTAYpmXOoXU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-map/-/p-map-2.1.0.tgz"
  "version" "2.1.0"

"p-map@^3.0.0":
  "integrity" "sha1-1wTZr4orpoTiYA2aIVmD1BQal50="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-map/-/p-map-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-map@^4.0.0":
  "integrity" "sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-map/-/p-map-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-retry@^3.0.1":
  "integrity" "sha1-MWtMiJPiyNwc+okfQGxLQivr8yg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-retry/-/p-retry-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "retry" "^0.12.0"

"p-try@^2.0.0":
  "integrity" "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@~0.2.0":
  "integrity" "sha1-8/dSL073gjSNqBYbrZ7P1Rv4OnU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pako/-/pako-0.2.9.tgz"
  "version" "0.2.9"

"pako@~1.0.5":
  "integrity" "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"parallel-transform@^1.1.0":
  "integrity" "sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parallel-transform/-/parallel-transform-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cyclist" "^1.0.1"
    "inherits" "^2.0.3"
    "readable-stream" "^2.1.5"

"param-case@2.1.x":
  "integrity" "sha1-35T9jPZTHs915r75oIWPvHK+Ikc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/param-case/-/param-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "^2.2.0"

"parent-module@^1.0.0":
  "integrity" "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-asn1@^5.0.0", "parse-asn1@^5.1.5":
  "integrity" "sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parse-asn1/-/parse-asn1-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "asn1.js" "^5.2.0"
    "browserify-aes" "^1.0.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"
    "safe-buffer" "^5.1.1"

"parse-glob@3.0.4":
  "integrity" "sha1-ssN2z7EfNVE7rdFz7wu246OIORw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parse-glob/-/parse-glob-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "glob-base" "^0.3.0"
    "is-dotfile" "^1.0.0"
    "is-extglob" "^1.0.0"
    "is-glob" "^2.0.0"

"parse-json@^2.1.0":
  "integrity" "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parse-json/-/parse-json-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "error-ex" "^1.2.0"

"parse-json@^4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse-json@^5.0.0":
  "integrity" "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse5-htmlparser2-tree-adapter@^6.0.0":
  "integrity" "sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "parse5" "^6.0.1"

"parse5@^5.1.1":
  "integrity" "sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parse5/-/parse5-5.1.1.tgz"
  "version" "5.1.1"

"parse5@^6.0.1":
  "integrity" "sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parse5/-/parse5-6.0.1.tgz"
  "version" "6.0.1"

"parseqs@0.0.6":
  "integrity" "sha1-jku1oZ0c3IRKCKyXTTTic6+mcNU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parseqs/-/parseqs-0.0.6.tgz"
  "version" "0.0.6"

"parserlib@~1.1.1":
  "integrity" "sha1-pkz6ckBiQ0/fw1HJpOwtkrlMBvQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parserlib/-/parserlib-1.1.1.tgz"
  "version" "1.1.1"

"parseuri@0.0.6":
  "integrity" "sha1-4Ulugp46wv9H85pN0ESzKCPEolo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parseuri/-/parseuri-0.0.6.tgz"
  "version" "0.0.6"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"particles.vue@^2.16.3":
  "integrity" "sha1-IoyA2OmVdx74MvJ5yi48WdoZOeo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/particles.vue/-/particles.vue-2.43.1.tgz"
  "version" "2.43.1"
  dependencies:
    "tsparticles" "^1.43.1"
    "vue-class-component" "<8"
    "vue-property-decorator" "<10"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pascalcase/-/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.1":
  "integrity" "sha1-5sTd1+06onxoogzE5Q4aTug7vEo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-browserify/-/path-browserify-0.0.1.tgz"
  "version" "0.0.1"

"path-dirname@^1.0.0":
  "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-dirname/-/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@^2.0.0":
  "integrity" "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-exists/-/path-exists-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pinkie-promise" "^2.0.0"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-exists/-/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.2":
  "integrity" "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-is-inside/-/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-key/-/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^3.0.0":
  "integrity" "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-type/-/path-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "^3.0.0"

"path-type@^4.0.0":
  "integrity" "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"pbkdf2@^3.0.3":
  "integrity" "sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pbkdf2/-/pbkdf2-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"pdfjs-dist@2.6.347":
  "integrity" "sha1-8lftZug76QDND9KFJKIYf7niXNU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pdfjs-dist/-/pdfjs-dist-2.6.347.tgz"
  "version" "2.6.347"

"peek-stream@^1.1.0":
  "integrity" "sha1-OzXYS3zLvSYv/zHcENpWhW6tbWc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/peek-stream/-/peek-stream-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "buffer-from" "^1.0.0"
    "duplexify" "^3.5.0"
    "through2" "^2.0.3"

"performance-now@^2.1.0":
  "integrity" "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picocolors@^0.2.1":
  "integrity" "sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/picocolors/-/picocolors-0.2.1.tgz"
  "version" "0.2.1"

"picocolors@^1.0.0":
  "integrity" "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/picocolors/-/picocolors-1.0.0.tgz"
  "version" "1.0.0"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.3.1":
  "integrity" "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"picomodal@^3.0.0":
  "integrity" "sha1-+s0w9PvzSoCcHgTqUl8ATzmcC4I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/picomodal/-/picomodal-3.0.0.tgz"
  "version" "3.0.0"

"pify@^2.0.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pify/-/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pify/-/pify-3.0.0.tgz"
  "version" "3.0.0"

"pify@^4.0.1":
  "integrity" "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pify/-/pify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@^2.0.0":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pinkie/-/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pinyin-match@^1.2.2":
  "integrity" "sha1-JKYKW86J+ykD8z83SSeC0/jjTus="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pinyin-match/-/pinyin-match-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "rollup" "^2.44.0"

"pkg-dir@^1.0.0":
  "integrity" "sha1-ektQio1bstYp1EcFb/TpyTFM89Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pkg-dir/-/pkg-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "find-up" "^1.0.0"

"pkg-dir@^3.0.0":
  "integrity" "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pkg-dir/-/pkg-dir-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^3.0.0"

"pkg-dir@^4.1.0":
  "integrity" "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pkg-dir/-/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"please-upgrade-node@^3.1.1":
  "integrity" "sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/please-upgrade-node/-/please-upgrade-node-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "semver-compare" "^1.0.0"

"pngjs@^5.0.0":
  "integrity" "sha1-553SshV2f9nARWHAEjbflgvOf7s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pngjs/-/pngjs-5.0.0.tgz"
  "version" "5.0.0"

"pnp-webpack-plugin@^1.6.4":
  "integrity" "sha1-ZXQThPbYBW824iVajWf/wghm9ck="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pnp-webpack-plugin/-/pnp-webpack-plugin-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "ts-pnp" "^1.1.6"

"portfinder@^1.0.26":
  "integrity" "sha1-L+G55YOJcSQp3CvqW+shRhRsf4E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/portfinder/-/portfinder-1.0.32.tgz"
  "version" "1.0.32"
  dependencies:
    "async" "^2.6.4"
    "debug" "^3.2.7"
    "mkdirp" "^0.5.6"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^7.0.1":
  "integrity" "sha1-+KbpnxLmGcLrwjz2xIb9wVhgkz4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-calc/-/postcss-calc-7.0.5.tgz"
  "version" "7.0.5"
  dependencies:
    "postcss" "^7.0.27"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.0.2"

"postcss-colormin@^4.0.3":
  "integrity" "sha1-rgYLzpPteUrHEmTwgTLVUJVr04E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-colormin/-/postcss-colormin-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "color" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-convert-values@^4.0.1":
  "integrity" "sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-convert-values/-/postcss-convert-values-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-discard-comments@^4.0.2":
  "integrity" "sha1-H7q9LCRr/2qq15l7KwkY9NevQDM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-discard-comments/-/postcss-discard-comments-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-duplicates@^4.0.2":
  "integrity" "sha1-P+EzzTyCKC5VD8myORdqkge3hOs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-discard-duplicates/-/postcss-discard-duplicates-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-empty@^4.0.1":
  "integrity" "sha1-yMlR6fc+2UKAGUWERKAq2Qu592U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-discard-empty/-/postcss-discard-empty-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-overridden@^4.0.1":
  "integrity" "sha1-ZSrvipZybwKfXj4AFG7npOdV/1c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-discard-overridden/-/postcss-discard-overridden-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-load-config@^2.0.0":
  "integrity" "sha1-xepQTyxK7zPHNZo03jVzdyrXUCo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-load-config/-/postcss-load-config-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "import-cwd" "^2.0.0"

"postcss-loader@^3.0.0":
  "integrity" "sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-loader/-/postcss-loader-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "postcss" "^7.0.0"
    "postcss-load-config" "^2.0.0"
    "schema-utils" "^1.0.0"

"postcss-merge-longhand@^4.0.11":
  "integrity" "sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-merge-longhand/-/postcss-merge-longhand-4.0.11.tgz"
  "version" "4.0.11"
  dependencies:
    "css-color-names" "0.0.4"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "stylehacks" "^4.0.0"

"postcss-merge-rules@^4.0.3":
  "integrity" "sha1-NivqT/Wh+Y5AdacTxsslrv75plA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-merge-rules/-/postcss-merge-rules-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "cssnano-util-same-parent" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"
    "vendors" "^1.0.0"

"postcss-minify-font-values@^4.0.2":
  "integrity" "sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-minify-font-values/-/postcss-minify-font-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-gradients@^4.0.2":
  "integrity" "sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-minify-gradients/-/postcss-minify-gradients-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "is-color-stop" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-params@^4.0.2":
  "integrity" "sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-minify-params/-/postcss-minify-params-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "browserslist" "^4.0.0"
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "uniqs" "^2.0.0"

"postcss-minify-selectors@^4.0.2":
  "integrity" "sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-minify-selectors/-/postcss-minify-selectors-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"postcss-modules-extract-imports@^2.0.0":
  "integrity" "sha1-gYcZoa4doyX5gyRGsBE27rSTzX4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-modules-extract-imports/-/postcss-modules-extract-imports-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "^7.0.5"

"postcss-modules-local-by-default@^3.0.2":
  "integrity" "sha1-uxTgzHgnnVBNvcv9fgyiiZP/u7A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-modules-local-by-default/-/postcss-modules-local-by-default-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "icss-utils" "^4.1.1"
    "postcss" "^7.0.32"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.1.0"

"postcss-modules-scope@^2.2.0":
  "integrity" "sha1-OFyuATzHdD9afXYC0Qc6iequYu4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-modules-scope/-/postcss-modules-scope-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "postcss" "^7.0.6"
    "postcss-selector-parser" "^6.0.0"

"postcss-modules-values@^3.0.0":
  "integrity" "sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-modules-values/-/postcss-modules-values-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "icss-utils" "^4.0.0"
    "postcss" "^7.0.6"

"postcss-normalize-charset@^4.0.1":
  "integrity" "sha1-izWt067oOhNrBHHg1ZvlilAoXdQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-normalize-charset/-/postcss-normalize-charset-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-normalize-display-values@^4.0.2":
  "integrity" "sha1-Db4EpM6QY9RmftK+R2u4MMglk1o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-normalize-display-values/-/postcss-normalize-display-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-positions@^4.0.2":
  "integrity" "sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-normalize-positions/-/postcss-normalize-positions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-repeat-style@^4.0.2":
  "integrity" "sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-string@^4.0.2":
  "integrity" "sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-normalize-string/-/postcss-normalize-string-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-timing-functions@^4.0.2":
  "integrity" "sha1-jgCcoqOUnNr4rSPmtquZy159KNk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-unicode@^4.0.1":
  "integrity" "sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-url@^4.0.1":
  "integrity" "sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-normalize-url/-/postcss-normalize-url-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^3.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-whitespace@^4.0.2":
  "integrity" "sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-normalize-whitespace/-/postcss-normalize-whitespace-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-ordered-values@^4.1.2":
  "integrity" "sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-ordered-values/-/postcss-ordered-values-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-prefix-selector@^1.6.0":
  "integrity" "sha1-rVtW+ac6LAkMpxYQSWMsnYm8tAQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-prefix-selector/-/postcss-prefix-selector-1.16.0.tgz"
  "version" "1.16.0"

"postcss-reduce-initial@^4.0.3":
  "integrity" "sha1-f9QuvqXpyBRgljniwuhK4nC6SN8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-reduce-initial/-/postcss-reduce-initial-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"

"postcss-reduce-transforms@^4.0.2":
  "integrity" "sha1-F++kBerMbge+NBSlyi0QdGgdTik="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-reduce-transforms/-/postcss-reduce-transforms-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-selector-parser@^3.0.0":
  "integrity" "sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "dot-prop" "^5.2.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^6.0.0", "postcss-selector-parser@^6.0.2":
  "integrity" "sha1-ebYeLA0b/CYC1UnhHQh2JW+N+I0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz"
  "version" "6.0.10"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-svgo@^4.0.3":
  "integrity" "sha1-NDos26yVBdQWJD1Jb3JPOIlMlB4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-svgo/-/postcss-svgo-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "svgo" "^1.0.0"

"postcss-unique-selectors@^4.0.1":
  "integrity" "sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-unique-selectors/-/postcss-unique-selectors-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "postcss" "^7.0.0"
    "uniqs" "^2.0.0"

"postcss-value-parser@^3.0.0":
  "integrity" "sha1-n/giVH4okyE88cMO+lGsX9G6goE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-value-parser@^4.0.2", "postcss-value-parser@^4.1.0":
  "integrity" "sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^5.2.17":
  "integrity" "sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss/-/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^7.0.0", "postcss@^7.0.1", "postcss@^7.0.14", "postcss@^7.0.27", "postcss@^7.0.32", "postcss@^7.0.36", "postcss@^7.0.5", "postcss@^7.0.6", "postcss@>4 <9":
  "integrity" "sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/postcss/-/postcss-7.0.39.tgz"
  "version" "7.0.39"
  dependencies:
    "picocolors" "^0.2.1"
    "source-map" "^0.6.1"

"posthtml-parser@^0.2.0", "posthtml-parser@^0.2.1":
  "integrity" "sha1-NdUw3jhnQMK6JP8usvrznM3ycd0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/posthtml-parser/-/posthtml-parser-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "htmlparser2" "^3.8.3"
    "isobject" "^2.1.0"

"posthtml-rename-id@^1.0":
  "integrity" "sha1-z39us3FGvxr6wx5o8YxswZrmFDM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/posthtml-rename-id/-/posthtml-rename-id-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "escape-string-regexp" "1.0.5"

"posthtml-render@^1.0.5", "posthtml-render@^1.0.6":
  "integrity" "sha1-QBFAcMRYgcrLkzR9rj7/U6+8/xM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/posthtml-render/-/posthtml-render-1.4.0.tgz"
  "version" "1.4.0"

"posthtml-svg-mode@^1.0.3":
  "integrity" "sha1-q9VU+s6BIjyrDLNn4Y5O/SpOdLA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/posthtml-svg-mode/-/posthtml-svg-mode-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "merge-options" "1.0.1"
    "posthtml" "^0.9.2"
    "posthtml-parser" "^0.2.1"
    "posthtml-render" "^1.0.6"

"posthtml@^0.9.2":
  "integrity" "sha1-9MBtufZ7Yf0XxOJW5+PZUVv3Jv0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/posthtml/-/posthtml-0.9.2.tgz"
  "version" "0.9.2"
  dependencies:
    "posthtml-parser" "^0.2.0"
    "posthtml-render" "^1.0.5"

"prelude-ls@~1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/prelude-ls/-/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prepend-http@^1.0.0", "prepend-http@^1.0.1":
  "integrity" "sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/prepend-http/-/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"prettier@^1.18.2 || ^2.0.0":
  "integrity" "sha1-4jWAaFDQV/l7sINopPfYmfd2DGQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/prettier/-/prettier-2.7.1.tgz"
  "version" "2.7.1"

"pretty-error@^2.0.2":
  "integrity" "sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pretty-error/-/pretty-error-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "lodash" "^4.17.20"
    "renderkid" "^2.0.4"

"private@^0.1.6":
  "integrity" "sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/private/-/private-0.1.8.tgz"
  "version" "0.1.8"

"process-nextick-args@~2.0.0":
  "integrity" "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@^0.11.10":
  "integrity" "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/process/-/process-0.11.10.tgz"
  "version" "0.11.10"

"progress@^2.0.0":
  "integrity" "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/progress/-/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise-inflight@^1.0.1":
  "integrity" "sha1-mEcocL8igTL8vdhoEputEsPAKeM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/promise-inflight/-/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"promzard@^0.3.0":
  "integrity" "sha1-JqXW7ox97kyxIggwWs+5O6OCqe4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/promzard/-/promzard-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "read" "1"

"proxy-addr@~2.0.7":
  "integrity" "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/proxy-addr/-/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"prr@~1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/prr/-/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pseudomap/-/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.28":
  "integrity" "sha1-0N8qE38AeUVl/K87LADNCfjVpac="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/psl/-/psl-1.9.0.tgz"
  "version" "1.9.0"

"public-encrypt@^4.0.0":
  "integrity" "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/public-encrypt/-/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^2.0.0":
  "integrity" "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pump/-/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pump@^3.0.0":
  "integrity" "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pump/-/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.3":
  "integrity" "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/pumpify/-/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^1.2.4":
  "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/punycode/-/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/punycode/-/punycode-2.1.1.tgz"
  "version" "2.1.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/punycode/-/punycode-1.3.2.tgz"
  "version" "1.3.2"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/q/-/q-1.5.1.tgz"
  "version" "1.5.1"

"qrcode@^1.4.4":
  "integrity" "sha1-AQP5cxdAn3vJF3LvMHk6VM1Z8Ms="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/qrcode/-/qrcode-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "dijkstrajs" "^1.0.1"
    "encode-utf8" "^1.0.3"
    "pngjs" "^5.0.0"
    "yargs" "^15.3.1"

"qs@^6.9.4":
  "integrity" "sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/qs/-/qs-6.11.0.tgz"
  "version" "6.11.0"
  dependencies:
    "side-channel" "^1.0.4"

"qs@~6.5.2":
  "integrity" "sha1-Ou7/yRln7241wOSI70b7KWq3aq0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/qs/-/qs-6.5.3.tgz"
  "version" "6.5.3"

"qs@6.10.3":
  "integrity" "sha1-1s3hsv/Kh7WqV4iYFsX4FTXiLo4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/qs/-/qs-6.10.3.tgz"
  "version" "6.10.3"
  dependencies:
    "side-channel" "^1.0.4"

"query-string@^4.1.0", "query-string@^4.3.2":
  "integrity" "sha1-u7aTucqRXCMlFbIosaArYJBD2+s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/query-string/-/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/querystring-es3/-/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/querystring/-/querystring-0.2.0.tgz"
  "version" "0.2.0"

"querystringify@^2.1.1":
  "integrity" "sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/querystringify/-/querystringify-2.2.0.tgz"
  "version" "2.2.0"

"queue-microtask@^1.2.2":
  "integrity" "sha1-SSkii7xyTfrEPg77BYyve2z7YkM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5", "randombytes@^2.1.0":
  "integrity" "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/randomfill/-/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.5.1":
  "integrity" "sha1-/hsWKLGBtwAhXl/UI4n5i3E5KFc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/raw-body/-/raw-body-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "bytes" "3.1.2"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"raw-loader@^4.0.2":
  "integrity" "sha1-GqxrfRrRUB5m79rBUixz5ZpYTrY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/raw-loader/-/raw-loader-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "loader-utils" "^2.0.0"
    "schema-utils" "^3.0.0"

"raw-loader@~0.5.1":
  "integrity" "sha1-DD0L6u2KAclm2Xh793goElKpeao="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/raw-loader/-/raw-loader-0.5.1.tgz"
  "version" "0.5.1"

"read-all-stream@^3.0.0":
  "integrity" "sha1-NcPhd/IHjveJ7kv6+kNzB06u9Po="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/read-all-stream/-/read-all-stream-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "pinkie-promise" "^2.0.0"
    "readable-stream" "^2.0.0"

"read-package-json@1 || 2":
  "integrity" "sha1-aZKytmxxdyWf646qxzw6zSi5Iio="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/read-package-json/-/read-package-json-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "glob" "^7.1.1"
    "json-parse-even-better-errors" "^2.3.0"
    "normalize-package-data" "^2.0.0"
    "npm-normalize-package-bin" "^1.0.0"

"read-pkg@^5.1.1":
  "integrity" "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/read-pkg/-/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"read@~1.0.1", "read@1":
  "integrity" "sha1-s9oZvQUkMal2cdRKQmNK33ELQMQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/read/-/read-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "mute-stream" "~0.0.4"

"readable-stream@^2.0.0", "readable-stream@^2.0.1", "readable-stream@^2.0.2", "readable-stream@^2.0.5", "readable-stream@^2.1.5", "readable-stream@^2.2.2", "readable-stream@^2.3.3", "readable-stream@^2.3.6", "readable-stream@~2.3.6", "readable-stream@1 || 2":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6":
  "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.6.0":
  "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@~1.0.17":
  "integrity" "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-1.0.34.tgz"
  "version" "1.0.34"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.1"
    "isarray" "0.0.1"
    "string_decoder" "~0.10.x"

"readable-stream@1.1":
  "integrity" "sha1-fPTFTvZI44EwhMY23SB54WbAgdk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-1.1.14.tgz"
  "version" "1.1.14"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.1"
    "isarray" "0.0.1"
    "string_decoder" "~0.10.x"

"readdir-scoped-modules@1.0.2":
  "integrity" "sha1-n6+jfShr5dksuuve4DDcm19AZ0c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/readdir-scoped-modules/-/readdir-scoped-modules-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "debuglog" "^1.0.1"
    "dezalgo" "^1.0.0"
    "graceful-fs" "^4.1.2"
    "once" "^1.3.0"

"readdirp@^2.2.1":
  "integrity" "sha1-DodiKjMlqjPokihcr4tOhGUppSU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/readdirp/-/readdirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"readdirp@~3.6.0":
  "integrity" "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"regenerate-unicode-properties@^10.1.0":
  "integrity" "sha1-fDGSyrbdJOIctEYeXd190k+oN0w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.2.1", "regenerate@^1.4.2":
  "integrity" "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.10.5":
  "integrity" "sha1-M2w+/BIgrc7dosn6tntaeVWjNlg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regenerator-runtime/-/regenerator-runtime-0.10.5.tgz"
  "version" "0.10.5"

"regenerator-runtime@^0.11.0":
  "integrity" "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@^0.13.4", "regenerator-runtime@^0.13.5":
  "integrity" "sha1-iSV0Kpj/2QgUmI11Zq0wyjsmO1I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  "version" "0.13.9"

"regenerator-transform@^0.10.0":
  "integrity" "sha1-HkmWg3Ix2ot/PPQRTXG1aRoGgN0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regenerator-transform/-/regenerator-transform-0.10.1.tgz"
  "version" "0.10.1"
  dependencies:
    "babel-runtime" "^6.18.0"
    "babel-types" "^6.19.0"
    "private" "^0.1.6"

"regenerator-transform@^0.15.0":
  "integrity" "sha1-y9nq1dd/rhpI2VfPiJrQWGrbZTc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regenerator-transform/-/regenerator-transform-0.15.0.tgz"
  "version" "0.15.0"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regex-not/-/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.2.0", "regexp.prototype.flags@^1.4.3":
  "integrity" "sha1-h8qzD4D2ZmAYGju3v1mBqHKzZ6w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regexp.prototype.flags/-/regexp.prototype.flags-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "functions-have-names" "^1.2.2"

"regexpp@^2.0.1":
  "integrity" "sha1-jRnTHPYySCtYkEn4KB+T28uk0H8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regexpp/-/regexpp-2.0.1.tgz"
  "version" "2.0.1"

"regexpp@^3.0.0":
  "integrity" "sha1-BCWido2PI7rXDKS5BGH6LxIT4bI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regexpp/-/regexpp-3.2.0.tgz"
  "version" "3.2.0"

"regexpu-core@^2.0.0":
  "integrity" "sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regexpu-core/-/regexpu-core-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "regenerate" "^1.2.1"
    "regjsgen" "^0.2.0"
    "regjsparser" "^0.1.4"

"regexpu-core@^5.1.0":
  "integrity" "sha1-ppwm8yTB6WLp/9C4iwVcq6gIkTk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regexpu-core/-/regexpu-core-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "regenerate" "^1.4.2"
    "regenerate-unicode-properties" "^10.1.0"
    "regjsgen" "^0.7.1"
    "regjsparser" "^0.9.1"
    "unicode-match-property-ecmascript" "^2.0.0"
    "unicode-match-property-value-ecmascript" "^2.0.0"

"regjsgen@^0.2.0":
  "integrity" "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regjsgen/-/regjsgen-0.2.0.tgz"
  "version" "0.2.0"

"regjsgen@^0.7.1":
  "integrity" "sha1-7l7zDhjT8Jt8Npt258I3PtJVRvY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regjsgen/-/regjsgen-0.7.1.tgz"
  "version" "0.7.1"

"regjsparser@^0.1.4":
  "integrity" "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regjsparser/-/regjsparser-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "jsesc" "~0.5.0"

"regjsparser@^0.9.1":
  "integrity" "sha1-Jy0FqhDHwfZwlbH/Ct2uhEL8Vwk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/regjsparser/-/regjsparser-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@0.2.x":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/relateurl/-/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@^2.0.4":
  "integrity" "sha1-Rk8namvc7mBvShWZP5sp/HTKhgk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/renderkid/-/renderkid-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "css-select" "^4.1.3"
    "dom-converter" "^0.2.0"
    "htmlparser2" "^6.1.0"
    "lodash" "^4.17.21"
    "strip-ansi" "^3.0.1"

"repeat-element@^1.1.2":
  "integrity" "sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/repeat-element/-/repeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/repeat-string/-/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"request@^2.88.2", "request@2.88.2":
  "integrity" "sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/request/-/request-2.88.2.tgz"
  "version" "2.88.2"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.3"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.5.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-main-filename@^2.0.0":
  "integrity" "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/require-main-filename/-/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-observer-polyfill@^1.5.0":
  "integrity" "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-cwd@^2.0.0":
  "integrity" "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/resolve-cwd/-/resolve-cwd-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "resolve-from" "^3.0.0"

"resolve-from@^3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/resolve-from/-/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/resolve-url/-/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.10.0", "resolve@^1.10.1", "resolve@^1.12.0", "resolve@^1.13.1", "resolve@^1.14.2", "resolve@^1.20.0", "resolve@^1.22.0":
  "integrity" "sha1-J8suu1P5GrtJRwqSi7p1WAZqwXc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/resolve/-/resolve-1.22.1.tgz"
  "version" "1.22.1"
  dependencies:
    "is-core-module" "^2.9.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/restore-cursor/-/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"restore-cursor@^3.1.0":
  "integrity" "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/restore-cursor/-/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ret/-/ret-0.1.15.tgz"
  "version" "0.1.15"

"retry@^0.12.0":
  "integrity" "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/retry/-/retry-0.12.0.tgz"
  "version" "0.12.0"

"retry@0.9.0":
  "integrity" "sha1-b2l+UKDk3cjI9/tUeptg3q1DZ40="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/retry/-/retry-0.9.0.tgz"
  "version" "0.9.0"

"reusify@^1.0.4":
  "integrity" "sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/reusify/-/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rgb-regex@^1.0.1":
  "integrity" "sha1-wODWiC3w4jviVKR16O3UGRX+rrE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rgb-regex/-/rgb-regex-1.0.1.tgz"
  "version" "1.0.1"

"rgba-regex@^1.0.0":
  "integrity" "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rgba-regex/-/rgba-regex-1.0.0.tgz"
  "version" "1.0.0"

"rimraf@^2.2.8", "rimraf@^2.5.4", "rimraf@^2.6.1", "rimraf@^2.6.3", "rimraf@2":
  "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rimraf/-/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.0":
  "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.2":
  "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rimraf@2.6.3":
  "integrity" "sha1-stEE/g2Psnz54KHNqCYt04M8bKs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rimraf/-/rimraf-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "glob" "^7.1.3"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ripemd160/-/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"rollup@^2.44.0":
  "integrity" "sha1-kXeZLJ8J61jF5Wy/pkFgehK1fOI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rollup/-/rollup-2.79.0.tgz"
  "version" "2.79.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"run-async@^2.4.0":
  "integrity" "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/run-async/-/run-async-2.4.1.tgz"
  "version" "2.4.1"

"run-parallel@^1.1.9":
  "integrity" "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"run-queue@^1.0.0", "run-queue@^1.0.3":
  "integrity" "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/run-queue/-/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "^1.1.1"

"rustpotter-web-slim@1.1.2":
  "integrity" "sha1-oeGGloNdDHdXTdstkML9wssB8YU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rustpotter-web-slim/-/rustpotter-web-slim-1.1.2.tgz"
  "version" "1.1.2"

"rustpotter-worklet@^1.0.4":
  "integrity" "sha1-z9M1r+9B8cVlvKFT/OgDLMd+kzY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rustpotter-worklet/-/rustpotter-worklet-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "rustpotter-web-slim" "1.1.2"

"rxjs@^6.3.3", "rxjs@^6.6.0":
  "integrity" "sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/rxjs/-/rxjs-6.6.7.tgz"
  "version" "6.6.7"
  dependencies:
    "tslib" "^1.9.0"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@>=5.1.0", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@^5.2.0":
  "integrity" "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@5.2.1":
  "integrity" "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/safe-regex/-/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@~2.1.0":
  "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sass-loader@^8.0.2":
  "integrity" "sha1-3r7NjDziQ8dkVPLoKQSCFQOACQ0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sass-loader/-/sass-loader-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "clone-deep" "^4.0.1"
    "loader-utils" "^1.2.3"
    "neo-async" "^2.6.1"
    "schema-utils" "^2.6.1"
    "semver" "^6.3.0"

"sass@^1.26.5", "sass@^1.3.0":
  "integrity" "sha1-DE08KTz+j4oujTtmbhzxv/gGXRw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sass/-/sass-1.55.0.tgz"
  "version" "1.55.0"
  dependencies:
    "chokidar" ">=3.0.0 <4.0.0"
    "immutable" "^4.0.0"
    "source-map-js" ">=0.6.2 <2.0.0"

"sax@~1.2.4":
  "integrity" "sha1-KBYjTiN4vdxOU1T6tcqold9xANk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"schema-utils@^0.4.0":
  "integrity" "sha1-unT1l9K+LqiAExdG7hfQoJPGgYc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-0.4.7.tgz"
  "version" "0.4.7"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^1.0.0":
  "integrity" "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-errors" "^1.0.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^2.0.0":
  "integrity" "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^2.5.0":
  "integrity" "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^2.6.1":
  "integrity" "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^2.6.5":
  "integrity" "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^2.7.0":
  "integrity" "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.0.0":
  "integrity" "sha1-vHTEtraZXB2I92qLd76nIZ4MgoE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"script-loader@^0.7.2":
  "integrity" "sha1-IBbbb4byX1z1baOJFdgzeLsWa6c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/script-loader/-/script-loader-0.7.2.tgz"
  "version" "0.7.2"
  dependencies:
    "raw-loader" "~0.5.1"

"seedrandom@~2.4.3":
  "integrity" "sha1-sl6phjLHPkX1i3fPqpMWeN8B+bo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/seedrandom/-/seedrandom-2.4.4.tgz"
  "version" "2.4.4"

"seedrandom@2.4.3":
  "integrity" "sha1-JDhQTa0zkXMUv/GKxNeU8W1qrsw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/seedrandom/-/seedrandom-2.4.3.tgz"
  "version" "2.4.3"

"select-hose@^2.0.0":
  "integrity" "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/select-hose/-/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"select@^1.1.2":
  "integrity" "sha1-DnNQrN7ICxEIUoeG7B1EGNEbOW0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/select/-/select-1.1.2.tgz"
  "version" "1.1.2"

"selfsigned@^1.10.8":
  "integrity" "sha1-7lHYTZ3OzGHgfkq6NPIpq1JcFXQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/selfsigned/-/selfsigned-1.10.14.tgz"
  "version" "1.10.14"
  dependencies:
    "node-forge" "^0.10.0"

"semver-compare@^1.0.0":
  "integrity" "sha1-De4hahyUGrN+nvsXiPavxf9VN/w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver-compare/-/semver-compare-1.0.0.tgz"
  "version" "1.0.0"

"semver@^5.5.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.6.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.7.1":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.1.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.1.1":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.1.2":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.3.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^7.3.5":
  "integrity" "sha1-EsW2Sa/b+QSXB3luIqQCiBTOUj8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-7.3.7.tgz"
  "version" "7.3.7"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@~5.0.1", "semver@2 || 3 || 4 || 5", "semver@2.x || 3.x || 4 || 5", "semver@4 || 5", "semver@5.0.1":
  "integrity" "sha1-n7P0AE+QDYPEeWj+QvdYPgWDLMk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/semver/-/semver-5.0.1.tgz"
  "version" "5.0.1"

"send@0.18.0":
  "integrity" "sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/send/-/send-0.18.0.tgz"
  "version" "0.18.0"
  dependencies:
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "mime" "1.6.0"
    "ms" "2.1.3"
    "on-finished" "2.4.1"
    "range-parser" "~1.2.1"
    "statuses" "2.0.1"

"serialize-javascript@^4.0.0":
  "integrity" "sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/serialize-javascript/-/serialize-javascript-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "randombytes" "^2.1.0"

"serve-index@^1.9.1":
  "integrity" "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.15.0":
  "integrity" "sha1-+q7wjP/goaYvYMrQxOUTz/CslUA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/serve-static/-/serve-static-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.18.0"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/set-value/-/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/setprototypeof/-/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.2.0":
  "integrity" "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/setprototypeof/-/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sha.js/-/sha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shallow-clone@^3.0.0":
  "integrity" "sha1-jymBrZJTH1UDWwH7IwdppA4C76M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/shallow-clone/-/shallow-clone-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^6.0.2"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/shebang-command/-/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/shebang-regex/-/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.7.3":
  "integrity" "sha1-qkDtrBcERbmkMeF7tiwLiBucQSM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/shell-quote/-/shell-quote-1.7.3.tgz"
  "version" "1.7.3"

"side-channel@^1.0.4":
  "integrity" "sha1-785cj9wQTudRslxY1CkAEfpeos8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/side-channel/-/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0", "signal-exit@^3.0.2":
  "integrity" "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"simple-swizzle@^0.2.2":
  "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "^0.3.1"

"slash@^1.0.0":
  "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/slash/-/slash-1.0.0.tgz"
  "version" "1.0.0"

"slash@^2.0.0":
  "integrity" "sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/slash/-/slash-2.0.0.tgz"
  "version" "2.0.0"

"slash@^3.0.0":
  "integrity" "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/slash/-/slash-3.0.0.tgz"
  "version" "3.0.0"

"slice-ansi@^2.1.0":
  "integrity" "sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/slice-ansi/-/slice-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "astral-regex" "^1.0.0"
    "is-fullwidth-code-point" "^2.0.0"

"slice-ansi@0.0.4":
  "integrity" "sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/slice-ansi/-/slice-ansi-0.0.4.tgz"
  "version" "0.0.4"

"snapdragon-node@^2.0.1":
  "integrity" "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/snapdragon/-/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"socket.io-client@^2.1.1":
  "integrity" "sha1-NPSG82QN3pwiEfzohawnRvm69cs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/socket.io-client/-/socket.io-client-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "backo2" "1.0.2"
    "component-bind" "1.0.0"
    "component-emitter" "~1.3.0"
    "debug" "~3.1.0"
    "engine.io-client" "~3.5.0"
    "has-binary2" "~1.0.2"
    "indexof" "0.0.1"
    "parseqs" "0.0.6"
    "parseuri" "0.0.6"
    "socket.io-parser" "~3.3.0"
    "to-array" "0.1.4"

"socket.io-parser@~3.3.0":
  "integrity" "sha1-OouEgj66h/P3Yk5kqKqrbWMYpy8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/socket.io-parser/-/socket.io-parser-3.3.3.tgz"
  "version" "3.3.3"
  dependencies:
    "component-emitter" "~1.3.0"
    "debug" "~3.1.0"
    "isarray" "2.0.1"

"sockjs-client@^1.5.0":
  "integrity" "sha1-NQuO2kLW1S3cAww5lDNkwR3K2AY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sockjs-client/-/sockjs-client-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "debug" "^3.2.7"
    "eventsource" "^2.0.2"
    "faye-websocket" "^0.11.4"
    "inherits" "^2.0.4"
    "url-parse" "^1.5.10"

"sockjs@^0.3.21":
  "integrity" "sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sockjs/-/sockjs-0.3.24.tgz"
  "version" "0.3.24"
  dependencies:
    "faye-websocket" "^0.11.3"
    "uuid" "^8.3.2"
    "websocket-driver" "^0.7.4"

"sort-keys@^1.0.0":
  "integrity" "sha1-RBttTTRnmPG05J6JIK37oOVD+a0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sort-keys/-/sort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"sortablejs@1.10.2":
  "integrity" "sha1-bkA2TZE/mLhaFPZnj5K1wSIfUpA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sortablejs/-/sortablejs-1.10.2.tgz"
  "version" "1.10.2"

"source-list-map@^2.0.0":
  "integrity" "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-list-map/-/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-js@>=0.6.2 <2.0.0":
  "integrity" "sha1-rbw2HZxi3zgBJefxYfccgm8eSQw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map-js/-/source-map-js-1.0.2.tgz"
  "version" "1.0.2"

"source-map-resolve@^0.5.0":
  "integrity" "sha1-GQhmvs51U+H48mei7oLGBrVQmho="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map-resolve/-/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@~0.5.12":
  "integrity" "sha1-BP58f54e0tZiIzwoyys1ufY/bk8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map-support/-/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha1-CvZmBadFpaL5HPG7+KevvCg97FY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map-url/-/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.6":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map/-/source-map-0.7.4.tgz"
  "version" "0.7.4"

"source-map@~0.6.0":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"spdx-correct@^3.0.0":
  "integrity" "sha1-3s6BrJweZxPl99G28X1Gj6U9iak="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/spdx-correct/-/spdx-correct-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha1-aQd4NavicQtl8DlpiYtmN7UFp3k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/spdx-license-ids/-/spdx-license-ids-3.0.12.tgz"
  "version" "3.0.12"

"spdy-transport@^3.0.0":
  "integrity" "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/spdy-transport/-/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.2":
  "integrity" "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/spdy/-/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/split-string/-/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sshpk@^1.7.0":
  "integrity" "sha1-V4CC2S1P5hKxMAdJblQ/oPvL5MU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/sshpk/-/sshpk-1.17.0.tgz"
  "version" "1.17.0"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"ssr-window@^2.0.0":
  "integrity" "sha1-mMMBrvmVIzF/jWlhjwAQeRCW78Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ssr-window/-/ssr-window-2.0.0.tgz"
  "version" "2.0.0"

"ssri@^6.0.1":
  "integrity" "sha1-FXk5E08gRk5zAd26PpD/qPdyisU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ssri/-/ssri-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "figgy-pudding" "^3.5.1"

"ssri@^8.0.1":
  "integrity" "sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ssri/-/ssri-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "minipass" "^3.1.1"

"stable@^0.1.8":
  "integrity" "sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/stable/-/stable-0.1.8.tgz"
  "version" "0.1.8"

"stackframe@^1.3.4":
  "integrity" "sha1-uIGgBMjBSaXo7+831RsW5BKUMxA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/stackframe/-/stackframe-1.3.4.tgz"
  "version" "1.3.4"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/static-extend/-/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/statuses/-/statuses-1.5.0.tgz"
  "version" "1.5.0"

"statuses@2.0.1":
  "integrity" "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/statuses/-/statuses-2.0.1.tgz"
  "version" "2.0.1"

"stream-browserify@^2.0.1":
  "integrity" "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/stream-browserify/-/stream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-each@^1.1.0":
  "integrity" "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/stream-each/-/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "^1.1.0"
    "stream-shift" "^1.0.0"

"stream-http@^2.7.2":
  "integrity" "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/stream-http/-/stream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"stream-shift@^1.0.0":
  "integrity" "sha1-1wiCgVWasneEJCebCHfaPDktWj0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/stream-shift/-/stream-shift-1.0.1.tgz"
  "version" "1.0.1"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.0.0", "string_decoder@^1.1.1", "string_decoder@~1.1.1":
  "integrity" "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string_decoder@~0.10.x":
  "integrity" "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/string_decoder/-/string_decoder-0.10.31.tgz"
  "version" "0.10.31"

"string-argv@^0.3.0":
  "integrity" "sha1-leL77AQnrhkYSTX4FtdKqkxcGdo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/string-argv/-/string-argv-0.3.1.tgz"
  "version" "0.3.1"

"string-width@^1.0.1":
  "integrity" "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/string-width/-/string-width-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "code-point-at" "^1.0.0"
    "is-fullwidth-code-point" "^1.0.0"
    "strip-ansi" "^3.0.0"

"string-width@^2.1.1":
  "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/string-width/-/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^3.0.0", "string-width@^3.1.0":
  "integrity" "sha1-InZ74htirxCBV0MG9prFG2IgOWE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/string-width/-/string-width-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "emoji-regex" "^7.0.1"
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^5.1.0"

"string-width@^4.1.0", "string-width@^4.2.0", "string-width@^4.2.3":
  "integrity" "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string.prototype.trimend@^1.0.5":
  "integrity" "sha1-kUpluqqyX73U7ikcp93lfoacuNA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.19.5"

"string.prototype.trimstart@^1.0.5":
  "integrity" "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.19.5"

"stringify-object@^3.3.0":
  "integrity" "sha1-cDBlrvyhkwDTzoivT1s5VtdVZik="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/stringify-object/-/stringify-object-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "get-own-enumerable-property-symbols" "^3.0.0"
    "is-obj" "^1.0.1"
    "is-regexp" "^1.0.0"

"strip-ansi@^3.0.0", "strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-ansi@^5.0.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^5.1.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^5.2.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@~0.1.0":
  "integrity" "sha1-OeipjQRNFQZgq+SmgIrPcLt7yZE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-0.1.1.tgz"
  "version" "0.1.1"

"strip-bom@^3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-bom/-/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-eof/-/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^2.0.0":
  "integrity" "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-indent/-/strip-indent-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@^3.0.1", "strip-json-comments@3.1.0":
  "integrity" "sha1-djjTFCISns9EV0QACfugP5+awYA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-json-comments/-/strip-json-comments-3.1.0.tgz"
  "version" "3.1.0"

"strip-json-comments@1.0.x":
  "integrity" "sha1-HhX7ysl9Pumb8tc7TGVrCCu6+5E="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/strip-json-comments/-/strip-json-comments-1.0.4.tgz"
  "version" "1.0.4"

"stylehacks@^4.0.0":
  "integrity" "sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/stylehacks/-/stylehacks-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/supports-color/-/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^3.2.3":
  "integrity" "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/supports-color/-/supports-color-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "has-flag" "^1.0.0"

"supports-color@^5.3.0":
  "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^6.1.0":
  "integrity" "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/supports-color/-/supports-color-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha1-btpL00SjyUrqN21MwxvHcxEDngk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg-baker-runtime@^1.4.7":
  "integrity" "sha1-9HIGN/W2IC7vY3jYHx/q0IFfik4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/svg-baker-runtime/-/svg-baker-runtime-1.4.7.tgz"
  "version" "1.4.7"
  dependencies:
    "deepmerge" "1.3.2"
    "mitt" "1.1.2"
    "svg-baker" "^1.7.0"

"svg-baker@^1.5.0", "svg-baker@^1.7.0":
  "integrity" "sha1-g2f3jYdVUMUv5HVvcwPVxdfC6ac="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/svg-baker/-/svg-baker-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "bluebird" "^3.5.0"
    "clone" "^2.1.1"
    "he" "^1.1.1"
    "image-size" "^0.5.1"
    "loader-utils" "^1.1.0"
    "merge-options" "1.0.1"
    "micromatch" "3.1.0"
    "postcss" "^5.2.17"
    "postcss-prefix-selector" "^1.6.0"
    "posthtml-rename-id" "^1.0"
    "posthtml-svg-mode" "^1.0.3"
    "query-string" "^4.3.2"
    "traverse" "^0.6.6"

"svg-sprite-loader@^5.2.1":
  "integrity" "sha1-8MN+dmZd3996vI/269HaNc3SEtk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/svg-sprite-loader/-/svg-sprite-loader-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "bluebird" "^3.5.0"
    "deepmerge" "1.3.2"
    "domready" "1.0.8"
    "escape-string-regexp" "1.0.5"
    "loader-utils" "^1.1.0"
    "svg-baker" "^1.5.0"
    "svg-baker-runtime" "^1.4.7"
    "url-slug" "2.0.0"

"svg-tags@^1.0.0":
  "integrity" "sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/svg-tags/-/svg-tags-1.0.0.tgz"
  "version" "1.0.0"

"svgo@^1.0.0":
  "integrity" "sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/svgo/-/svgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "^2.4.1"
    "coa" "^2.0.2"
    "css-select" "^2.0.0"
    "css-select-base-adapter" "^0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "^4.0.2"
    "js-yaml" "^3.13.1"
    "mkdirp" "~0.5.1"
    "object.values" "^1.1.0"
    "sax" "~1.2.4"
    "stable" "^0.1.8"
    "unquote" "~1.1.1"
    "util.promisify" "~1.0.0"

"swiper@^5.2.0", "swiper@5.x":
  "integrity" "sha1-o1D2VL9oQm27ZReTgkklUS0iPA8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/swiper/-/swiper-5.4.5.tgz"
  "version" "5.4.5"
  dependencies:
    "dom7" "^2.1.5"
    "ssr-window" "^2.0.0"

"symbol-observable@^1.1.0":
  "integrity" "sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/symbol-observable/-/symbol-observable-1.2.0.tgz"
  "version" "1.2.0"

"table@^5.2.3":
  "integrity" "sha1-EpLRlQDOP4YFOwXw6Ofko7shB54="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/table/-/table-5.4.6.tgz"
  "version" "5.4.6"
  dependencies:
    "ajv" "^6.10.2"
    "lodash" "^4.17.14"
    "slice-ansi" "^2.1.0"
    "string-width" "^3.0.0"

"tapable@^0.1.8":
  "integrity" "sha1-KcNXB8K3DlDQdIK10gLo7URtr9Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tapable/-/tapable-0.1.10.tgz"
  "version" "0.1.10"

"tapable@^1.0.0", "tapable@^1.1.3":
  "integrity" "sha1-ofzMBrWNth/XpF2i2kT186Pme6I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tapable/-/tapable-1.1.3.tgz"
  "version" "1.1.3"

"tar@^6.0.2":
  "integrity" "sha1-Z2CjjwA6+hsv/Q/+npq70Oqz1iE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tar/-/tar-6.1.11.tgz"
  "version" "6.1.11"
  dependencies:
    "chownr" "^2.0.0"
    "fs-minipass" "^2.0.0"
    "minipass" "^3.0.0"
    "minizlib" "^2.1.1"
    "mkdirp" "^1.0.3"
    "yallist" "^4.0.0"

"tar@2.2.1":
  "integrity" "sha1-jk0qJWwOIYXGsYrWlK7JaLg8sdE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tar/-/tar-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "block-stream" "*"
    "fstream" "^1.0.2"
    "inherits" "2"

"terser-webpack-plugin@^1.4.3", "terser-webpack-plugin@^1.4.4":
  "integrity" "sha1-oheu+uozDnNP+sthIOwfoxLWBAs="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/terser-webpack-plugin/-/terser-webpack-plugin-1.4.5.tgz"
  "version" "1.4.5"
  dependencies:
    "cacache" "^12.0.2"
    "find-cache-dir" "^2.1.0"
    "is-wsl" "^1.1.0"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^4.0.0"
    "source-map" "^0.6.1"
    "terser" "^4.1.2"
    "webpack-sources" "^1.4.0"
    "worker-farm" "^1.7.0"

"terser@^4.1.2":
  "integrity" "sha1-oA5WNFYt4iOf1ATGSQUb9vwhFE8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/terser/-/terser-4.8.1.tgz"
  "version" "4.8.1"
  dependencies:
    "commander" "^2.20.0"
    "source-map" "~0.6.1"
    "source-map-support" "~0.5.12"

"text-segmentation@^1.0.3":
  "integrity" "sha1-UqOIFZ7//nRrJKY7oxG2rJ8teUM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/text-segmentation/-/text-segmentation-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "utrie" "^1.0.2"

"text-table@^0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"thenify-all@^1.0.0", "thenify-all@^1.6.0":
  "integrity" "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/thenify-all/-/thenify-all-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "thenify" ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  "integrity" "sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/thenify/-/thenify-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "any-promise" "^1.0.0"

"thread-loader@^2.1.3":
  "integrity" "sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/thread-loader/-/thread-loader-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "loader-runner" "^2.3.1"
    "loader-utils" "^1.1.0"
    "neo-async" "^2.6.0"

"throttle-debounce@^1.0.1":
  "integrity" "sha1-UYU9o3vmihVctugns1FKPEIuic0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/throttle-debounce/-/throttle-debounce-1.1.0.tgz"
  "version" "1.1.0"

"through@^2.3.6":
  "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^0.4.1":
  "integrity" "sha1-2/WGYDEVHsg1K7bE22SiKSqEC5s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/through2/-/through2-0.4.2.tgz"
  "version" "0.4.2"
  dependencies:
    "readable-stream" "~1.0.17"
    "xtend" "~2.1.1"

"through2@^2.0.0":
  "integrity" "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"through2@^2.0.3":
  "integrity" "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"thunky@^1.0.2":
  "integrity" "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/thunky/-/thunky-1.1.0.tgz"
  "version" "1.1.0"

"timed-out@^2.0.0":
  "integrity" "sha1-84sK6B03R9YoAB9B2vxlKs5nHAo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/timed-out/-/timed-out-2.0.0.tgz"
  "version" "2.0.0"

"timers-browserify@^2.0.4":
  "integrity" "sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/timers-browserify/-/timers-browserify-2.0.12.tgz"
  "version" "2.0.12"
  dependencies:
    "setimmediate" "^1.0.4"

"timsort@^0.3.0":
  "integrity" "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/timsort/-/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tiny-emitter@^2.0.0":
  "integrity" "sha1-HRpW7fxRxD6GPLtTgqcjMONVVCM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tiny-emitter/-/tiny-emitter-2.1.0.tgz"
  "version" "2.1.0"

"tinymce@^5.8.1":
  "integrity" "sha1-Aq72pn6RXxVZ5R+o+wBycNlmZ3g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tinymce/-/tinymce-5.10.5.tgz"
  "version" "5.10.5"

"tmp@^0.0.33":
  "integrity" "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tmp/-/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"to-array@0.1.4":
  "integrity" "sha1-F+bBH3PdTz10zaek/zI46a2b+JA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/to-array/-/to-array-0.1.4.tgz"
  "version" "0.1.4"

"to-arraybuffer@^1.0.0":
  "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-fast-properties@^1.0.3":
  "integrity" "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/to-fast-properties/-/to-fast-properties-1.0.3.tgz"
  "version" "1.0.3"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/to-object-path/-/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/to-regex-range/-/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/to-regex/-/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toidentifier@1.0.1":
  "integrity" "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/toidentifier/-/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"toposort@^1.0.0":
  "integrity" "sha1-LmhELZ9k7HILjMieZEOsbKqVACk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/toposort/-/toposort-1.0.7.tgz"
  "version" "1.0.7"

"tough-cookie@~2.5.0":
  "integrity" "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tough-cookie/-/tough-cookie-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "psl" "^1.1.28"
    "punycode" "^2.1.1"

"tr46@~0.0.3":
  "integrity" "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tr46/-/tr46-0.0.3.tgz"
  "version" "0.0.3"

"traverse@^0.6.6":
  "integrity" "sha1-y99WD9e5r2MlAv7UD5GMFX6pcTc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/traverse/-/traverse-0.6.6.tgz"
  "version" "0.6.6"

"tryer@^1.0.1":
  "integrity" "sha1-8shUBoALmw90yfdGW4HqrSQSUvg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tryer/-/tryer-1.0.1.tgz"
  "version" "1.0.1"

"ts-pnp@^1.1.6":
  "integrity" "sha1-pQCtCEsHmPHDBxrzkeZZEshrypI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ts-pnp/-/ts-pnp-1.2.0.tgz"
  "version" "1.2.0"

"tsconfig-paths@^3.14.1":
  "integrity" "sha1-ugc0WZ6Oo2yGJ5jpILzxYyd7E3o="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tsconfig-paths/-/tsconfig-paths-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "@types/json5" "^0.0.29"
    "json5" "^1.0.1"
    "minimist" "^1.2.6"
    "strip-bom" "^3.0.0"

"tslib@^1.9.0":
  "integrity" "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tsparticles@^1.43.1":
  "integrity" "sha1-H2s37kG2qwQ9ZqDiC/4IVaGuoAY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tsparticles/-/tsparticles-1.43.1.tgz"
  "version" "1.43.1"

"tty-browserify@0.0.0":
  "integrity" "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tty-browserify/-/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tunnel-agent@^0.6.0":
  "integrity" "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/tweetnacl/-/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-check@~0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/type-check/-/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-fest@^0.21.3":
  "integrity" "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^0.6.0":
  "integrity" "sha1-jSojcNPfiG61yQraHFv2GIrPg4s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/type-fest/-/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha1-CeJJ696FHTseSNJ8EFREZn8XuD0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/type-fest/-/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"type-is@~1.6.18":
  "integrity" "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/type-is/-/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"typedarray@^0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/typedarray/-/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"uglify-js@3.4.x":
  "integrity" "sha1-mtlWPY6zrN+404WX0q8dgV9qdV8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/uglify-js/-/uglify-js-3.4.10.tgz"
  "version" "3.4.10"
  dependencies:
    "commander" "~2.19.0"
    "source-map" "~0.6.1"

"unbox-primitive@^1.0.2":
  "integrity" "sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-bigints" "^1.0.2"
    "has-symbols" "^1.0.3"
    "which-boxed-primitive" "^1.0.2"

"underscore@~1.6.0":
  "integrity" "sha1-izixDKze9jM3uLJOT/htRa6lKag="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/underscore/-/underscore-1.6.0.tgz"
  "version" "1.6.0"

"unicode-canonical-property-names-ecmascript@^2.0.0":
  "integrity" "sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-match-property-ecmascript@^2.0.0":
  "integrity" "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^2.0.0"
    "unicode-property-aliases-ecmascript" "^2.0.0"

"unicode-match-property-value-ecmascript@^2.0.0":
  "integrity" "sha1-GgGqVyR8FMVouJd1pUk4eIGJpxQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-property-aliases-ecmascript@^2.0.0":
  "integrity" "sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"unidecode@0.1.8":
  "integrity" "sha1-77swFTi8RSRqmsjFWdcvAVMFBT4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unidecode/-/unidecode-0.1.8.tgz"
  "version" "0.1.8"

"union-value@^1.0.0":
  "integrity" "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/union-value/-/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"uniq@^1.0.1":
  "integrity" "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/uniq/-/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@^2.0.0":
  "integrity" "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/uniqs/-/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@^1.1.1":
  "integrity" "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unique-filename/-/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unique-slug/-/unique-slug-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "imurmurhash" "^0.1.4"

"universalify@^0.1.0":
  "integrity" "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/universalify/-/universalify-0.1.2.tgz"
  "version" "0.1.2"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unquote@~1.1.1":
  "integrity" "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unquote/-/unquote-1.1.1.tgz"
  "version" "1.1.1"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unset-value/-/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"unzip-response@^1.0.0":
  "integrity" "sha1-uYTwh3/AqJwsdzzB73tbIytbBv4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/unzip-response/-/unzip-response-1.0.2.tgz"
  "version" "1.0.2"

"upath@^1.1.1":
  "integrity" "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/upath/-/upath-1.2.0.tgz"
  "version" "1.2.0"

"update-browserslist-db@^1.0.9":
  "integrity" "sha1-KSTTknNno41cVVQTp84Tj8lfyxg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/update-browserslist-db/-/update-browserslist-db-1.0.9.tgz"
  "version" "1.0.9"
  dependencies:
    "escalade" "^3.1.1"
    "picocolors" "^1.0.0"

"upper-case@^1.1.1":
  "integrity" "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/upper-case/-/upper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@^4.2.2":
  "integrity" "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/urix/-/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-loader@^2.2.0", "url-loader@^4.1.1":
  "integrity" "sha1-KFBekFyuFYzwfJLKYi1/I35wpOI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/url-loader/-/url-loader-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "loader-utils" "^2.0.0"
    "mime-types" "^2.1.27"
    "schema-utils" "^3.0.0"

"url-parse-lax@^1.0.0":
  "integrity" "sha1-evjzA2Rem9eaJy56FKxovAYJ2nM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/url-parse-lax/-/url-parse-lax-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "prepend-http" "^1.0.1"

"url-parse@^1.5.10":
  "integrity" "sha1-nTwvc2wddd070r5QfcwRHx4uqcE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/url-parse/-/url-parse-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"url-slug@2.0.0":
  "integrity" "sha1-p4nVrtSZXA2VrzM3etHVxo1NcCc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/url-slug/-/url-slug-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unidecode" "0.1.8"

"url@^0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/url/-/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/use/-/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@~1.0.0":
  "integrity" "sha1-a693dLgO6w91INi4HQeYKlmruu4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/util.promisify/-/util.promisify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.2"
    "has-symbols" "^1.0.1"
    "object.getownpropertydescriptors" "^2.1.0"

"util.promisify@1.0.0":
  "integrity" "sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/util.promisify/-/util.promisify-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "define-properties" "^1.1.2"
    "object.getownpropertydescriptors" "^2.0.3"

"util@^0.11.0":
  "integrity" "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/util/-/util-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha1-evsa/lCAUkZInj23/g7TeTNqwPk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/util/-/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/utila/-/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"utrie@^1.0.2":
  "integrity" "sha1-1C/kTem8ARnCXef1ZKbtGyyHpkU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/utrie/-/utrie-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "base64-arraybuffer" "^1.0.2"

"uuid@^3.3.2":
  "integrity" "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/uuid/-/uuid-3.4.0.tgz"
  "version" "3.4.0"

"uuid@^8.3.1", "uuid@^8.3.2":
  "integrity" "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"v-contextmenu@^2.9.0":
  "integrity" "sha1-F1Onr8QgUfHtXLdcIEmKFv1+wzI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/v-contextmenu/-/v-contextmenu-2.9.0.tgz"
  "version" "2.9.0"

"v-dropdown@2.1.1":
  "integrity" "sha1-XhpJ1hwpE6ar3aPMtGLiZH+dae0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/v-dropdown/-/v-dropdown-2.1.1.tgz"
  "version" "2.1.1"

"v-region@2.2.2":
  "integrity" "sha1-WrswXysE8P/ZoCW4TACCHaJzCKU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/v-region/-/v-region-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "v-dropdown" "2.1.1"

"v8-compile-cache@^2.0.3":
  "integrity" "sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha1-/JH2uce6FchX9MssXe/uw51PQQo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"validate-npm-package-name@^2.0.1":
  "integrity" "sha1-9laVsi9zJEQgGaPH+jmm5/0pkIU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/validate-npm-package-name/-/validate-npm-package-name-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "builtins" "0.0.7"

"vanilla-picker@^2.12.1":
  "integrity" "sha1-bmGe7PVTiRuNLQQrdFojyR8Z80w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vanilla-picker/-/vanilla-picker-2.12.1.tgz"
  "version" "2.12.1"
  dependencies:
    "@sphinxxxx/color-conversion" "^2.2.2"

"vant@^2.12.48":
  "integrity" "sha1-7lB6M35uALGKy/F0z68cXi9Seog="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vant/-/vant-2.12.50.tgz"
  "version" "2.12.50"
  dependencies:
    "@babel/runtime" "7.x"
    "@vant/icons" "^1.7.1"
    "@vant/popperjs" "^1.1.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "vue-lazyload" "1.2.3"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"vendors@^1.0.0":
  "integrity" "sha1-4rgApT56Kbk1BsPPQRANFsTErY4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vendors/-/vendors-1.0.4.tgz"
  "version" "1.0.4"

"verror@1.10.0":
  "integrity" "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/verror/-/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"vm-browserify@^1.0.1":
  "integrity" "sha1-eGQcSIuObKkadfUR56OzKobl3aA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vm-browserify/-/vm-browserify-1.1.2.tgz"
  "version" "1.1.2"

"vue-awesome-swiper@^4.1.1":
  "integrity" "sha1-j3qyIa0AMCHXVrhqphj0KZJJAP4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-awesome-swiper/-/vue-awesome-swiper-4.1.1.tgz"
  "version" "4.1.1"

"vue-class-component@*", "vue-class-component@<8":
  "integrity" "sha1-hHHgN7jkdi9aRkaG4Z5a/HCFAuQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-class-component/-/vue-class-component-7.2.6.tgz"
  "version" "7.2.6"

"vue-clipboard2@^0.3.3":
  "integrity" "sha1-Mx/shfnU8XXrDU/q7013M4VirzY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-clipboard2/-/vue-clipboard2-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "clipboard" "^2.0.0"

"vue-codemirror@^4.0.6":
  "integrity" "sha1-t4a7gNjXYqk6q45G95qBAG8EN8Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-codemirror/-/vue-codemirror-4.0.6.tgz"
  "version" "4.0.6"
  dependencies:
    "codemirror" "^5.41.0"
    "diff-match-patch" "^1.0.0"

"vue-eslint-parser@^7.0.0":
  "integrity" "sha1-IUtd6pYQB/z/su5luJEjB2KNDa8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-eslint-parser/-/vue-eslint-parser-7.11.0.tgz"
  "version" "7.11.0"
  dependencies:
    "debug" "^4.1.1"
    "eslint-scope" "^5.1.1"
    "eslint-visitor-keys" "^1.1.0"
    "espree" "^6.2.1"
    "esquery" "^1.4.0"
    "lodash" "^4.17.21"
    "semver" "^6.3.0"

"vue-grid-layout@^2.3.12":
  "integrity" "sha1-i2BOKYMmZrmLOhhqbql7PR7VIxA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-grid-layout/-/vue-grid-layout-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "@interactjs/actions" "1.10.2"
    "@interactjs/auto-scroll" "1.10.2"
    "@interactjs/auto-start" "1.10.2"
    "@interactjs/dev-tools" "1.10.2"
    "@interactjs/interactjs" "1.10.2"
    "@interactjs/modifiers" "1.10.2"
    "element-resize-detector" "^1.2.1"

"vue-hot-reload-api@^2.3.0":
  "integrity" "sha1-UylVzB6yCKPZkLOp+acFdGV+CPI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz"
  "version" "2.3.4"

"vue-lazyload@^1.3.3":
  "integrity" "sha1-KYiZj2vBogJyaPWwz/p6fjDmzLQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-lazyload/-/vue-lazyload-1.3.4.tgz"
  "version" "1.3.4"

"vue-lazyload@1.2.3":
  "integrity" "sha1-kB+ewVx+bKeHgaK65KNDaGve2yw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-lazyload/-/vue-lazyload-1.2.3.tgz"
  "version" "1.2.3"

"vue-loader-v16@npm:vue-loader@^16.1.0":
  "integrity" "sha1-1D5nXe9bqTRdbH8FkUwT2GGZcIc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-loader/-/vue-loader-16.8.3.tgz"
  "version" "16.8.3"
  dependencies:
    "chalk" "^4.1.0"
    "hash-sum" "^2.0.0"
    "loader-utils" "^2.0.0"

"vue-loader@^15.9.2":
  "integrity" "sha1-KhJpXEIaKizCE48FqUnQTtCG44s="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-loader/-/vue-loader-15.10.0.tgz"
  "version" "15.10.0"
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "vue-hot-reload-api" "^2.3.0"
    "vue-style-loader" "^4.1.0"

"vue-pdf@^4.3.0":
  "integrity" "sha1-1feQ7nln57eqkIm5exGrFo4Z29A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-pdf/-/vue-pdf-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "babel-plugin-syntax-dynamic-import" "^6.18.0"
    "loader-utils" "^1.4.0"
    "pdfjs-dist" "2.6.347"
    "raw-loader" "^4.0.2"
    "vue-resize-sensor" "^2.0.0"
    "worker-loader" "^2.0.0"

"vue-property-decorator@<10":
  "integrity" "sha1-JmourGG6ZSfi5oppM8+5j92rVFc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-property-decorator/-/vue-property-decorator-9.1.2.tgz"
  "version" "9.1.2"

"vue-ref@^2.0.0":
  "integrity" "sha1-SDCE1zKr7RHaeWd4qCZqOvDqGpw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-ref/-/vue-ref-2.0.0.tgz"
  "version" "2.0.0"

"vue-resize-sensor@^2.0.0":
  "integrity" "sha1-Olh/1oAuFohwnPLFqtrnoAdZUr8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-resize-sensor/-/vue-resize-sensor-2.0.0.tgz"
  "version" "2.0.0"

"vue-router@^3.2.0":
  "integrity" "sha1-lYR9Urmn4/E2HLYFyOZEHyAq+tg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-router/-/vue-router-3.6.5.tgz"
  "version" "3.6.5"

"vue-socket.io@^3.0.10":
  "integrity" "sha1-dhNb9QQ7r0hMZIY2p6mCK0wRwIQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-socket.io/-/vue-socket.io-3.0.10.tgz"
  "version" "3.0.10"
  dependencies:
    "socket.io-client" "^2.1.1"

"vue-style-loader@^4.1.0", "vue-style-loader@^4.1.2":
  "integrity" "sha1-bVWGOlH6dXqyTonZNxRlByqnvDU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-style-loader/-/vue-style-loader-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-template-compiler@^2.0.0", "vue-template-compiler@^2.6.11":
  "integrity" "sha1-wEcE749JixUxMAGJk+VjCdRpgIA="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-template-compiler/-/vue-template-compiler-2.6.11.tgz"
  "version" "2.6.11"
  dependencies:
    "de-indent" "^1.0.2"
    "he" "^1.1.0"

"vue-template-es2015-compiler@^1.9.0":
  "integrity" "sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.9.1.tgz"
  "version" "1.9.1"

"vue-virtual-scroll-list@^2.3.4":
  "integrity" "sha1-tYmsYkX6+FfDUJD4VOWdZT6QYmw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue-virtual-scroll-list/-/vue-virtual-scroll-list-2.3.5.tgz"
  "version" "2.3.5"

"vue@*", "vue@^2 || ^3.0.0-0", "vue@^2.0.0", "vue@^2.2.0", "vue@^2.4.3", "vue@^2.5.0", "vue@^2.5.17", "vue@^2.6.0", "vue@^2.6.11", "vue@<3", "vue@>= 2.6.0", "vue@2.x":
  "integrity" "sha1-dllNh31LEiNEBuhONSdcbVFBJcU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vue/-/vue-2.6.11.tgz"
  "version" "2.6.11"

"vuedraggable@^2.24.3":
  "integrity" "sha1-Q8k4SbdGokzlA+Ej1bJZxwG6DRk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vuedraggable/-/vuedraggable-2.24.3.tgz"
  "version" "2.24.3"
  dependencies:
    "sortablejs" "1.10.2"

"vuex@^3.4.0":
  "integrity" "sha1-I2vAhqhww655lG8QfxbeWdWJXnE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/vuex/-/vuex-3.6.2.tgz"
  "version" "3.6.2"

"watch-size@^2.0.0":
  "integrity" "sha1-CW7ijQNlvX6gPZyL8fL1CnO+FHQ="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/watch-size/-/watch-size-2.0.0.tgz"
  "version" "2.0.0"

"watchpack-chokidar2@^2.0.1":
  "integrity" "sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "chokidar" "^2.1.8"

"watchpack@^1.7.4":
  "integrity" "sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/watchpack/-/watchpack-1.7.5.tgz"
  "version" "1.7.5"
  dependencies:
    "graceful-fs" "^4.1.2"
    "neo-async" "^2.5.0"
  optionalDependencies:
    "chokidar" "^3.4.1"
    "watchpack-chokidar2" "^2.0.1"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/wbuf/-/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"wcwidth@^1.0.1":
  "integrity" "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/wcwidth/-/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webidl-conversions@^3.0.0":
  "integrity" "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  "version" "3.0.1"

"webpack-bundle-analyzer@^3.8.0":
  "integrity" "sha1-9vlNsQj7V05BWtMT3kGicH0z7zw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/webpack-bundle-analyzer/-/webpack-bundle-analyzer-3.9.0.tgz"
  "version" "3.9.0"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-walk" "^7.1.1"
    "bfj" "^6.1.1"
    "chalk" "^2.4.1"
    "commander" "^2.18.0"
    "ejs" "^2.6.1"
    "express" "^4.16.3"
    "filesize" "^3.6.1"
    "gzip-size" "^5.0.0"
    "lodash" "^4.17.19"
    "mkdirp" "^0.5.1"
    "opener" "^1.5.1"
    "ws" "^6.0.0"

"webpack-chain@^6.4.0":
  "integrity" "sha1-TycoTLu2N+PI+970Pu9YjU2GEgY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/webpack-chain/-/webpack-chain-6.5.1.tgz"
  "version" "6.5.1"
  dependencies:
    "deepmerge" "^1.5.2"
    "javascript-stringify" "^2.0.1"

"webpack-dev-middleware@^3.7.2":
  "integrity" "sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/webpack-dev-middleware/-/webpack-dev-middleware-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "memory-fs" "^0.4.1"
    "mime" "^2.4.4"
    "mkdirp" "^0.5.1"
    "range-parser" "^1.2.1"
    "webpack-log" "^2.0.0"

"webpack-dev-server@^3.11.0":
  "integrity" "sha1-jIa50oEr8TXTybzm8HtxjjD3w9M="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/webpack-dev-server/-/webpack-dev-server-3.11.3.tgz"
  "version" "3.11.3"
  dependencies:
    "ansi-html-community" "0.0.8"
    "bonjour" "^3.5.0"
    "chokidar" "^2.1.8"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^1.6.0"
    "debug" "^4.1.1"
    "del" "^4.1.1"
    "express" "^4.17.1"
    "html-entities" "^1.3.1"
    "http-proxy-middleware" "0.19.1"
    "import-local" "^2.0.0"
    "internal-ip" "^4.3.0"
    "ip" "^1.1.5"
    "is-absolute-url" "^3.0.3"
    "killable" "^1.0.1"
    "loglevel" "^1.6.8"
    "opn" "^5.5.0"
    "p-retry" "^3.0.1"
    "portfinder" "^1.0.26"
    "schema-utils" "^1.0.0"
    "selfsigned" "^1.10.8"
    "semver" "^6.3.0"
    "serve-index" "^1.9.1"
    "sockjs" "^0.3.21"
    "sockjs-client" "^1.5.0"
    "spdy" "^4.0.2"
    "strip-ansi" "^3.0.1"
    "supports-color" "^6.1.0"
    "url" "^0.11.0"
    "webpack-dev-middleware" "^3.7.2"
    "webpack-log" "^2.0.0"
    "ws" "^6.2.1"
    "yargs" "^13.3.2"

"webpack-log@^2.0.0":
  "integrity" "sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/webpack-log/-/webpack-log-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-colors" "^3.0.0"
    "uuid" "^3.3.2"

"webpack-merge@^4.2.2":
  "integrity" "sha1-onxS6ng9E5iv0gh/VH17nS9DY00="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/webpack-merge/-/webpack-merge-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "lodash" "^4.17.15"

"webpack-sources@^1.1.0", "webpack-sources@^1.4.0", "webpack-sources@^1.4.1", "webpack-sources@^1.4.3":
  "integrity" "sha1-7t2OwLko+/HL/plOItLYkPMwqTM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/webpack-sources/-/webpack-sources-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack@^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^3.0.0 || ^4.0.0-alpha.0 || ^4.0.0", "webpack@^3.0.0 || ^4.1.0 || ^5.0.0-0", "webpack@^4.0.0", "webpack@^4.0.0 || ^5.0.0", "webpack@^4.36.0 || ^5.0.0", "webpack@^4.4.0", "webpack@>=1.11.0", "webpack@>=2", "webpack@>=2.0.0 <5.0.0", "webpack@>=4.0.0":
  "integrity" "sha1-v5tEBOogoHNgXgoBHRiNd8tq1UI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/webpack/-/webpack-4.46.0.tgz"
  "version" "4.46.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "acorn" "^6.4.1"
    "ajv" "^6.10.2"
    "ajv-keywords" "^3.4.1"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^4.5.0"
    "eslint-scope" "^4.0.3"
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^2.4.0"
    "loader-utils" "^1.2.3"
    "memory-fs" "^0.4.1"
    "micromatch" "^3.1.10"
    "mkdirp" "^0.5.3"
    "neo-async" "^2.6.1"
    "node-libs-browser" "^2.2.1"
    "schema-utils" "^1.0.0"
    "tapable" "^1.1.3"
    "terser-webpack-plugin" "^1.4.3"
    "watchpack" "^1.7.4"
    "webpack-sources" "^1.4.1"

"websocket-driver@^0.7.4", "websocket-driver@>=0.5.1":
  "integrity" "sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/websocket-driver/-/websocket-driver-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "http-parser-js" ">=0.5.1"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha1-f4RzvIOd/YdgituV1+sHUhFXikI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  "version" "0.1.4"

"whatwg-url@^5.0.0":
  "integrity" "sha1-lmRU6HZUYuN2RNNib2dCzotwll0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/whatwg-url/-/whatwg-url-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "tr46" "~0.0.3"
    "webidl-conversions" "^3.0.0"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/which-module/-/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.9":
  "integrity" "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"word-wrap@~1.2.3":
  "integrity" "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/word-wrap/-/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"worker-farm@^1.7.0":
  "integrity" "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/worker-farm/-/worker-farm-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "errno" "~0.1.7"

"worker-loader@^2.0.0":
  "integrity" "sha1-Rf2j73asqBV3GokQc5nuQRm0MKw="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/worker-loader/-/worker-loader-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "loader-utils" "^1.0.0"
    "schema-utils" "^0.4.0"

"wrap-ansi@^3.0.1":
  "integrity" "sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/wrap-ansi/-/wrap-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "string-width" "^2.1.1"
    "strip-ansi" "^4.0.0"

"wrap-ansi@^5.1.0":
  "integrity" "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/wrap-ansi/-/wrap-ansi-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "string-width" "^3.0.0"
    "strip-ansi" "^5.0.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha1-6Tk7oHEC5skaOyIUePAlfNKFblM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write@1.0.3":
  "integrity" "sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/write/-/write-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "mkdirp" "^0.5.1"

"ws@^6.0.0":
  "integrity" "sha1-3Vzb1XqZeZFgl2UtePHMX66gwy4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ws/-/ws-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "async-limiter" "~1.0.0"

"ws@^6.2.1":
  "integrity" "sha1-3Vzb1XqZeZFgl2UtePHMX66gwy4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ws/-/ws-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "async-limiter" "~1.0.0"

"ws@~7.4.2":
  "integrity" "sha1-VlTKjs3u5HwzqaS/bSjivimAN3w="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/ws/-/ws-7.4.6.tgz"
  "version" "7.4.6"

"xml@1.0.1":
  "integrity" "sha1-eLpyAgApxbyHuKgaPPzXS0ovweU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/xml/-/xml-1.0.1.tgz"
  "version" "1.0.1"

"xmlhttprequest-ssl@~1.6.2":
  "integrity" "sha1-A7cThzsBZZ36LBxdBWBlsn3cLeY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/xmlhttprequest-ssl/-/xmlhttprequest-ssl-1.6.3.tgz"
  "version" "1.6.3"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"xtend@~2.1.1":
  "integrity" "sha1-bv7MKk2tjmlixJAbM3znuoe10os="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/xtend/-/xtend-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "object-keys" "~0.4.0"

"y18n@^4.0.0":
  "integrity" "sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/y18n/-/y18n-4.0.3.tgz"
  "version" "4.0.3"

"y18n@^5.0.5":
  "integrity" "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yallist/-/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.2":
  "integrity" "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yargs-parser@^13.1.2":
  "integrity" "sha1-Ew8JcC667vJlDVTObj5XBvek+zg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yargs-parser/-/yargs-parser-13.1.2.tgz"
  "version" "13.1.2"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^18.1.2":
  "integrity" "sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yargs-parser/-/yargs-parser-18.1.3.tgz"
  "version" "18.1.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^20.2.2":
  "integrity" "sha1-LrfcOwKJcY/ClfNidThFxBoMlO4="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs@^13.3.2":
  "integrity" "sha1-rX/+/sGqWVZayRX4Lcyzipwxot0="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yargs/-/yargs-13.3.2.tgz"
  "version" "13.3.2"
  dependencies:
    "cliui" "^5.0.0"
    "find-up" "^3.0.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^3.0.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^13.1.2"

"yargs@^15.3.1":
  "integrity" "sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yargs/-/yargs-15.4.1.tgz"
  "version" "15.4.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.2"

"yargs@^16.0.0":
  "integrity" "sha1-HIK/D2tqZur85+8w43b0mhJHf2Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yargs/-/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yargs@^16.0.3":
  "integrity" "sha1-HIK/D2tqZur85+8w43b0mhJHf2Y="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yargs/-/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yeast@0.1.2":
  "integrity" "sha1-AI4G2AlDIMNy28L47XagymyKxBk="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yeast/-/yeast-0.1.2.tgz"
  "version" "0.1.2"

"yorkie@^2.0.0":
  "integrity" "sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/yorkie/-/yorkie-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "execa" "^0.8.0"
    "is-ci" "^1.0.10"
    "normalize-path" "^1.0.0"
    "strip-indent" "^2.0.0"

"zrender@4.3.2":
  "integrity" "sha1-7HQy+UFcgsc1hLa3uMR+GwFiCcY="
  "resolved" "https://art.haizhi.com:443/artifactory/api/npm/npm/zrender/-/zrender-4.3.2.tgz"
  "version" "4.3.2"
