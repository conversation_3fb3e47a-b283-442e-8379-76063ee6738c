// console.log('service-worker!')
const version = '4.0.6';
const cachePrefix = 'fuxi:v';
const cacheName = cachePrefix + version;
// const preCacheList = ['./chart-common-lib/d3.min.js', './chart-common-lib/echarts.min.js', './chart-common-lib/highcharts.min.js', './chart-common-lib/jquery-3.6.0.min.js', './chart-common-lib/lodash.min.js', './chart-common-lib/three.min.js', './chart-common-lib/umd.js', './lib/system.js', './themes/dark.css', './themes/light.css'];
const preCacheList = [];
const ajaxWhiteList = new Set(['/api/packages/list', '/api/usershare/userlist']);
// fetch黑名单
const fetchBlackList = new Set([
  '.css',
  '.m3u8',
  '.flv',
  '.ts',
  'rtmp://',
  'rtmps://'
])

function matchKey (obj, url) {
  for (const key of obj) {
    if (new RegExp(key).test(url)) {
      return true;
    }
  }
  return false;
}

this.addEventListener('install', function (event) {
  this.skipWaiting();
  event.waitUntil(
    caches.open(cacheName).then(function (cache) {
      return cache.addAll(preCacheList);
    })
  );
});

this.addEventListener('fetch', function (event) {
  // 匹配到黑名单的不要缓存
  if (matchKey(fetchBlackList, event.request.url)) {
    return
  }

  if (event.request.method === 'GET') {
    if (matchKey(ajaxWhiteList, event.request.url)) {
      // ajax白名单请求以缓存优先
      event.respondWith(caches.open(cacheName).then(function (cache) {
        return cache.match(event.request).then(function (response) {
          if (response) {
            // 先显示缓存，后台更新缓存
            fetch(event.request).then(function (networkResponse) {
              var cloneResponse = networkResponse.clone();
              if (cloneResponse.status !== 206) {
                cache.put(event.request, cloneResponse);
              }
            })
            return response;
          } else {
            return fetch(event.request).then(function (networkResponse) {
              var cloneResponse = networkResponse.clone();
              if (cloneResponse.status !== 206) {
                cache.put(event.request, cloneResponse);
              }
              return networkResponse;
            })
          }
        })
      }));
    } else if (!new RegExp(/\/api\//).test(event.request.url) && event.request.mode !== 'navigate' && !new RegExp(/\/screenIcons\/symbol\//).test(event.request.url)) {
      event.respondWith(caches.open(cacheName).then(function (cache) {
        return cache.match(event.request).then(function (response) {
          if (response) {
            return response;
          } else {
            return fetch(event.request).then(function (networkResponse) {
              var cloneResponse = networkResponse.clone();
              if (cloneResponse.status !== 206 && cloneResponse.headers.get('Content-Type') && !cloneResponse.headers.get('Content-Type').includes('text/html')) {
                cache.put(event.request, cloneResponse);
              }
              return networkResponse;
            })
          }
        })
      }));
    }
  }
});

this.addEventListener('activate', function (event) {
  var setOfExpectedUrls = new Set(preCacheList.map(function (item) {
    var url = new URL(item, self.location);
    return url.toString();
  }));

  caches.keys().then(function (cacheNames) {
    return Promise.all(
      cacheNames.filter(function (cacheVersion) {
        return cacheName !== cacheVersion
      }).map(function (cacheVersion) {
        return caches.delete(cacheVersion);
      })
    );
  })
  event.waitUntil(
    caches.open(cacheName).then(function (cache) {
      return cache.keys().then(function (existingRequests) {
        return Promise.all(
          existingRequests.map(function (existingRequest) {
            if (!matchKey(setOfExpectedUrls, existingRequest.url)) {
              return cache.delete(existingRequest);
            }
          })
        );
      });
    }).then(function () {
      return this.clients.claim();
    })
  );
});
