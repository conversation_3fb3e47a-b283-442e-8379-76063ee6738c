!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).quarkDom=t()}(this,(function(){"use strict";function e(e={}){return Object.keys(e).map((t=>void 0!==e[t]?t+": "+e[t]:"")).join(";")+";"}const t={toCssVar(e={},t="--"){const r={};for(const a in e)r[`${t}${a}`]=e[a];return r},formatBox(t={},r=!1){let a={};if(void 0!==t.width){let e=t.widthUnit,r=t.width;switch(e){case"flex":a.flex=r;break;case"%":case"px":a.width=r+e}}if(void 0!==t.height){let e=t.heightUnit;a.height=("auto"===e?"":t.height)+e}return r?a:Object.keys(a).length?e(a):""},formatPadding(e={},t=!1){const r=`${e.top}px ${e.right}px ${e.bottom}px ${e.left}px`;return t?{padding:r}:`padding: ${r};`},formatText(r={},a=!1){let{font:i={}}=r,n={"font-family":i.fontFamily,"font-size":`${i.fontSize}px`,"font-weight":i.fontWeight,color:i.color,"font-style":r.italic?"italic":"","letter-spacing":`${r.wordsSpace}px`,"text-indent":`${r.textIndex}px`};return r.shadow&&Object.assign(n,t.formatTextShadow(r.shadow,!0)),r.reflect&&Object.assign(n,t.formatReflect(r.reflect,!0)),r.gradient&&Object.assign(n,t.formatTextGradient(r.gradient,!0)),a?n:e(n)},formatTextLine(t={},r=!1){let a={"justify-content":t.alignHor,"align-items":t.alignVer};return r?a:e(a)},formatImage(t={},r=!1){if(!1===t.show)return r?{}:"";let a={"background-image":`url(${t.url})`,"background-size":t.size,"background-position":`${t.positionX} ${t.positionY}`,"background-repeat":t.repeat,"align-self":t.positionY};return r?a:e(a)},formatBackground(t={},r=!1){if(!1===t.show)return r?{}:"";let a;switch(t.type){case"pure":a={"background-color":t.pure};break;case"gradient":let{gradient:e={}}=t;a={"background-image":`linear-gradient(${e.deg}deg, ${e.start}, ${e.end})`};break;case"image":let{image:r={}}=t,{changeColor:i={}}=r;a={"background-size":r.size,"background-position":`${r.positionX} ${r.positionY}`,"background-repeat":r.repeat};let n=[`url(${r.url})`];i&&i.show&&(n.push(function(e={}){return"radial-gradient"===e.type?`radial-gradient(${e.shape}, ${e.start} ${e.startScale}%, ${e.end} ${e.endScale}%)`:`linear-gradient(${e.deg}deg, ${e.start} ${e.startScale}%, ${e.end} ${e.endScale}%)`}(i.data)),a["mix-blend-mode"]=i.mixMode),a["background-image"]=n.join(",")}return r?a||{}:a?e(a):""},formatBorder(t={},r=!1){if(!1===t.show)return r?{}:"";let a={"border-style":t.style,"border-color":t.color,"border-width":`${t.width}px`};return r?a:e(a)},formatTextShadow(t={},r=!1){if(!1===t.show)return r?{}:"";const a={"text-shadow":`${t.shadowX}px ${t.shadowY}px ${t.shadowBlur}px ${t.shadowColor}`};return r?a:e(a)},formatReflect(t={},r=!1){if(!1===t.show)return r?{}:"";const a={"-webkit-box-reflect":`${t.direction} ${t.distance}px linear-gradient(transparent, transparent 0%, rgba(0, 0, 0, ${t.opacity}))`};return r?a:e(a)},formatTextGradient(t={},r=!1){if(!0===t.show){let a=t.data;const i={"-webkit-background-clip":"text","-webkit-text-fill-color":"transparent"};return"radial-gradient"===a.type?i["background-image"]=`radial-gradient(${a.shape}, ${a.start} ${a.startScale}%, ${a.end} ${a.endScale}%)`:i["background-image"]=`linear-gradient(${a.deg}deg, ${a.start} ${a.startScale}%, ${a.end} ${a.endScale}%)`,r?i:e(i)}return r?{}:""},formatMask(t={},r=!1){if(t&&t.show){let a={"-webkit-mask-image":`url(${t.shape})`,"-webkit-mask-position":`${t.positionX} ${t.positionY}`,"-webkit-mask-repeat":"no-repeat","-webkit-mask-size":t.size};return r?a:e(a)}return r?{}:""},formatStyle:e};return t}));
