!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).quarkChart=t()}(this,(function(){"use strict";const e={fontFamily:"思源黑体Medium",fontWeight:"normal",fontSize:12,color:"#fff"},t="rgba(255,255,255,0.1)",o={formatLegend(t={}){const{show:o,layout:i={},tag:l={},textStyle:a={}}=t,{value:n={}}=i;let r="";return r=!1===t.showOrient?"horizontal":t.orient,{show:void 0===o||o,icon:l.style,itemWidth:l.size,itemHeight:l.size,itemGap:12,top:n.top,left:n.left,orient:r,textStyle:a||e}},formatTooltip(t={}){const{show:o,type:i,textStyle:l={},bgBox:a={}}=t,{bgColor:n,padding:r,border:s={}}=a;let d={show:void 0===o||o,padding:r,backgroundColor:n,borderWidth:s.borderWidth,borderColor:s.borderColor,textStyle:l||e};if(!0===t.showType){if(d.trigger=i,"axis"===i){const{crossStyle:e={}}=t,{color:o={}}=e,{center:i,start:l}=o;d.axisPointer={lineStyle:{width:e.width,type:e.lineType||"solid",color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:l||"rgba(0, 255, 233,0)"},{offset:.5,color:i||"rgba(255, 255, 255,1)"},{offset:1,color:l||"rgba(0, 255, 233,0)"}],global:!1}}}}}else d.trigger="item";return d},formatXAxis(o=[],i={}){var l,a;const{show:n,dataType:r,xAxisLabel:s={},xAxisname:d={},xAxisLine:h={},xSplitLine:c={}}=i;let y=o.map((e=>e.x));y=[...new Set(y)];let f={show:void 0===n||n,name:d.content,nameLocation:d.location||"end",nameGap:d.offset,nameTextStyle:d.text||e,axisLine:{show:!(!h||!h.show),lineStyle:{color:h&&h.color||t,width:h&&h.weight}},axisLabel:{show:!h||void 0===s.show||h&&s.show,rotate:s.rotate,interval:null!==(l=s.interval)&&void 0!==l?l:0,width:null!==(a=s.width)&&void 0!==a?a:70,overflow:s.overflow||"truncate",textStyle:s.text||e},axisTick:{show:!1},splitLine:{show:!(!c||!c.show),lineStyle:{color:c&&c.color||t,width:c&&c.width,type:c&&c.type||"solid"}}};return!1!==i.showDataType?(f.data=y,f.type=r||"category"):(f.type="value",delete f.data),f},formatYAxis(o=[],i={}){var l;const{show:a,dataType:n,yAxisLabel:r={},yAxisname:s={},yAxisLine:d={},ySplitLine:h={}}=i;let c=o.map((e=>e.x));c=[...new Set(c)];let y=_.min(_.map(o,"y"))-r.min,f={show:void 0===a||a,name:s.content,nameLocation:s.location||"end",nameTextStyle:s.text||e,nameGap:s.offset,splitLine:{show:!(!h||!h.show),lineStyle:{color:h&&h.color||t,type:h&&h.type||"solid",width:h&&h.width}},axisLine:{show:!(!h||!d.show),lineStyle:{color:h&&d.color||t,width:h&&d.weight}},axisLabel:{show:void 0===r.show||r.show,rotate:r.rotate,width:null!==(l=r.width)&&void 0!==l?l:100,overflow:r.overflow||"none",textStyle:r.text||e},axisTick:{show:!1}};return!1===i.showDataType?f.type="value":(f.type=n,f.data=c),!1!==i.showMin?f.min=y:delete f.min,f},formatGrid(e={}){let{top:t,bottom:o,left:i,right:l}=e;return{top:t,bottom:o,left:i,right:l,containLabel:!0}},formatGuide(e=[],t=[],o=[],i){let l=_.map(e,"y");const a=_.max(l),n=_.min(l),r=(a+n)/2;if(t.length)for(let l=0;l<t.length;l++){const{type:s,compute:d,fixed:h,lineStyle:c={},dataSource:y="",label:f={}}=t[l],{text:w={}}=f;let p=0;switch(s){case"compute":switch(d){case"max":p=a;break;case"min":p=n;break;case"average":p=r}break;case"fixed":p=Number(h);break;case"dataSource":p=Number(e[0][y])||0}o.push({type:"line",markLine:{symbol:"none",lineStyle:{normal:{type:c.type,width:parseFloat(c.width/10),color:c.color}},label:{position:f.position,fontSize:w.fontSize,fontFamily:w.fontFamily,fontWeight:w.fontWeight,color:w.color}}});let m=[];m="y"===i?[{yAxis:p}]:[{xAxis:p}],o[o.length-1].markLine.data=m}return o}};return o}));
