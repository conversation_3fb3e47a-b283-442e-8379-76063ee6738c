!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).quarkDom=t()}(this,(function(){"use strict";function e(e={}){return Object.keys(e).map((t=>void 0!==e[t]?t+": "+e[t]:"")).join(";")+";"}const t={formatBox(t={}){let r={};if(void 0!==t.width){let e=t.widthUnit,a=t.width;switch(e){case"flex":r.flex=a;break;case"%":case"px":r.width=a+e}}if(void 0!==t.height){let e=t.heightUnit;r.height=("auto"===e?"":t.height)+e}return Object.keys(r).length?e(r):""},formatPadding:(e={})=>`padding: ${e.top}px ${e.right}px ${e.bottom}px ${e.left}px;`,formatText(r={}){let{font:a={}}=r,i=e({"font-family":a.fontFamily,"font-size":`${a.fontSize}px`,"font-weight":a.fontWeight,color:a.color,"font-style":r.italic?"italic":"","letter-spacing":`${r.wordsSpace}px`,"text-indent":`${r.textIndex}px`});return r.shadow&&(i+=t.formatTextShadow(r.shadow)),r.reflect&&(i+=t.formatReflect(r.reflect)),r.gradient&&(i+=t.formatTextGradient(r.gradient)),i},formatTextLine:(t={})=>e({"justify-content":t.alignHor,"align-items":t.alignVer}),formatImage(t={}){if(!1===t.show)return"";return e({"background-image":`url(${t.url})`,"background-size":t.size,"background-position":`${t.positionX} ${t.positionY}`,"background-repeat":t.repeat,"align-self":t.positionY})},formatBackground(t={}){if(!1===t.show)return"";let r;switch(t.type){case"pure":r={"background-color":t.pure};break;case"gradient":let{gradient:e={}}=t;r={"background-image":`linear-gradient(${e.deg}deg, ${e.start}, ${e.end})`};break;case"image":let{image:a={}}=t,{changeColor:i={}}=a;r={"background-size":a.size,"background-position":`${a.positionX} ${a.positionY}`,"background-repeat":a.repeat};let o=[`url(${a.url})`];i&&i.show&&(o.push(function(e={}){return"radial-gradient"===e.type?`radial-gradient(${e.shape}, ${e.start} ${e.startScale}%, ${e.end} ${e.endScale}%)`:`linear-gradient(${e.deg}deg, ${e.start} ${e.startScale}%, ${e.end} ${e.endScale}%)`}(i.data)),r["mix-blend-mode"]=i.mixMode),r["background-image"]=o.join(",")}return r?e(r):""},formatBorder(t={}){if(!1===t.show)return"";return e({"border-style":t.style,"border-color":t.color,"border-width":`${t.width}px`})},formatTextShadow(t={}){if(!1===t.show)return"";return e({"text-shadow":`${t.shadowX}px ${t.shadowY}px ${t.shadowBlur}px ${t.shadowColor}`})},formatReflect(t={}){if(!1===t.show)return"";return e({"-webkit-box-reflect":`${t.direction} ${t.distance}px linear-gradient(transparent, transparent 0%, rgba(0, 0, 0, ${t.opacity}))`})},formatTextGradient(t={}){if(!0===t.show){let r=t.data;const a={"-webkit-background-clip":"text","-webkit-text-fill-color":"transparent"};return"radial-gradient"===r.type?a["background-image"]=`radial-gradient(${r.shape}, ${r.start} ${r.startScale}%, ${r.end} ${r.endScale}%)`:a["background-image"]=`linear-gradient(${r.deg}deg, ${r.start} ${r.startScale}%, ${r.end} ${r.endScale}%)`,e(a)}return""},formatMask(t={}){if(t&&t.show){return e({"-webkit-mask-image":`url(${t.shape})`,"-webkit-mask-position":`${t.positionX} ${t.positionY}`,"-webkit-mask-repeat":"no-repeat","-webkit-mask-size":t.size})}return""},formatStyle:e};return t}));
