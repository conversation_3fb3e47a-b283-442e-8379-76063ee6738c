!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o():"function"==typeof define&&define.amd?define(o):(e="undefined"!=typeof globalThis?globalThis:e||self).quarkChart=o()}(this,(function(){"use strict";const e={fontFamily:"思源黑体Medium",fontWeight:"normal",fontSize:12,color:"#fff"},o="rgba(255,255,255,0.1)",t={formatLegend(o={}){const{show:t,layout:l={},tag:r={},textStyle:i={}}=o,{value:a={}}=l;let n="";return n=!1===o.showOrient?"horizontal":o.orient,{show:void 0===t||t,icon:r.style,itemWidth:r.size,itemHeight:r.size,itemGap:12,top:a.top,left:a.left,orient:n,textStyle:i||e}},formatTooltip(o={}){const{show:t,type:l,textStyle:r={},bgBox:i={},confine:a=!1}=o,{bgColor:n,padding:s,border:d={}}=i;let h={show:void 0===t||t,padding:s,backgroundColor:n,borderWidth:d.borderWidth,borderColor:d.borderColor,textStyle:r||e,confine:a};if(!0===o.showType){if(h.trigger=l,"axis"===l){const{crossStyle:e={}}=o,{color:t={}}=e,{center:l,start:r}=t;h.axisPointer={lineStyle:{width:e.width,type:e.lineType||"solid",color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:r||"rgba(0, 255, 233,0)"},{offset:.5,color:l||"rgba(255, 255, 255,1)"},{offset:1,color:r||"rgba(0, 255, 233,0)"}],global:!1}}}}}else h.trigger="item";return h},formatXAxis(t=[],l={}){var r,i;const{show:a,dataType:n,xAxisLabel:s={},xAxisname:d={},xAxisLine:h={},xSplitLine:c={}}=l;let f=t.map((e=>e.x));f=[...new Set(f)];let y={show:void 0===a||a,name:d.content,nameLocation:d.location||"end",nameGap:d.offset,nameTextStyle:d.text||e,axisLine:{show:!(!h||!h.show),lineStyle:{color:h&&h.color||o,width:h&&h.weight}},axisLabel:{show:!h||void 0===s.show||h&&s.show,rotate:s.rotate,interval:null!==(r=s.interval)&&void 0!==r?r:0,width:null!==(i=s.width)&&void 0!==i?i:70,overflow:s.overflow||"truncate",textStyle:s.text||e},axisTick:{show:!1},splitLine:{show:!(!c||!c.show),lineStyle:{color:c&&c.color||o,width:c&&c.width,type:c&&c.type||"solid"}}};return!1!==l.showDataType?(y.data=f,y.type=n||"category"):(y.type="value",delete y.data),y},formatYAxis(t=[],l={}){var r;const{show:i,dataType:a,yAxisLabel:n={},yAxisname:s={},yAxisLine:d={},ySplitLine:h={}}=l;let c=t.map((e=>e.x));c=[...new Set(c)];let f=_.min(_.map(t,"y"))-n.min,y={show:void 0===i||i,name:s.content,nameLocation:s.location||"end",nameTextStyle:s.text||e,nameGap:s.offset,splitLine:{show:!(!h||!h.show),lineStyle:{color:h&&h.color||o,type:h&&h.type||"solid",width:h&&h.width}},axisLine:{show:!(!h||!d.show),lineStyle:{color:h&&d.color||o,width:h&&d.weight}},axisLabel:{show:void 0===n.show||n.show,rotate:n.rotate,width:null!==(r=n.width)&&void 0!==r?r:100,overflow:n.overflow||"none",textStyle:n.text||e},axisTick:{show:!1}};return!1===l.showDataType?y.type="value":(y.type=a,y.data=c),!1!==l.showMin?y.min=f:delete y.min,y},formatGrid(e={}){let{top:o,bottom:t,left:l,right:r}=e;return{top:o,bottom:t,left:l,right:r,containLabel:!0}},formatGuide(e=[],o=[],t=[],l){let r=_.map(e,"y");const i=_.max(r),a=_.min(r),n=(i+a)/2;if(o.length)for(let r=0;r<o.length;r++){const{type:s,compute:d,fixed:h,lineStyle:c={},dataSource:f="",label:y={}}=o[r],{text:m={}}=y;let p=0;switch(s){case"compute":switch(d){case"max":p=i;break;case"min":p=a;break;case"average":p=n}break;case"fixed":p=Number(h);break;case"dataSource":p=Number(e[0][f])||0}t.push({type:"line",markLine:{symbol:"none",lineStyle:{normal:{type:c.type,width:parseFloat(c.width/10),color:c.color}},label:{position:y.position,fontSize:m.fontSize,fontFamily:m.fontFamily,fontWeight:m.fontWeight,color:m.color}}});let w=[];w="y"===l?[{yAxis:p}]:[{xAxis:p}],t[t.length-1].markLine.data=w}return t},formatDataZoom(e={}){var o,t,l,r,i,a;const{thumbnailLayout:n={},bottomAxis:s={},selectAxis:d={}}=e,{handIconStyle:h={},moveHandleStyle:c={},xAisInfo:f={},zoomPosition:y}=d,{horLayoutMargin:m={},verticalLayoutMargin:p={}}=n,{infoTextStyle:w={}}=f;let u=null,b=null,x=null,g=null;"vertical"===n.orient?(b=p.right||"90",u=p.top||"5%",x=p.left||"0%",g=p.bottom||"15%"):(u=m.top||"80",b=m.right||"3",x=m.left||"8",g=m.bottom||"4");const S={type:"slider",show:e.show,backgroundColor:s.backgroundColor||"rgba(47,69,84,0)",realTime:e.realTime||!1,dataBackground:{lineStyle:{color:s.lineColor||"blue",width:null!==(o=s.lineWidth)&&void 0!==o?o:.5,type:s.lineType||"solid"},areaStyle:{color:s.areaColor||"red"}},selectedDataBackground:{lineStyle:{color:d.selectLineColor||"#8fb0f7",width:d.selectLineWidth||.5,type:d.selectLineType||"solid"},areaStyle:{color:d.selectAreaColor||"#8fb0f7"}},fillerColor:d.fillerColor||"rgba(167,183,204,0.4)",borderColor:s.borderColor||"blue",handleIcon:h.handleIcon||"circle",handleSize:`${h.handleSize}%`||"100%",handleStyle:{color:h.handColor||"rgba(252, 249, 249, 1)",borderColor:h.borderColor||"rgba(249, 247, 247, 1)",borderWidth:null!==(t=h.borderWidth)&&void 0!==t?t:.5,borderType:h.borderType||"solid"},moveHandleIcon:c.moveHandleIcon||"circle",moveHandleSize:c.moveHandleSize||7,moveHandleStyle:{color:c.color||"rgba(249, 235, 235, 1)",borderColor:c.borderColor||"#000",borderWidth:c.borderWidth||0,borderType:c.borderType||"solid",shadowColor:c.shadowColor||'"#000"',shadowBlur:c.shadowBlur||0,shadowOffsetX:c.shadowOffsetX||0,shadowOffsetY:c.shadowOffsetY||0},showDetail:f.dragShow||!0,textStyle:{color:w.color||"red",fontStyle:w.fontStyle||"normal",fontWeight:w.fontWeight||"normal",fontFamily:w.fontFamily||"sans-serif"},orient:n.orient||"horizontal",zoomLock:!1,throttle:100,left:null!==(l=x+"%")&&void 0!==l?l:"10%",top:null!==(r=u+"%")&&void 0!==r?r:"90%",bottom:null!==(i=g+"%")&&void 0!==i?i:"10%",right:null!==(a=b+"%")&&void 0!==a?a:"10%",filterMode:"filter",showDataShadow:!0};if(e.show)if(y){const{start:e,end:o}=y;"percent"===e.type?S.start=e.value1:"absolute"===e.type?S.startValue=e.value2:S.startValue=e.value3,"percent"===o.type?S.end=o.value1:"absolute"===o.type?S.endValue=o.value2:S.endValue=o.value3}else S.start=d.start,S.end=d.end;else S.start=0,S.end=100;return[S]}};return t}));
