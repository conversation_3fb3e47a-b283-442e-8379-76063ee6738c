<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <link rel="preload" href="<%= BASE_URL %>video/loading.webp" as="image">
  <title>伏羲数据可视化平台</title>
  <script type="systemjs-importmap">
    {
      "imports": {
        "d3": "<%= BASE_URL %>chart-common-lib/d3.min.js",
        "echarts": "<%= BASE_URL %>chart-common-lib/echarts.min.js",
        "highcharts": "<%= BASE_URL %>chart-common-lib/highcharts.min.js",
        "lodash": "<%= BASE_URL %>chart-common-lib/lodash.min.js",
        "three": "<%= BASE_URL %>chart-common-lib/three.min.js",
        "hz-quark/dom": "<%= BASE_URL %>chart-common-lib/quark.dom.min.2.0.8.js",
        "hz-quark/chart": "<%= BASE_URL %>chart-common-lib/quark.chart.min.2.0.8.js",
        "hz-echarts5": "<%= BASE_URL %>chart-common-lib/echarts.min.5.3.2.js"
      }
    }
  </script>
 
  <style>
    .loading-wrap {
      position: absolute;
      z-index: 999999;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background: black;
      overflow: hidden;
      display: none;
    }
    .loading-container {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .loading-logo {
      width: 400px;
    }
  </style>
  <!-- <script src="https://cdn.jsdelivr.net/npm/systemjs/dist/extras/named-exports.js"></script> -->
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="prev"></div>
  <div id="loading-mask" class="loading-wrap">
    <div class="loading-container">
      <img class="loading-logo" src="<%= BASE_URL %>video/loading.webp" alt="">
    </div>
  </div>
  <!-- built files will be auto injected-->
  <script src="<%= BASE_URL %>lib/system.js?t=1"></script>
  <script type="text/javascript">
    const loadImg = document.querySelector('.loading-logo')
    loadImg.src = loadImg.src + '?_t=' + (+new Date)
    function emitUpdate() {
      var event = document.createEvent('Event')
      event.initEvent('sw.update', true, true)
      window.dispatchEvent(event)
    }

    if ('serviceWorker' in navigator) {
      console.log('支持sw')
      // 开始注册service workers
      if (location.protocol === 'http:') {
        //本地环境卸载service-worker
        navigator.serviceWorker.getRegistrations().then(function (registrations) {
          for (let registration of registrations) {
            registration.unregister()
          }
        })
      } else {
        navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        }).then(function (registration) {
          console.log('注册成功')
          if (registration.waiting) {
            emitUpdate()
            return
          }
          registration.onupdatefound = function () {
            var installingWorker = registration.installing
            installingWorker.onstatechange = function () {
              switch (installingWorker.state) {
                case 'installed':
                  if (navigator.serviceWorker.controller) {
                    emitUpdate()
                  }
                  break
              }
            }
          }
        }).catch(function (error) {
          console.log('注册没有成功', error)
        })
      }
    } else {
      console.log('不支持')
    }
  </script>
  <script>
    const href = location.href;
    if (href.indexOf('https://fuxi.haizhi.com') > -1) {
      var _hmt = _hmt || [];
      (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?d038e0cfca6e690e06bbd8264e02b114";
        var s = document.getElementsByTagName("script")[0]; 
        s.parentNode.insertBefore(hm, s);
      })();
    }
  </script>
</body>

</html>