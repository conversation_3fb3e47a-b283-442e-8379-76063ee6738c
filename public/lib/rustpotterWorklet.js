!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var r=t();for(var s in r)("object"==typeof exports?exports:e)[s]=r[s]}}("undefined"!=typeof self?self:this,(()=>(()=>{var e={344:function(e,t,r){!function(e){"use strict";function t(){}function r(){}t.prototype.encode=function(e){for(var t=[],r=e.length,s=0;s<r;){var o=e.codePointAt(s),n=0,i=0;for(o<=127?(n=0,i=0):o<=2047?(n=6,i=192):o<=65535?(n=12,i=224):o<=2097151&&(n=18,i=240),t.push(i|o>>n),n-=6;n>=0;)t.push(128|o>>n&63),n-=6;s+=o>=65536?2:1}return t},globalThis.TextEncoder=t,e.TextEncoder||(e.TextEncoder=t),r.prototype.decode=function(e){if(!e)return"";for(var t="",r=0;r<e.length;){var s=e[r],o=0,n=0;if(s<=127?(o=0,n=255&s):s<=223?(o=1,n=31&s):s<=239?(o=2,n=15&s):s<=244&&(o=3,n=7&s),e.length-r-o>0)for(var i=0;i<o;)n=n<<6|63&(s=e[r+i+1]),i+=1;else n=65533,o=e.length-r;t+=String.fromCodePoint(n),r+=o+1}return t},globalThis.TextDecoder=r,e.TextDecoder||(e.TextDecoder=r)}(typeof globalThis==""+void 0?typeof r.g==""+void 0?typeof self==""+void 0?this:self:r.g:globalThis)},721:(e,t,r)=>{"use strict";e.exports=r.p+"rustpotter_wasm_bg.wasm"}},t={};function r(s){var o=t[s];if(void 0!==o)return o.exports;var n=t[s]={exports:{}};return e[s].call(n.exports,n,n.exports,r),n.exports}r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.p="",r.b=void 0;var s={};return(()=>{"use strict";let e;r.r(s),r(344);const t=new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0});t.decode();let o=new Uint8Array;function n(){return 0===o.byteLength&&(o=new Uint8Array(e.memory.buffer)),o}function i(e,r){return t.decode(n().subarray(e,e+r))}const a=new Array(32).fill(void 0);a.push(void 0,null,!0,!1);let c=a.length,p=0;function d(e,t){const r=t(1*e.length);return n().set(e,r/1),p=e.length,r}let l=new Int32Array;function h(){return 0===l.byteLength&&(l=new Int32Array(e.memory.buffer)),l}let u=new Uint32Array;let f=new Uint16Array;let _=new Float32Array;Object.freeze({easiest:0,0:"easiest",easy:1,1:"easy",normal:2,2:"normal",hard:3,3:"hard",hardest:4,4:"hardest"});const w=Object.freeze({int:0,0:"int",float:1,1:"float"});class g{static __wrap(e){const t=Object.create(g.prototype);return t.ptr=e,t}__destroy_into_raw(){const e=this.ptr;return this.ptr=0,e}free(){const t=this.__destroy_into_raw();e.__wbg_rustpotterdetection_free(t)}getName(){try{const s=e.__wbindgen_add_to_stack_pointer(-16);e.rustpotterdetection_getName(s,this.ptr);var t=h()[s/4+0],r=h()[s/4+1];return i(t,r)}finally{e.__wbindgen_add_to_stack_pointer(16),e.__wbindgen_free(t,r)}}getScore(){return e.rustpotterdetection_getScore(this.ptr)}}class y{static __wrap(e){const t=Object.create(y.prototype);return t.ptr=e,t}__destroy_into_raw(){const e=this.ptr;return this.ptr=0,e}free(){const t=this.__destroy_into_raw();e.__wbg_rustpotterjs_free(t)}addWakewordModelBytes(t){try{const f=e.__wbindgen_add_to_stack_pointer(-16),_=d(t,e.__wbindgen_malloc),w=p;e.rustpotterjs_addWakewordModelBytes(f,this.ptr,_,w);var r=h()[f/4+0],s=h()[f/4+1],o=h()[f/4+2],n=h()[f/4+3],l=r,u=s;if(n)throw l=0,u=0,function(e){const t=function(e){return a[e]}(e);return function(e){e<36||(a[e]=c,c=e)}(e),t}(o);return i(l,u)}finally{e.__wbindgen_add_to_stack_pointer(16),e.__wbindgen_free(l,u)}}processInt32(t){const r=function(t,r){const s=r(4*t.length);return(0===u.byteLength&&(u=new Uint32Array(e.memory.buffer)),u).set(t,s/4),p=t.length,s}(t,e.__wbindgen_malloc),s=p,o=e.rustpotterjs_processInt32(this.ptr,r,s);return 0===o?void 0:g.__wrap(o)}processInt16(t){const r=function(t,r){const s=r(2*t.length);return(0===f.byteLength&&(f=new Uint16Array(e.memory.buffer)),f).set(t,s/2),p=t.length,s}(t,e.__wbindgen_malloc),s=p,o=e.rustpotterjs_processInt16(this.ptr,r,s);return 0===o?void 0:g.__wrap(o)}processInt8(t){const r=d(t,e.__wbindgen_malloc),s=p,o=e.rustpotterjs_processInt8(this.ptr,r,s);return 0===o?void 0:g.__wrap(o)}processFloat32(t){const r=function(t,r){const s=r(4*t.length);return(0===_.byteLength&&(_=new Float32Array(e.memory.buffer)),_).set(t,s/4),p=t.length,s}(t,e.__wbindgen_malloc),s=p,o=e.rustpotterjs_processFloat32(this.ptr,r,s);return 0===o?void 0:g.__wrap(o)}processBuffer(t){const r=d(t,e.__wbindgen_malloc),s=p,o=e.rustpotterjs_processBuffer(this.ptr,r,s);return 0===o?void 0:g.__wrap(o)}getFrameSize(){return e.rustpotterjs_getFrameSize(this.ptr)>>>0}getByteFrameSize(){return e.rustpotterjs_getByteFrameSize(this.ptr)>>>0}}class m{static __wrap(e){const t=Object.create(m.prototype);return t.ptr=e,t}__destroy_into_raw(){const e=this.ptr;return this.ptr=0,e}free(){const t=this.__destroy_into_raw();e.__wbg_rustpotterjsbuilder_free(t)}static new(){const t=e.rustpotterjsbuilder_new();return m.__wrap(t)}setThreshold(t){e.rustpotterjsbuilder_setThreshold(this.ptr,t)}setAveragedThreshold(t){e.rustpotterjsbuilder_setAveragedThreshold(this.ptr,t)}setBitsPerSample(t){e.rustpotterjsbuilder_setBitsPerSample(this.ptr,t)}setSampleRate(t){e.rustpotterjsbuilder_setSampleRate(this.ptr,t)}setSampleFormat(t){e.rustpotterjsbuilder_setSampleFormat(this.ptr,t)}setChannels(t){e.rustpotterjsbuilder_setChannels(this.ptr,t)}setComparatorBandSize(t){e.rustpotterjsbuilder_setComparatorBandSize(this.ptr,t)}setComparatorRef(t){e.rustpotterjsbuilder_setComparatorRef(this.ptr,t)}setEagerMode(t){e.rustpotterjsbuilder_setEagerMode(this.ptr,t)}setSingleThread(t){e.rustpotterjsbuilder_setSingleThread(this.ptr,t)}setNoiseSensitivity(t){e.rustpotterjsbuilder_setNoiseSensitivity(this.ptr,t)}setNoiseMode(t){e.rustpotterjsbuilder_setNoiseMode(this.ptr,t)}build(){const t=e.rustpotterjsbuilder_build(this.ptr);return y.__wrap(t)}}async function b(t){void 0===t&&(t=new URL(r(721),r.b));const s=function(){const e={wbg:{}};return e.wbg.__wbindgen_string_new=function(e,t){return function(e){c===a.length&&a.push(a.length+1);const t=c;return c=a[t],a[t]=e,t}(i(e,t))},e.wbg.__wbindgen_throw=function(e,t){throw new Error(i(e,t))},e}();("string"==typeof t||"function"==typeof Request&&t instanceof Request||"function"==typeof URL&&t instanceof URL)&&(t=fetch(t));const{instance:n,module:p}=await async function(e,t){if("function"==typeof Response&&e instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(e,t)}catch(t){if("application/wasm"==e.headers.get("Content-Type"))throw t;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",t)}const r=await e.arrayBuffer();return await WebAssembly.instantiate(r,t)}{const r=await WebAssembly.instantiate(e,t);return r instanceof WebAssembly.Instance?{instance:r,module:e}:r}}(await t,s);return function(t,r){return e=t.exports,b.__wbindgen_wasm_module=r,_=new Float32Array,l=new Int32Array,f=new Uint16Array,u=new Uint32Array,o=new Uint8Array,e}(n,p)}const S=b;class v{constructor(e,t,r){if(this.config=t,this.onSpot=r,!this.config.sampleRate)throw new Error("sampleRate value is required to record. NOTE: Audio is not resampled!");this.samplesOffset=0,this.wasmLoadedPromise=(()=>{return t=this,r=void 0,o=function*(){yield S(WebAssembly.compile(e));const t=m.new();t.setSampleRate(this.config.sampleRate),t.setSampleFormat(w.float),t.setBitsPerSample(32),t.setChannels(1),t.setAveragedThreshold(this.config.averagedThreshold),t.setThreshold(this.config.threshold),t.setComparatorRef(this.config.comparatorRef),t.setComparatorBandSize(this.config.comparatorBandSize),t.setEagerMode(this.config.eagerMode),null!=this.config.noiseMode&&(t.setNoiseMode(this.config.noiseMode),t.setNoiseSensitivity(this.config.noiseSensitivity)),this.rustpotterJS=t.build(),this.rustpotterFrameSize=this.rustpotterJS.getFrameSize(),this.samples=new Float32Array(this.rustpotterFrameSize),t.free()},new((s=void 0)||(s=Promise))((function(e,n){function i(e){try{c(o.next(e))}catch(e){n(e)}}function a(e){try{c(o.throw(e))}catch(e){n(e)}}function c(t){var r;t.done?e(t.value):(r=t.value,r instanceof s?r:new s((function(e){e(r)}))).then(i,a)}c((o=o.apply(t,r||[])).next())}));var t,r,s,o})()}waitReady(){return this.wasmLoadedPromise}addWakeword(e){this.rustpotterJS.addWakewordModelBytes(e)}process(e){const t=e[0],r=this.samplesOffset+t.length;if(r<=this.rustpotterFrameSize)this.samples.set(t,this.samplesOffset),r==this.rustpotterFrameSize-1?(this.handleDetection(this.rustpotterJS.processFloat32(this.samples)),this.samplesOffset=0):this.samplesOffset=r;else{var s=this.rustpotterFrameSize-this.samplesOffset;this.samples.set(t.subarray(0,s),this.samplesOffset),this.handleDetection(this.rustpotterJS.processFloat32(this.samples));var o=t.subarray(s);o.length>=t.length?(this.samplesOffset=0,this.process([o])):(this.samples.set(o,0),this.samplesOffset=t.length-s)}}handleDetection(e){e&&(this.onSpot(e.getName(),e.getScore()),e.free())}close(){this.rustpotterJS.free()}}if("function"==typeof registerProcessor){class e extends AudioWorkletProcessor{constructor(){super(),this.continueProcess=!0,this.port.onmessage=({data:e})=>{switch(e.command){case"close":this.continueProcess=!1;break;case"done":this.continueProcess=!1,this.recorder&&(this.recorder.close(),this.recorder=null),this.port.postMessage({type:"done"});break;case"init":this.recorder=new v(e.wasmBytes,{sampleRate:e.sampleRate,threshold:e.threshold,averagedThreshold:e.averagedThreshold,comparatorRef:e.comparatorRef,comparatorBandSize:e.comparatorBandSize,eagerMode:e.eagerMode,noiseMode:e.noiseMode,noiseSensitivity:e.noiseSensitivity},((e,t)=>{this.port.postMessage({type:"detection",name:e,score:t})})),this.recorder.waitReady().then((()=>{this.port.postMessage({type:"rustpotter-ready"})})).catch((e=>{console.error(e),this.port.postMessage({type:"rustpotter-error"})}));break;case"wakeword":const t=new Uint8Array(e.wakewordBytes);this.recorder||this.port.postMessage({type:"wakeword-error"});try{this.recorder.addWakeword(t),this.port.postMessage({type:"wakeword-loaded"})}catch(e){console.error(e),this.port.postMessage({type:"wakeword-error"})}break;default:console.error("Unknown command")}}}process(e){return this.recorder&&e[0]&&e[0].length&&e[0][0]&&e[0][0].length&&this.recorder.process(e[0]),this.continueProcess}}registerProcessor("rustpotter-worklet",e)}else{let e;onmessage=({data:t})=>{switch(t.command){case"process":e&&e.process(t.buffers);break;case"done":if(e){const t=e;e=null,t.close(),postMessage({type:"done"})}break;case"close":close();break;case"init":e=new v(t.wasmBytes,{sampleRate:t.sampleRate,threshold:t.threshold,averagedThreshold:t.averagedThreshold,comparatorRef:t.comparatorRef,comparatorBandSize:t.comparatorBandSize,eagerMode:t.eagerMode,noiseMode:t.noiseMode,noiseSensitivity:t.noiseSensitivity},((e,t)=>{postMessage({type:"detection",name:e,score:t})})),e.waitReady().then((()=>{postMessage({type:"rustpotter-ready"})})).catch((e=>{console.error(e),postMessage({type:"rustpotter-error"})}));break;case"wakeword":const r=new Uint8Array(t.wakewordBytes);e||postMessage({type:"wakeword-error"});try{e.addWakeword(r),postMessage({type:"wakeword-loaded"})}catch(e){console.error(e),postMessage({type:"wakeword-error"})}}}}})(),s})()));