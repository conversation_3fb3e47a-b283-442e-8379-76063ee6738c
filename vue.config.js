const webpack = require('webpack');
const CompressionPlugin = require('compression-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const args = require('minimist')(process.argv);
const dir = args.dir || 'www';
const path = require('path');
const publicPath = process.env.SUB_PATH || '/'
const outputDir = path.resolve(__dirname, `./${dir}/${publicPath}`)
// const BUILD_DEST = process.env.npm_lifecycle_event === 'serve' ? outputDir : process.env.npm_lifecycle_event;

function resolve (dir) {
  return path.join(__dirname, dir);
}
module.exports = {
  lintOnSave: process.env.NODE_ENV !== 'production',
  publicPath,
  outputDir,
  productionSourceMap: false,
  configureWebpack: {
    module: {
      rules: [
        {
          parser: {
            system: false
          }
        },
        {
          test: /\.(wasm)?$/,
          use: 'url-loader'
        }
      ]
    },
    plugins: [
      new CompressionPlugin({
        algorithm: 'gzip', // 使用gzip压缩
        test: /\.js$|\.html$|\.css$/, // 匹配文件名
        filename: '[path].gz[query]', // 压缩后的文件名(保持原文件名，后缀加.gz)
        minRatio: 1, // 压缩率小于1才会压缩
        threshold: 10240, // 对超过10k的数据压缩
        deleteOriginalAssets: false // 是否删除未压缩的源文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false（比如删除打包后的gz后还可以加载到原始资源文件）
      }),
      new CopyWebpackPlugin([
        {
          from: path.resolve(__dirname, './sw.js'),
          to: path.resolve(__dirname, outputDir),
          ignore: ['.*']
        }
      ])
    ]
  },
  pages: {
    index: {
      entry: ['./src/entry/edit/main.js'],
      template: './public/index.html',
      chunks: 'all',
      excludeChunks: ['preview'],
      filename: process.env.NODE_ENV !== 'production' ? 'index.html' : path.resolve(__dirname, `./${dir}/index.html`)
    },
    preview: {
      entry: ['./src/entry/preview/main.js'],
      template: './public/preview/index.html',
      chunks: 'all',
      excludeChunks: process.env.NODE_ENV === 'test' ? ['index'] : ['index', 'chunk-v-region', 'chunk-tinymce', 'chunk-pdfjs'],
      filename: 'preview/index.html'
    }
  },
  chainWebpack: config => {
    // 移除 prefetch 插件
    config.plugins.delete('prefetch-index')
    config.plugins.delete('preload-index')
    config.plugins.delete('prefetch-preview')
    config.plugins.delete('preload-preview')
    config
      .plugin('provide')
      .use(webpack.ProvidePlugin, [{
        _: 'lodash'
      }])
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/img/icon-svg'))
      .end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/img/icon-svg'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end();
    config.optimization
      .splitChunks({
        chunks: 'all',
        cacheGroups: {
          vendors: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            enforce: true, // 生产环境拆包不彻底，先用enforce强制拆包
            chunks: 'initial'
          },
          vue: {
            name: 'vue',
            test: /[\\/]node_modules[\\/]vue[\\/]/,
            enforce: true,
            priority: 20
          },
          vuex: {
            name: 'vuex',
            test: /[\\/]node_modules[\\/]vuex[\\/]/,
            priority: 20,
            enforce: true
          },
          'vue-router': {
            name: 'vue-router',
            test: /[\\/]node_modules[\\/]vue-router[\\/]/,
            enforce: true,
            priority: 20
          },
          elementUI: {
            name: 'chunk-elementUI', // 把elementUI拆分到一个单独的文件里面
            priority: 20, // 这个的优先级高于lib
            enforce: true,
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
          },
          echarts: {
            name: 'chunk-echarts',
            priority: 20, // 这个的优先级高于lib
            enforce: true,
            test: /[\\/]node_modules[\\/]_?echarts(.*)/
          },
          'v-region': {
            name: 'chunk-v-region',
            priority: 20, // 这个的优先级高于lib
            enforce: true,
            test: /[\\/]node_modules[\\/]_?v-region(.*)/
          },
          lodash: {
            name: 'chunk-lodash',
            priority: 20, // 这个的优先级高于lib
            enforce: true,
            test: /[\\/]node_modules[\\/]_?lodash(.*)/
          },
          tinymce: {
            name: 'chunk-tinymce',
            priority: 20, // 这个的优先级高于lib
            enforce: true,
            test: /[\\/]node_modules[\\/]tinymce[\\/]/
          },
          pdfjs: {
            name: 'chunk-pdfjs',
            priority: 20, // 这个的优先级高于lib
            enforce: true,
            test: /[\\/]node_modules[\\/]pdfjs-dist[\\/]/
          },
          commons: {
            name: 'chunk-commons',
            // test: resolve('src/components'), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 0,
            reuseExistingChunk: true
          }
        }
      })
  },
  devServer: {
    host: '0.0.0.0',
    port: '9999',
    open: true,
    proxy: {
      '/public': {
        target: process.env.VUE_APP_SERVER_URL, // 静态资源
        secure: false,
        changeOrigin: true,
        pathRewrite: {
          '^/public': '/public'
        }
      },
      '/tfs': {
        target: process.env.VUE_APP_IMG_SERVER_URL, // 静态资源
        secure: false,
        changeOrigin: true,
        pathRewrite: {
          '^/tfs': '/tfs'
        }
      },
      '/api': {
        target: process.env.VUE_APP_SERVER_URL, // 静态资源
        secure: false,
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      },
      '/monitor': {
        target: process.env.VUE_APP_SERVER_URL, // 静态资源
        secure: false,
        changeOrigin: true
      }
    }
  }
}
