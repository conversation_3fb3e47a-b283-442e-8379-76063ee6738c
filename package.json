{"name": "seatom", "version": "4.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development", "preview": "vue-cli-service serve --mode preview", "test": "vue-cli-service serve --mode test", "build": "vue-cli-service build --mode development", "build-dev": "npm run git-commit && vue-cli-service build --mode development --dest build-dev", "build-prev": "npm run git-commit && vue-cli-service build --mode preview --dir build-prev", "build-prod": "npm run git-commit && vue-cli-service build --mode production --dir build-prod", "build-test": "npm run git-commit && vue-cli-service build --mode test --dir build-test", "build-bid": "npm run git-commit && vue-cli-service build --mode bid --dest build-bid", "build-pre": "sh ./config/build-prod.sh", "lint": "vue-cli-service lint", "lint-fix": "eslint ./src --fix --ext .js,.vue", "webpack": "vue-cli-service inspect", "svgo": "svgo -f src/assets/img/icon-svg --config=src/assets/img/svgo.yml", "svgo-lossless": "svgo -f src/assets/img/icon-svg-lossless --config=src/assets/img/svgo-lossless.yml", "git-commit": "node git.commit.js"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^1.0.2", "@emmetio/codemirror-plugin": "^1.2.4", "@haizhi/monitor": "^1.0.1", "@picovoice/porcupine-web": "^2.1.15", "@picovoice/web-voice-processor": "^4.0.5", "@riophae/vue-treeselect": "^0.4.0", "@tensorflow-models/speech-commands": "^0.5.4", "@tensorflow/tfjs": "3.3.0", "@tensorflow/tfjs-core": "3.3.0", "@tensorflow/tfjs-data": "3.3.0", "@tensorflow/tfjs-layers": "3.3.0", "@tinymce/tinymce-vue": "^3.2.8", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "ace": "^1.3.0", "animate.css": "^4.1.1", "axios": "^0.21.0", "babel-polyfill": "^6.26.0", "brace": "^0.11.1", "bumblebee-hotword": "^0.2.1", "codemirror": "^5.39.2", "core-js": "^3.6.5", "cropperjs": "^1.5.12", "crypto-js": "^4.1.1", "csslint": "^1.0.5", "echarts": "^4.8.0", "element-ui": "^2.15.3", "fastclick": "^1.0.6", "fingerprintjs2": "^2.1.4", "html2canvas": "^1.0.0-rc.7", "htmlhint": "^0.14.2", "hz-message": "^1.2.0", "hz-product-list": "^1.1.10", "hz-quark": "^2.0.6", "hz-user": "^1.1.7", "js-audio-recorder": "^1.0.7", "js-base64": "^3.6.0", "jshint": "^2.13.4", "jsoneditor": "^9.4.0", "jsonlint": "^1.6.3", "keymaster": "^1.6.2", "lodash": "^4.17.20", "mitt": "^2.1.0", "mobile-drag-drop": "^2.2.0", "particles.vue": "^2.16.3", "pinyin-match": "^1.2.2", "qrcode": "^1.4.4", "qs": "^6.9.4", "rustpotter-worklet": "^1.0.4", "seedrandom": "^3.0.5", "socket.io-client": "^2.1.1", "swiper": "5.x", "tinymce": "^5.8.1", "uuid": "^8.3.1", "v-contextmenu": "^2.9.0", "v-region": "2.2.2", "vant": "^2.12.48", "vue": "2.6.11", "vue-awesome-swiper": "^4.1.1", "vue-clipboard2": "^0.3.3", "vue-codemirror": "^4.0.6", "vue-grid-layout": "^2.3.12", "vue-lazyload": "^1.3.3", "vue-pdf": "^4.3.0", "vue-ref": "^2.0.0", "vue-router": "^3.2.0", "vue-socket.io": "^3.0.10", "vue-virtual-scroll-list": "^2.3.4", "vuedraggable": "^2.24.3", "vuex": "^3.4.0"}, "devDependencies": {"@babel/generator": "^7.22.10", "@babel/parser": "^7.22.10", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/preset-env": "^7.18.9", "@babel/traverse": "^7.22.10", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.3", "babel-plugin-component": "^1.1.1", "babel-preset-es2015": "^6.24.1", "compression-webpack-plugin": "^5.0.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "file-loader": "^6.2.0", "lint-staged": "^9.5.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "script-loader": "^0.7.2", "svg-sprite-loader": "^5.2.1", "url-loader": "^4.1.1", "vue-template-compiler": "^2.6.11"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}}