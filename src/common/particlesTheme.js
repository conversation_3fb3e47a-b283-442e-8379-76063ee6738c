export const STYLE_NASA = {
  autoPlay: true,
  detectRetina: true,
  duration: 0,
  fpsLimit: 60,
  fullScreen: { enable: true, zIndex: 1 },
  particles: {
    reduceDuplicates: false,
    number: { value: 500 },
    color: { value: ['#ffffff'] },
    shape: { options: {}, type: 'circle' },
    zIndex: {
      random: { enable: false, minimumValue: 0 },
      value: 0,
      opacityRate: 1,
      sizeRate: 1,
      velocityRate: 1
    },
    move: {
      enable: true,
      random: true,
      speed: { min: 1, max: 1 },
      angle: { offset: 0, value: 180 },
      outModes: { default: 'out', bottom: 'out', left: 'out', right: 'out', top: 'out' }
    },
    opacity: {
      random: { enable: true, minimumValue: 0.1 },
      value: { min: 0, max: 1 },
      animation: {
        count: 0,
        enable: true,
        speed: 1,
        sync: false,
        destroy: 'none',
        minimumValue: 0,
        startValue: 'random'
      }
    },
    size: {
      random: { enable: true, minimumValue: 1 },
      value: { min: 1, max: 3 }
    }
  }
}

export const STYLE_AMONG_US = {
  autoPlay: true,
  detectRetina: true,
  duration: 0,
  fpsLimit: 60,
  fullScreen: { enable: true, zIndex: 1 },
  particles: {
    reduceDuplicates: false,
    number: { value: 200 },
    color: { value: ['#ffffff'] },
    shape: { options: {}, type: 'circle' },
    groups: {
      z5000: {
        number: { value: 70 },
        zIndex: { value: 5000 }
      },
      z7500: {
        number: { value: 30 },
        zIndex: { value: 7500 }
      },
      z2500: {
        number: { value: 50 },
        zIndex: { value: 2500 }
      },
      z1000: {
        number: { value: 40 },
        zIndex: { value: 1000 }
      }
    },
    zIndex: {
      random: { enable: false, minimumValue: 0 },
      value: 500,
      opacityRate: 0.5,
      sizeRate: 1,
      velocityRate: 1
    },
    move: {
      enable: true,
      random: true,
      drift: 0,
      decay: 0,
      speed: { min: 5, max: 5 },
      direction: 'right',
      angle: { offset: 0, value: 10 },
      outModes: { default: 'out', bottom: 'out', left: 'out', right: 'out', top: 'out' }
    },
    opacity: {
      random: { enable: true, minimumValue: 0.1 },
      value: { min: 0, max: 1 },
      animation: {
        count: 0,
        enable: true,
        speed: 1,
        sync: false,
        destroy: 'none',
        minimumValue: 0,
        startValue: 'random'
      }
    },
    size: {
      random: { enable: true, minimumValue: 1 },
      value: { min: 1, max: 3 }
    }
  }
}

export const STYLE_SNOW = {
  autoPlay: true,
  detectRetina: true,
  duration: 0,
  fpsLimit: 60,
  fullScreen: { enable: true, zIndex: 1 },
  particles: {
    reduceDuplicates: false,
    number: { value: 300 },
    color: { value: ['#ffffff'] },
    shape: { options: {}, type: 'circle' },
    zIndex: {
      random: { enable: false, minimumValue: 0 },
      value: 0,
      opacityRate: 1,
      sizeRate: 1,
      velocityRate: 1
    },
    move: {
      enable: true,
      random: true,
      decay: 0,
      drift: 0,
      speed: { min: 2, max: 2 },
      direction: 'bottom',
      angle: { offset: 0, value: 90 },
      outModes: { default: 'out', bottom: 'out', left: 'out', right: 'out', top: 'out' }
    },
    opacity: {
      value: { min: 0.1, max: 0.5 },
      random: { enable: true, minimumValue: 0.1 }
    },
    size: {
      random: { enable: true, minimumValue: 1 },
      value: { min: 1, max: 10 }
    }
  }
}

// not number
export const STYLE_GROWING = {
  autoPlay: true,
  detectRetina: true,
  fullScreen: { enable: true, zIndex: 1 },
  duration: 0,
  fpsLimit: 60,
  emitters: {
    autoPlay: true,
    rate: { quantity: 2, delay: 0.1 },
    size: { mode: 'percent', height: 0, width: 100 },
    direction: 'top',
    position: { x: 50, y: 100 }
  },
  particles: {
    reduceDuplicates: false,
    number: { value: 0 },
    color: { value: ['#ffffff'] },
    shape: { options: {}, type: 'circle' },
    zIndex: {
      random: { enable: false, minimumValue: 0 },
      value: 0,
      opacityRate: 1,
      sizeRate: 1,
      velocityRate: 1
    },
    move: {
      enable: true,
      random: false,
      drift: 0,
      decay: 0,
      speed: { min: 5, max: 5 },
      direction: 'none',
      angle: { offset: 0, value: 90 },
      outModes: { default: 'destroy', bottom: 'destroy', left: 'destroy', right: 'destroy', top: 'destroy' }
    },
    opacity: {
      random: { enable: true, minimumValue: 0.1 },
      value: { min: 0.1, max: 0.5 }
    },
    size: {
      random: { enable: true, minimumValue: 1 },
      value: { min: 0.1, max: 20 },
      animation: {
        count: 0,
        enable: true,
        speed: 5,
        sync: true,
        destroy: 'max',
        minimumValue: 0.1,
        startValue: 'min'
      }
    }
  },
  pauseOnBlur: false,
  pauseOnOutsideViewport: false
}

export const STYLE_TWINKLE = {
  autoPlay: true,
  detectRetina: true,
  duration: 0,
  fpsLimit: 60,
  fullScreen: { enable: true, zIndex: 1 },
  particles: {
    reduceDuplicates: false,
    number: { value: 80 },
    color: { value: ['#ffffff'] },
    shape: { options: {}, type: 'circle' },
    zIndex: {
      random: { enable: false, minimumValue: 0 },
      value: 0,
      opacityRate: 1,
      sizeRate: 1,
      velocityRate: 1
    },
    move: {
      enable: true,
      decay: 0,
      drift: 0,
      speed: { min: 2, max: 2 },
      direction: 'none',
      angle: { offset: 0, value: 90 },
      outModes: { default: 'out', bottom: 'out', left: 'out', right: 'out', top: 'out' }
    },
    opacity: {
      random: { enable: true, minimumValue: 0.1 },
      value: { min: 0.1, max: 0.5 },
      animation: {
        count: 0,
        enable: true,
        speed: 3,
        sync: false,
        destroy: 'none',
        minimumValue: 0.1,
        startValue: 'random'
      }
    },
    size: {
      random: { enable: true, minimumValue: 1 },
      value: { min: 0.1, max: 5 },
      animation: {
        count: 0,
        enable: true,
        speed: 20,
        sync: false,
        destroy: 'none',
        minimumValue: 0.1,
        startValue: 'random'
      }
    }
  }
}

export const STYLE_NINJA = {
  autoPlay: true,
  detectRetina: true,
  duration: 0,
  fpsLimit: 60,
  fullScreen: { enable: true, zIndex: 1 },
  zIndex: {
    random: { enable: false, minimumValue: 0 },
    value: 0,
    opacityRate: 1,
    sizeRate: 1,
    velocityRate: 1
  },
  particles: {
    reduceDuplicates: false,
    number: { value: 100 },
    color: { value: ['#ffffff'] },
    shape: { options: { star: { sides: 5 } }, type: 'star' },
    move: {
      enable: true,
      straight: true,
      decay: 0,
      drift: 0,
      speed: { min: 6, max: 6 },
      angle: { offset: 0, value: 90 },
      direction: 'top-right',
      outModes: { default: 'out', bottom: 'out', left: 'out', right: 'out', top: 'out' }
    },
    opacity: {
      value: { min: 0.5, max: 1 },
      random: { enable: true, minimumValue: 0.1 }
    },
    size: {
      random: { enable: true, minimumValue: 1 },
      value: { min: 1, max: 4 },
      animation: {
        count: 0,
        enable: false,
        speed: 40,
        sync: false,
        destroy: 'none',
        minimumValue: 0.1,
        startValue: 'random'
      }
    }
  }
}

// not number
export const STYLE_METEOR = {
  autoPlay: true,
  detectRetina: true,
  duration: 0,
  fpsLimit: 60,
  fullScreen: { enable: true, zIndex: 1 },
  emitters: {
    direction: 'bottom',
    life: { count: 0, duration: 0.01, delay: 0.01 },
    rate: { delay: 0.15, quantity: 0.1 }
  },
  particles: {
    reduceDuplicates: false,
    number: { value: 0 },
    shape: { type: 'line' },
    stroke: {
      color: { value: ['#ffffff'] },
      width: 1
    },
    rotate: {
      path: true,
      direction: 'clockwise'
    },
    move: {
      enable: true,
      random: true,
      speed: { min: 10, max: 30 },
      outModes: { default: 'out', left: 'out', right: 'out', top: 'out', bottom: 'out' }
    },
    opacity: {
      value: { min: 0.9, max: 1 },
      random: { enable: true, minimumValue: 0.1 }
    },
    size: {
      value: { min: 50, max: 100 },
      random: { enable: true, minimumValue: 50 },
      animation: {
        enable: true,
        sync: true,
        speed: 150,
        startValue: 'max',
        destroy: 'min'
      }
    }
  }
}
