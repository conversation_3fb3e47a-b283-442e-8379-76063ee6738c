/**
 * 组件列表左侧列表
 */
export const compPanelMenu = [{
  type: 'regular',
  title: '常规图表',
  icon: 'regular',
  name: '常规'
}, {
  type: 'layout',
  title: '布局组件',
  icon: 'layout',
  name: '布局'
}, {
  type: 'data',
  title: '数据展示',
  icon: 'data',
  name: '数据'
}, {
  type: 'map',
  title: '地图组件',
  icon: 'map',
  name: '地图'
}, {
  type: 'media',
  title: '媒体组件',
  icon: 'media',
  name: '媒体'
}, {
  type: 'interaction',
  title: '交互组件',
  icon: 'interaction',
  name: '交互'
}, {
  type: 'relation',
  title: '关系组件',
  icon: 'relation',
  name: '关系'
}, {
  type: 'decoration',
  title: '装饰组件',
  icon: 'decorationk',
  name: '装饰'
}, {
  type: 'custom',
  title: '自定义组件',
  icon: 'custom',
  name: '自定义'
}, {
  type: 'other',
  title: '其他',
  icon: 'custom',
  name: '其他'
}]

/**
 * 指标库组件列表左侧列表
 */
export const indicatorCompPanelMenu = [{
  type: 'regular',
  title: '常规图表',
  icon: 'regular',
  name: '常规'
}, {
  type: 'layout',
  title: '布局组件',
  icon: 'layout',
  name: '布局'
}, {
  type: 'data',
  title: '数据展示',
  icon: 'data',
  name: '数据'
}, {
  type: 'media',
  title: '媒体组件',
  icon: 'media',
  name: '媒体'
}, {
  type: 'interaction',
  title: '交互组件',
  icon: 'interaction',
  name: '交互'
}, {
  type: 'relation',
  title: '关系组件',
  icon: 'relation',
  name: '关系'
}, {
  type: 'decoration',
  title: '装饰组件',
  icon: 'decorationk',
  name: '装饰'
}, {
  type: 'custom',
  title: '自定义组件',
  icon: 'custom',
  name: '自定义'
}, {
  type: 'other',
  title: '其他',
  icon: 'custom',
  name: '其他'
}]

/**
 * 组件列表数据
 */
export const compPanelList = {
  regular: [{
    title: '全部',
    type: 'regular-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '柱形图',
    type: 'regular-bar',
    icon: 'bar',
    child: [],
    open: false
  }, {
    title: '折线图',
    type: 'regular-line',
    icon: 'line',
    child: [],
    open: false
  }, {
    title: '饼图',
    type: 'regular-pie',
    icon: 'pie',
    child: [],
    open: false
  }, {
    title: '散点图',
    type: 'regular-scatter',
    icon: 'scatter',
    child: [],
    open: false
  }, {
    title: '多类型图',
    type: 'regular-mult',
    icon: 'mult',
    child: [],
    open: false
  }, {
    title: '其他',
    type: 'regular-other',
    icon: 'other',
    child: [],
    open: false
  }],
  media: [{
    title: '全部',
    type: 'media-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '图片',
    type: 'media-image',
    icon: 'image',
    child: [],
    open: false
  }, {
    title: '视频',
    type: 'media-video',
    icon: 'media-video',
    child: [],
    open: false
  }, {
    title: '模型',
    type: 'media-model',
    icon: 'media-model',
    child: [],
    open: false
  }],
  relation: [{
    title: '全部',
    type: 'relation-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '流程图',
    type: 'relation-flow',
    icon: 'relation-flow',
    child: [],
    open: false
  }, {
    title: '关系图',
    type: 'relation-graph',
    icon: 'relation-graph',
    child: [],
    open: false
  }, {
    title: '树图',
    type: 'relation-tree',
    icon: 'relation-tree',
    child: [],
    open: false
  }],
  decoration: [{
    title: '全部',
    type: 'decoration-all',
    icon: 'all',
    child: [],
    open: true,
    thumIcon: 'el-icon-cold-drink'
  }],
  interaction: [{
    title: '全部',
    type: 'interaction-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '表单',
    type: 'interaction-form',
    icon: 'interaction-form',
    child: [],
    open: false
  }, {
    title: '导航',
    type: 'interaction-navigator',
    icon: 'interaction-navigator',
    child: [],
    open: false
  }, {
    title: '容器',
    type: 'interaction-container',
    icon: 'interaction-container',
    child: [],
    open: false
  }],
  data: [{
    title: '全部',
    type: 'data-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '文本',
    type: 'data-text',
    icon: 'data-text',
    child: [],
    open: false
  }, {
    title: '数据集',
    type: 'data-set',
    icon: 'data-set',
    child: [],
    open: false
  }, {
    title: '指标卡',
    type: 'data-indicator',
    icon: 'data-indicator',
    child: [],
    open: false
  }],
  map: [{
    title: '全部',
    type: 'map-all',
    icon: 'all',
    child: [],
    open: true,
    thumIcon: 'map'
  }],
  layout: [{
    title: '全部',
    type: 'layout-all',
    icon: 'all',
    child: [],
    open: false,
    thumIcon: 'all'
  }, {
    title: '3D背景',
    type: 'layout-background',
    icon: '3d-background',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '主视觉',
    type: 'layout-main',
    icon: 'layout-main',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '图文标题',
    type: 'layout-title',
    icon: 'layout-title',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '图文部件',
    type: 'layout-widget',
    icon: 'widget',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '骨架布局',
    type: 'layout-skeleton',
    icon: 'skeleton',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '功能布局',
    type: 'layout-functional',
    icon: 'functional',
    child: [],
    open: false,
    thumIcon: 'layout'
  }],
  custom: [{
    title: '全部',
    type: 'custom-all',
    icon: 'all',
    child: [],
    open: true,
    thumIcon: 'custom'
  }],
  other: [{
    title: '全部',
    type: 'other-all',
    icon: 'all',
    child: [],
    open: true,
    thumIcon: 'custom'
  }]
}

/**
 * 指标库组件列表
 */
export const indicatorCompList = {
  regular: [{
    title: '全部',
    type: 'regular-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '柱形图',
    type: 'regular-bar',
    icon: 'bar',
    child: [],
    open: false
  }, {
    title: '折线图',
    type: 'regular-line',
    icon: 'line',
    child: [],
    open: false
  }, {
    title: '饼图',
    type: 'regular-pie',
    icon: 'pie',
    child: [],
    open: false
  }, {
    title: '散点图',
    type: 'regular-scatter',
    icon: 'scatter',
    child: [],
    open: false
  }, {
    title: '多类型图',
    type: 'regular-mult',
    icon: 'mult',
    child: [],
    open: false
  }, {
    title: '其他',
    type: 'regular-other',
    icon: 'other',
    child: [],
    open: false
  }],
  media: [{
    title: '全部',
    type: 'media-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '图片',
    type: 'media-image',
    icon: 'image',
    child: [],
    open: false
  }, {
    title: '视频',
    type: 'media-video',
    icon: 'media-video',
    child: [],
    open: false
  }, {
    title: '模型',
    type: 'media-model',
    icon: 'media-model',
    child: [],
    open: false
  }],
  relation: [{
    title: '全部',
    type: 'relation-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '流程图',
    type: 'relation-flow',
    icon: 'relation-flow',
    child: [],
    open: false
  }, {
    title: '关系图',
    type: 'relation-graph',
    icon: 'relation-graph',
    child: [],
    open: false
  }, {
    title: '树图',
    type: 'relation-tree',
    icon: 'relation-tree',
    child: [],
    open: false
  }],
  decoration: [{
    title: '全部',
    type: 'decoration-all',
    icon: 'all',
    child: [],
    open: true,
    thumIcon: 'el-icon-cold-drink'
  }],
  interaction: [{
    title: '全部',
    type: 'interaction-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '表单',
    type: 'interaction-form',
    icon: 'interaction-form',
    child: [],
    open: false
  }, {
    title: '导航',
    type: 'interaction-navigator',
    icon: 'interaction-navigator',
    child: [],
    open: false
  }],
  data: [{
    title: '全部',
    type: 'data-all',
    icon: 'all',
    child: [],
    open: false
  }, {
    title: '文本',
    type: 'data-text',
    icon: 'data-text',
    child: [],
    open: false
  }, {
    title: '数据集',
    type: 'data-set',
    icon: 'data-set',
    child: [],
    open: false
  }, {
    title: '指标卡',
    type: 'data-indicator',
    icon: 'data-indicator',
    child: [],
    open: false
  }],
  layout: [{
    title: '全部',
    type: 'layout-all',
    icon: 'all',
    child: [],
    open: false,
    thumIcon: 'all'
  }, {
    title: '3D背景',
    type: 'layout-background',
    icon: '3d-background',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '主视觉',
    type: 'layout-main',
    icon: 'layout-main',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '图文标题',
    type: 'layout-title',
    icon: 'layout-title',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '图文部件',
    type: 'layout-widget',
    icon: 'widget',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '骨架布局',
    type: 'layout-skeleton',
    icon: 'skeleton',
    child: [],
    open: false,
    thumIcon: 'layout'
  }, {
    title: '功能布局',
    type: 'layout-functional',
    icon: 'functional',
    child: [],
    open: false,
    thumIcon: 'layout'
  }],
  custom: [{
    title: '全部',
    type: 'custom-all',
    icon: 'all',
    child: [],
    open: true,
    thumIcon: 'custom'
  }],
  other: [{
    title: '全部',
    type: 'other-all',
    icon: 'all',
    child: [],
    open: true,
    thumIcon: 'custom'
  }]
}

// 数据源类型
export const SOURCE_TYPES = {
  static: 'static',
  csv_file: 'csv_file',
  api: 'api',
  mysql: 'mysql',
  dmc_normal_table: 'dmc_normal_table',
  dmc_flow_table: 'dmc_flow_table',
  oracle: 'oracle',
  sql_server: 'sql_server',
  api_gateway: 'api_gateway',
  elastic_search: 'elastic_search',
  excel: 'excel',
  highgodb: 'highgodb'
}

// 组件公共行为
export const COMMON_ACTIONS = {
  show: 'show', // 显示
  hide: 'hide', // 隐藏
  showHideSwitch: 'showHideSwitch', // 显隐切换
  animationEvent: 'animationEvent', // 动画事件
  jumpEvent: 'jumpEvent', // 跳转事件
  switchScenePage: 'switchScenePage', // 切换场景页面
  updateConfig: 'updateConfig', // 更新组件配置
  clearCallbackValue: 'clearCallbackValue', // 重置回调参数
  executeScrpit: 'executeScrpit', // 执行脚本
  sendRequest: 'sendRequest', // 发送请求
  anchorLocation: 'anchorLocation' // 锚点定位
}

export const COMMON_EVENTS = [
  'dataChange',
  'compClick',
  'compHover',
  'compDbclick',
  'compMouseleave'
]

// showInPanel 配置规则支持的运算符
export const OPERATOR_TYPES = {
  $eq: '$eq',
  $ne: '$ne',
  $gt: '$gt',
  $lt: '$lt',
  $gte: '$gte',
  $lte: '$lte',
  $in: '$in',
  $nin: '$nin'
}

// showInPanel 配置规则支持的逻辑运算
export const LOGICAL_TYPES = {
  $and: '$and',
  $or: '$or'
}

// 对齐方式图标
export const aliginIcon = [{
  title: '顶部对齐',
  icon: 'topalign',
  event: 'top'
}, {
  title: '水平居中对齐',
  icon: 'centeralign',
  event: 'verticalCenter'
}, {
  title: '底部对齐',
  icon: 'bottomalign',
  event: 'bottom'
}, {
  title: '左对齐',
  icon: 'leftalign',
  event: 'left'
}, {
  title: '垂直居中对齐',
  icon: 'topbottomalign',
  event: 'horizontally'
}, {
  title: '右对齐',
  icon: 'rightalign',
  event: 'right'
}]

// 分布方式图标
export const distributionIcon = [{
  title: '水平',
  icon: 'shuipingfenbu',
  event: 'horizontal'
}, {
  title: '垂直',
  icon: 'chuizhifenbu',
  event: 'vertical'
}]

export const navMain = [{
  label: '我的可视化',
  route: '/',
  icon: 'nav-visualized'
}, {
  label: '我的数据',
  route: '/data',
  icon: 'nav-data'
}, {
  label: '我的资源',
  route: '/resources',
  icon: 'nav-resources'
}, {
  label: '我的组件',
  route: '/component',
  icon: 'nav-component'
}]

// 过滤器分类
export const filterType = [
  { label: '数字处理模板', value: '0' },
  { label: '格式转换模板', value: '1' },
  { label: '字段截取模板', value: '2' },
  { label: '文本处理模板', value: '3' },
  { label: '自定义模板', value: '4' }
]

// 图片平铺方式
export const repeatOpt = [
  { id: 1, label: '拉伸', value: 'fill' },
  { id: 2, label: '适应', value: 'contain' },
  { id: 3, label: '填充', value: 'cover' },
  { id: 4, label: '平铺', value: 'repeat' }
]

// pc屏幕分辨率
export const screenList = [
  { id: 1, label: '大屏推荐尺寸1920*1080', value: '1920*1080' },
  { id: 2, label: 'web最常见尺寸1366*768', value: '1366*768' },
  { id: 3, label: 'web最小尺寸1024*768', value: '1024*768' },
  { id: 4, label: '自定义', value: 'diy' }
]

// 屏幕缩放类型
export const scaleOpt = [
  { value: 'full_screen', label: '全屏铺满', icon: 'full_screen' },
  { value: 'full_width', label: '等比缩放宽度铺满', icon: 'fit-width' },
  { value: 'full_height', label: '等比缩放高度铺满', icon: 'fit-height' },
  { value: 'full_height_scroll', label: '等比缩放高度铺满（可滚动）', icon: 'fit-height-scrollable' },
  { value: 'no_scale', label: '不缩放', icon: 'Words_not_allowed' }
]

// 粒子效果
export const particlesTypeOpt = [
  { id: 'particles_1', label: '无效果', value: 'STYLE_NONE' },
  { id: 'particles_2', label: '效果一', value: 'STYLE_NASA' },
  { id: 'particles_4', label: '效果二', value: 'STYLE_SNOW' },
  { id: 'particles_5', label: '效果三', value: 'STYLE_GROWING' },
  { id: 'particles_6', label: '效果四', value: 'STYLE_TWINKLE' },
  { id: 'particles_7', label: '效果五', value: 'STYLE_NINJA' },
  { id: 'particles_8', label: '效果六', value: 'STYLE_METEOR' }
]

// 字段映射
export const keyMap = {
  name: ['x', 'label', '名称', '姓名', '类型'],
  value: ['y', 'val', 'num', '值', '数量', '人数'],
  x: ['name', 'label', '名称', '姓名', '类型'],
  y: ['value', 'val', 'num', '值', '数量', '人数'],
  s: ['series', '维度'],
  series: ['s', '维度'],
  lng: ['longitude', '经度'],
  lat: ['latitude', '纬度']
}

export const numberCondition = [
  {
    text: '等于',
    value: 'equal'
  }, {
    text: '不等于',
    value: 'unequal'
  }, {
    text: '大于',
    value: 'greater'
  }, {
    text: '小于',
    value: 'less'
  }, {
    text: '大于等于',
    value: 'greaterOrEqual'
  }, {
    text: '小于等于',
    value: 'lessOrEqual'
  }, {
    text: '被数组包含',
    value: 'in'
  }, {
    text: '不为空',
    value: 'notNull'
  }, {
    text: '为空',
    value: 'null'
  }
]

export const stringCondition = [
  {
    text: '等于',
    value: 'equal'
  }, {
    text: '不等于',
    value: 'unequal'
  }, {
    text: '包含',
    value: 'contain'
  }, {
    text: '被包含',
    value: 'contained'
  }, {
    text: '不包含',
    value: 'notContain'
  }, {
    text: '被数组包含',
    value: 'in'
  }, {
    text: '开头匹配',
    value: 'matchOnStart'
  }, {
    text: '结尾匹配',
    value: 'matchOnEnd'
  }, {
    text: '不为空',
    value: 'notNull'
  }, {
    text: '为空',
    value: 'null'
  }
]

export const dateCondition = [
  {
    text: '等于',
    value: 'equal'
  }, {
    text: '不等于',
    value: 'unequal'
  }, {
    text: '大于',
    value: 'greater'
  }, {
    text: '小于',
    value: 'less'
  }, {
    text: '大于等于',
    value: 'greaterOrEqual'
  }, {
    text: '小于等于',
    value: 'lessOrEqual'
  }, {
    text: '被数组包含',
    value: 'in'
  }, {
    text: '不为空',
    value: 'notNull'
  }, {
    text: '为空',
    value: 'null'
  }
]

export const booleanCondition = [
  {
    text: '等于',
    value: 'equal'
  }, {
    text: '不等于',
    value: 'unequal'
  }
]

export const sourceTypeOpt = [
  { value: 'static', label: '静态数据' },
  { value: 'datacontainer', label: '数据容器【荐】' },
  { value: 'csv_file', label: 'CSV文件' },
  { value: 'excel', label: 'Excel文件' },
  { value: 'api', label: 'API数据' },
  { value: 'mysql', label: 'MySQL数据库' },
  { value: 'postgresql', label: 'PostgreSQL数据库' },
  { value: 'oracle', label: 'Oracle数据库' },
  { value: 'dmc', label: 'DMC数据库' },
  { value: 'mongodb', label: 'MongoDB数据库' },
  { value: 'websocket', label: '实时数据' },
  { value: 'dashboard', label: '仪表盘数据' },
  { value: 'json', label: 'JSON文件' },
  { value: 'dmdb', label: '达梦数据库' },
  { value: 'highgodb', label: '瀚高数据库' }
]

// 支持后端下钻的数据类型
export const drillSourceOpt = [
  'csv_file', 'excel', 'mysql', 'postgresql', 'oracle', 'dmc', 'mongodb', 'dmdb'
]

export const dateCalculation = [
  { value: 'caculate_year', label: '按年' },
  { value: 'caculate_quarter', label: '按季' },
  { value: 'caculate_month', label: '按月' },
  { value: 'caculate_week', label: '按周' },
  { value: 'caculate_day', label: '按日' },
  { value: 'caculate_hour', label: '按时' },
  { value: 'caculate_min', label: '按分' },
  { value: 'caculate_second', label: '按秒' }
]

export const numCalculation = [
  { value: 'max', label: '最大值' },
  { value: 'min', label: '最小值' },
  { value: 'avg', label: '平均值' },
  { value: 'count', label: '计数' },
  { value: 'count_distinct', label: '去重计数' },
  { value: 'sum', label: '求和' }
]

export const stringCalculation = [
  { value: 'count', label: '计数' },
  { value: 'count_distinct', label: '去重计数' }
]

export const methodOpt = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
  { value: 'PATCH', label: 'PATCH' }
]

export const limitOption = [
  { id: 0, value: 10, label: '10' },
  { id: 1, value: 20, label: '20' },
  { id: 2, value: 50, label: '50' },
  { id: 3, value: 100, label: '100' },
  { id: 4, value: 200, label: '200' },
  { id: 5, value: 500, label: '500' }
]

export const timeOption = [
  { label: '分', value: 'minute' },
  { label: '秒', value: 'second' }
]

export const tipsControlConfig = {
  font: {
    name: '字体',
    type: 'font',
    default: {
      fontSize: 14,
      color: '#FFFFFF',
      fontFamily: 'Microsoft YaHei',
      fontWeight: 'bold'
    },
    isNecessary: true
  },
  background: {
    name: '背景',
    type: 'backgroundGroup',
    default: {
      show: false,
      type: 'image',
      pure: 'rgba(9,61,101,0.3)',
      gradient: {
        type: 'linear-gradient',
        deg: 0,
        start: '#0000ff',
        startScale: 0,
        end: '#ffc0cb',
        endScale: 100,
        shape: 'ellipse'
      },
      image: {
        url: '',
        size: '100% 100%',
        positionX: 'center',
        positionY: 'center',
        repeat: 'no-repeat'
      }
    },
    isNecessary: true
  }
}

// 移动端预览页需要设置高度为0的组件 如数据容器/弹出面板等
export const mobileZeroComs = [
  'interaction-container-datacontainer',
  'interaction-container-popup',
  'media-image-preview'
]

// 总是隐藏的组件列表
export const alawysHideComs = [
  'interaction-form-dateRangePicker',
  'interaction-container-modulepanel',
  'interaction-navigator-menu',
  'interaction-container-indicator',
  'interaction-container-dynamicpanel'
]

// 移动端需要隐藏的组件列表
export const mobileHideComs = [
  'interaction-container-carousepanel',
  'interaction-container-popoverpanel',
  'interaction-container-mapShadowPanel',
  'interaction-container-statusdialogpanel'
]

// 不需要显示主题的组件
export const noThemeComs = [
  'interaction-container-modulepanel'
]

export const CLICKTIME = 20

export const REQUEST_TIME_RANGE = 30

export const REQUEST_MERGE_NUM = 10

export const M_ZERO_HEIGHT = 0.001

export const MIN_RECORD_VOL = 30

export const MIN_RECORD_DURATION = 1
