import Vue from 'vue';
import router from '@/router/edit/index'
import DataTextEdit from '@/components/dialog/DataTextEdit.vue';
import store from '@/store/index';
const actions = {
  dblclick: {
    'data-text-normal': {
      handler: function (comInfo, comData) {
        if (!comInfo || !comData) return;
        if (comInfo.dataConfig.dataResponse.sourceType !== 'static') return;
        const Com = Vue.extend(DataTextEdit);
        const dom = document.createElement('div');
        document.body.appendChild(dom);
        const instance = new Com({ store }).$mount(dom);
        instance.show(comInfo, comData);
      }
    },
    'interaction-container-carousepanel': {
      handler: function (comInfo, comData) {
        if (!comInfo || !comData) return;
        const sourceType = comInfo.dataConfig.dataResponse.sourceType
        const screenId = comInfo.config.screens[0].id
        const routeData = router.resolve({
          path: `/screen/edit/${screenId}`,
          query: {
            screenId: comInfo.screenId,
            comp: 'dialog',
            type: sourceType,
            cid: comInfo.id
          }
        })
        window.open(routeData.href, `screen_${screenId}`)
      }
    }
  }
}

export default actions
