const FONT_ICON_OPTIONS = [
  // icon-svg文件夹
  {
    label: '3d-background',
    value: '3d-background'
  },
  {
    label: 'C200',
    value: 'C200'
  },
  {
    label: 'C210',
    value: 'C210'
  },
  {
    label: 'C211',
    value: 'C211'
  },
  {
    label: 'C212',
    value: 'C212'
  },
  {
    label: 'C213',
    value: 'C213'
  },
  {
    label: 'C220',
    value: 'C220'
  },
  {
    label: 'C221',
    value: 'C221'
  },
  {
    label: 'C230',
    value: 'C230'
  },
  {
    label: 'C240',
    value: 'C240'
  },
  {
    label: 'C241',
    value: 'C241'
  },
  {
    label: 'C242',
    value: 'C242'
  },
  {
    label: 'C243',
    value: 'C243'
  },
  {
    label: 'C250',
    value: 'C250'
  },
  {
    label: 'C261',
    value: 'C261'
  },
  {
    label: 'C271',
    value: 'C271'
  },
  {
    label: 'C280',
    value: 'C280'
  },
  {
    label: 'C281',
    value: 'C281'
  },
  {
    label: 'C282',
    value: 'C282'
  },
  {
    label: 'C290',
    value: 'C290'
  },
  {
    label: 'C301',
    value: 'C301'
  },
  {
    label: 'C310',
    value: 'C310'
  },
  {
    label: 'C320',
    value: 'C320'
  },
  {
    label: 'C330',
    value: 'C330'
  },
  {
    label: 'C340',
    value: 'C340'
  },
  {
    label: 'C350',
    value: 'C350'
  },
  {
    label: 'C351',
    value: 'C351'
  },
  {
    label: 'C352',
    value: 'C352'
  },
  {
    label: 'C360',
    value: 'C360'
  },
  {
    label: 'C370',
    value: 'C370'
  },
  {
    label: 'C381',
    value: 'C381'
  },
  {
    label: 'C382',
    value: 'C382'
  },
  {
    label: 'C383',
    value: 'C383'
  },
  {
    label: 'C390',
    value: 'C390'
  },
  {
    label: 'Words_not_allowed',
    value: 'Words_not_allowed'
  },
  {
    label: 'abnormal-info',
    value: 'abnormal-info'
  },
  {
    label: 'abnormal',
    value: 'abnormal'
  },
  {
    label: 'align',
    value: 'align'
  },
  {
    label: 'all',
    value: 'all'
  },
  {
    label: 'append-data',
    value: 'append-data'
  },
  {
    label: 'attachment',
    value: 'attachment'
  },
  {
    label: 'arrow',
    value: 'arrow'
  },
  {
    label: 'arrowDonw',
    value: 'arrowDonw'
  },
  {
    label: 'bar',
    value: 'bar'
  },
  {
    label: 'bg',
    value: 'bg'
  },
  {
    label: 'bottomalign',
    value: 'bottomalign'
  },
  {
    label: 'cancelgroup',
    value: 'cancelgroup'
  },
  {
    label: 'centeralign',
    value: 'centeralign'
  },
  {
    label: 'chuizhifenbu',
    value: 'chuizhifenbu'
  },
  {
    label: 'close',
    value: 'close'
  },
  {
    label: 'color-board',
    value: 'color-board'
  },
  {
    label: 'comlist',
    value: 'comlist'
  },
  {
    label: 'custom',
    value: 'custom'
  },
  {
    label: 'dark-icon',
    value: 'dark-icon'
  },
  {
    label: 'data-indicator',
    value: 'data-indicator'
  },
  {
    label: 'data-set',
    value: 'data-set'
  },
  {
    label: 'data-text',
    value: 'data-text'
  },
  {
    label: 'data',
    value: 'data'
  },
  {
    label: 'decorationk',
    value: 'decorationk'
  },
  {
    label: 'edit',
    value: 'edit'
  },
  {
    label: 'entity-view',
    value: 'entity-view'
  },
  {
    label: 'export',
    value: 'export'
  },
  {
    label: 'fenbu',
    value: 'fenbu'
  },
  {
    label: 'field-number',
    value: 'field-number'
  },
  {
    label: 'field-string',
    value: 'field-string'
  },
  {
    label: 'filter',
    value: 'filter'
  },
  {
    label: 'fit-height-scrollable',
    value: 'fit-height-scrollable'
  },
  {
    label: 'fit-height',
    value: 'fit-height'
  },
  {
    label: 'fit-width',
    value: 'fit-width'
  },
  {
    label: 'full_screen',
    value: 'full_screen'
  },
  {
    label: 'functional',
    value: 'functional'
  },
  {
    label: 'icon-copy',
    value: 'icon-copy'
  },
  {
    label: 'icon-docfile',
    value: 'icon-docfile'
  },
  {
    label: 'icon-font',
    value: 'icon-font'
  },
  {
    label: 'icon-img',
    value: 'icon-img'
  },
  {
    label: 'icon-plus',
    value: 'icon-plus'
  },
  {
    label: 'icon-video',
    value: 'icon-video'
  },
  {
    label: 'image',
    value: 'image'
  },
  {
    label: 'import',
    value: 'import'
  },
  {
    label: 'interaction-container',
    value: 'interaction-container'
  },
  {
    label: 'interaction-form',
    value: 'interaction-form'
  },
  {
    label: 'interaction-navigator',
    value: 'interaction-navigator'
  },
  {
    label: 'interaction',
    value: 'interaction'
  },
  {
    label: 'layer-cancelGroup',
    value: 'layer-cancelGroup'
  },
  {
    label: 'layer-creatGroup',
    value: 'layer-creatGroup'
  },
  {
    label: 'layer-folder-open',
    value: 'layer-folder-open'
  },
  {
    label: 'layer-folder',
    value: 'layer-folder'
  },
  {
    label: 'layer-hideOthers',
    value: 'layer-hideOthers'
  },
  {
    label: 'layer',
    value: 'layer'
  },
  {
    label: 'layout-main',
    value: 'layout-main'
  },
  {
    label: 'layout-title',
    value: 'layout-title'
  },
  {
    label: 'layout',
    value: 'layout'
  },
  {
    label: 'leftalign',
    value: 'leftalign'
  },
  {
    label: 'light-icon',
    value: 'light-icon'
  },
  {
    label: 'line',
    value: 'line'
  },
  {
    label: 'lock',
    value: 'lock'
  },
  {
    label: 'logout',
    value: 'logout'
  },
  {
    label: 'map',
    value: 'map'
  },
  {
    label: 'media-model',
    value: 'media-model'
  },
  {
    label: 'media-video',
    value: 'media-video'
  },
  {
    label: 'media',
    value: 'media'
  },
  {
    label: 'move-to',
    value: 'move-to'
  },
  {
    label: 'move',
    value: 'move'
  },
  {
    label: 'mult',
    value: 'mult'
  },
  {
    label: 'nav-component',
    value: 'nav-component'
  },
  {
    label: 'nav-course',
    value: 'nav-course'
  },
  {
    label: 'nav-data',
    value: 'nav-data'
  },
  {
    label: 'nav-resources',
    value: 'nav-resources'
  },
  {
    label: 'nav-visualized',
    value: 'nav-visualized'
  },
  {
    label: 'notice',
    value: 'notice'
  },
  {
    label: 'other',
    value: 'other'
  },
  {
    label: 'page-template',
    value: 'page-template'
  },
  {
    label: 'photo',
    value: 'photo'
  },
  {
    label: 'pie',
    value: 'pie'
  },
  {
    label: 'plus',
    value: 'plus'
  },
  {
    label: 'preview',
    value: 'preview'
  },
  {
    label: 'publish',
    value: 'publish'
  },
  {
    label: 'puzzle',
    value: 'puzzle'
  },
  {
    label: 'redo',
    value: 'redo'
  },
  {
    label: 'regular',
    value: 'regular'
  },
  {
    label: 'relation-flow',
    value: 'relation-flow'
  },
  {
    label: 'relation-graph',
    value: 'relation-graph'
  },
  {
    label: 'relation-tree',
    value: 'relation-tree'
  },
  {
    label: 'relation',
    value: 'relation'
  },
  {
    label: 'replace',
    value: 'replace'
  },
  {
    label: 'rightalign',
    value: 'rightalign'
  },
  {
    label: 'rightpanel',
    value: 'rightpanel'
  },
  {
    label: 'rotate',
    value: 'rotate'
  },
  {
    label: 'save',
    value: 'save'
  },
  {
    label: 'scatter',
    value: 'scatter'
  },
  {
    label: 'share',
    value: 'share'
  },
  {
    label: 'shortcut',
    value: 'shortcut'
  },
  {
    label: 'shuipingfenbu',
    value: 'shuipingfenbu'
  },
  {
    label: 'skeleton',
    value: 'skeleton'
  },
  {
    label: 'source',
    value: 'source'
  },
  {
    label: 'terminal',
    value: 'terminal'
  },
  {
    label: 'theme',
    value: 'theme'
  },
  {
    label: 'tobottom',
    value: 'tobottom'
  },
  {
    label: 'togroup',
    value: 'togroup'
  },
  {
    label: 'tools',
    value: 'tools'
  },
  {
    label: 'topalign',
    value: 'topalign'
  },
  {
    label: 'topbottomalign',
    value: 'topbottomalign'
  },
  {
    label: 'totop',
    value: 'totop'
  },
  {
    label: 'trash',
    value: 'trash'
  },
  {
    label: 'tutorial-document',
    value: 'tutorial-document'
  },
  {
    label: 'tutorial-edit',
    value: 'tutorial-edit'
  },
  {
    label: 'tutorial-media',
    value: 'tutorial-media'
  },
  {
    label: 'tutorial-selected',
    value: 'tutorial-selected'
  },
  {
    label: 'tutorial-unselect',
    value: 'tutorial-unselect'
  },
  {
    label: 'undo',
    value: 'undo'
  },
  {
    label: 'update',
    value: 'update'
  },
  {
    label: 'upload',
    value: 'upload'
  },
  {
    label: 'user-icon',
    value: 'user-icon'
  },
  {
    label: 'widget',
    value: 'widget'
  },
  // icon-svg-lossless文件夹
  { label: 'add-event', value: 'add-event' },
  { label: 'ico-add-img', value: 'ico-add-img' },
  { label: 'ico-select-no', value: 'ico-select-no' },
  { label: 'ico-select-ok', value: 'ico-select-ok' },
  { label: 'ico-select-other', value: 'ico-select-other' },
  { label: 'ico-text', value: 'ico-text' },
  { label: 'ico-view', value: 'ico-view' },
  { label: 'icon-img-load-error', value: 'icon-img-load-error' },
  { label: 'select-circle-no', value: 'select-circle-no' },
  { label: 'select-circle-ok', value: 'select-circle-ok' },
  { label: 'source-api', value: 'source-api' },
  { label: 'source-csv', value: 'source-csv' },
  { label: 'source-json', value: 'source-json' },
  { label: 'source-type', value: 'source-type' }
]

export default FONT_ICON_OPTIONS
