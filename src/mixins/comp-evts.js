import mitt from 'mitt';
import { uuid } from '@/utils/base'

const compBus = mitt();
const events = {};

// 注：此类仅用于组件的 methods mixin

// 此组件监听来自其他组件的事件
export function on (compId, eventName, callback, context) {
  const event = `${compId}:${eventName}`;
  compBus.on(event, callback);
  (events[event] || (events[event] = [])).push({
    fromId: this.id,
    cb: callback,
    ctx: context
  });
  return this;
}

// 移除监听
export function off (compId, eventName, callback) {
  const event = `${compId}:${eventName}`;
  if (!arguments.length) { // 移除此组件对外的所有监听
    Object.keys(events)
      .forEach(event => {
        const cbs = events[event];
        cbs && _.remove(cbs, e => {
          if (e.fromId === this.id) {
            compBus.off(event, e.cb);
            return true;
          }
        });
      });
  } else if (arguments.length === 1) { // 移除此组件对某个组件的所有事件监听
    Object.keys(events)
      .filter(event => new RegExp(`^${compId}:`).test(event))
      .forEach(event => {
        const cbs = events[event];
        if (cbs && cbs.length) {
          _.remove(cbs, e => {
            if (e.fromId === this.id) {
              compBus.off(event, e.cb);
              return true;
            }
          });
        }
      });
  } else if (arguments.length === 2) { // 移除此组件对某个组件某个事件的监听
    const cbs = events[event];
    if (cbs && cbs.length) {
      _.remove(cbs, e => {
        if (e.fromId === this.id) {
          compBus.off(event, e.cb);
          return true;
        }
      });
    }
  } else { // 移除此组件对某个组件某个事件的某个回调的监听
    const cbs = events[event];
    if (cbs && cbs.length) {
      _.remove(cbs, e => {
        if (e.fromId === this.id && e.cb === callback) {
          compBus.off(event, e.cb);
          return true;
        }
      });
    }
  }
  return this;
}

// 抛出事件
export function emit (eventName, data) {
  if (this.$isControl) {
    const message = {
      type: 'event',
      params: { id: this.id, eventName, data }
    }
    this.$socket && this.$socket.emit('chat', message);
  }
  const event = `${this.id}:${eventName}`;
  setTimeout(() => { // 注：解决触发事件内用回调值 事件先触发而回调还未触发获取不到回调值问题
    compBus.emit(event, { ...data, uuid: uuid() });
  }, 0);
  return this;
}
