import { mapGetters } from 'vuex';
import { OPERATOR_TYPES, LOGICAL_TYPES } from '@/common/constants';

// 获取对象下某个路径的值
function getValueByPath (obj, path = '', currentPathArr = []) {
  if (!_.isObject(obj)) {
    throw new TypeError('obj 不是一个对象');
  }
  if (!_.isString(path)) {
    throw new TypeError('path 格式错误');
  }
  if (!path) {
    return obj;
  }
  const isAbsolute = path.slice(0, 1) !== '.';
  if (isAbsolute) {
    // 绝对路径
    return _.result(obj, path);
  } else {
    const firstNoDotIndex = _.findIndex(path, c => c !== '.');
    if (firstNoDotIndex <= -1) {
      throw new Error('相对路径点标记后必须附加属性名');
    }
    const level = Math.floor(firstNoDotIndex / 2) + 1;
    if (level > currentPathArr.length) {
      throw new Error('相对路径级数超出范围');
    }
    const relativeBeforePath = _.dropRight(currentPathArr, level).join('.');
    const relativeAfterPath = path.slice(firstNoDotIndex);
    const absolutePath = relativeBeforePath.length ? relativeBeforePath + '.' + relativeAfterPath : relativeAfterPath;
    return _.result(obj, absolutePath);
  }
}

// 获取条件值
function getConditionValue (obj, [path, operator, value], currentPathArr = []) {
  if (!_.isObject(obj)) {
    throw new TypeError('obj 不是一个对象');
  }
  const objVal = getValueByPath(obj, path, currentPathArr);
  switch (operator) {
    // 相等
    case OPERATOR_TYPES.$eq: return objVal === value;
    // 不相等
    case OPERATOR_TYPES.$ne: return objVal !== value;
    // 大于
    case OPERATOR_TYPES.$gt: return objVal > value;
    // 小于
    case OPERATOR_TYPES.$lt: return objVal < value;
    // 大于等于
    case OPERATOR_TYPES.$gte: return objVal >= value;
    // 小于等于
    case OPERATOR_TYPES.$lte: return objVal <= value;
    // 在数组中
    case OPERATOR_TYPES.$in: return value.indexOf(objVal) > -1;
    // 不在数组中
    case OPERATOR_TYPES.$nin: return value.indexOf(objVal) === -1;
    default: throw new Error('不支持该运算符：' + operator);
  }
}

export default {
  props: ['node'],

  computed: {
    ...mapGetters('comtheme', ['comCtlConfigObj']),
    value: {
      get () {
        const tree = this.node && this.node.tree;
        if (tree) {
          const valuePathArr = tree.valuePathMap[this.node.id];
          return valuePathArr.reduce((a, v) => {
            return a[v];
          }, this.comCtlConfigObj);
        }
      },
      set (value) {
        const tree = this.node && this.node.tree;
        if (tree) {
          const paths = tree.valuePathMap[this.node.id];
          this.$store.dispatch('comtheme/updateScreenComTheme', {
            keyValPairs: [
              { key: `config.${paths.join('.')}`, value: value }
            ]
          });
        }
      }
    },
    // showInPanel配置规则: https://help.aliyun.com/document_detail/157252.html?spm=a2c4g.11186623.2.11.68af72f1uVoe1F
    showInPanel () {
      const { tree, data, id } = this.node;
      if (!tree || !data || !data.showInPanel) return true;
      const { conditions = [], logicalType = LOGICAL_TYPES.$and } = data.showInPanel;
      const currentPathArr = tree.valuePathMap[id];
      const conditionsRes = conditions.map(c => getConditionValue(this.comCtlConfigObj, c, currentPathArr));
      if (logicalType === LOGICAL_TYPES.$and) {
        return conditionsRes.every(res => res);
      } else {
        return conditionsRes.some(res => res);
      }
    }
  }
}
