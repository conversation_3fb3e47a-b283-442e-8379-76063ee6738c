import { formatSvg, insertSvgToHtml, formatIconFontSvg } from '@/utils/iconFont'
import http from '@/utils/http'
import { replaceUrl } from '@/utils/base'
import { _ } from 'core-js'
export default {
  props: {
    id: {
      type: [String, Number]
    },
    attr: {
      type: Object,
      default () { return {} }
    },
    config: {
      type: Object,
      default () { return {} }
    },
    callbacks: {
      type: Array,
      default () { return [] }
    },
    data: {
      type: [Array, Object],
      default () { return [] }
    },
    emit: {
      type: Function,
      default () { return function () {} }
    },
    seatom_updateCallbackValue: {
      type: Function,
      default () { return function () {} }
    },
    seatom_setParentStyle: {
      type: Function,
      default () { return function () {} }
    },
    parent: {
      type: Object,
      default () { return null }
    },
    isView: {
      type: Boolean,
      default: false
    },
    platform: {
      type: String,
      default: 'pc'
    }
  },

  computed: {
    newConfig () { // 此处刻意为之 解决config（类型为object）同源导致watch时新旧值始终相等问题
      return _.cloneDeep(this.config)
    },
    newData () {
      return _.cloneDeep(this.data)
    }
  },

  watch: {
    newConfig: { // 监听config 比较新旧值不一样重新render
      handler: function (val, oldVal) {
        if (!_.isEqual(val, oldVal)) {
          this.render('config')
          this.$emit('reRender')
        }
      },
      deep: true
    },
    newData: {
      handler: function (newVal, oldVal) {
        this.$nextTick(() => {
          if (!_.isEqual(newVal, oldVal)) {
            this.render('data')
            this.$emit('reRender')
          }
        })
        this._dataChange();
      },
      deep: true,
      immediate: true
    }
  },

  created () {
    this.$emit('created')
  },

  mounted () {
    this.$emit('mounted')
    try {
      this.init()
    } catch (e) {
      console.warn('组件：' + this.id + ' 出现问题：')
      console.warn(e)
      if (!this.isView) {
        this.$store.commit('editor/updateErrorCompIds', { // 收集出错的组件id
          componentId: this.id,
          errorInfo: e
        })
      }
    }
  },

  beforeDestroy () {
    this.destroy()
    this.$emit('destroyed')
  },

  methods: {
    /**
     * 初始化方法【必选】
     * 会在 vue 生命周期的 mounted 阶段调用
     */
    init () {
      /** write your code */
    },
    /**
     * 渲染组件【必选】
     * 当组件被初始化后，组件渲染逻辑被调用。
     */
    render (changeType) {
      /** write your code */
    },
    /**
     * 自适应尺寸【可选】
     * 当组件被拖拽、缩放时被调用。
     */
    resize ({ width, height }) {
      /** write your code */
    },
    /**
     * 清理组件，只清空画布，组件实例保留【可选】
     * 当组件被清理时调用
     */
    clear () {
      /** write your code */
    },
    /**
     * 销毁组件，彻底销毁，组件实例不可用【可选】
     * 组件被销毁时调用，会在 vue 组件声明周期勾子 beforeDestroy 里调用
     */
    destroy () {
      /** write your code */
    },
    clearCallbackValue () { // 清空组件回调值
      if (this.callbacks && this.callbacks.length) {
        const callbackObj = {}
        this.callbacks.forEach(item => {
          callbackObj[item.name] = undefined
        })
        this.seatom_updateCallbackValue(callbackObj)
      }
    },
    _dataChange () {
      const data = this.data.length ? this.data[0] : {}
      // this.emit('dataChange', { data, allData: this.data }) // 默认事件，当请求完成或数据变化时
      this.emit('dataChange', { ...data })
    },
    conditionalFilter (operation, data) {
      // Array,Object
      const typeOf = param => Object.prototype.toString.call(param).slice(8, -1)
      // 计算每条数据的布尔值
      const calcBool = (arr, logicalType) => logicalType === '$and' ? arr.every(bool => bool) : arr.some(bool => bool)
      // val {x:1,y:2,z:3} val:x; operate:>=; expect:0
      const getOperateValue = (val, expect, operate) => {
        switch (operate) {
          case '$eq' :
            // eslint-disable-next-line
            return val == expect
          case '$ne' :
            // eslint-disable-next-line
            return val != expect
          case '$gt' :
            return val > expect
          case '$lt' :
            return val < expect
          case '$gte' :
            return val >= expect
          case '$lte' :
            return val <= expect
          case '$in' : {
            if (typeOf(val) === 'String' || typeOf(val) === 'Array') {
              return val.includes(expect)
            } else if (typeOf(val) === 'Number' || typeOf(val) === 'Boolean') {
              const temp = String(val)
              return temp.includes(expect)
            } else {
              return false
            }
          }
          case '$nin' : {
            if (typeOf(val) === 'String' || typeOf(val) === 'Array') {
              return !val.includes(expect)
            } else if (typeOf(val) === 'Number' || typeOf(val) === 'Boolean') {
              const temp = String(val)
              return !temp.includes(expect)
            } else {
              return false
            }
          }
        }
      }

      // 嵌套计算
      // tab A
      // [
      //  [true,false], condition A
      //  [true,true]  condition B
      // ]
      // after computed tab A
      // [true,false]
      const getOneConditionResult = (arr, logicalType) => {
        const tempResult = []
        for (let j = 0; j < computedData.length; j++) {
          const temp = []
          for (let i = 0; i < arr.length; i++) {
            temp.push(arr[i][j])
          }
          const resultBool = calcBool(temp, logicalType)
          tempResult.push(resultBool)
        }
        return tempResult
      }

      const result = []
      const resultObj = {}
      const computedData = data.map((item, index) => ({ 'index(序号)': index, ...item }))

      //   [
      //     {
      //       "enable": true,
      //       "logicalType": "$or",
      //       "conditionArray": [
      //         {
      //           "logicalType": "$or",
      //           "condition": [
      //             {
      //               "parm": "name",
      //               "operator": "$ne",
      //               "value": 1
      //             }
      //           ]
      //         }
      //       ],
      //       "style": {
      //         "conditionColors": "rgba(141, 19, 186, 0.9)"
      //       }
      //     }
      //   ]
      operation && !!operation.length && operation.forEach(item => {
        const { conditionArray = [], logicalType, enable } = item
        const oneItemResult = []
        let itemResult = []
        if (enable) {
          conditionArray && !!conditionArray.length && conditionArray.forEach(arrayItem => {
            const arrayItemResult = []
            computedData.map(dataItem => {
              const temp = arrayItem.condition.map(conditionItem => {
                const expect = conditionItem.value
                const operate = conditionItem.operator
                const val = dataItem[conditionItem.parm]
                return getOperateValue(val, expect, operate)
              })
              // true ||false
              const resultBool = calcBool(temp, arrayItem.logicalType)
              // arrayItemResult => [true,false]
              arrayItemResult.push(resultBool)
            })
            oneItemResult.push(arrayItemResult)
          })
          // tab A ,
          itemResult = getOneConditionResult(oneItemResult, logicalType)
        } else {
          // [false,false]
          itemResult.concat(Array(computedData.length).fill(false))
        }
        // result => [[false,false],[true,false]]
        result.push(itemResult)
      })
      // 根据result数组,生成带上style的对象
      for (let i = 0; i < computedData.length; i++) {
        result.forEach((item, index) => {
          if (item[i]) {
            resultObj[i] = operation[index].style
          } else {
            if (!resultObj[i]) {
              resultObj[i] = null
            }
          }
        })
      }
      return resultObj
    },
    broadcast (eventName, params) { // 向下广播事件方法
      function broadcast (children) {
        children && !!children.length && children.forEach(child => {
          child.$emit(eventName, params)
          if (child.$children) {
            broadcast(child.$children)
          }
        })
      }
      broadcast(this.$children);
    },
    handlerAutoHeight (dom) { // 移动端监听dom高度变化，设置grid高度
      if (this.platform === 'mobile') {
        const ob = new ResizeObserver(_.debounce(enties => {
          enties && !!enties.length && enties.forEach(item => {
            const target = item.target;
            this.$emit('seatom_setComConfig', this.id, [{ key: 'attr.h', value: _.ceil(((target.offsetHeight + 5) / 15), 2) }]);
          })
        }, 100))
        ob.observe(dom);
        this.$once('hook:beforeDestroy', () => {
          ob.unobserve(dom)
        })
      }
    },
    loadIconFontFile (iconType, sourceType, url) {
      const componentId = this.id
      // 基础图标
      if (iconType === 1 || !url) {
        return;
      }

      // 自定义图标
      const absoluteUrl = replaceUrl(url)
      // iconfont 方式图标
      if (sourceType === 2) {
        http.get(absoluteUrl).then((res) => {
          const content = formatIconFontSvg(res, componentId);
          insertSvgToHtml(content, componentId);
        });
      } else {
        http.get(absoluteUrl).then((res) => {
          const content = formatSvg(res, componentId);
          insertSvgToHtml(content, componentId);
        });
      }
    }
  }
}
