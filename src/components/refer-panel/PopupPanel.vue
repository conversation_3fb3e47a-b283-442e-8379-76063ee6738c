<template>
  <div
    class="popupBox"
    ref="popupBox"
  >
    <vanPopup
     v-model="show"
     :lock-scroll="true"
     :overlay="true"
     :round="true"
     ref="popup"
     :style="{
      '--radius': this.config && this.config.panel.radius + 'px',
      ...border_background
      }"
     :position="acticveObj.position"
     :get-container="getContainer"
     >
      <div class="content">
        <p ref="popupTitle" class="popupTitle" :style="titleStyle" v-if="titleObj.open">
          <span class="title" :style="textStyle">{{titleObj.text && titleObj.text.content}}</span>
          <span class="icon" :style="iconStyle"><Icon name="cross" @click="closePanel"/></span>
        </p>
        <div class="panelBox">
          <PitchViewScreen
            v-if="isLoadedScreen"
            :item-data="dialogData"
            :screen-style="screenConfig"
            :screen-coms="childScreenComps"
            :screen-layers="childScreenLayers"
            :workspace-id="childWorkspaceId"
            :screen-filters="childFiltersMap"
            :platform="screenInfo.type"
            :heightAuto="heightAuto"
            :isView="isView"
            :mainScreenId="mainScreenId"
            v-on="$listeners"
          />
        </div>
        <seatom-loading v-if="showLoading"/>
      </div>
    </vanPopup>
  </div>
</template>

<script>
import comp from '@/mixins/comp'
import { updateScreenParentId } from '@/api/screen'
import vanPopup from 'vant/lib/popup'
import { Icon } from 'vant';
import 'vant/lib/popup/style'
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'
import quarkDom from 'hz-quark/dist/dom'

export default {
  name: 'PopupPanel', // 弹出层

  inject: ['callbackManager'],

  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager()
    }
  },
  mixins: [comp],
  components: {
    vanPopup,
    Icon
  },
  data () {
    return {
      show: false,
      screenInfo: {},
      dialogData: [],
      screenConfig: {},
      childScreenComps: {},
      childScreenLayers: [],
      childWorkspaceId: 0,
      childFiltersMap: {},
      acticveObj: {},
      titleObj: {},
      heightAuto: false,
      showLoading: false,
      isLoadedScreen: false
    }
  },
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },
  computed: {
    screenList () {
      return this.config.screens || []
    },
    titleStyle () {
      if (!this.titleObj.open) {
        return ''
      }
      const obj = this.titleObj
      const cssText = `text-align:${obj.align};height:${obj.height}px;`
      return cssText
    },
    textStyle () {
      if (!this.titleObj.open) {
        return ''
      }
      const obj = this.titleObj
      const cssText = `color:${obj.text.font.color};font-family:${obj.text.font.fontFamily};font-size:${obj.text.font.fontSize}px;fontWeight:${obj.text.font.fontWeight};line-height:${obj.height}px;padding:0 ${obj.icon.size}px`
      return cssText
    },
    iconStyle () {
      if (!this.titleObj.open) {
        return ''
      }
      const obj = this.titleObj
      const position = obj.icon.postion === 'top-right' ? 'right' : 'left'
      const cssText = `color:${obj.icon.color};font-size:${obj.icon.size}px;${position}:8px;line-height:${obj.height}px;`
      return cssText
    },
    border_background () {
      const myConfig = _.cloneDeep(this.config)
      let background = {}
      let border = {}
      if (myConfig.background.show) {
        background = quarkDom.formatBackground(myConfig.background)
      }
      if (myConfig.border.show) {
        border = quarkDom.formatBorder(myConfig.border)
      }
      return Object.assign({}, background, border)
    },
    panelBoxStyle () {
      const obj = this.titleObj
      if (this.titleObj.open) {
        return `height:${300 - obj.height}px`
      } else {
        return 'height:100%'
      }
    }
  },
  watch: {
    show: {
      handler (val) {
        this.emitEvent(val)
      }
    }
  },
  methods: {
    init () {
      this.popupBox = this.$refs.popupBox
      this.popup = this.$refs.popup
      const { mobileSetting = { heightAuto: false } } = this.config
      if (mobileSetting.heightAuto && this.isView) {
        this.heightAuto = mobileSetting.heightAuto;
        this.handlerAutoHeight && this.handlerAutoHeight(this.popupBox)
      }
    },
    async fetchData () {
      if (!this.screenList.length) {
        return
      }

      if (this.showLoading || this.isLoadedScreen) {
        return
      }

      this.showLoading = true

      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id })

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }
      // 检查修复结束

      this.screenConfig = screenData.config
      this.childWorkspaceId = screenData.workspaceId
      this.childScreenComps = screenData.components
      this.childScreenLayers = screenData.layers
      this.childFiltersMap = _.keyBy(screenData.filter, 'id')
      this.showLoading = false
      this.isLoadedScreen = true
    },
    async render () {
      if (this.data) {
        this.dialogData = _.cloneDeep(this.data)
      }
      // await this.fetchData()

      const myConfig = _.cloneDeep(this.config)
      const acticveObj = {
        position: this.config.panel.position,
        radius: this.config.panel.radius
      }
      this.acticveObj = acticveObj
      this.titleObj = myConfig.title
    },
    resize ({ width, height }) {},
    destroy () {},
    showPopup () {
      this.show = true
    },
    getContainer () {
      // const div = document.querySelector('#prev .screen-view-wrap')
      // if (div) {
      //   return div
      // }
      return document.querySelector('body')
    },
    emitEvent (val) {
      if (val) {
        this.emit('panelOpen')
      } else {
        this.emit('panelClose')
      }
    },
    openPanel () {
      // console.log('打开面板');
      this.show = true
      this.fetchData()
    },
    closePanel () {
      this.show = false
    }
  }
};
</script>

<style lang="scss" scoped>
.content{
  min-height: 100px;
}
.popupBox {
  // position: relative;
  // width: 100%;
  // height: 100%;
}
.popupTitle {
  // display: flex;
  // justify-content: space-between;
  position: sticky;
  top: 0,;
  padding: 0 12px;
  text-align: right;
  background: rgba(255, 255, 255, 0);
  .title {
    display: inline-block;
  }
  .icon {
    position: absolute;
    line-height: unset;
  }
}
.van-popup {
  // border: 2px yellow solid;
  // background: var(--bockground);
}
.panelBox {
  overflow: hidden;
}
.van-popup--center.van-popup--round {
    border-radius: var(--radius);
}
.van-popup--bottom.van-popup--round {
    border-radius: var(--radius) var(--radius) 0 0;
}
.van-popup--top.van-popup--round {
    border-radius: 0 0 var(--radius) var(--radius);
}
.van-popup--left.van-popup--round {
    border-radius: 0 var(--radius) var(--radius) 0;
}
.van-popup--right.van-popup--round {
    border-radius: var(--radius) 0 0 var(--radius);
}
</style>
