<template>
  <div class="module-panel-container" :class="{ '--module-panel-heightauto': heightAuto, '--overflow-hidden': overflowHidden }" ref="container">
    <grid-layout
      ref="gridlayout"
      :style="{}"
      :layout.sync="layout"
      :col-num="24"
      :row-height="10"
      :is-draggable="false"
      :is-resizable="false"
      :is-mirrored="false"
      :vertical-compact="true"
      :margin="[5, 5]"
      :padding="[paddingX, paddingY]"
      :use-css-transforms="true">
      <grid-item
        v-for="(item, index) in layout"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        :key="item.i"
        :static="item.lock"
        :style="{
          zIndex: layout.length - index
        }">
        <ViewCompNode
          :workspaceId="$attrs.workspaceId"
          :mainScreenId="mainScreenId"
          :id="item.i"
          :type="item.type"
          :item-data="item.data"
          :zIndex="layout.length - index"
          :platform="'mobile'"
          v-on="$listeners"
          @seatom_setComConfig="handleSetComConfig" />
      </grid-item>
    </grid-layout>
  </div>
</template>

<script>
import { GridLayout, GridItem } from '@/lib/vue-grid-layout.common';
import { M_ZERO_HEIGHT, mobileZeroComs } from '@/common/constants'
import comp from '@/mixins/comp';
import quarkDom from 'hz-quark/dist/dom'
export default {
  inheritAttrs: true,
  name: 'ViewModulePanel', // 模块面板

  inject: ['getLayerTree', 'screenComs'],

  props: {
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },

  components: {
    GridLayout,
    GridItem
  },

  mixins: [comp],

  data () {
    return {
      overflowHidden: true,
      groupId: '',
      paddingX: 0,
      paddingY: 0,
      heightAuto: false,
      layoutComs: {}, // 当前模块下的组件集合
      layout: []
    }
  },

  computed: {
    layerTree () {
      return this.getLayerTree();
    }
  },

  watch: {
    layoutComs: {
      handler: function (val) {
        this.getLayout();
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    /**
     * 初始化方法【必选】
     * 会在 vue 生命周期的 mounted 阶段调用
     */
    init () {
      /** write your code */
      this.container = this.$refs.container;

      // 移动端高度自适应
      const { mobileSetting = { heightAuto: false, overflow: 'hidden' } } = this.config;
      this.overflowHidden = (!mobileSetting.overflow || mobileSetting.overflow === 'hidden')
      this.heightAuto = mobileSetting.heightAuto;
      if (mobileSetting.heightAuto && this.isView) {
        this.$nextTick(() => {
          this.handlerAutoHeight && this.handlerAutoHeight(this.$refs.gridlayout.$el);
        })
      }
    },
    /**
     * 渲染组件【必选】
     * 当组件被初始化后，组件渲染逻辑被调用。
     */
    render () {
      /** write your code */
      // 可通过 `this.config this.data` 读取配置和数据
      const { groupId, background = {}, border = {}, padding = {}, shadow = {}, radius = {} } = this.config;
      // 声明容器的样式
      let containerCssText = '';
      // 格式化背景样式
      containerCssText += quarkDom.formatBackground(background);
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border);

      this.paddingX = padding.left;
      this.paddingY = padding.top;

      // 阴影设置
      let shadowCssText = '';
      shadowCssText = this.formatShadow(shadow);
      containerCssText += shadowCssText;

      // 圆角设置
      let radiusCssText = '';
      radiusCssText = this.formatRadius(radius);
      containerCssText += radiusCssText;

      this.container.style.cssText = containerCssText;

      // 绑定的分组id
      this.groupId = groupId;
      this.getLayoutComs();
    },
    getLayoutComs () {
      const node = this.layerTree.getNodeById(this.groupId);
      if (!node) return;
      const coms = {};
      node.children.forEach(item => {
        let com;
        if (item.data.type === 'com') {
          com = this.screenComs()[item.data.id];
        } else {
          com = this.screenComs()[item.data.comId];
        }
        coms[com.id] = com;
      })
      this.layoutComs = coms;
    },
    getMobileHeight (comCfg) { // 获取移动端组件h
      if (!comCfg.show) return M_ZERO_HEIGHT;
      if (mobileZeroComs.includes(comCfg.comName)) return M_ZERO_HEIGHT;
      return comCfg.attr.h
    },
    getCompData (comCfg) { // 获取模块面板组件里的数据
      const { dataConfig: { dataResponse: { sourceType = 'static' } } } = comCfg
      return ['inherit', 'dialog'].includes(sourceType) ? this.data : []
    },
    getLayout () {
      const node = this.layerTree.getNodeById(this.groupId);
      if (!node) return;
      const layout = [];
      const nodes = (node.children || []).filter(item => !item.data.id.startsWith('interaction-container-modulepanel'));
      nodes.forEach(item => { // 按照图层的顺序
        let com;
        if (item.data.type === 'com') {
          com = this.layoutComs[item.data.id];
        } else {
          com = this.layoutComs[item.data.comId];
        }
        if (com) {
          layout.push({
            x: com.attr.x,
            y: com.attr.y,
            w: com.attr.w,
            h: this.getMobileHeight(com),
            i: com.id,
            lock: com.attr.lock,
            type: com.type,
            data: this.getCompData(com)
          })
        }
      })
      this.layout = layout;
    },
    handleSetComConfig (id, keyValPairs = []) { // 移动端设置组件config
      const com = this.layoutComs[id]
      if (com) {
        keyValPairs.forEach(pair => {
          _.set(com, pair.key, pair.value);
        })
      }
      this.$emit('seatom_setComConfig', id, keyValPairs)
    },
    /**
     * 自适应尺寸【可选】
     * 当组件被拖拽、缩放时被调用。
     */
    resize ({ width, height }) {
      /** write your code */
    },
    /**
     * 清理组件，只清空画布，组件实例保留【可选】
     * 当组件被清理时调用
     */
    clear () {
      /** write your code */
    },
    /**
     * 销毁组件，彻底销毁，组件实例不可用【可选】
     * 组件被销毁时调用，会在 vue 组件声明周期勾子 beforeDestroy 里调用
     */
    destroy () {
      /** write your code */
    },
    /**
     * 组件内置，不可在此处覆盖的方法
     * emit(eventName, data)：抛出事件。开发者可以调用，勿改勿覆盖
     * on(compId, eventName, callback)：此组件监听来自其他组件的事件。组件内部调用，勿改勿覆盖
     * off(compId, eventName, callback)：移除监听。组件内部调用，勿改勿覆盖
     */
    formatShadow (shadow = {}) {
      if (shadow.show) {
        return `box-shadow: ${shadow.offsetX}px ${shadow.offsetY}px ${shadow.blur}px ${shadow.color};`
      }
      return ''
    },
    formatRadius (radius) {
      if (radius.show) {
        return `border-radius: ${radius.val}px;`
      }
      return ''
    }
  }
};
</script>

<style lang="scss" scoped>
/**
 在此处添加组件 css 样式（用 scoped 声明隔离其他组件样式，支持纯css、scss、less。）
 注：组件根元素的宽高系统自动设定，不需在此写死宽高，此处宽高用百分比适配。
 */
.module-panel-container {
  &.--module-panel-heightauto {
    height: auto !important;
  }
  &.--overflow-hidden {
    overflow: hidden;
  }
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
