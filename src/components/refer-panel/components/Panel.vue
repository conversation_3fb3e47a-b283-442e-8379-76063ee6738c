<template>
  <div class="screen-view-wrap_box">
    <div class="screen-view-wrap" :style="pitchStyle">
      <template v-if="platform === 'pc'">
        <template v-for="(item, index) in pageLayers">
          <template v-if="item.type === 'com'">
            <ViewCompNode
              :key="item.id"
              :workspaceId="workspaceId"
              :id="item.id"
              :type="item.type"
              :item-data="itemData"
              :zIndex="screenLayers.length - index"
              :platform="platform"
              :mainScreenId="mainScreenId"
              @compLoaded="handleCompLoaded"
              v-on="$listeners" />
          </template>
          <template v-else>
            <ViewGroupComp
              :key="item.id"
              :workspaceId="workspaceId"
              :id="item.id"
              :pageLayers="item.children"
              :mainScreenId="screenInfo.id"
              :item-data="itemData"
              :zIndex="screenLayers.length - index"
              :platform="platform"
              @compLoaded="handleCompLoaded"
              v-on="$listeners" />
          </template>
        </template>
      </template>
      <template v-else>
        <grid-layout
          ref="gridlayout"
          v-if="!!pageLayout[0]"
          :style="{
            width: screenStyle.width + 'px'
          }"
          :layout.sync="pageLayout[0].layout"
          :col-num="24"
          :row-height="10"
          :is-draggable="false"
          :is-resizable="false"
          :is-mirrored="false"
          :vertical-compact="true"
          :margin="[5, 5]"
          :use-css-transforms="true"
          >
          <grid-item
            v-for="(item, index) in pageLayout[0].layout"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
            :key="item.id"
            :style="{
              zIndex: pageLayout[0].layout.length - index
            }">
            <ViewCompNode
              :workspaceId="workspaceId"
              :id="item.id"
              :type="item.type"
              :item-data="itemData"
              :zIndex="pageLayout[0].layout.length - index"
              :platform="platform"
              :mainScreenId="screenInfo.id"
              v-on="listeners"
              @compLoaded="handleCompLoaded"
              @seatom_setComConfig="handleSetComConfig" />
          </grid-item>
        </grid-layout>
      </template>
    </div>
  </div>
</template>

<script>
import { M_ZERO_HEIGHT, mobileZeroComs } from '@/common/constants'
import VueGridLayout from '@/lib/vue-grid-layout.common'
import { uuid, replaceUrl, encryptTilemapPath, fastLoadedImg } from '@/utils/base'
import Tree from '@/lib/Tree'
import * as PARTICLES_THEME from '@/common/particlesTheme'

export default {
  name: 'Panel',

  components: {
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem
  },

  props: {
    showScroll: { // 是否显示滚动条
      type: Boolean,
      default: false
    },
    itemData: {
      type: Array,
      default: () => []
    },
    screenStyle: {
      type: Object,
      default: () => ({})
    },
    screenInfo: {
      type: Object,
      default: () => ({})
    },
    sceneConfig: {
      type: Array,
      default: () => ([])
    },
    screenFilters: {
      type: Object,
      default: () => ({})
    },
    screenComs: {
      type: Object,
      default: () => {}
    },
    screenLayers: {
      type: Array,
      default: () => []
    },
    workspaceId: {
      type: [Number, String],
      default: 0
    },
    platform: {
      type: String,
      default: 'pc'
    },
    isView: {
      type: Boolean,
      default: false
    },
    heightAuto: {
      type: Boolean,
      default: false
    },
    mainScreenId: {
      type: Number,
      required: true
    }
  },

  inject: ['callbackFatherManager', 'permissionFatherMap'],
  provide: function () {
    return {
      getLayerTree: () => this.layerTree, // 通过函数实现响应式
      filtersMap: () => this.screenFilters,
      screenComs: () => this.screenComs,
      callbackManager: () => this.callbackManager,
      permissionMap: this.permissionFatherMap
    }
  },
  data () {
    return {
      loadCount: 0,
      ParticlesKey: uuid('Particles'),
      layerTree: new Tree(),
      pageLayout: [],
      pageId: null,
      sceneId: null,
      parentSize: {
        width: 0,
        height: 0
      }
    }
  },

  computed: {
    pitchStyle () {
      const { screenStyle, parentSize, screenInfo } = this
      if (_.isEmpty(screenStyle)) return screenStyle
      const {
        width,
        height,
        backgroundColor,
        backgroundImage,
        backgroundRepeat,
        globalFilterParams
      } = screenStyle
      const ratio = { w: parentSize.width / width, h: parentSize.height / height }
      const style = {
        width: width + 'px',
        height: height + 'px'
      }

      const setBackground = (image, repeat = 'fill') => {
        let cssText = ''
        if (!image) return cssText
        const zipable = ['png', 'jpg', 'jpeg']; // 支持压缩的图片类型
        const ext = image.split('.').pop();
        let attr = {
          w: width,
          h: height
        };
        if (this.platform === 'mobile' && !screenInfo.isDynamicScreen) {
          attr = {
            w: width
          };
        }
        if (zipable.includes(ext) && image.indexOf('/public/') > -1) {
          image = replaceUrl(image, 'imageServer');
          const path = image.split('/public/')[1];
          const encryptUrl = '/' + encryptTilemapPath(path) + '.' + ext;
          const fastPath = fastLoadedImg(encryptUrl, 'string', attr);
          const fullPath = image.split('/public/')[0] + fastPath;
          image = fullPath;
        }
        switch (repeat) {
          case 'fill':
            cssText += ` url(${image}) left top/100% 100% no-repeat`
            break
          case 'contain':
            cssText += ` url(${image}) center/contain no-repeat`
            break
          case 'cover':
            cssText += ` url(${image}) left top/cover no-repeat`
            break
          case 'repeat':
            cssText += ` url(${image}) left top/auto repeat`
            break
          default:
            break
        }
        return cssText
      }
      const scene = this.sceneConfig.find(item => {
        return item.pageList.findIndex(page => page.pageId === this.pageId) > -1
      })
      if (scene) {
        let page = {}
        if (this.pageId) {
          page = scene.pageList.find(p => p.pageId === this.pageId)
        }
        const target = [{ ...page, backgroundImage: page.pageBackground }, {
          ...scene,
          backgroundImage: scene.sceneBackground
        }, { backgroundColor, backgroundImage, backgroundRepeat }].find(item => {
          return item.backgroundColor || item.backgroundImage
        })
        if (target) {
          if (target.backgroundImage) {
            style.background = `${setBackground(target.backgroundImage, target.backgroundRepeat)}`
          } else {
            style.background = `${target.backgroundColor}`
          }
        } else {
          if (backgroundImage) {
            style.background = `${setBackground(backgroundImage, backgroundRepeat)}`
          } else {
            style.background = `${backgroundColor}`
          }
        }
      }

      if (globalFilterParams.enable) {
        const { hue, saturate, brightness, contrast, opacity, grayscale } = globalFilterParams
        style.filter = `
          hue-rotate(${hue}deg)
          saturate(${saturate}%)
          brightness(${brightness}%)
          contrast(${contrast}%)
          opacity(${opacity}%)
          grayscale(${grayscale}%)
        `
      }
      if (this.platform === 'mobile') { // 移动端
        // 分页布局
        // 移动端容器组件
        style.width = '100%'
        style.height = '100%'
        style.overflow = 'hidden';
        if (!this.heightAuto) { // 自适应高度关闭 新版动态面板出滚动条
          style.overflowY = 'auto';
        }
        style.height = parentSize.height / ratio.w + 'px'
        style.position = 'unset'
        // 容器组件 删除最小高度，防止出现滚动条
        delete style.minHeight;
        style.height = '100%';
      }
      return style
    },
    computedParticlesConfig () {
      const custom = this.screenConfig.backgroundParticlesCustom
      const type = this.screenConfig.backgroundParticlesType
      const temp = PARTICLES_THEME[this.screenConfig.backgroundParticlesType]
      if (type === 'STYLE_NONE') return {}
      if (!custom) return temp
      if (_.isEmpty(custom)) {
        return {}
      }
      temp.particles.number.value = custom.number
      temp.particles.move.speed.min = custom.speed[0]
      temp.particles.move.speed.max = custom.speed[1]
      temp.particles.opacity.value.min = custom.opacity[0]
      temp.particles.opacity.value.max = custom.opacity[1]
      temp.particles.size.value.min = custom.size[0]
      temp.particles.size.value.max = custom.size[1]
      type === 'STYLE_METEOR'
        ? temp.particles.stroke.color.value = [...new Set(custom.color.map(item => item.value))]
        : temp.particles.color.value = [...new Set(custom.color.map(item => item.value))]
      return temp
    },
    pageList () {
      const list = []
      this.sceneConfig.forEach(scene => {
        list.push(...scene.pageList)
      })
      return list
    },
    pageLayers () {
      let layers = []
      if (this.pageId) {
        layers = this.screenLayers.filter(layer => layer.pageId === this.pageId || (layer.sceneId === this.sceneId && !layer.pageId))
      } else {
        layers = this.screenLayers.filter(layer => layer.sceneId === this.sceneId && !layer.pageId)
      }
      return layers
    },
    compCount () {
      const showLayers = this.pageLayers.filter(l => {
        return this.screenComs[l.id] && this.screenComs[l.id].show && this.screenComs[l.id].comType !== 'interaction-container-modulepanel'
      })
      return showLayers.length
    },
    listeners () {
      const listeners = this.$listeners
      delete listeners.seatom_setComConfig
      return listeners
    }
  },

  watch: {
    // this.initCallbackManager()
    screenLayers: {
      handler: function (val) {
        this.initLayers()
        if (this.platform === 'mobile') {
          if (this.screenType === 'common') { // 自定义布局 刷新layout
            this.layout = this.transToLayout(this.layout)
          } else { // 新版动态面板或多页布局 刷新layout
            this.getLayout();
          }
        }
      },
      deep: true
    }
  },
  mounted () {
    window.addEventListener('resize', e => {
      this.resize({
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight
      })
    })
    this.initParentSize()
  },
  methods: {
    setPage (sceneId, pageId) {
      this.sceneId = sceneId
      this.pageId = pageId
    },
    initLayers () {
      this.layerTree = new Tree({ id: 'root', children: this.screenLayers })
    },
    initCallbackManager () {
      const { screenComs } = this
      if (!this.screenComs) return

      _.values(screenComs).forEach(com => {
        if (com.interactionConfig && com.interactionConfig.callbackParams) {
          com.interactionConfig.callbackParams.forEach(cb => {
            this.callbackFatherManager().addCallbackKey(cb.variableName)
          })
        }
      })
      this.callbackManager = this.callbackFatherManager()
    },
    handleSetComConfig (id, keyValPairs = []) { // 移动端设置组件config
      const com = this.screenComs[id]
      if (com) {
        keyValPairs.forEach(pair => {
          _.set(com, pair.key, pair.value);
        })
        if (this.screenType === 'common') { // 自定义布局 刷新layout
          this.layout = this.transToLayout(this.layout)
        } else { // 新版动态面板或多页布局 刷新layout
          this.getLayout();
        }
      }
    },
    getMobileHeight (comCfg) { // 获取移动端组件h
      if (!comCfg.show) return M_ZERO_HEIGHT;
      if (mobileZeroComs.includes(comCfg.comName)) return M_ZERO_HEIGHT;
      return comCfg.attr.h
    },
    initParentSize () {
      this.parentSize.width = document.documentElement.clientWidth
      this.parentSize.height = document.documentElement.clientHeight
    },
    resize ({ width, height }) {
      if (typeof width !== 'undefined') {
        this.parentSize.width = Number.parseInt(width, 10)
      }
      if (typeof height !== 'undefined') {
        this.parentSize.height = Number.parseInt(height, 10)
      }
    },
    getLayout () { // 获取移动端布局layout
      if (this.platform !== 'mobile') return;
      // 新版动态面板
      let layout = []
      if (this.sceneId) {
        let layers = this.screenLayers.filter(layer => layer.sceneId === this.sceneId);
        layers = layers.filter(item => !item.id.startsWith('interaction-container-modulepanel'));
        layout = this.transToLayout(layers);
      }
      this.pageLayout = [{ layout }]
      if (!layout.length) this.handleCompLoaded()
    },
    transToLayout (coms) { // 将组件集合转换成vue-grid数据
      const layout = coms.map((item) => {
        let com;
        if (item.type === 'com') {
          com = this.getComDataById(item.id)
        } else {
          com = this.getComDataById(item.comId)
        }
        const attr = com.attr
        const obj = {
          x: attr.x,
          y: attr.y,
          w: attr.w,
          h: this.getMobileHeight(com),
          i: com.id,
          id: com.id,
          type: com.type
        }
        return obj
      })
      layout.sort((a, b) => { return (a.y + a.h) - (b.y + b.h) }); // 根据底部高度排序
      return layout
    },
    getComDataById (id) {
      if (_.has(this.screenComs, id)) {
        return this.screenComs[id]
      } else {
        console.warn(`获取不到 id 为 ${id} 的组件数据！`)
      }
    },
    domObserver () { // 移动端监听grid组件高度，面板类组件高度自适应
      this.$nextTick(() => {
        const comp = this.$refs.gridlayout;
        if (comp) {
          const ob = new ResizeObserver(_.throttle(enties => {
            enties.forEach(item => {
              const contentRect = item.contentRect;
              this.$emit('seatom_setComConfig', this.$parent.id, [{ key: 'attr.h', value: _.ceil((contentRect.height + 5) / 15, 2) }]);
            })
          }, 100))
          ob.observe(comp.$el);
          this.$once('hook:beforeDestroy', () => {
            ob.unobserve(comp.$el)
          })
        }
      })
    },
    switchScene (params) { // 切换场景
      const pageName = params.pageName || (params.pageData && params.pageData.pageName)
      if (pageName) {
        const page = this.pageList.find(item => item.pageName === pageName)
        if (page) {
          const scene = this.sceneConfig.find(item => {
            return item.pageList.findIndex(v => v.pageId === page.pageId) > -1
          })
          if (scene) {
            this.sceneId = scene.sceneId
          }
          if (page) {
            this.pageId = page.pageId
          }
          this.getLayout();
          return true
        }
      }
    },
    initParentStyle () {
      if (_.isEmpty(this.screenStyle)) return
      let { scaleType } = this.screenStyle
      let style = {}
      if (this.platform === 'mobile') {
        scaleType = 'full_width'
      }
      switch (scaleType) {
        case 'full_screen':
        case 'full_width':
        case 'full_height':
        case 'no_scale':
          style = {
            overflowX: 'hidden',
            overflowY: 'visible'
          }
          break
        case 'full_height_scroll':
          style = {
            overflow: 'auto'
          }
          break
      }

      if (this.$el && this.$el.parentNode) {
        const parentNode = this.$el.parentNode
        Object.keys(style).forEach(attr => {
          parentNode.style[attr] = style[attr]
        })
      }
    },
    handleCompLoaded () {
      this.loadCount++
      if (this.loadCount >= this.compCount) {
        this.$emit('loaded')
        this.$emit('mounted')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-view-wrap_box {
  position: relative;
  width: 100%;
  height: 100%;
}
.screen-view-wrap {
  // position: absolute;
  // left: 0;
  // top: 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transform-origin: left top;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
</style>
