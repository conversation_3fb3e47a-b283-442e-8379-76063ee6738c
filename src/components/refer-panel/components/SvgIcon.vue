<template>
    <svg
      class="hz-icon"
      aria-hidden="true"
    >
      <use :xlink:href="IconName"></use>
    </svg>
  </template>
<script>
export default {
  name: 'hz-icon',
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  computed: {
    IconName () {
      return `#icon-${this.name}`;
    }
  },
  methods: {

  }
};
</script>
  <style scoped>
  .hz-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
  </style>
