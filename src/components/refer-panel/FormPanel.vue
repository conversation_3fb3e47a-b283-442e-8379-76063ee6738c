<template>
  <div class="form-group" ref="container">
    <template v-for="item in screenList">
      <div :key="item.id" class="screen" :style="validateStyle">
        <FormViewScreen
          ref="formView"
          v-if="!showLoading"
          :item-data="formData"
          :screen-style="screenConfig"
          :screen-coms="childScreenComps"
          :screen-layers="childScreenLayers"
          :workspace-id="childWorkspaceId"
          :screen-filters="childFiltersMap"
          :platform="screenInfo.type"
          :mainScreenId="mainScreenId"
          v-on="$listeners"
          isContainer />
      </div>
    </template>
    <seatom-loading v-if="showLoading"/>
  </div>
</template>

<script>
import comp from '@/mixins/comp'
import { updateScreenParentId } from '@/api/screen'
import { findImageUrl, replaceUrl } from '@/utils/base'
import { addImportComponents } from '@/utils/systemImport'
import quarkDom from 'hz-quark/dist/dom'
export default {
  name: 'FormPanel', // form表单面板

  props: {
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },

  inject: ['callbackManager'],

  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager(),
      formPanel: this
    }
  },

  mixins: [comp],

  data () {
    return {
      container: null,
      form: {},
      screenInfo: {},
      childScreenComps: {},
      childScreenLayers: [],
      screenConfig: {},
      childFiltersMap: {},
      childScreenStyle: {},
      formData: [],
      childWorkspaceId: 0,
      showLoading: false
    }
  },

  computed: {
    screenList () {
      return this.config.screens || []
    },
    validateStyle () {
      const { config: { validateStyle: { fontStyle: { color, fontFamily, fontSize, fontWeight } } } } = this
      return {
        '--validate-color': color,
        '--validate-fontFamily': fontFamily,
        '--validate-fontSize': fontSize + 'px',
        '--validate-fontWeight': fontWeight
      }
    }
  },

  methods: {
    // 获取当前大屏信息
    async fetchData () {
      this.showLoading = true
      const screenData = (await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id }))
      Object.values(screenData).forEach(item => {
        findImageUrl(item, replaceUrl)
      })

      // 导入组件
      addImportComponents(screenData.components)

      this.screenInfo = screenData;

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }

      this.childWorkspaceId = screenData.workspaceId
      this.childScreenComps = screenData.components
      this.screenConfig = screenData.config
      this.childScreenLayers = screenData.layers
      this.childFiltersMap = _.keyBy(screenData.filter, 'id')
      this.showLoading = false;
    },
    init () {
      /** write your code */
      this.container = this.$refs.container
    },
    async render () {
      /** write your code */
      // 可通过 `this.config this.data` 读取配置和数据
      const {
        background = {},
        border = {}
      } = this.config

      await this.fetchData()

      if (this.data) {
        this.formData = _.cloneDeep(this.data)
      }

      let containerCssText = ''

      // containerCssText += this.formatCSS({
      //   // width: this.attr.w + 'px',

      // })
      // 格式化背景样式
      containerCssText += quarkDom.formatBackground(background)
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border)
      this.container.style.cssText = containerCssText
    },
    resize ({ width, height }) {
      /** write your code */
    },
    clear () {
      /** write your code */
    },
    destroy () {
      /** write your code */
    },
    formatCSS (arg) {
      return (
        Object.keys(arg)
          .map((key) => {
            return key + ':' + arg[key]
          })
          .join(';') + ';'
      )
    },
    submit () {
      // console.log(this.$refs['formView'][0].submitForm())
      this.$refs.formView[0].submitForm('form')
    }
  }

}
</script>

<style lang="scss" scoped>

</style>
