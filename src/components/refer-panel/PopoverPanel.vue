<template>
  <div class="popover-container" ref="container" v-show="dialogBoxVisible">
    <el-popover
      v-model="dialogVisible"
      ref="dialog"
      placement="bottom"
      @show="dialogOpen"
      @hide="dialogClose"
    >
      <span class="popoverButton" @click="closePopover">
        <hz-icon name="close"></hz-icon>
      </span>
      <template v-for="item in screenList">
        <div :key="item.id" class="screen">
          <PitchViewScreen
            v-if="isLoadedScreen && !showLoading"
            :showScroll="showScroll"
            :item-data="dialogData"
            :screen-style="screenConfig"
            :screen-coms="childScreenComps"
            :screen-layers="childScreenLayers"
            :workspace-id="childWorkspaceId"
            :screen-filters="childFiltersMap"
            :platform="screenInfo.type"
            :isView="isView"
            :mainScreenId="mainScreenId"
            v-on="$listeners" />
        </div>
      </template>
      <seatom-loading v-if="showLoading" />
    </el-popover>
  </div>
</template>

<script>
import comp from '@/mixins/comp';
import { updateScreenParentId } from '@/api/screen';
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'
import quarkDom from 'hz-quark/dist/dom'

export default {
  name: 'PopoverPanel', // 邻位面板

  inject: ['callbackManager'],

  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager()
    };
  },

  mixins: [comp],

  data () {
    return {
      active: null,
      show: true,
      showBox: false,
      dialogWidth: '',
      dialogData: [],
      activeStyle: {},
      screenInfo: {},
      childScreenComps: {},
      childScreenLayers: [],
      childWorkspaceId: 0,
      childFiltersMap: {},
      childScreenStyle: {},
      showLoading: false,
      screenConfig: {},
      screenType: 'common',
      isLoadedScreen: false,
      clickDomStyle: {},
      offsetX: 0,
      offsetY: 0
    };
  },

  props: {
    isView: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },

  computed: {
    screenList () {
      return this.config.screens || [];
    },
    dialogVisible: {
      get () {
        return !this.isView || this.show;
      },
      set (val) {
        this.show = val;
      }
    },
    dialogBoxVisible: {
      get () {
        return !this.isView || this.showBox;
      },
      set (val) {
        this.showBox = val;
      }
    },
    showScroll () {
      const {
        dialog: { isShowScroll }
      } = this.config;
      return isShowScroll;
    }
  },
  watch: {
    dialogBoxVisible (val) {
      if (val) {
        this.dialogOpen();
      }
    }
  },
  mounted () {
    setTimeout(() => {
      // tip： 同个大屏内多个弹窗时会同时出现，需要把隐藏
      this.dialogVisible = false;
      // 此处绑定点击事件以便获取之后点击的dom属性
      if (document.querySelector('.screen-view-wrap')) {
        document
          .querySelector('.screen-view-wrap')
          .addEventListener('click', this.dialogPosition);
      }
      this.clickDomStyle = {};
    }, 0);
  },
  methods: {
    // 获取当前的大屏信息
    async fetchData () {
      if (!this.screenList.length) {
        this.showLoading = false
        return;
      }

      if (this.showLoading || this.isLoadedScreen) {
        return
      }

      this.showLoading = true;

      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id })

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData;

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }
      // 检查修复结束

      this.childWorkspaceId = screenData.workspaceId;
      this.childScreenComps = screenData.components;
      this.screenConfig = screenData.config;
      this.childScreenLayers = screenData.layers;
      this.childFiltersMap = _.keyBy(screenData.filter, 'id');
      this.showLoading = false
      this.isLoadedScreen = true;
    },
    init () {
      this.container = this.$refs.container;
      this.dialog = this.$refs.dialog.$el.firstChild;
      this.testDom = this.$refs.container.parentNode;
    },
    async render () {
      const { screens = [], border = {} } = this.config;

      if (!Object.keys(this.config).length) {
        return;
      }

      // await this.fetchData();

      if (this.data && this.data.length) {
        this.dialogData = _.cloneDeep(this.data);
      }

      this.showLoading = false;

      const screenList = screens;

      if (screenList.length) {
        this.active = screenList[0].id;
      }

      let containerCssText = '';
      containerCssText += this.formatCSS({
        transform: this.isView ? 'none' : null // && !this.isCenter ? "none" : null,
      });

      // 声明容器的样式
      // 格式化背景样式
      // containerCssText += quarkDom.formatBackground(background);
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border);
      this.dialog.style.cssText = containerCssText;
    },
    dialogPosition (event) {
      this.tempEventTarget = event.target;
    },
    // 通过点击事件触发的dom进行属性解析
    realPosition (dom) {
      // const domClassList = dom.className.split(' ');
      if (dom.className.animVal === 'hz-icon') {
        return;
      }
      if (
        (typeof dom === 'object' && dom.getAttribute('popover') === 'true') ||
        (typeof dom.className !== 'object' &&
          dom.className.includes('view-comp-node'))
        //  || dom.className.includes('gridRow')
      ) {
        // 窗口滚动边距
        let scrollTop = 0;
        let scrollLeft = 0;
        if (document.querySelector('.screen-container')) {
          scrollTop = document.querySelector('.screen-container').scrollTop;
          scrollLeft = document.querySelector('.screen-container').scrollLeft;
        }
        let screenTransform, transformX, transformY; //, scale;
        // 窗口缩放比例
        if (document.querySelector('.screen-view-wrap')) {
          screenTransform =
            document.querySelector('.screen-view-wrap').style.transform;
        }
        if (screenTransform !== '') {
          if (screenTransform.includes(',')) {
            // 横纵坐标缩放不一样
            let splitTransform = screenTransform.split('(');
            splitTransform = splitTransform[1].split(',');
            transformX = +splitTransform[0];
            transformY = +splitTransform[1].slice(
              0,
              splitTransform[1].length - 1
            );
          } else {
            const splitTransform = screenTransform.split('(');
            transformX = +splitTransform[1].slice(
              0,
              splitTransform[1].length - 1
            );
            transformY = +splitTransform[1].slice(
              0,
              splitTransform[1].length - 1
            );
          }
        }

        if (
          dom.style.left !== '' &&
          dom.style.top !== '' &&
          dom.style.width !== '' &&
          dom.style.height !== ''
        ) {
          // 组件最外层定位弹窗
          this.clickDomStyle = { ...dom.style }; // .getBoundingClientRect();

          let top = this.clickDomStyle.top;
          let left = this.clickDomStyle.left;

          top = top.split('p');
          left = left.split('p');
          // 如果没有缩放且点击的组件不贴着页面顶部
          // 这一步
          if (screenTransform === '' && dom.style.top !== '0px') {
            this.clickDomStyle.top = +top[0];
            this.clickDomStyle.left = +left[0];
          }
          // 显示弹窗组件传入的偏移量
          this.clickDomStyle.top = +top[0] + this.offsetY;
          this.clickDomStyle.left = +left[0] + this.offsetX;
          this.clickDomStyle.scaleX = transformX;
          this.clickDomStyle.scaleY = transformY;
        } else {
          // 组件内部定位弹窗

          if (screenTransform !== '') {
            const domStyle = dom.getBoundingClientRect();
            let boxTop = 0;
            let boxLeft = 0;
            if (document.querySelector('.screen-view-wrap')) {
              boxTop = document.querySelector('.screen-view-wrap').style.top;
              boxLeft = document.querySelector('.screen-view-wrap').style.left;
              boxTop = boxTop.split('p');
              boxTop = +boxTop[0];
              boxLeft = boxLeft.split('p');
              boxLeft = +boxLeft[0];
            }
            scrollTop = scrollTop + +(boxTop / transformY).toFixed(2);
            scrollLeft = scrollLeft + +(boxLeft / transformX).toFixed(2);
            this.clickDomStyle = {
              width: +(domStyle.width / transformX).toFixed(2),
              height: +(domStyle.height / transformY).toFixed(2),
              scaleX: transformX,
              scaleY: transformY
            };
            this.clickDomStyle.top =
              +(domStyle.top / transformY).toFixed(2) - scrollTop;
            this.clickDomStyle.left =
              +(domStyle.left / transformX).toFixed(2) - scrollLeft;
            // 显示弹窗组件传入的偏移量
            this.clickDomStyle.top += this.offsetY;
            this.clickDomStyle.left += this.offsetX;
          } else {
            this.clickDomStyle = dom.getBoundingClientRect();
            // 显示弹窗组件传入的偏移量
            this.clickDomStyle.top += this.offsetY;
            this.clickDomStyle.left += this.offsetX;
          }
        }
        const position = this.autoPosition();
        this.seatom_setParentStyle({
          top: this.isView ? position.top : null,
          left: this.isView ? position.left : null
        })
        // this.clickDomStyle.layer = 0;
      } else if (
        dom.className.includes('el-dialog__wrapper') ||
        dom.className.includes('el-dialog__close') ||
        dom.className === 'screen-view-wrap' ||
        dom.className === 'popoverButton'
      ) {

      } else {
        this.realPosition(dom.parentNode);
      }
    },
    autoPosition () {
      const { dialog = {} } = this.config;
      const { showPosition, showOffset } = dialog;
      let top = '';
      let left = '';
      // 找出这个弹窗面板的宽高
      const containerStyle = this.container.style;
      const containerWidth = containerStyle.width.split('p');
      const containerHeight = containerStyle.height.split('p');
      // 对点击的dom元素进行判定 找出该点击dom的宽高left top
      let clickDomX =
        typeof this.clickDomStyle.left === 'string'
          ? this.clickDomStyle.left.split('p')
          : this.clickDomStyle.left;
      let clickDomY =
        typeof this.clickDomStyle.top === 'string'
          ? this.clickDomStyle.top.split('p')
          : this.clickDomStyle.top;
      let clickDomHeight =
        typeof this.clickDomStyle.height === 'string'
          ? this.clickDomStyle.height.split('p')
          : this.clickDomStyle.height;
      let clickDomWidth =
        typeof this.clickDomStyle.width === 'string'
          ? this.clickDomStyle.width.split('p')
          : this.clickDomStyle.width;
      // 对面板的实际宽高进行转化
      containerWidth[0] = +containerWidth[0];
      containerHeight[0] = +containerHeight[0];
      // 对点击dom的left top 宽高进行转化
      clickDomX = typeof clickDomX === 'object' ? +clickDomX[0] : clickDomX;
      clickDomY = typeof clickDomY === 'object' ? +clickDomY[0] : clickDomY;
      clickDomWidth =
        typeof clickDomWidth === 'object' ? +clickDomWidth[0] : clickDomWidth;
      clickDomHeight =
        typeof clickDomHeight === 'object'
          ? +clickDomHeight[0]
          : clickDomHeight;
      // 获得其点击dom的父级宽高以及定位
      const windowScreenStyle =
        document.querySelector('.screen-view-wrap').style; // window.screen;
      const windowScreen = {};
      const windowWidth = windowScreenStyle.width.split('p');
      windowScreen.width = +windowWidth[0];
      const windowHeight = windowScreenStyle.height.split('p');
      windowScreen.height = +windowHeight[0];
      switch (showPosition) {
        case 'top': {
          if (containerHeight[0] + showOffset.top <= clickDomY) {
            top = clickDomY - containerHeight[0] + showOffset.top + 'px';
            if (
              windowScreen.width - clickDomX <
              containerWidth[0] + showOffset.left
            ) {
              left =
                windowScreen.width - containerWidth[0] + showOffset.left + 'px';
            } else {
              left = clickDomX + showOffset.left + 'px';
            }
          } else {
            top = this.attr.y + 'px';
            left = this.attr.x + 'px';
          }

          break;
        }
        case 'bottom': {
          if (
            windowScreen.height - (clickDomY + clickDomHeight) >=
            containerHeight[0] + showOffset.top
          ) {
            top = clickDomY + clickDomHeight + showOffset.top + 'px';
            if (
              windowScreen.width - clickDomX <
              containerWidth[0] + showOffset.left
            ) {
              left =
                windowScreen.width - containerWidth[0] + showOffset.left + 'px';
            } else {
              left = clickDomX + showOffset.left + 'px';
            }
          } else {
            top = this.attr.y + 'px';
            left = this.attr.x + 'px';
          }
          break;
        }
        case 'left': {
          if (containerWidth[0] + showOffset.left <= clickDomX) {
            if (
              windowScreen.height - clickDomY <
              containerHeight[0] + showOffset.top
            ) {
              top =
                windowScreen.height -
                containerHeight[0] +
                showOffset.top +
                'px';
            } else {
              top = clickDomY + showOffset.top + 'px';
            }
            left = clickDomX - containerWidth[0] + showOffset.left + 'px';
          } else {
            top = this.attr.y + 'px';
            left = this.attr.x + 'px';
          }
          break;
        }
        case 'right': {
          top = clickDomY + showOffset.top + 'px';
          left = clickDomX + clickDomWidth + showOffset.left + 'px';
          if (
            windowScreen.width - (clickDomX + clickDomWidth) >=
            containerWidth[0] + showOffset.left
          ) {
            if (
              windowScreen.height - clickDomY <
              containerHeight[0] + showOffset.top
            ) {
              top =
                windowScreen.height -
                containerHeight[0] +
                showOffset.top +
                'px';
            } else {
              top = clickDomY + showOffset.top + 'px';
            }
            left = clickDomX + clickDomWidth + showOffset.left + 'px';
          } else {
            top = this.attr.y + 'px';
            left = this.attr.x + 'px';
          }
          break;
        }
        case 'auto': {
          // 右
          if (
            windowScreen.width /* / transformX */ -
              (clickDomX + clickDomWidth) >=
            containerWidth[0] + showOffset.left
          ) {
            if (
              windowScreen.height /* / transformY */ - clickDomY <
              containerHeight[0] + showOffset.top
            ) {
              top =
                windowScreen.height - // / transformY -
                containerHeight[0] +
                showOffset.top +
                'px';
            } else {
              top = clickDomY + showOffset.top + 'px';
            }
            left = clickDomX + clickDomWidth + showOffset.left + 'px';
            break;
          } else if (
            // 下
            windowScreen.height /* / transformY */ -
              (clickDomY + clickDomHeight) >=
            containerHeight[0] + showOffset.top
          ) {
            top = clickDomY + clickDomHeight + showOffset.top + 'px';
            if (
              windowScreen.width /* / transformX */ - clickDomX <
              containerWidth[0] + showOffset.left
            ) {
              left =
                windowScreen.width /* / transformX */ -
                containerWidth[0] +
                showOffset.left +
                'px';
            } else {
              left = clickDomX + showOffset.left + 'px';
            }
            break;
          } else if (containerWidth[0] + showOffset.left <= clickDomX) {
            // 左
            if (
              windowScreen.height /* / transformY */ - clickDomY <
              containerHeight[0] + showOffset.top
            ) {
              top =
                windowScreen.height /* / transformY */ -
                containerHeight[0] +
                showOffset.top +
                'px';
            } else {
              top = clickDomY + showOffset.top + 'px';
            }
            left = clickDomX - containerWidth[0] + showOffset.left + 'px';
            break;
          } else if (containerHeight[0] + showOffset.top <= clickDomY) {
            // 左
            top = clickDomY - containerHeight[0] + showOffset.top + 'px';
            left = clickDomX + showOffset.left + 'px';
            break;
          } else {
            // 初始位置
            top = this.attr.y + 'px';
            left = this.attr.x + 'px';
            break;
          }
        }
        case 'map': {
          // 窗口滚动边距
          let scrollTop = 0;
          let scrollLeft = 0;
          if (document.querySelector('.screen-container')) {
            scrollTop = document.querySelector('.screen-container').scrollTop;
            scrollLeft = document.querySelector('.screen-container').scrollLeft;
          }
          top =
            clickDomY + this.mapY - containerHeight[0] + scrollTop - 20 + 'px';
          left =
            clickDomX + this.mapX - containerWidth[0] / 2 + scrollLeft + 'px';
          // top = (clickDomY) + 'px';
          // left = (clickDomX) + 'px';
          break;
        }
      }
      return { top: top, left: left };
    },
    resize ({ width, height }) {},
    clear () {},
    destroy () {},
    formatCSS (arg) {
      return (
        Object.keys(arg)
          .map((key) => {
            return key + ':' + arg[key];
          })
          .join(';') + ';'
      );
    },
    dialogShow (params) {
      if (params && params.offsetX) {
        this.offsetX = +params.offsetX.offsetX || 0;
      }
      if (params && params.offsetY) {
        this.offsetY = +params.offsetY.offsetY || 0;
      }
      // this.testDom.style.zIndex = 9999;
      this.seatom_setParentStyle({
        zIndex: 9999
      })

      this.fetchData()

      this.show = true;
      this.showBox = true;
      const timer = setTimeout(() => {
        this.throttle(this.realPosition, 15, true, this.tempEventTarget);
      })
      this.$once('hook:beforeDestroy', () => {
        clearTimeout(timer)
      })
    },
    dialogHidden () {
      this.show = false;
      this.showBox = true;
    },
    dialogOpen () {
      this.emit('open');
    },
    dialogClose () {
      this.emit('close');
    },
    mapInnerTest (params) {
      const { coordinates = {}, paramsType = '0' } = params;
      this.mapX = coordinates.x;
      this.mapY = coordinates.y;
      if (!this.show && paramsType === '1') {
        return;
      }
      this.throttle(this.realPosition, 15, true, this.tempEventTarget);
      // this.testDom.style.zIndex = 9999;
      this.seatom_setParentStyle({
        zIndex: 9999
      })
      this.show = true;
      this.showBox = true;
    },
    closePopover () {
      // 动态调整弹窗的z-index的值防止弹窗组件遮挡造成组件事件无法触发
      // this.testDom.style.zIndex = -1;
      this.seatom_setParentStyle({
        zIndex: -1
      })
      this.show = false;
      this.showBox = true;
    },
    // 节流方法
    throttle (func, wait = 300, immediate = true, params) {
      if (immediate) {
        if (!this.flag) {
          this.flag = true;
          // 如果是立即执行，则在wait毫秒内开始时执行
          typeof func === 'function' && func(params);
          this.timer = setTimeout(() => {
            this.flag = false;
          }, wait);
        }
      } else {
        if (!this.flag) {
          this.flag = true;
          // 如果是非立即执行，则在wait毫秒内的结束处执行
          this.timer = setTimeout(() => {
            this.flag = false;
            typeof func === 'function' && func(params);
          }, wait);
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.popover-container {
  position: relative;
  width: 100%;
  height: 100%;

  .screen {
    // position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #434b55;
      border: 1px solid #434b55;
    }
  }

  .empty__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #524b50;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    span {
      font-size: 14px;
      color: #fff;
    }
  }
}
::v-deep .el-popover {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  // background: transparent;
  box-shadow: none;
  border: none;
}
::v-deep .el-popover .el-popover__header {
  padding: 0;
}
::v-deep .el-popover .el-popover__body {
  width: 100%;
  height: 100%;
  padding: 0;
}
::v-deep .el-popover .el-popover__header .el-popover__headerbtn {
  z-index: 10010;
}
.popoverButton {
  cursor: pointer;
  width: 30px;
  height: 30px;
  line-height: 30px;
  position: absolute;
  z-index: 99;
  right: 0;
  top: 0;
  text-align: center;
  color: #bfbfbf;
}
</style>
