<template>
  <div
    class="carouse-container"
    :class="{'hide': !isView}"
    ref="container"
    v-show="dialogBoxVisible">
    <el-dialog
      v-if="isView"
      :visible.sync="dialogVisible"
      ref="dialog"
      :style="dialogStyle"
      :close-on-click-modal="iscloseOnClickModal"
      :modal="modal"
      :append-to-body="false"
      :modal-append-to-body="false"
      :show-close="showClose"
      @open="dialogOpen"
      @close="dialogClose"
      @close-on-press-escape="true">
      <template v-for="item in screenList">
        <div :key="item.id" class="screen" v-if="dialogVisible">
          <PitchViewScreen
            v-if="isLoadedScreen && !showLoading"
            :showScroll="showScroll"
            :item-data="dialogData"
            :screen-style="screenConfig"
            :screen-coms="childScreenComps"
            :screen-layers="childScreenLayers"
            :workspace-id="childWorkspaceId"
            :screen-filters="childFiltersMap"
            :platform="screenInfo.type"
            :isView="isView"
            :mainScreenId="mainScreenId"
            v-on="$listeners" />
        </div>
      </template>
      <seatom-loading v-if="showLoading"/>
    </el-dialog>
    <div v-else>
      双击查看面板内详情
    </div>
  </div>

</template>

<script>
import comp from '@/mixins/comp'
import { updateScreenParentId } from '@/api/screen'
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'
import quarkDom from 'hz-quark/dist/dom'

export default {
  name: 'DialogPanel', // 弹窗面板

  inject: ['callbackManager'],

  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager()
    }
  },

  mixins: [comp],

  data () {
    return {
      active: null,
      show: false,
      showBox: false,
      dialogWidth: '',
      dialogData: [],
      activeStyle: {},
      screenInfo: {},
      childScreenComps: {},
      childScreenLayers: [],
      childWorkspaceId: 0,
      childFiltersMap: {},
      childScreenStyle: {},
      showLoading: false,
      screenConfig: {},
      screenType: 'common',
      isLoadedScreen: false
    }
  },

  props: {
    isView: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },

  computed: {
    dialogStyle () {
      const {
        dialog = {}
      } = this.config
      return {
        '--iconColor': dialog?.closeColor ?? '#fafafa',
        '--iconSize': (dialog?.closeSize ?? '18') + 'px',
        'background-color': dialog.modal ? dialog?.closeModalColor ?? 'rgba(30, 30, 30, 0.1)' : 'unset'
      }
    },
    screenList () {
      return this.config.screens || []
    },
    modal () {
      const { dialog: { modal } } = this.config
      return this.isView ? modal : false
    },
    dialogVisible: {
      get () {
        return !this.isView || this.show
      },
      set (val) {
        this.show = val
      }
    },
    dialogBoxVisible: {
      get () {
        return !this.isView || this.showBox
      },
      set (val) {
        this.showBox = val
      }
    },
    showClose () {
      const { dialog: { showClose } } = this.config
      return showClose
    },
    isCenter () {
      const { dialog: { isCenter } } = this.config
      return isCenter
    },
    iscloseOnClickModal () {
      const { dialog: { modal, closeOnClickModal } } = this.config
      return modal ? closeOnClickModal : false
    },
    showScroll () {
      const { dialog: { isShowScroll } } = this.config
      return isShowScroll
    }
  },
  watch: {
    dialogBoxVisible (val) {
      if (val) {
        this.dialogOpen();
      }
    }
  },
  created () {

  },
  mounted () {

  },
  methods: {
    // 获取当前的大屏信息
    async fetchData () {
      if (!this.screenList.length) {
        this.showLoading = false
        return
      }

      if (this.showLoading || this.isLoadedScreen) {
        return
      }

      this.showLoading = true

      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id })

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData;

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }
      // 检查修复结束

      this.childWorkspaceId = screenData.workspaceId
      this.childScreenComps = screenData.components
      this.screenConfig = screenData.config
      this.childScreenLayers = screenData.layers
      this.childFiltersMap = _.keyBy(screenData.filter, 'id')
      this.showLoading = false;
      this.isLoadedScreen = true
    },
    init () {
      this.container = this.$refs.container
      this.dialog = this.$refs?.dialog?.$el?.firstChild
    },
    async render () {
      if (!this.isView) {
        return
      }
      const {
        screens = [],
        background = {},
        border = {}
      } = this.config

      if (!this.isView) {
        await this.fetchData()
      }

      if (this.data) {
        this.dialogData = _.cloneDeep(this.data)
      }

      const screenList = screens

      if (screenList.length) {
        this.active = screenList[0].id
      }

      let containerCssText = ''

      containerCssText += this.formatCSS({
        width: this.isView ? this.attr.w + 'px' : '100%',
        height: this.isView ? this.attr.h + 'px' : '100%',
        top: (this.isView && !this.isCenter) ? this.attr.y + 'px' : null,
        left: (this.isView && !this.isCenter) ? this.attr.x + 'px' : null,
        transform: (this.isView && !this.isCenter) ? 'none' : null
      })

      // 声明容器的样式
      // 格式化背景样式
      containerCssText += quarkDom.formatBackground(background)
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border)
      this.dialog.style.cssText = containerCssText

      // 预览页设置父div样式 防止覆盖其他图层
      if (this.isView) {
        this.seatom_setParentStyle({
          left: -this.attr.w + 'px',
          top: -this.attr.h + 'px',
          zIndex: '10010'
        })
      }
    },
    resize ({ width, height }) {},
    clear () {},
    destroy () {},
    formatCSS (arg) {
      return (
        Object.keys(arg)
          .map((key) => {
            return key + ':' + arg[key]
          })
          .join(';') + ';'
      )
    },
    dialogShow () {
      this.show = true
      this.showBox = true
      this.fetchData()
    },
    dialogHidden () {
      this.show = false
      this.showBox = true
    },
    dialogOpen () {
      this.emit('open')
    },
    dialogClose () {
      this.emit('close')
    }
  }

}
</script>

<style lang="scss" scoped>
.carouse-container {
  position: relative;
  width: 100%;
  height: 100%;

  &.hide {
    background: #84717161;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    font-weight: 600;
  }

  .screen {
    // position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #434b55;
      border: 1px solid #434b55;
    }
  }

  .empty__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #524b50;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    span {
      font-size: 14px;
      color: #fff;
    }
  }

}
::v-deep .el-dialog {
  padding: 0;
  margin: 0;
  background: transparent;
  box-shadow: none;
  .el-dialog__headerbtn{
    top:10px;
    right:10px;
  }
  .el-icon-close{
    color: var(--iconColor);
    font-size: var(--iconSize);
    transition:unset;
  }
}
::v-deep .el-dialog .el-dialog__header {
  padding: 0
}
::v-deep .el-dialog .el-dialog__body {
  width: 100%;
  height: 100%;
  padding: 0;
}
::v-deep .el-dialog .el-dialog__header .el-dialog__headerbtn {
  z-index: 10010;
}
</style>
