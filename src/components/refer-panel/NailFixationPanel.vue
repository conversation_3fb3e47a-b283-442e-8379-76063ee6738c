<template>
  <div ref='container' class="fold-panel-wrap">
    <PitchViewScreen
      v-if="isLoadedScreen"
      :item-data='fixData'
      :screen-style='screenConfig'
      :screen-coms='childScreenComps'
      :screen-layers='childScreenLayers'
      :workspace-id='childWorkspaceId'
      :screen-filters='childFiltersMap'
      :platform='screenInfo.type'
      :heightAuto="heightAuto"
      :isView="isView"
      :mainScreenId="mainScreenId"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import comp from '@/mixins/comp';
import { updateScreenParentId } from '@/api/screen';
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'
import quarkDom from 'hz-quark/dist/dom'

export default {
  name: 'NailFixationPanel', // 固钉面板
  data () {
    return {
      childScreenComps: {},
      fixData: [],
      screenConfig: {},
      childScreenLayers: [],
      childWorkspaceId: 0,
      childFiltersMap: {},
      screenInfo: {},
      container: null,
      containerParent: null,
      offsetTop: 0,
      isFixed: true,
      viewDom: null,
      wrapper: null,
      heightAuto: false,
      showLoading: false,
      isLoadedScreen: false
    };
  },
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },
  inject: ['callbackManager', 'getScale'],

  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager()
    };
  },

  mixins: [comp],
  computed: {
    screenList () {
      return this.config.screens || [];
    }
  },
  watch: {
    isFixed: {
      handler (newVal, oldVal) {
        const scale = this.getScale();
        const pRect = this.containerParent.getBoundingClientRect();
        if (newVal) {
          // 面板解除固定时
          this.emit('endFixed', {});
          this.container.style.height = this.heightAuto ? 'auto' : '100%'; // 解除固定时 高度重新设置
          this.containerParent.append(this.container);
          if (this.wrapper) { // 移除dom
            this.wrapper.parentNode.removeChild(this.wrapper);
          }
        } else {
          // 面板处于配置固定高度时
          this.emit('startFixed', {});
          if (this.isView) {
            this.wrapper = document.createElement('div');
            this.wrapper.className = 'fold-panel-wrapper';
            const styleObj = {
              position: 'fixed',
              top: '0px',
              transform: `scale(${scale}) translate(0, ${this.offsetTop}px)`,
              transformOrigin: '0px 0px',
              width: (pRect.width / scale) + 'px'
            }
            Object.keys(styleObj).forEach(key => {
              this.wrapper.style[key] = styleObj[key];
            })

            if (!this.heightAuto) { // 面板高度固定时需要设置container高度值，否则受缩放影响有问题
              this.container.style.height = (pRect.height / scale) + 'px';
            }

            this.wrapper.append(this.container);
            this.bodyDom.append(this.wrapper);
          }
        }
      }
    }
  },
  methods: {
    // 被监听
    rollingheight: _.throttle(function (e) {
      const rect = this.containerParent.getBoundingClientRect();
      const top = rect.top;
      if (top < this.offsetTop) {
        // 固定
        if (this.isFixed) {
          this.isFixed = false;
        }
        // 归位
      } else {
        this.isFixed = true;
      }
    }, 100),
    // 获取当前的大屏信息
    async fetchData () {
      if (!this.screenList.length) {
        this.showLoading = false
        return;
      }

      if (this.showLoading || this.isLoadedScreen) {
        return
      }

      this.showLoading = true

      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id })

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData;

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }
      // 检查修复结束

      this.childWorkspaceId = screenData.workspaceId;
      this.childScreenComps = screenData.components;
      this.screenConfig = screenData.config;
      this.childScreenLayers = screenData.layers;
      this.childFiltersMap = _.keyBy(screenData.filter, 'id');
      this.showLoading = false
      this.isLoadedScreen = true
    },
    init () {
      this.container = this.$refs.container;
      this.containerParent = this.$el.parentElement;
      this.viewDom = document.getElementsByClassName('screen-container')[0];
      this.bodyDom = document.body;
      // 移动端高度自适应
      const { mobileSetting = { heightAuto: false } } = this.config
      if (mobileSetting.heightAuto && this.isView) {
        this.heightAuto = mobileSetting.heightAuto;
        this.handlerAutoHeight && this.handlerAutoHeight(this.container)
      }
      if (this.viewDom) {
        this.viewDom.addEventListener('scroll', this.rollingheight);
      }
    },
    async render () {
      const { background = {}, border = {} } = this.config;

      if (this.data) {
        this.fixData = _.cloneDeep(this.data);
      }
      await this.fetchData();

      let containerCssText = '';

      // 格式化背景样式
      containerCssText += quarkDom.formatBackground(background);
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border);

      if (this.heightAuto) { // 高度自适应设置高度auto
        containerCssText += 'height: auto;'
      }

      this.container.style.cssText = containerCssText;
      this.offsetTop = this.config.function.fixedValue || 0;
    },
    resize: _.debounce(async function ({ width, height }) {
      // await this.render();
    }, 500),
    destroy () {
      if (this.viewDom) {
        this.viewDom.removeEventListener('scroll', this.rollingheight)
      }
    }
  }
};
</script>

<style scoped lang="scss">
.fold-panel-wrap {
  overflow: hidden;
}
</style>
