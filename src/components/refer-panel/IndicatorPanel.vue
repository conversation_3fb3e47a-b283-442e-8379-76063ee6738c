<template>
  <div class="indicator-container" ref="container">
    <div class="ct-wrapper" :style="style">
      <template v-for="(item, index) in screenLayers">
        <template v-if="item.type === 'com'">
          <ViewCompNode
            :key="item.id"
            :workspaceId="workspaceId"
            :id="item.id"
            :type="item.type"
            :item-data="compData"
            :zIndex="screenLayers.length - index"
            :platform="platform"
            :mainScreenId="mainScreenId" />
        </template>
        <template v-else>
          <ViewGroupComp
            :key="item.id"
            :workspaceId="workspaceId"
            :id="item.id"
            :pageLayers="item.children"
            :item-data="compData"
            :zIndex="screenLayers.length - index"
            :platform="platform"
            :mainScreenId="mainScreenId" />
        </template>
      </template>
    </div>
  </div>
</template>
<script>
import comp from '@/mixins/comp'
import { replaceUrl, findImageUrl } from '@/utils/base'
import { addImportComponents } from '@/utils/systemImport'
import { updateScreenParentId } from '@/api/screen';
import quarkDom from 'hz-quark/dist/dom'
import Tree from '@/lib/Tree';
export default {
  name: 'indicatorPanel',
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    platform: {
      type: String,
      default: 'pc'
    },
    attr: {
      type: Object,
      default () {
        return {}
      }
    },
    mainScreenId: {
      type: Number,
      required: true
    }
  },
  provide: function () {
    return {
      getLayerTree: () => this.layerTree, // 通过函数实现响应式
      filtersMap: () => this.filtersMap,
      screenComs: () => this.screenComs,
      callbackManager: () => this.callbackManager(),
      getScale: this.getScale(),
      permissionMap: () => {}
    }
  },
  inject: ['callbackManager'],
  mixins: [comp],
  computed: {
    screens () {
      return this.config.screens || []
    },
    style () {
      const { w, h } = this.attr;
      const originWidth = this.screenConfig.width;
      const originHeight = this.screenConfig.height;
      const scaleX = w / originWidth;
      const scaleY = h / originHeight;
      const scale = Math.min(scaleX, scaleY);
      const translateX = (w - originWidth) / 2;
      const translateY = (h - originHeight) / 2;
      return {
        width: originWidth + 'px',
        height: originHeight + 'px',
        transformOrigin: '50% 50%',
        transform: `translateX(${translateX}px) translateY(${translateY}px) scaleX(${scale}) scaleY(${scale})`
      }
    }
  },
  data () {
    return {
      layerTree: new Tree(),
      showLoading: false,
      screenLayers: [],
      screenInfo: {},
      workspaceId: '',
      screenConfig: {
        width: 400,
        height: 200
      },
      screenComs: {},
      filtersMap: {},
      compData: []
    }
  },
  created () {
    this.fetchData()
  },
  methods: {
    async fetchData () {
      const screenId = this.screens[0]?.id
      if (!screenId) {
        return
      }

      this.showLoading = true
      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId })
      this.showLoading = false

      addImportComponents(screenData.components)
      Object.values(screenData).forEach(item => {
        findImageUrl(item, replaceUrl)
      })

      this.screenInfo = screenData;
      // 检查修复面板类组件parentId错误问题
      if (!!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }

      this.workspaceId = screenData.workspaceId
      this.screenComs = screenData.components

      this.screenConfig = screenData.config
      this.screenLayers = screenData.layers
      this.filtersMap = _.keyBy(screenData.filter, 'id')

      this.layerTree = new Tree({ id: 'root', children: screenData.layers });
    },
    init () {
      this.container = this.$refs.container
    },
    async render () {
      this.compData = _.cloneDeep(this.data) || [];
      const {
        background = {},
        border = {}
      } = this.config

      // 声明容器的样式
      let containerCssText = ''
      // 格式化背景样式
      containerCssText += quarkDom.formatBackground(background)
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border)
      this.container.style.cssText = containerCssText
    },
    resize: _.debounce(function ({ width, height }) {
      this.render()
    }, 500),
    clear () {},
    destroy () {},
    getScale () { // 获取缩放比例
      return () => {
        return 1
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.indicator-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .ct-wrapper {
    width: 100%;
    height: 100%;
  }
}
</style>
