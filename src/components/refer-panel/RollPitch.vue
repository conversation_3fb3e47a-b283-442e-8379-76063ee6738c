<template>
  <div class="roll-pitch-container" ref="rollPitchContainer">
    <div class="roll-wrapper"
         ref="rollWrapper"
         :style="computedRollAreaStyle"
         @mouseenter="pauseRoll"
         @mouseleave="playRoll"
    >
      <ul class="roll-area area2"
          ref="realList"
          :style="computedRollUlStyle"
      >
        <li class="roll-area-item"
            v-for="(item,index) in renderData"
            :key="index"
            :style="{...liStyle, ...shiftingStyle}"
        >
          <div class="screen"
               ref="screen"
               :style="computedActiveStyle(itemData.renderIndex)"
               v-for="itemData in item"
               :key="itemData.renderIndex"
               @mouseenter="setActiveAndPause(itemData)"
               @mouseleave="setActiveAndPlay"
               @click="clickItem(itemData,itemData.renderIndex)"
          >
            <PitchViewScreen
              v-if="isLoadedScreen && Object.keys(itemData[0]).length"
              :item-data="itemData"
              :screen-style="childScreenStyle"
              :screen-coms="childScreenComps"
              :screen-layers="childScreenLayers"
              :workspace-id="childWorkspaceId"
              :screen-filters="childFiltersMap"
              :platform="screenInfo.type"
              :isView="isView"
              :mainScreenId="mainScreenId"
              v-on="$listeners"
            />
          </div>
        </li>
      </ul>
      <ul class="roll-area area3"
          ref="upAndLeftWrap"
          :style="computedRollUlStyle"
          v-if="canRoll"
      >
        <li class="roll-area-item"
            v-for="(item,index) in copyRollPitchData"
            :key="index"
            :style="liStyle"
        >
          <div class="screen"
               ref="screen"
               :style="computedActiveStyle(itemData.renderIndex)"
               v-for="itemData in item"
               :key="itemData.renderIndex"
               @mouseenter="setActiveAndPause(itemData)"
               @mouseleave="setActiveAndPlay"
               @click="clickItem(itemData,itemData.renderIndex)"
          >
            <PitchViewScreen
              v-if="isLoadedScreen && Object.keys(itemData[0]).length"
              :item-data="itemData"
              :screen-style="childScreenStyle"
              :screen-coms="childScreenComps"
              :screen-layers="childScreenLayers"
              :workspace-id="childWorkspaceId"
              :screen-filters="childFiltersMap"
              :platform="screenInfo.type"
              :isView="isView"
              :mainScreenId="mainScreenId"
              v-on="$listeners"
            />
          </div>
        </li>
      </ul>
    </div>
    <seatom-loading v-if="showLoading"/>
  </div>
</template>

<script>
import comp from '@/mixins/comp'
import { formatBorder, formatBackground, formatPadding } from '@/utils/styleHandler'
import { updateScreenParentId } from '@/api/screen'
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'

const speedHash = {
  1: 50,
  2: 100,
  3: 500,
  4: 1000,
  5: 5000
}

export default {
  name: 'rollPitch', // 轮播切换面板
  mixins: [comp],
  props: {
    id: {
      type: String,
      default: ''
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },
  inject: ['callbackManager'],
  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager()
    }
  },
  data () {
    this.animate1 = null
    this.animate2 = null
    this.animate3 = null
    this.timeout = null
    return {
      liStyle: {},
      ulStyle: {},
      cacheData: [],
      rollPitchData: [],
      copyRollPitchData: [],
      screenStyle: {},
      canRoll: false,
      activeIndex: -1,
      realIndex: -1,
      selected: {
        isHover: false,
        isClick: false
      },
      roll: {
        isRoll: false,
        rollMode: 'smooth',
        speed: 1,
        count: 1,
        directionY: 'up',
        directionX: 'left',
        delay: 1000
      },
      activeStyle: {},
      showLoading: false,
      clickIndex: -1,
      stop: false,
      rollSpeed: 0,
      rollDistance: 0,
      lineDistance: 0,
      // 大屏实例参数
      screenInfo: {},
      childScreenComps: {},
      childScreenLayers: [],
      childWorkspaceId: 0,
      childFiltersMap: {},
      childScreenStyle: {},
      isLoadedScreen: false,
      shiftingStyle: {}, // 偏移量
      renderData: [], // 渲染的列表
      start: 0, // 开始渲染的位置
      scrollWH: 0 // 真实移动时的偏移量
    }
  },
  computed: {
    screenList () {
      return this.config.screens || []
    },
    computedActiveStyle () {
      return index => (this.activeIndex === index || this.clickIndex === index)
        ? { ...this.screenStyle, ...this.activeStyle } : this.screenStyle
    },
    computedCondition () {
      return (this.config.item.direction === 'row' && this.roll.directionY === 'up') ||
        (this.config.item.direction === 'column' && this.config.roll.directionX === 'left')
    },
    computedRollUlStyle () {
      const { item = {}, roll = {} } = this.config
      if (item.direction === 'row' && roll.directionY === 'down' && this.canRoll) {
        return {
          height: this.itemH * Math.ceil(this.initData.length / item.xCount) + 'px',
          transform: 'translateY(-100%)'
        }
      } else if (item.direction === 'column' && roll.directionX === 'right' && this.canRoll) {
        return {
          width: this.itemW * Math.ceil(this.initData.length / item.yCount) + 'px',
          transform: 'translateX(-100%)'
        }
      } else if (item.direction === 'row' && roll.directionY === 'up' && this.canRoll) {
        return {
          height: this.itemH * Math.ceil(this.initData.length / item.xCount) + 'px'
        }
      } else if (item.direction === 'column' && roll.directionX === 'left' && this.canRoll) {
        return {
          width: this.itemW * Math.ceil(this.initData.length / item.yCount) + 'px'
        }
      } else {
        return {}
      }
    },
    computedRollAreaStyle () {
      const { item = {} } = this.config
      return {
        flexDirection: item.direction === 'row' ? 'column' : 'row'
      }
    },
    // FIXME:  兼容老数据格式，如果不匹配老数据则用新数据格式处理
    initData () {
      return this.data?.[0]?.data || this.data.map(item => {
        return [item]
      })
    },
    itemH () {
      return parseInt(this.screenInfo.type === 'pc'
        ? this.attr.h * (1 / this.config.item.yCount)
        : (this.attr.h * 10 + ((this.attr.h - 1) * 5)) * (1 / this.config.item.yCount))
    },
    itemW () {
      return parseInt(this.screenInfo.type === 'pc'
        ? this.attr.w * (1 / this.config.item.xCount)
        : this.screenInfo?.config?.width * (this.attr.w / 24) * (1 / this.config.item.xCount))
    },
    // 可视范围展示几行
    showCount () {
      const { item: { xCount, yCount, direction } } = this.config
      return direction === 'row' ? yCount : xCount
    },
    // 计算页面能容下几个基点,并且增加四个容错
    volume () {
      return this.showCount + 4
    }
  },
  watch: {
    realIndex () {
      this.changeItemState()
    },
    cacheData: {
      handler () {
        this.calcData(this.config.item)
      },
      deep: true
    }
  },
  methods: {
    init () {
      this.rollPitchContainer = this.$refs.rollPitchContainer
      this.rollWrapper = this.$refs.rollWrapper
      this.realList = this.$refs.realList
    },
    async render (a) {
      cancelAnimationFrame(this.animate1)
      cancelAnimationFrame(this.animate2)
      cancelAnimationFrame(this.animate3)
      clearTimeout(this.timeout)
      this.animate1 = null
      this.animate2 = null
      this.animate3 = null
      this.timeout = null

      const {
        background = {},
        border = {},
        item = {},
        selected = {},
        roll = {},
        base = {}
      } = this.config
      await this.getItemScreen()

      if (_.isEmpty(this.childScreenStyle)) return;

      let containerCssText = ''

      if (this.data.length) {
        this.selected = selected
        this.roll = _.cloneDeep(roll)
        if (roll.rollMode === 'fullPage' && (this.initData.length % (item.xCount * item.yCount) !== 0)) {
          const mod = item.xCount * item.yCount
          const count = mod - (this.initData.length % mod)
          for (let i = 0; i < count; i++) {
            this.initData.push([{}])
          }
        } else if (roll.rollMode !== 'fullPage') {
          this.initData.forEach((item, index) => {
            if (!Object.keys(this.data?.[0]?.data ? item[0] : item).length) {
              this.initData.length = index
            }
          })
        }
        // TODO : 数据量大的情况下realIndex也要带进去，需要在列表截取区域，默认截单屏即可
        this.cacheData = _.cloneDeep(this.initData)
        await this.calcData(item)
        this.renderData = this.rollPitchData.slice(0, this.volume);
      }
      const height = parseInt(this.childScreenStyle.height)
      const width = parseInt(this.childScreenStyle.width)
      this.liStyle = {
        height: item.direction === 'row' ? `${height}px` : `${height * item.yCount}px`,
        width: item.direction === 'row' ? `${width * item.xCount}px` : `${width}px`,
        flexDirection: item.direction
      }
      this.lineDistance = item.direction === 'row' ? parseInt(this.childScreenStyle.height) : parseInt(this.childScreenStyle.width)
      this.rollSpeed = speedHash[this.roll.speed] * this.lineDistance / 10000
      this.canRoll = roll.isRoll && this.cacheData.length > (item.yCount * item.xCount)
      // 列表项
      this.screenStyle = this.calcScreenStyle(item)
      // 列表项选中样式
      this.activeStyle = this.formatActiveStyle(selected.activeStyle)
      this.$nextTick(() => {
        // 容器
        containerCssText += `height:${this.rollPitchContainer.style.height}px;width:${this.rollPitchContainer.style.width}px;`
        containerCssText += formatBackground(background)
        containerCssText += formatBorder(border)
        containerCssText += formatPadding(base.padding)
        this.rollPitchContainer.style.cssText = containerCssText
        this.shiftingStyle = {
          transform: `translate${item.direction === 'row' ? 'Y' : 'X'}(0px)`
        }
        if (this.canRoll) {
          if (this.$refs.screen[0].style.height) {
            this.startRoll(roll.rollMode)
          }
        } else {
          this.rollWrapper.style.transform = ''
          this.rollDistance = 0
        }
      })
    },
    destroy () {
      cancelAnimationFrame(this.animate1)
      cancelAnimationFrame(this.animate2)
      cancelAnimationFrame(this.animate3)
      clearTimeout(this.timeout)
      this.animate1 = null
      this.animate2 = null
      this.animate3 = null
      this.timeout = null
    },
    resize: _.debounce(async function ({ width, height }) {
      this.init();
      await this.render()
    }, 500),
    async calcData (item) {
      if (!this.cacheData) return
      const temp = this.cacheData.map((dataItem, dataIndex) => {
        dataItem.renderIndex = dataIndex
        dataItem.realIndex = dataIndex
        return dataItem
      })
      this.rollPitchData = this.calcRenderArray(temp, item.direction, item.xCount, item.yCount)
      let length = temp.length
      const copyTemp = _.cloneDeep(temp).map((copyItem, copyIndex) => {
        copyItem.renderIndex = length++
        copyItem.realIndex = copyIndex
        return copyItem
      })
      // this.copyRollPitchData = this.calcRenderArray(copyTemp, item.direction, item.xCount, item.yCount)
      this.copyRollPitchData = this.calcRenderArray(copyTemp, item.direction, item.xCount, item.yCount).slice(0, this.showCount)
    },
    startRoll (rollMode) {
      switch (rollMode) {
        case 'smooth':
          this.animate1 = requestAnimationFrame(this.smoothRollLoop)
          break
        case 'custom':
          this.animate1 = requestAnimationFrame(this.customRollLoop)
          break
        case 'fullPage':
          this.animate1 = requestAnimationFrame(this.fullPageRollLoop)
          break
        case 'single' :
          this.roll.count = 1
          this.animate1 = requestAnimationFrame(this.customRollLoop)
          break
      }
    },
    moveRollWrapper () {
      this.rollWrapper.style.transform = this.config.item.direction === 'row'
        ? (`translateY(${this.config.roll.directionY === 'up'
          ? -(this.rollDistance += this.rollSpeed)
          : (this.rollDistance += this.rollSpeed)}px)`)
        : (`translateX(${this.config.roll.directionX === 'left'
          ? -(this.rollDistance += this.rollSpeed)
          : (this.rollDistance += this.rollSpeed)}px)`)
    },
    smoothRollLoop () {
      if (this.stop) return
      const { item = {}, roll = {} } = this.config
      const condition = this.lineDistance * this.rollPitchData.length
      if (this.rollDistance >= condition) this.rollDistance = 0
      this.moveRollWrapper()
      if ((item.direction === 'column' && roll.directionX === 'right') || (item.direction === 'row' && roll.directionY === 'down')) {
        this.scrollWH = condition - this.rollDistance
      } else {
        this.scrollWH = this.rollDistance
      }
      const start = this.getCurStart(this.scrollWH);
      if (this.start !== start) {
        const offsetXY = this.scrollWH - (this.scrollWH % this.lineDistance);
        this.renderData = this.rollPitchData.slice(start, start + this.volume);
        this.start = start;
        this.shiftingStyle = {
          transform: `translate${item.direction === 'row' ? 'Y' : 'X'}(${offsetXY}px)`
        }
      }
      if (!this.stop) this.animate2 = requestAnimationFrame(this.smoothRollLoop)
    },
    customRollLoop () {
      if (this.stop) return
      const { item = {}, roll = {} } = this.config
      const condition = this.lineDistance * this.rollPitchData.length
      const computedCondition = this.lineDistance * this.roll.count
      // console.log('this.rollDistance', this.rollDistance)
      // console.log('condition=====', condition)
      if (this.rollDistance >= condition) this.rollDistance = 0
      this.moveRollWrapper()
      if ((item.direction === 'column' && roll.directionX === 'right') || (item.direction === 'row' && roll.directionY === 'down')) {
        this.scrollWH = condition - this.rollDistance
      } else {
        this.scrollWH = this.rollDistance
      }
      const start = this.getCurStart(this.scrollWH);
      if (this.start !== start) {
        const offsetXY = this.scrollWH - (this.scrollWH % this.lineDistance);
        this.renderData = this.rollPitchData.slice(start, start + this.volume);
        this.start = start;
        this.shiftingStyle = {
          transform: `translate${item.direction === 'row' ? 'Y' : 'X'}(${offsetXY}px)`
        }
      }
      if ((Math.round(this.rollDistance * 100000) / 100000) % computedCondition !== 0) {
        this.animate2 = requestAnimationFrame(this.customRollLoop)
      } else {
        cancelAnimationFrame(this.animate2)
        this.timeout = setTimeout(() => {
          this.animate3 = requestAnimationFrame(this.customRollLoop)
          clearTimeout(this.timeout)
        }, this.roll.delay)
      }
    },
    fullPageRollLoop () {
      if (this.stop) return
      const { item = {}, roll = {} } = this.config
      const condition = this.lineDistance * this.rollPitchData.length
      const computedCondition = this.lineDistance * (this.config.item.direction === 'row' ? this.config.item.yCount : this.config.item.xCount)
      if (this.rollDistance >= condition) this.rollDistance = 0
      this.moveRollWrapper()
      if ((item.direction === 'column' && roll.directionX === 'right') || (item.direction === 'row' && roll.directionY === 'down')) {
        this.scrollWH = condition - this.rollDistance
      } else {
        this.scrollWH = this.rollDistance
      }
      const start = this.getCurStart(this.scrollWH);
      if (this.start !== start) {
        const offsetXY = this.scrollWH - (this.scrollWH % this.lineDistance);
        this.renderData = this.rollPitchData.slice(start, start + this.volume);
        this.start = start;
        this.shiftingStyle = {
          transform: `translate${item.direction === 'row' ? 'Y' : 'X'}(${offsetXY}px)`
        }
      }
      if ((Math.round(this.rollDistance * 100000) / 100000) % computedCondition !== 0) {
        this.animate2 = requestAnimationFrame(this.fullPageRollLoop)
      } else {
        cancelAnimationFrame(this.animate2)
        this.timeout = setTimeout(() => {
          this.animate3 = requestAnimationFrame(this.fullPageRollLoop)
          clearTimeout(this.timeout)
        }, this.roll.delay)
      }
    },
    async getItemScreen () {
      if (!this.screenList.length) {
        this.showLoading = false
        return
      }

      if (this.showLoading) {
        return
      }

      this.showLoading = true

      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id })

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData;

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }
      // 检查修复结束

      this.childScreenStyle = screenData.config
      this.childWorkspaceId = screenData.workspaceId
      this.childScreenComps = screenData.components
      this.childScreenLayers = screenData.layers
      this.childFiltersMap = _.keyBy(screenData.filter, 'id')
      this.showLoading = false
      this.isLoadedScreen = true
    },
    calcScreenStyle (item = {}) {
      const { xCount, yCount, direction } = item
      return {
        width: direction === 'row' ? `${1 / xCount * 100}%` : '100%',
        height: direction === 'row' ? '100%' : `${1 / yCount * 100}%`
      }
    },
    calcRenderArray (arr, direction, xCount, yCount) {
      const result = []
      let temp = []
      const limit = direction === 'row' ? xCount : yCount
      for (let i = 0; i < arr.length; i++) {
        temp.push(arr[i])
        if (temp.length === limit || i === arr.length - 1) {
          result.push(temp)
          temp = []
        }
      }
      return result
    },
    setActiveAndPause (item) {
      this.activeIndex = item.renderIndex
      this.realIndex = item.realIndex
    },
    setActiveAndPlay () {
      this.activeIndex = -1
      this.realIndex = -1
    },
    clickItem (item, index) {
      if (this.selected.isClick) this.clickIndex = this.clickIndex === index ? -1 : index
      this.emit('clickItemEvent', { index: item.realIndex, item, data: item[0] || {} })
      this.seatom_updateCallbackValue({
        clickData: item,
        clickIndex: item.realIndex
      })
    },
    changeItemState (index) {
      const data = {
        index: index || this.realIndex,
        item: this.initData[index || this.realIndex]
      }
      this.emit('changeStateEvent', data)
      this.seatom_updateCallbackValue({
        activeData: this.initData[index || this.realIndex],
        activeIndex: index || this.realIndex
      })
    },
    changeRollPitchState ({ itemIndex, itemData }) {
      this.activeIndex = itemIndex
      this.realIndex = itemIndex
      if (itemData && Object.keys(itemData).length !== 0) {
        this.cacheData[itemIndex] = itemData
      }
    },
    playRoll () {
      this.stop = false
      if (this.canRoll) this.startRoll(this.roll.rollMode)
    },
    pauseRoll () {
      this.stop = true
      cancelAnimationFrame(this.animate1)
      cancelAnimationFrame(this.animate2)
      cancelAnimationFrame(this.animate3)
      this.animate1 = null
      this.animate2 = null
      this.animate3 = null
    },
    formatActiveStyle (activeStyle = {}) {
      const background = formatBackground(activeStyle.background, true)
      const border = formatBorder(activeStyle.border, true)
      return {
        ...background,
        ...border
      }
    },
    getCurStart (width) {
      return Math.floor(width / this.lineDistance);
    }
  }
}
</script>

<style lang="scss" scoped>
.roll-pitch-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .roll-wrapper {
    display: flex;
    .roll-area {
      flex-shrink: 0;
      display: flex;
      align-content: flex-start;
      flex-wrap: wrap;
      overflow: hidden;
      &-item {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        box-sizing: border-box;
        flex-shrink: 0;
      }
    }
  }
  .screen {
    box-sizing: border-box !important;
    overflow: hidden !important;
  }
}
</style>
