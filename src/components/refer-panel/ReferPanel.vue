<template>
  <div class="refer-container" :class="{'height-auto': heightAuto}" ref="container">
    <div class="panel-container" :key="key">
      <div class="close" v-if="config.showClose" @click="closeClick">
        <hz-icon class="icon" name="close"></hz-icon>
      </div>
      <template v-for="item in screenList">
        <div
          :data-id="item.id"
          :key="item.id"
          class="screen animate__animated"
          v-if="item.id == active"
          :class="{
            active: active == item.id,
            [`animate__${config.animationType}`]: active == item.id
          }"
        >
          <ViewScreen
            :screenId="item.id"
            parent
            showLoading
            isReferPanel
            :heightAuto="heightAuto"
            v-on="$listeners" />
        </div>
      </template>
    </div>
    <div class="empty__bg" :class="{ transparent: showBg }" v-if="showEmpty">
      <span v-if="!isView && !screenList.length">右侧面板设置引用大屏</span>
    </div>
  </div>
</template>

<script>
import comp from '@/mixins/comp';
import { uuid } from '@/utils/base'
import emitter from '@/utils/bus'
import quarkDom from 'hz-quark/dist/dom'
export default {
  name: 'ReferPanel', // 引用面板-容器

  inject: ['callbackManager', 'getScale', 'permissionMap'],

  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager(),
      permissionFatherMap: () => this.permissionMap()
    }
  },

  props: {
    isView: {
      type: Boolean,
      default: false
    }
  },

  components: {},

  mixins: [comp],

  data () {
    return {
      active: null,
      timer: null,
      show: true,
      showBg: true,
      list: [],
      screenList: [],
      key: uuid(),
      heightAuto: false
    };
  },

  computed: {
    showEmpty () {
      if (!this.isView && (!this.screenList.length || !this.active)) {
        return true;
      }
      return false;
    },
    panelClass () {
      return function (item) {
        return {
          // eslint-disable-next-line eqeqeq
          active: this.active == item.id,
          // eslint-disable-next-line eqeqeq
          [`animate__${this.config.animationType}`]: this.active == item.id
        }
      }
    }
  },

  created () {
    this.show = true;

    if (!this.isView) {
      emitter.on('ReferPanelState', this.changePanelState)
      this.$once('hook:beforeDestroy', () => {
        emitter.off('ReferPanelState', this.changePanelState)
      })
    }
  },

  methods: {
    /**
     * 初始化方法【必选】
     * 会在 vue 生命周期的 mounted 阶段调用
     */
    init () {
      /** write your code */
      this.container = this.$refs.container;
      // 移动端高度自适应
      const { mobileSetting = { heightAuto: false } } = this.config;
      if (this.isView) {
        this.heightAuto = mobileSetting.heightAuto;
      }
    },
    /**
     * 渲染组件【必选】
     * 当组件被初始化后，组件渲染逻辑被调用。
     */
    render () {
      /** write your code */
      // 可通过 `this.config this.data` 读取配置和数据
      const {
        autoPlay,
        updateTime = 3,
        screens = [],
        isControlled,
        background = {},
        border = {}
      } = this.config;

      const screenList = isControlled ? (_.cloneDeep(this.data) || []) : screens;
      this.screenList = screenList.filter(item => item.id);
      this.showBg = background.show;

      if (this.screenList.length) {
        this.active = this.screenList[0].id;
      } else {
        this.active = '';
      }

      // 声明容器的样式
      let containerCssText = '';
      // 格式化背景样式
      containerCssText += quarkDom.formatBackground(background);
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border);
      this.container.style.cssText = containerCssText;

      if (this.timer) clearInterval(this.timer);
      if (autoPlay && this.screenList.length > 1) {
        let index = 0;
        this.timer = setInterval(() => {
          this.active = this.screenList[index].id;
          if (index < this.screenList.length - 1) {
            index++;
          } else {
            index = 0;
          }
        }, updateTime * 1000);
      }
    },
    /**
     * 自适应尺寸【可选】
     * 当组件被拖拽、缩放时被调用。
     */
    resize ({ width, height }) {
      /** write your code */
      this.$nextTick(() => {
        if (!this.isView) this.key = uuid();
        // this.render();
      })
    },
    /**
     * 清理组件，只清空画布，组件实例保留【可选】
     * 当组件被清理时调用
     */
    clear () {
      /** write your code */
    },
    /**
     * 销毁组件，彻底销毁，组件实例不可用【可选】
     * 组件被销毁时调用，会在 vue 组件声明周期勾子 beforeDestroy 里调用
     */
    destroy () {
      /** write your code */
      if (this.timer) clearInterval(this.timer);
    },
    /**
     * 组件内置，不可在此处覆盖的方法
     * emit(eventName, data)：抛出事件。开发者可以调用，勿改勿覆盖
     * on(compId, eventName, callback)：此组件监听来自其他组件的事件。组件内部调用，勿改勿覆盖
     * off(compId, eventName, callback)：移除监听。组件内部调用，勿改勿覆盖
     */
    closeClick () {
      this.show = false;
      this.emit('COMMON_CLOSE');
    },
    changeScreen (params) {
      const changeId = params.changeId;
      const changeData = params.changeData;
      try {
        if (changeId) {
          // eslint-disable-next-line eqeqeq
          const index = this.screenList.findIndex((item) => item.id == changeId);
          if (index === -1) {
            this.screenList.push({ id: changeId });
          }
          this.active = changeId;
          return
        }
        if (changeData) {
          const changeId = changeData.id;
          if (changeId) {
            // eslint-disable-next-line eqeqeq
            const index = this.screenList.findIndex((item) => item.id == changeId);
            if (index === -1) {
              this.screenList.push({ id: changeId });
            }
            this.active = changeId;
          } else {
            console.warn('screenId is required');
          }
        }
      } catch (e) {}
    },
    changePanelState ({ id, state }) {
      if (id === this.id) {
        if (state) {
          this.active = state;
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
/**
 在此处添加组件 css 样式（用 scoped 声明隔离其他组件样式，支持纯css、scss、less。）
 注：组件根元素的宽高系统自动设定，不需在此写死宽高，此处宽高用百分比适配。
 */
.refer-container {
  position: relative;
  width: 100%;
  height: 100%;
  &.height-auto{
    // 修复小屏页面使用引用面板，在安卓手机端滑动操作需要点两次才会触发滑动
    .screen{
      overflow: hidden !important;
    }
  }
  .panel-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .close {
    position: absolute;
    width: 40px;
    height: 40px;
    top: 20px;
    right: 20px;
    padding: 3px;
    z-index: 1;
    cursor: pointer;
    .icon {
      color: #fafafa;
      font-size: 34px;
    }
  }
  .screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0;
    animation-duration: 1s;
    &.active {
      z-index: 0;
      opacity: 1;
    }
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #434b55;
      border: 1px solid #434b55;
    }
  }
  .empty__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: #524b50;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    &.transparent {
      background: transparent;
    }
    span {
      font-size: 14px;
      color: #fff;
    }
  }
}
</style>
