<template>
  <div class="loop-pitch-container" ref="loopPitchContainer" @mouseleave="handlContainerMouseleave">
    <template v-for="(item,index) in loopPitchData">
      <div
        class="screen"
        :style="computedActiveStyle(index)"
        :key="index"
        @mouseenter="setActiveAndPause(index)"
        @mouseleave="setActiveAndPlay(index)"
        @click="clickItem(item,index)"
      >
        <PitchViewScreen
          v-if="isLoadedScreen"
          :item-data="item"
          :screen-style="childScreenStyle"
          :screen-coms="childScreenComps"
          :screen-layers="childScreenLayers"
          :workspace-id="childWorkspaceId"
          :screen-filters="childFiltersMap"
          :platform="screenInfo.type"
          :isView="isView"
          :mainScreenId="mainScreenId"
          v-on="$listeners"
        />
      </div>
    </template>
    <seatom-loading v-if="showLoading"/>
  </div>
</template>

<script>
import comp from '@/mixins/comp'
import { updateScreenParentId } from '@/api/screen'
import { formatBorder, formatBackground, formatPadding } from '@/utils/styleHandler'
import isUndefined from 'lodash/isUndefined'
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'

export default {
  name: 'LoopPitch', // 轮播选中面板
  mixins: [comp],
  props: {
    id: {
      type: String,
      default: ''
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },
  inject: ['callbackManager'],
  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager()
    }
  },
  data () {
    this.timer = null
    return {
      loopPitchData: [],
      screenStyle: {},
      activeIndex: 0,
      hoverIndex: -1,
      loop: {
        isHover: false,
        isLoop: false
      },
      screenInfo: {},
      activeStyle: {},
      hoverStyle: {},
      childScreenComps: {},
      childScreenLayers: [],
      childWorkspaceId: 0,
      childFiltersMap: {},
      childScreenStyle: {},
      showLoading: false,
      isLoadedScreen: false
    }
  },
  computed: {
    screenList () {
      return this.config.screens || []
    },
    computedActiveStyle () {
      return index => {
        let style = { ...this.screenStyle }
        if (this.activeIndex === index) {
          style = Object.assign(style, this.activeStyle)
        }
        if (this.hoverIndex === index) {
          style = Object.assign(style, this.hoverStyle)
        }
        return style
      }
    },
    // FIXME:  兼容老数据格式，如果不匹配老数据则用新数据格式处理
    initData () {
      return this.data?.[0]?.data || this.data.map((item, index) => {
        return [item]
      })
    }
  },
  watch: {
    'activeIndex' () {
      this.changeItemState()
    }
  },
  methods: {
    async getItemScreen () {
      if (!this.screenList.length) {
        this.showLoading = false
        return
      }
      if (this.showLoading) {
        return
      }
      this.showLoading = true

      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id })

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData;

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }
      // 检查修复结束

      this.childScreenStyle = screenData.config
      this.childWorkspaceId = screenData.workspaceId
      this.childScreenComps = screenData.components
      this.childScreenLayers = screenData.layers
      this.childFiltersMap = _.keyBy(screenData.filter, 'id')
      this.showLoading = false
      this.isLoadedScreen = true
    },
    init () {
      this.loopPitchContainer = this.$refs.loopPitchContainer
    },
    async render (a) {
      await this.getItemScreen()

      const {
        background = {},
        border = {},
        item = {},
        loop = {},
        base = {}
      } = this.config

      let containerCssText = ''

      this.loop = loop
      this.loopPitchData = _.cloneDeep(this.initData);

      // 从数据中查找是否有isSelected为true
      const activeIndex = this.loopPitchData.findIndex((item) => {
        return item && item[0] && item[0].isSelected === true
      })

      this.loop = loop
      if (this.loopPitchData?.length && loop.isLoop) {
        window.clearInterval(this.timer)

        if (activeIndex === -1) {
          this.activeIndex = 0
        } else {
          this.activeIndex = activeIndex
        }

        this.timer = null
        this.timer = setInterval(() => {
          if (this.activeIndex === this.loopPitchData.length - 1) {
            this.activeIndex = -1
          }
          this.activeIndex++
        }, loop.time)
      } else {
        window.clearInterval(this.timer)
        this.timer = null
        // 找不到则为-1
        this.activeIndex = activeIndex
      }

      const timeout = setTimeout(() => {
        this.changeItemState()
        window.clearTimeout(timeout)
      }, 300)

      this.$nextTick(() => {
        // 列表项
        this.screenStyle = this.formatItem(item)
        // 列表项选中样式
        this.activeStyle = this.formatStyle(loop.activeStyle)
        // 列表项hover样式
        this.hoverStyle = !loop.isHover && item?.hoverStyle && this.formatStyle(item?.hoverStyle)
        // 容器
        containerCssText += `height:${this.loopPitchContainer.style.height}px;width:${this.loopPitchContainer.style.width}px;`
        containerCssText += formatBackground(background)
        containerCssText += formatBorder(border)
        containerCssText += formatPadding(base.padding)
        this.loopPitchContainer.style.cssText = containerCssText
      })
    },
    destroy () {
      clearTimeout(this.timer)
    },
    setActiveAndPause (index) {
      if (this.loop.isLoop && this.loop.isHover) {
        window.clearInterval(this.timer)
        this.timer = null
        this.activeIndex = index
      }

      if (!this.loop.isLoop && this.loop.isHover) {
        this.activeIndex = index
      }

      if (!this.loop.isHover) {
        this.hoverIndex = index
      }
    },
    // 轮播
    loopPlay () {
      window.clearInterval(this.timer)
      this.timer = null
      this.timer = setInterval(() => {
        if (this.activeIndex === this.loopPitchData.length - 1) {
          this.activeIndex = -1
        }
        this.activeIndex++
      }, this.loop.time)
    },
    setActiveAndPlay () {
      if (this.loop.isHover && this.loop.isLoop) {
        this.loopPlay()
      }
      this.hoverIndex = -1
    },
    // 处理整个容器鼠标移开
    handlContainerMouseleave () {
      if (this.loop.isClick && this.loop.isLoop) {
        this.loopPlay()
      }
    },
    // 处理点击事件
    clickItem (item, index) {
      this.emit('clickItemEvent', {
        index,
        item,
        data: item[0] || {}
      })
      this.seatom_updateCallbackValue({
        clickData: item,
        clickIndex: index
      })
      if (this.loop.isClick) {
        window.clearInterval(this.timer)
        this.timer = null
        this.activeIndex = index
      }
    },
    // 更新选中状态数据
    updateSelectedStatus () {
      if (!this.loopPitchData[0]) {
        return
      }

      if (isUndefined(this.loopPitchData[0][0].isSelected)) {
        return
      }

      this.loopPitchData.forEach((item, index) => {
        this.loopPitchData[index][0].isSelected = false
      })

      if (this.activeIndex === -1) {
        return
      }
      this.loopPitchData[this.activeIndex][0].isSelected = true
    },
    // 处理改变状态
    changeItemState (index) {
      this.updateSelectedStatus()

      const item = this.loopPitchData[index || this.activeIndex] || []

      const data = {
        index: index || this.activeIndex,
        item: item,
        data: item[0] || {}
      }
      this.emit('changeStateEvent', data)

      this.seatom_updateCallbackValue({
        // changeData是新改的回调参数名称
        changeData: data.item,
        changeIndex: data.index,
        // activeData是兼容历史旧的回调参数名称
        activeData: data.item,
        activeIndex: data.index
      })
    },
    changeLoopPitchState ({ itemIndex, itemData }) {
      this.activeIndex = itemIndex
      if (itemData && Object.keys(itemData).length !== 0) {
        this.loopPitchData[itemIndex] = itemData
      }
      window.clearInterval(this.timer)
      this.timer = null
      if (this.loop.isLoop) {
        this.timer = setInterval(() => {
          if (this.activeIndex === this.loopPitchData.length - 1) {
            this.activeIndex = -1
          }
          this.activeIndex++
        }, this.loop.time)
      }
    },
    playLoop () {
      this.loop.isLoop = true
      window.clearInterval(this.timer)
      this.timer = null
      this.timer = setInterval(() => {
        if (this.activeIndex === this.loopPitchData.length - 1) {
          this.activeIndex = -1
        }
        this.activeIndex++
      }, this.loop.time)
    },
    pauseLoop () {
      this.loop.isLoop = false
      window.clearInterval(this.timer)
      this.timer = null
    },
    formatStyle (style = {}) {
      const background = formatBackground(style.background, true)
      const border = formatBorder(style.border, true)
      return {
        ...background,
        ...border
      }
    },
    formatItem (item = {}) {
      const { height, heightUnit, width, widthUnit } = item
      return {
        height: `${height}${heightUnit}`,
        width: `${width}${widthUnit}`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.loop-pitch-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: row wrap;
  align-content: flex-start;
  overflow: auto;
  .screen {
    box-sizing: border-box !important;
    overflow: hidden !important;
  }
}
</style>
