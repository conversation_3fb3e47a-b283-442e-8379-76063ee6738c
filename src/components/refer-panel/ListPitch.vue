<template>
<div :style="scrollStyle">
  <div class="list-pitch-container" :class="{ heightAuto }" ref="listPitchContainer">
    <template v-for="(item,index) in listPitchData">
      <div
        class="screen"
        :key="index"
        :style="computedScreenStyle(index)"
        @mouseenter="mouseEnterEvent(index,item)"
        @mouseleave="mouseLeaveEvent"
        @click="clickEvent(index,item)"
      >
        <PitchViewScreen
          v-if="isLoadedScreen"
          :item-data="item"
          :screen-coms="childScreenComps"
          :screen-filters="childFiltersMap"
          :screen-layers="childScreenLayers"
          :screen-style="childScreenStyle"
          :workspace-id="childWorkspaceId"
          :platform="screenInfo.type"
          :isView="isView"
          :mainScreenId="mainScreenId"
          v-on="$listeners"
        />
      </div>
    </template>
  </div>
  </div>
</template>

<script>
import comp from '@/mixins/comp'
import { formatBorder, formatBackground, formatPadding } from '@/utils/styleHandler'
import { updateScreenParentId } from '@/api/screen'
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'

export default {
  name: 'listPitch', // 列表面板
  mixins: [comp],
  props: {
    id: {
      type: String,
      default: ''
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },
  inject: ['callbackManager'],
  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager()
    }
  },
  data () {
    return {
      screenInfo: {},
      childScreenComps: {},
      childScreenLayers: [],
      childWorkspaceId: 0,
      childFiltersMap: {},
      childScreenStyle: {},
      listPitchData: [],
      itemStyle: {},
      hoverStyle: {},
      clickStyle: {},
      clickIndex: -1,
      hoverIndex: -1,
      heightAuto: false,
      showLoading: false,
      isLoadedScreen: false,
      scrollShow: this.config?.scroll?.scrollSwitch
    }
  },
  computed: {
    computedScreenStyle () {
      return index => {
        const style = { ...this.itemStyle }
        if (index === this.hoverIndex) {
          Object.assign(style, this.hoverStyle)
        }
        if (index === this.clickIndex) {
          Object.assign(style, this.clickStyle)
        }
        return style
      }
    },
    screenList () {
      return this.config.screens || []
    },
    initData () {
      if (this.data?.[0]?.data && Array.isArray(this.data?.[0]?.data)) {
        return this.data[0].data
      } else {
        return this.data.map(item => {
          return [item]
        })
      }
    },
    scrollStyle () {
      return {
        '--show': this.config?.scroll?.scrollSwitch ? 'block' : 'none',
        '--width': this.config?.scroll?.scrollWidth + 'px',
        '--scrollBack': this.config?.scroll?.scrollBox?.scrollBoxColor,
        '--scrollBorder': this.config?.scroll?.scrollBox?.scrollBoxRound + 'px',
        '--lineColor': this.config?.scroll?.scrollLine?.scrollLineColor,
        '--lineBorder': this.config?.scroll?.scrollLine?.scrollRound + 'px'
      }
    }
  },
  methods: {
    init () {
      this.listPitchContainer = this.$refs.listPitchContainer;
      // 移动端高度自适应
      const { mobileSetting = { heightAuto: false } } = this.config;
      this.heightAuto = mobileSetting.heightAuto;
      if (mobileSetting.heightAuto && this.isView) {
        this.$nextTick(() => {
          this.handlerAutoHeight && this.handlerAutoHeight(this.listPitchContainer);
        })
      }
    },
    async render () {
      const {
        base = {},
        item = {},
        background = {},
        border = {}
      } = this.config

      await this.getScreenInfo()

      let containerCssText = ''

      this.listPitchData = _.cloneDeep(this.initData)

      this.itemStyle = {
        height: `${item.height}px`,
        width: `${item.width}px`
      }

      this.hoverStyle = {
        ...formatBackground(item.hover.background, true),
        ...formatBorder(item.hover.border, true)
      }

      this.clickStyle = {
        ...formatBackground(item.click.background, true),
        ...formatBorder(item.click.border, true)
      }

      this.initNormalClickItem()

      this.$nextTick(() => {
        // 整体容器
        containerCssText += formatBackground(background)
        containerCssText += formatBorder(border)
        containerCssText += formatPadding(base.padding)
        containerCssText += `flex-direction:${base.mode};`
        this.listPitchContainer.style.cssText = containerCssText
      })
    },
    async getScreenInfo () {
      if (!this.screenList.length) {
        this.showLoading = false
        return
      }

      if (this.showLoading || this.isLoadedScreen) {
        return
      }

      this.showLoading = true

      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id })

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }
      // 检查修复结束

      this.childScreenStyle = screenData.config
      this.childWorkspaceId = screenData.workspaceId
      this.childScreenComps = screenData.components
      this.childScreenLayers = screenData.layers
      this.childFiltersMap = _.keyBy(screenData.filter, 'id')
      this.showLoading = false
      this.isLoadedScreen = true
    },
    mouseEnterEvent (index, item) {
      this.hoverIndex = index
      this.emit('hoverItemEvent', { index, item })
      this.seatom_updateCallbackValue({
        hoverIndex: index,
        hoverData: item
      })
      // if (this.clickIndex !== index) {
      //   this.changeEvent(index, item)
      // }
    },
    mouseLeaveEvent () {
      this.hoverIndex = -1
    },
    clickEvent (index, item) {
      this.clickIndex = this.clickIndex === index ? -1 : index
      this.emit('clickItemEvent', { index, item })
      this.seatom_updateCallbackValue({
        clickIndex: index,
        clickData: item
      })
      // this.changeEvent(index, item)
    },
    initNormalClickItem () {
      const { item: { click: { clickIndex: index = -1 } } } = this.config
      if (index > 0 && index <= this.listPitchData.length) {
        this.clickEvent(index - 1, this.listPitchData[index - 1])
      }
    }
    // changeEvent(index, item) {
    //   this.emit('changeStateEvent', { index, item })
    //   this.seatom_updateCallbackValue({ activeIndex: index, activeData: item })
    // },
    // changeListPitchState({ index, item }) {
    //   if (item && Object.keys(item).length !== 0) {
    //     this.listPitchData[index] = item
    //   }
    // }
  }
}
</script>

<style lang="scss" scoped>
.list-pitch-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  overflow: auto;
  align-content: flex-start;
  justify-content: flex-start;
  &.heightAuto {
    height: auto !important;
  }
  .screen {
    flex-shrink: 0;
    box-sizing: border-box !important;
    overflow: hidden !important;
  }
}
::-webkit-scrollbar {
  display: var(--show) !important;
  width: var(--width) !important;
}
::-webkit-scrollbar-thumb {
  background-color: var(--scrollBack) !important;
  border-radius: var(--scrollBorder) !important;
}
::-webkit-scrollbar-track {
  background-color: var(--lineColor) !important;
  border-radius: var(--lineBorder) !important;
}
</style>
