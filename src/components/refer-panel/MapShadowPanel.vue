<template>
  <div
    class="popover-container"
    ref="container"
    v-show="dialogBoxVisible"
    :style="cssVar">
    <el-popover
      v-model="dialogVisible"
      ref="dialog"
      placement="bottom"
      @show="dialogOpen"
      @hide="dialogClose">
      <span class="popoverButton" @click="closePopover" v-if="showClose">
        <hz-icon name="close"></hz-icon>
      </span>
      <template v-for="item in screenList">
        <div :key="item.id" class="screen">
          <PitchViewScreen
            v-if="isLoadedScreen && !showLoading"
            :showScroll="showScroll"
            :item-data="dialogData"
            :screen-style="screenConfig"
            :screen-coms="childScreenComps"
            :screen-layers="childScreenLayers"
            :workspace-id="childWorkspaceId"
            :screen-filters="childFiltersMap"
            :platform="screenInfo.type"
            :isView="isView"
            :mainScreenId="mainScreenId"
            v-on="$listeners" />
        </div>
      </template>
      <seatom-loading v-if="showLoading" />
    </el-popover>
  </div>
</template>

<script>
import comp from '@/mixins/comp';
import { updateScreenParentId } from '@/api/screen';
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'

export default {
  name: 'DialogPanel', // 弹窗面板

  inject: ['callbackManager'],

  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager()
    };
  },

  mixins: [comp],

  data () {
    return {
      active: null,
      show: true,
      showBox: false,
      dialogWidth: '',
      dialogData: [],
      activeStyle: {},
      screenInfo: {},
      childScreenComps: {},
      childScreenLayers: [],
      childWorkspaceId: 0,
      childFiltersMap: {},
      childScreenStyle: {},
      showLoading: false,
      screenConfig: {},
      screenType: 'common',
      isLoadedScreen: false,
      clickDomStyle: {},
      lockScroll: false
    };
  },

  props: {
    isView: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },

  computed: {
    screenList () {
      return this.config.screens || [];
    },
    modal () {
      const {
        dialog: { modal }
      } = this.config;
      return this.isView ? modal : false;
    },
    dialogVisible: {
      get () {
        return !this.isView || this.show;
      },
      set (val) {
        this.show = val;
      }
    },
    dialogBoxVisible: {
      get () {
        return !this.isView || this.showBox;
      },
      set (val) {
        this.showBox = val;
      }
    },
    showClose () {
      const {
        dialog: { showClose }
      } = this.config;
      return showClose;
    },
    isCenter () {
      const {
        dialog: { isCenter }
      } = this.config;
      return isCenter;
    },
    iscloseOnClickModal () {
      const {
        dialog: { closeOnClickModal }
      } = this.config;
      return closeOnClickModal;
    },
    showScroll () {
      const {
        dialog: { isShowScroll }
      } = this.config;
      return isShowScroll;
    },
    cssVar () {
      const { border = {} } = this.config
      const { backgroundColor = '' } = this.screenConfig
      return {
        '--container-transform': this.isView ? 'none' : null,
        '--container-bg': backgroundColor,
        '--container-border': border.show ? `${border.width}px ${border.style} ${border.color}` : 'none'
      }
    }
  },
  watch: {
    dialogBoxVisible (val) {
      if (val) {
        this.dialogOpen();
      }
    }
  },
  mounted () {
    setTimeout(() => {
      // tip： 同个大屏内多个弹窗时会同时出现，需要把隐藏
      this.dialogVisible = false;
      // window.addEventListener("click", this.dialogPosition);
      // 此处绑定点击事件以便获取之后点击的dom属性
      if (document.querySelector('.screen-view-wrap')) {
        document
          .querySelector('.screen-view-wrap')
          .addEventListener('click', this.dialogPosition);
      }
      this.clickDomStyle = {};
    }, 0);
  },
  methods: {
    // 获取当前的大屏信息
    async fetchData () {
      if (!this.screenList.length) {
        this.showLoading = false
        return;
      }

      if (this.showLoading || this.isLoadedScreen) {
        return
      }

      this.showLoading = true;

      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id })

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }
      // 检查修复结束

      this.childWorkspaceId = screenData.workspaceId;
      this.childScreenComps = screenData.components;
      this.screenConfig = screenData.config;
      this.childScreenLayers = screenData.layers;
      this.childFiltersMap = _.keyBy(screenData.filter, 'id');
      this.showLoading = false
      this.isLoadedScreen = true;
    },
    init () {
      this.container = this.$refs.container;
      this.dialog = this.$refs.dialog.$el.firstChild;
      this.testDom = this.$refs.container.parentNode;
      // console.log(this.dialog,"this.dialog");
    },
    async render () {
      const { screens = [] } = this.config;

      if (!Object.keys(this.config).length) {
        return;
      }

      // await this.fetchData();

      if (this.data) {
        this.dialogData = _.cloneDeep(this.data);
      }

      const screenList = screens;

      if (screenList.length) {
        this.active = screenList[0].id;
      }
    },
    dialogPosition (event) {
      this.tempEventTarget = event.target;
    },
    // 通过点击事件触发的dom进行属性解析
    realPosition (dom) {
      if (dom.className.animVal === 'hz-icon') { return };
      if (
        (typeof dom === 'object' && dom.getAttribute('popover') === 'true') ||
        (typeof dom.className !== 'object' && dom.className.includes('view-comp-node'))
      ) {
        // 窗口滚动边距
        let scrollTop = 0;
        let scrollLeft = 0;
        if (document.querySelector('.screen-container')) {
          scrollTop = document.querySelector('.screen-container').scrollTop;
          scrollLeft = document.querySelector('.screen-container').scrollLeft;
        }
        let screenTransform, transformX, transformY; //, scale;
        // 窗口缩放比例
        if (document.querySelector('.screen-view-wrap')) {
          screenTransform =
            document.querySelector('.screen-view-wrap').style.transform;
        }
        // console.log('缩放比例---', screenTransform);
        if (screenTransform !== '') {
          if (screenTransform.includes(',')) {
            // 横纵坐标缩放不一样
            let splitTransform = screenTransform.split('(');
            splitTransform = splitTransform[1].split(',');
            transformX = +splitTransform[0];
            transformY = +splitTransform[1].slice(
              0,
              splitTransform[1].length - 1
            );
          } else {
            const splitTransform = screenTransform.split('(');
            transformX = +splitTransform[1].slice(
              0,
              splitTransform[1].length - 1
            );
            transformY = +splitTransform[1].slice(
              0,
              splitTransform[1].length - 1
            );
          }
        }

        if (
          dom.style.left !== '' &&
          dom.style.top !== '' &&
          dom.style.width !== '' &&
          dom.style.height !== ''
        ) {
          // 组件最外层定位弹窗
          this.clickDomStyle = { ...dom.style }; // .getBoundingClientRect();

          let top = this.clickDomStyle.top;
          let left = this.clickDomStyle.left;
          top = top.split('p');
          left = left.split('p');
          // 如果没有缩放且点击的组件不贴着页面顶部
          // 这一步
          if (screenTransform === '' && dom.style.top !== '0px') {
            this.clickDomStyle.top = +top[0];
            this.clickDomStyle.left = +left[0];
          }

          this.clickDomStyle.scaleX = transformX;
          this.clickDomStyle.scaleY = transformY;
        } else {
          // 组件内部定位弹窗

          if (screenTransform !== '') {
            const domStyle = dom.getBoundingClientRect();
            let boxTop = 0;
            let boxLeft = 0;
            if (document.querySelector('.screen-view-wrap')) {
              boxTop = document.querySelector('.screen-view-wrap').style.top;
              boxLeft = document.querySelector('.screen-view-wrap').style.left;
              boxTop = boxTop.split('p');
              boxTop = +boxTop[0];
              boxLeft = boxLeft.split('p');
              boxLeft = +boxLeft[0];
            }
            scrollTop = scrollTop + +(boxTop / transformY).toFixed(2);
            scrollLeft = scrollLeft + +(boxLeft / transformX).toFixed(2);
            this.clickDomStyle = {
              width: +(domStyle.width / transformX).toFixed(2),
              height: +(domStyle.height / transformY).toFixed(2),
              scaleX: transformX,
              scaleY: transformY
            };
            this.clickDomStyle.top =
                +(domStyle.top / transformY).toFixed(2) - scrollTop;
            this.clickDomStyle.left =
                +(domStyle.left / transformX).toFixed(2) - scrollLeft;
          } else {
            this.clickDomStyle = dom.getBoundingClientRect();
          }
        }
        const position = this.autoPosition();
        // this.testDom.style.cssText += this.formatCSS({
        //   top: this.isView ? position.top : null,
        //   left: this.isView ? position.left : null
        // });
        this.seatom_setParentStyle({
          top: this.isView ? position.top : null,
          left: this.isView ? position.left : null
        })
        this.show = true;
        this.showBox = true;
      } else if (
        dom.className.includes('el-dialog__wrapper') ||
        dom.className.includes('el-dialog__close') ||
        dom.className === 'screen-view-wrap' ||
        dom.className === 'popoverButton'
      ) {
      } else {
        this.realPosition(dom.parentNode);
      }
    },
    autoPosition () {
      const { dialog = {} } = this.config;
      const { showPosition, showOffset } = dialog;
      let top = '';
      let left = '';
      // 找出这个弹窗面板的宽高
      const containerStyle = this.container.style;
      const containerWidth = containerStyle.width.split('p');
      const containerHeight = containerStyle.height.split('p');
      // 对点击的dom元素进行判定 找出该点击dom的宽高left top
      let clickDomX =
        typeof this.clickDomStyle.left === 'string'
          ? this.clickDomStyle.left.split('p')
          : this.clickDomStyle.left;
      let clickDomY =
        typeof this.clickDomStyle.top === 'string'
          ? this.clickDomStyle.top.split('p')
          : this.clickDomStyle.top;
      // 对面板的实际宽高进行转化
      containerWidth[0] = +containerWidth[0];
      containerHeight[0] = +containerHeight[0];
      // 对点击dom的left top 宽高进行转化
      clickDomX = typeof clickDomX === 'object' ? +clickDomX[0] : clickDomX;
      clickDomY = typeof clickDomY === 'object' ? +clickDomY[0] : clickDomY;
      // 获得其点击dom的父级宽高以及定位
      const windowScreenStyle =
        document.querySelector('.screen-view-wrap').style; // window.screen;
      const windowScreen = {};
      const windowWidth = windowScreenStyle.width.split('p');
      windowScreen.width = +windowWidth[0];
      const windowHeight = windowScreenStyle.height.split('p');
      windowScreen.height = +windowHeight[0];
      let tempTopLeft = null;
      // console.log('宽高---', windowHeight, windowScreen.height);
      switch (showPosition) {
        case 'top': {
          tempTopLeft = this.judgeFineDirection('top', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
          break;
        }
        case 'bottom': {
          tempTopLeft = this.judgeFineDirection('bottom', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
          break;
        }
        case 'left': {
          tempTopLeft = this.judgeFineDirection('left', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
          break;
        }
        case 'right': {
          tempTopLeft = this.judgeFineDirection('right', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
          // console.log('我是右侧---', left);
          break;
        }
        case 'auto': {
          // 按照上下左右的顺序依次判断
          // 如果该组件top > 弹窗的height则上
          if ((clickDomY + this.mapY) > (containerHeight[0] + showOffset.top + this.mapOffsetY)) {
            // 弹窗在上的情况下再考虑左右边界问题
            // 如果弹窗在左边越界就调整到右侧
            if (((clickDomX + this.mapX)) < (containerWidth[0] + showOffset.left + this.mapOffsetX)) {
              tempTopLeft = this.judgeFineDirection('right', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
              break;
            // 如果弹窗在右边界越界就调整到左侧
            } else if (windowScreen.width - (clickDomX + this.mapX) < (containerWidth[0] + showOffset.left + this.mapOffsetX)) {
              tempTopLeft = this.judgeFineDirection('left', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
              break;
              // 一般情况下的上方
            } else {
              tempTopLeft = this.judgeFineDirection('top', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
              break;
            }
            // 如果该页面高度减去该组件top与其子组件高度后剩余的高度大于弹窗高度 下
          } else if ((windowScreen.height - (clickDomY + this.mapY)) > (containerHeight[0] + showOffset.top + this.mapOffsetY)) {
            // 当弹窗在下面的时候 会有左右超出边界的情况对此一一分析
            if (((clickDomX + this.mapX)) < (containerWidth[0] + showOffset.left + this.mapOffsetX)) {
              tempTopLeft = this.judgeFineDirection('right', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
              break;
            // 如果弹窗在右边界越界就调整到左侧
            } else if (windowScreen.width - (clickDomX + this.mapX) < (containerWidth[0] + showOffset.left + this.mapOffsetX)) {
              tempTopLeft = this.judgeFineDirection('left', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
              break;
              // 一般情况下的下方
            } else {
              tempTopLeft = this.judgeFineDirection('bottom', clickDomY, clickDomX, containerHeight[0], containerWidth[0]);
            }
            break;
            // 该组件left加上其子组件内的left大于弹窗的宽度 左
          }
        }
      }
      top = (tempTopLeft.top + showOffset.top + this.mapOffsetY) + 'px';
      left = (tempTopLeft.left + showOffset.left + this.mapOffsetX) + 'px';
      return { top: top, left: left };
    },
    resize ({ width, height }) {
    },
    clear () {},
    destroy () {},
    formatCSS (arg) {
      return (
        Object.keys(arg)
          .map((key) => {
            return key + ':' + arg[key];
          })
          .join(';') + ';'
      );
    },
    dialogShow () {
      // this.testDom.style.zIndex = 9999;
      this.seatom_setParentStyle({
        zIndex: 9999
      })

      this.fetchData()

      this.show = true;
      this.showBox = true;
      this.throttle(this.realPosition, 15, true, this.tempEventTarget);
    },
    dialogHidden () {
      this.show = false;
      this.showBox = true;
    },
    dialogOpen () {
      this.fetchData()
      this.emit('open');
    },
    dialogClose () {
      this.emit('close');
    },
    mapInnerTest (params) {
      // console.log(params,"params");
      const { coordinates = {}, paramsType = '0' } = params;
      this.mapOffsetX = 0;
      this.mapOffsetY = 0;
      if (params.offsetX || params.offsetY) {
        const { offsetX = 0, offsetY = 0 } = params.offsetX ? params.offsetX : params.offsetY;
        this.mapOffsetX = offsetX * 1;
        this.mapOffsetY = offsetY * 1;
      };
      if (params.classId) {
        this.tempEventTarget = document
          .querySelector(`.${params.classId}`)
      }
      this.mapX = coordinates.x;
      this.mapY = coordinates.y;
      // console.log(params,"params");
      if (!this.show && paramsType === '1') { return };
      // 当触发事件为点击时就让其先隐藏再出现
      this.throttle(this.realPosition, 15, true, this.tempEventTarget);
      // this.testDom.style.zIndex = 9999;
      this.seatom_setParentStyle({
        zIndex: 9999
      })
    },
    closePopover () {
      // 动态调整弹窗的z-index的值防止弹窗组件遮挡造成组件事件无法触发
      // this.testDom.style.zIndex = -1;
      this.seatom_setParentStyle({
        zIndex: -1
      })
      this.show = false;
      this.showBox = true;
    },
    // 节流方法
    throttle (func, wait = 300, immediate = true, params) {
      if (immediate) {
        if (!this.flag) {
          this.flag = true;
          // 如果是立即执行，则在wait毫秒内开始时执行
          typeof func === 'function' && func(params);
          this.timer = setTimeout(() => {
            this.flag = false;
          }, wait);
        }
      } else {
        if (!this.flag) {
          this.flag = true;
          // 如果是非立即执行，则在wait毫秒内的结束处执行
          this.timer = setTimeout(() => {
            this.flag = false
            typeof func === 'function' && func(params);
          }, wait);
        }
      }
    },
    // 返回弹窗最合适的位置
    judgeFineDirection (showPosition, clickDomY, clickDomX, containerHeight, containerWidth) {
      let top, left;
      switch (showPosition) {
        case 'top': {
          top = (clickDomY + this.mapY - containerHeight - 20);
          left = (clickDomX + this.mapX - (containerWidth / 2));
          break;
        }
        case 'bottom': {
          top = (clickDomY + this.mapY);
          left = (clickDomX + this.mapX - (containerWidth / 2));
          break;
        }
        case 'left': {
          top = (clickDomY + this.mapY - (containerHeight / 2));
          left = (clickDomX + this.mapX - containerWidth - 20);
          break;
        }
        case 'right': {
          top = (clickDomY + this.mapY - (containerHeight / 2));
          left = (clickDomX + this.mapX);
          // console.log('我是右侧---', left);
          break;
        }
      }
      return { top, left };
    }
  }
};
</script>

<style lang="scss" scoped>
.popover-container {
  position: relative;
  width: 100%;
  height: 100%;

  .screen {
    // position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #434b55;
      border: 1px solid #434b55;
    }
  }

  .empty__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #524b50;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    span {
      font-size: 14px;
      color: #fff;
    }
  }
}
// ::v-deep .el-dialog {
//   padding: 0;
//   margin: 0;
//   background: transparent;
//   box-shadow: none;
// }
// ::v-deep .el-dialog .el-dialog__header {
//   padding: 0;
// }
// ::v-deep .el-dialog .el-dialog__body {
//   width: 100%;
//   height: 100%;
//   padding: 0;
// }
// ::v-deep .el-dialog .el-dialog__header .el-dialog__headerbtn {
//   z-index: 10010;
// }
::v-deep .el-popover {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  background: var(--container-bg);
  transform: var(--container-transform);
  border: var(--container-border);
  box-shadow: none;
}
::v-deep .el-popover .el-popover__header {
  padding: 0;
}
::v-deep .el-popover .el-popover__body {
  width: 100%;
  height: 100%;
  padding: 0;
}
::v-deep .el-popover .el-popover__header .el-popover__headerbtn {
  z-index: 10010;
}
.popoverButton {
  cursor: pointer;
  width: 30px;
  height: 30px;
  line-height: 30px;
  position: absolute;
  z-index: 99;
  right: 0;
  top: 0;
  text-align: center;
  color: #bfbfbf;
}
</style>
