<template>
  <div class="module-panel-container" ref="container">
    <grid-layout
      ref="gridlayout"
      :style="{}"
      :layout.sync="layout"
      :col-num="24"
      :row-height="10"
      :is-draggable="true"
      :is-resizable="true"
      :is-mirrored="false"
      :vertical-compact="true"
      :margin="[5, 5]"
      :padding="[paddingX, paddingY]"
      :use-css-transforms="true"
      @layout-updated="layoutUpdatedEvent">
      <grid-item
        :ref="item.i"
        v-for="(item, index) in layout"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        :key="item.i"
        :static="item.lock"
        :style="{
          zIndex: layout.length - index
        }"
        :class="{
          isActive: isActive(item)
        }"
        @click.native.stop="handleCompClick(item)"
        v-contextmenu:contextmenu>
        <EditCompNode
          v-if="item.i !== 'drop' && getComDataById(item.i)"
          :id="item.i"
          :platform="'mobile'"
          :type="getComDataById(item.i).type" />
      </grid-item>
    </grid-layout>
    <!-- 右键菜单（暂时用 div 解决报错问题 https://github.com/vuejs/vue/issues/11838)-->
    <div style="display:none;">
      <v-contextmenu
        ref="contextmenu"
        @contextmenu="handleCtxMenu">
        <v-contextmenu-item
          v-for="menu in ctxMenuList"
          v-show="menu.show && !isModule && !['cancelGroup', 'copy', 'paste'].includes(menu.key)"
          :key="menu.key"
          @click="handleCtxClick(menu.key)">
          {{ menu.label }}
        </v-contextmenu-item>
      </v-contextmenu>
    </div>
  </div>
</template>

<script>
import EditCompNode from '@/components/editor/canvas/EditCompNode';
import { GridLayout, GridItem } from '@/lib/vue-grid-layout.common';
import { mapGetters, mapState } from 'vuex';
import comp from '@/mixins/comp';
import canvasBus from '@/utils/canvasBus';
import quarkDom from 'hz-quark/dist/dom'
export default {
  name: 'EditModulePanel', // 模块面板

  inject: ['getLayerTree'],

  props: {},

  components: {
    EditCompNode,
    GridLayout,
    GridItem
  },

  mixins: [comp],

  data () {
    return {
      groupId: '',
      paddingX: 0,
      paddingY: 0,
      layout: []
    }
  },

  computed: {
    ...mapGetters('editor', ['getComDataById', 'ctxMenuList']),
    ...mapState({
      currentSelectId: state => state.editor.currentSelectId,
      screenComs: state => state.editor.screenComs
    }),
    layerTree () {
      return this.getLayerTree();
    },
    isActive () {
      return function (item) {
        if (_.isArray(this.currentSelectId)) {
          return this.currentSelectId.includes(item.i)
        }
        return this.currentSelectId === item.i
      }
    },
    isModule () {
      if (_.isArray(this.currentSelectId)) {
        return this.currentSelectId.findIndex(id => id.startsWith('interaction-container-modulepanel')) > -1
      }
      return (this.currentSelectId || '').startsWith('interaction-container-modulepanel');
    }
  },

  watch: {
    screenComs: {
      handler: function (val) {
        this.$nextTick(() => {
          this.getLayout();
        })
      },
      deep: true
    },
    currentSelectId () {
      this.hideCtxMenu();
    }
  },

  methods: {
    /**
     * 初始化方法【必选】
     * 会在 vue 生命周期的 mounted 阶段调用
     */
    init () {
      /** write your code */
      this.container = this.$refs.container;
    },
    /**
     * 渲染组件【必选】
     * 当组件被初始化后，组件渲染逻辑被调用。
     */
    render () {
      /** write your code */
      // 可通过 `this.config this.data` 读取配置和数据
      const { groupId, background = {}, border = {}, padding = {}, shadow = {}, radius = {} } = this.config;
      // 声明容器的样式
      let containerCssText = '';
      // 格式化背景样式
      containerCssText += quarkDom.formatBackground(background);
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border);

      this.paddingX = padding.left;
      this.paddingY = padding.top;

      // 阴影设置
      let shadowCssText = '';
      shadowCssText = this.formatShadow(shadow);
      containerCssText += shadowCssText;

      // 圆角设置
      let radiusCssText = '';
      radiusCssText = this.formatRadius(radius);
      containerCssText += radiusCssText;

      this.container.style.cssText = containerCssText;

      // 绑定的分组id
      this.groupId = groupId;
      this.getLayout();
    },
    getLayout () {
      const node = this.layerTree.getNodeById(this.groupId);
      if (!node) return;
      const layout = [];
      const nodes = (node.children || []).filter(item => !item.data.id.startsWith('interaction-container-modulepanel'));
      nodes.forEach(item => {
        let com;
        if (item.data.type === 'com') {
          com = this.getComDataById(item.data.id);
        } else {
          com = this.getComDataById(item.data.comId);
        }
        if (com) {
          layout.push({
            x: com.attr.x,
            y: com.attr.y,
            w: com.attr.w,
            h: com.attr.h,
            i: com.id,
            lock: com.attr.lock,
            type: com.type
          })
        }
      })
      this.layout = layout;
    },
    handleCompClick (com) {
      this.hideCtxMenu();
      this.selectNode(com.i);
      this.layerTree.clearSelect();
      this.layerTree.select(com.i)
    },
    hideCtxMenu () {
      this.$refs.contextmenu && this.$refs.contextmenu.hide()
    },
    selectNode (nodeId, clearOther = true) {
      if (_.isNil(nodeId) || clearOther) { // Checks if value is null or undefined.
        // 如果未传参数，则表示清空选中
        this.$store.commit('editor/updateCurrentSelectId', null)
      }

      if (!_.isNil(nodeId)) {
        if (_.isArray(nodeId) && nodeId.length === 1) {
          nodeId = nodeId[0]
        }
        this.$store.commit('editor/updateCurrentSelectId', nodeId)
      }
    },
    layoutUpdatedEvent (newLayout) {
      const updateParams = newLayout.map(n => {
        return {
          id: n.i,
          keyValPairs: [
            { key: 'attr.x', value: n.x },
            { key: 'attr.y', value: n.y },
            { key: 'attr.w', value: n.w },
            { key: 'attr.h', value: n.h }
          ]
        };
      });
      this.$store.dispatch('editor/updateScreenCom', updateParams);
    },
    handleCtxMenu (vnode, e) {
      const compInstance = vnode.componentInstance;
      if (compInstance) { // 右键组件
        const nodeId = compInstance.$props.i;
        if (nodeId) {
          const comData = this.getComDataById(nodeId);
          if (!comData.attr.lock) {
            if (!this.isSelect(nodeId)) {
              this.selectNode(nodeId);
              this.layerTree.select(nodeId);
            }
          } else {
            this.hideCtxMenu();
          }
        } else {
          if (this.handleRectLock) {
            this.hideCtxMenu();
          }
        }
      } else { // 右键空白
        // 记录右键位置
        const rectInfo = this.$refs.canvasPanel.getBoundingClientRect();
        const left = (e.clientX - rectInfo.x) / this.scale;
        const top = (e.clientY - rectInfo.y) / this.scale;
        this.$store.commit('editor/setMenuPosition', { top, left });
        // 清空选择
        this.selectNode(null);
        this.layerTree.clearSelect();
      }
    },
    handleCtxClick (key) {
      canvasBus.emit('ctx_click', key);
    },
    isSelect (nodeId) {
      return false
    },
    /**
     * 自适应尺寸【可选】
     * 当组件被拖拽、缩放时被调用。
     */
    resize ({ width, height }) {
      /** write your code */
    },
    /**
     * 清理组件，只清空画布，组件实例保留【可选】
     * 当组件被清理时调用
     */
    clear () {
      /** write your code */
    },
    /**
     * 销毁组件，彻底销毁，组件实例不可用【可选】
     * 组件被销毁时调用，会在 vue 组件声明周期勾子 beforeDestroy 里调用
     */
    destroy () {
      /** write your code */
    },
    /**
     * 组件内置，不可在此处覆盖的方法
     * emit(eventName, data)：抛出事件。开发者可以调用，勿改勿覆盖
     * on(compId, eventName, callback)：此组件监听来自其他组件的事件。组件内部调用，勿改勿覆盖
     * off(compId, eventName, callback)：移除监听。组件内部调用，勿改勿覆盖
     */
    formatShadow (shadow = {}) {
      if (shadow.show) {
        return `box-shadow: ${shadow.offsetX}px ${shadow.offsetY}px ${shadow.blur}px ${shadow.color};`
      }
      return ''
    },
    formatRadius (radius) {
      if (radius.show) {
        return `border-radius: ${radius.val}px;`
      }
      return ''
    }
  }
};
</script>

<style lang="scss" scoped>
/**
 在此处添加组件 css 样式（用 scoped 声明隔离其他组件样式，支持纯css、scss、less。）
 注：组件根元素的宽高系统自动设定，不需在此写死宽高，此处宽高用百分比适配。
 */
.module-panel-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .isActive {
    border: 1px solid #2681ff !important;
  }
}
</style>
