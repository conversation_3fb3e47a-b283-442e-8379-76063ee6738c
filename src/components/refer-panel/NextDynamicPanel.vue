<template>
  <div class="next-dynamic-container" ref="container">
    <div class="ct-wrapper" :key="key">
      <template v-if="screens.length">
        <Panel
          ref="screen"
          :screenLayers="screenLayers"
          :screenFilters="filtersMap"
          :screenStyle="screenConfig"
          :sceneConfig="sceneConfig"
          :screenInfo="screenInfo"
          :mainScreenId="mainScreenId"
          :workspaceId="workspaceId"
          :screenComs="screenComs"
          :platform="panelPlatform"
          :isView="isView"
          :heightAuto="heightAuto"
          @loaded="screenLoaded"
          @mounted="$emit('mounted')"
          v-on="listeners"></Panel>
      </template>
    </div>
    <seatom-loading v-if="showLoading || !loaded" :style="{ maxHeight: panelPlatform == 'mobile' ? '300px' : 'unset' }"></seatom-loading>
    <div class="empty__bg" :class="{ transparent: showBg }" v-if="showEmpty">
      <span v-if="!isView">右侧面板设置面板状态</span>
    </div>
  </div>
</template>
<script>
import Panel from './components/Panel.vue'
import comp from '@/mixins/comp'
import emitter from '@/utils/bus'
import { uuid, replaceUrl, findImageUrl } from '@/utils/base'
import { off, on } from '@/mixins/comp-evts'
import { addImportComponents } from '@/utils/systemImport'
// import { getScreen } from '@/api/screen'
// import { sendCustomReq } from '@/api/common'
export default {
  name: 'NextDynamicPanel',
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    platform: {
      type: String,
      default: 'pc'
    },
    mainScreenId: {
      type: Number,
      required: true
    }
  },
  components: {
    Panel
  },
  inject: ['callbackManager', 'permissionMap'],
  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager(),
      permissionFatherMap: () => this.permissionFatherMap
    }
  },
  mixins: [comp, { methods: { off, on } }],
  computed: {
    screens () {
      return this.config.screens || []
    },
    pages () {
      return this.config.pages || []
    },
    showEmpty () {
      return !this.screens.length || !this.pages.length
    },
    listeners () {
      const listeners = this.$listeners
      delete listeners.mounted
      delete listeners.compLoaded
      return listeners
    }
  },
  data () {
    return {
      key: uuid(),
      showLoading: false,
      showBg: true,
      loaded: false,
      params: null,
      heightAuto: false,
      screenLayers: [],
      screenInfo: {},
      workspaceId: '',
      screenConfig: {},
      sceneConfig: [],
      screenName: '',
      screenComs: {},
      filtersMap: {},
      changeEvents: [], // 暂存切换状态的事件
      panelPlatform: this.platform,
      permissionFatherMap: new Map()
    }
  },
  created () {
    if (!this.isView) {
      emitter.on('DynamicPanelState', this.changePanelState)
      this.$once('hook:beforeDestroy', () => {
        emitter.off('DynamicPanelState', this.changePanelState)
      })
    }
    this.fetchData().then(() => {
      this.$emit('dataLoad', this.screenName)
      this.$nextTick(() => {
        this.$refs.screen && this.$refs.screen.initParentStyle()
        if (this.changeEvents.length) {
          this.changeStatus(this.changeEvents.pop())
        }
      })
    })
  },
  methods: {
    async fetchData () {
      const screenId = this.screens[0]?.id
      if (!screenId) {
        this.loaded = true
        return
      }

      let screenData
      // 大屏数据，如果传入不为空，则直接使用
      if (this.screenData) {
        screenData = this.screenData
      } else {
        this.showLoading = true
        // screenData = (await getScreen({ id: screenId })).data
        screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId })
        this.showLoading = false
      }
      addImportComponents(screenData.components)
      Object.values(screenData).forEach(item => {
        findImageUrl(item, replaceUrl)
      })
      this.screenInfo = screenData;
      this.permissionFatherMap = this.permissionMap()
      // if (this.permissionMap && this.permissionMap()) {
      //   this.permissionFatherMap = this.permissionMap()
      // } else {
      //   await this.handlePermissionData()
      // }
      this.workspaceId = screenData.workspaceId
      this.screenType = screenData.screenType
      // if (this.screenType === 'scene') {
      this.sceneConfig = screenData.sceneConfig
      if (this.sceneConfig.length) {
        this.$refs.screen.setPage(this.sceneConfig[0].sceneId, this.sceneConfig[0].pageList[0].pageId)
      }
      // }
      this.panelPlatform = screenData.type
      this.screenName = screenData.name
      this.screenComs = screenData.components
      // this.layerTree = new Tree({ id: 'root', children: screenData.layers });
      this.$nextTick(() => {
        this.$refs.screen.initCallbackManager()
        this.screenConfig = screenData.config
        this.screenLayers = screenData.layers
        let layers = []
        const pageId = this.sceneConfig[0].pageList[0].pageId
        if (pageId) {
          layers = this.screenLayers.filter(layer => layer.pageId === pageId || (layer.sceneId === this.sceneId && !layer.pageId))
        } else {
          layers = this.screenLayers.filter(layer => layer.sceneId === this.sceneId && !layer.pageId)
        }
        if (!layers.length) {
          this.screenLoaded()
          this.$emit('mounted')
        }
        this.filtersMap = _.keyBy(screenData.filter, 'id')
        if (this.panelPlatform === 'mobile') {
          this.$nextTick(() => {
            this.$refs.screen.getLayout();
            if (this.heightAuto) {
              this.$refs.screen.domObserver()
            }
          })
        }
      })
    },
    init () {
      this.container = this.$refs.container
      // 移动端高度自适应
      const { mobileSetting = { heightAuto: false } } = this.config;
      this.heightAuto = (mobileSetting.heightAuto && this.isView);
    },
    async render () {
      const {
        background = {},
        border = {}
      } = this.config

      this.showBg = background.show;
      // 声明容器的样式
      let containerCssText = ''
      // 格式化背景样式
      containerCssText += this.formatBackground(background)
      // 格式化边框样式
      containerCssText += this.formatBorder(border)
      this.container.style.cssText = containerCssText
    },
    resize: _.debounce(function ({ width, height }) {
      if (!this.isView) this.key = uuid();
      this.render()
    }, 500),
    clear () {},
    destroy () {},
    formatBackground (background = {}) {
      if (background.show === false) {
        return ''
      }
      let backgroundStyle
      const { gradient = {}, image = {} } = background
      switch (background.type) {
        case 'pure':
          backgroundStyle = {
            'background-color': background.pure
          }
          break
        case 'gradient':
          backgroundStyle = {
            'background-image': `linear-gradient(${gradient.deg}deg, ${gradient.start}, ${gradient.end})`
          }
          break
        case 'image':
          backgroundStyle = {
            'background-image': `url(${image.url})`,
            'background-size': image.size,
            'background-position': `${image.positionX} ${image.positionY}`,
            'background-repeat': image.repeat
          }
          break
      }
      return this.formatStyle(backgroundStyle)
    },
    formatBorder (border = {}) {
      if (border.show === false) {
        return ''
      }
      const borderStyle = {
        'border-style': border.style,
        'border-color': border.color,
        'border-width': `${border.width}px`
      }
      return this.formatStyle(borderStyle)
    },
    formatStyle (boxStyle = {}) {
      return (
        Object.keys(boxStyle)
          .map((key) => {
            const value = boxStyle[key]
            if (value !== undefined) {
              return key + ': ' + boxStyle[key]
            } else {
              return ''
            }
          })
          .join(';') + ';'
      )
    },
    async changeStatus (params) { // 切换场景
      if (!this.screenInfo.id) {
        this.changeEvents.push(params)
        return
      }
      const changeKey = params.changeKey
      const changeData = params.changeData
      const state = params.state
      try {
        if (changeKey) { // changeKey优先级高于changeData
          // this.loaded = false;
          this.$refs.screen.switchScene({ pageName: changeKey })
          this.emitPanelStatus(changeKey)
          return
        }
        if (changeData) {
          const key = changeData.key
          // this.loaded = false;
          this.$refs.screen.switchScene({ pageName: key })
          this.emitPanelStatus(key)
          return
        }
        if (state) {
          this.$refs.screen.switchScene({ sceneId: state })
        }
      } catch (e) {}
    },
    emitPanelStatus (name) {
      this.emit('changeStatus', {
        name
      })
    },
    screenLoaded () {
      this.loaded = true;
      // this.$emit('mounted')
      if (!this.isView) {
        this.params && this.changeStatus(this.params)
      }
    },
    changePanelState (params) { // 编辑页保持面板状态，防止刷新后回到第一个状态
      if (params.id === this.id) {
        this.params = params;
        this.changeStatus(params);
      }
    }
    // async handlePermissionData () {
    //   const {
    //     permissionDataConfig: {
    //       dataResponse: {
    //         source,
    //         sourceType
    //       },
    //       mappingRelation: {
    //         id,
    //         permission
    //       },
    //       tips,
    //       userPermissionList
    //     }
    //   } = this.screenInfo
    //   const params = source[sourceType].data;
    //   const { baseUrl } = params
    //   if (!baseUrl) {
    //     // this.$message.warn('请选择数据源')
    //     // this.loading = false
    //     return
    //   }
    //   const res = await sendCustomReq(params)
    //   if (res && res.success) {
    //     const permissionData = res.data.data || []
    //     const permissionObj = {}
    //     permissionData.forEach(item => {
    //       permissionObj[item[id]] = !!item[permission]
    //     })
    //     userPermissionList.filter(item => {
    //       return !!item.componentsId.length && !!item.id
    //     }).forEach(item => {
    //       this.permissionFatherMap.set(item.componentsId, { result: permissionObj[item.id], tips })
    //     })
    //   }
    // }
  }
}
</script>
<style lang="scss" scoped>
.next-dynamic-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .empty__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: #524b50;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    &.transparent {
      background: transparent;
    }
    span {
      font-size: 14px;
      color: #fff;
    }
  }
  .ct-wrapper {
    width: 100%;
    height: 100%;
  }
}
</style>
