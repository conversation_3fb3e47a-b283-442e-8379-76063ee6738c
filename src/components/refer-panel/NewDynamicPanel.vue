<template>
  <div class="dynamic-container" ref="container">
    <div class="container-wrap" :key="key">
      <template v-if="screens.length">
        <ViewScreen
          ref="screen"
          :screenId="screens[0].id"
          @loaded="screenLoaded"
          showLoading
          parent
          isContainer
          :heightAuto="heightAuto"
          :parentMainScreenId="mainScreenId"
          v-on="$listeners" />
      </template>
    </div>
    <seatom-loading v-if="showLoading"></seatom-loading>
    <div class="empty__bg" :class="{ transparent: showBg }" v-if="showEmpty">
      <span v-if="!isView">右侧面板设置面板状态</span>
    </div>
  </div>
</template>

<script>
import comp from '@/mixins/comp'
import emitter from '@/utils/bus'
import { uuid } from '@/utils/base'
import { off, on } from '@/mixins/comp-evts'
import quarkDom from 'hz-quark/dist/dom'
export default {
  name: 'NewDynamicPanel', // 新版动态面板
  components: {},
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    platform: {
      type: String,
      default: 'pc'
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },

  inject: ['callbackManager', 'permissionMap'],
  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager(),
      permissionFatherMap: () => this.permissionMap()
    }
  },
  mixins: [comp, { methods: { off, on } }],
  data () {
    return {
      key: uuid(),
      showLoading: false,
      showBg: true,
      loaded: false,
      params: null,
      heightAuto: false
    }
  },
  computed: {
    screens () {
      return this.config.screens || []
    },
    pages () {
      return this.config.pages || []
    },
    showEmpty () {
      return !this.screens.length || !this.pages.length
    }
  },
  created () {
    if (!this.isView) {
      emitter.on('DynamicPanelState', this.changePanelState)
      this.$once('hook:beforeDestroy', () => {
        emitter.off('DynamicPanelState', this.changePanelState)
      })
    }
  },
  methods: {
    init () {
      this.container = this.$refs.container
      // 移动端高度自适应
      const { mobileSetting = { heightAuto: false } } = this.config;
      this.heightAuto = (mobileSetting.heightAuto && this.isView);
    },
    async render () {
      const {
        background = {},
        border = {}
      } = this.config

      this.showBg = background.show;
      // 声明容器的样式
      let containerCssText = ''
      // 格式化背景样式
      containerCssText += quarkDom.formatBackground(background)
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border)
      this.container.style.cssText = containerCssText
    },
    resize: _.debounce(function ({ width, height }) {
      if (!this.isView) this.key = uuid();
      this.render()
    }, 500),
    clear () {},
    destroy () {},
    async changeStatus (params) { // 切换场景
      const changeKey = params.changeKey
      const changeData = params.changeData
      const state = params.state
      try {
        if (changeKey) { // changeKey优先级高于changeData
          this.loaded = false;
          this.$refs.screen.switchScene({ pageName: changeKey })
          this.emitPanelStatus(changeKey)
          return
        }
        if (changeData) {
          const key = changeData.key
          this.loaded = false;
          this.$refs.screen.switchScene({ pageName: key })
          this.emitPanelStatus(key)
          return
        }
        if (state) {
          this.$refs.screen.switchScene({ sceneId: state })
        }
      } catch (e) {}
    },
    emitPanelStatus (name) {
      this.emit('changeStatus', {
        name
      })
    },
    screenLoaded () {
      this.loaded = true;
      if (!this.isView) {
        this.params && this.changeStatus(this.params)
      }
    },
    changePanelState (params) { // 编辑页保持面板状态，防止刷新后回到第一个状态
      if (params.id === this.id) {
        this.params = params;
        this.changeStatus(params);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .container-wrap {
    width: 100%;
    height: 100%;
  }
  .screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #434b55;
      border: 1px solid #434b55;
    }
  }
  .empty__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: #524b50;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    &.transparent {
      background: transparent;
    }
    span {
      font-size: 14px;
      color: #fff;
    }
  }
}
</style>
