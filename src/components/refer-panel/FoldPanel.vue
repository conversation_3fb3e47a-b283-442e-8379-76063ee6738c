<template>
  <div class="fold-panel-container" ref="container" :style="computedContainerStyle">
    <el-collapse
      ref="panel"
      v-model="activeName"
      class="fold-panel-collapse"
      :accordion="isAccordion"
      @change="panelChange"
    >
      <el-collapse-item
        v-for="(item, index) in foldListData"
        :key="index"
        :title="item[0].title"
        :name="index + 1"
      >
        <template slot="title" >
          <span class="title-name" v-if="titleConfig.toggleIcon.iconLocation==='right'" :style="computedFontStyle">
            {{ item[0].title }}
          </span>
          <span class="toggle-icon" :style="computedToggleIconStyle(index+1).parent">
            <SvgIcon class="complete" :name="computedToggleIconStyle(index+1).name" :style="computedToggleIconStyle(index+1).child"/>
          </span>
          <span class="title-name" v-if="titleConfig.toggleIcon.iconLocation==='left'" :style="computedFontStyle">
            {{ item[0].title }}
          </span>
          <span class="state-img" :style="computedStateImgStyle(index).style">
            <img :style="computedStateImgStyle(index).imgSize" :src="computedStateImgStyle(index).image">
          </span>
          <span class="go-icon" @click.stop=jumpbuttonClick(item) :style="computedGoIconStyle.style">
            <SvgIcon class="jumpIcon" :name="computedGoIconStyle.name" />
          </span>
        </template>
        <div :style="{'height': titleConfig.titleMargin + 'px'}"></div>
        <PitchViewScreen
          v-if="isLoadedScreen"
          :item-data="item"
          :screen-coms="pitchViews[index].screenComs"
          :screen-filters="childFiltersMap"
          :screen-layers="childScreenLayers"
          :screen-style="childScreenStyle"
          :workspace-id="childWorkspaceId"
          :platform="screenInfo.type"
          :heightAuto="heightAuto"
          :isView="isView"
          :mainScreenId="mainScreenId"
          v-on="$listeners"
        />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>
import comp from '@/mixins/comp'
import { formatBorder, formatBackground, formatPadding } from '@/utils/styleHandler'
import { updateScreenParentId } from '@/api/screen'
import SvgIcon from './components/SvgIcon.vue'
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'

export default {
  name: 'FoldPanel', // 折叠面板
  mixins: [comp],
  components: { SvgIcon },
  props: {
    id: {
      type: String,
      default: ''
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },
  inject: ['callbackManager'],
  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager()
    }
  },
  data () {
    return {
      isChange: false, // 点击改变了，用于展开和收起时的使用
      titleConfig: {},
      isAccordion: false, // 是否手风琴
      itemPanelList: [], // 折叠面板列表
      panelData: {}, // 面板数据
      activeName: [1],
      foldListData: [], // 折叠面板数据
      screenInfo: {},
      stateImgList: [],
      childScreenComps: {},
      childScreenLayers: [],
      childWorkspaceId: 0,
      childFiltersMap: {},
      childScreenStyle: {},
      listPitchData: [],
      itemStyle: {},
      hoverStyle: {},
      clickStyle: {},
      clickIndex: -1,
      hoverIndex: -1,
      pitchViews: [],
      heightAuto: false,
      showLoading: false,
      isLoadedScreen: false
    }
  },
  computed: {
    computedScreenStyle () {
      return index => {
        const style = { ...this.itemStyle }
        if (index === this.hoverIndex) {
          Object.assign(style, this.hoverStyle)
        }
        if (index === this.clickIndex) {
          Object.assign(style, this.clickStyle)
        }
        return style
      }
    },
    // 切换图标样式
    computedToggleIconStyle () {
      return index => {
        let parent = { display: 'none' }
        let child = {}
        let name = ''
        if (this.titleConfig?.toggleIcon) {
          const { toggleIcon: { iconfont = {}, leftMargin, isIconVisible } } = this.titleConfig
          const isOpen = (Array.isArray(this.activeName) && this.activeName.includes(index)) || this.activeName === index
          parent = {
            fontSize: iconfont.size + 'px',
            color: iconfont.color,
            'margin-left': leftMargin + 'px',
            display: isIconVisible ? 'inline-block' : 'none'
          }
          child = {
            transform: !isOpen ? 'rotate(-90deg)' : 'rotate(0deg)',
            transitionDuration: !isOpen ? '.5s' : '0s'
          }
          name = iconfont.iconName
        }
        return {
          parent,
          child,
          name
        }
      }
    },
    // 跳转图标样式
    computedGoIconStyle () {
      let style = { display: 'none' }
      let name = ''
      if (this.titleConfig?.iconGo) {
        const { iconGo: { iconfont, leftMargin, isIcon } } = this.titleConfig
        style = {
          fontSize: iconfont.size + 'px',
          color: iconfont.color,
          'margin-right': (100 - leftMargin) + '%',
          display: isIcon ? 'inline-block' : 'none'
        }
        name = iconfont.iconName
      }
      return {
        style,
        name
      }
    },
    // 状态图片样式
    computedStateImgStyle () {
      return (index) => {
        const { show = false, height = 20, width = 20, image = '', leftMargin = 0 } = this.stateImgList[index] || {}
        const style = {
          display: show ? 'inline-block' : 'none',
          'margin-left': leftMargin + 'px'
        }
        const imgSize = {
          height: height + 'px',
          width: width + 'px'
        }
        return {
          style,
          imgSize,
          image
        }
      }
    },
    // 标题字体样式
    computedFontStyle () {
      const { text } = this.titleConfig
      let style = {}
      if (text) {
        style = {
          display: text.isTextVisible ? 'inline-block' : 'none',
          color: text.font.color,
          'font-family': text.font.fontFamily,
          'font-size': text.font.fontSize + 'px',
          'font-weight': text.font.fontWeight,
          'padding-left': text.leftMargin + 'px'
          // padding: `${this.titleConfig.padding?.top}px ${this.titleConfig.padding?.right}px ${this.titleConfig.padding?.bottom}px ${this.titleConfig.padding?.left}px`
        }
      }
      return style
    },
    // 容器样式
    computedContainerStyle () {
      const {
        base = {},
        background = {},
        border = {},
        panel = {},
        scroll = {},
        titleConfig = {}
      } = this.config
      // 整体容器
      let containerCssText = ''
      containerCssText += formatBackground(background)
      containerCssText += formatBorder(border)
      containerCssText += formatPadding(base.padding)
      const containerStyle = this.strToObj(containerCssText)
      const varStyle = {
        'flex-direction': base.mode,
        overflow: this.heightAuto ? 'hidden' : 'auto',
        '--scrollBtn': scroll.show ? '4px' : '0px',
        '--itemBorderRadius': (panel?.fillet || 0) + 'px',
        '--itemArrowDisplay': titleConfig?.iconGo || titleConfig?.toggleIcon?.isIconVisible ? 'none' : 'inline-block',
        '--itemArrowColor': titleConfig?.toggleIcon?.iconColor || '#333',
        '--titleContentTop': `-${titleConfig.titleBorder.show ? titleConfig.titleBorder.width : 0}px`,
        '--headerBgColor': titleConfig.titleBackground.show ? 'transparent' : '#fff'
      }
      const style = Object.assign(containerStyle, varStyle)
      return style
    },
    // 标题容器样式
    computedHeaderCss () {
      const { titleConfig, panel = {} } = this.config
      let headerInfoCss = ''
      if (titleConfig) {
        const borderS = titleConfig.titleBorder.show ? '' : 'border:none;'
        headerInfoCss =
          formatBackground(titleConfig.titleBackground) +
          formatBorder(titleConfig.titleBorder) +
          borderS +
          `display: block;
          text-align: ${titleConfig.alignment};
          height: ${titleConfig.height}px;
          line-height: ${titleConfig.height}px;
          border-top-left-radius: ${panel.fillet}px;
          border-top-right-radius: ${panel.fillet}px;`
      }
      return headerInfoCss
    },
    screenList () {
      return this.config.screens || []
    },
    initData () {
      return this.data?.[0]?.data || this.data.map(item => [item])
    }
  },
  watch: {
    activeName: {
      deep: true,
      handler (newValue, oldValue) {
        if (this.isChange) {
          let name = ''
          if (newValue.length > oldValue.length) { // 展开
            const thisIndex = newValue.filter(item => !oldValue.includes(item))[0]
            this.foldListData.forEach((item, index) => {
              if (index + 1 === thisIndex) {
                name = item[0].title
              }
            })
            this.emit('panelOpen', name)
          } else if (!Array.isArray(newValue) && newValue) {
            const thisIndex = newValue
            this.foldListData.forEach((item, index) => {
              if (index + 1 === thisIndex) {
                name = item[0].title
              }
            })
            this.emit('panelOpen', name)
          } else if (!Array.isArray(newValue) && !newValue) {
            const thisIndex = Array.isArray(oldValue) ? oldValue[0] : oldValue
            this.foldListData.forEach((item, index) => {
              if (index + 1 === thisIndex) {
                name = item[0].title
              }
            })
            this.emit('panelClose', name)
          } else { // 收起
            const thisIndex = oldValue.filter(item => !newValue.includes(item))[0]
            this.foldListData.forEach((item, index) => {
              if (index + 1 === thisIndex) {
                name = item[0].title
              }
            })
            this.emit('panelClose', name)
          }
        }
      }
    }
  },
  methods: {
    jumpbuttonClick (item) {
      this.emit('jumpbuttonClick', {
        name: item[0].title,
        data: item[0]
      })
      this.seatom_updateCallbackValue({
        name: item[0].title,
        data: item[0]
      })
    },
    init () {
      this.container = this.$refs.container
      // 移动端高度自适应
      const { mobileSetting = { heightAuto: false } } = this.config;
      if (mobileSetting.heightAuto && this.isView) {
        this.heightAuto = mobileSetting.heightAuto;
        const dom = this.$refs.panel.$el;
        this.$nextTick(() => {
          this.handlerAutoHeight && this.handlerAutoHeight(dom);
        })
      }

      const { titleConfig = {} } = this.config
      this.loadIconFontFile(
       titleConfig.toggleIcon?.iconfont?.iconType,
       titleConfig.toggleIcon?.iconfont?.sourceType,
       titleConfig.toggleIcon?.iconfont?.url
      );
      this.loadIconFontFile(
       titleConfig.iconGo?.iconfont?.iconType,
       titleConfig.iconGo?.iconfont?.sourceType,
       titleConfig.iconGo?.iconfont?.url
      );
    },
    async render () {
      const {
        panel = {},
        titleConfig = {}
      } = this.config
      this.panelData = panel
      this.titleConfig = titleConfig
      this.stateImgList = this.getImageData()

      await this.getScreenInfo()

      this.foldListData = _.cloneDeep(this.initData)
      this.pitchViews = this.foldListData.map(item => {
        return {
          screenComs: _.cloneDeep(this.childScreenComps)
        }
      })

      // 面板默认全部展开，防止面板内容高度加载错误
      this.activeName = this.initData.map((item, index) => index + 1)

      this.$nextTick(() => {
        const itemPanelList = Array.from(this.$refs.container.getElementsByClassName('el-collapse-item'))
        itemPanelList.forEach((item, index) => {
          item.querySelector('.el-collapse-item__header').style.cssText = this.computedHeaderCss
        })
        // 初始化功能配置
        this.initActiveNames()
      })
    },
    async getScreenInfo () {
      if (!this.screenList.length) {
        this.showLoading = false
        return
      }

      if (this.showLoading || this.isLoadedScreen) {
        return
      }

      this.showLoading = true

      const screenData = await this.$store.dispatch('editor/getPreviewScreen', { screenId: this.screenList[0]?.id })

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.mainScreenId &&
         Number(this.screenInfo.parentId) !== this.mainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.mainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }

      this.childScreenStyle = screenData.config
      this.childScreenStyle.height = this.panelData.panelHeight
      this.childWorkspaceId = screenData.workspaceId
      this.childScreenComps = screenData.components
      this.childScreenLayers = screenData.layers
      this.childFiltersMap = _.keyBy(screenData.filter, 'id')
      this.showLoading = false
      this.isLoadedScreen = true
    },
    resize ({ width, height }) {},
    clear () {},
    destroy () {},
    // 打开折叠面板
    openOnePanel (name) {
      let thisIndex = 0
      this.foldListData.forEach((item, index) => {
        if (item[0].title === name) {
          thisIndex = index + 1
        }
      })
      if (!this.activeName.includes(thisIndex)) {
        this.activeName.push(thisIndex)
      }
    },
    // 关闭折叠面板
    closeOnePanel (name) {
      let thisIndex = 0
      this.foldListData.forEach((item, index) => {
        if (item[0].title === name) {
          thisIndex = index + 1
        }
      })
      if (this.activeName.includes(thisIndex)) {
        this.activeName = this.activeName.filter(item => item !== thisIndex)
      }
    },
    // 打开所有面板
    openAllPanel () {
      this.activeName = this.foldListData.map((item, index) => index + 1)
    },
    // 关闭所有面板
    closeAllPanel () {
      this.activeName = []
    },
    panelChange (activeNames) {
      this.isChange = true
    },
    getImageData () {
      const { config, data } = this
      if (config.condition && config.condition.length && config.condition.length > 0) {
        const tempObj = this.conditionalFilter(config.condition, data)
        const tempList = Object.keys(tempObj).map(item => (tempObj[item] && tempObj[item].stateImg) || {})
        return tempList
      }
      return []
    },
    initActiveNames () {
      const { domain = {} } = this.config
      this.isAccordion = domain.accordion
      const timer = setTimeout(() => {
        // 功能配置: 手风琴模式及默认展开
        if (domain.accordion && domain.panelOpen) {
          this.activeName = [1]
        }
        if (!domain.panelOpen) {
          this.activeName = []
        }
      }, 50)
      this.$once('hook:beforeDestroy', () => {
        clearInterval(timer)
      })
    },
    strToObj (str) {
      return str.split(';').reduce((acc, pre) => {
        if (pre) {
          const temp = pre.split(': ')
          acc[temp[0]] = temp[1]
        }
        return acc
      }, {})
    }
  }
}
</script>
<style lang="scss" scoped>
.fold-panel-container{
  width: 100%;
  height: 100%;
  overflow: auto;
  &.hidden {
    overflow: hidden !important;
  }
  .fold-panel-collapse{
    ::v-deep .el-collapse-item{
      overflow: hidden;
      border-top-left-radius: var(--itemBorderRadius);
      border-top-right-radius: var(--itemBorderRadius);
    }
    ::v-deep .el-collapse-item__content{
      height: 100%;
      padding-bottom: 0px;
    }
    ::v-deep .el-collapse-item__wrap{
      border-bottom: none;
      background-color: transparent;
    }
    ::v-deep .el-collapse-item__arrow{
      display: var(--itemArrowDisplay);
      color: var(--itemArrowColor);
    }
    ::v-deep .el-collapse-item__header{
      width: 100%;
      border-bottom: 0;
      background-color: var(--headerBgColor);
    }
    .title-name{
      margin-top: var(--titleContentTop);
      vertical-align: top;
      margin-right: 5px;
    }
    .state-img{
      margin-top: var(--titleContentTop);
      vertical-align: top;
    }

    .toggle-icon{
      margin-top: var(--titleContentTop);
      vertical-align: top;
      svg {
        vertical-align: middle;
      }
    }

    .go-icon{
      float: right;
      margin-top: var(--titleContentTop);
      vertical-align: top;
      svg {
        vertical-align: middle;
      }
    }
  }
  &::-webkit-scrollbar {
    width: var(--scrollBtn) !important;
  }
  // /*滚动条区域*/
  // &::-webkit-scrollbar {
  //   width: var(--scrollWidth) !important;
  //   // background-color: #fff !important;
  // }
  // /*滚动条*/
  // &::-webkit-scrollbar-thumb {
  //   background-color:  var(--scrollBoxColor) !important;
  //   border-radius: var(--scrollBoxRound) !important;
  // }
  // /*滚动条外层轨道*/
  // &::-webkit-scrollbar-track {
  //   // -webkit-box-shadow: inset 0 0 1px;
  //   width: var(--scrollWidth) !important;
  //   background-color: var(--scrollLineColor) !important;
  //   border-radius: var(--scrollRound) !important;
  // }
}
</style>
