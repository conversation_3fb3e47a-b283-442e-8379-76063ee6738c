<template>
  <div class="dynamic-container" ref="container">
    <div class="container-wrap" :key="key">
      <template v-for="item in screenList">
        <div
          :key="id + '_' + item.id"
          v-if="item.id == active"
          class="screen">
          <keep-alive>
            <ViewScreen
              :screenId="item.id"
              @loaded="screenLoaded"
              showLoading
              parent
              isContainer
              :heightAuto="heightAuto"
              :parentMainScreenId="mainScreenId"
              v-on="$listeners" />
          </keep-alive>
        </div>
      </template>
    </div>
    <seatom-loading v-if="showLoading"></seatom-loading>
    <div class="empty__bg" :class="{ transparent: showBg }" v-if="showEmpty">
      <span v-if="!isView">右侧面板设置面板状态</span>
    </div>
  </div>
</template>

<script>
import comp from '@/mixins/comp'
import emitter from '@/utils/bus'
import { uuid } from '@/utils/base'
import { off, on } from '@/mixins/comp-evts'
import quarkDom from 'hz-quark/dist/dom'

export default {
  name: 'DynamicPanel', // 动态面板
  components: {},
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    platform: {
      type: String,
      default: 'mobile'
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },
  inject: ['callbackManager', 'permissionMap'],
  provide: function () {
    return {
      callbackFatherManager: () => this.callbackManager(),
      permissionFatherMap: () => this.permissionMap()
    }
  },
  mixins: [comp, { methods: { off, on } }],
  data () {
    return {
      active: null,
      showLoading: false,
      showBg: true,
      key: uuid(),
      heightAuto: false
    }
  },
  computed: {
    screenList () {
      return this.config.screens || []
    },
    showEmpty () {
      return !this.screenList.length || !this.active
    }
  },
  created () {
    if (!this.isView) {
      emitter.on('DynamicPanelState', this.changePanelState)
      this.$once('hook:beforeDestroy', () => {
        emitter.off('DynamicPanelState', this.changePanelState)
      })
    }
  },
  methods: {
    init () {
      this.container = this.$refs.container;
      // 移动端高度自适应
      const { mobileSetting = { heightAuto: false } } = this.config;
      this.heightAuto = (mobileSetting.heightAuto && this.isView);
    },
    async render () {
      const {
        screens = [],
        background = {},
        border = {}
      } = this.config

      const screenList = screens
      if (screenList.length && !this.active) {
        this.active = screenList[0].id
        this.emitPanelStatus(this.active)
      }

      this.showBg = background.show;
      // 声明容器的样式
      let containerCssText = ''
      // 格式化背景样式
      containerCssText += quarkDom.formatBackground(background)
      // 格式化边框样式
      containerCssText += quarkDom.formatBorder(border)
      this.container.style.cssText = containerCssText
    },
    resize: _.debounce(function ({ width, height }) {
      if (!this.isView) this.key = uuid();
      this.render()
    }, 500),
    clear () {},
    destroy () {},
    changeStatus (params) {
      const changeKey = params.changeKey
      const changeData = params.changeData
      try {
        if (changeKey) { // changeKey优先级高于changeData
          // eslint-disable-next-line eqeqeq
          const index = this.screenList.findIndex(item => item.key == changeKey)
          if (index > -1) {
            this.active = this.screenList[index].id
            this.emitPanelStatus(this.active)
          }
          return
        }
        if (changeData) {
          const key = changeData.key
          // eslint-disable-next-line eqeqeq
          const index = this.screenList.findIndex(item => item.key == key)
          if (index > -1) {
            this.active = this.screenList[index].id
            this.emitPanelStatus(this.active)
          }
        }
      } catch (e) {}
    },
    changePanelState ({ id, state }) { // 右侧设置面板切换状态同步切换动态面板状态
      if (id === this.id) {
        this.active = state
      }
    },
    emitPanelStatus (name) {
      this.emit('changeStatus', {
        name
      })
    },
    screenLoaded () {
      this.loaded = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .container-wrap {
    width: 100%;
    height: 100%;
  }
  .screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #434b55;
      border: 1px solid #434b55;
    }
  }
  .empty__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: #524b50;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    &.transparent {
      background: transparent;
    }
    span {
      font-size: 14px;
      color: #fff;
    }
  }
}
</style>
