<template>
  <div class="scene-footer">
    <div class="scene">
      <el-select class="scene-select" v-model="sId" size="mini" @change="sceneChange">
        <el-option v-for="item in sceneConfig" :key="item.sceneId" :value="item.sceneId" :label="item.sceneName"></el-option>
      </el-select>
    </div>
    <div class="page">
      <div class="page-prev hv" @click="toPrev">
        <i class="el-icon el-icon-arrow-left"></i>
      </div>
      <div class="page-count">{{ pageNum }}/{{ pageList.length }}</div>
      <div class="page-next hv" @click="toNext">
        <i class="el-icon el-icon-arrow-right"></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SceneFooter', // 场景页脚组件
  props: {
    sceneConfig: {
      type: Array,
      default () {
        return []
      }
    },
    sceneId: String,
    pageId: String
  },
  data () {
    return {}
  },
  computed: {
    sId: {
      get: function () {
        return this.sceneId
      },
      set: function (val) {
        this.$emit('update:sceneId', val);
      }
    },
    pageList () {
      const list = [];
      this.sceneConfig.forEach(scene => {
        list.push(...scene.pageList);
      })
      return list
    },
    pageNum () {
      const index = this.pageList.findIndex(page => page.pageId === this.pageId);
      if (index > -1) {
        return index + 1
      }
      return 0
    }
  },
  created () {
    // this.sceneId = this.sceneConfig[0].sceneId;
  },
  methods: {
    toPrev () {
      if (this.pageNum <= 1) {
        return
      }
      const page = this.pageList[this.pageNum - 2];
      this.$emit('update:pageId', page.pageId);
      this.switchScene(page.pageId);
    },
    toNext () {
      if (this.pageNum >= this.pageList.length) {
        return
      }
      const page = this.pageList[this.pageNum];
      this.$emit('update:pageId', page.pageId);
      this.switchScene(page.pageId);
    },
    switchScene (pageId) {
      const scene = this.sceneConfig.find(item => {
        return item.pageList.findIndex(page => page.pageId === pageId) > -1
      })
      if (scene) {
        this.sId = scene.sceneId;
      }
    },
    sceneChange (sceneId) {
      if (!sceneId) {
        const page = this.pageList[0];
        this.$emit('update:pageId', page.pageId);
      } else {
        const scene = this.sceneConfig.find(item => item.sceneId === sceneId);
        if (scene) {
          const pageList = scene.pageList;
          this.$emit('update:pageId', pageList[0].pageId);
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.scene-footer {
  position: fixed;
  display: flex;
  align-items: center;
  right: 40px;
  bottom: 20px;
  width: 210px;
  height: 35px;
  color: #fff;
  background: #20202b;
  padding: 0 10px;
  .scene {
    width: 95px;
  }
  .page {
    width: 95px;
    display: flex;
    justify-content: center;
    .page-prev, .page-next, .page-count {
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
      &.hv:hover {
        background-color: #2a2b37;
        i {
          color: #fff;
        }
      }
    }
    .page-count {
      width: 30px;
      cursor: auto;
    }
  }
  .scene-select {
    ::v-deep {
      .el-input__inner {
        border: none;
        background-color: #20202b;
      }
    }
  }
}
</style>
