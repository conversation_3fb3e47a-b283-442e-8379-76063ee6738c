<!--
 * @Author: your name
 * @Date: 2022-03-01 16:46:45
 * @LastEditTime: 2023-06-30 10:41:49
 * @LastEditors: chenxingyu
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /seatom/src/components/view/ViewGroupComp.vue
-->
<template>
  <div class="view-group-comp" :style="groupStyle">
    <template v-for="(item, index) in pageLayers">
      <template v-if="item.type === 'com'">
        <ViewCompNode
          :key="item.id"
          :workspaceId="workspaceId"
          :id="item.id"
          :mainScreenId="mainScreenId"
          :type="item.type"
          :item-data="itemData"
          :zIndex="pageLayers.length - index"
          :platform="platform"
          v-on="$listeners" />
      </template>
      <template v-else>
        <ViewGroupComp
          :key="item.id"
          :workspaceId="workspaceId"
          :id="item.id"
          :mainScreenId="mainScreenId"
          :pageLayers="item.children"
          :item-data="itemData"
          :zIndex="pageLayers.length - index"
          :platform="platform"
          v-on="$listeners" />
      </template>
    </template>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'ViewGroupComp',
  props: {
    workspaceId: {
      type: Number,
      required: true
    },
    id: {
      type: String,
      required: true
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    },
    pageLayers: {
      type: Array,
      default: () => ([])
    },
    itemData: {
      type: Array,
      default: () => ([])
    },
    zIndex: {
      type: Number,
      default: 0
    },
    platform: {
      type: String,
      default: 'pc'
    }
  },
  data () {
    return {}
  },
  computed: {
    ...mapState({
      linkComps: state => state.view.linkComps
    }),
    groupStyle () {
      return {
        position: 'absolute',
        zIndex: this.hasHighLight ? 'unset' : this.zIndex
      }
    },
    hasHighLight () {
      const comps = this.linkComps?.comps || [];
      return !!comps.length
    }
  }
}
</script>
