<template>
  <div class="screen-view-wrap_box">
    <div class="screen-view-wrap" :style="pitchStyle">
      <template v-if="platform === 'pc'">
        <el-form ref="form" :rules="rules" :model="ruleForm">
          <template v-for="(item, index) in screenLayers">
            <template v-if="item.type === 'com'">
              <template v-if="formItemIds.includes(item.id)">
                <el-form-item :key="item.id" :prop="formItemId(item.id)" :style="compWrapStyle(item.id, index)">
                  <ViewCompNode
                    ref="formCompRef"
                    :key="item.id"
                    :workspaceId="workspaceId"
                    :id="item.id"
                    :mainScreenId="mainScreenId"
                    :type="item.type"
                    :item-data="itemData"
                    :zIndex="screenLayers.length - index"
                    :platform="platform"
                    v-on="$listeners"
                    isContainer
                    />
                </el-form-item>
              </template>
              <template v-else>
                <ViewCompNode
                  :key="item.id"
                  :workspaceId="workspaceId"
                  :id="item.id"
                  :mainScreenId="mainScreenId"
                  :type="item.type"
                  :item-data="itemData"
                  :zIndex="screenLayers.length - index"
                  :platform="platform"
                  v-on="$listeners" />
              </template>
            </template>
            <!-- <template v-else>
              <ViewGroupComp
                :key="item.id"
                :workspaceId="workspaceId"
                :id="item.id"
                :mainScreenId="mainScreenId"
                :pageLayers="item.children"
                :item-data="itemData"
                :zIndex="screenLayers.length - index"
                :platform="platform"
                v-on="$listeners" />
            </template> -->
          </template>
        </el-form>
      </template>
      <!-- <template v-else>
        <grid-layout
          ref="gridlayout"
          :style="{
            width: screenStyle.width + 'px'
          }"
          :layout.sync="layout"
          :col-num="24"
          :row-height="10"
          :is-draggable="false"
          :is-resizable="false"
          :is-mirrored="false"
          :vertical-compact="true"
          :margin="[5, 5]"
          :use-css-transforms="true"
          isView>
          <grid-item
            v-for="(item, index) in layout"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
            :key="item.id"
            :style="{
              zIndex: layout.length - index
            }">
            <ViewCompNode
              :workspaceId="workspaceId"
              :id="item.id"
              :mainScreenId="mainScreenId"
              :type="item.type"
              :item-data="itemData"
              :zIndex="layout.length - index"
              :platform="platform"
              v-on="$listeners"
              @seatom_setComConfig="handleSetComConfig" />
          </grid-item>
        </grid-layout>
      </template> -->
    </div>
  </div>
</template>

<script>
import { M_ZERO_HEIGHT } from '@/common/constants'
// import VueGridLayout from '@/lib/vue-grid-layout.common'
import { isFormComp } from '@/utils/base'

export default {
  name: 'FormViewScreen',

  // components: {
  //   GridLayout: VueGridLayout.GridLayout,
  //   GridItem: VueGridLayout.GridItem
  // },

  props: {
    showScroll: { // 是否显示滚动条
      type: Boolean,
      default: false
    },
    itemData: {
      type: Array,
      default: () => []
    },
    isContainer: { // 是否是容器
      type: Boolean,
      default: false
    },
    screenStyle: {
      type: Object,
      default: () => ({})
    },
    screenFilters: {
      type: Object,
      default: () => ({})
    },
    screenComs: {
      type: Object,
      default: () => {}
    },
    screenLayers: {
      type: Array,
      default: () => []
    },
    workspaceId: {
      type: [Number, String],
      default: 0
    },
    platform: {
      type: String,
      default: 'pc'
    },
    isView: {
      type: Boolean,
      default: false
    },
    heightAuto: {
      type: Boolean,
      default: false
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },

  inject: ['callbackFatherManager', 'permissionMap', 'formPanel'],
  provide: function () {
    return {
      filtersMap: () => this.screenFilters,
      screenComs: () => this.screenComs,
      callbackManager: () => this.callbackManager,
      permissionMap: this.permissionMap
    }
  },
  data () {
    return {
      loadCount: 0,
      ruleForm: {}, // 表单数据
      rules: {}
    }
  },

  computed: {
    compCount () {
      const showLayers = this.screenLayers.filter(l => {
        return this.screenComs[l.id] && this.screenComs[l.id].show
      })
      return showLayers.length
    },
    layout () {
      const { screenComs, screenLayers } = this;
      function getMobileHeight (comCfg) { // 获取移动端组件h
        if (!comCfg.show) return M_ZERO_HEIGHT;
        return comCfg.attr.h
      }
      return screenLayers.map(item => {
        const com = screenComs[item.id];
        return {
          ...com,
          x: com.attr.x,
          y: com.attr.y,
          w: com.attr.w,
          h: getMobileHeight(com),
          i: item.id,
          id: item.id,
          type: item.type
        }
      })
    },
    pitchStyle () { // screenStyle实际是大屏的config配置，需要转换成真实的大屏的style
      const { screenStyle } = this;
      const { width, height, backgroundColor, backgroundImage, backgroundRepeat, globalFilterParams } = screenStyle;
      const setBackground = (image, repeat = 'fill') => {
        let cssText = ''
        if (!image) return cssText
        switch (repeat) {
          case 'fill':
            cssText += ` url(${image}) left top/100% 100% no-repeat`
            break
          case 'contain':
            cssText += ` url(${image}) center/contain no-repeat`
            break
          case 'cover':
            cssText += ` url(${image}) left top/cover no-repeat`
            break
          case 'repeat':
            cssText += ` url(${image}) left top/auto repeat`
            break
          default:
            break
        }
        return cssText
      }
      const bgImage = setBackground(backgroundImage, backgroundRepeat);
      const background = backgroundImage ? bgImage : backgroundColor
      const style = {
        width: width + 'px',
        height: height + 'px',
        background
      }
      if (globalFilterParams?.enable) {
        const { hue, saturate, brightness, contrast, opacity, grayscale } = globalFilterParams
        style.filter = `
          hue-rotate(${hue}deg)
          saturate(${saturate}%)
          brightness(${brightness}%)
          contrast(${contrast}%)
          opacity(${opacity}%)
          grayscale(${grayscale}%)
        `
      }
      if (this.showScroll) {
        style.overflow = 'auto'
      }
      if (this.platform === 'mobile') {
        style.width = '100%';
        if (this.heightAuto && this.isView) { // 预览状态高度设置auto，否则编辑页展示有问题
          style.height = 'auto !important'
        }
      }
      return style
    },
    compWrapStyle () {
      return (id, index) => {
        const { screenComs } = this;
        const curCom = screenComs[id]
        const { platform } = this
        const { x, y, w, h } = curCom.attr
        if (_.isEmpty(curCom)) return {};

        if (platform === 'pc') {
          return {
            width: w + 'px',
            height: h + 'px',
            left: x + 'px',
            top: y + 'px',
            zIndex: this.screenLayers.length - index
          }
        } else {
          // return {
          //   width: isContainer ? `${((w / 24) * 100)}%` : '100%',
          //   height: isContainer ? `${h * 10}px` : '100%',
          //   zIndex
          // }
        }
      }
    },
    formItems () {
      return Object.values(this.screenComs).filter(item => {
        return isFormComp(item.comType)
      })
    },
    formItemIds () {
      return this.formItems.map(item => item.id)
    },
    formItemId () {
      return (id) => {
        return this.screenComs[id]?.config?.rules?.itemId || id
      }
    }
  },

  created () {
    this.initCallbackManager()
  },
  mounted () {
  },
  methods: {
    generateRules () {
      const result = {};
      let hasResult = false;
      this.ruleTemplate()
        .filter((e) => typeof e.rule !== 'undefined')
        .forEach((e) => {
          if (Array.isArray(e.rule)) {
            hasResult = true;
            result[e.key] = e.rule;
          }
        });
      if (hasResult) {
        this.rules = result;
      }
    },
    ruleTemplate () {
      return Object.values(this.screenComs).filter(item => isFormComp(item.comType))
        .map(item => {
          const regularRules = item.config.rules.regular.map(ruleItem => {
            return {
              validator: (rule, value, callback) => {
                // eslint-disable-next-line
                const func = new Function('value', `return ${ruleItem.regularItem}`)
                const ruleValue = func(value)
                if (ruleValue) {
                  callback(new Error(ruleItem.errorText))
                } else {
                  callback();
                }
              }
            }
          })
          const generalRules = item.config.rules.general.map(ruleItem => {
            return ruleItem.ruleType === 'required' ? {
              required: true,
              message: ruleItem.errorText
            } : {
              type: ruleItem.ruleType,
              message: ruleItem.errorText
            }
          })
          return {
            key: item.config.rules.itemId || item.id,
            rule: [...generalRules, ...regularRules]
          }
        })
    },
    submitForm (formName) {
      this.getFormValue(); // 获取表单组内getVlaue值
      this.generateRules(); // 获取表单内组件的校验规则
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.formPanel.emit('validatePass', this.ruleForm)
        } else {
          this.formPanel.emit('validateFail', this.ruleForm)
          return false;
        }
      });
    },
    getFormValue () {
      if (this.$refs.formCompRef) {
        this.ruleForm = this.$refs.formCompRef.reduce((prev, cur) => {
          const id = cur.$refs.compRef.config.rules.itemId || cur.$refs.compRef.id
          prev[id] = cur.$refs.compRef.getValue() || ''
          return prev
        }, {})
      }
    },
    initCallbackManager () {
      const { screenComs } = this
      if (!this.screenComs) return

      _.values(screenComs).forEach(com => {
        if (com.interactionConfig && com.interactionConfig.callbackParams) {
          com.interactionConfig.callbackParams.forEach(cb => {
            this.callbackFatherManager().addCallbackKey(cb.variableName)
          })
        }
      })
      this.callbackManager = this.callbackFatherManager()
    },
    handleSetComConfig (id, keyValPairs = []) { // 移动端设置组件config
      const com = this.screenComs[id]
      if (com) {
        keyValPairs.forEach(pair => {
          _.set(com, pair.key, pair.value);
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-view-wrap_box {
  position: relative;
  width: 100%;
  height: 100%;

  .el-form {
    height: 100%;
    width: 100%;

    .el-form-item {
      position: absolute;
      left: 0;
      top: 0;
    }

    ::v-deep {
      .el-form-item__content {
        position: static;
      }
      .el-form-item__error {
        color: var( --validate-color);
        font-size: var(--validate-fontSize);
        font-family: var(--validate-fontFamily);
        font-weight: var(--validate-fontWeight)
      }
    }
  }
}
.screen-view-wrap {
  // position: absolute;
  // left: 0;
  // top: 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transform-origin: left top;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
</style>
