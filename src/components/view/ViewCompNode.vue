<template>
  <div
    ref="compWrap"
    class="view-comp-node"
    :style="compWrapStyle"
    :data-comp-id="comCfg.id"
    v-show="isShow && type !== 'subCom'"
    @click.capture="compBoxClick"
    @mouseover="compBoxMouseover"
    @mouseleave="compBoxMouseleave"
    @dblclick="compBoxDbclick">
    <div class="transform-box" v-if="permissionShow" :style="nodeStyle">
      <div class="render-box" :style="comStyle">
        <component
          ref="compRef"
          class="component"
          :id="id"
          :data-id="id"
          :attr="comCfg.attr"
          :config="comCfg.config"
          :callbacks="comCfg.callbacks"
          :data="compData"
          :emit="seatom_emit"
          :seatom_updateCallbackValue="seatom_updateCallbackValue"
          :seatom_setParentStyle="seatom_setParentStyle"
          :parent="type === 'subCom' ? $parent.$refs.compRef : null"
          :is="compDefObj"
          :platform="platform"
          :workspaceId="workspaceId"
          isView
          :mainScreenId="mainScreenId"
          v-if="isCreateComp"
          @mounted="handleCompMounted"
          @destroyed="handleCompDestroyed"
          @reRender="handleRerender"
          v-on="$listeners" />
        <template v-if="compMounted">
          <ViewCompNode
            ref="childCompRef"
            v-for="childId in comCfg.children"
            :key="childId"
            :id="childId"
            type="subCom"
            :workspaceId="workspaceId"
            :mainScreenId="mainScreenId"
            :item-data="itemData"
            v-on="$listeners" />
        </template>
      </div>
    </div>
    <div class="link-wrap" v-if="drillPath.length">
      <div class="link-item" @click="drilldownCrumbClick(0, 0)">全部</div>
      <div class="link-item" v-for="(item, index) in drillPath" :key="index" @click="drilldownCrumbClick(0, index + 1)">{{ item }}</div>
    </div>
    <div class="error-popup" v-if="showError" :style="tipStyle">{{ errorText }}</div>
    <div class="permission-error" v-if="!permissionShow" :style="permissionTipStyle">{{ permissionTipText }}</div>
  </div>
</template>

<script>
import compMixins from '@/mixins/comp'
import { SOURCE_TYPES, COMMON_ACTIONS, drillSourceOpt } from '@/common/constants'
import { datastorageList } from '@/api/datastorage'
import { sendCustomReq } from '@/api/common'
import { replaceUrl, transDrillData, encryptTilemapPath, fastLoadedImg, getQueryVariable, formatBackground, isFormComp, getNodeByParam, getGroupComs } from '@/utils/base'
import dataUtil from '@/utils/data'
import VueSocket from 'vue-socket.io'
import getCompData from '@/utils/mergeRequest'
import runAnimation from '@/utils/runAnimation'
import emitter from '@/utils/bus'
import quarkDom from 'hz-quark/dist/dom'
import { mapState, mapActions, mapGetters } from 'vuex'

const compMap = {
  'interaction-container-referpanel': () => import('@/components/refer-panel/ReferPanel'),
  'interaction-container-dynamicpanel': () => import('@/components/refer-panel/DynamicPanel'),
  'interaction-container-newdynamicpanel': () => import('@/components/refer-panel/NextDynamicPanel'),
  'interaction-container-carousepanel': () => import('@/components/refer-panel/DialogPanel'),
  'interaction-form-group': () => import('@/components/refer-panel/FormPanel.vue'),
  'interaction-container-affixPanel': () => import('@/components/refer-panel/NailFixationPanel'),
  'interaction-container-popoverpanel': () => import('@/components/refer-panel/PopoverPanel.vue'),
  'interaction-container-mapShadowPanel': () => import('@/components/refer-panel/MapShadowPanel'),
  'interaction-container-loop-pitch': () => import('@/components/refer-panel/LoopPitch'),
  'interaction-container-roll-pitch': () => import('@/components/refer-panel/RollPitch'),
  'interaction-container-list-pitch': () => import('@/components/refer-panel/ListPitch'),
  'interaction-container-fold-panel': () => import('@/components/refer-panel/FoldPanel'),
  'interaction-container-popup': () => import('@/components/refer-panel/PopupPanel'),
  'interaction-container-modulepanel': () => import('@/components/refer-panel/ViewModulePanel'),
  'interaction-container-statusdialogpanel': () => import('@/components/refer-panel/StatusDialogPanel'),
  'interaction-container-flowlayoutpanel': () => import('@/components/refer-panel/NextDynamicPanel'),
  'interaction-container-indicator': () => import('@/components/refer-panel/IndicatorPanel')
}

// 错误码对应错误信息
const errorMap = {
  801: '请求接口失败，请检查数据源'
}

export default {
  name: 'ViewCompNode',

  inheritAttrs: true,

  inject: [
    'getLayerTree',
    'filtersMap',
    'screenComs',
    'callbackManager',
    'permissionMap'
  ],

  components: {},

  data () {
    this.mergeCompData = getCompData.bind(this);
    return {
      compMap,
      compDefObj: null,
      originData: [],
      compData: [],
      isShow: true,
      permissionShow: true,
      showPermissionTips: false,
      dataLoaded: false,
      compMounted: false,
      socketStartTime: null,
      socket: null,
      copyData: [],
      drillPath: [],
      drillSourceOpt,
      linkParams: {}, // 定义一个Map保存组件联动的事件参数
      eventMap: {},
      compLoading: false,
      showError: false,
      errorText: '接口响应超时',
      lastLinkageParams: {}, // 上次的联动请求参数
      tipStyle: {},
      unwatchEvent: null,
      unwatchContainer: null,
      unwatchData: null
    }
  },
  props: {
    workspaceId: {
      type: Number,
      required: true
    },
    id: {
      type: String,
      required: true
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    },
    type: {
      type: String,
      required: true,
      default: 'com',
      validator: v => ['com', 'subCom', 'group'].indexOf(v) > -1
    },
    zIndex: {
      type: Number,
      default: 0
    },
    itemData: {
      type: Array,
      default: () => ([])
    },
    platform: {
      type: String,
      default: 'pc'
    },
    isContainer: { // 是否在容器内
      type: Boolean,
      default: false
    }
  },

  computed: {
    ...mapState({
      fnQueue: state => state.event.fnQueue,
      linkComps: state => state.view.linkComps,
      immediateLink: state => state.view.immediateLink
    }),
    ...mapGetters('datacontainer', ['getContainerDataById']),
    // 服务端渲染数据
    ssrShowCompIds () {
      return this.$store.state.editor.ssrRender.showCompIds
    },
    layerTree () {
      return this.getLayerTree()
    },
    queueList () {
      return this.fnQueue[this.id];
    },
    bindContainerData () {
      if (this.sourceType === 'datacontainer') {
        const sourceId = this.dataResponse.source.datacontainer.data.dataContainerComId;
        return this.getContainerDataById(sourceId)
      }
      return { loaded: false, data: [] }
    },
    groupQueue () { // 移动端代理模块分组事件
      if (this.id.startsWith('interaction-container-modulepanel')) {
        const groupId = this.comCfg.config.groupId;
        return this.fnQueue[groupId]
      }
      return []
    },
    isCreateComp () {
      return this.compDefObj && this.isShow && this.dataLoaded
    },
    comCfg () {
      return this.screenComs()[this.id]
    },
    compWrapStyle () {
      const { comCfg, platform, isContainer, zIndex } = this
      if (this.isContainer && isFormComp(comCfg.comType)) return
      const { x, y, w, h } = comCfg.attr
      if (_.isEmpty(comCfg)) return {};

      if (platform === 'pc' || comCfg.comName === 'interaction-container-flowlayoutpanel') {
        return {
          width: w + 'px',
          height: h + 'px',
          left: x + 'px',
          top: y + 'px',
          zIndex: this.isHightlight ? 10000 + zIndex : zIndex
        }
      } else {
        return {
          width: isContainer ? `${((w / 24) * 100)}%` : '100%',
          height: isContainer ? `${h * 10}px` : '100%',
          zIndex
        }
      }
    },
    isHightlight () {
      const comps = this.linkComps?.comps || [];
      const isActive = comps.findIndex(id => id === this.id) > -1
      return isActive
    },
    nodeStyle () {
      const { attr, comName } = this.comCfg
      const { enable3D, perspective, deg } = attr
      const blackList = [
        'interaction-container-carousepanel',
        'interaction-container-affixPanel',
        'interaction-container-popoverpanel',
        'interaction-container-mapShadowPanel',
        'interaction-container-statusdialogpanel'
      ]
      if (blackList.includes(comName)) {
        return {}
      }
      return {
        transition: 'transform 600ms ease 0s',
        perspective: enable3D ? perspective + 'px' : null,
        transform: `rotate(${deg}deg)`,
        'transform-origin': 'center center'
      }
    },
    comStyle () {
      const { comCfg, platform, isContainer } = this
      const { w, h, opacity, cursor, enable3D, flipH, flipV } = comCfg.attr;
      let style = {}
      const blackList = [
        'interaction-container-carousepanel',
        'interaction-container-affixPanel',
        'interaction-container-popoverpanel',
        'interaction-container-mapShadowPanel',
        'interaction-container-statusdialogpanel'
      ]
      if (platform === 'pc' || comCfg.comName === 'interaction-container-flowlayoutpanel') {
        style = {
          position: 'absolute',
          left: 0,
          top: 0,
          width: w + 'px',
          height: h + 'px',
          opacity,
          cursor
        }
      } else {
        style = {
          width: isContainer ? `${((w / 24) * 100)}%` : '100%',
          height: isContainer ? `${h * 10}px` : '100%',
          opacity,
          cursor
        }
      }

      if (!blackList.includes(comCfg.comName)) {
        style.transform = (enable3D ? `rotateX(${flipH}deg) rotateY(${flipV}deg)` : '');
      }

      return style
    },
    fieldMapping () {
      if (_.isEmpty(this.comCfg)) return []
      return this.comCfg.dataConfig.fieldMapping
    },
    dataResponse () {
      return this.comCfg.dataConfig.dataResponse;
    },
    sourceType () {
      if (_.isEmpty(this.comCfg)) return SOURCE_TYPES.static
      return this.dataResponse.sourceType
    },
    validFilters () {
      if (_.isEmpty(this.comCfg)) return []
      const filters = this.dataResponse.filters
      if (this.drillDown.length) {
      }
      if (!filters.enable) return []
      return _.filter(filters.list, { enable: true }).map(({ id }) => this.filtersMap()[id])
    },
    callbackKeys () {
      const keys = this.validFilters.reduce((arr, f) => {
        const systemParams = f.systemParams || []
        if (systemParams.length) {
          const cs = systemParams.filter(param => param.source === 'callback')
          cs.forEach(call => {
            arr.push(call.value[0])
          })
        }
        arr.push(...f.callbackKeys)
        return arr
      }, [])
      return _.uniq(keys)
    },
    drillDown () { // 下钻配置
      if (_.isEmpty(this.comCfg)) return []
      return this.comCfg.interactionConfig.drillDown
    },
    drillDownData () { // 下钻处理好的数据
      return function () {
        if (!this.drillDown.length) return []
        let result = [];
        this.drillDown.forEach(e => {
          if (e.linkType === 'links') {
            result = transDrillData(this.originData, e)
            result = dataUtil.mapData(result, this.fieldMapping)
          }
        })
        return result
      }
    },
    comSize () {
      if (_.isEmpty(this.comCfg)) return {}
      const { attr } = this.comCfg;
      return {
        width: attr.w,
        height: attr.h
      }
    },
    permissionTipObj () {
      const permissionMap = this.permissionMap()
      if (permissionMap && permissionMap.size) {
        const key = Array.from(permissionMap.keys()).filter(item => {
          return item.includes(this.id)
        })
        return permissionMap.get(key[key.length - 1])?.tips
      }
      return {}
    },
    permissionTipText () {
      const { type, info } = this.permissionTipObj || {}
      if (type && type === 'tips') {
        return info
      }
      return ''
    },
    permissionTipStyle () {
      const { type, background, font } = this.permissionTipObj || {}
      if (type && type === 'tips') {
        return {
          ...formatBackground(background),
          ...font
        }
      }
      return {}
    }
  },

  watch: {
    groupQueue: { // 将移动端分组事件转移到模块组件上
      handler: async function (list) {
        if (list && list.length) {
          const groupId = this.comCfg.config.groupId;
          const fns = await this.runFnQueue(groupId)
          if (fns.length) {
            const groupNode = this.layerTree.getNodeById(groupId);
            const events = [];
            fns.forEach(item => {
              events.push({
                comId: groupNode.data.comId,
                type: item.type,
                data: item.data
              })
            })
            this.pushFnQueue(events)
          }
        }
      },
      immediate: true,
      deep: true
    },
    itemData () {
      // 判断是父数据源再拉取数据，防止初始化时下钻数据被覆盖
      if (['inherit', 'dialog'].includes(this.sourceType)) {
        this.updateCompData()
      }
    },
    comSize (newAttr, oldAttr) {
      if (!_.isEqual(newAttr, oldAttr)) {
        const comp = this.$refs.compRef
        this.$nextTick(() => {
          const compWrap = this.$refs.compWrap
          if (comp) {
            if (this.platform === 'pc' || this.comCfg.comName === 'interaction-container-flowlayoutpanel') {
              comp.resize({ width: newAttr.w, height: newAttr.h })
            } else {
              const rect = compWrap.getBoundingClientRect()
              comp.resize({ width: rect.width, height: rect.height })
            }
          }
        })
      }
    },
    dataLoaded (val) {
      if (process.env.IS_SSR_RENDER !== 'true') {
        return
      }
      if (val) {
        const index = this.ssrShowCompIds.indexOf(this.id);
        if (index > -1) {
          this.$nextTick(() => {
            this.$store.commit('editor/removeSsrShowCompId', this.id);
          })
        }
      }
    }
  },

  async created () {
    if (!this.comCfg) {
      return
    }
    const { show } = this.comCfg

    this.initZippedImage();
    this.$nextTick(() => {
      show ? this.show() : this.hide(false)
    })

    // 加载组件定义
    await this.loadCompDef()

    const vm = this

    // 初始化联动/自定义事件/下钻
    this.initCompEvents();

    // 初始化自动更新数据
    this.initCompRefresh();

    // 订阅api数据源回调 过滤器与api数据源 共同监听回调参数
    const apiCallbackKeys = this.getFormatParamArr();
    const varKeys = dataUtil.getVarParamObj(this.dataResponse);
    const allKeys = _.uniq([...this.callbackKeys, ...apiCallbackKeys, ...varKeys]);

    // 定义变量保存上次回调参数 用于判断是否需要更新数据
    let state = _.cloneDeep(this.callbackManager().pick(allKeys));
    if (allKeys.length) {
      this.callbackManager().subscribeCallbackKey(this.id, allKeys, _.debounce(function (params) {
        // 获取最新的回调值
        const currentState = vm.callbackManager().pick(allKeys);
        if (_.isEqual(state, currentState)) {
          state = _.cloneDeep(vm.callbackManager().pick(allKeys))
          this.lastLinkageParams = state || {}
          return
        };
        state = _.cloneDeep(vm.callbackManager().pick(allKeys))

        const newParams = _.pickBy(currentState, val => !_.isNil(val));
        vm.updateCompDataByParams(newParams).then(() => {
          vm.originData = _.cloneDeep(vm.compData);
          vm.initCompDrillDowns();
        }).catch(e => {})
      }, 500))

      this.$once('hook:beforeDestroy', () => {
        this.callbackManager().unsubscribeCallbackKey(this.id, allKeys)
      })
    }

    // 初始化组件数据
    const { sourceType } = this.dataResponse
    if (sourceType === 'websocket') {
      this.createSocket()
    } else if (sourceType === 'datacontainer') { // 若数据源为数据容器，监听绑定的数据容器数据
      this.unwatchContainer = this.$watch('bindContainerData', async data => {
        if (data && data.loaded) {
          this.dataLoaded = true;
          this.handleCompData(data.data);
        }
      }, { deep: true, immediate: true })
    } else {
      if (!this.immediateLink[this.id]) { // 没有立即触发的联动则请求数据
        await this.initCompData()
      } else {
        this.dataLoaded = true;
      }
    }

    // 监听事件队列
    this.unwatchEvent = vm.$watch('queueList', async list => {
      if (list && list.length) {
        const fns = await vm.runFnQueue(vm.id)
        vm.handlerQueueList(fns);
      }
    }, { deep: true, immediate: true })

    emitter.on('refreshApiData', vm.refreshApiDataHandler)

    // 若组件是数据容器，则监听compData, 更新全局数据
    if (this.comCfg.comName === 'interaction-container-datacontainer') {
      this.unwatchData = this.$watch('compData', async data => {
        this.$store.dispatch('datacontainer/setContanerData', { id: this.id, data });
      }, { deep: true, immediate: true })
    }
  },

  beforeDestroy () {
    this.unwatchEvent && this.unwatchEvent();
    this.unwatchData && this.unwatchData();
    this.unwatchContainer && this.unwatchContainer();
    emitter.off('refreshApiData', this.refreshApiDataHandler)
    this.$store.commit('event/clearFnQueue', this.id);
    if (this._intervalId) {
      clearInterval(this._intervalId)
    }
    this.socket && this.socket.io.close()
  },

  methods: {
    ...mapActions('event', [
      'pushFnQueue',
      'runFnQueue'
    ]),
    show () {
      this.isShow = true;
      this.$emit('seatom_setComConfig', this.id, [{ key: 'show', value: true }]);
    },
    async hide (animation = true) {
      if (animation && this.isShow) {
        await runAnimation(this.$el, this.comCfg.animation.animationOut, '')
      }
      this.isShow = false;
      this.$emit('seatom_setComConfig', this.id, [{ key: 'show', value: false }]);
    },
    async loadCompDef () {
      const { comName, version } = this.comCfg
      const moduleName = `${comName}@${version}`
      try {
        // 加载面板类组件
        if (_.has(this.compMap, comName)) {
          const compDef = await this.compMap[comName]();
          this.compDefObj = _.cloneDeep(compDef.default);
          return
        }
        // eslint-disable-next-line
        const res = await window.System.import(moduleName)
        // 深拷贝res.default 解决相同组件之间mixins引用导致执行多次render
        const compDef = _.cloneDeep(res.default);
        (compDef.mixins || (compDef.mixins = [])).push(compMixins)
        this.compDefObj = compDef
      } catch (e) {
        this.$emit('compLoaded')
        console.warn('组件加载出错', e)
      }
    },
    async createSocket () {
      if (this.socket) {
        this.socketStartTime = Date.now()
        this.socket.io.connect()
        return true
      }
      const id = this.comCfg.dataConfig ? this.dataResponse.source[this.sourceType].data.sourceId : ''
      if (!id) return false
      const data = {
        id
      }
      const sourceRes = await datastorageList(data)
      const source = sourceRes.success ? sourceRes.data : []
      const address = source[0] ? source[0].config.host : ''
      let path = source[0] ? source[0].config.path : ''
      const duration = this.comCfg.dataConfig ? this.dataResponse.source[this.sourceType].data.duration : 10
      const durationType = this.comCfg.dataConfig ? this.dataResponse.source[this.sourceType].data.durationType : 'minute'
      const range = duration * (durationType === 'minute' ? 60 : 1) * 1000
      if (address) {
        const options = {
          debug: false,
          connection: address
        }
        if (path) {
          if (path.charAt(0) !== '/') path = '/' + path
          options.options = {
            path
          }
        }
        this.socket = new VueSocket(options)
        this.socket.io.on('connect', () => {
          this.dataLoaded = true
        })
        this.socket.emitter.addListener('message', data => {
          let newData = _.cloneDeep(this.compData)
          const now = Date.now()
          if (!this.socketStartTime) {
            this.socketStartTime = now
          } else {
            if (now - this.socketStartTime > range) {
              // 超出时间窗口范围
              let willRemoveIdx = -1
              for (let i = 0; i < this.copyData.length; i++) {
                if (now - this.copyData[i].date > range) {
                  willRemoveIdx = i
                } else break
              }
              if (willRemoveIdx > -1) {
                this.copyData.splice(0, Math.max(willRemoveIdx, 1))
                newData.splice(0, Math.max(willRemoveIdx, 1))
              }
              this.socketStartTime = this.copyData[0] ? this.copyData[0].date : now
            }
          }
          if (typeof data === 'object') {
            if (Array.isArray(data)) {
              let copyData = _.cloneDeep(data)
              copyData = copyData.map(item => {
                item.date = now
                return item
              })
              this.copyData = this.copyData.concat(copyData)
              newData = newData.concat(data)
            } else {
              const copyData = _.cloneDeep(data)
              copyData.date = now
              this.copyData.push(copyData)
              newData.push(data)
            }
            this.handleCompData(newData)
          }
        }, this)
      }
    },
    packActualWhere (where, needOrigin) { // 组装实际的筛选条件,needOrigin:需要返回原始where
      let flag = false; // 标识是否用到路由参数
      where = Object.assign({}, where);
      if (where.enable) {
        where.whereCondition = where.whereCondition.map(cond => {
          cond = Object.assign({}, cond);
          cond.compareValue = cond.compareValue.map(v => {
            if (typeof v === 'string' && v.indexOf('${') > -1) {
              const param = v.replace(/[${}]/g, '');
              const callbackVal = this.callbackManager().pick([param]);
              const value = _.get(callbackVal, param);
              flag = true;
              return value;
            }
            return v;
          })
          return cond;
        })
      } else return {}
      if (flag) { // 有用到路由参数，带参数进行请求
        where.temporary = true;
        return where;
      }
      return (needOrigin ? where : {});
    },
    async initCompData () { // 初始化组件数据
      if (!this.isShow) {
        this.dataLoaded = true;
        return Promise.resolve();
      }
      if (!this.permissionShow) {
        this.dataLoaded = true;
        return Promise.resolve();
      }

      // 数据筛选支持路由参数，需在此解析
      // const where = this.dataResponse.source[this.sourceType]?.data?.where || {};
      // const packedWhere = this.packActualWhere(where);
      // 结束
      await this.updateCompData()
    },
    // 响应数据处理
    handleReponseData (response) {
      if (response?.success && response?.data?.code !== 801) {
        this.handleCompData(response.data)
      } else {
        this.showErrorFn(true, errorMap[response?.data?.code] || response.message);
        this.dataLoaded = true
      }
    },
    // 数据处理
    handleCompData (data) {
      this.showErrorFn(false, '')
      const filterData = dataUtil.filterData(data, this.validFilters, this.callbackManager())
      const resultData = dataUtil.mapData(filterData, this.fieldMapping)
      this.handleTipsInfo(resultData);
      this.compData = Object.freeze(resultData);
      this.originData = Object.freeze(resultData);
      this.initCompDrillDowns();
      this.dataLoaded = true;
      this.compLoading = false;
    },
    handleTipsInfo (resultData) {
      // 提示遮罩层信息
      const tips = this.dataResponse.tips;
      if (tips && tips.open) { // 提示信息
        const conditions = tips.conditions || [];
        let target = null;
        for (const item of conditions) {
          if (item.type === 'empty') {
            if (resultData.length === 0) {
              target = item;
              break;
            }
          } else if (item.type === 'diy') {
            let flag = false;
            const obj = resultData[0];
            if (obj) {
              const condition = item.condition;
              const val = obj[item.field];
              const expected = item.value;
              switch (condition) {
                // eslint-disable-next-line
                case '==': flag = val == expected;
                  break;
                case '>': flag = val > expected;
                  break;
                case '<': flag = val < expected;
                  break;
                case '>=': flag = val >= expected;
                  break;
                case '<=': flag = val <= expected;
                  break;
                default: flag = false;
                  break;
              }
              if (flag) {
                target = item;
                break;
              }
            }
          }
        }
        if (target) {
          let tipsStyle = '';
          tipsStyle += quarkDom.formatText(target);
          tipsStyle += quarkDom.formatBackground(target.background);
          this.tipStyle = tipsStyle;
          this.showErrorFn(true, target.info);
          return
        }
      }
      this.showError = false;
      return true
    },
    // 显示错误遮罩层
    showErrorFn (show, msg) {
      this.showError = show;
      if (show) {
        this.compData = [];
      }
      this.errorText = msg;
    },
    // 更新数据 合并请求 (静态数据源不发请求)
    async updateCompData (params = {}) {
      const res = await this.getCompData(params);
      this.handleReponseData(res);
    },
    // 组装数据筛选和后端联动的数据
    async getCompData (params, loading) {
      if (!Object.prototype.hasOwnProperty.call(params, 'whereCondition') && !Object.prototype.hasOwnProperty.call(params, 'orderCondition')) {
        const where = this.dataResponse.source[this.sourceType]?.data?.where || {};
        const packedWhere = this.packActualWhere(where);
        params = _.assign(packedWhere, params)
      }
      this.lastLinkageParams = Object.assign({}, params);
      if (loading) this.compLoading = true;

      // 判断如果存在后端下钻，则传入isDrill=true
      if (this.drillSourceOpt.includes(this.sourceType) && this.drillDown[0]?.drillType === 'server') {
        params._isDrill = true;
      }
      return await this.mergeCompData(params);
    },
    // 更新数据 用于定时刷新、后端联动或订阅回调参数发起请求
    async updateCompDataByParams (params = {}) {
      const res = await this.getCompData(params, true);
      if (res && res.success) {
        const filterData = dataUtil.filterData(res.data, this.validFilters, this.callbackManager());
        const resultData = dataUtil.mapData(filterData, this.fieldMapping);
        this.compData = Object.freeze(resultData);
        this.originData = _.cloneDeep(resultData);
        this.handleTipsInfo(resultData);
        this.dataLoaded = true;
      } else {
        this.showErrorFn(true, errorMap[res.code] || res.message);
      }
    },
    // 处理发送请求设置的请求
    handleSendCustomReq (send, param) {
      this.compLoading = true;
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        let params = {}
        switch (send.type) {
          case 'api':
            params = {
              method: send.data[send.type].method,
              headers: send.data[send.type].headers,
              params: this.handleParamToString(send.data[send.type].params, param),
              body: this.handleBodyObj(send.data[send.type].body, param),
              path: send.data[send.type].path,
              baseUrl: send.data[send.type].baseUrl
            }
            break
          default:
        }
        sendCustomReq(params).then(res => {
          if (res && res.success) {
            resolve(res)
          } else {
            // eslint-disable-next-line
            console.log(res)
            reject(res)
          }
        }).catch(err => {
          // eslint-disable-next-line
          console.log(err)
          reject(err)
        }).finally(() => {
          this.compLoading = false;
        })
      })
    },
    handleBodyObj (data, param) { // 处理发送请求数据-body
      const cloneData = _.cloneDeep(data)
      cloneData.forEach((item) => {
        const key = item[0]
        const value = item[1]
        if (!!key && !!value.length) {
          if (value.length === 1) {
            if (value[0] && value[0].includes('${')) {
              const callbackVal = value[0].replace(/[${}]/g, '')
              const callbackObj = this.callbackManager().pick([callbackVal])
              item[1] = _.get(callbackObj, callbackVal)
            } else if (value[0] && value[0].includes('#{')) {
              const constant = isNaN(Number(value[0].replace(/[#{}]/g, ''))) ? value[0].replace(/[#{}]/g, '') : Number(value[0].replace(/[#{}]/g, ''))
              item[1] = constant
            } else {
              item[1] = _.get(param, this.getObjPathByArr(value))
            }
          } else {
            item[1] = _.get(param, this.getObjPathByArr(value))
          }
        } else {
          item[1] = undefined
        }
      })
      return JSON.stringify(Object.fromEntries(cloneData))
    },
    handleParamToString (data, param) { // 处理发送请求数据-params
      let paramsUrl = ''
      data.forEach((item) => {
        const key = item[0]
        const value = item[1]
        if (!!key && !!value.length) {
          if (value.length === 1) {
            if (value[0] && value[0].includes('${')) {
              const callbackVal = value[0].replace(/[${}]/g, '')
              const callbackObj = this.callbackManager().pick([callbackVal])
              paramsUrl += '&' + key + '=' + _.get(callbackObj, callbackVal)
            } else if (value[0] && value[0].includes('#{')) {
              const constant = isNaN(Number(value[0].replace(/[#{}]/g, ''))) ? value[0].replace(/[#{}]/g, '') : Number(value[0].replace(/[#{}]/g, ''))
              paramsUrl += '&' + key + '=' + constant
            } else {
              paramsUrl += '&' + key + '=' + _.get(param, this.getObjPathByArr(value))
            }
          } else {
            paramsUrl += '&' + key + '=' + _.get(param, this.getObjPathByArr(value))
          }
        }
      })
      return paramsUrl
    },
    initCompRefresh () { // 自动更新数据
      const { autoUpdate } = this.dataResponse
      if (autoUpdate.enable) {
        this._intervalId = setInterval(() => {
          if (this.isCreateComp) {
            this.updateCompDataByParams(this.lastLinkageParams).then(() => {
              this.originData = _.cloneDeep(this.compData);
              this.initCompDrillDowns();
            }).catch(e => {})
          }
        }, autoUpdate.time * 1000)
      }
    },
    getFormatParamArr () { // 获取数据源订阅的参数 ${name}
      // 匹配${name}字符串
      const strList = [];
      const { source } = this.dataResponse;
      if (this.sourceType === 'api') {
        const { body, headers, params, path } = source.api.data
        const _body = JSON.stringify(JSON.parse(body || '{}'))
        const _headers = JSON.stringify(JSON.parse(headers || '{}'))
        strList.push(_body, _headers, params, path)
      } else if (this.sourceType === 'mysql') {
        const { sql } = source.mysql.data
        strList.push(sql)
      } else {
        return []
      }
      let paramArr = []
      strList.forEach(item => {
        const result = item.match(/\$\{(.*?)\}/ig)
        if (result && result.length) {
          result.forEach(item2 => {
            // 去掉${}和.
            const s = item2.replace(/[${}]/g, '').replace(/\.[\w\W]*/g, '')
            paramArr.push(s)
          })
        }
      })
      paramArr = _.uniq(paramArr)
      return paramArr
    },
    initCompEvents () { // 新版初始化事件
      const commonEvents = [
        'dataChange',
        'compClick',
        'compHover',
        'compDbclick',
        'compMouseleave'
      ]
      commonEvents.forEach(trigger => {
        this.eventMap[trigger] = {
          triggerId: this.id,
          trigger,
          event: [],
          link: [],
          drill: []
        }
      })
      const { events, linkAge, drillDown } = this.comCfg.interactionConfig;
      const evtArr = [
        { data: events, type: 'event' },
        { data: linkAge, type: 'link' },
        { data: drillDown, type: 'drill' }
      ]

      evtArr.forEach(item => {
        const data = item.data;
        data.forEach(e => {
          const { triggerId, trigger } = e;
          if (!_.has(this.eventMap, trigger)) {
            this.eventMap[trigger] = {
              triggerId,
              trigger,
              event: [],
              link: [],
              drill: []
            }
          }
          this.eventMap[trigger][item.type].push(e)
        })
      })
    },
    async handlerQueueList (queueList) { // 执行事件队列
      const selfEvent = queueList.filter(item => item.type === 'emit_event');
      const events = queueList.filter(item => item.type === 'event');
      const links = queueList.filter(item => item.type === 'link');
      const drills = queueList.filter(item => item.type === 'drill');
      // 记录初始显示状态
      const isShow = this.isShow;

      for (const item of selfEvent) {
        this.selfEventHandler(item.data.event, item.data.params);
      }

      for (const item of events) {
        await this.eventHandler([item.data.event], item.data.params, item.data.source);
      }
      if (!isShow && this.isShow) { // 由隐藏切换到显示 才判断是否拉取数据
        const len = links.length;
        let hasServerLink = false; // 是否有后端联动
        for (const link of links) {
          const componentList = link.data.event.linkageConfig[0].componentList;
          const comp = componentList.find(item => item.componentId === this.id);
          if (comp) {
            if (comp.sourceType === 'server') {
              hasServerLink = true;
              break;
            }
          }
        }
        if ((!len || (len && !hasServerLink))) { // 无后端联动，需要拉取数据
          await this.updateCompData();
        }
      }
      if (!this.isShow) { // 隐藏组件 清空数据
        this.compData = [];
      }

      for (const item of links) {
        await this.linkHandler([item.data.event], item.data.params, item.data.source);
      }

      for (const item of drills) {
        await this.drillHandler([item.data.event], item.data.params);
      }
    },
    async selfEventHandler (event, param) { // 语音交互
      let actionFunc, compRef
      switch (event.action) {
        case 'compClick':
          this.compBoxClick('voice');
          break;
        case 'compDbclick':
          this.compBoxDbclick('voice');
          break;
        case 'compHover':
          this.compBoxMouseover('voice');
          break;
        case 'compMouseleave':
          this.compBoxMouseleave('voice');
          break;
        case 'refresh': // 刷新组件数据
          this.updateCompDataByParams();
          break;
        case 'close': // 关闭弹窗面板
          this.eventHandler([{ action: 'dialogHidden' }], {}, 'voice');
          this.$store.commit('view/clearLinkComps');
          break;
        default:
          compRef = this.$refs.compRef
          actionFunc = compRef && compRef.responseEvent
          if (_.isFunction(actionFunc)) {
            // 事件字段映射
            await actionFunc.call(compRef, event.action, param)
          } else {
            this.seatom_emit(event.action, param, 'voice');
          }
          break;
      }
    },
    async eventHandler (events, param, source) { // 执行自定义事件
      if (!events.length) return;
      if (source === 'voice') { // 语音控制的联动，需要显示高亮边框
        this.$store.commit('view/updateLinkComps', { uid: events[0].triggerId, comps: [this.id] })
      }
      for await (const e of events) { // for + await保证事件按同步的顺序执行
        this.$nextTick(async () => {
          let actionFunc, compRef
          const conditionRes = dataUtil.conditionResult(e, param, this.callbackManager())
          if (!conditionRes) return
          switch (e.action) {
            case COMMON_ACTIONS.show:
              this.show()
              break
            case COMMON_ACTIONS.hide:
              this.hide()
              break
            case COMMON_ACTIONS.showHideSwitch:
              this.isShow ? this.hide() : this.show()
              break
            case COMMON_ACTIONS.animationEvent:
              runAnimation(this.$el, e.eventAnimation)
              break
            case COMMON_ACTIONS.jumpEvent:
              this.funJumpEvent(e, param)
              break
            case COMMON_ACTIONS.switchScenePage: {
              const params = dataUtil.mapData([param], e.fieldMap)[0]
              emitter.emit('onEvent', { type: 'switchScene', params })
              break
            }
            case COMMON_ACTIONS.executeScrpit: {
              this.handleExeScript(e, param);
              break
            }
            case COMMON_ACTIONS.updateConfig: {
              this.comCfg.config = _.merge(this.comCfg.config, _.get(e.comConfig, 'updateConfig.configObj', e.comConfig.configObj));
              this.comCfg.attr = _.merge(this.comCfg.attr, _.get(e.comConfig, 'updateConfig.attr', {}));
              this.comCfg.show = _.get(e.comConfig, 'updateConfig.show', e.comConfig.show);
              const { show } = this.comCfg
              this.$nextTick(() => {
                show ? this.show() : this.hide()
              })
              break;
            }
            case COMMON_ACTIONS.clearCallbackValue: // 重置回调参数
              var callbackParams = this.comCfg.interactionConfig.callbackParams || [];
              if (callbackParams.length) {
                const callbackObj = {}
                callbackParams.forEach(item => {
                  callbackObj[item.variableName] = undefined;
                })
                this.callbackManager().updateCallbackValue(callbackObj)
              }
              break;
            case COMMON_ACTIONS.sendRequest: { // 发送请求
              await this.handleSendReq(e, param)
              break
            }
            case COMMON_ACTIONS.anchorLocation: { // 锚点定位
              this.handleAnchorLocation(e, param)
              break
            }
            default:
              compRef = this.$refs.compRef
              actionFunc = compRef && compRef[e.action]
              if (_.isFunction(actionFunc)) {
                // 事件字段映射
                const reflectParam = dataUtil.mapData([param], e.fieldMap)[0]
                await actionFunc.call(compRef, reflectParam)
              }
          }
        })
      }
    },
    async linkHandler (links, param, source) { // 执行联动 source: 联动来源 normal:用户点击 voice:语音控制
      if (!links.length) return;

      if (source === 'voice') { // 语音控制的联动，需要显示高亮边框
        this.$store.commit('view/updateLinkComps', { uid: links[0].triggerId, comps: [this.id] })
      }

      const isDef = (val) => {
        return (!val && val !== 0) || (Array.isArray(val) && !val.length);
      }

      for (let i = 0; i < links.length; i++) {
        const e = links[i];
        this.linkParams[e.triggerId] = {
          event: e,
          param
        };
        if (!this.isShow) { // 组件隐藏状态 保存组合联动数据 不执行联动
          continue;
        }
        let linkConfig = []; // 保存所有的初始联动配置
        const linkMode = e.linkMode || 'multiple'; // 联动模式 single:单独联动 multiple:组合联动
        if (linkMode === 'single') {
          linkConfig = [{
            event: e,
            param
          }];
          // 单独联动且为常量NULL 则清空组件组合联动数据
          const linkageConfig = e.linkageConfig;
          linkageConfig.forEach(cf => {
            if (cf.fieldValue === '#{NULL}') {
              this.linkParams = {}
            }
          })
        } else if (linkMode === 'multiple') {
          if (e.coms && e.coms.length) {
            const linkIds = [e.triggerId, ...e.coms.map(c => c.id)];
            linkConfig = Object.values(_.pick(this.linkParams, linkIds));
          } else {
            linkConfig = Object.values(this.linkParams);
          }
        }

        const linkObjs = []; // 存放处理好的联动配置
        for (let i = 0; i < linkConfig.length; i++) {
          const item = linkConfig[i].event;
          const param = linkConfig[i].param;
          if (!param) {
            continue;
          }
          const configs = [];
          const linkageConfig = item.linkageConfig;
          linkageConfig.forEach(cf => {
            const comp = cf.componentList.find(cm => cm.componentId === this.id)
            if (comp) {
              configs.push({
                id: cf.id,
                name: cf.name,
                fieldValue: cf.fieldValue,
                fieldType: comp.fieldType,
                fieldId: comp.fieldId,
                fieldRequestType: comp.fieldRequestType,
                targetValue: comp.fieldValue,
                compare: comp.compare,
                sourceType: comp.sourceType
              })
            }
          })
          if (configs.length) {
            linkObjs.push({
              id: item.triggerId,
              source: item.source,
              param,
              configs
            })
          }
        }

        const where = this.dataResponse.source[this.sourceType]?.data?.where || {};
        const packedWhere = this.packActualWhere(where, true);
        const whereCondition = [];
        const validFilters = [];
        linkObjs.forEach(obj => {
          const configs = obj.configs;
          configs.forEach(cf => {
            let value;
            if (cf.fieldValue && cf.fieldValue.includes('${')) {
              const callbackVal = cf.fieldValue.replace(/[${}]/g, '')
              const callbackObj = this.callbackManager().pick([callbackVal])
              value = _.get(callbackObj, callbackVal);
            } else if (cf.fieldValue && cf.fieldValue.includes('#{')) {
              value = isNaN(Number(cf.fieldValue.replace(/[#{}]/g, ''))) ? cf.fieldValue.replace(/[#{}]/g, '') : Number(cf.fieldValue.replace(/[#{}]/g, ''))
            } else {
              value = _.get(obj.param, cf.fieldValue);
            }
            if (!isDef(value)) {
              if (cf.sourceType === 'server') { // 后端联动
                const condition = {
                  field: cf.targetValue,
                  type: cf.fieldType,
                  fid: cf.fieldId,
                  fieldRequestType: cf.fieldRequestType,
                  compare: cf.compare,
                  compareValue: Array.isArray(value) ? value : [value]
                }
                whereCondition.push(condition);
              } else { // 前端联动
                const filter = this.getFilterByCondition(cf.compare, cf.targetValue, value);
                validFilters.push(filter)
              }
            }
          })
        })
        if (!whereCondition.length && !validFilters.length) { // 联动条件为空 则重置组件数据
          this.initCompData();
        } else {
          if (whereCondition.length) { // 存在后端联动
            packedWhere.whereCondition = (packedWhere.whereCondition || []).concat(whereCondition);
            packedWhere.temporary = true;
            packedWhere.enable = true;
            try {
              await this.updateCompDataByParams(packedWhere);
            } catch (e) {}
          }
          if (validFilters.length) {
            const filterData = dataUtil.linkAgeFilterData(this.originData, param || {}, _.compact(validFilters))
            this.compData = Object.freeze(filterData);
            this.handleTipsInfo(filterData);
          }
        }
        if (this.drillDown.length) { // 如果有下钻，联动后要初始化下钻 TODO:源数据被修改了
          this.originData = _.cloneDeep(this.compData)
          this.drilldownByPath([]);
        }
      }
    },
    async drillHandler (drills, param) { // 执行下钻
      if (!drills.length || !this.isShow) return;
      for (const e of drills) {
        if (e.linkType === 'parent') {
          const parentField = e.parentField || 'parent'
          const validFilters = [{
            content: `return data.filter(item => item['${parentField}'] == param['${e.fieldValue}'])`
          }]
          const filterData = dataUtil.linkAgeFilterData(this.originData, param, validFilters)
          if (filterData.length) {
            this.compData = Object.freeze(filterData);
          }
        } else if (e.linkType === 'links') {
          const path = [...this.drillPath, param[e.fieldValue]];
          this.drilldownByPath(path);
        }
      }
    },
    drilldownCrumbClick (start, end) { // 下钻面包屑点击
      const path = this.drillPath.slice(start, end);
      this.drilldownByPath(path);
    },
    async drilldownByPath (path) { // 根据path下钻数据 path：Array
      const drill = this.drillDown[0];
      if (path.length >= drill.links.length) {
        return
      }
      if (this.drillSourceOpt.includes(this.sourceType) && drill.drillType === 'server') {
        await this.updateCompData({ _drillPath: path })
        this.drillPath = path;
      } else {
        const drillDownData = this.drillDownData();
        let filterData = drillDownData.filter(item => {
          return item._parentId === path.join('-')
        });
        filterData = filterData.map(item => _.omit(item, ['_id', '_parentId']))
        if (filterData.length) {
          if (drill.sortField && drill.sortType) { // 下钻排序
            filterData = filterData.sort((a, b) => {
              if (drill.sortType === 'asc') {
                return a[drill.sortField] - b[drill.sortField]
              } else {
                return b[drill.sortField] - a[drill.sortField]
              }
            })
          }
          this.compData = Object.freeze(filterData);
          this.drillPath = path;
        }
      }
    },
    getFilterByCondition (condition, targetValue, value) {
      const filter = {};
      switch (condition) {
        case 'equal':
          filter.content = `return data.filter(item => item['${targetValue}'] == '${value}');`;
          break;
        case 'unequal':
          filter.content = `return data.filter(item => item['${targetValue}'] != '${value}');`;
          break;
        case 'greater':
          filter.content = `return data.filter(item => item['${targetValue}'] > '${value}');`;
          break;
        case 'less':
          filter.content = `return data.filter(item => item['${targetValue}'] < '${value}');`;
          break;
        case 'greaterOrEqual':
          filter.content = `return data.filter(item => item['${targetValue}'] >= '${value}');`;
          break;
        case 'lessOrEqual':
          filter.content = `return data.filter(item => item['${targetValue}'] <= '${value}');`;
          break;
        case 'notNull':
          filter.content = `return data.filter(item => item['${targetValue}'] !== '' || item['${targetValue}'] != null);`;
          break;
        case 'null':
          filter.content = `return data.filter(item => item['${targetValue}'] === '' || item['${targetValue}'] == null);`;
          break;
        case 'contain':
          filter.content = `return data.filter(item => {
            return item['${targetValue}'] && item['${targetValue}'].includes && item['${targetValue}'].includes('${value}')
          });`;
          break;
        case 'contained':
          filter.content = `return data.filter(item => {
            return '${value}' && '${value}'.includes && '${value}'.includes(item['${targetValue}'])
          });`;
          break;
        case 'notContain':
          filter.content = `return data.filter(item => {
            if (item['${targetValue}'] != null) {
              return !item['${targetValue}'].toString().includes('${value}');
            }
            return false;
          });`;
          break;
        case 'matchOnStart':
          filter.content = `return data.filter(item => {
            return item['${targetValue}'] && item['${targetValue}'].startsWith && !item['${targetValue}'].startsWith('${value}')
          });`;
          break;
        case 'matchOnEnd':
          filter.content = `return data.filter(item => {
            return item['${targetValue}'] && item['${targetValue}'].endsWith && !item['${targetValue}'].endsWith('${value}')
          });`;
          break;
        case 'in':
          filter.extraParam = 'value';
          filter.extraValue = value;
          filter.content = `return data.filter(item => {
            if (Array.isArray(value)) {
              return value.includes(item['${targetValue}']);
            }
            return false;
          });`;
          break;
      }
      return filter;
    },
    initCompDrillDowns () { // 初始化下钻数据
      for (const e of this.drillDown) {
        if (drillSourceOpt.includes(this.sourceType) && e.drillType === 'server') {
          continue
        }
        if (e.linkType === 'parent') { // 下钻类型为父级模式
          const parentField = e.parentField || 'parent'
          const parentFieldVal = e.parentFieldVal || null
          const validFilters = [
            { content: `return data.filter(item => item.${parentField} == '${parentFieldVal}')`, callbackKeys: [] }
          ]
          const filterData = dataUtil.linkAgeFilterData(this.originData, {}, validFilters)
          this.compData = Object.freeze(filterData);
        } else if (e.linkType === 'links') { // 下钻类型为级联模式
          this.drilldownByPath([]);
        }
      }
    },
    // 此处有问题，需要优化，兼容多层对象数据
    funJumpEvent (e, param) {
      if (e.jumpInfo && e.jumpInfo.jumpUrl && e.jumpInfo.jumpUrl !== '') {
        // eslint-disable-next-line
        let originUrl = e.jumpInfo.jumpUrl.replace(/\$\{([^\{|\}]+)\}/g, ($0, $1) => {
          const callbackObj = this.callbackManager().pick([$1])
          if (!Object.values(callbackObj).length) {
            return _.get(param, $1)
          } else {
            if ($1 && $1.includes('.')) {
              try {
                // eslint-disable-next-line
                const paramFun = new Function('data', `return data.${$1}`)
                return paramFun(callbackObj)
              } catch (error) {}
            } else {
              return callbackObj[$1]
            }
          }
        })
        let urlParam = ''
        if (e.jumpInfo.paramsList.length > 0) {
          const isParam = (e.jumpInfo.jumpUrl.split('#')[1] || e.jumpInfo.jumpUrl).includes('?')
          e.jumpInfo.paramsList.forEach((item, idx) => {
            if (item.length > 0) {
              const isUrlParam = urlParam.includes('?')
              if (item[0] !== '' && e.jumpInfo.mappingName[idx] !== '' && item[0].includes('${')) {
                const s = item[0].replace(/[${}]/g, '').replace(/\.[\w\W]*/g, '')
                const callbackObj = this.callbackManager().pick([s])
                if (isParam || isUrlParam) {
                  urlParam += '&' + e.jumpInfo.mappingName[idx] + '=' + callbackObj[s]
                } else {
                  urlParam += '?' + e.jumpInfo.mappingName[idx] + '=' + callbackObj[s]
                }
              } else {
                if (item[0] !== '' && e.jumpInfo.mappingName[idx] !== '') {
                  if (isParam || isUrlParam) {
                    urlParam += '&' + e.jumpInfo.mappingName[idx] + '=' + _.get(param, this.getObjPathByArr(item))
                  } else {
                    urlParam += '?' + e.jumpInfo.mappingName[idx] + '=' + _.get(param, this.getObjPathByArr(item))
                  }
                }
              }
            }
          })
        }
        if (originUrl.indexOf('/preview/index.html') > -1) {
          // 是否是服务端渲染
          const isSsrRender = window.location.href.includes('/screen/share/') && !window.location.href.includes('/preview/index.html')
          if (isSsrRender) {
            originUrl = window.location.origin + window.location.pathname.replace(/\/screen\/share\/[a-zA-Z0-9]+/, originUrl.split('#')[1]);
          } else {
            originUrl = window.location.origin + process.env.BASE_URL + 'preview/index.html' + originUrl.split('/preview/index.html')[1];
          }
        }
        window.open(originUrl + urlParam, e.jumpInfo.jumpIsLocal ? '_self' : '_blank')
      }
    },
    // 根据数组拼接对象路径
    getObjPathByArr (arr) {
      let tempPath = ''
      arr.forEach((item, idx) => {
        if (idx === 0) {
          tempPath += item
        } else {
          tempPath += '.' + item
        }
      })
      return tempPath
    },
    async handleSendReq (e, param) { // 发送请求动作
      const executionOrder = e.executionOrder // 请求执行类型
      const sendEvents = e.eventSend // 请求
      if (executionOrder === 'await') {
        for (const send of sendEvents) {
          await this.handleSendCustomReq(send, param).then(() => {
            this.sendEventHandler(send, param)
          })
        }
      } else {
        const sendArray = sendEvents.map(item => {
          return this.handleSendCustomReq(item, param)
        })
        Promise.all(sendArray).then(() => {
          this.sendEventHandler(e.sendReqAllAfter, param)
        })
      }
    },
    sendEventHandler (send, param) { // 处理发送请求动作-处理事件
      switch (send.action) {
        case 'sendReqNormal': // 无动作
          break
        case 'updateComp': // 更新组件数据
          emitter.emit('refreshApiData', { componentId: send.componentId })
          break
        case 'sendReqTips': { // 弹窗提示
          const title = send.alert.title
          const content = send.alert.content
          const buttonText = send.alert.buttonText
          this.$alert(title, content, {
            confirmButtonText: buttonText,
            callback: () => {}
          })
          break
        }
        case 'sendReqJumpEvent': // 链接跳转
          this.funJumpEvent(send, param)
          break
        default:
      }
    },
    handleExeScript (e, param = '') { // 处理脚本
      if (!e.scripts.length) return
      try {
        const paramsStr = Object.keys(param)?.toString() || ''
        // eslint-disable-next-line
        const func = new Function(paramsStr, e.scripts[0].code).bind(this);
        func(...Object.values(param));
      } catch (error) {
        // this.$message.error('error')
      }
    },
    handleAnchorLocation (e, param) { // 定位到指定组件
      if (e?.locatedCompId) {
        const domTop = document.querySelector(`div[data-comp-id=${e?.locatedCompId}]`).getBoundingClientRect().top
        document.querySelector('.screen-container').scrollTop += domTop
      }
    },
    async handleCompMounted () {
      this.compMounted = true
      this.permissionShow = _.isNil(this.permissionValue()) || this.permissionValue()
      this.$emit('compLoaded')
      await runAnimation(this.$el, this.comCfg.animation.animationIn)
      runAnimation(this.$el, this.comCfg.animation.animationKeep, 'animationKeep')
    },
    permissionValue () {
      const permissionMap = this.permissionMap()
      let value = true
      if (permissionMap && permissionMap.size) {
        const key = Array.from(permissionMap.keys()).filter(item => {
          return item.includes(this.id)
        })
        value = permissionMap.get(key[key.length - 1])?.result
      }
      return value
    },
    handleCompDestroyed () {
      this.compMounted = false
    },
    handleRerender () {
      const childRefs = this.$refs.childCompRef
      if (childRefs && childRefs.length) {
        childRefs.forEach(childComp => {
          const comp = childComp.$refs.compRef
          if (comp && _.isFunction(comp.render)) {
            comp.render()
          }
        })
      }
    },
    refreshApiDataHandler ({ componentId }) { // 刷新数据
      if (componentId.includes(this.id)) {
        this.initCompData()
      }
    },
    initZippedImage () { // 初始化压缩图片
      const { config, attr } = this.comCfg; const actulAttr = { w: attr.w, h: attr.h };
      if (this.platform === 'mobile' && this.comCfg.comName !== 'interaction-container-flowlayoutpanel') {
        actulAttr.w = document.documentElement.clientWidth / 24 * attr.w * 2;
        actulAttr.h = 10 * attr.h * 2;
      }
      let url = '';
      const zipable = ['png', 'jpg', 'jpeg']; // 支持压缩的图片类型
      if (config) {
        if (config.background && config.background.show && config.background.type === 'image') {
          url = config.background.image.url;
        }
        if (config.url) {
          url = config.url;
        }
        const isZip = getQueryVariable('isZip', url.split('?')[1]);
        if (url && url.indexOf('/public/') > -1 && isZip !== 'false') {
          url = replaceUrl(url.split('?')[0], 'imageServer');
          const path = url.split('/public/')[1];
          const ext = url.split('.').pop();
          if (zipable.includes(ext)) {
            const encryptUrl = '/' + encryptTilemapPath(path) + '.' + ext;
            const fastPath = fastLoadedImg(encryptUrl, 'string', actulAttr);
            if (fastPath) {
              const fullPath = url.split('/public/')[0] + fastPath;
              if (config.background && config.background.show && config.background.type === 'image') {
                config.background.image.url = fullPath;
              }
              if (config.url) {
                config.url = fullPath;
              }
            }
          }
        }
      }
    },
    // fix：解决大屏无限loading问题
    seatom_emit (...params) {
      const ets = []
      if (params[0] === 'COMMON_CLOSE') { // 控制自身隐藏 通用事件
        ets.push({
          comId: this.id,
          type: 'event',
          data: {
            event: {
              action: 'hide',
              componentId: [this.id],
              isConnect: false,
              connectCompId: []
            },
            params: {},
            source: params[2]
          }
        })
      }

      const trigger = params[0]
      const events = [...ets];
      const links = [];
      const drills = [];
      if (_.has(this.eventMap, trigger)) {
        const { event, link, drill } = this.eventMap[trigger];
        if (event.length) {
          event.forEach(item => {
            const { componentId = [], isConnect, connectCompId, action } = item;
            const evts = [];
            // 多个组件取反逻辑
            if (componentId.length > 1 && isConnect && connectCompId.length) {
              // 选中组件取默认行为
              connectCompId.forEach(id => {
                evts.push({
                  comId: id,
                  type: 'event',
                  data: {
                    event: item,
                    params: params[1],
                    source: params[2]
                  }
                })
              })
              // 其余组件取相反操作
              const otherComponentId = _.difference(componentId, connectCompId);
              otherComponentId.forEach(id => {
                evts.push({
                  comId: id,
                  type: 'event',
                  data: {
                    event: {
                      ...item,
                      action: action === COMMON_ACTIONS.show ? COMMON_ACTIONS.hide : action === COMMON_ACTIONS.hide ? COMMON_ACTIONS.show : action
                    },
                    params: params[1],
                    source: params[2]
                  }
                })
              })
            } else {
              componentId.forEach(id => {
                if (id && id.startsWith('groups_')) { // 控制分组
                  if (this.platform === 'mobile') { // 移动端控制分组事件
                    evts.push({
                      comId: id,
                      type: 'event',
                      data: {
                        event: item,
                        params: params[1],
                        source: params[2]
                      }
                    })
                  } else { // pc端如果控制分组，则给分组下组件派发事件
                    const group = getNodeByParam(this.layerTree.data.children, 'id', id);
                    if (group) {
                      const coms = getGroupComs(group);
                      coms.forEach(com => {
                        evts.push({
                          comId: com.id,
                          type: 'event',
                          data: {
                            event: item,
                            params: params[1],
                            source: params[2]
                          }
                        })
                      })
                    }
                  }
                } else { // 控制组件
                  evts.push({
                    comId: id,
                    type: 'event',
                    data: {
                      event: item,
                      params: params[1],
                      source: params[2]
                    }
                  })
                }
              })
            }
            events.push(...evts);
          })
        }
        if (link.length) {
          link.forEach(item => {
            const { linkageComList } = item;
            linkageComList.forEach(com => {
              links.push({
                comId: com.componentId,
                type: 'link',
                data: {
                  event: item,
                  params: params[1],
                  source: params[2] || 'normal'
                }
              })
            })
          })
        }
        if (drill.length) {
          drill.forEach(item => {
            drills.push({
              comId: this.id,
              type: 'drill',
              data: {
                event: item,
                params: params[1]
              }
            })
          })
        }
      }
      const taskQueue = [...events, ...links, ...drills];
      if (taskQueue.length) {
        this.$emit('seatom_eventsAndCallbacks', { type: 'event', taskQueue });
      }
    },
    // 回调函数
    seatom_updateCallbackValue (params = {}) {
      const callbackParams = this.comCfg.interactionConfig.callbackParams || [];
      if (callbackParams && callbackParams.length) {
        this.$emit('seatom_eventsAndCallbacks', { type: 'callback', callbackParams, params });
      }
    },
    seatom_setParentStyle (style = {}) {
      Object.keys(style).forEach(key => {
        if (key === 'zIndex') {
          this.$el.style.zIndex = style[key];
        } else {
          this.$el.style.setProperty(key, style[key])
        }
      })
    },
    compBoxClick (source) {
      this.seatom_emit('compClick', this.compData[0], source);
    },
    compBoxMouseover: _.throttle(function (source) {
      this.seatom_emit('compHover', this.compData[0], source);
    }, 200),
    compBoxDbclick (source) {
      this.seatom_emit('compDbclick', this.compData[0], source);
    },
    compBoxMouseleave (source) {
      this.seatom_emit('compMouseleave', this.compData[0], source);
    }
  }
}
</script>

<style lang="scss" scoped>
.view-comp-node {
  position: absolute;
  left: 0;
  top: 0;
  .transform-box {
    width: 100%;
    height: 100%;
    .render-box {
      .component {
        width: 100%;
        height: 100%;
      }
    }
  }
  .link-wrap {
    position: absolute;
    top: -20px;
    left: 0;
    display: flex;
    z-index: 10;
    .link-item {
      font-size: 12px;
      color: #151618;
      margin-right: 5px;
      cursor: pointer;
    }
  }
  .error-popup {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgb(255 255 255 / 80%);
    overflow: hidden;
    font-size: 16px;
    z-index: 100;
  }
  .permission-error {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 101;
  }
}
</style>
