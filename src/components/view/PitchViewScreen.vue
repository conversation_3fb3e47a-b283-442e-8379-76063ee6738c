<template>
  <div class="screen-view-wrap_box">
    <div class="screen-view-wrap" :style="pitchStyle">
      <template v-if="platform === 'pc'">
        <template v-for="(item, index) in screenLayers">
          <template v-if="item.type === 'com'">
            <ViewCompNode
              :key="item.id"
              :workspaceId="workspaceId"
              :id="item.id"
              :mainScreenId="mainScreenId"
              :type="item.type"
              :item-data="itemData"
              :zIndex="screenLayers.length - index"
              :platform="platform"
              v-on="$listeners" />
          </template>
          <template v-else>
            <ViewGroupComp
              :key="item.id"
              :workspaceId="workspaceId"
              :id="item.id"
              :mainScreenId="mainScreenId"
              :pageLayers="item.children"
              :item-data="itemData"
              :zIndex="screenLayers.length - index"
              :platform="platform"
              v-on="$listeners" />
          </template>
        </template>
      </template>
      <template v-else>
        <grid-layout
          ref="gridlayout"
          :style="{
            width: screenStyle.width + 'px'
          }"
          :layout.sync="layout"
          :col-num="24"
          :row-height="10"
          :is-draggable="false"
          :is-resizable="false"
          :is-mirrored="false"
          :vertical-compact="true"
          :margin="[5, 5]"
          :use-css-transforms="true"
          isView>
          <grid-item
            v-for="(item, index) in layout"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
            :key="item.id"
            :style="{
              zIndex: layout.length - index
            }">
            <ViewCompNode
              :workspaceId="workspaceId"
              :id="item.id"
              :mainScreenId="mainScreenId"
              :type="item.type"
              :item-data="itemData"
              :zIndex="layout.length - index"
              :platform="platform"
              v-on="$listeners"
              @seatom_setComConfig="handleSetComConfig" />
          </grid-item>
        </grid-layout>
      </template>
    </div>
  </div>
</template>

<script>
import { M_ZERO_HEIGHT } from '@/common/constants'
import VueGridLayout from '@/lib/vue-grid-layout.common'
import Tree from '@/lib/Tree'

export default {
  name: 'PitchViewScreen',

  components: {
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem
  },

  props: {
    showScroll: { // 是否显示滚动条
      type: Boolean,
      default: false
    },
    itemData: {
      type: Array,
      default: () => []
    },
    screenStyle: {
      type: Object,
      default: () => ({})
    },
    screenFilters: {
      type: Object,
      default: () => ({})
    },
    screenComs: {
      type: Object,
      default: () => {}
    },
    screenLayers: {
      type: Array,
      default: () => []
    },
    workspaceId: {
      type: [Number, String],
      default: 0
    },
    platform: {
      type: String,
      default: 'pc'
    },
    isView: {
      type: Boolean,
      default: false
    },
    heightAuto: {
      type: Boolean,
      default: false
    },
    // 主屏ID，使用引用面板引用的屏也是主屏
    mainScreenId: {
      type: Number,
      required: true
    }
  },

  inject: ['callbackFatherManager', 'permissionMap'],
  provide: function () {
    return {
      getLayerTree: () => this.layerTree, // 通过函数实现响应式
      filtersMap: () => this.screenFilters,
      screenComs: () => this.screenComs,
      callbackManager: () => this.callbackManager,
      permissionMap: this.permissionMap
    }
  },
  data () {
    return {
      loadCount: 0,
      layerTree: new Tree()
    }
  },

  computed: {
    compCount () {
      const showLayers = this.screenLayers.filter(l => {
        return this.screenComs[l.id] && this.screenComs[l.id].show
      })
      return showLayers.length
    },
    layout () {
      const { screenComs, screenLayers } = this;
      function getMobileHeight (comCfg) { // 获取移动端组件h
        if (!comCfg.show) return M_ZERO_HEIGHT;
        return comCfg.attr.h
      }
      const layers = screenLayers.filter(item => !item.id.startsWith('interaction-container-modulepanel'));
      const result = [];
      layers.forEach(item => {
        const com = screenComs[item.type === 'com' ? item.id : item.comId];
        if (com) {
          result.push({
            ...com,
            x: com.attr.x,
            y: com.attr.y,
            w: com.attr.w,
            h: getMobileHeight(com),
            i: com.id,
            id: com.id,
            type: com.type
          })
        }
      })
      return result
    },
    pitchStyle () { // screenStyle实际是大屏的config配置，需要转换成真实的大屏的style
      const { screenStyle } = this;
      const { width, height, backgroundColor, backgroundImage, backgroundRepeat, globalFilterParams } = screenStyle;
      const setBackground = (image, repeat = 'fill') => {
        let cssText = ''
        if (!image) return cssText
        switch (repeat) {
          case 'fill':
            cssText += ` url(${image}) left top/100% 100% no-repeat`
            break
          case 'contain':
            cssText += ` url(${image}) center/contain no-repeat`
            break
          case 'cover':
            cssText += ` url(${image}) left top/cover no-repeat`
            break
          case 'repeat':
            cssText += ` url(${image}) left top/auto repeat`
            break
          default:
            break
        }
        return cssText
      }
      const bgImage = setBackground(backgroundImage, backgroundRepeat);
      const background = backgroundImage ? bgImage : backgroundColor
      const style = {
        width: width + 'px',
        height: height + 'px',
        background
      }
      if (globalFilterParams?.enable) {
        const { hue, saturate, brightness, contrast, opacity, grayscale } = globalFilterParams
        style.filter = `
          hue-rotate(${hue}deg)
          saturate(${saturate}%)
          brightness(${brightness}%)
          contrast(${contrast}%)
          opacity(${opacity}%)
          grayscale(${grayscale}%)
        `
      }
      if (this.showScroll) {
        style.overflow = 'auto'
      }
      if (this.platform === 'mobile') {
        style.width = '100%';
        if (this.heightAuto && this.isView) { // 预览状态高度设置auto，否则编辑页展示有问题
          style.height = 'auto !important'
        }
      }
      return style
    }
  },

  created () {
    this.initCallbackManager()
    this.layerTree = new Tree({ id: 'root', children: this.screenLayers })
  },
  methods: {
    initCallbackManager () {
      const { screenComs } = this
      if (!this.screenComs) return

      _.values(screenComs).forEach(com => {
        if (com.interactionConfig && com.interactionConfig.callbackParams) {
          com.interactionConfig.callbackParams.forEach(cb => {
            this.callbackFatherManager().addCallbackKey(cb.variableName)
          })
        }
      })
      this.callbackManager = this.callbackFatherManager()
    },
    handleSetComConfig (id, keyValPairs = []) { // 移动端设置组件config
      const com = this.screenComs[id]
      if (com) {
        keyValPairs.forEach(pair => {
          _.set(com, pair.key, pair.value);
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-view-wrap_box {
  position: relative;
  width: 100%;
  height: 100%;
}
.screen-view-wrap {
  // position: absolute;
  // left: 0;
  // top: 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transform-origin: left top;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
</style>
