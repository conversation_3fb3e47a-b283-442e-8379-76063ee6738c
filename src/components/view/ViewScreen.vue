<template>
  <div class="screen-view-wrap_box">
    <div ref="screen"  class="screen-view-wrap" :style="screenStyle">
      <!-- 粒子效果容器 -->
      <Particles
        :key="ParticlesKey"
        v-if="screenConfig.backgroundParticlesType !== 'STYLE_NONE' && screenConfig.backgroundParticlesType"
        :options="computedParticlesConfig"
        :id="`${screenId}_preview_particles`" />
      <template v-if="platform === 'pc'">
        <template v-for="(item, index) in pageLayers">
          <template v-if="item.type === 'com'">
            <ViewCompNode
              :key="item.id"
              :workspaceId="workspaceId"
              :id="item.id"
              :mainScreenId="mainScreenId"
              :type="item.type"
              :item-data="itemData"
              :zIndex="pageLayers.length - index"
              :platform="platform"
              @compLoaded="handleCompLoaded"
              @seatom_eventsAndCallbacks="handleSeatomEventsAndCallbacks" />
          </template>
          <template v-else>
            <ViewGroupComp
              :key="item.id"
              :workspaceId="workspaceId"
              :id="item.id"
              :mainScreenId="mainScreenId"
              :pageLayers="item.children"
              :item-data="itemData"
              :zIndex="pageLayers.length - index"
              :platform="platform"
              @compLoaded="handleCompLoaded"
              @seatom_eventsAndCallbacks="handleSeatomEventsAndCallbacks" />
          </template>
        </template>
      </template>
      <template v-else-if="platform === 'mobile'">
        <template v-if="screenType === 'common' || screenType === 'child'">
          <grid-layout
            ref="gridlayout"
            :style="{
              width: screenInfo.config.width + 'px'
            }"
            :layout.sync="layout"
            :col-num="24"
            :row-height="10"
            :is-draggable="false"
            :is-resizable="false"
            :is-mirrored="false"
            :vertical-compact="true"
            :margin="[5, 5]"
            :use-css-transforms="false">
            <grid-item
              v-for="(item, index) in layout"
              :x="item.x"
              :y="item.y"
              :w="item.w"
              :h="item.h"
              :i="item.i"
              :key="item.id"
              :style="{
                zIndex: layout.length - index
              }">
              <ViewCompNode
                :workspaceId="workspaceId"
                :id="item.id"
                :mainScreenId="mainScreenId"
                :type="item.type"
                :item-data="itemData"
                :zIndex="layout.length - index"
                :platform="platform"
                @compLoaded="handleCompLoaded"
                @seatom_eventsAndCallbacks="handleSeatomEventsAndCallbacks"
                @seatom_setComConfig="handleSetComConfig" />
            </grid-item>
          </grid-layout>
        </template>
        <template v-if="screenType === 'scene'">
          <!-- 移动端 新版动态面板 场景大屏方式 -->
          <template v-if="isContainer">
            <grid-layout
              v-if="!!pageLayout[0]"
              ref="gridlayout"
              :style="{
                width: screenInfo.config.width + 'px'
              }"
              :layout.sync="pageLayout[0].layout"
              :col-num="24"
              :row-height="10"
              :is-draggable="false"
              :is-resizable="false"
              :is-mirrored="false"
              :vertical-compact="true"
              :margin="[5, 5]"
              :use-css-transforms="true">
              <grid-item
                v-for="(item, index) in pageLayout[0].layout"
                :x="item.x"
                :y="item.y"
                :w="item.w"
                :h="item.h"
                :i="item.i"
                :key="item.id"
                :style="{
                  zIndex: pageLayout[0].layout.length - index
                }">
                <ViewCompNode
                  :workspaceId="workspaceId"
                  :id="item.id"
                  :mainScreenId="mainScreenId"
                  :type="item.type"
                  :item-data="itemData"
                  :zIndex="layout.length - index"
                  :platform="platform"
                  @compLoaded="handleCompLoaded"
                  @seatom_eventsAndCallbacks="handleSeatomEventsAndCallbacks"
                  @seatom_setComConfig="handleSetComConfig" />
              </grid-item>
            </grid-layout>
          </template>
          <template v-else>
            <full-page
              :options="options"
              v-if="sceneConfig.length && pageLayout.length">
              <template v-for="layout in pageLayout">
                <div class="section" :key="layout.page.pageId" :style="pageStyle(layout.page)">
                  <grid-layout
                    ref="gridlayout"
                    :style="{
                      width: screenInfo.config.width + 'px'
                    }"
                    :layout="layout.layout"
                    :col-num="24"
                    :row-height="10"
                    :is-draggable="false"
                    :is-resizable="false"
                    :is-mirrored="false"
                    :vertical-compact="true"
                    :margin="[5, 5]"
                    :use-css-transforms="true">
                    <grid-item
                      v-for="(item, index) in layout.layout"
                      :x="item.x"
                      :y="item.y"
                      :w="item.w"
                      :h="item.h"
                      :i="item.i"
                      :key="item.id"
                      :style="{
                        zIndex: layout.layout.length - index
                      }">
                      <ViewCompNode
                        :workspaceId="workspaceId"
                        :id="item.id"
                        :mainScreenId="mainScreenId"
                        :type="item.type"
                        :item-data="itemData"
                        :zIndex="layout.length - index"
                        :platform="platform"
                        @compLoaded="handleCompLoaded"
                        @seatom_eventsAndCallbacks="handleSeatomEventsAndCallbacks"
                        @seatom_setComConfig="handleSetComConfig" />
                    </grid-item>
                  </grid-layout>
                </div>
              </template>
            </full-page>
          </template>
        </template>
      </template>
      <HighlightBlock v-if="!(isContainer || isReferPanel)" />
    </div>
    <seatom-loading v-if="showLoading && loading" :style="{ maxHeight: $parent.platform == 'mobile' ? '300px' : 'unset' }"></seatom-loading>
  </div>
</template>

<script>
import Vue from 'vue'
import { GridLayout, GridItem } from '@/lib/vue-grid-layout.common';
import VueFullpage from '@/lib/vue-fullpage/vue-fullpage.js'
import { sendCustomReq } from '@/api/common'
import { getScreens, updateScreenParentId } from '@/api/screen'
import CallbackManager from '@/lib/CallbackManager'
import GlobalDataManager from '@/lib/GlobalDataManager'
import { M_ZERO_HEIGHT, mobileZeroComs } from '@/common/constants'
import * as PARTICLES_THEME from '@/common/particlesTheme'
import { uuid, replaceUrl, encryptTilemapPath, fastLoadedImg } from '@/utils/base'
import { addImportComponents } from '@/utils/systemImport'
import { screenDataImageReplaceUrl } from '@/utils/screen'
import VueSocket from 'vue-socket.io';
import ClientSocketIO from 'socket.io-client';

import emitter from '@/utils/bus'
import Tree from '@/lib/Tree';
import { mapActions } from 'vuex'

Vue.use(VueFullpage)

export default {
  name: 'ViewScreen',

  components: {
    GridLayout,
    GridItem,
    HighlightBlock: () => import('@/components/editor/voicecontrol/HighlightBlock')
  },

  provide: function () {
    return {
      getLayerTree: () => this.layerTree, // 通过函数实现响应式
      filtersMap: () => this.filtersMap,
      screenComs: () => this.screenComs,
      callbackManager: () => this.callbackManager,
      getScale: this.getScale(),
      permissionMap: () => this.permissionMap
    }
  },

  inject: ['callbackFatherManager', 'permissionFatherMap'],

  props: {
    screenId: {
      type: [String, Number],
      required: true
    },
    // 大屏数据，如果传入不为空，则直接使用
    screenData: {
      type: Object
    },
    parent: { // 是否定义父级元素，如果定义，则适应父级宽高，否则适应窗口变化
      type: Boolean,
      default: false
    },
    showLoading: { // 显示内置的loading
      type: Boolean,
      default: false
    },
    itemData: {
      type: Array,
      default: () => ([])
    },
    isContainer: { // 是否是容器
      type: Boolean,
      default: false
    },
    isReferPanel: { // 是否是引用面板
      type: Boolean,
      default: false
    },
    heightAuto: {
      type: Boolean,
      default: false
    },
    lazyload: { // 是否开启首屏优先加载 （容器无效）
      type: Boolean,
      default: false
    },
    authObj: {
      type: Object,
      default: () => ({})
    },
    // 父级主面板ID
    parentMainScreenId: {
      type: Number
    }
  },

  data () {
    return {
      layerTree: new Tree(),
      ParticlesKey: uuid('Particles'),
      PARTICLES_THEME,
      screenInfo: {},
      screenComs: {},
      screenConfig: {},
      screenLayers: [],
      screenType: 'common',
      sceneConfig: [],
      sceneId: null,
      pageId: null,
      filtersMap: {},
      screenName: '',
      workspaceId: 0, // '',
      platform: 'pc',
      parentSize: {
        width: 0,
        height: 0
      },
      scale: 1,
      initEvents: [],
      loadCount: 0,
      loading: true,
      options: {
        licenseKey: 'OPEN-SOURCE-GPLV3-LICENSE',
        navigation: true,
        verticalCentered: false,
        sectionsColor: [],
        scrollBar: false
      },
      layout: [],
      pageLayout: [],
      permissionMap: new Map(),
      loadedComp: [],
      crossSocket: null,
      roomId: '', // 跨屏联动的房间号
      childScreens: []
    }
  },

  computed: {
    // 主屏Id，动态面板要找parentId
    mainScreenId () {
      // 不是动态面板代表是主面板
      if (!this.screenInfo.isDynamicScreen) {
        return Number(this.screenInfo.id)
      }

      // 取传递的父级ScreenId，因为this.screenInfo.parentId在数据库有错误
      if (this.parentMainScreenId) {
        return this.parentMainScreenId
      } else {
        return Number(this.screenInfo.parentId)
      }
    },
    ratioObj () {
      const { screenConfig, parentSize } = this
      if (_.isEmpty(screenConfig)) return {}
      const { width, height } = screenConfig
      return { w: parentSize.width / width, h: parentSize.height / height }
    },
    screenStyle () {
      const { screenConfig, parentSize, screenInfo } = this
      if (_.isEmpty(screenConfig)) return screenConfig
      const {
        scaleType,
        width,
        height,
        backgroundColor,
        backgroundImage,
        backThumbnail,
        backgroundRepeat,
        globalFilterParams
      } = screenConfig
      const ratio = this.ratioObj
      const style = {
        width: width + 'px',
        height: height + 'px'
      }

      const setBackground = (image, repeat = 'fill') => {
        let cssText = ''
        if (!image) return cssText
        let url = image;
        const zipable = ['png', 'jpg', 'jpeg']; // 支持压缩的图片类型
        const isZip = backThumbnail
        let attr = {
          w: width,
          h: height
        };
        switch (scaleType) {
          case 'full_screen':
            attr.w = ratio.w * width;
            attr.h = ratio.h * height;
            break
          case 'full_width':
            attr.w = ratio.w * width;
            attr.h = ratio.w * height;
            break
          case 'full_height':
          case 'full_height_scroll':
            attr.w = ratio.h * width;
            attr.h = ratio.h * height;
            break
          case 'no_scale':
            break
        }
        if (this.platform === 'mobile' && !screenInfo.isDynamicScreen) {
          attr = {
            w: width
          };
        }
        if (url && url.indexOf('/public/') > -1 && isZip) {
          url = replaceUrl(url.split('?')[0], 'imageServer');
          const path = url.split('/public/')[1];
          const ext = url.split('.').pop();
          if (zipable.includes(ext)) {
            const encryptUrl = '/' + encryptTilemapPath(path) + '.' + ext;
            const fastPath = fastLoadedImg(encryptUrl, 'string', attr);
            if (fastPath) {
              const fullPath = url.split('/public/')[0] + fastPath;
              image = fullPath
            }
          }
        }
        switch (repeat) {
          case 'fill':
            cssText += ` url(${image}) left top/100% 100% no-repeat`
            break
          case 'contain':
            cssText += ` url(${image}) center/contain no-repeat`
            break
          case 'cover':
            cssText += ` url(${image}) left top/cover no-repeat`
            break
          case 'repeat':
            cssText += ` url(${image}) left top/auto repeat`
            break
          default:
            break
        }
        return cssText
      }

      if (this.screenType === 'scene') {
        const scene = this.sceneConfig.find(item => {
          return item.pageList.findIndex(page => page.pageId === this.pageId) > -1
        })
        if (scene) {
          let page = {}
          if (this.pageId) {
            page = scene.pageList.find(p => p.pageId === this.pageId)
          }
          const target = [{ ...page, backgroundImage: page.pageBackground }, {
            ...scene,
            backgroundImage: scene.sceneBackground
          }, { backgroundColor, backgroundImage, backgroundRepeat }].find(item => {
            return item.backgroundColor || item.backgroundImage
          })
          if (target) {
            if (target.backgroundImage) {
              style.background = `${setBackground(target.backgroundImage, target.backgroundRepeat)}`
            } else {
              style.background = `${target.backgroundColor}`
            }
          } else {
            if (backgroundImage) {
              style.background = `${setBackground(backgroundImage, backgroundRepeat)}`
            } else {
              style.background = `${backgroundColor}`
            }
          }
        }
      } else {
        if (backgroundImage) {
          style.background = `${setBackground(backgroundImage, backgroundRepeat)}`
        } else {
          style.background = `${backgroundColor}`
        }
      }

      if (globalFilterParams.enable) {
        const { hue, saturate, brightness, contrast, opacity, grayscale } = globalFilterParams
        style.filter = `
          hue-rotate(${hue}deg)
          saturate(${saturate}%)
          brightness(${brightness}%)
          contrast(${contrast}%)
          opacity(${opacity}%)
          grayscale(${grayscale}%)
        `
      }

      const centerScreen = (type, scale = 1) => {
        this.scale = scale;
        let cx = 0
        let cy = 0
        if (width * scale < parentSize.width) {
          cx = `${(parentSize.width - width * scale) / 2}px`
        }
        if (height * scale < parentSize.height) {
          cy = `${(parentSize.height - height * scale) / 2}px`
        }
        switch (type) {
          case 'x':
            style.left = cx
            style.top = 0
            break
          case 'y':
            style.left = 0
            style.top = cy
            break
          default:
            style.left = cx
            style.top = cy
        }
      }
      if (this.platform === 'pc') {
        // 容器组件 不设置屏幕缩放
        if (!this.isContainer) {
          switch (scaleType) {
            case 'full_screen':
              style.transform = `scale(${ratio.w},${ratio.h})`
              break
            case 'full_width':
              style.transform = `scale(${ratio.w})`
              centerScreen('y', ratio.w)
              break
            case 'full_height':
            case 'full_height_scroll':
              style.transform = `scale(${ratio.h})`
              centerScreen('x', ratio.h)
              break
            case 'no_scale':
              centerScreen()
              break
          }
        }
      } else if (this.platform === 'mobile') { // 移动端
        if (this.screenType === 'common') { // 自定义布局
          if ((!this.isContainer) && (!this.isReferPanel)) {
            style.transform = `scale(${ratio.w})`
            centerScreen('y', ratio.w)
            delete style.height
          } else {
            style.width = '100%';
            style.overflow = 'hidden';
            if (!this.heightAuto) { // 自适应高度关闭 动态面板出滚动条
              style.overflowY = 'auto';
            } else {
              delete style.height
            }
          }
          style.minHeight = height + 'px'
          style.position = 'unset'
        } else { // 分页布局
          if ((!this.isContainer) && (!this.isReferPanel)) {
            style.transform = `scale(${ratio.w})`
            centerScreen('y', ratio.w)
            style.overflow = 'hidden'
            delete style.background
          } else { // 移动端容器组件
            style.width = '100%'
            style.height = '100%'
            style.overflow = 'hidden';
            if (!this.heightAuto) { // 自适应高度关闭 新版动态面板出滚动条
              style.overflowY = 'auto';
            }
          }
          style.height = parentSize.height / ratio.w + 'px'
          style.position = 'unset'
        }
        // 容器组件 删除最小高度，防止出现滚动条
        if (this.isContainer) {
          delete style.minHeight;
          style.height = '100%';
        }
      }
      return style
    },
    pageStyle () {
      return function (page) {
        const { backgroundColor, backgroundImage } = this.screenConfig
        const setBackground = (image, repeat = 'fill') => {
          let cssText = ''
          if (!image) return cssText
          switch (repeat) {
            case 'fill':
              cssText += ` url(${image}) left top/100% 100% no-repeat`
              break
            case 'contain':
              cssText += ` url(${image}) center/contain no-repeat`
              break
            case 'cover':
              cssText += ` url(${image}) left top/cover no-repeat`
              break
            case 'repeat':
              cssText += ` url(${image}) left top/auto repeat`
              break
            default:
              break
          }
          return cssText
        }
        if (page.pageBackground) {
          return {
            background: `${setBackground(page.pageBackground, page.backgroundRepeat)}`
          }
        }
        if (page.backgroundColor) {
          return {
            background: page.backgroundColor
          }
        }
        const style = {
          background: backgroundColor
        }
        backgroundImage && (style.background = `url(${backgroundImage})`)
        return style
      }
    },
    pageLayers () {
      if (this.screenType === 'scene') {
        if (this.pageId) {
          return this.screenLayers.filter(layer => layer.pageId === this.pageId || (layer.sceneId === this.sceneId && !layer.pageId))
        } else {
          return this.screenLayers.filter(layer => layer.sceneId === this.sceneId && !layer.pageId)
        }
      } else {
        return this.screenLayers
      }
    },
    compCount () {
      const showLayers = this.pageLayers.filter(l => {
        return this.screenComs[l.id] && this.screenComs[l.id].show && this.screenComs[l.id].comType !== 'interaction-container-modulepanel'
      })
      return showLayers.length
    },
    showScene () {
      return this.screenType === 'scene' && this.sceneConfig.length
    },
    pageList () {
      const list = []
      this.sceneConfig.forEach(scene => {
        list.push(...scene.pageList)
      })
      return list
    },
    getComDataById () {
      return function (id) {
        if (_.has(this.screenComs, id)) {
          return this.screenComs[id]
        } else {
          console.warn(`获取不到 id 为 ${id} 的组件数据！`)
        }
      }
    },
    computedParticlesConfig () {
      const custom = this.screenConfig.backgroundParticlesCustom
      const type = this.screenConfig.backgroundParticlesType
      const temp = PARTICLES_THEME[this.screenConfig.backgroundParticlesType]
      if (type === 'STYLE_NONE') return {}
      if (!custom) return temp
      if (_.isEmpty(custom)) {
        return {}
      }
      temp.particles.number.value = custom.number
      temp.particles.move.speed.min = custom.speed[0]
      temp.particles.move.speed.max = custom.speed[1]
      temp.particles.opacity.value.min = custom.opacity[0]
      temp.particles.opacity.value.max = custom.opacity[1]
      temp.particles.size.value.min = custom.size[0]
      temp.particles.size.value.max = custom.size[1]
      type === 'STYLE_METEOR'
        ? temp.particles.stroke.color.value = [...new Set(custom.color.map(item => item.value))]
        : temp.particles.color.value = [...new Set(custom.color.map(item => item.value))]
      return temp
    },
    // 服务端渲染数据
    ssrChildScreenInfo () {
      return this.$store.state.editor.ssrRender.childScreensInfo[this.screenId]
    },
    ssrShowCompIds () {
      return this.$store.state.editor.ssrRender.showCompIds
    }
  },

  watch: {
    $route: {
      handler: function (val) {
        this.callbackManager && this.callbackManager.updateCallbackValue({
          route: {
            ...val.params,
            ...val.query
          }
        })
      }
    },
    screenComs: {
      handler: function (val) {
        if (this.platform === 'mobile') {
          if (this.screenType === 'common') { // 自定义布局 刷新layout
            this.layout = this.transToLayout(this.layout)
          } else { // 新版动态面板或多页布局 刷新layout
            this.getLayout();
          }
        }
      },
      deep: true
    }
  },
  created () {
    this.fetchData().then(() => {
      this.$emit('dataLoad', this.screenName)
      this.$nextTick(() => {
        if (this.compCount <= 0) {
          this.$emit('loaded')
          this.loading = false
        }
        this.initParentStyle()
      })
    })

    emitter.on('onEvent', this.onEvent);
    this.$once('hook:beforeDestroy', () => {
      emitter.off('onEvent', this.onEvent);
    })
  },
  mounted () {
    if (!this.parent) {
      window.addEventListener('resize', e => {
        this.resize({
          width: document.documentElement.clientWidth,
          height: document.documentElement.clientHeight
        })
      })
    }
    this.initParentSize()
  },
  methods: {
    ...mapActions('event', [
      'pushFnQueue'
    ]),
    createSocketRoom (room) {
      const uid = uuid()
      const option = {
        transports: ['websocket'],
        path: '/api/socket',
        reconnectionAttempts: 10,
        query: {
          room,
          userId: localStorage.getItem('userId') || '',
          uid
        }
      }
      const socket = this.crossSocket = new VueSocket({
        debug: false,
        connection: ClientSocketIO.connect('/linkage', option)
      })
      socket.io.on('connect', () => {
        // console.log('连接成功')
      })
      socket.emitter.addListener('online', e => {
        // console.log('接收消息：', e)
      }, this)
      socket.emitter.addListener('linkage', e => {
        const { currId, params } = e
        if (currId === this.screenInfo.id) return true; // 自己发的事件，不处理
        this.pushFnQueue(params);
      }, this)
      socket.io.on('connect_error', () => {
        // console.log('connect_error')
      })
      socket.io.on('connection', () => {
        // console.log('connection')
      })
      socket.io.on('reconnect', () => {
        // console.log('reconnect')
      })
      // 重连失败
      socket.io.on('reconnect_failed', (e) => {
        // console.info('reconnect_failed')
      })
      // 重连错误
      socket.io.on('reconnect_error', (e) => {
        // console.info('reconnect_error')
      })
    },
    getScale () { // 获取缩放比例
      return () => {
        if (this.isReferPanel) {
          return this.$parent.getScale()
        }
        return this.scale
      }
    },
    async fetchData () {
      const { screenId } = this
      if (!screenId) {
        return
      }
      let screenData

      // 大屏数据，如果传入不为空，则直接使用
      if (this.screenData) {
        screenData = this.screenData
      } else if (this.ssrChildScreenInfo) {
        screenData = _.cloneDeep(this.ssrChildScreenInfo)
      } else {
        const params = { screenId }
        if (this.isReferPanel) { // 引用面板获取大屏信息时添加标识，防止导出后无权限无法进入编辑页
          params.isReferPanel = true
        }
        screenData = await this.$store.dispatch('editor/getPreviewScreen', params)
      }

      // 导入组件
      addImportComponents(screenData.components)

      // 替换图片url
      screenDataImageReplaceUrl(screenData)

      this.screenInfo = screenData;
      if (screenData.screenType !== 'child' && !screenData.isDynamicScreen) {
        const res = await getScreens({ screenType: 'child', parentId: screenData.id })
        if (res && res.success) {
          this.childScreens = res.data
          if (this.childScreens.length) {
            this.roomId = screenData.id
          }
        }
      } else if (screenData.screenType === 'child') {
        this.roomId = screenData.parentId
      } else {
        this.roomId = ''
      }
      if (this.roomId) {
        this.createSocketRoom(this.roomId)
      }
      if (this.permissionFatherMap && this.permissionFatherMap()) {
        this.permissionMap = this.permissionFatherMap()
      } else {
        await this.handlePermissionData()
      }

      // 检查修复面板类组件parentId错误问题
      if (this.screenInfo.isDynamicScreen &&
         !!this.parentMainScreenId &&
         Number(this.screenInfo.parentId) !== this.parentMainScreenId) {
        updateScreenParentId({
          // 保留原来的字符串类型
          parentId: String(this.parentMainScreenId)
        }, {
          id: this.screenInfo.id
        })
      }
      // 检查修复结束

      this.workspaceId = screenData.workspaceId
      this.screenType = screenData.screenType
      if (this.screenType === 'scene' && screenData.sceneConfig.length) {
        this.sceneConfig = screenData.sceneConfig
        this.sceneId = this.sceneConfig[0].sceneId
        this.pageId = this.sceneConfig[0].pageList[0].pageId
      }
      this.platform = screenData.type
      this.screenName = screenData.name
      this.screenComs = screenData.components
      this.layerTree = new Tree({ id: 'root', children: screenData.layers });
      this.initCallbackManager()
      this.$nextTick(() => {
        this.screenConfig = screenData.config
        this.screenLayers = screenData.layers
        this.filtersMap = Object.freeze(_.keyBy(screenData.filter, 'id'))
        if (this.platform === 'mobile') {
          this.getLayout(this.lazyload);
          if ((this.isContainer || this.isReferPanel) && this.heightAuto) {
            this.domObserver()
          }
        }
      })
    },
    getLayout (lazyload = false) { // 获取移动端布局layout
      if (this.platform !== 'mobile') return;
      if (this.screenType === 'common' || this.screenType === 'child') { // 自定义布局
        const layers = this.pageLayers.filter(item => !item.id.startsWith('interaction-container-modulepanel'));
        const layout = this.transToLayout(layers);
        if (!lazyload || this.isContainer) { // 1.容器组件不走懒加载
          this.layout = layout;
        } else {
          // 优先加载首屏
          const pages = [];
          layout.forEach(item => { // 根据高度分页
            const t = item.y * 10; // 顶部高度
            const pNum = parseInt(t / 667) > 0 ? 1 : 0;
            if (pages[pNum]) {
              pages[pNum].push(item)
            } else {
              pages[pNum] = [item]
            }
          })

          this.layout = [];
          if (pages[0]) {
            this.layout.push(...pages[0]);
          }
          if (pages[1]) {
            setTimeout(() => {
              this.layout.push(...pages[1]);
            }, 1500)
          }
        }
      } else if (this.screenType === 'scene') {
        if (this.isContainer) { // 新版动态面板
          let layout = []
          if (this.sceneId) {
            let layers = this.screenLayers.filter(layer => layer.sceneId === this.sceneId);
            layers = layers.filter(item => !item.id.startsWith('interaction-container-modulepanel'));
            layout = this.transToLayout(layers);
          }
          this.pageLayout = [{ layout }]
        } else { // 多页布局
          const pageLayout = this.sceneConfig[0].pageList.map(page => {
            let layers = this.screenLayers.filter(layer => layer.pageId === page.pageId);
            layers = layers.filter(item => !item.id.startsWith('interaction-container-modulepanel'));
            const layout = this.transToLayout(layers);
            return {
              page,
              layout
            }
          })
          this.pageLayout = pageLayout;
        }
      }
    },
    getMobileHeight (comCfg) { // 获取移动端组件h
      if (!comCfg.show) return M_ZERO_HEIGHT;
      if (mobileZeroComs.includes(comCfg.comName)) return M_ZERO_HEIGHT;

      // 预览容器使用屏幕高度，目前就预览容器有该配置，暂时只支持最外层，面板、分组内部不支持
      if (comCfg?.config?.fullHeight === true && ['interaction-container-preview', 'interaction-container-oss-preview'].includes(comCfg.comType)) {
        // 已知高度计算公式 height= rowHeight * h +( h - 1 ) * margin + border
        // 667 = 10 * 44.8 +( 44.8 - 1 ) * 5 + 0
        // 公式将 屏幕高度 转换为 vue-grid-layout 的 h(高度)，15是 10(rowHeight) + 5(margin) 得出
        return document.documentElement.clientHeight / this.ratioObj.w / 15 + 0.3333
      }

      return comCfg.attr.h
    },
    transToLayout (coms) { // 将组件集合转换成vue-grid数据
      const layout = coms.map((item) => {
        let com;
        if (item.type === 'com') {
          com = this.getComDataById(item.id)
        } else {
          com = this.getComDataById(item.comId)
        }
        const attr = com.attr
        const obj = {
          x: attr.x,
          y: attr.y,
          w: attr.w,
          h: this.getMobileHeight(com),
          i: com.id,
          id: com.id,
          type: com.type
        }
        return obj
      })
      layout.sort((a, b) => { return (a.y + a.h) - (b.y + b.h) }); // 根据底部高度排序
      return layout
    },
    domObserver () { // 移动端监听grid组件高度，面板类组件高度自适应
      this.$nextTick(() => {
        const comp = this.$refs.gridlayout;
        if (comp) {
          const ob = new ResizeObserver(_.throttle(enties => {
            enties.forEach(item => {
              const contentRect = item.contentRect;
              this.$emit('seatom_setComConfig', this.$parent.id, [{ key: 'attr.h', value: _.ceil((contentRect.height + 5) / 15, 2) }]);
            })
          }, 100))
          ob.observe(comp.$el);
          this.$once('hook:beforeDestroy', () => {
            ob.unobserve(comp.$el)
          })
        }
      })
    },
    initCallbackManager () {
      const { screenComs } = this
      const { params, query } = this.$route;
      const route = {
        ...params,
        ...query
      };
      const loginUser = {
        username: localStorage.getItem('username') || '',
        userid: localStorage.getItem('userId') || '',
        name: localStorage.getItem('name') || ''
      }
      const user = this.authObj || {};
      let callbackManager;
      if (this.isContainer || this.isReferPanel) { // 子面板或引用面板使用父级的回调管理器
        callbackManager = this.callbackFatherManager();
      } else {
        callbackManager = new CallbackManager();
        callbackManager.addCallbackKey('route', route);
        callbackManager.addCallbackKey('loginUser', loginUser);
        callbackManager.addCallbackKey('user', user);
      }
      _.values(screenComs).forEach(com => {
        if (com.interactionConfig && com.interactionConfig.callbackParams) {
          com.interactionConfig.callbackParams.forEach(cb => {
            callbackManager.addCallbackKey(cb.variableName)
          })
        }
      })
      // 初始化全局变量
      if (!callbackManager.state.globalData) {
        const variableList = this.screenInfo.variableList || [];
        const globalData = new GlobalDataManager(variableList, { state: callbackManager.state, route, loginUser })
        Object.defineProperty(callbackManager.state, 'globalData', {
          get () {
            return globalData.getGlobalData()
          }
        })
      }

      this.callbackManager = callbackManager;
    },
    initParentStyle () {
      if (_.isEmpty(this.screenConfig)) return
      let { scaleType } = this.screenConfig
      let style = {}
      if (this.platform === 'mobile') {
        scaleType = 'full_width'
      }
      switch (scaleType) {
        case 'full_screen':
        case 'full_width':
        case 'full_height':
        case 'no_scale':
          style = {
            overflowX: 'hidden',
            overflowY: 'visible'
          }
          break
        case 'full_height_scroll':
          style = {
            overflow: 'auto'
          }
          break
      }

      if (this.$el && this.$el.parentNode) {
        const parentNode = this.$el.parentNode
        Object.keys(style).forEach(attr => {
          parentNode.style[attr] = style[attr]
        })
      }
    },
    initParentSize () {
      if (!this.parent) {
        this.parentSize.width = document.documentElement.clientWidth
        this.parentSize.height = document.documentElement.clientHeight
      } else {
        const style = window.getComputedStyle(this.$el.parentNode, null)
        this.parentSize.width = parseInt(style.getPropertyValue('width'), 10)
        this.parentSize.height = parseInt(style.getPropertyValue('height'), 10)
      }
    },
    resize ({ width, height }) {
      if (typeof width !== 'undefined') {
        this.parentSize.width = Number.parseInt(width, 10)
      }
      if (typeof height !== 'undefined') {
        this.parentSize.height = Number.parseInt(height, 10)
      }
    },
    handleCompLoaded () {
      this.loadCount++
      this.$emit('progress', ((this.loadCount / this.compCount) * 100).toFixed(0))
      if (this.loadCount >= this.compCount) {
        this.$emit('loaded')
        this.loading = false
        this.handleSeatomEventsAndCallbacks(this.initEvents);
        if (process.env.IS_SSR_RENDER === 'true') {
          this.handleSsrComps()
        }
      }
    },
    handleSsrComps () {
      this.$nextTick(() => {
        if (!this.ssrShowCompIds.length) {
          this.timeout && clearTimeout(this.timeout)
          this.timeout = setTimeout(() => {
            clearTimeout(this.timeout)
            const ssrEl = document.getElementById('ssr-preload');
            ssrEl && ssrEl.remove();
          }, 160)
        }
      })
    },
    handleSeatomEventsAndCallbacks (e) {
      if (this.isContainer) {
        this.$emit('seatom_eventsAndCallbacks', e);
        return
      }
      if (this.loading && e.type !== 'callback') { // 大屏未加载完成时，存储事件和回调
        this.initEvents.unshift(e);
        return
      }
      // 执行事件和回调
      this.handleEventsAndCallbacks(e)
    },
    handleSetComConfig (id, keyValPairs = []) { // 移动端设置组件config
      const com = this.screenComs[id]
      if (com) {
        keyValPairs.forEach(pair => {
          _.set(com, pair.key, pair.value);
        })
      }
    },
    handleEventsAndCallbacks (data) { // 处理事件和回调
      if (!_.isArray(data)) {
        data = [data]
      }
      let e;
      // eslint-disable-next-line no-cond-assign
      while (e = data.pop()) {
        if (e.type === 'event') {
          const { taskQueue } = e;
          this.pushFnQueue(taskQueue);
          if (this.$isControl) {
            const message = {
              type: 'event',
              params: taskQueue
            }
            this.$socket && this.$socket.emit('chat', message)
          }
          if (this.childScreens.length || (this.screenInfo.screenType === 'child' && this.screenInfo.parentId)) { // 存在子屏或父屏，广播联动
            const msg = {
              currId: this.screenInfo.id,
              type: 'event',
              params: taskQueue
            }
            this.crossSocket && this.crossSocket.io.emit('linkage', {
              room: this.roomId,
              msg
            })
          }
        }
        if (e.type === 'callback') {
          const { params = {}, callbackParams = [] } = e
          const newParam = {};
          callbackParams.forEach(p => {
            if (_.has(params, p.fieldValue)) {
              newParam[p.variableName] = params[p.fieldValue]
            }
          })
          this.callbackManager.updateCallbackValue(newParam)
          if (this.$isControl) {
            const message = {
              type: 'callback',
              params: newParam
            }
            this.$socket && this.$socket.emit('chat', message)
          }
        }
      }
    },
    onEvent ({ type, params }) { // 监听子组件抛出的事件
      switch (type) {
        case 'switchScene':
          this.switchScene(params)
          break
        default:
          break
      }
    },
    switchScene (params) { // 切换场景
      const pageName = params.pageName || (params.pageData && params.pageData.pageName)
      if (pageName) {
        const page = this.pageList.find(item => item.pageName === pageName)
        if (page) {
          const scene = this.sceneConfig.find(item => {
            return item.pageList.findIndex(v => v.pageId === page.pageId) > -1
          })
          if (scene) {
            this.sceneId = scene.sceneId
          }
          if (page) {
            this.pageId = page.pageId
          }
          this.getLayout();
          return
        }
      }
      // 新版动态面板切换 用于编辑页切换状态预览使用
      const sceneId = params.sceneId;
      if (sceneId) {
        const scene = this.sceneConfig.find(item => item.sceneId === sceneId)
        if (scene) {
          this.sceneId = scene.sceneId
          if (scene.pageList && scene.pageList.length) {
            this.pageId = scene.pageList[0].pageId
            this.getLayout();
          }
        }
      }
    },
    // 切换第一个场景
    switchFirstScene () {
      const firstPage = this.pageList[0]
      if (!firstPage) {
        return
      }

      this.switchScene({
        pageName: firstPage.pageName
      })
    },
    afterLoad (e) {},
    async handlePermissionData () {
      const {
        permissionDataConfig: {
          dataResponse: {
            source,
            sourceType
          },
          mappingRelation: {
            id,
            permission
          },
          tips,
          userPermissionList
        }
      } = this.screenInfo
      const params = source[sourceType].data;
      const { baseUrl } = params
      if (!baseUrl) {
        // this.$message.warn('请选择数据源')
        // this.loading = false
        return
      }
      const res = await sendCustomReq(params)
      if (res && res.success) {
        const permissionData = res.data.data || []
        const permissionObj = {}
        permissionData.forEach(item => {
          permissionObj[item[id]] = !!item[permission]
        })
        userPermissionList.filter(item => {
          return !!item.componentsId.length && !!item.id
        }).forEach(item => {
          this.permissionMap.set(item.componentsId, { result: permissionObj[item.id], tips })
        })
      }
    },
    receivedMessage (msg) { // 终端控制接收消息 勿删！！
      if (msg.type === 'event') {
        this.pushFnQueue(msg.params);
      }
      if (msg.type === 'callback') {
        this.callbackManager && this.callbackManager.updateCallbackValue(msg.params)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../lib/vue-fullpage/vue-fullpage.css";
.screen-view-wrap_box {
  position: relative;
  width: 100%;
  height: 100%;
}
.screen-view-wrap {
  position: absolute;
  left: 0;
  top: 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transform-origin: left top;
  overflow: hidden;
  .screen-view {
    overflow: hidden;
  }
  ::v-deep {
    .vue-grid-item {
      border: none !important;
    }
  }
}
</style>
