<template>
  <div class="password-protect">
    <p class="info-txt">{{ tips }}</p>
    <div>
      <input v-model="pwd" type="password" class="pwd-input">
      <button type="button" class="pwd-btn" @click.stop="handleConfirm">确 定</button>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      pwd: ''
    };
  },
  props: {
    tips: {
      type: String,
      default: '已打开密码保护，请输入密码'
    }
  },
  methods: {
    handleConfirm () {
      this.$emit('confirm', this.pwd);
    }
  }
}
</script>

<style lang="scss" scoped>
.password-protect {
  width: 330px;
  margin: 100px auto 0;
  .info-txt {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 12px;
    color: #fff;
  }
  .pwd-input {
    width: 250px;
    box-sizing: border-box;
    margin: 0;
    padding: 5.6px 11px;
    margin-right: 16px;
    position: relative;
    display: inline-block;
    color: rgba(0,0,0,.65);
    font-size: 12px;
    line-height: 1.5715;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    transition: all .3s;
  }
  .pwd-btn {
    color: #fff;
    background: #2491f7;
    border-color: #2491f7;
    border: 1px solid transparent;
    border-radius: 2px;
    text-shadow: 0 -1px 0 rgba(0,0,0,.12);
    text-align: center;
    box-shadow: none;
    line-height: 1.5715;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    cursor: pointer;
    transition: all .3s cubic-bezier(.645,.045,.355,1);
    user-select: none;
    touch-action: manipulation;
    height: 32px;
    padding: 5.6px 15px;
    font-size: 12px;
    opacity: 0.9;
    &:hover {
      opacity: 1;
    }
  }
}
</style>
