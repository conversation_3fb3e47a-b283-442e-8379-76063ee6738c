<template>
  <div class="data-text-edit-wrapper">
    <el-dialog
      title="编辑"
      :visible.sync="dialogVisible"
      destroy-on-close
      @close="close"
      top="0"
      width="550px">
      <el-form ref="form" size="mini" :model="form" label-width="110px" label-suffix="：">
        <el-form-item label="文本内容">
          <el-input type="textarea" v-model="form.data" ref="textarea"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" class="el-button--light-blue" @click="confirm" :loading="loading">确定</el-button>
        <el-button size="medium" class="el-button--text" @click="cancel">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getData } from '@/api/datastorage'
export default {
  name: 'DataTextEdit',
  data () {
    return {
      dialogVisible: false,
      loading: false,
      comInfo: null,
      form: {
        data: ''
      }
    }
  },
  methods: {
    show (comInfo, comData) {
      this.dialogVisible = true;
      this.comInfo = comInfo;
      this.$nextTick(() => {
        this.$refs.textarea.focus();
        const { target } = comInfo.dataConfig.fieldMapping[0];
        const data = comData[0];
        this.form.data = data[target];
        // console.log(comInfo, comData)
      })
    },
    confirm () {
      const result = this.form.data;
      if (this.comInfo) {
        const { target } = this.comInfo.dataConfig.fieldMapping[0];
        this.loading = true;
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.comInfo.id,
          keyValPairs: [{
            key: 'staticData',
            value: [{ [target]: result }]
          }]
        }).then(async () => {
          const res = await getData({
            componentId: this.comInfo.id,
            type: 'static',
            workspaceId: this.$store.state.editor.screenInfo.workspaceId
          })
          this.$store.commit('editor/updateComData', { componentId: this.comInfo.id, data: res.data })
          this.loading = false;
          this.dialogVisible = false;
        }).catch(e => {
          console.warn(e);
          this.loading = false;
          this.dialogVisible = false;
        })
      }
    },
    cancel () {
      this.dialogVisible = false;
    },
    close () {
      document.body.removeChild(this.$el);
    }
  }
}
</script>
<style lang="scss">
  .data-text-edit-wrapper {
    .el-form-item__label {
      color: #fafafa;
    }
    .el-textarea__inner {
      color: var(--control-text-color);
    }
  }
</style>
