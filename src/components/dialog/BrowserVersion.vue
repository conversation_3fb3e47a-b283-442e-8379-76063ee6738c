<template>
  <div v-if="browserVisible">
    <el-dialog
      title="提示"
      :visible="browserVisible"
      width="400px"
      top="0"
      class="browser-dialog"
      :close-on-click-modal="false"
      :before-close="close">
      <div class="browser-title">检测到您的浏览器版本过低，建议升级</div>
      <div>
        <!-- <div class="browser-item" v-for="item in browserData" :key="item.browserName">
          <span class="item text">{{item.browserName}}</span>
          <template v-for="browser in item.list" >
            <a :key="browser.resourceUrl"
               class="item link"
               :href="browser.resourceUrl">
                {{browser.description}}
            </a>
          </template>
          <span class="item link">64位下载</span>
        </div> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="light-blue" size="medium" @click="noUpgrade">暂不升级</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getResourceList } from '@/api/workspace';
export default {
  data () {
    return {
      show: false,
      browserData: {}
    }
  },

  created () {
    // this.show = this.browserVisible
    // // console.log(this.$store);
    // this.getResourceList();
  },

  computed: {
    ...mapState({
      browserVisible: state => state.browserVisible
    })
  },

  methods: {
    async getResourceList () {
      const resourceList = await getResourceList({ resourceType: 'app' })
      if (resourceList && resourceList.success) {
        this.browserData = resourceList.data[0].folderList.map(item => {
          return {
            browserName: item.resourceFolder,
            list: item.resourceList.map(item => {
              return {
                description: item.description + '位浏览器',
                resourceUrl: process.env.VUE_APP_SERVER_URL + item.resourceUrl
              }
            })
          }
        })
      }
    },
    noUpgrade () {
      window.sessionStorage.setItem('browser_visible', '1');
      this.close();
    },
    close () {
      this.$store.commit('changeBrowserVisible', false)
    }
  }

}
</script>

<style  lang="scss" scoped>
::v-deep .el-dialog .el-dialog__body {
  padding:20px 32px 0px;
  .browser-title {
    // width: 100%;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.5);
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 20px;
  }

  .browser-item {
    width: 100%;
    padding: 8px 16px;
    display: flex;
    background: rgba(66, 74, 90, 0.3);
    border: 1px dashed rgba(67, 75, 91, 0.3);
    box-sizing: border-box;
    border-radius: 4px;
    margin-bottom: 8px;

    .text {
      color: #FFFFFF;
      font-family: PingFang SC;
      font-style: normal;
      font-weight: normal;
      font-size: 12px;
    }

    .link {
      font-family: PingFang SC;
      font-style: normal;
      font-weight: normal;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.5);
      cursor: pointer;
      text-decoration: none;
    }

    .item {
      margin-right: 16px;
    }
  }
}
</style>
