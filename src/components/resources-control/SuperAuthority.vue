<template>
  <div class="super-authority-root">
    <el-input
    ref="superInput"
    v-show="superAuthority.isShow"
    v-model="superAuthority.inputKeyInfo"
    @change="focusCheck()"></el-input>
    <div class="blank-other" @click="clickKeep()"></div>
  </div>
</template>

<script>
export default {
  name: 'SuperAuthority',
  data () {
    return {
      timer: null,
      // 超级权限信息
      superAuthority: {
        backupKeyInfo: 'fuxi', // 大屏名称已从 ’仓颉‘ 改为 ’伏羲‘
        inputKeyInfo: '',
        isShow: false,
        clickIdx: 0
      }
    }
  },
  created () {
    // 开启超级权限的密码，备份至localStorage，担心发布某个版本后改密码，会忘记
    localStorage.setItem('backupKeyInfo', this.superAuthority.backupKeyInfo);
  },
  methods: {
    // 连续单击 3 次开启超级入口
    clickKeep () {
      this.stopRun();
      this.timer = setTimeout(() => {
        this.superAuthority.clickIdx = 0;
        this.superAuthority.isShow = false;
        this.superAuthority.inputKeyInfo = '';
      }, 5000);
      this.superAuthority.clickIdx++;
      this.superAuthority.isShow = this.superAuthority.clickIdx > 2;
      if (this.superAuthority.isShow) {
        this.$message.success('请输入密码');
      }
      setTimeout(() => {
        this.$refs.superInput.focus();
      }, 100);
    },
    // 判断输入是否正确
    focusCheck () {
      if (this.superAuthority.backupKeyInfo === this.superAuthority.inputKeyInfo) {
        this.$message.success('已开启超级权限');
        this.$emit('checkAdmin');
        this.stopRun();
      }
      this.superAuthority.clickIdx = 0;
      this.superAuthority.isShow = false;
    },
    stopRun () {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.super-authority-root {
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  .blank-other {
    flex: 1;
  }
  ::v-deep .el-input__inner {
    padding: 0;
    border: 0;
    width: 0;
    height: 32px;
  }
}
</style>
