<template>
  <div class="upload-file-root">
  <!-- 弹窗 上传文件 -->
  <el-dialog
    title="文件上传"
    :visible.sync="uploadDialogVisible"
    width="50%"
    top="0"
    class="upload-file-dialog-body"
    :before-close="handleClose"
    :append-to-body="true"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    :close-on-click-modal="false">
    <el-upload
      class="upload-demo"
      :class="[$route.name.includes('edit') ? '' : 'upload-theme']"
      drag
      ref="uploadFileRef"
      :action="fontUploadUrl"
      :on-change="addFile"
      :on-remove="removeFile"
      :on-success="uploadSuccess"
      :limit='200'
      :auto-upload = 'false'
      :http-request="uploadSectionFile"
      :file-list="uploadFileList"
      :on-exceed="maxLimit"
      multiple>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>
      <div>{{filterFileInfo}}，单次最多可上传200个文件</div>
      </div>
    </el-upload>
    <div class="dialog-content" v-if="isShowSelectFlolder">
      <div class="content-list">
        <div class="content-name">选择文件夹</div>
        <div class="content-input">
          <el-cascader
          class="input-theme"
          popper-class="poper-theme"
          v-model="setSelectFolderInfo"
          :options="setSelectFolderOptions"
          size="mini"
          style="flex: 1;"
          >
          </el-cascader>
          <div class="add-folder" @click="addFolder()"><i class="el-icon-plus"></i> 新建文件夹</div>
        </div>
      </div>
      <div class="content-list mt-8" v-if="resourceType === 'icon'">
        <div class="content-name">icon来源</div>
        <div class="content-input">
          <el-select
            v-model="iconSvgType"
            size="mini"
            class="input-theme"
            popper-class="poper-theme"
            >
            <el-option
              v-for="item in iconSource"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="handleClose()">取 消</el-button>
      <el-button size="mini" type="primary" @click="submitUpload()">确 定</el-button>
    </span>
    <seatom-loading v-if="loading"></seatom-loading>
  </el-dialog>
  <!-- 弹窗 新建文件夹 -->
  <el-dialog
    title="新建文件夹"
    :visible.sync="dialogVisibleAddFolder"
    width="30%"
    top="0"
    class="upload-file-add-folder"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    :append-to-body="true"
    :close-on-click-modal="false">
    <div class="dialog-content dialog-content-add-folder">
      <div class="content-list" style="margin-bottom: 8px;">
        <div class="content-name">所属文件夹</div>
        <div class="content-input">
          <el-select
            v-model="newFolderInfo.folder"
            filterable
            size="mini"
            class="input-theme"
            popper-class="poper-theme"
            >
            <el-option
              v-for="(item, index) in setSelectFolderOptions"
              :key="item.value + index"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
              >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="content-list">
        <div class="content-name">文件夹名称</div>
        <div class="content-input">
          <el-input
            class="input-theme"
            v-model="newFolderInfo.name"
            size="mini"
            >
          </el-input>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="dialogVisibleAddFolder = false;">取 消</el-button>
      <el-button size="mini" type="primary" @click="submitNewFolder()">确 定</el-button>
    </span>
  <seatom-loading v-if="loadingNewFolder"></seatom-loading>
  </el-dialog>
  </div>
</template>
<script>
import { uploadResource, ResourceCreateFolder } from '@/api/workspace';
export default {
  name: 'UploadFile',
  props: {
    isAdmin: Boolean,
    isUploadDialog: Boolean,
    // 上传资源类型 enum: 'video', 'picture', 'tplVideo', 'tplPicture', 'themeVideo', 'themePicture', 'icon',
    resourceType: String,
    selectFolderOptions: {
      type: Array,
      default: function () {
        return [];
      }
    },
    selectFolderInfo: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  watch: {
    isUploadDialog: {
      handler: function (val) {
        this.uploadDialogVisible = val;
        if (this.isShowSelectFlolder) {
          // 重置选择项
          this.setSelectFolderInfo = this.resourceType === 'docfile' ? ['personal', '默认文件夹'] : ['private', '默认文件夹'];
        }
        // 重置选择文件
        this.uploadFileList = [];
        this.uploadFileCountAll = 0;
        this.uploadedFileCount = 0;
        this.getFilterFileInfo();
      },
      immediate: true
    },
    selectFolderOptions: {
      handler: function (val) {
        this.setSelectFolderOptions = val
          .filter(item => {
            return !item.disabled
          });
        // this.isShowSelectFlolder = ['tplVideo', 'tplPicture', 'themeVideo', 'themePicture'].indexOf(this.resourceType) === -1;
        if (!this.isShowSelectFlolder) {
          this.setSelectFolderInfo = [];
          this.setSelectFolderInfo.push(val[0].value);
          this.setSelectFolderInfo.push(val[0].children[0].value);
        }
      },
      immediate: true,
      deep: true
    }
  },
  data () {
    return {
      loading: false,
      loadingNewFolder: false,
      userId: '',
      uploadDialogVisible: false,
      dialogVisibleAddFolder: false,
      uploadFileList: [],
      // 需要上传文件的总数
      uploadFileCountAll: 0,
      // 上穿完成的文件数目
      uploadedFileCount: 0,
      newFolderInfo: {
        folder: '',
        name: ''
      },
      fontUploadUrl: 'xxx', // 此处自定义上传，无需配置 process.env.VUE_APP_SERVER_URL + '/api/resource/upload',
      setSelectFolderOptions: [],
      setSelectFolderInfo: ['private', '默认文件夹'],
      // 模板库 与 主题库 上传时不允许选择文件夹
      isShowSelectFlolder: true,
      filterFileInfo: '支持 JPG PNG 文件',
      iconSvgType: 'common',
      iconSource: [{
        label: '单个SVG',
        value: 'common'
      }, {
        label: 'iconFont合图',
        value: 'merge'
      }]
    }
  },
  created () {
    this.userId = localStorage.getItem('userId');
    this.isShowSelectFlolder = ['tplVideo', 'tplPicture', 'themeVideo', 'themePicture'].indexOf(this.resourceType) === -1;
  },
  methods: {
    // 添加文件前检查
    addFile (file, fileList) {
      const temp = fileList.filter((item) => item.name === file.name).length;
      if (temp > 1) {
        this.$message.warn('文件添加重复');
        fileList.pop();
      }
      const isLt100M = file.size / 1024 / 1024 < 500;
      if (!isLt100M) {
        this.$message.warn('上传文件大小不能超过 500MB!');
        fileList.pop();
      }
      this.uploadFileCountAll = fileList.length;
      // console.log('确认文件类型', file, file.raw.type);
      switch (true) {
        // 图片
        case this.resourceType.includes('icture'): {
          const isJPG = (file.raw.type === 'image/jpeg') || (file.raw.type === 'image/png') || (file.raw.type === 'image/gif') || (file.raw.type === 'image/svg+xml');
          if (!isJPG) {
            this.$message.error('上传图片只能是 JPG / PNG / GIF / SVG 格式!');
            fileList.pop();
            this.uploadFileCountAll = fileList.length;
            return false;
          }
          break;
        }
        // 视频
        case this.resourceType.includes('ideo'): {
          const isMp4 = (file.raw.type === 'video/mp4');
          if (!isMp4) {
            this.$message.error('上传视频只能是 MP4 格式!');
            fileList.pop();
            this.uploadFileCountAll = fileList.length;
            return false;
          }
          break;
        }
        // 文件类型
        case this.resourceType.includes('ocfile'): {
          const isPdf = (file.raw.type === 'application/pdf' || file.raw.type === 'text/javascript' || file.raw.type === 'text/html' || file.raw.type === 'text/css' || file.raw.type === 'application/json');
          if (!isPdf) {
            this.$message.error('上传文件仅支持 pdf、js、html、css、json 格式!');
            fileList.pop();
            this.uploadFileCountAll = fileList.length;
            return false;
          }
          break;
        }
        // 图标类型
        case this.resourceType.includes('con'): {
          const isSVG = file.raw.type === 'image/svg+xml';
          if (!isSVG) {
            this.$message.error('上传图标只能是 SVG 格式!');
            fileList.pop();
            this.uploadFileCountAll = fileList.length;
            return false
          }
          break;
        }
        default:
          break;
      }
    },

    // 移除文件时更新
    removeFile (file, fileList) {
      // console.log('当前文件列表 removeFile', fileList);
      this.uploadFileCountAll = fileList.length;
    },
    // 获取 限制上传文案
    getFilterFileInfo () {
      switch (true) {
        // 图片
        case this.resourceType.includes('icture'): {
          this.filterFileInfo = '支持 JPG PNG GIF SVG 文件';
          break;
        }
        // 视频
        case this.resourceType.includes('ideo'): {
          this.filterFileInfo = '支持 MP4 文件';
          break;
        }
        // 文件类型
        case this.resourceType.includes('ocfile'): {
          this.filterFileInfo = '支持 pdf、js、html、css、json 文件';
          break;
        }
        // 图标类型
        case this.resourceType.includes('con'): {
          this.filterFileInfo = '支持 SVG 文件';
          break;
        }
        default:
          break;
      }
    },

    // 上传完成 -- 自定义上传 弃用
    uploadSuccess (res, file, fileList) {
      // this.$refs.uploadFileRef.clearFiles();
      // this.loading = false;
      // console.log('上传完成后执行', res);
      // if (res.success) {
      //   this.$emit('closeUploadDialog');
      // } else {
      //   this.$message.error(res.message);
      // }
    },
    // 自定义formdata 上传
    uploadSectionFile (content) {
      const uploadData = new FormData();
      uploadData.append('file', content.file);
      uploadData.append('userId', this.userId);
      uploadData.append('resourceType', this.resourceType);
      uploadData.append('resourceLibrary', this.getResourceLibrary(this.setSelectFolderInfo[0]));
      uploadData.append('resourceLibraryCode', this.setSelectFolderInfo[0]);
      uploadData.append('resourceFolder', this.setSelectFolderInfo[1]);
      if (this.resourceType === 'icon') {
        uploadData.append('svgType', this.iconSvgType)
      }
      uploadResource(uploadData).then(res => {
        this.uploadedFileCount++;
        // if (res.success) {
        //   this.$emit('refreshResourceList');
        //   // 在刷新的 emit 中有关闭弹窗的功能。
        //   this.$emit('closeUploadDialog');
        //   console.log('上传成功', res);
        // } else {
        //   console.log('上传失败', res);
        // }
        if (this.uploadedFileCount >= this.uploadFileCountAll) {
          this.loading = false;
          this.$emit('refreshResourceList', this.resourceType, this.setSelectFolderInfo);
          // 在刷新的 emit 中有关闭弹窗的功能。
          this.$emit('closeUploadDialog');
        }
      });
    },
    // 此处后端需要 库文件夹名字，无法直接获取
    getResourceLibrary (strCode) {
      let tempName = '';
      this.setSelectFolderOptions.forEach(item => {
        if (item.value === strCode) {
          tempName = item.label;
        }
      });
      return tempName;
    },
    // 确定上传
    submitUpload () {
      if (this.setSelectFolderInfo.length === 0 || this.setSelectFolderInfo[0] === '') {
        this.$message({
          text: '请选择文件夹',
          type: 'warn'
        });
        return;
      }
      if (this.uploadFileCountAll === 0) {
        this.loading = false;
        this.$message({
          text: '请选择文件',
          type: 'warn'
        });
        return;
      }
      this.uploadedFileCount = 0;
      this.loading = true;
      this.$refs.uploadFileRef.submit();
      // this.$emit('closeUploadDialog');
    },
    maxLimit () {
      this.$message.warn('请最多上传200个文件');
    },
    // 打开新建文件夹弹窗
    addFolder () {
      this.newFolderInfo.folder = this.resourceType === 'docfile' ? 'personal' : 'private';
      this.newFolderInfo.name = '';
      this.dialogVisibleAddFolder = true;
    },
    // 提交新建文件夹
    submitNewFolder () {
      // 判断文件夹名称重复去重
      const systemSet = new Set();
      const personalSet = new Set();
      const privateSet = new Set();
      const systemChildren = this.setSelectFolderOptions.filter(item => item.value === 'system')[0]?.children;
      const personalChildren = this.setSelectFolderOptions.filter(item => item.value === 'personal')[0]?.children;
      const privateChildren = this.setSelectFolderOptions.filter(item => item.value === 'private')[0]?.children
      systemChildren && systemChildren.forEach((item) => {
        systemSet.add(item.value);
      });
      personalChildren && personalChildren.forEach((item) => {
        personalSet.add(item.value);
      });
      privateChildren && privateChildren.forEach((item) => {
        privateSet.add(item.value);
      });
      const systemSetLength = systemSet.size;
      const personalSetLength = personalSet.size;
      const privateSetLength = privateSet.size;
      if (this.newFolderInfo.folder === 'system') {
        systemSet.add(this.newFolderInfo.name);
        if (systemSetLength !== systemSet.size) {
        } else {
          this.$message({
            text: '文件夹名称已存在',
            type: 'warn'
          });
          return;
        };
      } else if (this.newFolderInfo.folder === 'personal') {
        personalSet.add(this.newFolderInfo.name);
        if (personalSetLength !== personalSet.size) {
        } else {
          this.$message({
            text: '文件夹名称已存在',
            type: 'warn'
          });
          return;
        }
      } else if (this.newFolderInfo.folder === 'private') {
        privateSet.add(this.newFolderInfo.name)
        if (privateSetLength !== privateSet.size) {
        } else {
          this.$message({
            text: '文件夹名称已存在',
            type: 'warn'
          });
          return;
        }
      }
      if (this.newFolderInfo.folder === '') {
        this.$message({
          text: '所属文件夹不能为空',
          type: 'warn'
        });
        return;
      }
      if (this.newFolderInfo.name === '') {
        this.$message({
          text: '文件夹名称不能为空',
          type: 'warn'
        });
        return;
      }
      this.loadingNewFolder = true;
      ResourceCreateFolder({
        userId: this.userId,
        resourceType: this.resourceType,
        resourceLibrary: this.getResourceLibrary(this.newFolderInfo.folder),
        resourceLibraryCode: this.newFolderInfo.folder,
        resourceFolder: this.newFolderInfo.name
      }).then(res => {
        this.loadingNewFolder = false;
        if (res.success) {
          this.setSelectFolderOptions.forEach(item => {
            if (item.value === this.newFolderInfo.folder) {
              item.children.push({
                value: this.newFolderInfo.name,
                label: this.newFolderInfo.name
              });
            }
          });
          this.setSelectFolderInfo[0] = this.newFolderInfo.folder;
          this.setSelectFolderInfo[1] = this.newFolderInfo.name;
          this.dialogVisibleAddFolder = false;
          this.$emit('refreshResourceList', this.resourceType);
        }
      });
    },
    // 关闭上传对话框
    handleClose (done) {
      this.$confirm('确认关闭？', '', {
        type: 'warning',
        closeOnClickModal: false,
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme'
      }).then(_ => {
        this.$emit('closeUploadDialog');
        done();
      }).catch(_ => {});
    }
  }

}
</script>
<style lang="scss">
.upload-file-dialog-body {
/* position: relative; */
.upload-demo {
  width: 80%;
  margin: auto;
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      background-color: unset;
      width: 100%;
      border: 1px dashed #c4c3c357;
      border-radius: 4px;
    }
  }
  .el-upload-list {
    margin-bottom: 8px;
  }
  .el-upload-list__item:hover {
    background-color: #419eff14;
  }
  .el-upload-list--text {
    max-height: 200px;
    overflow: hidden;
    overflow-y: auto;
  }
}
.dialog-content {
  padding: 4px 64px;
    .content-list {
      display: flex;
      // margin-bottom: 8px;
      .content-name {
        width: 88px;
        line-height: 28px;
        color: var(--seatom-type-900);
      }
      .content-input {
        flex: 1;
        display: flex;
        align-items: center;
        .el-select {
          flex: 1;
        }
        .add-folder {
          margin-left: 6px;
          cursor: pointer;
          color: var(--seatom-type-900);
          // &:hover {
          //   color: #fff;
          // }
        }
      }
      .el-radio {
        margin-right: 10px;
      }
      .el-cascader-node.is-disabled {
        color: rgba(117, 116, 116, 0.8);
      }
    }
  }
  .el-button {
    padding: 6px 12px;
  }
}
.upload-file-add-folder {
  .content-list {
    display: flex;
    // margin-bottom: 8px;
    .content-name {
      width: 88px;
      line-height: 28px;
    }
    .content-input {
      color: var(--seatom-type-900);
      flex: 1;
      display: flex;
      align-items: center;
      .el-select {
        flex: 1;
      }
      .add-folder {
        margin-left: 6px;
        cursor: pointer;
        &:hover {
          color: #fff;
        }
      }
    }
    .el-radio {
      margin-right: 10px;
    }
    .el-cascader-node.is-disabled {
      color: rgba(117, 116, 116, 0.8);
    }
  }
  .dialog-content-add-folder {
    padding: 4px 24px;
    color: #FFFFFFB3;
  }
  .el-button {
    padding: 6px 12px;
  }
}
.mt-8 {
  margin-top: 8px;
}

</style>
