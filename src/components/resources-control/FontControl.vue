<template>
  <div class="font-resources">
    <!-- <div class="title-add">
      <el-button type="primary" class="datav-btn-md datav-btn add-data" @click="uploadFont()">
        <div class="add-text">+ 上传字体</div>
      </el-button>
      <div class="data-border"></div>
    </div> -->
    <div class="title-add">
      <el-button type="primary" style="padding: 12px 15px;" @click="uploadFont()">
        <div class="add-text"><hz-icon name="upload" style="margin-right: 8px;"></hz-icon>上传</div>
      </el-button>
    </div>
    <div class="font-table">
      <table class="hz-table">
        <thead>
          <tr>
            <th>序号</th>
            <th>字体名称</th>
            <th>描述</th>
            <th>上传时间</th>
            <th>大小(MB)</th>
            <th style="text-align: center;">状态</th>
            <th style="text-align: center;">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in fontList" :key="index">
            <td style="width: 80px;">{{ index + 1 }}</td>
            <td>
              {{ item.fontName }}
            </td>
            <td>{{ item.description || '--'}}</td>
            <td style="width: 180px;">{{ item.createdAt.slice(0, 10) || '--'}}</td>
            <td style="width: 120px;">{{ item.size || '--'}}{{item.size ? ' MB' : ''}}</td>
            <td style="width: 110px;">
              <div class="tb-config">
                <div class="tb-config-btn pointer" @click="fontEnable(item)">
                  <div class="btn">
                    <!-- <i :class="[item.enable ? 'el-icon-open': 'el-icon-turn-off']"></i><span>{{item.enable ? '显示中' : '未显示'}}</span> -->
                    <el-switch v-model="item.enable" /><span>{{item.enable ? '显示中' : '未显示'}}</span>
                  </div>
                </div>
              </div>
            </td>
            <td style="width: 256px;">
              <div class="tb-config">
                <div class="tb-config-btn pointer" @click="fontView(item)">
                  <div class="btn">
                    <i style="color: rgba(86, 98, 118, 0.64);" class="icon-visible"></i><span>查看</span>
                  </div>
                </div>
                <div class="tb-config-btn pointer" v-if="!item.systemFont" @click="fontEdit(item)">
                  <div class="btn">
                    <i style="color: rgba(86, 98, 118, 0.64);" class="el-icon-edit"></i><span>编辑</span>
                  </div>
                </div>
                <div class="tb-config-btn pointer" v-if="!item.systemFont" @click="fontDel(item)">
                  <div class="btn">
                    <i style="color: rgba(86, 98, 118, 0.64);" class="el-icon-delete"></i><span>删除</span>
                  </div>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <div class="nodata" v-if="fontList.length ==0">
          <p>暂无数据</p>
      </div>
    </div>
  <!-- 弹窗 上传字体 -->
  <el-dialog
    title="字体上传"
    :visible.sync="dialogVisible"
    width="50%"
    top="0"
    :before-close="handleClose"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    :close-on-click-modal="false">
    <el-upload
      class="upload-demo upload-theme"
      drag
      ref="uploadFont"
      :action="fontUploadUrl"
      :headers="uploadHeaders"
      :on-change="addFile"
      :on-success="uploadSuccess"
      :limit='1'
      :auto-upload = 'false'
      :data="{userId: userInfo.userId, fontName:uploadFontInfo.name, description:uploadFontInfo.descr}"
      multiple>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>
      <div>只能上传ttf文件</div>
      </div>
    </el-upload>
    <div class="dialog-content">
      <div class="content-list">
        <div class="content-name">字体名称</div>
        <div class="content-input">
          <el-input
            class="input-theme"
            v-model="uploadFontInfo.name"
            size="mini"
            >
          </el-input>
        </div>
      </div>
      <div class="content-list">
        <div class="content-name">字体描述</div>
        <div class="content-input">
          <el-input
            type="textarea"
            :rows="2"
            class="input-theme"
            placeholder="请输入字体描述"
            v-model="uploadFontInfo.descr"
            resize="none"
            >
          </el-input>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="handleClose()">取 消</el-button>
      <el-button size="mini" type="primary" @click="submitUpload()">确 定</el-button>
    </span>
  </el-dialog>
  <!-- 弹窗 字体编辑 -->
  <el-dialog
    title="编辑"
    :visible.sync="dialogVisibleEdit"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    width="40%"
    top="0"
    :before-close="handleCloseEdit"
    :close-on-click-modal="false">
    <div class="dialog-content">
      <div class="content-list">
        <div class="content-name">字体名称</div>
        <div class="content-input">
          <el-input
            class="input-theme"
            v-model="selectFont.fontName"
            size="mini"
            >
          </el-input>
        </div>
      </div>
      <div class="content-list">
        <div class="content-name">字体描述</div>
        <div class="content-input">
          <el-input
            type="textarea"
            :rows="4"
            class="input-theme"
            placeholder="请输入字体描述"
            v-model="selectFont.description"
            resize="none"
            >
          </el-input>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="handleCloseEdit()">取 消</el-button>
      <el-button size="mini" type="primary" @click="fontEditSubmint()">确 定</el-button>
    </span>
  </el-dialog>
  <!-- 弹窗 字体编辑 -->
  <el-dialog
    title="查看字体"
    :visible.sync="dialogVisibleFontView"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    width="40%"
    top="0"
    :close-on-click-modal="false">
    <div class="dialog-content"
    :style="dialogVisibleFontViewStyle">
      <div v-for="it in dialogVisibleFontViewData.setList"
      :key="it.fontSize"
      :style="{
        'font-size': it.fontSize,
        'font-weight': it.fontWeight,
        margin: '4px 0'
      }">
        {{dialogVisibleFontViewData.item.fontName}}
      </div>
    </div>
  </el-dialog>
  <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import { getFontList, deleteFont, updateFont } from '@/api/workspace';
import { replaceUrl, getHttpHeaders } from '@/utils/base';

export default {
  name: 'FontControl',
  data () {
    return {
      loading: false,
      dialogVisible: false,
      dialogVisibleEdit: false,
      dialogVisibleFontView: false,
      dialogVisibleFontViewData: {
        item: {
          fontName: '',
          fontUrl: ''
        },
        setList: [{ fontSize: '12px', fontWeight: '300' },
          { fontSize: '18px', fontWeight: '400' },
          { fontSize: '20px', fontWeight: '500' },
          { fontSize: '28px', fontWeight: '600' },
          { fontSize: '36px', fontWeight: '800' }]
      },
      dialogVisibleFontViewStyle: {
        '--font-name': '-1',
        '--font-url': ''
      },
      fontUploadUrl: replaceUrl(process.env.VUE_APP_SERVER_URL + '/api/font/upload'),
      uploadHeaders: getHttpHeaders(),
      userInfo: {
        token: '',
        userId: 'd0438698030d3e206cf55f8e1005e996'
      },
      uploadFontInfo: {
        name: '',
        descr: ''
      },
      selectFont: {
        fontName: '平方',
        description: '系统字体',
        createdAt: '2021-1-27',
        fontUrl: ''
      },
      fontList: [{
        fontName: '平方',
        description: '系统字体',
        createdAt: '2021-1-27',
        fontUrl: '',
        enable: true, // 是否在组件中展示
        systemFont: false // 是否为系统字体
      }]
    };
  },
  created () {
    this.getUserId();
    this.getFontListFun();
  },
  methods: {
    // 当前userid 存在localStorage 后续需要修改
    getUserId () {
      this.userInfo.userId = localStorage.getItem('userId');
    },
    async getFontListFun () {
      this.loading = true;
      const res = await getFontList({ userId: this.userInfo.userId });
      this.loading = false;
      if (res.success) {
        this.fontList = res.data;
        this.setFontface(res.data);
      }
    },
    // 字体预览
    fontView (item) {
      this.dialogVisibleFontViewData.item = item;
      this.dialogVisibleFontViewStyle = {
        'font-family': this.dialogVisibleFontViewData.item.fontName
      }
      this.dialogVisibleFontView = true;
    },
    // 字体编辑
    fontEdit (item) {
      this.dialogVisibleEdit = true;
      this.selectFont = _.cloneDeep(item);
    },
    fontEditSubmint () {
      updateFont({ fontName: this.selectFont.fontName, description: this.selectFont.description }, { userId: this.userInfo.userId, id: this.selectFont.id }).then(res => {
        if (res.success) {
          this.getFontListFun();
          this.dialogVisibleEdit = false;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 是否在控件下拉列表中展示
    fontEnable (item) {
      item.enable = !item.enable;
      updateFont({ enable: item.enable }, { userId: this.userInfo.userId, id: item.id }).then(res => {
        if (res.success) {
          this.getFontListFun();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 字体删除
    fontDel (item) {
      this.$confirm('确认删除当前字体吗？', '', {
        type: 'warning',
        closeOnClickModal: false,
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme'
      }).then(_ => {
        deleteFont({ userId: this.userInfo.userId, id: item.id }).then(res => {
          if (res.success) {
            this.getFontListFun();
          }
        });
      }).catch(_ => {});
    },
    submitUpload () {
      this.$refs.uploadFont.submit();
    },
    // 上传完成
    uploadSuccess (res, file, fileList) {
      this.$refs.uploadFont.clearFiles();
      if (res.success) {
        this.dialogVisible = false;
        this.fontList.push(res.data);
      } else {
        this.$message.error(res.message);
      }
    },
    addFile (file, fileList) {
      const fileType = file.name.split('.');
      if (fileType.length > 1) {
        const fileSuffix = fileType[fileType.length - 1].toLowerCase();
        if (fileSuffix !== 'ttf' && fileSuffix !== 'otf') {
          this.$message.error('您选择的[ ' + file.name + ' ]格式不正确。');
        }
        this.uploadFontInfo.name = file.name.substring(0, file.name.lastIndexOf('.'));
      } else {
        this.$message.error('您选择的[ ' + file.name + ' ]格式不正确。');
      }
    },
    uploadFont () {
      this.dialogVisible = true;
    },
    handleClose (done) {
      this.$confirm('确认关闭？', '', {
        type: 'warning',
        closeOnClickModal: false,
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme'
      }).then(_ => {
        this.dialogVisible = false;
        done();
      }).catch(_ => {});
    },
    // 编辑窗口关闭
    handleCloseEdit (done) {
      this.$confirm('确认关闭？', '', {
        type: 'warning',
        closeOnClickModal: false,
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme'
      }).then(_ => {
        this.dialogVisibleEdit = false;
        done();
      }).catch(_ => {});
    },
    setFontface (fontlist) {
      let rule = '';
      fontlist.forEach(item => {
        rule += '@font-face {font-family:"' + item.fontName + '";src:url("' + replaceUrl(process.env.VUE_APP_SERVER_URL + item.fontUrl) + '");}'
      });
      const sty = document.createElement('style');
      sty.type = 'text/css';
      sty.innerHTML = rule;
      document.getElementsByTagName('head')[0].appendChild(sty);
    }
  }
}
</script>

<style lang="scss" scoped>
.font-resources {
  position: relative;
  .title-add {
    height: 64px;
    display: flex;
    margin-left: 32px;
    width: calc(100% - 64px);
    padding: 16px 0;
    border-bottom: 1px solid var(--seatom-mono-a300);
    .datav-btn {
      transform: skewX(-40deg);
      border-radius: 0;
      background: #2681ff;
      &.datav-btn-md {
        height: 32px;
        line-height: 32px;
        padding: 0 30px;
      }
      .add-text {
        display: block;
        transform: skewX(40deg);
      }
    }
    .add-text {
      top: -4px;
      position: relative;
    }
    .data-border {
      border: 1px solid;
      border-color: #3a4659 transparent transparent #3a4659;
      flex: 1;
      transform: skewX(-40deg);
      margin-left: 20px;
    }
  }
  .font-table {
    position: relative;
    height: 100%;
    overflow: auto;
  }
  .tb-config {
    display: flex;
    ::v-deep .el-button {
      border: unset;
    }
    .tb-config-btn {
      height: 32px;
      line-height: 32px;
      display: flex;
      align-items: center;
      padding: 0 8px;
      position: relative;
      .btn {
        display: flex;
        line-height: 32px;
        align-items: center;
        padding: 0 8px;
      }
      span {
        line-height: 28px;
        margin-left: 4px;
      }
      &::after {
        content: "";
        display: block;
        background: #9fa0a126;
        border-radius: 8px;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
      }
      &:hover {
        &::after{
          opacity: 1;
          transform: scale(0.8);
          transition: all 0.4s cubic-bezier(0.2, 1.2, 0.5, 0.9);
        }
      }
    }
  }
}
::v-deep .upload-demo {
  width: 80%;
  margin: auto;
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      background-color: unset;
      width: 100%;
      border: 1px dashed #c4c3c357;
      border-radius: 4px;
    }
  }
  .el-upload-list {
    margin-bottom: 8px;
  }
}
::v-deep .dialog-content {
  padding: 4px 24px;
    .content-list {
      display: flex;
      margin-bottom: 8px;
      .content-name {
        width: 68px;
        line-height: 28px;
        color: var(--seatom-type-700);
      }
      .content-input {
        flex: 1;
      }
    }
  }
table.hz-table {
  width: 100%;
  padding: 0 32px;
  border-spacing: 0;
}
table.hz-table th,  table.hz-table td {
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-right: none;
  border-left: none;
  box-shadow: 0 -1px 0 0 var(--seatom-mono-a300) inset;
  text-align: left;
  line-height: 40px;
  padding: 0 8px;
}
table.hz-table th {
  color: var(--seatom-type-700);
  font-size: 14px;
  font-weight: 600;
}
table.hz-table tbody tr {
  color: var(--seatom-type-900);
  font-weight: 400;
  font-size: 14px;
}
table.hz-table tbody tr:hover {
  background-color: rgba(204, 219, 255, 0.06);
}
.hz-table {
  table {
    //position: relative;
    th {
      position: sticky;
      top: 0;
      z-index: 1;
      // background: #fff;
      color: rgba(21, 22, 24, 0.36);
    }
  }
}
.icon-visible {
  display: block;
  width: 16px;
  height: 16px;
  background: url("../../assets/img/svg/visible.svg") bottom center no-repeat;
}
.pointer {
  cursor: pointer;
}
.nodata {
  color: rgba(255, 255, 255, 0.35);
  font-size: 14px;
  text-align: center;
  margin: 20px;
}
</style>
