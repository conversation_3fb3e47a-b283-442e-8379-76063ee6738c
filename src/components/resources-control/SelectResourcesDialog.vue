<template>
  <div class="select-file-root">
  <!-- 弹窗 上传文件 -->
  <el-dialog
    class="select-resources-dialog"
    :visible.sync="openDialogVisible"
    width="60%"
    top="0"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :append-to-body="true"
    >
    <div class="dialog-content-select-file">
      <div class="content-left">
        <div class="left-overflow">
          <div v-for="(item, idx) in fileList" :key="idx">
            <div class="left-list"
              @click="item.isOpen = !item.isOpen"
              >
              <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !item.isOpen}"></i>
              <span class="list-title">{{item.name}}</span>
            </div>
            <div v-show="item.isOpen">
              <div v-for="(it, idx) in item.folderList"
              :key="idx"
              @click="selectFolderLeft(it)"
              :id="'id-folder-name-' + it.id"
              >
                <div class="left-list padding-left-24"
                :class="{'active': it.isOpen}">
                  <div class="list-name-flex">
                    <i :class="[it.isOpen ? 'el-icon-folder-opened' : 'el-icon-folder']" class="margin-right-4"></i>
                    <span class="list-title">{{it.name}}</span>
                  </div>
                  <span class="file-total">{{it.list | fileListTotal}}</span>
                  </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="content-right">
        <div class="content-right-title" style="display: flex;justify-content: flex-end;height: 28px;align-items: center;margin-bottom: 4px;">
          <div style="width: 160px;margin-right: 16px;">
            <el-input
              placeholder="请输入搜索内容"
              size="mini"
              prefix-icon="el-icon-search"
              @change="searchFile(searchFileName)"
              v-model="searchFileName">
            </el-input>
          </div>
          <el-button type="primary" @click="isUploadDialog = true;" style="margin-right: 8px;">上 传</el-button>
          <div style="display: block; width: 16px; height:16px;cursor: pointer;"
          @click="handleClose()">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="folder-concent-parent">
          <div class="folder-concent" >
            <div class="img-concent">
              <div class="concent-div" v-for="(imgfile, index) in activeFolderList.list" :key="index" v-show="imgfile.isShow">
                <!-- 主题管理中 有 themePicture type -->
                <el-image
                  class="img-style"
                  style="width: 100px; height: 80px"
                  :src="imgfile.ecryptUrl"
                  v-if="resourceType.includes('icture')"
                  lazy
                  >
                  <div slot="placeholder" class="image-slot" style="line-height: 80px;text-align: center;color: #797979;">
                    加载中<span class="dot">...</span>
                  </div>
                </el-image>
                <video class="img-style"
                  :src="imgfile.url"
                  style="width: 100px; height: 80px"
                  v-if="resourceType.includes('ideo')"
                  ></video>
                <el-image
                  class="img-style"
                  :class="{'icon-img': resourceType === 'icon' }"
                  style="width: 100px; height: 80px"
                  :src="imgfile.resourceUrl"
                  v-if="resourceType.includes('con')"
                  lazy
                  >
                  <div slot="placeholder" class="image-slot" style="line-height: 80px;text-align: center;color: #797979;">
                    加载中<span class="dot">...</span>
                  </div>
                </el-image>
                <div class="img-select" @click="selectImg(imgfile)">
                  <hz-icon
                  class="img-select-svg"
                  :name="imgfile.resourceId === selectFile.resourceId ? 'select-circle-ok' : 'select-circle-no'"></hz-icon>
                </div>
                <div class="img-name" :title="imgfile.name" :id="'id-file-name-' + imgfile.resourceId">{{imgfile.name}}</div>
              </div>
            </div>
            <div class="nodata" v-show="activeFolderList.list && activeFolderList.list.length === 0">
              暂无数据
            </div>
          </div>
        </div>
        <div class="select-dialog-footer">
          <el-button @click="handleClose()">取 消</el-button>
          <el-button type="primary" @click="selected()">确 定</el-button>
        </div>
      </div>
    </div>
  <seatom-loading v-if="loading"></seatom-loading>
  </el-dialog>
  <upload-file
    :isUploadDialog="isUploadDialog"
    :resourceType="resourceType"
    :selectFolderOptions="selectFolderOptions"
    @closeUploadDialog="closeUploadDialog"
    @refreshResourceList="refreshResourceList"
    ></upload-file>
  </div>
</template>
<script>
import UploadFile from './UploadFile';
import { getResourceList } from '@/api/workspace';
import { replaceUrl } from '@/utils/base';

export default {
  name: 'SelectResourcesDialog',
  components: {
    UploadFile
  },
  props: {
    isOpenDialog: Boolean,
    // 资源类型 enum: ['video', 'audio', 'picture', 'json'], // 主题管理中，themePicture  themeVideo
    resourceType: String,
    openFolderName: {
      type: String,
      default: '边框'
    },
    // 设置上传至某库
    uploadFloderLibName: {
      type: String,
      default: '公共库'
    },
    // 此处为了定位 模板库，主题库
    imagePath: {
      type: String,
      default: ''
    },
    // 当前选择的 图片, 需要根据它定位图片位置
    selectImgUrl: {
      type: String,
      default: ''
    },
    isAdmin: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    isOpenDialog: {
      handler: function (val) {
        this.openDialogVisible = val;
      },
      immediate: true
    }
  },
  data () {
    return {
      loading: false,
      userId: '',
      uploadDialogVisible: false,
      // 上传弹窗开关
      isUploadDialog: false,
      selectFile: {},
      fileList: [
        {
          name: '系统库',
          isOpen: false,
          folderList: []
        },
        {
          name: '公共库',
          isOpen: false,
          folderList: [
          ]
        }
      ],
      activeFolderList: {
        list: []
      },
      selectFolderOptions: [],
      // 搜索名称
      searchFileName: '',
      // 用于保存文件夹选中状态 临时，刷新文件夹时使用
      tempSelectFolder: '',
      allTreeNodeMap: new Map([])
    }
  },
  created () {
    this.selectFile = {};
    this.userId = localStorage.getItem('userId');
    this.getResourceListFun(this.resourceType);
  },
  methods: {
    // 上传后刷新数据
    refreshResourceList (type, data) {
      this.getResourceListFun(this.resourceType, data);
    },
    async getResourceListFun (type, folderInfo = null) {
      this.loading = true;
      const res = await getResourceList({ resourceType: type });
      this.loading = false;
      if (res.success) {
        if (res.data.length === 0) {
          this.$message.error('获取资源文件为空，请联系管理员');
        }
        if (folderInfo && folderInfo.length > 1) {
          // 上传文件夹以后 定位到所选文件夹
          this.fileList = this.getFileListSelectFolder(res.data, folderInfo);
        } else {
          this.fileList = this.getFileList(res.data);
        }
        // console.log('最终选择文件信息', this.selectImgUrl, this.selectFile);
        this.selectFolderOptions = this.getSelectFolderOptions(res.data);
        // const self = this;
        setTimeout(() => {
          // 定位打开的文件夹
          const selectFolderDocument = document.getElementById('id-folder-name-' + this.activeFolderList.id);
          if (selectFolderDocument) {
            selectFolderDocument.scrollIntoView(true);
          }
          const selectFildDocument = document.getElementById('id-file-name-' + this.selectFile.resourceId);
          if (selectFildDocument) {
            selectFildDocument.scrollIntoView(true);
          }
        }, 100);
      }
    },
    // 控制加载loading
    setLoading (data) {
      this.loading = data;
    },
    // 获取某个对象是否选中
    getSelectItem (list) {
      if (this.selectImgUrl === '') {
        return list;
      }
      let selectLock = true;
      // 把所有树展开 方便节点控制
      list.forEach((item, index) => {
        item.isOpen = true;
        item.folderList.forEach((itemfolder, indexfolder) => {
          itemfolder.isOpen = false;
          itemfolder.resourceList.forEach((itemfield, indexfield) => {
            if (this.selectImgUrl.includes(this.handleResourceUrl(itemfield.resourceUrl))) {
              // 获取到
              itemfolder.isOpen = true;
              itemfield.isSelect = true;
              if (selectLock) {
                selectLock = false;
                this.selectFile = itemfield;
              }
              // console.log('所选择的文件信息', this.selectImgUrl, item, itemfolder, this.selectFile);
            } else {
              itemfield.isSelect = false;
            }
            // 写入map
            this.allTreeNodeMap.set(itemfield.resourceUrl, {
              rootInfo: {
                name: item.name,
                node: item,
                index: index
              },
              folderInfo: {
                name: itemfolder.resourceFolder,
                node: itemfolder,
                id: itemfolder.id,
                index: indexfolder
              },
              fieldInfo: {
                name: itemfield.resourceName,
                node: itemfield,
                id: itemfield.resourceId,
                index: indexfield
              }
            });
          });
        });
      });
      // console.log('资源Map=>', this.selectImgUrl, this.allTreeNodeMap);
    },

    // 上传完文件夹定位所选文件夹
    getFileListSelectFolder (list, folderInfo) {
      list.forEach((item) => {
        item.isOpen = true;
        item.folderList.forEach(folder => {
          folder.name = folder.resourceFolder;
          folder.imgList = folder.resourceList.map(it => {
            return replaceUrl(process.env.VUE_APP_SERVER_URL + it.resourceUrl);
          });
          folder.resourceList.forEach(img => {
            img.name = img.resourceName;
            img.isSelect = false;
            img.isShow = true;
            img.url = replaceUrl(process.env.VUE_APP_SERVER_URL + img.resourceUrl);
          });
          folder.list = folder.resourceList;
          folder.isOpen = folder.name === folderInfo[1];
        });
      });
      // 选中的文件夹确认
      this.setFolderStateNew(list);
      return list;
    },
    // 数据映射
    getFileList (list) {
      this.getSelectItem(list);
      list.forEach((item, index) => {
        // 个人库 与 系统库 展开 方便定位文件夹
        item.isOpen = true;
        item.folderList.forEach((folder, index) => {
          folder.name = folder.resourceFolder;
          folder.imgList = folder.resourceList.map(it => {
            return replaceUrl(process.env.VUE_APP_SERVER_URL + it.resourceUrl);
          });
          folder.resourceList.forEach(img => {
            img.name = img.resourceName;
            // img.isSelect = false;
            img.isShow = true;
            img.url = replaceUrl(process.env.VUE_APP_SERVER_URL + img.resourceUrl);
            // 判断是否有预览缩略图
            if (img.ecryptUrl) {
              // 走图片服务器
              // if (process.env.VUE_APP_IMG_SERVER_URL) {
              //   img.ecryptUrl = process.env.VUE_APP_IMG_SERVER_URL + img.ecryptUrl + '?width=100&height=80';
              // } else {
              //   img.ecryptUrl = process.env.VUE_APP_SERVER_URL + img.ecryptUrl + '?width=100&height=80';
              // }
              img.ecryptUrl = replaceUrl(img.ecryptUrl + '?width=100&height=80');
              if (this.resourceType === 'icon') {
                img.resourceUrl = replaceUrl(process.env.VUE_APP_SERVER_URL + img.resourceUrl)
              }
            } else {
              img.ecryptUrl = img.url;
            }
          });
          folder.list = folder.resourceList;
          if (this.resourceType.includes('tpl') || this.resourceType.includes('theme')) {
            folder.isOpen = index === 0;
          } else {
            if (this.selectImgUrl === '') {
              if (this.tempSelectFolder === '') {
                folder.isOpen = folder.name === this.openFolderName;
              } else {
                folder.isOpen = folder.name === this.tempSelectFolder;
                if (folder.isOpen) {
                  this.activeFolderList = folder;
                }
              }
            } else {
              folder.resourceList.forEach(imgInfo => {
                if (this.selectImgUrl.includes(this.handleResourceUrl(imgInfo.resourceUrl))) {
                  imgInfo.isSelect = true;
                  folder.isOpen = true;
                  this.activeFolderList = folder;
                } else {
                  imgInfo.isSelect = false;
                }
              });
            }
          }
        });
      });
      this.setFolderStateNew(list);
      return list;
    },
    // 图标文件选中单独处理
    handleResourceUrl (url) {
      if (this.resourceType === 'icon') {
        const resourceAry = url.split('/')
        const resourceName = resourceAry[resourceAry.length - 1].split('.')[0]
        return resourceName
      }
      return url
    },
    // 检查是否有选中文件夹
    checkFolderIsOpen (list) {
      const listIsOpen = [];
      list.forEach((item, index) => {
        const isClose = item.folderList.every(function (it) {
          return it.isOpen === false;
        });
        listIsOpen.push(isClose);
      });
      // 有选中返回false , 没有任何选中 返回true
      return listIsOpen.every(function (it) {
        return it;
      });
    },
    // 设置默认选中的文件夹
    setFolderStateNew (list) {
      if (this.checkFolderIsOpen(list)) {
        list.forEach((item, index) => {
          item.folderList.forEach(folder => {
            folder.isOpen = folder.name === this.openFolderName;
          });
        });
        if (this.checkFolderIsOpen(list)) {
          list.forEach((item, index) => {
            if (index === 0) {
              item.folderList[0].isOpen = true;
              this.activeFolderList = item.folderList[0];
            }
          });
        } else {
          list = this.selectFolderOnly(list);
        }
      } else {
        list = this.selectFolderOnly(list);
      }
      return list;
    },
    // 只允许一个文件夹选中
    selectFolderOnly (list) {
      const tempArr = [];
      list.forEach((item) => {
        const temp = item.folderList.filter(function (it) {
          return it.isOpen;
        });
        if (temp.length > 0) {
          tempArr.push(temp[0]);
        }
      });
      if (tempArr.length > 0) {
        tempArr.forEach((fld, idxarr) => {
          if (idxarr !== 0) {
            fld.isOpen = false;
          }
        });
        this.activeFolderList = tempArr[0];
      }
      return list;
    },
    // 搜索文件
    searchFile (name) {
      this.activeFolderList.list.forEach(item => {
        if (name === '') {
          item.isShow = true;
        } else {
          item.isShow = item.name.includes(name);
        }
      });
    },
    // 选择文件夹
    selectFolderLeft (item) {
      this.fileList.forEach(file => {
        file.folderList.forEach(it => {
          it.isOpen = false;
        });
      });
      item.isOpen = true;
      this.activeFolderList = item;
      this.tempSelectFolder = item.name;
    },
    // 图片选择
    selectImg (item) {
      item.isSelect = !item.isSelect;
      this.selectFile = item;
    },
    // 获取上传文件夹列表
    getSelectFolderOptions (list) {
      const tempOptions = [];
      list.forEach(item => {
        const tempItem = {
          value: item.resourceLibraryCode,
          label: item.name,
          disabled: this.isAdmin ? false : item.name === '系统库',
          children: []
        };
        item.folderList.forEach(folder => {
          tempItem.children.push({
            value: folder.resourceFolder,
            label: folder.resourceFolder
          });
        });
        tempOptions.push(tempItem);
      });
      return tempOptions;
    },
    selected () {
      this.$emit('selectedFile', this.selectFile);
      this.$emit('closeRescourcesDialog');
    },
    // 关闭上传对话框
    handleClose () {
      this.$emit('closeRescourcesDialog');
      // this.$confirm('确认关闭？', '', {
      //   type: 'warning',
      //   closeOnClickModal: false
      // }).then(_ => {
      //   this.$emit('closeRescourcesDialog');
      //   done();
      // }).catch(_ => {});
    },
    closeUploadDialog () {
      this.isUploadDialog = false;
    }
  }

}
</script>
<style lang="scss" >
/**专为文件选择器弹窗写 以前是放在全局写，现优化至此处start*/
.select-resources-dialog .el-dialog {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
    color: #bfbfbf;
  }
}

.select-resources-dialog {
  .dialog-content-select-file {
    padding: 0;
    display: flex;
    height: 500px;
    .content-left {
      width: 210px;
      padding: 16px 0;
      background-color: rgb(40, 48, 62);
      cursor: pointer;
      .left-overflow {
        height: 100%;
        overflow: hidden;
        overflow-y: auto;
      }
      .left-list {
        height: 32px;
        line-height: 32px;
        margin: 0 8px;
        padding-left: 8px;
        display: flex;
        align-items: center;
        .list-title {
          display: block;
          max-width: 108px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .list-name-flex {
          display: flex;
          align-items: center;
        }
      }
      .padding-left-24 {
        padding-left: 24px;
        justify-content: space-between;
      }
      .active {
        background-color: #409effd9;
      }
    }
    .content-right {
      flex: 1;
      margin: 12px;
      .folder-concent-parent {
        overflow: auto;
        // overflow-y: auto;
        height: 420px;
      }
      .folder-name {
        line-height: 28px;
        height: 28px;
        color: #fff;
        width: 400px;
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
      }
      .folder-name-max-width {
        max-width: 288px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .img-concent {
        display: flex;
        // max-height: 0px;
        overflow: hidden;
        flex-flow: row wrap;
        .img-style {
          margin: 8px 12px;
          border-radius: 8px;
          background: var(--seatom-background-400);
        }
        .icon-img {
          background-color: #ffffff;
        }
        .concent-div {
          position: relative;
          .img-name {
            max-width: 120px;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            top: -10px;
            margin-left: 4px;
            position: relative;
            cursor: default;
          }
          .img-select {
            position: absolute;
            display: flex;
            width: 100px;
            height: 80px;
            top: 5px;
            left: 5px;
            padding: 3px;
            cursor: pointer;
            // align-items: flex-end;
            .img-select-svg {
              position: absolute;
              left: 80px;
              top: 10px;
            }
          }
        }
        .el-image__preview {
          cursor: default;
          object-fit: scale-down;
        }
        .el-image__inner {
          object-fit: scale-down;
        }
        .el-image__error {
          background: #303643;
        }
      }
    }
    .arrow-rotate {
      transform: rotate(-90deg);
      transition: transform 0.3s;
    }
    .file-total {
      display: block;
      font-weight: bold;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
      margin-right: 8px;
    }
  }
  .select-dialog-footer {
    justify-content: flex-end;
    display: flex;
    margin-top: 4px;
  }
  .el-button {
    padding: 4px 12px;
  }
  .hz-icon {
    width: 16px;
    height: 16px;
  }
  .margin-right-4 {
    margin-right: 4px;
  }
  .nodata {
    color: rgba(255, 255, 255, 0.35);
    font-size: 14px;
    text-align: center;
    margin: 20px;
  }
}
/** 文件选择组件弹窗样式覆盖 end*/
</style>
