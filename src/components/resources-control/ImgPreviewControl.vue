<template>
  <div class="img-resources">
    <div class="title-add">
      <el-button type="primary" style="padding: 12px 15px;" @click="dialogCreateIndicator=true" v-if="(resourceType === 'indicator')">
        <div class="add-text"><hz-icon name="plus" style="margin-right: 8px;"></hz-icon>
        <span>创建</span>
        </div>
      </el-button>
      <el-button type="primary" style="padding: 12px 15px;" @click="uploadFileOpenDialog()" v-if="(isAdmin && resourceType !=='indicator') || (!isAdmin && resourceLibraryCode !== 'system' && resourceType !=='indicator')">
        <div class="add-text"><hz-icon name="upload" style="margin-right: 8px;"></hz-icon>
        <span>上传</span>
        </div>
      </el-button>
      <el-button class="el-button--plain" type="primary" style="padding: 12px 15px;" @click="traFile()" v-if="resourceType !== 'indicator' && (isAdmin || (!isAdmin && resourceLibraryCode !== 'system'))">
        <div class="add-text"><hz-icon name="replace" style="margin-right: 8px;"></hz-icon>
        <span>移动</span>
        </div>
      </el-button>
      <el-button class="el-button--plain" type="primary" style="padding: 12px 15px;" @click="delFile()" v-if="resourceType!=='indicator' && (isAdmin || ((!isAdmin && resourceLibraryCode !== 'system') && (!isAdmin && resourceLibraryCode !== 'personal')))">
        <div class="add-text"><hz-icon name="trash" style="margin-right: 8px;"></hz-icon>
        <span>删除</span>
        </div>
      </el-button>
      <el-button class="el-button--plain" type="primary" style="padding: 12px 15px;" @click="delIndicator" v-if="(resourceType === 'indicator')">
        <div class="add-text"><hz-icon name="trash" style="margin-right: 8px;"></hz-icon>
        <span>删除</span>
        </div>
      </el-button>
      <super-authority class="title-other" @checkAdmin="checkAdmin"></super-authority>
      <div style="width: 245px;">
        <el-input
          placeholder="请输入搜索内容"
          size="mini"
          class="input-theme"
          prefix-icon="el-icon-search"
          @change="searchFile(searchFileName)"
          v-model="searchFileName">
        </el-input>
      </div>
    </div>
    <div class="select-all" @click="setSelectImgList()">
      <hz-icon :name="'ico-select-' + selectAll" style="margin-right: 4px;"></hz-icon>
      <span style="font-size: 14px">全选</span>
    </div>
    <div class="img-perview">
      <div class="img-concent">
        <div v-for="(imgfile, index) in selfList" :key="imgfile.id" v-show="imgfile.isShow" class="concent-div">
          <div class="img-border" v-if="resourceType.includes('icture')">
            <el-image
              class="img-style"
              style="width: 268px; height: 170px"
              :src="imgfile.ecryptUrl"
              :preview-src-list="getPrivewImages(index)"
            >
              <div slot="placeholder" class="image-slot" style="line-height: 170px;text-align: center;color: #797979;">
                加载中<span class="dot">...</span>
              </div>
              <div slot="error" class="image-slot" style="padding: 45px 0;text-align: center;color: #797979;">
                <hz-icon name="icon-img-load-error" style="width: 61px;height: 52px;margin-bottom: 8px;"></hz-icon>
                <span style="display: block;">图片加载失败</span>
              </div>
            </el-image>
          </div>
          <div class="img-border"
          v-if="resourceType.includes('ideo')"
          @click="previewVideo(imgfile.url)">
            <video class="img-style"
              :src="imgfile.url"
              style="width: 268px; height: 170px"
              ></video>
          </div>
          <div class="img-border"
            @click="previewDocfile(imgfile.url)"
            v-if="resourceType.includes('ocfile')">
            <pdf class="img-style"
              v-if="imgfile.url.includes('pdf')"
              :src="imgfile.url"
              style="width: 268px; height: 170px; overflow: hidden;"
              ></pdf>
            <div class="img-style"
              v-if="!imgfile.url.includes('pdf')"
              style="width: 268px; height: 170px; overflow: hidden;"
              >
                <span class="not-preview">该文件暂无缩略图，请点击预览</span>
            </div>
          </div>
          <div class="img-border" v-if="resourceType.includes('con')">
            <el-image
              class="img-style img-icon"
              style="width: 134px; height: 85px"
              :src="imgfile.ecryptUrl"
              :preview-src-list="getPrivewImages(index)"
            >
              <div slot="placeholder" class="image-slot" style="line-height: 85px;text-align: center;color: #797979;">
                加载中<span class="dot">...</span>
              </div>
              <div slot="error" class="image-slot" style="padding: 45px 0;text-align: center;color: #797979;">
                <hz-icon name="icon-img-load-error" style="width: 61px;height: 52px;margin-bottom: 8px;"></hz-icon>
                <span style="display: block;">图片加载失败</span>
              </div>
            </el-image>
          </div>
          <div class="img-border indicator" v-if="resourceType.includes('indicator')">
            <el-image
              v-if="imgfile.ecryptUrl"
              class="img-style"
              style="width:268px; height:170px"
              :src="imgfile.ecryptUrl?imgfile.ecryptUrl:''"
            >
              <div slot="placeholder" class="image-slot" style="line-height: 170px;text-align: center;color: #797979;">
                加载中<span class="dot">...</span>
              </div>
              <div slot="error" class="image-slot" style="padding: 45px 0;text-align: center;color: #797979;">
                <hz-icon name="icon-img-load-error" style="width: 61px;height: 52px;margin-bottom: 8px;"></hz-icon>
                <span style="display: block;">图片加载失败</span>
              </div>
            </el-image>
            <div v-else :style="{width:'268px',height:'170px'}" class="default"></div>
            <div class="screen-edit">
              <div class="screen-btn" @click="goToIndicatorEdit(imgfile)">
                <hz-icon name="edit" class="mr-8"></hz-icon>编辑
              </div>
            </div>
          </div>
          <div class="img-select" :class="{'icon-select': resourceType.includes('con')}" @click="selectImg(imgfile)">
            <hz-icon class="hz-icon-18" :name="imgfile.isSelect ? 'select-circle-ok' : 'select-circle-no'"></hz-icon>
          </div>
          <div class="img-bottom">
            <div class="img-name" :title="imgfile.name.split('.')[0]">
              <div class="edit-name" :style="{display:'flex',alignItems:'center'}">
                <input class="input" type="text" :title="imgfile.name" v-model="imgfile.name" :spellcheck="false" @blur="updateIndicator(imgfile)">
              </div>
              <!-- <input  class="input" type="text" :title="imgfile.name"> -->
              <hz-icon
              v-if="isAdmin"
              name="edit"
              class="name-edit"
              @click="editName(imgfile, index)"></hz-icon>
            </div>
            <div class="copy-url" v-clipboard:copy="imgfile.url" v-clipboard:success="onCopy" v-clipboard:error="onCopyError" v-if="resourceType !== 'indicator'">
              <hz-icon name="icon-copy" style="width: 16px;height: 16px;margin-right: 4px;"></hz-icon>
              <span>复制URL</span>
            </div>
            <div v-else class="date">
              <span>{{imgfile.createAt | formatDate}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="nodata" v-if="list.length === 0">
          <p>暂无数据</p>
      </div>
    </div>
  <upload-file
    :isUploadDialog="isUploadDialog"
    :isAdmin="isAdmin"
    :resourceType="resourceType"
    :selectFolderOptions="selectFolderOptions"
    @closeUploadDialog="closeUploadDialog"
    @refreshResourceList="refreshResourceList"
    ></upload-file>
  <!-- 删除文件前提示 -->
  <el-dialog
    title="提示"
    :visible.sync="dialogVisibleDel"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    width="30%"
    top="0"
    :close-on-click-modal="false">
    <div class="dialog-content-del-info">
      <div>是否确认删除？</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="dialogVisibleDel = false;">取 消</el-button>
      <el-button size="mini" type="primary" @click="delFiles()">确 定</el-button>
    </span>
  </el-dialog>
  <!-- 移动文件提示框 -->
  <el-dialog
    title="选择文件夹"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    :visible.sync="dialogVisibleTransfer"
    width="452px"
    top="0"
  >
  <el-cascader
    class="input-theme"
    popper-class="poper-theme"
    v-model="setSelectTraFolderInfo"
    :options="setSelectFolderOptions"
    size="medium"
    style="flex: 1;width: 100%"
  >
  </el-cascader>
  <div slot="footer" class="dialog-footer">
    <el-button size="mini" type="text" @click="dialogVisibleTransfer = false">取 消</el-button>
    <el-button size="mini" type="primary" @click="traFiles()">添加至文件夹</el-button>
  </div>
  </el-dialog>
  <el-dialog
    title="预览"
    :visible.sync="dialogVisibleVideo"
    width="600px"
    top="0"
    :before-close="closeVideoPreview"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    :close-on-click-modal="false">
    <video
    controls
    autoplay
    :src="previewVideoUrl"
    ref="previewVideoDialog"
    width="100%"
    height="350px"
    ></video>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closePreview()">确 定</el-button>
    </span>
  </el-dialog>
  <!-- 弹窗 修改文件名称 -->
  <el-dialog
    title="修改名称"
    :visible.sync="dialogVisibleEditName"
    width="30%"
    top="0"
    class="upload-file-add-folder"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    :append-to-body="true"
    :close-on-click-modal="false">
    <div class="dialog-content dialog-content-add-folder">
      <div class="content-list" style="margin-bottom: 8px;">
        <div class="content-name">原名称1</div>
        <div class="content-input">
          {{editFileInfo.originName}}
        </div>
      </div>
      <div class="content-list">
        <div class="content-name">新名称</div>
        <div class="content-input">
          <el-input
            v-model="editFileInfo.newName"
            size="mini"
            >
          </el-input>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="dialogVisibleEditName = false;">取 消</el-button>
      <el-button size="mini" type="primary" @click="submitNewName()">确 定</el-button>
    </span>
  </el-dialog>
  <!-- 创建指标库提示 -->
  <div class="indicator-dialog">
  <el-dialog
    :visible.sync="dialogCreateIndicator"
    title="创建指标"
    width="500px"
    top="0">
    <el-form label-width="90px" label-position="right" size="mini">
      <el-form-item label="中文名称：" status-icon>
          <el-input class="input-theme" v-model="indicatorName" placeholder="请输入指标名称" v-focus></el-input>
        </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="dialogCreateIndicator = false">取 消</el-button>
      <el-button size="mini" type="plain" @click="createIndicator">确 定</el-button>
    </span>
  </el-dialog>
  </div>
  <!-- 删除指标库提示 -->
  <el-dialog
    title="提示"
    :visible.sync="dialogVisibleDelIndicator"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    width="30%"
    top="0"
    :close-on-click-modal="false">
    <div class="dialog-content-del-info">
      <div>是否确认删除？</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="dialogVisibleDelIndicator = false;">取 消</el-button>
      <el-button size="mini" type="primary" @click="delIndicators()">确 定</el-button>
    </span>
  </el-dialog>
  <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import pdf from 'vue-pdf';
import SuperAuthority from './SuperAuthority';
import UploadFile from './UploadFile';
import { delResourceFile, editResourceFileName, moveResourcdFile, getResourceList, createIndicator, deleteIndicator, updateIndicator } from '@/api/workspace';
import { createComponent } from '@/api/component'
import { uuid } from '@/utils/base'
export default {
  name: 'ImgPreviewControl',
  props: {
    list: {
      type: Array,
      default: function () {
        return [];
      }
    },
    imgList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    folderId: Number,
    selectFolderOptions: {
      type: Array,
      default: function () {
        return [];
      }
    },
    selectFolderInfo: {
      type: Array,
      default: function () {
        return [];
      }
    },
    // 资源类型
    resourceType: String,
    resourceLibraryCode: String
  },
  components: {
    SuperAuthority,
    UploadFile,
    pdf
  },
  data () {
    return {
      loading: false,
      // 打开上传弹窗
      isUploadDialog: false,
      // 移动文件夹弹窗
      dialogVisibleTransfer: false,
      // 删除前弹窗提示
      dialogVisibleDel: false,
      // 删除指标库提示
      dialogVisibleDelIndicator: false,
      // 预览视频弹窗
      dialogVisibleVideo: false,
      // 重命名弹窗
      dialogVisibleEditName: false,
      // 创建指标弹窗
      dialogCreateIndicator: false,
      indicatorName: '',
      // 预览视频的url
      previewVideoUrl: '',
      delFolderList: [],
      userInfo: {
        name: 'admin',
        token: '',
        userId: 'd0438698030d3e206cf55f8e1005e996'
      },
      isAdmin: false,
      selectAll: 'no',
      // 搜索文件
      searchFileName: '',
      // 图片缩略图列表
      selfList: [],
      selfImgList: [],
      editFileInfo: {
        originName: '',
        newName: '',
        resourceId: '',
        index: 0
      },
      setSelectFolderOptions: [],
      setSelectTraFolderInfo: [],
      fileListImg: []
    };
  },
  watch: {
    isAdmin: {
      handler: function (val) {
        this.selectFolderOptions.forEach((item) => {
          item.disabled = val ? false : item.resourceLibraryCode === 'system';
        });
      },
      immediate: true
    },
    list: {
      handler: function (val) {
        this.selfList = _.cloneDeep(val);
        this.selectAll = 'no';
      },
      immediate: true,
      deep: true
    },
    imgList: {
      handler: function (val) {
        this.selfImgList = val;
      },
      immediate: true,
      deep: true
    },
    selectFolderOptions: {
      handler: function (val) {
        if (this.resourceLibraryCode === 'personal') {
          this.setSelectFolderOptions = val
            .filter(item => {
              return !item.disabled && item.value === 'personal'
            });
        } else {
          this.setSelectFolderOptions = val
            .filter(item => {
              return !item.disabled
            });
        }
        // this.isShowSelectFlolder = ['tplVideo', 'tplPicture', 'themeVideo', 'themePicture'].indexOf(this.resourceType) === -1;
        if (!this.isShowSelectFlolder) {
          this.setSelectTraFolderInfo = [];
          // this.setSelectFolderInfo.push(val[0].value);
          // this.setSelectFolderInfo.push(val[0].children[0].value);
        }
      },
      immediate: true,
      deep: true
    }
  },
  created () {
    this.getUserId();
    // 骨骼页面默认开启超级权限
    if (this.resourceType === 'skeletonPicture') {
      this.isAdmin = true;
    }
  },
  methods: {
    // 图片点击预览大图数组重新排序
    getPrivewImages (index) {
      const tempImgList = [...this.selfImgList];// 所有图片地址
      if (index === 0) return tempImgList;
      const start = tempImgList.splice(index);
      const remain = tempImgList.splice(0, index);
      return start.concat(remain);
    },
    // 当前userid 存在localStorage 后续需要修改
    getUserId () {
      this.userInfo.userId = localStorage.getItem('userId');
      this.userInfo.name = localStorage.getItem('name') || 'admin';
    },
    // 控制加载loading
    setLoading (data) {
      this.loading = data;
    },
    // 刷新资源文件夹
    refreshResourceList (type) {
      this.$emit('refreshResourceList', type);
    },
    // 删除图片
    delFile () {
      const tempSelect = this.selfList.every(function (item) {
        // console.log(item,'item_one');
        return !item.isSelect;
      });

      if (tempSelect) {
        this.$message.warn('您未选择文件');
        return;
      }
      this.dialogVisibleDel = true;
    },
    // 确定删除
    async delFiles () {
      const selectImgList = [];
      this.selfList.forEach(item => {
        if (item.isSelect) {
          selectImgList.push({
            id: this.folderId,
            resourceId: item.resourceId,
            resourceType: this.resourceType
          });
        }
      });

      this.loading = true;
      const res = await delResourceFile(selectImgList);
      this.loading = false;
      // 删除成功
      if (res.success) {
        this.refreshResourceList(this.resourceType);
        this.selectAll = 'no';
        this.$message.success('删除成功');
        this.dialogVisibleDel = false;
      }
    },
    // 打开弹窗
    uploadFileOpenDialog () {
      // this.saveFolderOpenList();
      this.isUploadDialog = true;
    },
    // 移动文件
    async traFile () {
      const tempSelect = this.selfList.every(function (item) {
        return !item.isSelect;
      });
      if (tempSelect) {
        this.$message.warn('您未选择文件');
        return;
      }
      this.dialogVisibleTransfer = true;
      const res = await getResourceList({ resourceType: this.resourceType });
      if (res.success) {
        this.SelectFolderOptions = res.data
        this.setSelectFolderOptions.forEach(item => {
          const curtSelectFolderOptions = this.SelectFolderOptions.filter(ops => ops.resourceLibraryCode === item.value)[0]
          if (curtSelectFolderOptions && curtSelectFolderOptions.folderList) {
            item.children.forEach((cur, idx) => {
              if (curtSelectFolderOptions.folderList[idx]) {
                cur.id = curtSelectFolderOptions.folderList[idx].id
                cur.value = cur.value + '_' + curtSelectFolderOptions.folderList[idx].id
              }
            })
          }
        })
      }
    },
    // 确定移动文件
    async traFiles () {
      const isSelectMoveimage = []
      this.selfList.forEach(item => {
        if (item.isSelect) {
          isSelectMoveimage.push(item);
        }
      });
      const str = parseInt(this.setSelectTraFolderInfo[1].split('_')[1])
      if (str === this.folderId) {
        this.$message({
          text: '已在当前文件夹',
          type: 'warn'
        });
        return;
      }
      const param = {
        id: this.folderId,
        moveId: str,
        resourceList: isSelectMoveimage
      }
      this.loading = true;
      const ress = await moveResourcdFile(param);
      this.loading = false;
      // 移动成功
      if (ress.success) {
        this.refreshResourceList(this.resourceType);
        // this.selectAll = 'no';
        this.$message.success('移动成功');
        this.dialogVisibleTransfer = false;
      }
    },
    // 子组件关闭自己
    closeUploadDialog () {
      this.isUploadDialog = false;
    },
    // 选择图片文件夹
    setSelectImgList () {
      let tempSelect = false;
      if (this.selectAll === 'no' || this.selectAll === 'other') {
        this.selectAll = 'ok';
        tempSelect = true;
      } else {
        this.selectAll = 'no';
        tempSelect = false;
      }
      this.selfList.forEach(item => {
        item.isSelect = tempSelect;
      });
    },
    // 选择图片时 设置文件夹状态
    selectImg (item) {
      item.isSelect = !item.isSelect;
      const allOk = this.selfList.every(it => it.isSelect);
      const allNo = this.selfList.every(it => !it.isSelect);
      if (allOk) {
        this.selectAll = 'ok';
      } else if (allNo) {
        this.selectAll = 'no';
      } else {
        this.selectAll = 'other';
      }
    },
    checkAdmin () {
      this.isAdmin = true;
      this.$emit('checkAdmin');
    },
    // 搜索文件
    searchFile (name) {
      this.selfList.forEach(img => {
        if (name === '') {
          img.isShow = true;
        } else {
          img.isShow = img.name.includes(name);
        }
      });
    },
    // 预览视频
    previewVideo (url) {
      this.previewVideoUrl = url;
      this.dialogVisibleVideo = true;
    },
    closeVideoPreview (done) {
      this.previewVideoUrl = '';
      done();
      this.dialogVisibleVideo = false;
    },
    closePreview () {
      this.previewVideoUrl = '';
      this.dialogVisibleVideo = false;
    },
    onCopy () {
      this.$message.success('复制url成功');
    },
    onCopyError () {
      this.$message.warn('复制失败');
    },
    editName (item, index) {
      this.editFileInfo.originName = item.name;
      this.editFileInfo.resourceId = item.resourceId;
      this.editFileInfo.newName = '';
      this.dialogVisibleEditName = true;
      this.editFileInfo.index = index;
    },
    async submitNewName () {
      const param = {
        resourceLibraryCode: this.resourceLibraryCode,
        id: this.folderId
      }
      const data = {
        resourceId: this.editFileInfo.resourceId,
        resourceName: this.editFileInfo.newName
      }
      this.loading = true;
      const res = await editResourceFileName(data, param);
      this.loading = false;
      if (res.success) {
        this.selfList[this.editFileInfo.index].name = this.editFileInfo.newName;
        this.dialogVisibleEditName = false;
      }
    },
    previewDocfile (url) {
      window.open(url, '_blank')
    },
    async createIndicator () {
      if (this.indicatorName) {
        const id = uuid('interaction-container-indicator')
        const data = {
          id,
          indicatorContainer: true,
          name: 'interaction-container-indicator',
          attr: {
            x: 0,
            y: 0,
            w: 500,
            h: 300
          }
        }
        this.loading = true
        createComponent(data, { screenId: 1 }).then(res => {
          if (res.success) {
            const param = {
              name: this.indicatorName,
              componentId: id,
              userId: this.userInfo.userId,
              userName: this.userInfo.name
            }
            const data = { workspaceId: this.$route.params.workspaceId }
            createIndicator(param, data).then(res => {
              this.dialogCreateIndicator = false
              this.indicatorName = ''
              this.refreshResourceList(this.resourceType)
              this.$message.success('创建成功')
              this.$router.push({
                name: 'screen/indicator/edit',
                params: { id: res.data.componentId },
                query: { indicatorId: res.data.id }
              })
            }).finally(() => {
              this.loading = false
            })
          }
          this.loading = false
        })
      } else {
        this.$message.warn('指标名称不能为空')
      }
    },
    delIndicator () {
      const indicatorSelect = this.selfList.filter(item => {
        return item.isSelect
      })
      if (indicatorSelect.length === 0) {
        this.$message.warn('您未选择指标')
        return
      }
      this.dialogVisibleDelIndicator = true
    },
    async delIndicators () {
      const indicatorSelect = this.selfList.filter(item => {
        return item.isSelect
      })
      const componentIds = []
      const ids = []
      indicatorSelect.forEach(el => {
        componentIds.push(el.componentId)
        ids.push(el.id)
      })
      const param = { componentIds, ids }
      this.loading = true
      deleteIndicator(param).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.refreshResourceList(this.resourceType)
          this.dialogVisibleDelIndicator = false
        }
        this.searchFileName = ''
        this.loading = false
      })
    },
    goToIndicatorEdit (item) {
      this.$router.push({
        name: 'screen/indicator/edit',
        params: { id: item.componentId },
        query: { indicatorId: item.id }
      })
    },
    updateIndicator (data) {
      const query = {
        id: data.id
      }
      const param = {
        name: data.name
      }
      this.loading = true
      updateIndicator(param, query).then(res => {
        if (res.success) {
          this.$message.success('更新成功');
          this.loading = false
          this.refreshResourceList(this.resourceType)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.img-resources {
  position: relative;
  height: 100%;
  .title-add {
    position: sticky;
    top: 120px;
    height: 64px;
    z-index: 9;
    display: flex;
    margin-left: 32px;
    width: calc(100% - 72px);
    background-color: var(--seatom-background-200);
    border-bottom: 1px solid var(--seatom-mono-a300);
    padding: 16px 0;
    .add-text {
      top: -5px;
      position: relative;
      display: flex;
      align-items: center;
    }
    .title-other {
      flex: 1;
      height: 100%;
    }
  }
  .select-all {
    height: 32px;
    line-height: 32px;
    display: flex;
    margin-left: 32px;
    color: var(--seatom-type-800);
    cursor: pointer;
    width: 120px;
    align-items: center;
    margin-top: 8px;
  }
  .img-perview {
    position: relative;
    // height: 100%;
    overflow: auto;
    margin-left: 18px;
    // margin: 8px 32px;
    .classify-title {
      height: 32px;
      line-height: 32px;
      // background: rgba(204, 219, 255, 0.06);
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #fff;
      width: 200px;
      cursor: pointer;
    }
    .folder-concent {
      max-height: 0px;
      overflow: hidden;
      transition: max-height .3s;
      .folder-name {
        line-height: 28px;
        height: 28px;
        margin-bottom: 12px;
        color: #fff;
        width: 400px;
        cursor: pointer;
        margin-left: 4px;
        display: flex;
        align-items: center;
        font-size: 14px;
        .folder-name-max-width {
          max-width: 288px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .del-folder-icon {
          display: none;
        }
        &:hover {
          .del-folder-icon {
            display: block;
          }
        }
      }
    }
    .img-concent {
      display: flex;
      overflow: hidden;
      flex-flow: row wrap; // flex 布局下超出自动换行
      .img-style {
        background: var(--seatom-background-400);
        border-radius: 8px;
        margin: 4px;
        .not-preview {
          display: block;
          line-height: 170px;
          text-align: center;
          color: var(--seatom-type-800);
          cursor: default;
        }
      }
      .img-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        ::v-deep {
          img {
            filter: invert(1) !important;
            width: 32px;
            height: 32px;
          }
        }
      }
      .img-border {
        border-radius: 8px;
        border: 2px solid #5291ff00;
        &:hover {
          border: 2px solid #5291FF;
        }
      }
      .indicator {
        position: relative;
        display: flex;
        justify-content: center;
        .screen-edit{
          position: absolute;
          width: calc(100% - 4px);
          height: calc(100% - 4px);
          top:2px;
          left:2px;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          background: rgba(21,22,24,0.8);
          transition: opacity .2s;
          .screen-btn{
            width: 178px;
            height: 39px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgb(31, 113, 255);
            font-family: 思源黑体Medium;
            font-style: normal;
            font-weight: 700;
            font-size: 16px;
            color: rgb(255, 255, 255);
            cursor: pointer;
            border-radius: 8px;
          }
        }
      }
      .indicator:hover .screen-edit{
        opacity: 1;
        pointer-events: all;
      }
      .concent-div {
        position: relative;
        margin: 0 12px;
        margin-bottom: 16px;
        .img-select {
          position: absolute;
          display: block;
          top: 6px;
          left: 234px;
          padding: 10px;
          cursor: pointer;
        }

        .icon-select {
          left: 100px;
          &:before {
            content: unset;
          }
        }
        .img-bottom {
          display: flex;
          justify-content: space-between;
          .copy-url{
            height: 28px;
            line-height: 28px;
            cursor: pointer;
            border-radius: 4px;
            color: var(--seatom-type-800);
            padding: 4px 8px;
            display: flex;
            align-items: center;
            margin-right: 6px;
            font-weight: 400;
            &:hover {
              background: var(--seatom-primary-a100);
              color: var(--seatom-primary-900);
            }
          }
          .date{
            height: 28px;
            line-height: 28px;
            cursor: pointer;
            border-radius: 4px;
            color: var(--seatom-type-800);
            padding: 4px 8px;
            display: flex;
            align-items: center;
            margin-right: 6px;
            font-weight: 400;
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
          }
        }
        .img-name {
          line-height: 28px;
          font-size: 14px;
          color: var(--seatom-type-800);
          margin-left: 8px;
          position: relative;
          cursor: default;
          align-items: center;
          display: flex;
          .name {
            max-width: 175px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .icon-name {
            max-width: 52px !important;
          }
          .name-edit {
            width: 20px;
            height: 20px;
            margin-left: 4px;
            color: rgb(128, 125, 125);
            cursor: pointer;
            &:hover {
              color: #fff;
            }
          }
          .edit-name{
            .input{
              color: var(--seatom-type-800);
              background: 0 0;
              line-height: 20px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              border: 1px solid transparent;
              max-width: 120px;
              font-weight: 600;
              font-size: 16px;
              font-family: PingFang SC;
              font-style: normal;
            }
          }
        }
      }
      .img-border-div {
        // border: 1px solid var(--seatom-mono-a300);
        // border-radius: 16px;
      }
      ::v-deep .el-image__preview {
        cursor: default;
        object-fit: scale-down;
      }
    }
  }
  .file-total{
    height: 20px;
    width: 26px;
    border-radius: 8px;
    background-color: rgba(92, 156, 255, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    // color: #398BFF;
    font-size: 10px;
    margin-left: 6px;
    margin-right: 8px;
  }
  ::v-deep .el-button {
    padding: 6px 12px;
  }
  .hz-icon {
    width: 16px;
    height: 16px;
  }
  .hz-icon-18 {
    width: 20px;
    height: 20px;
  }
  ::v-deep .el-dialog {
    left: calc(50% - 250px);
    top: calc(50% - 240px);
    transform: unset;
  }
  .indicator-dialog{
    ::v-deep .el-dialog {
      left: calc(50% - 250px);
      top: calc(50% - 90px);
      transform: unset;
    }
  }
}
::v-deep .upload-demo {
  width: 80%;
  margin: auto;
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      background-color: unset;
      width: 100%;
      border: 1px dashed #c4c3c357;
      border-radius: 4px;
    }
  }
  .el-upload-list {
    margin-bottom: 8px;
  }
  .el-upload-list__item:hover {
    background-color: #419eff14;
  }
}
::v-deep .dialog-content {
  padding: 4px 64px;
    .content-list {
      display: flex;
      // margin-bottom: 8px;
      .content-name {
        width: 88px;
        line-height: 28px;
        color: var(--seatom-type-700);
        font-weight: 600;
        font-size: 14px !important;
      }
      .content-input {
        color: red;
        flex: 1;
        display: flex;
        align-items: center;
        .el-select {
          flex: 1;
        }
        .add-folder {
          margin-left: 6px;
          cursor: pointer;
          &:hover {
            color: #fff;
          }
        }
      }
      .el-radio {
        margin-right: 10px;
      }
    }
  }
::v-deep .dialog-content-del-info {
  padding: 0 16px;
}
::v-deep .dialog-content-add-folder {
  padding: 4px 24px;
}

.icon-visible {
  display: block;
  width: 16px;
  height: 16px;
  background: url("../../assets/img/svg/visible.svg") bottom center no-repeat;
}
.pointer {
  cursor: pointer;
}
.nodata {
  color: rgba(255, 255, 255, 0.35);
  font-size: 14px;
  text-align: center;
  margin: 20px;
}

.arrow-rotate {
  transform: rotate(-90deg);
  transition: transform .3s;
}

.arrow-down {
  transition: transform .3s;
}
::v-deep .el-button--plain  {
    // background: #1b3d6187;
    // border-color: #4679af;
    border-radius: 4px;
    &:hover {
      color: #409EFF;
    }
  }
.hz-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.default{
  background: url('../../assets/img/defaultIndicator.png') no-repeat center/contain;
  background-color: var(--fuxi-dark-background-400, #202737);
  border-radius: 8px;
  border: 2px solid rgba(0,0,0,0);
  margin:4px
}
</style>
