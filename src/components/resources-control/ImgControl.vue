<template>
  <div class="img-resources">
    <!-- <div class="title-add">
      <el-button type="primary" style="padding: 12px 15px;" @click="uploadFileOpenDialog()">
        <div class="add-text"><hz-icon name="upload" style="margin-right: 8px;"></hz-icon>上传</div>
      </el-button>
      <el-button type="primary" plain style="padding: 12px 15px;" @click="delImg()">
        <div class="add-text"><hz-icon name="trash" style="margin-right: 8px;"></hz-icon>删除</div>
      </el-button>
      <super-authority class="title-other" @checkAdmin="checkAdmin"></super-authority>
      <div style="width: 160px;margin-right: 8px;">
        <el-input
          placeholder="请输入搜索内容"
          size="mini"
          prefix-icon="el-icon-search"
          @change="searchFile(searchFileName)"
          v-model="searchFileName">
        </el-input>
      </div>
    </div>
    <div class="img-perview">
      <div v-for="(item, index) in fileList" :key="index">
        <div class="classify-title" @click="item.isOpen = !item.isOpen" v-if="isAdmin">
          <span :title="item.name">{{item.name}}</span>
          <i class="el-icon-arrow-down arrow-down" :class="{'arrow-rotate': !item.isOpen}"></i>
        </div>
        <div v-if="isAdmin || (!isAdmin && item.resourceLibraryCode !== 'system')" :class="{'folder-concent-root': (!isAdmin && item.resourceLibraryCode !== 'system')}">
          <div class="folder-concent" :class="{'folder-concent-Open': item.isOpen}" v-for="(it, idx) in item.folderList" :key="idx">
            <div class="folder-name" @click="it.isOpen = !it.isOpen">
              <hz-icon :name="'ico-select-' + it.isSelect" @click.native.stop="selectFolder(it)"></hz-icon>
              <i :class="[it.isOpen ? 'el-icon-folder-opened' : 'el-icon-folder']" style="margin:0 4px;"></i>
              <span class="folder-name-max-width" :title="it.name">{{it.name}}</span>
              <span class="file-total">{{it.list | fileListTotal}}</span>
              <i class="el-icon-arrow-down arrow-down" :class="{'arrow-rotate': !it.isOpen}" style="margin-left: 4px;margin-right: 4px;"></i>
              <hz-icon class="del-folder-icon"
              v-if="isAdmin && it.name !== '背景' && it.name !== '边框' && it.name !== '默认文件夹'"
              name="trash"
              style="margin-left: 4px"
              @click.native.stop="delFolder(it)"></hz-icon>
            </div>
            <div class="img-concent" :class="{'img-concent-open': it.isOpen}">
              <div class="concent-div" v-for="imgfile in it.list" :key="imgfile.id" v-show="imgfile.isShow">
                <el-image
                  class="img-style"
                  style="width: 140px; height: 100px"
                  :src="imgfile.url"
                  :preview-src-list="it.imgList">
                </el-image>
                <div class="img-select" @click="selectImg(it, imgfile)">
                  <hz-icon class="hz-icon-18" :name="imgfile.isSelect ? 'select-circle-ok' : 'select-circle-no'"></hz-icon>
                </div>
                <div class="img-name" :title="imgfile.name">{{imgfile.name}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="nodata" v-if="fileList.length === 0">
          <p>暂无数据</p>
      </div>
    </div>
  <upload-file
    :isUploadDialog="isUploadDialog"
    :isAdmin="isAdmin"
    resourceType="picture"
    :selectFolderOptions="selectFolderOptions"
    @closeUploadDialog="closeUploadDialog"
    @refreshResourceList="refreshResourceList"
    ></upload-file> -->
  <!-- 删除文件前提示 -->
  <!-- <el-dialog
    title="提示"
    :visible.sync="dialogVisibleDel"
    width="30%"
    top="0"
    :close-on-click-modal="false">
    <div class="dialog-content-del-info" style="color: #bfbfbf;">
      <div v-show="isDelFolder">删除文件夹后，该文件夹内文件会同步删除</div>
      <div>是否确认删除？</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisibleDel = false;">取 消</el-button>
      <el-button type="primary" @click="delFiles()">确 定</el-button>
    </span>
  </el-dialog>
  <seatom-loading v-if="loading"></seatom-loading>
  -->
  </div>
</template>

<script>
/// 该组件暂时弃用
// import SuperAuthority from './SuperAuthority';
// import UploadFile from './UploadFile';
// import { getResourceList, delResourceFile, delResourceFolder } from '@/api/workspace';

// export default {
//   name: 'ImgControl',
//   components: {
//     SuperAuthority,
//     UploadFile
//   },
//   data () {
//     return {
//       loading: false,
//       // 打开上传弹窗
//       isUploadDialog: false,
//       selectFolderOptions: [],
//       // 删除前弹窗提示
//       dialogVisibleDel: false,
//       // 当前删除是否为文件夹
//       isDelFolder: false,
//       delFolderList: [],
//       userInfo: {
//         name: 'admin',
//         token: '',
//         userId: 'd0438698030d3e206cf55f8e1005e996'
//       },
//       isAdmin: false,
//       selectFolderInfo: {
//         folderRoot: '个人库',
//         name: ''
//       },
//       // 搜索文件
//       searchFileName: '',
//       folderOptions: [],
//       fileList: [
//         {
//           name: '系统库',
//           id: 'system-img',
//           isOpen: false,
//           folderList: [
//             {
//               name: '背景',
//               id: 'folder-bg',
//               isOpen: false,
//               isSelect: 'no',
//               list: [{
//                 name: '图片1',
//                 isSelect: false,
//                 url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
//               },
//               {
//                 name: '图片2',
//                 isSelect: false,
//                 url: 'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg'
//               },
//               {
//                 name: '图片3',
//                 isSelect: false,
//                 url: 'https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg'
//               }],
//               imgList: [
//                 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
//                 'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
//                 'https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg'
//               ]
//             }
//           ],
//           list: [],
//           imgList: []
//         }
//       ],
//       folderOpenList: []
//     };
//   },
//   watch: {
//     isAdmin: {
//       handler: function (val) {
//         this.selectFolderOptions.forEach((item) => {
//           item.disabled = val ? false : item.resourceLibraryCode === 'system';
//         });
//       },
//       immediate: true
//     }
//   },
//   created () {
//     this.getUserId();
//     this.getResourceListFun();
//   },
//   methods: {
//     // 当前userid 存在localStorage 后续需要修改
//     getUserId () {
//       this.userInfo.userId = localStorage.getItem('userId');
//       this.userInfo.name = localStorage.getItem('name') || 'admin';
//     },
//     async getResourceListFun () {
//       this.loading = true;
//       const res = await getResourceList({ resourceType: 'picture' });
//       this.loading = false;
//       if (res.success) {
//         if (res.data.length === 0) {
//           this.$message.error('获取资源文件为空，请联系管理员');
//         }
//         this.fileList = this.getFileList(res.data);
//         this.selectFolderOptions = this.getSelectFolderOptions(res.data);
//       }
//     },
//     // 控制加载loading
//     setLoading (data) {
//       this.loading = data;
//     },
//     // 刷新资源文件夹
//     refreshResourceList () {
//       this.getResourceListFun();
//     },
//     // 数据映射
//     getFileList (list) {
//       list.forEach(item => {
//         item.id = item.name;
//         item.isOpen = true;
//         item.folderList.forEach(folder => {
//           folder.name = folder.resourceFolder;
//           folder.isOpen = this.folderOpenList.includes(folder.resourceFolder);
//           folder.isSelect = 'no';
//           folder.imgList = folder.resourceList.map(it => {
//             return process.env.VUE_APP_SERVER_URL + it.resourceUrl;
//           });
//           folder.resourceList.forEach(img => {
//             img.name = img.resourceName;
//             img.isSelect = false;
//             img.isShow = true;
//             img.url = process.env.VUE_APP_SERVER_URL + img.resourceUrl;
//           });
//           folder.list = folder.resourceList;
//         });
//       });
//       return list;
//     },
//     // 获取上传文件夹列表
//     getSelectFolderOptions (list) {
//       const tempOptions = [];
//       list.forEach(item => {
//         const tempItem = {
//           value: item.resourceLibraryCode,
//           label: item.name,
//           disabled: this.isAdmin ? false : item.resourceLibraryCode === 'system',
//           children: []
//         };
//         item.folderList.forEach(folder => {
//           tempItem.children.push({
//             value: folder.resourceFolder,
//             label: folder.resourceFolder
//           });
//         });
//         tempOptions.push(tempItem);
//       });
//       return tempOptions;
//     },
//     // 保存打开的文件夹 用户上传完 刷新列表 保存打开记录
//     saveFolderOpenList () {
//       this.folderOpenList = [];
//       this.fileList.forEach(item => {
//         item.folderList.forEach(folder => {
//           if (folder.isOpen) {
//             this.folderOpenList.push(folder.name);
//           }
//         });
//       });
//     },
//     // 删除图片
//     delImg () {
//       this.isDelFolder = false;
//       this.dialogVisibleDel = true;
//     },
//     // 删除文件夹
//     delFolder (item) {
//       this.delFolderList = [item.id];
//       this.isDelFolder = true;
//       this.dialogVisibleDel = true;
//     },
//     // 确定删除
//     async delFiles () {
//       if (this.isDelFolder) {
//         this.loading = true;
//         const res = await delResourceFolder(this.delFolderList);
//         this.loading = false;
//         // 删除成功
//         if (res.success) {
//           this.getResourceListFun();
//           this.$message.success('操作成功');
//           this.isDelFolder = false;
//           this.dialogVisibleDel = false;
//         }
//       } else {
//         const selectImgList = [];
//         this.fileList.forEach(item => {
//           if (this.isAdmin) {
//             item.folderList.forEach(it => {
//               it.list.forEach(img => {
//                 if (img.isSelect) {
//                   selectImgList.push({
//                     id: it.id,
//                     resourceId: img.resourceId
//                   });
//                 }
//               });
//             });
//           } else {
//             if (item.name !== '系统库') {
//               item.folderList.forEach(it => {
//                 it.list.forEach(img => {
//                   if (img.isSelect) {
//                     selectImgList.push({
//                       id: it.id,
//                       resourceId: img.resourceId
//                     });
//                   }
//                 });
//               });
//             }
//           }
//         });
//         if (selectImgList.length === 0) {
//           this.$message.warn('您未选择文件');
//           return;
//         }
//         this.loading = true;
//         const res = await delResourceFile(selectImgList);
//         this.loading = false;
//         // 删除成功
//         if (res.success) {
//           this.getResourceListFun();
//           this.$message.success('操作成功');
//           this.isDelFolder = false;
//           this.dialogVisibleDel = false;
//         }
//       }
//     },
//     // 打开弹窗
//     uploadFileOpenDialog () {
//       this.saveFolderOpenList();
//       this.isUploadDialog = true;
//     },
//     // 子组件关闭自己
//     closeUploadDialog () {
//       this.isUploadDialog = false;
//     },
//     // 选择图片文件夹
//     selectFolder (it) {
//       let tempSelect = false;
//       if (it.isSelect === 'no' || it.isSelect === 'other') {
//         it.isSelect = 'ok';
//         tempSelect = true;
//       } else {
//         it.isSelect = 'no';
//         tempSelect = false;
//       }
//       if (it.list) {
//         it.list.forEach(item => {
//           item.isSelect = tempSelect;
//         });
//       }
//     },
//     // 选择图片时 设置文件夹状态
//     selectImg (itemparent, item) {
//       item.isSelect = !item.isSelect;
//       const allOk = itemparent.list.every(it => it.isSelect);
//       const allNo = itemparent.list.every(it => !it.isSelect);
//       if (allOk) {
//         itemparent.isSelect = 'ok';
//       } else if (allNo) {
//         itemparent.isSelect = 'no';
//       } else {
//         itemparent.isSelect = 'other';
//       }
//     },
//     checkAdmin () {
//       this.isAdmin = true;
//     },
//     // 搜索文件
//     searchFile (name) {
//       this.fileList.forEach(file => {
//         file.folderList.forEach(item => {
//           item.list.forEach(img => {
//             if (name === '') {
//               img.isShow = true;
//             } else {
//               img.isShow = img.name.includes(name);
//             }
//           });
//         });
//       });
//     }
//   }
// }
</script>
