<!--
 * @Author: your name
 * @Date: 2022-02-22 19:34:58
 * @LastEditTime: 2022-03-04 14:48:50
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /seatom/src/components/editor/LayerSearchTree.vue
-->
<template>
  <div class="layer-tree">
    <div class="layer-tree-list">
      <template v-for="(node, index) in loadedNodes">
      <li
        v-if="node.searchNode"
        :key="node.data.id"
        :ref="`ref_node_${node.data.id}`"
        :class="[
          'layer-node',
          node.data.type === 'group' ? 'layer-node-group' : 'layer-node-com',
          {
            collapse: node.collapse,
            selected: node.selected,
            'layer-node-thumbnail': !isThumbnail,
          },
        ]"
        :style="{
          paddingLeft: `${8 + 10 * (node.depth - 1)}px`,
        }"
        :data-id="node.data.id"
        :data-index="index"
        @click.exact="handleClick(node)"
        @click.meta.exact="handleClickCtrl(node)"
        @click.shift.exact="handleClickShift(node)"
        v-contextmenu:contextmenu
      >
        <i
          :class="[
            node.collapse ? 'icon el-icon-arrow-right' : 'icon el-icon-arrow-right active',
          ]"
          v-if="node.data.type === 'group'"
          @click.stop="handleCollapseClick(node)"
        >
        </i>
        <div class="folder" v-if="!!node.data.groupName">
          <i
            :class="[
              node.collapse ? 'el-icon-folder' : 'el-icon-folder-opened',
            ]"
            style="margin: 0 4px"
          ></i>
          {{ node.data.groupName }}
        </div>
        <div
          v-show="isThumbnail && node.data.type !== 'group'"
          class="components-item-img"
          :style="{
            backgroundImage: getComDataById(node.data.id)
              ? `url(${replaceUrl(getComDataById(node.data.id).icon)})`
              : null,
          }"
        ></div>
        <hz-icon
          class="mr-10"
          v-show="!isThumbnail"
          :name="handleIsThumbnailIcon(node.data.id)"
        ></hz-icon>
        <span>{{
          getComDataById(node.data.id) && getComDataById(node.data.id).alias
        }}</span>
        <div class="layer-thumbail-item">
          <i
            v-show="
              node.data.type === 'group'
                ? compLock(node)
                : getComDataById(node.data.id) &&
                  getComDataById(node.data.id).attr.lock
            "
            class="el-icon-lock mr-6"
            @click="handleCtxClick('unLock')"
          ></i>
          <i v-show="node.data.type === 'group'
                ? !compShow(node)
                : getComDataById(node.data.id) && !getComDataById(node.data.id).show" class="el-icon-view mr-6" @click="handleCtxClick('show')"></i>
        </div>
      </li>
      </template>
      <div
        v-if="layerMoveLineShow"
        class="layer-move-to-line"
        :style="{
          transform: `translate(0, ${layerMoveLineY}px)`,
        }"
      ></div>
      <div ref="dragingWrap" class="draging-wrap"></div>
    </div>
    <!-- 右键菜单 -->
    <div style="display: none">
      <v-contextmenu ref="contextmenu" @contextmenu="handleCtxMenu">
        <v-contextmenu-item
          v-for="menu in ctxMenuList"
          v-show="menu.show"
          :key="menu.key"
          @click="handleCtxClick(menu.key)"
          :class="[{'contextmenu-disable': (!cancelGroupShow && menu.key === 'cancelGroup') ||
          (groupShow && menu.key === 'group') ||
          (renameShow && menu.key === 'rename')}]"
        >
          {{ menu.label }}
        </v-contextmenu-item>
      </v-contextmenu>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { compPanelList } from '@/common/constants';
import canvasBus from '@/utils/canvasBus';
import { replaceUrl } from '@/utils/base';
// import { log } from '@chenfengyuan/vue-qrcode';

export default {
  name: 'LayerTree',

  inject: ['getLayerTree'],

  props: {
    searchWord: {
      type: String,
      default: ''
    },
    isThumbnail: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    searchWord: {
      handler (newVal, oldVal) {
        this.judgeCollapse = true;
        // this.searchTree();
      }
    }
  },
  data () {
    return {
      shiftNode: null,
      layerMoveLineShow: false,
      layerMoveLineY: 0,
      replaceUrl: replaceUrl,
      // groupLock: [], //分组锁住结果
      contextmenuNode: '', // 右键组件名称
      judgeCollapse: false,
      originTree: []
    };
  },

  directives: {
    focus: {
      inserted: function (el) {
        el.querySelector('input').focus();
      }
    }
  },
  // created () {
  //   // if (this.originTree.length === 0) {
  //   this.originTree = _.cloneDeep(this.layerTree.loadedNodes);
  //   // }
  // },
  computed: {
    ...mapState({
      screenInfo: (state) => state.editor.screenInfo,
      sceneId: (state) => state.editor.sceneId,
      pageId: (state) => state.editor.pageId
    }),
    ...mapGetters('editor', ['getComDataById', 'ctxMenuList']),
    layerTree () {
      return this.getLayerTree();
    },
    loadedNodes () {
      const loadedNodes = this.searchTree;// this.layerTree.loadedNodes;
      if (this.screenInfo.screenType === 'scene') {
        if (this.pageId) {
          return loadedNodes.filter((node) => {
            return (
              node.data.pageId === this.pageId ||
              (node.data.sceneId === this.sceneId && !node.data.pageId)
            );
          });
        } else {
          return loadedNodes.filter((node) => {
            return node.data.sceneId === this.sceneId && !node.data.pageId;
          });
        }
      } else {
        return loadedNodes;
      }
    },
    searchTree () {
      // 将原有tree保存
      // const tree = _.cloneDeep(this.layerTree.loadedNodes);
      // console.log('树---', this.originTree);
      // this.$store.commit('editor/setOriginTree', this.layerTree.loadedNodes);
      // console.log('搜索关键词---', this.searchWord, this.searchWord.length)
      if (this.searchWord !== '') {
        // 循环将带有分组的tree树全部展开便于搜索
        for (let i = 0; i < this.layerTree.loadedNodes.length; i++) {
          if (!this.judgeCollapse) {
            const tree = this.layerTree.loadedNodes;
            if (tree[i].data.groupName && tree[i].collapse === true) {
              tree[i].collapse = false;
              this.layerTree.collapseChildNodes(tree[i].data, false);
            }
          }
        }
        this.recursionFindSearchComp(this.layerTree.loadedNodes);
      } else {
        this.layerTree.loadedNodes.forEach(node => {
          if (node.searchNode && node.searchNode === true) delete node.searchNode;
        })
      }
      // console.log('是否进行更新---', this.judgeCollapse)
      // if (!this.judgeCollapse)
      // canvasBus.emit('saveOriginTree', this.originTree);
      return this.layerTree.loadedNodes; // tree;
    },
    cancelGroupShow () {
      const selectedNode = []; let show = false;
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNode.push(item.data.id);
      })
      if (selectedNode.length) {
        let sum = 0;
        selectedNode.forEach(item => {
          if (item.includes('groups_')) sum++;
        })
        if (sum > 0) {
          show = true
        }
      }
      return show;
    },
    groupShow () {
      const selectedNodes = []; let show = false;
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNodes.push(item);
      })
      if (selectedNodes.length > 1) {
        for (let i = 1; i < selectedNodes.length; i++) {
          // 判断所选组件是否是同一层级
          if (!(selectedNodes[0]?.parent?.data?.id === selectedNodes[i]?.parent?.data?.id)) {
            show = true; break;
          }
          // 判断在场景大屏中是否是同一场景或页面
          if (!(selectedNodes[0]?.data?.pageId === selectedNodes[i]?.data?.pageId &&
          selectedNodes[0]?.data?.sceneId === selectedNodes[i]?.data?.sceneId)) {
            show = true; break;
          }
        }
      }
      return show;
    },
    renameShow () {
      const selectedNodes = [];
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNodes.push(item);
      })
      return selectedNodes.length !== 1;
    }
  },

  methods: {
    handleCtxMenu (vnode) {
      const nodeId = vnode.elm.dataset.id;
      let node;
      if (nodeId && (node = this.layerTree.getNodeById(nodeId))) {
        if (!node.selected) {
          this.layerTree.select(node.data.id);
          canvasBus.emit('select_node', nodeId);
        }
      }
    },
    handleCtxClick (key) {
      canvasBus.emit('ctx_click', key);
      if (key !== 'rename' && key !== 'delete') canvasBus.emit('select_node', null);
      if (key === 'copy') this.layerTree.clearSelect();
    },
    handleClick (node) {
      this.$refs.contextmenu.hide();
      this.layerTree.select(node.data.id);
      this.shiftNode = node;
      canvasBus.emit('select_node', node.data.id);
    },
    handleCollapseClick (node) {
      this.judgeCollapse = true;
      node.collapse = !node.collapse;
      this.layerTree.collapseChildNodes(node.data, node.collapse);
    },
    handleClickCtrl (node) {
      this.layerTree.select(node.data.id, 1);
      this.shiftNode = node;
      canvasBus.emit('select_node', this.getSelectedIds());
    },
    handleClickShift (node) {
      this.layerTree.select(
        node.data.id,
        2,
        this.shiftNode && this.shiftNode.data
      );
      this.shiftNode = node;
      canvasBus.emit('select_node', this.getSelectedIds());
    },
    handleIsThumbnailIcon (id) {
      if (!this.getComDataById(id)) return;
      const type = this.getComDataById(id).comType;
      if (!id.includes('groups_')) {
        const comList = compPanelList[type.split('-')[0]];
        if (comList) {
          if (comList.length > 1) {
            const iconObj = comList.filter((item) => {
              return item.type === type.split('-').slice(0, 2).join('-');
            });
            return iconObj[0].icon;
          } else {
            const iconObj = comList.filter((item) => {
              return item.type.split('-')[0] === type.split('-')[0];
            });
            return iconObj[0].thumIcon;
          }
        }
      }
    },
    getSelectedIds () {
      return this.layerTree.getSelectedNodes().map((n) => n.data.id);
    },
    findGroupLock (node) {
      node.children.forEach((item) => {
        if (item.children) {
          this.findGroupLock(item);
        } else {
          if (!this.groupLock) this.groupLock = []
          this.groupLock.push(item);
        }
      });
    },
    compLock (node) {
      this.findGroupLock(node)
      const lock = this.groupLock.map((item) => {
        return !!(this.getComDataById(item.data.id)?.attr.lock);
      });
      this.groupLock = [];
      return !lock.includes(false);
    },
    findGroupShow (node) {
      node.children.forEach((item) => {
        if (item.children) {
          this.findGroupShow(item);
        } else {
          if (!this.groupShowList) this.groupShowList = []
          this.groupShowList.push(item);
        }
      });
    },
    compShow (node) {
      this.findGroupShow(node)
      const show = this.groupShowList.map((item) => {
        return !!(this.getComDataById(item.data.id)?.show);
      });
      this.groupShowList = [];
      return !!show.includes(true);
    },
    recursionFindSearchComp (nodes) {
      nodes.forEach(node => {
        if (node.searchNode && node.searchNode === true) delete node.searchNode;
        if ((node.data.groupName && node.data.groupName.includes(this.searchWord)) ||
          (node.data.type !== 'group' &&
          this.getComDataById(node.data.id) &&
          this.getComDataById(node.data.id).alias.includes(this.searchWord)) || node.data.id.includes(this.searchWord)) {
          node.searchNode = true;
        }
        if (node.children) {
          this.recursionFindSearchComp(node.children);
        }
        if (node.parent.searchNode) node.searchNode = true;
      })
    }
  }
};
</script>

<style scoped lang="scss">
.layer-tree {
  .layer-tree-list {
    background: #1d2127;
    user-select: none;
    position: relative;
    cursor: pointer;
    .layer-node {
      width: 100%;
      height: 48px;
      background: transparent; //#1b1f25;
      color: white;
      padding-right: 6px;
      display: flex;
      align-items: center;
      position: relative;
      .layer-thumbail-item {
        position: absolute;
        line-height: 1;
        right: 4px;
        top: 50%;
        font-size: 14px;
        transform: translate(0, -50%);
      }
      &:hover {
        background: #8fe1ff1a;
        color: #fff;
        .layer-thumbail-item i {
          color: #fff;
        }
      }
      &.selected {
        background: #2681ff;
        .layer-thumbail-item i {
          color: #fff;
        }
      }
    }
    .layer-node-thumbnail {
      height: 32px;
    }
    .layer-move-to-line {
      height: 2px;
      width: 100%;
      background: #00c1de;
      position: absolute;
      left: 0;
      top: 0;
    }
    .draging-wrap {
      width: 200px;
      position: fixed;
      right: 150%;
    }
  }
  .components-item-img {
    width: 53px;
    height: 34px;
    flex: none;
    display: block;
    border: 1px solid #3a4659;
    background: #282a30;
    background-clip: content-box;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 10px;
    margin-left: 6px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mr-6 {
    margin-right: 6px;
  }

}
.icon {
  transition: transform 0.25s linear;
}
.active {
  transform: rotate(90deg);
}
.contextmenu-disable {
    pointer-events: none;
    opacity: 0.5;
  }
</style>
