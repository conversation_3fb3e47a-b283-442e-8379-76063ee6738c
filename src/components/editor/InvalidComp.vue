<template>
  <div class="invalid-comp">
    <el-dialog title="提示" :visible.sync="show" :close-on-click-modal="false" width="600px" top="0">
      <div class="com-wrap">
        <div class="com-title">以下组件已失效：</div>
        <div class="comp-list">
          <div class="comp-item" v-for="item in nodes" :key="item.id">组件ID：{{ item.id }}</div>
        </div>
        <div class="tips">点击确认删除失效组件，否则可能无法使用大屏</div>
      </div>
      <div slot="footer">
        <el-button type="primary" size="mini" @click="deleteComp()">确认删除</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
export default {
  name: 'InvalidComp', // 失效组件弹框
  props: {
    nodes: {
      type: Array,
      default () { return [] }
    },
    layerTree: {
      type: Object,
      default () { return {} }
    }
  },
  data () {
    return {
      show: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    ...mapGetters('editor', ['getComDataById'])
  },
  methods: {
    showDialog () {
      this.show = true;
    },
    async deleteComp () {
      // 记录已经删除的layerId
      const deleteLayerIds = []

      // 获取处理后的layerTree
      const getValidComs = (layers) => {
        const validComs = [];
        for (let i = 0; i < layers.length; i++) {
          const layer = layers[i];
          if (layer.type === 'group') {
            layer.children = getValidComs(layer.children);
            validComs.push(layer);
          } else if (layer.type === 'com') {
            const comData = this.getComDataById(layer.id);
            if (comData) {
              validComs.push(layer);
            } else {
              deleteLayerIds.push(layer.id)
            }
          }
        }
        return validComs;
      }
      const validLayers = getValidComs(this.layerTree.data.children);

      if (!!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene') {
        await this.$store.dispatch('editor/deleteScreenLayerByIds', {
          layerIds: deleteLayerIds
        })
      } else {
        await this.$store.dispatch('editor/updateScreenLayers', validLayers)
      }

      this.$nextTick(() => {
        this.$emit('refresh');
        this.show = false;
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invalid-comp {
  .com-wrap {
    color: #fff;
    .com-title {
      font-size: 14px;
      margin-bottom: 10px;
    }
    .comp-list {
      margin-bottom: 20px;
      .comp-item {}
    }
    .tips {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>
