<template>
  <div class="screen-share-target">
    <div class="flex-row">
      <el-input
        v-model="userQuery"
        class="input-theme"
        size="mini"
        clearable>
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
      <el-button size="mini" type="primary" @click="syncUser">同步用户</el-button>
    </div>
    <el-tabs
      v-model="activeTab"
      @tab-click="handleClick"
      :class="{'tab-theme': !$route.name.includes('edit')}"
      >
      <el-tab-pane label="用户" name="user">
        <seatom-loading v-if="loadingUser"></seatom-loading>
        <div class="user-list-container list-container">
          <el-scrollbar style="flex: 1;">
            <li
              class="list-item common get-checked"
              v-if="isSync"
              @click.prevent="showUserChecked=!showUserChecked"
            >
              <el-checkbox v-model="showUserChecked"></el-checkbox>
              <span class="list-content">查看已选</span>
            </li>
            <li
              class="list-item common"
              v-for="user in filterUserList"
              :key="user.userId"
            >
              <el-checkbox v-model="user.select" @change="select(user)"></el-checkbox>
              <CircleAvatar
                class="avatar"
                :avatarStyle="handleAvatarStyle(user)"
                :avatarText="handleAvatarText(user.userName)"
              ></CircleAvatar>
              <span class="list-content">{{ user.userName }}</span>
            </li>
            <div class="empty-info" v-if="!filterUserList.length">
              <span class="empty-text">暂无数据</span>
            </div>
          </el-scrollbar>
        </div>
      </el-tab-pane>
      <el-tab-pane label="角色" name="role">
        <seatom-loading v-if="loadingRole"></seatom-loading>
        <div class="role-list-container list-container">
          <el-scrollbar style="height: 100%" v-if="roleList.length">
            <li
              class="list-item common get-checked"
              v-if="isSync"
              @click.prevent="showRoleChecked=!showRoleChecked"
            >
              <el-checkbox v-model="showRoleChecked"></el-checkbox>
              <span class="list-content">查看已选</span>
            </li>
            <li
              class="list-item common"
              v-for="role in filterRoleList"
              :key="role.role_id"
            >
              <el-checkbox @change="select($event, role)" :value="isChecked(role, 'role')"></el-checkbox>
              <CircleAvatar
                class="avatar"
                :avatarStyle="handleAvatarStyle(role)"
                :avatarText="handleAvatarText(role.role_name)"
              ></CircleAvatar>
              <span class="list-content">{{ role.role_name }}</span>
            </li>
            <div class="empty-info" v-if="!filterRoleList.length">
              <span class="empty-text">暂无数据</span>
            </div>
          </el-scrollbar>
        </div>
      </el-tab-pane>
      <el-tab-pane label="组织机构" name="group">
        <seatom-loading v-if="loadingOrgan"></seatom-loading>
        <div class="organ-list-container list-container">
          <el-scrollbar style="height: 100%">
            <li
              class="list-item common get-checked"
              v-if="isSync"
              @click.prevent="showGroupChecked=!showGroupChecked"
            >
              <el-checkbox v-model="showGroupChecked"></el-checkbox>
              <span class="list-content">查看已选</span>
            </li>
            <el-tree
              :data="filterGroupList"
              :props="groupProps"
              :filter-node-method="filterNode"
              ref="groupTree">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span class="icon" @click.stop>
                  <span>
                    <el-checkbox size="mini" @change="select($event, data)" :value="isChecked(data, 'group')"></el-checkbox>
                  </span>
                  <span v-if="!node.isLeaf">
                    <i v-if="node.expanded" class="el-icon-folder-opened"></i>
                    <i v-else class="el-icon-folder"></i>
                  </span>
                </span>
                <span>{{ node.label }}</span>
              </span>
            </el-tree>
          </el-scrollbar>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import CircleAvatar from '@/components/editor/CircleAvatar.vue';
import { getShareUser, getRoleList, getGroupList, syncUser } from '@/api/user';
export default {
  name: 'ShareList',
  components: {
    CircleAvatar
  },
  props: {
    mode: {
      type: String,
      default: 'sync'
    },
    selectedList: {
      type: Array,
      default: () => ([])
    }
  },
  data () {
    return {
      activeTab: 'user',
      userList: [], // 用户列表
      allUserList: [], // 全部用户列表
      roleList: [], // 角色列表
      groupList: [], // 组织机构列表，树形结构
      selectedRoleList: [],
      selectedGroupList: [],
      props: {
        label: 'name',
        children: 'children'
      },
      userQuery: '',
      loadingUser: false,
      loadingRole: false,
      loadingOrgan: false,
      showUserChecked: false,
      showRoleChecked: false,
      showGroupChecked: false,
      roleProps: {
        label: 'role_name',
        id: 'role_id',
        children: 'simple_volist'
      },
      groupProps: {
        label: 'group_name',
        id: 'group_id',
        children: 'group_list'
      }
    }
  },
  computed: {
    userId () {
      return this.$store.state.user.id;
    },
    userName () {
      return this.$store.state.user.name;
    },
    screenInfo () {
      return this.$store.state.editor.screenInfo;
    },
    filterUserList () {
      return this.showUserChecked ? this.allUserList.filter(item => item.select) : (this.userQuery ? this.fuzzyQuery(this.allUserList) : this.allUserList);
    },
    filterRoleList () {
      return this.showRoleChecked ? this.selectedRoleList : (this.userQuery ? this.fuzzyQuery(this.roleList) : this.roleList);
    },
    filterGroupList () {
      return this.showGroupChecked ? this.selectedGroupList : this.groupList;
    },
    isSync () {
      return this.mode === 'sync';
    },
    isChecked () {
      return (data, type) => {
        if (type === 'role') {
          return this.selectedRoleList.findIndex(item => item.role_id === data.role_id) > -1
        } else if (type === 'group') {
          return this.selectedGroupList.findIndex(item => item.group_id === data.group_id) > -1
        }
      }
    }
  },
  watch: {
    userQuery (val) {
      this.$refs.groupTree.filter(val)
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.getShareUser()
      this.getRoleList()
      this.getGroupList()
    },
    // async loadNode (node, resolve) {
    //   if (node.data?.sub_group?.length) {
    //     return resolve(node.data.sub_group)
    //   } else {
    //     if (node.data?.group_id) {
    //       const res = await getGroupTree({
    //         group_id: node.data.group_id
    //       })
    //       const data = res.data?.result ?? []
    //       data.forEach(item => {
    //         item.isLeaf = !item.has_sub
    //       })
    //       return resolve(data)
    //     }
    //   }
    // },
    async syncUser () {
      const res = await syncUser()
      if (res.success) {
        this.getShareUser()
      }
    },
    handleAvatarText (userName) {
      return userName.slice(0, 1).toUpperCase();
    },
    // 处理头像样式
    handleAvatarStyle (user) {
      if (!user) return {};
      return {
        fontSize: 12,
        color: '#fff',
        width: 20,
        height: 20,
        borderRadius: 10,
        bgc: '#00BBC2'
      };
    },
    handleClick (tab) {},
    async getShareUser () {
      this.loadingUser = true
      const res = await getShareUser();
      this.loadingUser = false
      let data = [];
      if (res.success) {
        const userId = localStorage.getItem('userId'); // 当前登录用户id
        // 共享列表不包含当前登录用户
        data = res.data.filter((item) => {
          if (this.selectedList.findIndex(s => s.userId === item.userId) > -1) {
            item.select = true
          } else {
            item.select = false;
          }
          return item.userId !== userId
        });
      }
      this.userList = data;
      this.allUserList = this.userList.slice(0);
    },
    async getRoleList () {
      this.loadingRole = true
      const res = await getRoleList()
      this.loadingRole = false
      if (res.success && res.data?.result) {
        this.selectedRoleList = _.cloneDeep(this.selectedList.filter(item => item.type === 'role'))
        this.roleList = res.data.result
      }
    },
    async getGroupList () {
      this.loadingOrgan = true
      const res = await getGroupList()
      this.loadingOrgan = false
      if (res.success && res.data?.result) {
        this.selectedGroupList = _.cloneDeep(this.selectedList.filter(item => item.type === 'group'))
        this.groupList = res.data.result
      }
    },
    getActiveTabData (type) {
      switch (type || this.activeTab) {
        case 'user':
          return this.userList.filter(item => item.select === true)
        case 'role':
          return this.selectedRoleList
        case 'group':
          return this.selectedGroupList
        default:
          return []
      }
    },
    select (val, item) {
      if (this.activeTab === 'role') {
        if (val) {
          this.selectedRoleList.push(item)
        } else {
          const index = this.selectedRoleList.findIndex(s => s.role_id === item.role_id)
          index > -1 && this.selectedRoleList.splice(index, 1)
        }
      } else if (this.activeTab === 'group') {
        if (val) {
          this.selectedGroupList.push(item)
        } else {
          const index = this.selectedGroupList.findIndex(s => s.group_id === item.group_id)
          index > -1 && this.selectedGroupList.splice(index, 1)
        }
      }
      const data = this.getActiveTabData()
      this.$emit('change', data, this.activeTab)
    },
    unselect (item, all = false) {
      if (all) {
        this.userList.forEach(item => (item.select = false))
        this.selectedRoleList = []
        this.selectedGroupList = []
        this.$emit('change', [])
        return
      }
      const type = item.type || 'user'
      const data = this.getActiveTabData(type)
      let index = -1
      if (type === 'user') {
        index = data.findIndex(s => s.userId === item.id)
        index > -1 && (data[index].select = false);
      } else if (type === 'role') {
        index = data.findIndex(s => s.role_id === item.id)
      } else if (type === 'group') {
        index = data.findIndex(s => s.group_id === item.id)
      }
      index > -1 && data.splice(index, 1)
      this.$emit('change', data, type)
    },
    fuzzyQuery (list) {
      // 前端模糊查询
      const keyWord = this.userQuery
      const reg = new RegExp(keyWord);
      const arr = [];
      for (let i = 0; i < list.length; i++) {
        const name = list[i].userName || list[i].role_name
        if (reg.test(name)) {
          arr.push(list[i]);
        }
      }
      return arr;
    },
    filterNode (value, data, node) {
      if (!value) return true;
      return node.label.indexOf(value) !== -1;
    }
  }
}
</script>
<style lang="scss">
.screen-share-target {
  padding: 16px 16px 0;
  .list-container {
    height: 400px;
    display: flex;
    flex-direction: column;
    .empty-info {
      min-height: 60px;
      position: relative;
      text-align: center;
      .empty-text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        color: #909399;
        font-size: 14px;
      }
    }
  }
  .flex-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .avatar {
    margin-left: 8px;
    margin-right: 3px;
  }
  .list-item {
    color: var(--seatom-type-900) !important;
    list-style: none;
    padding: 10px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    &.get-checked {
      .list-content {
        margin-left: 8px;
      }
    }
    &:hover {
      background-color: rgba(61, 133, 255, 0.1);
    }
    &.common {
      padding-left: 5px;
    }
  }
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-tabs__item {
    font-size: 14px;
  }
  .tab-theme .el-tabs__active-bar {
    height: 2px;
  }
  .tab-theme .el-tabs__nav-wrap::after {
    background-color: var(--seatom-mono-a300);
  }
  .el-tree, .filter-tree {
      background-color: var(--seatom-background-300);
  }
  .el-tree-node__label {
    color: var(--seatom-type-900);
  }
  .custom-tree-node {
    display: flex;
    padding: 10px 10px 10px 5px;
    align-items: center;
    align-self: stretch;
    color: var(--seatom-type-900);
    .avatar {
      margin-left: 4px;
    }
    .icon {
      margin-right: 4px;
      i {
        margin-left: 5px;
        color: var(--seatom-type-900);
      }
      .suffix-icon {
        margin-left: 6px;
      }
    }
  }
}
</style>
