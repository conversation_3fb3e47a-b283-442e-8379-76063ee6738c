<template>
  <div class="my-resources">
  <el-container class="seatom-main-resources">
    <el-aside class="project-manage-resources">
    <div class="resources-list">
      <div v-show="mediaImgObj.isOpen" v-for="(item, index) in fileListImg" :key="'res-picture-' + index">
        <a class="link-title padding-left-5" @click="item.isOpen = !item.isOpen">
          <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !item.isOpen}"></i>
          <span :title="item.name">{{item.name}}</span>
        </a>
        <div class="folder-concent link-title padding-left-24"
        :class="[{'folder-concent-Open': item.isOpen}, {'select': it.isOpen}]"
        v-for="(it) in item.folderList"
        :key="it.id"
        @click="slelectFolder(it, 'picture')">
          <div class="folder-name" >
            <i :class="[it.isOpen ? 'el-icon-folder-opened' : 'el-icon-folder']" style="margin:0 4px 0 0;"></i>
            <span class="folder-name-max-width" :title="it.name">{{it.name}}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="resources-name">
      <router-link to="/workspace/1/resources">我的资源</router-link>
    </div>
    </el-aside>
    <section class="resources-section">
      <div class="resources-tabls" ref='myScrollbar'>
        <div class="empty-text" v-if="!dataList.length">暂无数据</div>
        <div class="resources-tabls-content" v-else>
          <span v-for="(item, index) in dataList" :key="item._id">
            <CompList>
              <CompListItem
                :title="item.name"
                :icon="imgList[index]"
                :data="{isActive: true, result: item.url, currentType: 'media-image-normal', isLazy: true}"
                draggable
                isSource
                @click="handleItemClick($event)" />
            </CompList>
          </span>
        </div>
      </div>
      <div class="resources-tabls-search">
        <div class="resources-tabls-search_div">
          <el-input v-model="values" placeholder="搜索图片" size="mini" @input="handleSearch"></el-input>
        </div>
      </div>
    </section>
  </el-container>
  <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { replaceUrl, uuid } from '@/utils/base';
import canvasBus from '@/utils/canvasBus';
import CompList from '@/components/editor/CompList';
import CompListItem from '@/components/editor/CompListItem';
import { getResourceList } from '@/api/workspace';

export default {
  name: 'ImgList',
  data () {
    return {
      loading: false,
      // 是否有超级管理权限
      isAdmin: false,
      mediaImgObj: {
        title: '全部',
        name: 'img',
        id: 'picture',
        isOpen: false,
        icon: 'icon-img'
      },
      fileListImg: [],
      // 图片信息列表
      list: [],
      dataList: [],
      // 图片预览列表
      imgList: [],
      // 上传文件夹目录
      selectFolderOptions: [],
      values: ''
    };
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  components: {
    CompList,
    CompListItem
  },
  created () {
    this.mediaImgObj.isOpen = true;
    this.getResourceListFun(false, 'picture');
  },
  methods: {
    handleSearch (val) {
      const reg = new RegExp(val)
      this.dataList = this.list.filter((item) => (reg.test(item.name)))
    },
    handleItemClick (value) {
      const mediaImg = this.$store.state.editor.compPackgesMap['media-image-normal'];
      const w = mediaImg.width;
      const h = mediaImg.height;
      const { width, height } = this.screenInfo.config;
      const x = Math.max((width - w) / 2, 0);
      const y = Math.max((height - h) / 2, 0);
      const data = {
        id: uuid(mediaImg.type),
        name: mediaImg.name,
        version: mediaImg.version,
        isActive: true,
        attr: { w, h, x, y }
      };
      if (this.platform === 'mobile') { // 移动端删除attr 创建时重新赋值
        delete data.attr;
      }
      data.result = value.result;
      canvasBus.emit('create_com', data);
    },

    async getResourceListFun (isRefresh, val) {
      this.loading = true;
      const res = await getResourceList({ resourceType: 'picture' });
      this.loading = false;
      if (res.code === 200 && res.data.length > 0) {
        const arr = this.getFileList(res.data, isRefresh);
        this.fileListImg = [arr[1], arr[0]]
        this.setFolderState(isRefresh, 'picture');
      } else {
        this.$message.error('获取资源文件为空，请联系管理员');
      }
    },
    // 刷新后 设置文件夹打开状态
    setFolderState (isRefresh, resourceType) {
      this.setSelectFolder(this.fileListImg, isRefresh, resourceType);
    },

    // 默认选中
    setSelectFolder (list, isRefresh, resourceType) {
      list.forEach(item => {
        if (item.name === '个人库') {
          item.folderList.forEach((fdr, index) => {
            if (!isRefresh) {
              if (index === 0) {
                this.slelectFolder(fdr, resourceType, isRefresh);
              }
            } else {
              if (fdr.isOpen) {
                this.slelectFolder(fdr, resourceType, isRefresh);
              }
            }
          });
        } else {
          if (isRefresh) {
            item.folderList.forEach((fdr) => {
              if (fdr.isOpen) {
                this.slelectFolder(fdr, resourceType, isRefresh);
              }
            });
          }
        }
      });
    },

    // 数据映射
    getFileList (list, isRefresh) {
      const resourceLibrary = localStorage.getItem('resourceSelectFolderLibrary');
      const folderId = localStorage.getItem('resourcesSelectFolderId');
      list.forEach(item => {
        item.id = item.name;
        if (isRefresh) {
          item.isOpen = item.name === resourceLibrary;
        } else {
          item.isOpen = item.name === '个人库';
        }
        item.folderList.forEach(folder => {
          folder.name = folder.resourceFolder;
          if (isRefresh) {
            folder.isOpen = folder.id.toString() === folderId;
          } else {
            folder.isOpen = false;
          }
          folder.isSelect = 'no';
          folder.imgList = folder.resourceList.map(it => {
            return replaceUrl(process.env.VUE_APP_SERVER_URL + it.ecryptUrl);
          });
          folder.resourceList.forEach(img => {
            img.name = img.resourceName;
            img.isSelect = false;
            img.isShow = true;
            img.url = replaceUrl(process.env.VUE_APP_SERVER_URL + img.resourceUrl);
            // 判断是否有预览缩略图
            if (img.ecryptUrl) {
              // 走图片服务器
              // if (process.env.VUE_APP_IMG_SERVER_URL) {
              //   img.ecryptUrl = process.env.VUE_APP_IMG_SERVER_URL + img.ecryptUrl + '?width=100&height=80';
              // } else {
              //   img.ecryptUrl = process.env.VUE_APP_SERVER_URL + img.ecryptUrl + '?width=100&height=80';
              // }
              img.ecryptUrl = replaceUrl(img.ecryptUrl + '?width=100&height=80');
            } else {
              img.ecryptUrl = img.url;
            }
          });
          folder.list = folder.resourceList;
        });
      });
      return list;
    },
    // 获取上传文件夹列表
    slelectFolder (item, type, isRefresh = false) {
      this.$refs.myScrollbar.scrollTop = 0;
      if (!isRefresh) {
        item.isOpen = true;
        this.fileListImg.forEach(folder => {
          folder.folderList.forEach(it => {
            if (it.id !== item.id) {
              it.isOpen = false;
            }
          });
        });
      }
      this.list = item.list;
      this.dataList = item.list;
      this.imgList = item.imgList;
      this.dataList = this.dataList.reverse();
      this.imgList = this.imgList.reverse();
      if (!isRefresh) {
        localStorage.setItem('resourcesSelectFolderId', item.id);
        localStorage.setItem('resourceSelectFolderLibrary', item.resourceLibrary);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.my-resources {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  .resources-section{
    position: relative;
    width: 100%;
    height: 100%;
    .resources-tabls {
      position: relative;
      width: 100%;
      height: calc(100% - 30px);
      background: #0a0b0e;
      overflow-y: auto;
      .resources-tabls-content{
        position: relative;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        padding-left: 8px;
        overflow-y: auto;
      }
      .empty-text{
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .resources-tabls-search{
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 30px;
      background: #0a0b0e;
      .resources-tabls-search_div{
        width: 50%;
        height: 30px;
      }
    }
  }
  .arrow-down {
    transition: transform .3s;
  }
  .arrow-rotate {
    transform: rotate(-90deg);
    transition: transform .3s;
  }
  ::v-deep .dialog-content-del-info {
    padding: 0 16px;
  }
  ::v-deep .el-button--primary.is-plain {
    background: #1b3d6187;
    border-color: #4679af;
  }
  ::v-deep .el-button {
    padding: 6px 12px;
  }
  ::v-deep .el-input__inner {
    background-color: rgba(29,38,46,.54);
    color: #fff;
    padding: 0 6px;
    border: 1px solid #2681ff;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .project-manage-resources {
    position: sticky;
    min-width: 150px;
    max-width: 150px;
    font-size: 14px;
    height: 100%;
    color: #fff;
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
    }

    // &::-webkit-scrollbar-thumb{
    //   background: #434b55;
    //   border: 1px solid #434b55;
    // }

    &-title {
      position: sticky;
      top: 0;
      background: #171b22;
      z-index: 10;
      &-header {
        display: flex;
        justify-content: space-between;
        padding-right: 30px;
        height: 60px;
        padding-left: 24px;
        align-items: center;
      }

      &-bottom {
        transition: color .2s;
        cursor: pointer;
      }
    }
    .resources-name{
      position: relative;
      width: 100%;
      height: 30px;
      text-align: left;
      line-height: 36px;
      padding-left:10px;
      > a {
        color:#3D85FF;
        text-decoration: none;
      }
    }
    .resources-list {
      position: relative;
      width: 100%;
      height: calc(100% - 30px);
      font-size: 14px;
      cursor: pointer;
      overflow-y: auto;
    .link-title {
      display: flex;
      align-items: center;
      line-height: 42px;
      padding-left: 22px;
      color: rgba(255, 255, 255, 0.7);
      transition: color 0.2s;
      text-decoration: none;
      position: relative;
      .hz-icon {
        height: 20px;
        width: 20px;
        margin-right: 4px;
      }
      &:hover {
        background: linear-gradient(0deg, #1F2430, #1F2430);
      }
      &.active {
        color: #fff;
        background: linear-gradient(0deg, #1F2430, #1F2430);
      }
      &.select {
        color: #fff;
        font-weight: bold;
        background: #3D85FF;
        box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25);
      }
    }
    .folder-concent {
      max-height: 0px;
      overflow: hidden;
      transition: max-height .3s;
      justify-content: space-between;
      cursor: pointer;
      .folder-name {
        cursor: pointer;
        margin-left: 4px;
        display: flex;
        align-items: center;
        font-size: 14px;
        width: 148px;
        .folder-name-max-width {
          max-width: 108px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .folder-concent-Open {
      max-height: 900px;
    }
    .padding-left-5 {
      padding-left: 5px;
    }
    .padding-left-24 {
      padding-left: 24px;
    }
    .margin-right-4 {
      margin-right: 4px;
    }
  }
  }
}
</style>
