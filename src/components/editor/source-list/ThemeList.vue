<template>
  <div class="components-panel">
    <div class="components-panel-wrapper">
      <div class="wrapper-right-tree">
        <div v-for="item in list['regular']" :key="item.title">
          <div>
            <div @click="nodeClick(item)" class="list-title">
              <i class="icon el-icon-arrow-right" :class="{ active: item.open }"></i>
              <span class=""><hz-icon :name="item.icon"></hz-icon>{{item.title+'('+item.child.length+')'}}</span>
            </div>
            <ul v-show="item.open" class="catalog">
              <li v-for="comp in item.child" :key="comp.id" @click="getThemeList(comp)" :class="[currentId === comp.id ? 'avtive' : null]">
                <span>
                  <i :class="[currentId === comp.id ? 'el-icon-folder-opened' : 'el-icon-folder']"></i>
                  {{comp.alias}}
                  </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="comp_panel">
        <div class="empty-text" v-if="!dataList.length">暂无数据</div>
        <div class="comp_panel_content" v-else>
          <CompList>
            <CompListItem
              v-for="comp in dataList"
              :key="comp.id"
              :title="comp.name"
              :icon="comp.icon"
              :data="{isActive: true, result: comp.config, currentType: comp.comType}"
              draggable
              isSource
              class="comp_theme"
              @click="handleItemClick(comp)" />
          </CompList>
        </div>
        <div class="search_item">
          <div class="search_item_div">
            <el-input v-model="values" placeholder="搜索主题" size="mini" @input="handleSearch"></el-input>
          </div>
        </div>
      </div>
    </div>
  <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import CompList from '@/components/editor/CompList';
import CompListItem from '@/components/editor/CompListItem';
import { mapState } from 'vuex';
import { uuid, treeToJson } from '@/utils/base';
import { compPanelList } from '@/common/constants';
import canvasBus from '@/utils/canvasBus';
import { themeList } from '@/api/theme'

export default {
  name: 'ThemeList',
  data () {
    return {
      loading: false,
      list: [],
      compArr: [],
      dataList: [],
      currentId: '',
      values: ''
    };
  },
  components: {
    CompList,
    CompListItem
  },
  mounted () {
    this.handleCompPackges();

    this.getThemeList({ type: 'regular-bar-normal' })
  },
  computed: {
    ...mapState({
      compPackges: state => state.editor.compPackges,
      screenInfo: state => state.editor.screenInfo
    }),
    filterCompPackges () { // 如果是动态面板或类似组件 则面板类组件不可用
      const blackList = [
        'interaction-container-referpanel',
        'interaction-container-dynamicpanel',
        'interaction-container-carousepanel',
        'interaction-container-popoverpanel',
        'interaction-container-mapShadowPanel',
        'interaction-container-loop-pitch',
        'interaction-container-list-pitch',
        'interaction-container-roll-pitch',
        'interaction-container-fold-panel',
        'interaction-container-statusdialogpanel'
      ]
      if (this.screenInfo.isDynamicScreen) {
        return this.compPackges.filter(item => {
          return blackList.findIndex(type => item.type === type) === -1
        })
      }
      return this.compPackges
    },
    platform () {
      return this.screenInfo.type
    }
  },
  methods: {
    handleSearch (val) {
      const reg = new RegExp(val)
      this.dataList = this.compArr.filter((item) => (reg.test(item.name)))
    },
    async getThemeList (value, item) {
      this.loading = true
      this.currentId = value.id
      const res = await themeList({ comType: value.type, themeType: 'function' });
      if (res.code === 200) {
        this.compArr = res.data;
        this.dataList = res.data;
        this.dataList = this.dataList.reverse();
        this.loading = false
      }
    },
    async handleItemClick (comp) {
      const obj = this.$store.state.editor.compPackgesMap[comp.comType];
      const w = obj.width;
      const h = obj.height;
      const { width, height } = this.screenInfo.config;
      const x = Math.max((width - w) / 2, 0);
      const y = Math.max((height - h) / 2, 0);
      const data = {
        id: uuid(obj.type),
        name: obj.name,
        version: obj.version,
        isActive: true,
        attr: { w, h, x, y }
      };
      if (this.platform === 'mobile') { // 移动端删除attr 创建时重新赋值
        delete data.attr;
      }
      data.result = this.transforData(comp.config, 'config');
      canvasBus.emit('create_com', data);
    },
    transforData (values, str) {
      const obj = {}
      treeToJson(values, str, obj)
      return obj
    },
    handleCompPackges () {
      if (compPanelList.regular[0].title === '全部') {
        compPanelList.regular.shift();
      }
      this.list = compPanelList;
      Object.values(compPanelList).forEach(listItem => {
        listItem.forEach(item => {
          item.child = []
        })
      })
      // 主题子目录
      this.filterCompPackges.forEach(com => {
        if (!this.list[com.type.split('-')[0]]) {
          this.list.other[0].child.push(com);
        } else {
          this.list[com.type.split('-')[0]][0].child.push(com);
        }
      })
      Object.values(this.list).forEach(listItem => {
        const allChild = listItem[0].child;
        listItem.slice(1).length && listItem.slice(1).forEach(item => {
          allChild.forEach(childItem => {
            if (childItem.type.split('-').slice(0, 2).join('-') === item.type) {
              item.child.push(childItem);
            }
          })
        })
      })
    },
    nodeClick (item) {
      item.open = !item.open;
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/style/mixins';
.components-panel {
  position: relative;
  width: 100%;
  height: 100%;
  transition: .3s ease;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  user-select: none;
  .components-panel-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    .comp_panel{
      position: relative;
      width: 100%;
      height: 100%;
      background: #0a0b0d;
      .comp_panel_content{
        position: relative;
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        .comp_theme{
          margin-left:10px ;
        }
      }
      .search_item{
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 30px;
        background: #0a0b0d;
        .search_item_div{
          width: 50%;
          height: 30px;
          line-height: 30px;
        }
      }
      .components-list{
        position: relative;
        display: flex;
        justify-content: flex-start;
        width: 100%;
      }
      .empty-text{
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .wrapper-right-tree {
    width: 210px;
    background-color: #22252b;
    overflow: auto;
    .list-title {
      height: 40px;
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 14px;
      padding-left: 5px;
      padding-right: 16px;
      color: #bcc9d4;
      box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.25);
      > .icon {
        transition: transform .25s linear;
        &.active {
          transform: rotate(90deg);
        }
      }
      .hz-icon {
        font-size: 20px;
        margin: 0 6px;
      }
    }
    .catalog{
      font-size: 12px;
      list-style: none;
      overflow:hidden;
      white-space:nowrap;
      text-overflow:ellipsis;
      li{
        width: 100%;
        height: 42px;
        line-height: 42px;
        font-size: 14px;
        overflow:hidden;
        white-space:nowrap;
        text-overflow:ellipsis;
        cursor: pointer;
        > span{
          margin-left: 38px;
        }
      }
      .avtive{
        background: #2681ff;
      }
    }
  }
  .wrapper-right-tree::-webkit-scrollbar {
    display: block;
    width: 4px;
  }

  .wrapper-right-tree::-webkit-scrollbar-thumb{
    background: #434b55;
    border: 1px solid #434b55;
  }

  ::v-deep .el-input__inner {
    background-color: rgba(29,38,46,.54);
    color: #fff;
    padding: 0 6px;
    border: 1px solid #2681ff;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}
.hz-icon {
  color: white;
  font-size: 18px;
}
::v-deep .is-active .hz-icon {
  color: #2681FF;
}
</style>
