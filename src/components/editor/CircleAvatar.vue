<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-11-09 17:32:25
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-11-17 11:08:23
 * @FilePath: /seatom/src/components/editor/CircleAvatar.vue
 * @Description: 圆形姓名头像
-->
<template>
  <div class="avatar-container" :style="computedAvatarStyle">
    <span>{{ avatarText }}</span>
  </div>
</template>

<script>
export default {
  name: 'CircleAvatar',
  props: {
    avatarStyle: {
      type: Object,
      default: () => {
        return {
          fontSize: 12,
          color: '#fff',
          width: 20,
          height: 20,
          bgc: '#00BBC2',
          borderRadius: 10
        }
      }
    },
    avatarText: {
      type: String,
      default: () => '-'
    }
  },
  computed: {
    computedAvatarStyle () {
      const style = {
        'font-size': `${this.avatarStyle.fontSize}px`,
        color: this.avatarStyle.color,
        width: `${this.avatarStyle.width}px`,
        height: `${this.avatarStyle.height}px`,
        'background-color': this.avatarStyle.bgc,
        'border-radius': `${this.avatarStyle.borderRadius}px`
      };

      return this.formatStyle(style)
    }
  },
  mounted () {

  },
  data () {
    return {

    }
  },
  methods: {
    formatStyle (boxStyle = {}) {
      return Object.keys(boxStyle).map(key => {
        const value = boxStyle[key];
        if (value !== undefined) {
          return key + ': ' + boxStyle[key];
        } else {
          return ''
        }
      }).join(';') + ';';
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar-container {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #fff;
    font-weight: 600;
}

</style>
