<template>
  <div class="theme-btn" v-clickoutside="close">
    <div class="--label" :class="{active: show}" @click="toggle">主题配置</div>
    <div class="drop-pop" v-show="show">
      <div class="arrow-top"></div>
      <div class="top-border"></div>
      <div class="content">
        <div class="theme-list">
          <div class="theme-item" :class="{ active: theme.id === screenInfo.themeSchemeId }" v-for="theme in themeScheme" :key="theme.id" @click="selectTheme(theme)">
            <div class="item-inner">
              <span>{{ theme.name }}</span>
              <div class="color-list">
                <div class="color-item" :style="{'background-color': item}" v-for="(item, index) in theme.colorList" :key="index"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <seatom-loading v-if="loading" append-to-body></seatom-loading>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getComtheme } from '@/api/theme';
import { flatten } from '@/lib/Flat';
import emitter from '@/utils/bus';
export default {
  name: 'GolbalTheme', // 全局主题
  data () {
    return {
      show: false,
      colorList: [
        { color: 'rgba(225, 95, 14, 1)' },
        { color: 'rgba(255, 200, 0, 1)' },
        { color: 'rgba(245, 220, 146, 1)' },
        { color: 'rgba(255, 255, 0, 1)' }
      ],
      loading: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      loaded: state => state.editor.loaded,
      themeScheme: state => state.editor.themeScheme,
      screenComs: state => state.editor.screenComs
    })
  },
  methods: {
    toggle () {
      this.show = !this.show;
      if (this.show) {
        this.$store.dispatch('editor/getThemeScheme')
      }
    },
    close () {
      if (this.show) {
        this.show = false;
      }
    },
    async selectTheme (theme) {
      this.loading = true;
      const themeId = (this.screenInfo.themeSchemeId === theme.id) ? '' : theme.id
      const params = {
        screenId: this.screenInfo.id,
        themeSchemeId: themeId
      }
      const res = await getComtheme(params);
      if (res && res.success) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'themeSchemeId', value: themeId }]);
        if (!themeId) {
          this.loading = false;
          return
        }
        const data = res.data;
        const keys = Object.keys(data);
        const toString = Object.prototype.toString;
        if (keys.length) {
          const params = [];
          keys.forEach(key => {
            const com = this.screenComs[key];
            const themeObj = data[key];
            if (themeObj && com) {
              const keyObj = flatten({ ...themeObj });
              let result = _.map(keyObj, (val, key) => {
                return { key: key, value: val }
              })
              // 去掉不兼容的属性 （根据值的类型判断）
              result = result.filter(item => {
                const targetVal = item.value;
                const sourceVal = _.get(com, item.key);
                return toString.call(targetVal) === toString.call(sourceVal)
              })
              params.push({
                id: key,
                keyValPairs: result
              })
            }
          })
          if (params.length) {
            this.$store.dispatch('editor/updateScreenCom', params).then(() => {
              emitter.emit('updateConfigKey');
            })
          }
        }
      }
      this.loading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
  .theme-btn {
    position: relative;
    display: flex;
    align-items: center;
    color: #ddd;
    margin-left: 15px;
  }
  .--label {
    cursor: pointer;
    margin-right: 5px;
    &:hover, &.active {
      color: #2681ff;
      border-bottom: 1px solid #2681ff;
    }
  }
  .drop-pop {
    position: absolute;
    top: 35px;
    left: 0;
    width: 228px;
    height: 306px;
    background: #1F2430;
    box-shadow: 0px 8px 24px -6px rgba(4, 8, 16, 0.56), 0px 1px 3px rgba(4, 8, 16, 0.3), 0px 0px 1px rgba(4, 8, 16, 0.32);
    .arrow-top {
      position: absolute;
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-bottom-color: #3F4F73;
      left: 18px;
      top: -16px;
    }
    .top-border {
      height: 0px;
      background: #3F4F73;
    }
    .content {
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: 0;
      overflow: hidden;
      .theme-list {
        flex: 1;
        overflow: auto;
        overflow-x: hidden;
        .theme-item {
          height: 42px;
          line-height: 42px;
          padding: 0 15px;
          cursor: pointer;
          &:hover {
            background-color: #293246;
            .item-inner {
              span {
                color: #fff;
              }
            }
          }
          &.active {
            background-color: #293246;
            .item-inner {
              span {
                color: #fff;
              }
            }
          }
          .item-inner {
            display: flex;
            align-items: center;
            // border-bottom: 1px solid rgba(204, 219, 255, 0.1);
            span {
              display: inline-block;
              width: 80px;
              font-size: 12px;
              font-family: '思源黑体Medium';
              color: rgba(230, 235, 255, 0.9);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .color-list {
              display: flex;
              flex: 1;
              margin-left: 10px;
              overflow: hidden;
              .color-item {
                height: 14px;
                width: 26px;
              }
            }
          }
        }
      }
    }
  }
</style>
