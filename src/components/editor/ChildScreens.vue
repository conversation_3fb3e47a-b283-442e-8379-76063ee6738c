<template>
  <div class="child-screens-wrapper"
    v-loading="loading"
    element-loading-text="正在删除"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)">
    <div class="create">
      <el-button
        type="primary"
        size="small"
        style="width: 100%"
        @click.stop="createScreen"
        >新建子屏</el-button
      >
      <ul class="screen-list" v-if="childScreens.length">
        <li v-for="item in childScreens" class="screen" :key="item.value">
          <span class="label">{{ item.name }}</span>
          <div class="tools">
            <span @click.stop="editScreen(item)">
              <i class="el-icon-edit"></i>
            </span>
            <span class="ml-8" @click.stop="deleteScreen(item)">
              <i class="el-icon-delete"></i>
            </span>
          </div>
        </li>
      </ul>
      <div class="no-data" v-else>
        <img src="../../assets/img/no-data.png" alt="" />
        <p class="no-data-text">
          暂无子屏，请去<span class="link" @click.stop="createScreen"
            >新建子屏</span
          >
        </p>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="visible"
      width="400px"
      top="0"
      append-to-body>
      <div class="name-item">
        <span class="label">子屏名称：</span>
        <el-input size="small" :class="{ 'input-error': invalid }" v-model="childScreenName" v-focus></el-input>
      </div>
      <p class="ml-80 text-red" v-if="invalid">子屏名称不能为空！</p>
      <span slot="footer" class="dialog-footer">
        <el-button type="light-blue" @click="createChildScreen" size="small">确定</el-button>
        <el-button type="text" @click="cancel" size="small">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { createScreen, deleteScreen } from '@/api/screen'
// import { replaceUrl, randomStr } from '@/utils/base'
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'ChildScreens',
  data () {
    return {
      visible: false,
      childScreenName: '',
      invalid: false,
      loading: false
    };
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      childScreens: state => state.editor.childScreens
    }),
    ...mapGetters('editor', ['currentCom']),
    platform () {
      return this.screenInfo.type
    },
    computedWidth () {
      return this.screenInfo.config.width
    },
    computedHeight () {
      return this.screenInfo.config.height
    },
    childScreensList () {
      return this.childScreens.map(item => {
        return _.pick(['name', 'id'], item)
      })
    }
  },
  methods: {
    ...mapActions('editor', ['getChildScreens']),
    createScreen () {
      this.visible = true
    },
    editScreen (item) {
      const path = this.$router.resolve({
        path: `/screen/edit/${item.id}`,
        query: {
          screenId: this.screenInfo.id
        }
      })
      window.open(path.href, `screen_${item.id || this.screenInfo.id}`)
    },
    deleteScreen (item) {
      const text = `${item.name} 删除后无法恢复，确认删除？`
      this.$confirm(text, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme',
        closeOnClickModal: false
      }).then(async () => {
        this.loading = true
        const res = await deleteScreen({ id: item.id });
        if (res.success) {
          await this.getChildScreens()
          this.loading = false
          this.$message.success('删除成功！')
        }
      }).catch(() => {})
    },
    createChildScreen () {
      if (!this.childScreenName) {
        this.invalid = true
        return false
      }
      this.invalid = false
      this.visible = false
      this.handleCreate()
    },
    async handleCreate () {
      const screen = {
        name: this.childScreenName,
        projectId: this.screenInfo.projectId,
        workspaceId: this.screenInfo.workspaceId,
        level: 0,
        type: this.platform,
        screenType: 'child',
        templateId: 1,
        config: {
          height: this.computedHeight,
          width: this.computedWidth,
          scaleType: 'no_scale',
          backgroundImage: ''
        },
        // isDynamicScreen: true,
        // relationCompId: this.currentCom.id,
        parentId: this.screenInfo.id
      }
      const res = await createScreen(screen)
      if (res && res.success) {
        await this.getChildScreens()
        const id = res.data.id
        const path = this.$router.resolve({
          path: `/screen/edit/${id}`,
          query: {
            screenId: this.screenInfo.id
          }
        })
        window.open(path.href, `screen_${id || this.screenInfo.id}`)
      }
    },
    cancel () {
      this.invalid = false
      this.visible = false
    }
  }
};
</script>
<style lang="scss">
  .input-error .el-input__inner {
    border-color: #F56C6C;
  }
</style>
<style lang="scss" scoped>
.ml-8 {
  margin-left: 8px;
}
.ml-80 {
  margin-left: 80px;
}
.text-red {
  color: #F56C6C;
}
.name-item {
  display: flex;
  align-items: center;
  .label {
    width: 100px;
    color: var(--control-text-color);
  }
}
.child-screens-wrapper {
  width: 232px;
  background: #1f2430;
  box-shadow: 0px 48px 128px -16px rgba(4, 8, 16, 0.64),
    0px 16px 64px -16px rgba(4, 8, 16, 0.72), 0px 0px 1px rgba(4, 8, 16, 0.32);
  border-radius: 8px;
  max-height: 288px;
  padding: 16px;
  overflow: auto;
  ::v-deep .is-horizontal {
    display: none;
  }
}
.screen-list {
  margin-top: 16px;
  .screen {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 -6px;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    .label {
    }
    .tools {
      display: none;
    }
    &:hover {
      background: rgba(204, 219, 255, 0.1);
      .tools {
        display: block;
      }
    }
  }
}
.no-data {
  text-align: center;
  font-size: 14px;
  &-text {
    color: rgba(255, 255, 255, 0.5);
    .link {
      color: #3d85ff;
      text-decoration-line: underline;
      margin-left: 5px;
      cursor: pointer;
    }
  }
}
</style>
