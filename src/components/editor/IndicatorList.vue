<template>
  <div class="indicators-panel">
    <div class="panel-title">
      <div class="title-left">
        <span class="panel-text"> 指标库 </span>
        <hz-icon name="plus" style="margin-right: 8px;" class="create-icon" @click="jumpTo"></hz-icon>
      </div>
      <i
        class="seatom-icon datav-font icon-back close-btn"
        @click.stop="
          $store.commit('editor/updateEditPanelSelect', {
            type: 'tool',
            value: false,
          })
        "
      ></i>
    </div>
    <div class="indicators-panel-wrapper">
      <div :style="{ width: '100%' }">
        <IndicatorItem
          v-for="(el, index) in list"
          :key="index"
          :info="el"
          @click="handleItemClick(el)"
        ></IndicatorItem>
      </div>
    </div>
    <div class="indicators-search">
      <el-input
        placeholder="搜索"
        v-model="searchKey"
        size="mini"
        class="my-project project-input"
        @change="searchIndicators"
      >
      </el-input>
    </div>
    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>
<script>
import IndicatorItem from './IndicatorItem.vue';
import { getIndicators } from '@/api/workspace';
import { mapState } from 'vuex';
export default {
  data () {
    return {
      list: [],
      searchKey: '',
      loading: false
    };
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  components: {
    IndicatorItem
  },
  created () {
    this.getIndicators();
  },
  methods: {
    searchIndicators (val) {
      this.list.forEach((e) => {
        e.isShow = e.name.includes(val);
      });
    },
    handleDragStart (e) {
      // console.log(e, "event$$$$");
    },
    handleItemClick (data) {
      // console.log(data, "点击创建指标组件");
    },
    async getIndicators () {
      this.loading = true;
      const result = await getIndicators();
      if (result.success) {
        result.data.forEach((el) => {
          this.list.push({
            name: el.name,
            ecryptUrl: el.config.backgroundImage,
            backgroundColor: el.config.backgroundColor,
            componentId: el.componentId,
            id: el.id,
            isShow: true
          });
        });
      }
      this.loading = false;
    },
    jumpTo () {
      localStorage.setItem('resourcesActiveItemId', 'indicator')
      this.$router.push({
        name: 'workspace/resources',
        params: {
          workspaceId: this.screenInfo.workspaceId
        }
      })
    }
  }
};
</script>
<style lang="scss" scoped>
.indicators-panel {
  position: relative;
  width: 300px;
  transition: 0.3s ease;
  height: 100%;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  user-select: none;
  .panel-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    background: var(--seatom-panel-title-bg);
    color: #bcc9d4;
    padding: 10px 16px;
    user-select: none;
    .title-left {
      .panel-text {
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
      }
      .create-icon {
        cursor: pointer;
      }
    }
    .close-btn {
      cursor: pointer;
      transition: color 0.2s;
    }
  }
}
.indicators-panel-wrapper {
  height: calc(100% - 60px);
  overflow-y: auto;
  background-color: #0a0b0d;
  padding: 8px;
  div {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    row-gap: 8px;
    justify-content: flex-start;
    span {
      .indicator-list {
        background: #0a0b0d;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 5px;
        width: 100px;
        height: 80px;
        .indicator-list-item {
          width: 100%;
          cursor: pointer;
          display: inline-block;
          color: #bcc9d4;
          user-select: none;
          flex: none;
          margin-bottom: 6px;
          border-radius: 3px;
          overflow: hidden;
          .indicator-item-img {
            width: 100%;
            height: 58px;
            background: cornflowerblue;
          }
          .indicator-item-title {
            font-size: 12px;
            padding: 0 5px;
            text-align: left;
            overflow: hidden;
            background: #212326;
            white-space: nowrap;
            text-overflow: ellipsis;
            line-height: 22px;
            width: 100%;
          }
        }
      }
    }
  }
}
.indicators-search {
  display: flex;
  position: absolute;
  bottom: 0;
  width: 100%;
}
</style>
