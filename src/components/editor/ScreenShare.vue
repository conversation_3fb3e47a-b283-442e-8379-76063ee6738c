<template>
  <div class="screen-share-wrapper element-ui-override">
    <el-dialog
      title="请选择同步对象"
      :visible.sync="dialogVisible"
      destroy-on-close
      width="428px"
      top="0"
      :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    >
      <share-list @change="onChange"></share-list>
      <span slot="footer" class="dialog-footer">
        <el-button class="poper-cancel" size="mini" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" size="mini" @click="shareScreen()">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { screenShare } from '@/api/screen';
import ShareList from './ShareList.vue';
export default {
  components: { ShareList },
  name: 'ScreenShare',
  comments: {
    ShareList: () => import('./ShareList')
  },
  data () {
    return {
      dialogVisible: false,
      list: []
    };
  },
  computed: {
    userId () {
      return this.$store.state.user.id;
    },
    userName () {
      return this.$store.state.user.name;
    },
    screenInfo () {
      return this.$store.state.editor.screenInfo;
    },
    filterUserList () {
      return this.showChecked ? this.allUserList.filter(item => item.select) : this.userList;
    }
  },
  methods: {
    showDialog (screen) {
      if (screen) this.$store.commit('editor/initScreenInfo', Object.assign({}, this.screenInfo, screen));
      this.dialogVisible = true;
    },
    onChange (list = [], type) {
      let userList = this.list.filter(user => user.type === 'user')
      let roleList = this.list.filter(user => user.type === 'role')
      let groupList = this.list.filter(user => user.type === 'group')
      if (type === 'user') {
        userList = list.map(item => {
          item.type = type
          return item
        })
      } else if (type === 'role') {
        roleList = list.map(item => {
          item.type = type
          return item
        })
      } else if (type === 'group') {
        groupList = list.map(item => {
          item.type = type
          return item
        })
      } else {
        this.list = []
        return
      }
      this.list = userList.concat(roleList).concat(groupList)
    },
    shareScreen () {
      const data = _.cloneDeep(this.list);
      this.list = []
      const paramsBody = {
        userList: [],
        roleList: [],
        groupList: []
      }
      paramsBody.userList = data.filter(item => item.type === 'user')?.map(s => {
        return {
          userId: this.userId,
          userName: this.userName,
          screenId: this.screenInfo.id,
          screenName: this.screenInfo.name,
          shareUserId: s.userId
        }
      })
      paramsBody.roleList = data.filter(item => item.type === 'role')?.map(s => {
        return {
          userId: this.userId,
          userName: this.userName,
          screenId: this.screenInfo.id,
          screenName: this.screenInfo.name,
          role_id: s.role_id
        }
      })
      paramsBody.groupList = data.filter(item => item.type === 'group')?.map(s => {
        return {
          userId: this.userId,
          userName: this.userName,
          screenId: this.screenInfo.id,
          screenName: this.screenInfo.name,
          group_id: s.group_id
        }
      })
      if (!paramsBody.userList.length && !paramsBody.roleList.length && !paramsBody.groupList.length) {
        this.$message.warn('请选择同步对象！')
        return false
      }
      screenShare(paramsBody).then(res => {
        this.dialogVisible = false;
        this.$message.success('同步成功！')
      }).catch(err => {
        console.error(err);
      });
    }
    // 防抖
    // debounce(fn, wait) {
    //   let timeout = null;
    //   let vm = this;
    //   return function() {
    //     if(timeout !== null)   clearTimeout(timeout);
    //     timeout = setTimeout(function() {
    //       fn.apply(vm, arguments);
    //     }, wait);
    //   }
    // },
  }
};
</script>
