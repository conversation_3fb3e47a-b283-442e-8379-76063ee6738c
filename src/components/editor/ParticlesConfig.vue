<template>
  <div class="page-setting">
    <div class="page-title">
      <span class="icon el-icon-back" @click="$emit('closeParticlesConfig')"/>
      <span class="title">粒子效果</span>
    </div>
    <div class="page-content">
      <el-form label-width="90px" label-position="left" size="mini">
        <el-form-item label="粒子密度" v-if="numberVisible">
          <el-input-number
            class="full-w"
            :max="800"
            :step="10"
            v-model="particlesStyleChange.number"
          />
        </el-form-item>
        <el-form-item label="粒子颜色" class="label-group">
          <div class="group-wrapper">
            <div
              class="input-row"
              v-for="(item,index) in particlesStyleChange.color"
              :key="index"
            >
              <el-color-picker v-model="item.value"/>
              <el-input class="item2" v-model="item.value"/>
              <el-button type="primary" size="mini" @click="removeColor(index)">
                <span class="icon el-icon-delete"/>
              </el-button>
            </div>
          </div>
          <el-button type="primary" size="mini" @click="addColor">
            <span class="icon el-icon-plus"/>
          </el-button>
        </el-form-item>
        <el-form-item label="移动速率">
          <el-slider
            v-model="speed"
            class="width-90"
            range
            :min="1"
            :max="100"
            :step="1"
            :marks="speedMarks"
            @change="changeSpeed"
          />
        </el-form-item>
        <el-form-item label="粒子尺寸">
          <el-slider
            v-model="size"
            class="width-90"
            range
            :min="1"
            :max="100"
            :step="1"
            :marks="speedMarks"
            @change="changeSize"
          />
        </el-form-item>
        <el-form-item label="粒子透明度">
          <el-slider
            v-model="opacity"
            class="width-90"
            range
            :min="0"
            :max="1"
            :step="0.05"
            :marks="opacityMarks"
            @change="changeOpacity"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'ParticlesConfig',
  props: {
    customStyle: {
      type: Object,
      default: () => ({}),
      required: true
    },
    particlesType: {
      type: String,
      default: '',
      required: true
    }
  },
  data () {
    return {
      isCustom: false,
      particlesStyleChange: {
        size: [1, 1],
        speed: [1, 1],
        opacity: [0.5, 0.5],
        number: 100,
        color: [
          { value: '#ffffff' }
        ]
      },
      speed: [],
      size: [],
      opacity: [],
      speedMarks: {
        1: '最小值',
        100: '最大值'
      },
      opacityMarks: {
        0: '最小值',
        1: '最大值'
      }
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    numberVisible () {
      return ['STYLE_NONE', 'STYLE_GROWING', 'STYLE_METEOR'].indexOf(this.screenInfo.config.backgroundParticlesType) < 0
    }
  },
  watch: {
    particlesStyleChange: {
      handler (val) {
        this.$emit('update:customStyle', _.cloneDeep(val))
      },
      deep: true
    }
  },
  created () {
    const temp = _.cloneDeep(this.customStyle)
    this.particlesStyleChange = temp
    this.size = temp.size
    this.opacity = temp.opacity
    this.speed = temp.speed
  },
  methods: {
    addColor () {
      this.particlesStyleChange.color.push({ value: '#ffffff' })
    },
    removeColor (index) {
      this.particlesStyleChange.color.splice(index, 1)
    },
    changeSpeed (val) {
      this.particlesStyleChange.speed = val
    },
    changeSize (val) {
      this.particlesStyleChange.size = val
    },
    changeOpacity (val) {
      this.particlesStyleChange.opacity = val
    }
  }
}
</script>

<style lang="scss" scoped>
.page-setting {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
  .page-title {
    position: relative;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    span.icon {
      float: left;
      font-size: 16px;
      margin: 12px;
      cursor: pointer;
    }
    span.title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .page-content {
    padding: 20px 16px;
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
      color: var(--control-text-color);
    }
    .el-radio__label {
      font-size: 12px;
      color: #bfbfbf;
    }
    .el-upload-dragger {
      width: 200px;
      height: 90px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
      }
      .uploaded-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        object-fit: contain;
      }
    }
  }
  .single-line ::v-deep .el-radio {
    display: block;
    line-height: 26px;
  }
  .full-w {
    width: 100%;
  }
  .content-wrap {
    padding-top: 15px;
  }
  .input-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item {
      width: 100px;
    }
    .item1 {
      width: 100px;
      margin-left: 10px
    }
    .item2 {
      flex: 1;
      margin-left: 10px;
    }
  }
  .radio-group {
    .el-radio {
      margin-right: 20px;
    }
  }
  .scale-radio ::v-deep {
    .el-radio-button--mini .el-radio-button__inner {
      padding: 7px 12px;
    }
    .el-radio-button:first-child .el-radio-button__inner,
    .el-radio-button:last-child .el-radio-button__inner {
      border-radius: 0;
    }
  }
}

.width-90 {
  width: 90%;
}

.label-group {
  ::v-deep .el-form-item__content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .group-wrapper {
      width: 80%;
      .input-row {
        margin-top: 8px;
        &:first-of-type {
          margin-top: 0;
        }
      }
    }
    .el-button {
      cursor: pointer;
      width: 28px;
      height: 28px;
      margin-left: calc(20% - 28px);
      display: flex;
      justify-content: center;
      align-items: center;
      .icon {
        font-size: 16px;
        color: #ffffff;
      }
    }
  }
}
::v-deep {
  .el-slider__marks {
    &-text {
      width: 3em;
    }
  }
}
</style>
