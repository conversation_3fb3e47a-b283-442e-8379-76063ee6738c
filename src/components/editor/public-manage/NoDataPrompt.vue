<template>
  <div class="data-prompt config-control">
    <!-- <el-button @click="showBatchNoData" size="medium" class="data-prompt-all-button el-button--light-blue">批量编辑</el-button> -->
    <div class="data-prompt-content">
      <el-collapse  class="pt-4">
        <template v-for="item in allTips">
          <el-collapse-item :key="item.id" :title="alias(item)">
            <div class="content-block">
              <div class="config-control">
                <div class="config-title nowrap">
                  <el-checkbox v-model="item.tips.open" size="mini" class="mr5" @change="tipsChange('open', item)"></el-checkbox>
                  开启
                </div>
              </div>
            </div>
            <div class="config-control">
              <template v-if="item.tips.open">
                <el-tabs v-model="activeTab" type="card" size="mini" editable class="tab-b w100" @edit="(targetName, action) => handleTipsEdit(targetName, action, item)">
                  <el-tab-pane :name="i + ''" :label="'条件' + (i + 1)" v-for="(it, i) in item.tips.conditions" :key="i">
                    <div class="config-control">
                      <div class="config-title nowrap">
                        匹配类型：
                      </div>
                      <div class="width-div">
                        <el-select v-model="it.type" size="mini" class="w100" @change="tipsChange('conditions', item)">
                          <el-option label="数据为空" value="empty"></el-option>
                          <el-option label="自定义条件" value="diy"></el-option>
                        </el-select>
                      </div>
                    </div>
                    <template v-if="it.type == 'diy'">
                      <div class="config-control">
                        <div class="config-title nowrap">
                          匹配字段
                          <el-tooltip content="匹配数据第一项">
                            <span class="el-icon-info"></span>
                          </el-tooltip>
                        </div>
                        <div class="width-div">
                          <el-select v-model="it.field" size="mini" class="w100" @change="tipsChange('conditions', item)">
                            <el-option v-for="opt in fieldOptions(item)" :key="opt" :value="opt" :label="opt"></el-option>
                          </el-select>
                        </div>
                      </div>
                      <div class="config-control">
                        <div class="config-title nowrap">
                          满足条件：
                        </div>
                        <div class="width-div flex-row">
                          <el-select v-model="it.condition" size="mini" style="width:140px;flex:none;margin-right:5px;" @change="tipsChange('conditions', item)">
                            <el-option label="等于" value="=="></el-option>
                            <el-option label="大于" value=">"></el-option>
                            <el-option label="小于" value="<"></el-option>
                            <el-option label="大于等于" value=">="></el-option>
                            <el-option label="小于等于" value="<="></el-option>
                          </el-select>
                          <el-input v-model="it.value" size="mini" @change="tipsChange('conditions', item)"></el-input>
                        </div>
                      </div>
                    </template>
                    <div class="config-control">
                      <div class="config-title nowrap">
                        提示信息：
                      </div>
                      <div class="width-div">
                        <el-input v-model="it.info" size="mini" placeholder="请输入提示信息" @change="tipsChange('conditions', item)"></el-input>
                      </div>
                    </div>
                    <div class="config-control">
                      <ConfigTree
                        :treeData="controlConfig"
                        :configObj="it"
                        @change="({path, value}) => handleConfigTreeChange(path, value, item, i)"
                       />
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </template>
            </div>
          </el-collapse-item>
        </template>
      </el-collapse>
    </div>
    <!-- 批量更新无数据 -->
    <BatchNoDataPrompt @updateTips="updateTips" :allTips="allTips" ref="batchNoData" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getData } from '@/api/datastorage'
import { getFilters } from '@/api/filter'
import BatchNoDataPrompt from '../public-manage/BatchNoDataPrompt'
import dataUtil from '@/utils/data'
const controlConfig = {
  font: {
    name: '字体',
    type: 'font',
    default: {
      fontSize: 14,
      color: '#FFFFFF',
      fontFamily: 'Microsoft YaHei',
      fontWeight: 'bold'
    }
  },
  background: {
    name: '背景',
    type: 'backgroundGroup',
    default: {
      show: false,
      type: 'image',
      pure: 'rgba(9,61,101,0.3)',
      gradient: {
        type: 'linear-gradient',
        deg: 0,
        start: '#0000ff',
        startScale: 0,
        end: '#ffc0cb',
        endScale: 100,
        shape: 'ellipse'
      },
      image: {
        url: '',
        size: '100% 100%',
        positionX: 'center',
        positionY: 'center',
        repeat: 'no-repeat'
      }
    }
  }
}
export default {
  name: 'NoDataPrompt',
  components: {
    BatchNoDataPrompt
  },
  props: {
    allComs: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      allTips: [],
      activeTab: '0',
      otherScreenComsData: {}, // 其他大屏组件的数据
      otherScreenFilters: {}, // 其他大屏的过滤器信息
      controlConfig
    }
  },
  computed: {
    ...mapState({
      comsData: state => state.editor.comsData,
      screenComs: state => state.editor.screenComs,
      screenInfo: state => state.editor.screenInfo,
      screenFilters: state => state.editor.screenFilters
    }),
    alias () {
      return (item) => {
        return (item.pAlias ? `${item.pAlias}/` : '') + item.alias
      }
    },
    fieldOptions () { // 字段映射集合
      return function (item) {
        let result = []
        try {
          const keys = []
          let filter = []
          const filters = item.filters
          if (!filters.enable) {
            filter = []
          } else {
            filter = _.filter(filters.list, { enable: true }).map(({ id }) => this.allScreenFilters[id])
          }
          const sourceData = _.cloneDeep(this.allComsData[item.id]);
          const filterData = dataUtil.filterData(sourceData || [], filter);
          filterData.forEach(item => {
            const key = Object.keys(item)
            keys.push(...key)
          })
          const uniKeys = Array.from(new Set(keys))
          result = uniKeys.map((key) => {
            return key
          })
        } catch (e) {}
        return result;
      }
    },
    allComsData () {
      return { ...this.comsData, ...this.otherScreenComsData }
    },
    allScreenFilters () {
      return { ...this.screenFilters, ...this.otherScreenFilters }
    },
    isCurrScreenCom () { // 是否是当前大屏的组件
      return function (id) {
        return !!this.screenComs[id]
      }
    }
  },
  async created () {
    this.allTips = _.cloneDeep(this.allComs).filter(coms => coms.type === 'com').map(item => {
      return {
        fieldMapping: item.dataConfig.fieldMapping,
        filters: item.dataConfig.dataResponse.filters,
        sourceType: item.dataConfig.dataResponse.sourceType,
        tips: item.dataConfig.dataResponse.tips,
        alias: item.alias,
        pAlias: item.pAlias,
        id: item.id,
        screenId: item.screenId
      }
    })
    await this.getOtherScreenComData();
    await this.getOtherScreenFilters();
    this.allTips.forEach(item => {
      Object.assign(item, {
        fieldOption: this.handleFieldOptions(item) || []
      })
    })
  },
  methods: {
    showBatchNoData () {
      this.$refs.batchNoData.showBatchNoData();
    },
    tipsChange (type, item) {
      this.$store.dispatch('editor/updateScreenCom', {
        id: item.id,
        sid: item.screenId,
        keyValPairs: [
          { key: `dataConfig.dataResponse.tips.${type}`, value: item.tips[type] }
        ]
      })
    },
    handleFieldOptions (item) {
      let result = []
      try {
        const keys = []
        let filter = []
        const filters = item.filters
        if (!filters.enable) {
          filter = []
        } else {
          filter = _.filter(filters.list, { enable: true }).map(({ id }) => this.allScreenFilters[id])
        }
        const sourceData = _.cloneDeep(this.allComsData[item.id]);
        const filterData = dataUtil.filterData(sourceData || [], filter);
        filterData.forEach(item => {
          const key = Object.keys(item)
          keys.push(...key)
        })
        const uniKeys = Array.from(new Set(keys))
        result = uniKeys.map(key => {
          return key
        })
      } catch (e) {}
      return result;
    },
    handleConfigTreeChange (path, value, item, i) {
      _.set(item.tips.conditions[i], path, value);
      this.$store.dispatch('editor/updateScreenCom', {
        id: item.id,
        sid: item.screenId,
        keyValPairs: [
          { key: 'dataConfig.dataResponse.tips', value: _.cloneDeep(item.tips) }
        ]
      })
    },
    async getOtherScreenComData () { // 获取其它大屏组件 static/csv/api/mysql数据
      return new Promise((resolve, reject) => {
        this.allTips.forEach(async item => {
          const isCurrScreenCom = this.isCurrScreenCom(item.id);
          if (!isCurrScreenCom) {
            try {
              const res = await getData({
                componentId: item.id,
                type: item.sourceType,
                workspaceId: this.screenInfo.workspaceId
              })
              if (res && res.success) {
                const data = res.data || []
                this.$set(this.otherScreenComsData, item.id, data);
                resolve()
              }
            } catch (e) {
              reject(e)
            }
          } else {
            resolve()
          }
        })
      })
    },
    getOtherScreenFilters () { // 获取其他大屏的过滤器
      return new Promise((resolve, reject) => {
        this.allTips.forEach(async item => {
          const isCurrScreenCom = this.isCurrScreenCom(item.id);
          if (!isCurrScreenCom) {
            if (!this.otherScreenFilters[item.screenId]) {
              try {
                const res = await getFilters({ screenId: item.screenId })
                if (res && res.success) {
                  const data = res.data || []
                  const screenFilters = _.keyBy(data, 'id')
                  this.$set(this.otherScreenFilters, item.screenId, screenFilters);
                  resolve()
                }
              } catch (e) {
                reject(e)
              }
            }
          } else {
            resolve()
          }
        })
      })
    },
    handleTipsEdit (targetName, action, item) {
      const { conditions } = item.tips
      if (action === 'add') {
        conditions.push({
          type: 'empty',
          field: '',
          condition: '==',
          value: '0',
          info: '暂无数据',
          background: {
            show: true,
            type: 'pure',
            pure: '#fff',
            gradient: {
              type: 'linear-gradient',
              deg: 0,
              start: '#0000ff',
              startScale: 0,
              end: '#ffc0cb',
              endScale: 100,
              shape: 'ellipse'
            },
            image: {
              url: '',
              size: '100% 100%',
              positionX: 'center',
              positionY: 'center',
              repeat: 'no-repeat'
            }
          },
          font: {
            fontSize: 14,
            color: '#999',
            fontFamily: '系统自带字体',
            fontWeight: 'bold'
          }
        })
        this.activeTab = (conditions.length - 1) + '';
      }
      if (action === 'remove') {
        if (conditions.length <= 1) {
          this.$message.warn('请至少保留一项');
          return
        }
        if (conditions[+targetName]) {
          conditions.splice(+targetName, 1);
          if (+targetName > 0) {
            this.activeTab = (+targetName - 1) + '';
          } else {
            this.activeTab = '0'
          }
        }
      }
      this.$store.dispatch('editor/updateScreenCom', {
        id: item.id,
        sid: item.screenId,
        keyValPairs: [
          { key: 'dataConfig.dataResponse.tips', value: _.cloneDeep(item.tips) }
        ]
      })
    },
    updateTips (data) {
      this.allTips = data;
    }
  }
}
</script>

<style lang="scss" scoped>
.data-prompt {
  height: 100%;
  // padding: 16px;
  display: flex;
  flex-direction: column;

  &-content {
    width: 100%;
    height: calc(100% - 8px);
    overflow: auto;

    .content-block {
      ::v-deep {
        .config-control {
          padding: 4px 0;
          margin-bottom: 10px;
          .config-title {
            &.width-auto {
              width: auto;
            }
            width: 80px;
          }
        }
        .eidtor-wrap {
          margin-bottom: 10px;
        }
        .flex-row {
          display: flex;
          justify-content: space-between;
          .ml10 {
            margin-left: 10px;
          }
          .table-name {
            flex: none;
            width: 110px;
            line-height: 28px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .field-box {
          width: 100%;
          min-height: 85px;
          background: #0c0b0b;
          padding-bottom: 8px;
          border-radius: 8px;
          .field-div {
            &.add-field {
              & i {
                color:#2681ff;
              }
              color:#2681ff;
              border-color:#2681ff;
            }
            display: inline-block;
            cursor: pointer;
            margin-left: 8px;
            margin-top: 8px;
            padding: 0px 8px;
            background: #191D25;
            line-height: 22px;
            border-radius: 16px;
            border: 1px solid rgba(76, 82, 95, 0.32);
            ::v-deep {
              .el-input__inner {
                height: 22px;
                border: none;
                padding-right: 20px;
                background: none;
              }
              .el-input__icon {
                width: 13px;
                height: 22px;
                line-height: 22px;
                cursor: pointer;
              }
              .el-select {
                max-width: 105px;
              }
            }
          }
        }
      }
    }

    .flex-row {
      display: flex;
      justify-content: space-between;
      .ml10 {
        margin-left: 10px;
      }
      .table-name {
        flex: none;
        width: 110px;
        line-height: 28px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  &-all-button {
    align-self: flex-end;
    margin-bottom: 8px;
  }

  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 8px 0;
    }
  }
  .w100 {
    width: 100%;
  }
  .pb10 {
    padding-bottom: 10px;
  }
  .tips {
    padding-left: 8px;
    font-size: 12px;
    color: #999;
    margin-bottom: 10px;
  }
  .tab-b {
    // width: 287px;
    ::v-deep {
      .el-tabs__nav-next,
      .el-tabs__nav-prev {
        line-height: 30px;
      }
      .el-tabs__item {
        height: 30px;
        line-height: 30px;
        font-size: 12px;
      }
      .el-tabs__new-tab {
        line-height: 16px;
        margin: 5px 0 8px 10px;
      }
      .el-tabs__header {
        min-height: 30px;
        border-bottom: 1px solid #393b4a;
      }
      .el-tabs__nav-wrap {
        justify-content: unset;
      }
    }
  }
}
.config-control {
  position: unset;
}
.pt-4 {
  padding-top: 4px;
}
</style>
