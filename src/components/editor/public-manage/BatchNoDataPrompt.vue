<template>
  <div class="batch-no-data" :class="{'hide': !show}">
    <div class="page-title">
      <span class="icon el-icon-back" @click="show = false"></span>
      <span class="title">批量设置无数据提示</span>
    </div>
    <div class="batch-no-data-content">
      <el-steps direction="vertical" :key="'stepkey'">
        <el-step title="配置基准组件">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">基准组件</div>
            <div class="width-div">
              <el-select v-model="selectId" size="mini" class="w100" @change="selectChange">
                <el-option
                  v-for="opt in list"
                  :key="opt.id"
                  :value="opt.id"
                  :label="alias(opt)">
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
        </el-step>
        <el-step title="配置无数据提示信息">
          <div class="content-block" slot="description">
            <div class="content-block">
              <div class="config-control">
                <div class="config-title nowrap">
                  <el-checkbox v-model="tips.open" size="mini" class="mr5"></el-checkbox>
                  开启
                </div>
              </div>
            </div>
            <div class="config-control">
              <template v-if="tips.open">
                <el-tabs v-model="activeTab" type="card" size="mini" editable class="tab-b w100" @edit="handleTipsEdit">
                  <el-tab-pane :name="i + ''" :label="'条件' + (i + 1)" v-for="(it, i) in tips.conditions" :key="i">
                    <div class="config-control">
                      <div class="config-title nowrap">
                        匹配类型：
                      </div>
                      <div class="width-div">
                        <el-select v-model="it.type" size="mini" class="w100">
                          <el-option label="数据为空" value="empty"></el-option>
                          <el-option label="自定义条件" value="diy"></el-option>
                        </el-select>
                      </div>
                    </div>
                    <template v-if="it.type == 'diy'">
                      <div class="config-control">
                        <div class="config-title nowrap">
                          匹配字段
                          <el-tooltip content="匹配数据第一项">
                            <span class="el-icon-info"></span>
                          </el-tooltip>
                        </div>
                        <div class="width-div">
                          <el-select v-model="it.field" size="mini" class="w100" >
                            <el-option v-for="opt in fieldOption" :key="opt" :value="opt" :label="opt"></el-option>
                          </el-select>
                        </div>
                      </div>
                      <div class="config-control">
                        <div class="config-title nowrap">
                          满足条件：
                        </div>
                        <div class="width-div flex-row">
                          <el-select v-model="it.condition" size="mini" style="width:140px;flex:none;margin-right:5px;">
                            <el-option label="等于" value="=="></el-option>
                            <el-option label="大于" value=">"></el-option>
                            <el-option label="小于" value="<"></el-option>
                            <el-option label="大于等于" value=">="></el-option>
                            <el-option label="小于等于" value="<="></el-option>
                          </el-select>
                          <el-input v-model="it.value" size="mini"></el-input>
                        </div>
                      </div>
                    </template>
                    <div class="config-control">
                      <div class="config-title nowrap">
                        提示信息：
                      </div>
                      <div class="width-div">
                        <el-input v-model="it.info" size="mini" placeholder="请输入提示信息"></el-input>
                      </div>
                    </div>
                    <div class="config-control">
                      <ConfigTree
                        :treeData="controlConfig"
                        :configObj="it"
                        @change="({path, value}) => handleConfigTreeChange(path, value, tips, i)"
                       />
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </template>
            </div>
          </div>
        </el-step>
        <el-step title="选择组件">
          <div class="content-block" slot="description">
            <ul class="item-content" v-if="accordList.length">
              <li class="batch-update-item" @click.prevent="handleCheckAllChange">
                <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"></el-checkbox>
                <span class="list-content">全选</span>
              </li>
              <template v-for="item in accordList">
                <li class="batch-update-item" @click.prevent="select(item)" :key="item.id">
                  <el-checkbox v-model="item.select"></el-checkbox>
                  <span class="list-content" :title="alias(item)">{{alias(item)}}</span>
                </li>
              </template>
            </ul>
            <div class="no-data"  v-if="!accordList.length">
              暂无匹配组件
            </div>
          </div>
        </el-step>
      </el-steps>
      <el-button v-if="checkAccordList.length && !!selectId" class="ok-button" @click="batchSubmit" type="primary" size="mini">提 交</el-button>
    </div>
  </div>
</template>

<script>
const controlConfig = {
  font: {
    name: '字体',
    type: 'font',
    default: {
      fontSize: 14,
      color: '#FFFFFF',
      fontFamily: 'Microsoft YaHei',
      fontWeight: 'bold'
    }
  },
  background: {
    name: '背景',
    type: 'backgroundGroup',
    default: {
      show: false,
      type: 'image',
      pure: 'rgba(9,61,101,0.3)',
      gradient: {
        type: 'linear-gradient',
        deg: 0,
        start: '#0000ff',
        startScale: 0,
        end: '#ffc0cb',
        endScale: 100,
        shape: 'ellipse'
      },
      image: {
        url: '',
        size: '100% 100%',
        positionX: 'center',
        positionY: 'center',
        repeat: 'no-repeat'
      }
    }
  }
}
export default {
  name: 'BatchNoDataPrompt',
  props: {
    allTips: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      show: false,
      checkAll: false,
      isIndeterminate: false,
      activeTab: '0',
      fieldOption: [],
      tips: {
        open: false,
        conditions: []
      },
      list: [],
      selectId: '',
      controlConfig
    }
  },
  computed: {
    alias () {
      return (item) => {
        return (item.pAlias ? `${item.pAlias}/` : '') + item.alias
      }
    },
    selectField () {
      return this.list.find(item => item.id === this.selectId)?.fieldOption || []
    },
    accordList () {
      return this.list.filter(item => {
        return !!item?.fieldOption?.length
      })
    },
    checkAccordList () {
      return this.accordList.filter(item => item.select)
    }
  },
  created () {
  },
  methods: {
    showBatchNoData () {
      this.handlerAllTips();
      this.show = true
    },
    handlerAllTips () {
      this.checkAll = false;
      this.isIndeterminate = false;
      this.selectId = '';
      this.tips = {
        open: false,
        conditions: []
      }
      this.list = _.cloneDeep(this.allTips).map(item => {
        return {
          ...item,
          select: false
        }
      })
    },
    handleCheckAllChange () {
      this.checkAll = !this.checkAll
      this.isIndeterminate = false;
      this.list.forEach(item => {
        item.select = this.checkAll
      })
      this.isIndeterminate = false;
    },
    handleConfigTreeChange (path, value, tips, i) {
      _.set(tips.conditions[i], path, value);
    },
    handleTipsEdit (targetName, action) {
      const { conditions } = this.tips
      if (action === 'add') {
        conditions.push({
          type: 'empty',
          field: '',
          condition: '==',
          value: '0',
          info: '暂无数据',
          background: {
            show: true,
            type: 'pure',
            pure: '#fff',
            gradient: {
              type: 'linear-gradient',
              deg: 0,
              start: '#0000ff',
              startScale: 0,
              end: '#ffc0cb',
              endScale: 100,
              shape: 'ellipse'
            },
            image: {
              url: '',
              size: '100% 100%',
              positionX: 'center',
              positionY: 'center',
              repeat: 'no-repeat'
            }
          },
          font: {
            fontSize: 14,
            color: '#999',
            fontFamily: '系统自带字体',
            fontWeight: 'bold'
          }
        })
        this.activeTab = (conditions.length - 1) + '';
      }
      if (action === 'remove') {
        if (conditions.length <= 1) {
          this.$message.warn('请至少保留一项');
          return
        }
        if (conditions[+targetName]) {
          conditions.splice(+targetName, 1);
          if (+targetName > 0) {
            this.activeTab = (+targetName - 1) + '';
          } else {
            this.activeTab = '0'
          }
        }
      }
    },
    select (item) {
      item && (item.select = !item.select);
      const checkCount = this.checkAccordList.length
      this.checkAll = checkCount === this.accordList.length
      this.isIndeterminate = checkCount > 0 && checkCount < this.accordList.length;
    },
    selectChange () {
      const selectItem = this.list.find(item => {
        return item.id === this.selectId
      })
      this.tips = selectItem.tips
      this.fieldOption = selectItem.fieldOption
      this.checkAll = false;
      this.isIndeterminate = false;
      this.list.forEach(item => {
        item.select = false
      })
    },
    batchSubmit () {
      const obj = {};
      const params = {
        screenId: this.$route.params.screenId
      };
      const checkAccordListIds = this.checkAccordList.map(item => item.id)
      for (const item of this.checkAccordList) {
        if (obj[item.screenId]) {
          obj[item.screenId].push(item)
        } else {
          obj[item.screenId] = [item]
        }
      }
      const promises = Object.keys(obj).map(item => {
        return obj[item].map(n => {
          return {
            id: n.id,
            sid: n.screenId,
            keyValPairs: [
              { key: 'dataConfig.dataResponse.tips', value: _.cloneDeep(this.tips) }
            ]
          }
        })
      }).map(ary => {
        return this.$store.dispatch('editor/updateScreenCom', ary)
      })
      Promise.all(promises).then(() => {
        this.$message.success('批量更新成功');
        this.$emit('updateTips', this.list.map(item => {
          if (checkAccordListIds.includes(item.id)) {
            Object.assign(item, { tips: this.tips })
          }
          return item
        }))
        this.$store.dispatch('editor/initScreenAction', params)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.batch-no-data {
  position: absolute;
  width: 510px;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 90;
  background: #1c1f25;
  box-shadow: -1px 0 #000;
  transition: transform 0.25s linear;
  overflow: hidden;
  &.hide {
    transform: translateX(-510px);
  }
  .page-title {
    position: relative;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    span.icon {
      float: left;
      font-size: 16px;
      margin: 12px;
      cursor: pointer;
    }
    span.title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .batch-no-data-content {
    padding: 16px 4px;
    // padding-left: 4px;
    height: calc(100% - 40px);
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .content-block {
    .config-control {
      padding: 4px 0 4px 8px;
      margin-bottom: 10px;
      .config-title {
        width: 100px;
      }
    }
    .eidtor-wrap {
      margin-bottom: 10px;
    }
    .flex-row {
      display: flex;
      justify-content: space-between;
      .ml10 {
        margin-left: 10px;
      }
      .table-name {
        flex: none;
        width: 110px;
        line-height: 28px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .field-box {
      width: 100%;
      min-height: 85px;
      background: #0c0b0b;
      padding-bottom: 8px;
      border-radius: 8px;
      .field-div {
        &.add-field {
          & i {
            color:#2681ff;
          }
          color:#2681ff;
          border-color:#2681ff;
        }
        display: inline-block;
        cursor: pointer;
        margin-left: 8px;
        margin-top: 8px;
        padding: 0px 8px;
        background: #191D25;
        line-height: 22px;
        border-radius: 16px;
        border: 1px solid rgba(76, 82, 95, 0.32);
        ::v-deep {
          .el-input__inner {
            height: 22px;
            border: none;
            padding-right: 20px;
            background: none;
          }
          .el-input__icon {
            width: 13px;
            height: 22px;
            line-height: 22px;
            cursor: pointer;
          }
          .el-select {
            max-width: 105px;
          }
        }
      }
    }
  }
  .item-content {
    padding-left: 16px;
    max-height: 570px;
    overflow: auto;
    // height: 100px;
    // overflow: auto;
  }

  .batch-update-item {
    color: #fff;
    list-style: none;
    padding: 10px 8px;
    cursor: pointer;
    span.list-content {
      margin-left: 8px;
    }
  }
  .no-data {
    display: flex;
    align-content: center;
    padding: 16px;
  }
}
::v-deep {
    .el-steps--vertical {
      height: unset;
    }
    .el-step {
      .el-step__description {
        padding-right: 10px;
        margin-top: 0;
      }
      &.is-vertical .el-step__main {
        padding-left: 8px;
      }
      .el-step__icon {
        width: 18px;
        height: 18px;
        font-size: 12px;
        background-color: #2681ff;
      }
      .el-step__icon.is-text {
        border: none;
      }
      .el-step__icon-inner {
        font-weight: 400;
      }
      &.is-vertical .el-step__line {
        width: 2px;
        top: 0;
        bottom: 0;
        left: 8px;
        background-color: #2e343c;
      }
      &.is-vertical .el-step__title {
        font-size: 14px;
        line-height: 18px;
      }
      &.is-vertical .el-step__head {
        width: 18px;
      }
      &.error .el-step__icon {
        background: #F56C6C;
      }
    }
    .tab-b {
    // width: 287px;
      .el-tabs__nav-next,
      .el-tabs__nav-prev {
        line-height: 30px;
      }
      .el-tabs__item {
        height: 30px;
        line-height: 30px;
        font-size: 12px;
      }
      .el-tabs__new-tab {
        line-height: 16px;
        margin: 5px 0 8px 10px;
      }
      .el-tabs__header {
        min-height: 30px;
        border-bottom: 1px solid #393b4a;
      }
      .el-tabs__nav-wrap {
        justify-content: unset !important;
      }
    }
    .ok-button {
      align-self: center;
    }
  }
.config-control {
  position: unset;
}
.pt-4 {
  padding-top: 4px;
}
.w100 {
  width: 100%;
}
.pb10 {
  padding-bottom: 10px;
}
.tips {
  padding-left: 8px;
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}
</style>
