<template>
  <div class="batch-auto-update" :class="{'hide': !show}">
    <div class="page-title">
      <span class="icon el-icon-back" @click="show = false"></span>
      <span class="title">批量设置更新</span>
    </div>
    <div class="batch-update-content">
      <p class="content-title">选择组件</p>
      <ul class="item-content">
        <li class="batch-update-item" @click.prevent="handleCheckAllChange">
           <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"></el-checkbox>
           <span class="list-content">全选</span>
        </li>
        <template v-for="item in list">
          <li class="batch-update-item" @click.prevent="select(item)" :key="item.id">
            <el-checkbox v-model="item.select"></el-checkbox>
            <span class="list-content" :title="alias(item)">{{alias(item)}}</span>
          </li>
        </template>
      </ul>
      <p class="content-title">设置更新</p>
      <div class="config-control">
        <div class="config-title nowrap">
          <el-checkbox v-model="autoUpdate.enable" size="mini" class="mr5"></el-checkbox>自动更新
        </div>
        <div class="width-div" v-if="autoUpdate.enable">
          每
          <el-input-number v-model="autoUpdate.time" class="mlr5" controls-position="right" size="mini" :min="1"></el-input-number>
          秒请求一次
        </div>
      </div>
      <el-button v-if="checkList.length" class="ok-button" @click="batchSubmit" type="primary" size="mini">提 交</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BatchAutoUpdate',
  props: {
    allSource: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      show: false,
      checkAll: false,
      isIndeterminate: false,
      list: [],
      autoUpdate: {
        enable: false,
        time: 600
      }
      // originalList: []
    }
  },
  computed: {
    alias () {
      return (item) => {
        return (item.pAlias ? `${item.pAlias}/` : '') + item.alias
      }
    },
    checkList () {
      return this.list.filter(item => item.select)
    }
  },
  methods: {
    showBatchUpdate () {
      this.handlerAllSource();
      this.show = true
    },
    select (item) {
      item && (item.select = !item.select);
      const checkCount = this.checkList.length
      this.checkAll = checkCount === this.list.length
      this.isIndeterminate = checkCount > 0 && checkCount < this.list.length;
    },
    handlerAllSource () {
      this.checkAll = false;
      this.isIndeterminate = false;
      this.list = _.cloneDeep(this.allSource).map(item => {
        return {
          ...item,
          select: false
        }
      })
    },
    handleCheckAllChange () {
      this.checkAll = !this.checkAll
      this.list.forEach(item => {
        item.select = this.checkAll
      })
      this.isIndeterminate = false;
    },
    batchSubmit () {
      const obj = {};
      const params = {
        screenId: this.$route.params.screenId
      };
      const checkListIds = this.checkList.map(item => item.id)
      for (const item of this.checkList) {
        if (obj[item.screenId]) {
          obj[item.screenId].push(item)
        } else {
          obj[item.screenId] = [item]
        }
      }
      const promises = Object.keys(obj).map(item => {
        return obj[item].map(n => {
          return {
            id: n.id,
            sid: n.screenId,
            keyValPairs: [
              { key: 'dataConfig.dataResponse.autoUpdate', value: _.cloneDeep(this.autoUpdate) }
            ]
          }
        })
      }).map(ary => {
        return this.$store.dispatch('editor/updateScreenCom', ary)
      })
      Promise.all(promises).then(() => {
        this.$message.success('批量更新成功');
        this.$emit('updateSource', this.list.map(item => {
          if (checkListIds.includes(item.id)) {
            Object.assign(item, { autoUpdate: this.autoUpdate })
          }
          return item
        }))
        this.$store.dispatch('editor/initScreenAction', params)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.batch-auto-update {
  position: absolute;
  width: 510px;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 90;
  background: #1c1f25;
  box-shadow: -1px 0 #000;
  transition: transform 0.25s linear;
  overflow: hidden;
  &.hide {
    transform: translateX(-510px);
  }
  .page-title {
    position: relative;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    span.icon {
      float: left;
      font-size: 16px;
      margin: 12px;
      cursor: pointer;
    }
    span.title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .batch-update-content {
    height: calc(100% - 40px);
    overflow: auto;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    padding: 16px 32px;

    .content-title {
      color: #FFFFFF;
      font-size: 18px;
    }

    .item-content {
      padding-left: 16px;
      max-height: 80%;
      overflow: auto;
    }

    .batch-update-item {
      color: #fff;
      list-style: none;
      padding: 10px 8px;
      cursor: pointer;
      span.list-content {
        margin-left: 8px;
      }
    }

    .config-control {
      padding-left: 16px;
      margin-bottom: 8px;
    }

    .ok-button {
      align-self: center;
    }
  }
  .mr5 {
    margin-right: 5px;
  }
  .mlr5 {
    margin: 0 5px;
  }
}
</style>
