<template>
  <div class="auto-update">
    <!-- <el-button @click="showBatchUpdate" size="medium" class="auto-update-all-button el-button--light-blue">批量编辑</el-button> -->
    <div class="auto-update-content">
      <el-table :data="allSource" height="100%">
        <el-table-column label="组件名称" width="153" class="content-item">
          <template slot-scope="scope">
            <span class="com-name" :title="alias(scope.row)">{{alias(scope.row)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否自动更新" width="137">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.autoUpdate.enable" @change="autoUpdateChange('enable', scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="每请求1次时间(s)" width="172">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.autoUpdate.time" @change="autoUpdateChange('time', scope.row)" class="mlr5" controls-position="right" size="mini" :min="1"></el-input-number>
          </template>
        </el-table-column>
      </el-table>
      <!-- <template v-for="item in allSource">
        <div class="content-item" :key="item.id">
          <span class="com-name" :title="alias(item)">{{alias(item)}}</span>
          <div class="config-control">
            <div class="config-title nowrap">
              <el-switch v-model="item.autoUpdate.enable" @change="autoUpdateChange('enable', item)" />
              <span class="update-text">自动更新</span>
            </div>
            <div class="width-div" v-if="item.autoUpdate.enable">
              每
              <el-input-number v-model="item.autoUpdate.time" @change="autoUpdateChange('time', item)" class="mlr5" controls-position="right" size="mini" :min="1"></el-input-number>
              秒请求一次
            </div>
          </div>
        </div>
      </template> -->
    </div>
    <!-- 批量编辑 -->
    <BatchAutoUpdate @updateSource="updateSource" :allSource="allSource" ref="batchUpdate" />
  </div>
</template>

<script>
import BatchAutoUpdate from '../public-manage/BatchAutoUpdate'
export default {
  name: 'AutoUpdate',
  components: {
    BatchAutoUpdate
  },
  props: {
    allComs: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      allSource: []
    }
  },
  computed: {
    alias () {
      return (item) => {
        return (item.pAlias ? `${item.pAlias}/` : '') + item.alias
      }
    }
  },
  created () {
    this.allSource = _.cloneDeep(this.allComs).map(item => {
      return {
        autoUpdate: item.dataConfig.dataResponse.autoUpdate,
        alias: item.alias,
        pAlias: item.pAlias,
        id: item.id,
        screenId: item.screenId
      }
    })
  },
  methods: {
    showBatchUpdate () {
      this.$refs.batchUpdate.showBatchUpdate();
    },
    autoUpdateChange (type, item) {
      const params = {
        screenId: this.$route.params.screenId
      };
      this.$store.dispatch('editor/updateScreenCom', {
        id: item.id,
        sid: item.screenId,
        keyValPairs: [
          { key: `dataConfig.dataResponse.autoUpdate.${type}`, value: item.autoUpdate[type] }
        ]
      }).then(() => {
        this.$store.dispatch('editor/initScreenAction', params)
      })
    },
    updateSource (data) {
      this.allSource = data;
    }
  }
}
</script>

<style lang="scss" scoped>
.auto-update {
  height: 100%;
  padding-top: 8px;
  display: flex;
  flex-direction: column;

  &-content {
    height: calc(100% - 8px);
    overflow: auto;

    // .content-item {
    //   font-size: 14px;
    //   color: rgba(255, 255, 255, 0.7);
    //   display: flex;
    //   align-items: center;
    //   margin-bottom: 8px;
    //   line-height: 28px;
    //   height: 28px;

      .com-name {
        width: 118px;
        overflow: hidden;
        // font-weight: 600;
        white-space: nowrap;
        text-overflow:ellipsis;
        text-align: right;
        margin-right: 16px;
        color: #ffffff;
      }

      .config-control .config-title {
        width: 110px;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 400;
      }

      .update-text {
        font-size: 14px;
        margin-left: 8px;
      }

      .config-control {
        width: 327px;
      }
    // }

    .el-input-number {
      width: 98px;
    }
  }

  &-all-button {
    align-self: flex-end;
    margin-bottom: 8px;
  }

}
.mr5 {
  margin-right: 5px;
}
.mlr5 {
  margin: 0 5px;
}

</style>
