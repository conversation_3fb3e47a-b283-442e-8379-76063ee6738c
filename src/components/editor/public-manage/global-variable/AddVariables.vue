<template>
  <el-dialog
    :visible.sync="show"
    :title="title"
    append-to-body
    top="0"
    width="550px"
    @close="closeDialog">
    <div class="dialog-content">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" size="mini">
        <el-form-item prop="name" label="变量名">
          <el-input v-model="form.name" placeholder="请输入变量名称"></el-input>
        </el-form-item>
        <el-form-item prop="content" label="变量内容">
          <div class="func-tit">
            <span>function (<span>callbackArgs</span>) {</span>
          </div>
          <el-input v-model="form.content" type="textarea" :rows="4" resize="none" placeholder="可以为固定值，也可以为表达式，可复用内置变量、回调变量"></el-input>
          <div class="func-tit">
            <span>}</span>
          </div>
        </el-form-item>
        <el-form-item prop="type" label="类型">
          <el-select v-model="form.type" placeholder="请选择变量类型">
            <el-option :label="opt.label" :value="opt.value" v-for="opt in dataTypeOpt" :key="opt.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="description" label="描述">
          <el-input v-model="form.description" type="textarea" :rows="4" resize="none" placeholder="请输入描述信息"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="plain" size="mini" @click="submit">确定</el-button>
      <el-button type="text" size="mini" @click="closeDialog">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addVariable, updateVariable } from '@/api/screen';
import { mapState } from 'vuex';
const dataTypeOpt = [
  { label: 'string', value: 'string' },
  { label: 'number', value: 'number' },
  { label: 'object', value: 'object' }
]
export default {
  name: 'AddVariables', // 添加自定义变量
  components: {},
  data () {
    return {
      show: false,
      title: '',
      form: {
        name: '',
        content: '',
        type: '',
        description: ''
      },
      rules: {
        name: [
          { required: true, message: '变量名必填', trigger: 'change' }
        ],
        type: [
          { required: true, message: '类型必填', trigger: 'change' }
        ]
      },
      dataTypeOpt
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  methods: {
    showDialog (data) {
      if (data) {
        this.form = Object.assign({}, data);
        this.title = '修改自定义变量';
      } else {
        this.form._id = '';
        this.title = '添加自定义变量';
      }
      this.show = true;
    },
    closeDialog () {
      this.show = false;
    },
    submit () {
      // 获取所有表单值
      this.$refs.form.validate(valid => {
        if (valid) {
          const form = Object.assign({}, this.form);
          const params = {
            screenId: this.screenInfo.id
          }
          if (!this.form._id) {
            delete form._id;
            addVariable(form, params).then(res => {
              if (res.success) {
                this.$emit('update');
                this.$message.success('添加成功');
                this.closeDialog();
                this.resetForm();
              }
            })
          } else {
            params.variableId = this.form._id;
            updateVariable(form, params).then(res => {
              if (res.success) {
                this.$emit('update');
                this.$message.success('修改成功');
                this.closeDialog();
                this.resetForm();
              }
            })
          }
        }
      })
    },
    resetForm () {
      this.$refs.form.resetFields();
    }
  }
}
</script>

<style lang="scss" scoped>
.func-tit {
  color: #aeaeae;
  font-size: 12px;
}
</style>
