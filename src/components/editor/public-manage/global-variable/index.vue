<template>
  <div class="global-variable">
    <div class="global-var-block">
      <div class="var-title">内置变量</div>
      <div class="var-content">
        <el-table :data="innerVars" size="mini">
          <el-table-column prop="name" label="变量名" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="类型" width="80"></el-table-column>
          <el-table-column prop="description" label="描述"></el-table-column>
          <el-table-column prop="used" label="使用组件" width="100" align="center"></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="global-var-block">
      <div class="var-title">
        自定义变量
        <el-tooltip placement="right" content="自定义变量可使用globalData.xxx访问到">
          <i class="el-icon-info"></i>
        </el-tooltip>
        <span class="icon el-icon-plus" @click="addVariables"></span>
      </div>
      <div class="var-content">
        <el-table :data="customVars" size="mini">
          <el-table-column prop="name" label="变量名" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="类型" width="80"></el-table-column>
          <el-table-column prop="description" label="描述"></el-table-column>
          <el-table-column prop="used" label="使用组件" width="100" align="center">
            <template slot-scope="scope">
              <div class="row-item-used">
                <div class="txt">{{ scope.row.used }}</div>
                <div class="btn">
                  <el-button type="text" size="mini" @click.stop="modifyVariables(scope.row)">修改</el-button>
                  <el-button type="text" size="mini" @click.stop="deleteVariable(scope.row)">删除</el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="global-var-block">
      <div class="var-title">回调变量</div>
      <div class="var-content">
        <el-table :data="callbackVars" size="mini">
          <el-table-column prop="name" label="变量名" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="类型" width="80"></el-table-column>
          <el-table-column prop="source" label="抛出组件"></el-table-column>
          <el-table-column prop="used" label="使用组件" width="100" align="center"></el-table-column>
        </el-table>
      </div>
    </div>
    <AddVariables ref="add" @update="getCustomVariables" />
  </div>
</template>

<script>
import { getVariables, delVariables } from '@/api/screen';
import { mapState } from 'vuex';
export default {
  name: 'globalVariable', // 全局变量
  components: {
    AddVariables: () => import('./AddVariables')
  },
  data () {
    return {
      innerVars: [
        { name: 'route', type: 'object', description: '浏览器的参数，通过route.参数名获取，如url中有city=100，可以通过route.city获取到', used: '--' },
        { name: 'loginUser', type: 'object', description: '用户信息，通过loginUser.参数名获取，如loginUser.userid、loginUser.username', used: '--' }
      ],
      customVars: [],
      callbackVars: []
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      screenComs: state => state.editor.screenComs
    })
  },
  created () {
    this.getInnerVariables();
    this.getCallbackVars();
    this.getCustomVariables();
  },
  methods: {
    addVariables () {
      this.$refs.add.showDialog();
    },
    modifyVariables (data) {
      this.$refs.add.showDialog(data);
    },
    async deleteVariable (data) {
      this.$alert('确认删除该自定义变量？', { type: 'warning' }).then(async () => {
        const params = {
          screenId: this.screenInfo.id,
          variableId: data._id
        }
        const res = await delVariables(params);
        if (res.success) {
          this.getCustomVariables();
        }
      }).catch(() => {})
    },
    async getInnerVariables () { // 获取内置变量
    },
    getCallbackVars () { // 获取所有的回调参数
      const comMap = _.pickBy(this.screenComs, item => item.interactionConfig.callbackParams.length > 0);
      const coms = Object.values(comMap);
      const arr = [];
      coms.forEach(item => {
        const { callbackParams } = item.interactionConfig;
        callbackParams.forEach(callbackParam => {
          const obj = {
            name: callbackParam.variableName,
            type: callbackParam.type || 'string',
            source: item.alias,
            used: '--'
          }
          arr.push(obj);
        })
      })
      this.callbackVars = arr;
    },
    async getCustomVariables () { // 获取自定义变量
      const params = {
        screenId: this.screenInfo.id
      }
      const res = await getVariables(params);
      if (res.success) {
        const variableList = res.data.variableList;
        this.customVars = variableList.map(item => {
          return {
            ...item,
            used: '--'
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.global-variable {
  height: 100%;
  overflow: auto;
  padding-right: 10px;
  .global-var-block {
    margin-bottom: 15px;
    .var-title {
      font-size: 12px;
      color: #fff;
      .icon {
        float: right;
        margin-top: 2px;
        font-size: 18px;
        cursor: pointer;
      }
    }
    .var-content {
      margin-top: 10px;
    }
  }

  .row-item-used {
    position: relative;
    min-height: 20px;
    &:hover {
      .txt {
        opacity: 0.3;
      }
      .btn {
        display: block;
      }
    }
    .btn {
      display: none;
      position: absolute;
      width: 100px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
