<template>
  <el-dialog :visible.sync="show" append-to-body top="0" @close="closeDialog" width="500px" height="668" class="refer-component">
    <div slot="title" class="dialog-title">
      <span v-if="state === 0">{{ title }}</span>
      <div class="title-wrapper" v-if="state === 1 || state === 2 || state === 3">
        <hz-icon name="back" class="back" @click="toPrev()"></hz-icon>
        <span v-if="state === 1 || state === 2">新增组件</span>
        <span v-if="state === 3">设置</span>
      </div>
    </div>
    <div class="dialog-inner-wrapper">
      <div class="step-wrapper" v-if="state === 1 || state === 2">
        <div class="step-item" :class="{ active: state === 1 }">
          <div class="index" v-if="state === 1">1</div>
          <hz-icon class="icon-svg" name="finished-big" v-if="state === 2"></hz-icon>
          <div class="step-txt">选择组件</div>
        </div>
        <div class="step-line" :class="{ active: state === 2 }"></div>
        <div class="step-item" :class="{ active: state === 2 }">
          <div class="index">2</div>
          <div class="step-txt">配置数据</div>
        </div>
      </div>
      <!-- 应用组件列表 -->
      <template v-if="state === 0">
        <div class="top-header">
          <span>{{ containerName }}</span>
          <el-button type="primary" size="mini" @click="addCom()">新增组件</el-button>
        </div>
        <div class="refer-list">
          <div class="refer-item" v-for="item in list" :key="item.id">
            <div class="refer-icon">
              <img :src="item.icon">
            </div>
            <div class="refer-name">{{ item.alias }}</div>
            <div class="refer-btn">
              <hz-icon name="setting" class="btn" @click="setting(item)"></hz-icon>
              <hz-icon name="trash" class="btn" @click="deleteCom(item)"></hz-icon>
            </div>
          </div>
          <EmptyBlock
            v-if="list.length === 0"
            emptyText="暂无组件，请去"
            showButton
            buttonText="新增组件"
            style="margin-top: 100px;"
            @addClick="addCom()" />
        </div>
      </template>
      <!-- 新增组件 -->
      <template v-else-if="state === 1">
        <div class="refer-list" style="margin: 0;">
          <div class="refer-item" v-for="item in comList" :key="item.id">
            <el-checkbox v-model="item.check"></el-checkbox>
            <div class="refer-icon ml20">
              <img :src="item.icon">
            </div>
            <div class="refer-name">{{ item.alias }}</div>
          </div>
          <EmptyBlock
            v-if="comList.length === 0"
            emptyText="暂无数据"
            style="margin-top: 100px;" />
        </div>
      </template>
      <!-- 配置映射 -->
      <template v-else>
        <div class="refer-list" style="margin: 0;">
          <div class="field-item" v-for="item in fieldList" :key="item.id">
            <div class="com-name">{{ item.alias }}</div>
            <div class="field-table">
              <el-table class="mapping-t" :data="item.dataConfig.fieldMapping" size="mini">
                <el-table-column label="字段" prop="source" width="80">
                </el-table-column>
                <el-table-column label="映射" prop="target" align="center" width="140">
                  <template slot-scope="scope">
                    <el-select
                      size="mini"
                      v-model="scope.row.target"
                      clearable
                      :title="scope.row.target">
                      <el-option v-for="opt in fieldOptions" :key="opt.id" :value="opt.name" :label="opt.name"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="说明" prop="description"></el-table-column>
                <el-table-column label="状态" align="center" width="60">
                  <template slot-scope="scope">
                    <span v-if="isMatch(scope.row) === 0">未匹配</span>
                    <span v-if="isMatch(scope.row) === 1" class="el-icon-check t-icon green"></span>
                    <span v-if="isMatch(scope.row) === 2" class="el-icon-close t-icon red"></span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div slot="footer">
      <template v-if="state === 0">
        <el-button type="plain" size="mini" @click="closeDialog">取消</el-button>
      </template>
      <template v-if="state === 1">
        <el-button type="plain" size="mini" @click="toNext">下一步</el-button>
        <el-button type="plain" size="mini" @click="closeDialog">取消</el-button>
      </template>
      <template v-if="state === 2">
        <el-button type="plain" size="mini" @click="toPrev">上一步</el-button>
        <el-button type="plain" size="mini" @click="submit" :loading="loading">完成</el-button>
      </template>
      <template v-if="state === 3">
        <el-button type="plain" size="mini" @click="submit" :loading="loading">确定</el-button>
        <el-button type="plain" size="mini" @click="toPrev">取消</el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import EmptyBlock from './EmptyBlock'
import { mapState, mapGetters } from 'vuex'
import dataUtil from '@/utils/data'
export default {
  name: 'ReferComponent',
  inject: ['getLayerTree', 'callbackManager'],
  components: {
    EmptyBlock
  },
  data () {
    return {
      show: false,
      state: 0, // 状态：0.组件列表 1.新增组件 2.多组件字段映射 3.单组件字段映射
      title: '应用',
      containerName: '',
      containerId: '',
      list: [], // 引用的组件
      comList: [], // 待引用的组件
      fieldList: [], // 组件映射列表
      loading: false
    }
  },
  computed: {
    ...mapState({
      screenComs: state => state.editor.screenComs,
      screenInfo: state => state.editor.screenInfo
    }),
    ...mapGetters('datacontainer', ['getContainerDataById']),
    layerTree () {
      return this.getLayerTree()
    },
    fieldOptions () { // 字段映射集合
      let result = []
      const keys = []
      try {
        const sourceData = this.getContainerDataById(this.containerId)
        const filterData = dataUtil.filterData(sourceData.data, [])

        filterData.forEach(item => {
          const key = Object.keys(item)
          keys.push(...key)
        })
        const uniKeys = Array.from(new Set(keys))
        result = uniKeys.map((key, idx) => {
          return {
            id: idx,
            name: key
          }
        })
      } catch (e) {
        console.warn(e)
      }
      return result
    },
    isMatch () { // 匹配状态 0：未匹配 1：匹配成功 2：匹配失败
      return function (item) {
        if (item.target === '' || item.target === undefined) {
          return 0
        }
        return item.target &&
        this.fieldOptions.findIndex(opt => opt.name === item.target) > -1 ? 1 : 2
      }
    }
  },
  methods: {
    showDialog (item) {
      this.show = true;
      this.containerName = item.alias;
      this.containerId = item.id;
      this.getReferComs();
    },
    closeDialog () {
      this.show = false;
      this.state = 0;
    },
    addCom () {
      this.state = 1;
      this.getOtherComs();
    },
    getReferComs () { // 获取引用该数据容器的组件
      const coms = Object.values(this.screenComs);
      this.list = coms.filter(item => {
        const dataResponse = item.dataConfig.dataResponse;
        return dataResponse.sourceType === 'datacontainer' && dataResponse.source.datacontainer.data.dataContainerComId === this.containerId
      })
    },
    getOtherComs () { // 获取未使用该数据容器的组件
      const coms = Object.values(this.screenComs);
      const list = coms.filter(item => {
        const dataResponse = item.dataConfig.dataResponse;
        const node = this.layerTree.getNodeById(item.id);
        return !!node && dataResponse.sourceType !== 'datacontainer' && item.comName !== 'interaction-container-datacontainer'
      })
      this.comList = _.cloneDeep(list).map(item => ({ ...item, check: false }))
    },
    toPrev () {
      if (this.state === 3) {
        this.state = 0;
        return
      }
      this.state--;
    },
    toNext () {
      if (this.state === 1) {
        const checkList = this.comList.filter(item => item.check);
        if (!checkList.length) {
          this.$message.warn('请选择组件！')
          return
        }
        this.fieldList = _.cloneDeep(checkList);
        this.state++;
      }
    },
    setting (item) {
      this.fieldList = _.cloneDeep([item]);
      this.state = 3
    },
    deleteCom (item) {
      this.$alert('确认删除组件对该数据源的应用？', { title: '提示', type: 'warning' }).then(() => {
        this.loading = true;
        this.$store.dispatch('editor/updateScreenCom', {
          id: item.id,
          keyValPairs: [
            { key: 'dataConfig.dataResponse.sourceType', value: 'static' }
          ]
        }).then(() => {
          this.getReferComs();
          this.$store.dispatch('editor/getCompData', {
            componentId: item.id,
            workspaceId: this.screenInfo.workspaceId,
            callbackManager: this.callbackManager
          });
        }).finally(() => {
          this.loading = false;
        })
      }).catch(() => {})
    },
    submit () {
      const params = this.fieldList.map(item => {
        const id = item.id;
        const fieldMapping = item.dataConfig.fieldMapping;
        return {
          id,
          keyValPairs: [
            { key: 'dataConfig.dataResponse.sourceType', value: 'datacontainer' },
            { key: 'dataConfig.dataResponse.source.datacontainer', value: { data: { dataContainerComId: this.containerId, isLimit: false, limitNum: 150 } } },
            { key: 'dataConfig.fieldMapping', value: fieldMapping }
          ]
        }
      })
      this.loading = true;
      this.$store.dispatch('editor/updateScreenCom', params).then(() => {
        this.$message.success('修改成功！')
        this.getReferComs();
        this.state = 0;
      }).finally(() => {
        this.loading = false;
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.refer-component {
  ::v-deep {
    .el-dialog__header {
      .dialog-title {
        font-size: 16px;
        color: rgba(255, 255, 255, 1);
        .title-wrapper {
          display: flex;
          align-items: center;
          .back {
            width: 20px;
            height: 20px;
            margin-right: 16px;
            cursor: pointer;
          }
        }
      }
    }
    .el-dialog__body {
      .dialog-inner-wrapper {
        height: 500px;
        .step-wrapper {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 370px;
          margin-left: 35px;
          margin-bottom: 24px;
          .step-item {
            display: flex;
            align-items: center;
            &.active {
              .index {
                color: #fff;
                background: #3D85FF;
              }
              .step-txt {
                color: #3D85FF;
              }
            }
            .index {
              width: 32px;
              height: 32px;
              line-height: 30px;
              border: 1px solid rgba(204, 219, 255, 0.32);
              border-radius: 16px;
              text-align: center;
              color: rgba(204, 219, 255, 0.32);
            }
            .icon-svg {
              font-size: 32px;
            }
            .step-txt {
              width: 64px;
              height: 24px;
              font-family: 'PingFang SC';
              font-style: normal;
              font-weight: 600;
              font-size: 16px;
              line-height: 24px;
              color: rgba(255, 255, 255, 0.5);
              margin-left: 16px;
            }
          }
          .step-line {
            width: 104px;
            height: 0px;
            border: 1px solid #4b5263;
            margin: 0 25px;
            &.active {
              border-color: #3D85FF;
            }
          }
        }
      }
      .top-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #fff;
      }
      .refer-list {
        height: 440px;
        margin-top: 30px;
        overflow: auto;
        .refer-item {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          height: 50px;
          padding: 8px 16px 8px 8px;
          border-radius: 8px;
          &:hover {
            background: rgba(204, 219, 255, 0.1);
            .refer-btn {
              display: block;
            }
          }
          .refer-icon {
            flex: none;
            width: 55px;
            height: 36px;
            border: 1px solid #3D85FF;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .refer-name {
            flex: 1;
            font-family: 'PingFang SC';
            color: #fff;
            padding: 0 8px;
          }
          .refer-btn {
            cursor: pointer;
            .btn {
              width: 16px;
              height: 16px;
              color: #fff;
              margin-left: 18px;
              cursor: pointer;
            }
          }
        }
        .field-item {
          padding: 0 10px 20px 5px;
          .com-name {
            font-size: 14px;
            line-height: 22px;
            color: rgba(255, 255, 255, 0.7);
            margin: 12px 0 6px 0;
          }
          .el-table th {
            background-color: rgba(204, 219, 255, 0.1) !important;
          }
          .el-table tr {
            background-color: #1f2430 !important;
          }
          .t-icon {
            font-size: 16px;
            &.green {
              color: #00a755;
            }
            &.red {
              color: #ef5350;
            }
          }
        }
      }
    }
    .ml20 {
      margin-left: 20px;
    }
  }
}
</style>
