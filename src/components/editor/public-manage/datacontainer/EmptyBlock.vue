<template>
  <div class="empty-block">
    <div class="container-empty">
      <div class="container-empty-icon">
        <hz-icon name="empty" class="empty-icon"></hz-icon>
      </div>
      <div class="container-tip">
        {{ emptyText }}<span v-if="showButton" class="link-txt" @click="addClick()">{{ buttonText }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmptyBlock', // 暂无数据组件
  props: {
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    showButton: {
      type: <PERSON>olean,
      default: false
    },
    buttonText: {
      type: String,
      default: '新增'
    }
  },
  data () {
    return {}
  },
  methods: {
    addClick () {
      this.$emit('addClick')
    }
  }
}
</script>

<style lang="scss" scoped>
.container-empty {
  width: 200px;
  margin: 0 auto;
  .container-empty-icon {
    .empty-icon {
      width: 192px;
      height: 192px;
    }
  }
  .container-tip {
    font-family: 'PingFang SC';
    font-size: 14px;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 16px;
    .link-txt {
      color: #3D85FF;
      cursor: pointer;
      margin-left: 8px;
    }
  }
}
</style>
