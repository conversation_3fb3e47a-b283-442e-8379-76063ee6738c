<template>
  <div class="container-manage">
    <EmptyBlock
      v-if="list.length === 0"
      emptyText="暂无数据容器，请去"
      showButton
      buttonText="新增容器"
      style="margin-top: 125px;"
      @addClick="addContainer()" />
    <div class="container-wrap">
      <div class="container-item" v-for="item in list" :key="item.id">
        <div class="container-name" :title="item.alias" @click.stop="selectContainer(item)">{{ item.alias }}</div>
        <div class="ope-btn">
          <hz-icon name="tutorial-edit" class="btn" title="编辑" @click.native.stop="addContainer(item)"></hz-icon>
          <hz-icon name="application" class="btn app" title="应用" @click.native.stop="openRefer(item)"></hz-icon>
          <hz-icon name="trash" class="btn" title="删除" @click.native.stop="deleteContainer(item)"></hz-icon>
        </div>
      </div>
    </div>
    <!-- 新增数据容器name -->
    <EditName ref="add" @update="updateData" />
    <ReferComponent ref="refer" />
  </div>
</template>

<script>
import EditName from './EditName'
import ReferComponent from './ReferComponent'
import EmptyBlock from './EmptyBlock'
import { mapState } from 'vuex'
import canvasBus from '@/utils/canvasBus'

export default {
  name: 'ContainerManage',
  inject: ['getLayerTree'],
  components: {
    EditName,
    ReferComponent,
    EmptyBlock
  },
  data () {
    return {
      list: []
    }
  },
  computed: {
    ...mapState({
      compPackgesMap: state => state.editor.compPackgesMap,
      screenComs: state => state.editor.screenComs
    }),
    layerTree () {
      return this.getLayerTree()
    }
  },
  created () {
    this.getContainerList();
  },
  methods: {
    getContainerList () {
      const containerMap = _.pickBy(this.screenComs, com => (com.comName === 'interaction-container-datacontainer'));
      this.list = Object.values(containerMap);
    },
    addContainer (item) {
      this.$refs.add.showDialog(item);
    },
    openRefer (item) {
      this.$refs.refer.showDialog(item);
    },
    deleteContainer (item) {
      this.$alert('确认删除该数据容器？', { title: '提示', type: 'warning' }).then(async () => {
        this.layerTree.delete([item.id]);
        await this.$store.dispatch('editor/updateScreenLayers', this.layerTree.data.children)
        await this.$store.dispatch('editor/deleteComponent', [item.id])
        this.$message.success('删除成功');
        this.getContainerList();
      }).catch(() => {})
    },
    updateData () {
      this.getContainerList();
    },
    selectContainer (item) {
      canvasBus.emit('select_node', item.id);
      this.layerTree.select(item.id);
    }
  }
}
</script>

<style lang="scss" scoped>
.container-manage {
  .container-empty {
    width: 200px;
    margin: 125px auto 0;
    .container-empty-icon {
      .empty-icon {
        width: 192px;
        height: 192px;
      }
    }
    .container-tip {
      font-family: 'PingFang SC';
      font-size: 14px;
      text-align: center;
      color: rgba(255, 255, 255, 0.5);
      margin-top: 16px;
      .link-txt {
        color: #3D85FF;
        cursor: pointer;
        margin-left: 8px;
      }
    }
  }
  .container-wrap {
    .container-item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      height: 52px;
      background: rgba(204, 219, 255, 0.1);
      border-radius: 8px;
      margin-bottom: 8px;
      .container-name {
        flex: none;
        width: 200px;
        font-family: 'PingFang SC';
        font-size: 14px;
        color: #FFFFFF;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
      }
      .ope-btn {
        flex: none;
        .btn {
          width: 18px;
          height: 18px;
          color: #fff;
          margin-left: 18px;
          cursor: pointer;
          &.app {
            color: unset;
          }
        }
      }
    }
  }
}
</style>
