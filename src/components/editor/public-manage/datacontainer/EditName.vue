<template>
  <el-dialog :visible.sync="show" :title="title" top="0" append-to-body width="450px" class="edit-name">
    <el-form ref="form" :model="ruleForm" :rules="rules" label-position="right" label-width="100px" size="medium">
      <el-form-item prop="name">
        <span class="form-name" slot="label">容器名称：</span>
        <el-input v-model="ruleForm.name" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="plain" size="mini" :loading="loading" @click="submit">确定</el-button>
      <el-button type="text" size="mini" @click="closeDialog">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import canvasBus from '@/utils/canvasBus'
import { uuid } from '@/utils/base'
import { mapState } from 'vuex'

export default {
  name: 'EditName', // 新增重命名-数据容器
  inject: ['getLayerTree'],
  data () {
    return {
      show: false,
      title: '新增容器',
      isEdit: false,
      form: {
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入容器名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'change' }
        ]
      },
      ruleForm: {
        id: '',
        name: ''
      },
      loading: false
    }
  },
  computed: {
    ...mapState({
      compPackgesMap: state => state.editor.compPackgesMap,
      screenInfo: state => state.editor.screenInfo,
      sceneId: state => state.editor.sceneId,
      pageId: state => state.editor.pageId
    }),
    layerTree () {
      return this.getLayerTree()
    }
  },
  methods: {
    showDialog (item) {
      if (item) {
        this.title = '修改名称';
        this.ruleForm.id = item.id;
        this.ruleForm.name = item.alias;
        this.isEdit = true;
      } else {
        this.title = '新增容器';
        this.ruleForm.id = '';
        this.isEdit = false;
      }
      this.show = true;
    },
    resetForm () {
      this.$refs.form.resetFields();
    },
    closeDialog () {
      this.show = false;
      this.resetForm();
    },
    submit () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          if (this.isEdit) { // 编辑名称
            this.loading = true;
            this.$store.dispatch('editor/updateScreenCom', {
              id: this.ruleForm.id,
              keyValPairs: [
                { key: 'alias', value: this.ruleForm.name }
              ]
            }).then(() => {
              this.$message.success('修改成功');
              this.closeDialog();
              this.$emit('update')
            }).finally(() => {
              this.loading = false;
            })
          } else { // 新增容器
            const compData = this.compPackgesMap['interaction-container-datacontainer'];
            if (compData) {
              const cid = uuid(compData.name);
              const data = {
                id: cid,
                name: compData.name,
                alias: this.ruleForm.name,
                version: compData.version,
                attr: {
                  w: compData.width,
                  h: compData.height,
                  x: 0,
                  y: 0
                }
              }
              if (this.screenInfo.screenType === 'scene') {
                data.sceneId = this.sceneId
                data.pageId = this.pageId
              }
              this.loading = true;
              const res = await this.$store.dispatch('editor/createScreenComp', data);
              if (res.success) {
                const layer = {
                  id: cid,
                  type: 'com'
                }
                if (this.screenInfo.screenType === 'scene') {
                  layer.sceneId = data.sceneId
                  layer.pageId = data.pageId
                }
                this.layerTree.addChild(layer);
                await this.$store.dispatch('editor/updateScreenLayers', this.layerTree.data.children);
                this.$message.success('数据容器创建成功，请到右侧面板配置数据');
                canvasBus.emit('select_node', cid);
                this.layerTree.select(cid);
                this.closeDialog();
                this.$emit('update');
                this.loading = false;
              }
            } else {
              console.warn('未找到数据容器组件，请先发布【数据容器】组件！')
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-name {
  ::v-deep {
    .form-name {
      color: rgba(255, 255, 255, 0.7);;
    }
  }
}
</style>
