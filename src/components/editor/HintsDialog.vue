<template>
  <el-dialog
    title="提示"
    :visible.sync="show"
    width="529px"
    top="0"
    :close-on-click-modal="false"
    @open="openDialog">
    <div class="hints-content">
      <div class="hints-text">
        <hz-icon style="font-size: 28px" name="icon-prompt"></hz-icon>
        <p class="text">{{hintsText[hintsType] ? hintsText[hintsType].text : ''}}</p>
      </div>
      <div class="tutorial">
        <p class="title">点击查看教程:</p>
        <span @click="viewTutorial" class="view-tutorial">查看教程</span>
      </div>
      <div class="comp-list">
        <p class="title">相关组件有:</p>
        <div class="comp-content">
          <template v-for="item in compData">
            <p :key="item.id">{{item.alias}}</p>
          </template>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
    <el-button type="plain" @click="noPrompt" size="mini">不再提示</el-button>
  </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'HintsDialog',
  data () {
    return {
      show: false,
      hintsType: '',
      compData: [],
      hintsText: {
        source: {
          text: '多个组件使用相同数据源时建议使用数据容器数据源',
          tutorial: '/tutorial/preview?tutorialId=ffb97ccf-4b54-491a-b5c4-e66dd8eb374b&showMode=doc'
        }
      }
    }
  },
  methods: {
    showDialog ({ type = '', data = [] }) {
      this.hintsType = type
      this.compData = data
      this.show = true;
    },
    openDialog () {},
    noPrompt () {
      this.show = false;
      localStorage.setItem('hintsShow', false)
    },
    viewTutorial () {
      window.open(this.hintsText[this.hintsType].tutorial, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.hints-content {
  .hints-text {
    display: flex;
    align-items: center;

    .text {
      color: #FFFFFF;
      margin-left: 8px;
      font-weight: 600;
    }
  }

  .tutorial {
    .view-tutorial {
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      color: #ffffff;
      cursor: pointer;
    }
  }

  .comp-list {
    .comp-content {
      padding: 16px;
      display: flex;
      max-height: 124px;
      background-color: #191D25;
      overflow: auto;

      p {
        color: rgba(255, 255, 255, 0.70);
        width: 138px;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 16px;
      }
    }
  }
  .title {
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    margin-top: 16px;
    margin-bottom: 8px;
  }
}
</style>
