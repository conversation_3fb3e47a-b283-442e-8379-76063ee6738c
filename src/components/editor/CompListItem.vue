<template>
  <div
    class="components-list-item"
    :style="isSource && {width: '100px', height: '80px'}"
    :draggable="draggable"
    @dragstart="handleDragStart"
    @click.stop="handleClick">

    <el-tooltip :content="title" popper-class="atooltip" :visible-arrow="false"  placement="top" v-if="!isSource">
      <div class="components-item-text"> {{ title }} </div>
    </el-tooltip>

    <div  class="components-item-img" :style="{ backgroundImage: backgroundImage }" v-if="!data.isLazy">
      <div v-if="platforms == 'mobile'" class="components-item-img-commonMarker" >APP</div>
      <div v-if="platforms == 'pc'" class="components-item-img-mobileMarker" >PC</div>
    </div>
    <div class="components-item-img" v-else>
      <img class="components-item-img" v-lazy="getIhumbmailUrl(icon)" />
    </div>
    <el-tooltip :content="title" popper-class="atooltip" :visible-arrow="false"  placement="top" v-if="isSource">
      <div class="components-item-text"> {{ title }} </div>
    </el-tooltip>
  </div>
</template>

<script>
import { replaceUrl, treeToJson } from '@/utils/base'
import { polyfill } from 'mobile-drag-drop'
import { scrollBehaviourDragImageTranslateOverride } from 'mobile-drag-drop/scroll-behaviour'
export default {
  name: 'CompListItem',
  props: {
    draggable: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: '//img.alicdn.com/tfs/TB17NeSDSf2gK0jSZFPXXXsopXa-160-116.png'
    },
    data: Object,
    isSource: { // 是否是资源面板
      type: Boolean,
      default: false
    },
    platforms: {
      type: String,
      default: ''
    }
  },
  computed: {
    backgroundImage: function () {
      let icon = this.icon || '//img.alicdn.com/tfs/TB17NeSDSf2gK0jSZFPXXXsopXa-160-116.png';
      icon = replaceUrl(icon)
      return 'url(' + icon + ')';
    }
  },

  beforeCreate () {
    this.$parent.compList.push(this);
  },
  mounted () {
    this.$nextTick(() => {
      polyfill({
        dragImageTranslateOverride: scrollBehaviourDragImageTranslateOverride
      })
    })
  },

  methods: {
    getIhumbmailUrl (icon) {
      return `${icon}?width=100&height=80`
    },
    handleClick () {
      this.$emit('click', this.data);
    },
    transforData (values, str) {
      const obj = {}
      treeToJson(values, str, obj)
      return obj
    },
    handleDragStart (e) {
      let commonData = {}
      if (this.isSource) {
        commonData = this.$store.state.editor.compPackgesMap[this.data.currentType]
        if (typeof this.data.result === 'object') {
          commonData.result = this.transforData(this.data.result, 'config')
        } else {
          commonData.result = this.data.result
        }
      }
      const dataList = this.isSource ? { ...commonData, isActive: this.isSource } : this.data;
      const dragData = {
        type: 'com',
        data: dataList
      };
      // if (e._dndHandle && e.dataTransfer.setDragImage) {
      //   e.dataTransfer.setDragImage(e.currentTarget);
      // }
      e.dataTransfer.setData('application/json', JSON.stringify(dragData));
    }
  }
}
</script>

<style lang="scss" scoped>
.components-list-item {
  width: 80px;
  cursor: pointer;
  display: inline-block;
  color: #bcc9d4;
  vertical-align: top;
  user-select: none;
  flex: none;
  margin-bottom: 6px;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  .components-item-text {
    font-size: 12px;
    padding: 0 5px;
    text-align: left;
    overflow: hidden;
    background: #212326;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 22px;
  }
  .components-item-img {
    overflow: hidden;
    width: 100%;
    height: 58px;
    background-clip: content-box;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-color: #17191c;
    position: relative;
    text-align: center;
    box-sizing: content-box;
    transition: border-color .2s;
    pointer-events: none;
  }
  .components-item-img-commonMarker {
    width: 28px;
    height: 16px;
    background-color: #FF475D;
    position: relative;
    bottom: -41px;
    padding: 0px 2px 0px 0px;
    border-radius: 0px 10px 10px 0px;
    font-family: "PingFang SC";
    line-height: 16px;
    text-align: center;
    color: #FFFFFF;
  }
  .components-item-img-mobileMarker {
    width: 28px;
    height: 16px;
    background-color: #FF9029;
    position: relative;
    bottom: -41px;
    padding: 0px 2px 0px 0px;
    border-radius: 0px 10px 10px 0px;
    font-family: "PingFang SC";
    line-height: 16px;
    text-align: center;
    color: #FFFFFF;
  }
}
</style>
