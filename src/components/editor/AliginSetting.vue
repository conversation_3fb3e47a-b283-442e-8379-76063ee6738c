<template>
  <div class="aligin-setting">
    <div class="aligin-title">对齐方式</div>
    <div class="aligin-content">
      <div class="aligin">
        <div class="aligin-text">对齐</div>
        <div class="aligin-icon">
          <span
            v-for="item in aliIcon"
            :key="item.title" :title="item.title"
            @click="handleAliginClick(item.event)">
            <hz-icon :name="item.icon"></hz-icon>
          </span>
        </div>
      </div>
      <div class="aligin">
        <div class="aligin-text">分布</div>
        <div class="aligin-icon">
          <span
            v-for="item in diIcon"
            :key="item.title" :title="item.title"
            @click="handleAliginClick(item.event)">
            <hz-icon :name="item.icon"></hz-icon>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { aliginIcon, distributionIcon } from '@/common/constants';
import canvasBus from '@/utils/canvasBus';

export default {

  data () {
    return {
    }
  },
  computed: {
    aliIcon () {
      return aliginIcon
    },
    diIcon () {
      return distributionIcon
    }
  },
  methods: {
    handleAliginClick (key) {
      canvasBus.emit('align_comp', key);
    }
  }

}
</script>

<style lang="scss" scoped>
.aligin-setting {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
  .aligin-title {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
  }
  .aligin-content {
    padding: 20px 16px;
    color: #fff;
    .aligin {
      display: flex;
      margin-top: 12px;
      &-text {
        flex-basis: 25%;
      }

      &-icon {
        font-size: 16px;
        flex-basis: 75%;
        display: flex;
        // justify-content: space-evenly;
        span {
          cursor: pointer;
          margin-right: 20px;
        }
      }
    }
  }
}
</style>
