<template>
  <div
    class="config-tree-root"
    v-if="tree && tree.root && tree.root.children">
    <div class="control-config">

    <ConfigNode
      v-for="node in tree.root.children"
      :key="node.id"
      :node="node"
      :configObj="newConfigObj"
    />
    </div>
  </div>
</template>

<script>
import ConfigNode from '@/components/editor/ConfigNode';
import ConfigTree, { parseConfig } from '@/lib/ConfigTree';

export default {
  name: 'ConfigTree',

  components: {
    ConfigNode
  },

  props: {
    treeData: { // 传图表 package.json 原始控件配置项 config 的数据
      type: Object,
      default () { return {} },
      require: true
    },
    configObj: { // 传配置解析后对象，可选
      type: Object,
      default () { return null }
    }
  },

  computed: {
    tree () {
      const cfgTree = new ConfigTree({ children: this.treeData });
      cfgTree.toObject();
      return cfgTree;
    },
    newConfigObj () {
      if (_.isEmpty(this.configObj)) {
        return parseConfig(this.treeData);
      }
      return this.configObj;
    }
  }
}
</script>
