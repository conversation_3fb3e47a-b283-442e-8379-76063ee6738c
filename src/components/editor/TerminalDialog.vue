<template>
  <el-dialog :visible.sync="show" title="智能操控" class="terminal" width="380px" :close-on-click-modal="false" top="0px">
    <div class="terminal-content">
      <div class="info-wrapper">
        <span class="el-icon-info"></span>
        <span class="info-txt"> 智能操控有两种模式，开启时只能单选其中一种模式</span>
      </div>
      <div class="switch-wrapper">
        <span class="switch-name">终端交互</span>
        <el-switch v-model="isConnect" @change="e => switchChange('control', e)"></el-switch>
      </div>
      <div class="switch-wrapper">
        <span class="switch-name">语音交互</span>
        <el-switch v-model="voiceControl" @change="e => switchChange('voice', e)"></el-switch>
      </div>
      <div class="voice-config" v-if="voiceControl">
        <span class="voice-config-item" @click="updateConfig">更新配置</span>
        <span class="voice-config-item" @click="voiceConfig">参数调整</span>
      </div>
      <seatom-loading v-if="loading"></seatom-loading>
    </div>
    <div slot="footer">
      <el-button type="primary" size="mini" @click="submit">确定</el-button>
      <el-button type="plain" size="mini" @click="closeDialog">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { screensocket, updateScreensocket, createInitControl } from '@/api/screen';
import { mapState } from 'vuex'
export default {
  name: 'Terminal', // 终端交互弹窗
  props: {
    screenId: [String, Number]
  },
  data () {
    return {
      show: false,
      isConnect: false,
      voiceControl: false,
      loading: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  methods: {
    showDialog () {
      this.show = true;
      this.voiceControl = !!this.screenInfo.voiceControl;
      this.getScreensocket();
    },
    closeDialog () {
      this.show = false;
    },
    async getScreensocket () {
      const params = {
        screenId: +this.screenId
      }
      this.loading = true;
      const res = await screensocket(params);
      if (res && res.success) {
        const data = res.data;
        this.isConnect = data.isConnect;
      }
      this.loading = false;
    },
    switchChange (type, value) {
      if (value) {
        if (type === 'control') {
          this.voiceControl = false;
        } else {
          this.isConnect = false;
        }
      }
    },
    async submit () {
      const data1 = {
        isConnect: this.isConnect
      }
      this.loading = true;
      const updateScreen = this.$store.dispatch('editor/updateScreenInfo', [
        { key: 'voiceControl', value: this.voiceControl }
      ])
      const promise = [
        updateScreensocket(data1, { screenId: this.screenId }),
        updateScreen
      ]
      return Promise.all(promise).then(() => {
        this.loading = false;
        this.closeDialog();
      })
    },
    async updateConfig () {
      const params = {
        screenId: this.screenInfo.id
      }
      const res = await createInitControl(params);
      if (res && res.success) {
        this.$message.success('更新成功')
      }
    },
    async voiceConfig () {
      await this.submit();
      this.$store.commit('editor/initVoiceConfigShow', { show: true })
      this.closeDialog();
    }
  }
}
</script>

<style lang="scss" scoped>
.terminal {
  ::v-deep {
    .el-dialog__body {
      padding: 10px 30px 10px 30px
    }
  }
  .terminal-content {
    .info-wrapper {
      .info-txt {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
    .switch-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 18px;
      .switch-name {
        font-size: 14px;
        color: #fff;
      }
    }
    .voice-config {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 15px;
      .voice-config-item {
        color: #3D85FF;
        font-size: 12px;
        cursor: pointer;
        margin-left: 10px;
      }
    }
  }
}
</style>
