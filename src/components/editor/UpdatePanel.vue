<template>
  <div class="update-panel" v-clickoutside="close">
    <div class="select-item all-update">
      <div class="can-be-updated">
        可更新组件
        <span>{{updateComs.length}}</span>
      </div>
      <div v-if="updateComs.length" class="update-btn" @click="updateAll">一键更新</div>
    </div>
    <div class="update-list">
      <div class="select-item update-item" v-for="item in updateComs" :key="item.id">
        <span class="com-name" :title="alias(item)"><hz-icon class="mr-4" name="all"></hz-icon>{{alias(item)}}</span>
        <span class="com-version">{{item.version}}</span>
      </div>
      <div class="update-empty" v-if="!updateComs.length">组件都已是最新版本</div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { upgradeComponent } from '@/api/component'
import { isPanel } from '@/utils/base'

export default {
  name: 'UpdatePanel',
  data () {
    return {}
  },
  computed: {
    ...mapState({
      compPackges: state => state.editor.compPackges,
      screenComs: state => state.editor.screenComs,
      compPackgesMap: state => state.editor.compPackgesMap,
      childScreenComs: state => state.editor.childScreenComs
    }),
    updateComs () {
      const { compPackgesMap } = this;
      return [...Object.values(this.screenComs), ...this.childComs].reduce((needUpdateArr, com) => {
        const pkg = compPackgesMap[com.comName];
        if (pkg && pkg.version !== com.version) {
          needUpdateArr.push({
            ...com,
            version: pkg.version
          });
        }
        return needUpdateArr;
      }, []);
    },
    childComs () {
      const panelComs = (Object.values(this.screenComs) || []).filter(item => {
        return isPanel(item.comType)
      }).reduce((prev, cur) => {
        const screens = (cur.config.screens || []).reduce((coms, screen) => {
          const curComs = Object.values(this.childScreenComs[screen.id]) || []
          coms.push(...curComs)
          return coms
        }, []).map(com => {
          return {
            pAlias: cur.alias,
            ...com
          }
        })
        prev.push(...screens)
        return prev
      }, [])
      return panelComs
    },
    alias () {
      return (item) => {
        return (item.pAlias ? `${item.pAlias}/` : '') + item.alias
      }
    }
  },
  methods: {
    close () {
      this.$store.commit('editor/updateDrawerSelect', { type: 'update', value: false })
    },
    updateAll () {
      const params = {
        screenId: this.$route.params.screenId
      };
      const text = '一键更新组件有可能导致组件不可用，如果不确定请咨询客服，如果有重要数据请备份大屏。更新过程中，请不要编辑组件，是否确定要更新？';
      this.$confirm(text, '', {
        confirmButtonText: '确定(Enter)',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(async () => {
        // 点击确认的逻辑
        const data = this.updateComs.map(item => {
          return { id: item.id, version: item.version }
        })
        const res = await upgradeComponent(data);
        if (res && res.success && res.data) {
          // this.$store.commit('editor/updateScreenComs', res.data);
          this.$store.dispatch('editor/initScreenAction', params).then(() => {
            this.$message.success('组件升级成功！')
          })
        }
      }).catch(() => {})
    }
  }

}
</script>

<style lang="scss" scoped>
.update-panel {
  display: flex;
  flex-direction: column;
  width: 320px;
  min-height: 204px;
  max-height: 332px;
  background: #1c1f25;
  overflow: hidden;
  box-shadow: 1px 1px 8px -2px #000;
  border-radius: 1px;
  user-select: none;
  .select-item {
    color: #bcc9d4;
    display: flex;
    align-items: center;
    height: 40px;
    line-height: 40px;
    padding: 0 16px;
    .com-name {
      max-width: 240px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow:ellipsis;
    }
  }
  .all-update {
    justify-content: space-between;
    border-bottom: 1px solid #3a4659;
    .update-btn {
      color: #2681ff;
      cursor: pointer;
      text-align: right;
      white-space: nowrap;
      line-height: 30px;
      flex: 1;
    }
  }
  .update-list {
    overflow: auto;
    .update-item {
      justify-content: space-between;
      &:nth-child(even) {
        background: #22262d;
      }
    }
  }
  .update-empty {
    height: 160px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #bcc9d4;
  }
  .mr-4 {
    margin-right: 4px;
  }
}
</style>
