<template>
  <div class="container-config">
    <!-- 新版动态面板 场景大屏 状态弹窗面板 -->
    <div class="dynamic-panel-config"
       v-if="isStatusPanel"
     >
      <div class="config-control">
        <el-collapse
          v-model="active"
          accordion
          class="w100">
          <el-collapse-item name="1">
            <template slot="title">
              <div class="c-header">
                <span class="name">状态列表</span>
                <div class="btn-box" v-if="active === '1'">
                  <span class="btn el-icon-circle-plus" @click.stop="createPage"/>
                  <span class="btn el-icon-delete" @click.stop="deletePage"/>
                  <span class="btn el-icon-download" v-if="!isTopPage" @click.stop="topPage"/>
                </div>
              </div>
            </template>
            <el-tabs
              class="tab-c"
              type="card"
              v-model="activePanel"
              size="mini">
              <el-tab-pane
                :label="'状态' + (index + 1)"
                :name="item.sceneId"
                v-for="(item, index) in pages"
                :key="index">
                <el-form
                  class="form"
                  label-width="70px"
                  label-position="left"
                  size="mini">
                  <el-form-item label="状态key">
                    <el-input
                      v-model="item.pageName"
                      placeholder="请输入状态key"
                      @focus="handlePageNameFocus"
                      @blur="updatePageName(item)" />
                  </el-form-item>
                </el-form>
                <div class="edit-btn">
                  <el-button
                    v-if="screens.length"
                    type="primary"
                    @click="enterScenePage">
                    编辑面板
                  </el-button>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <!-- 指标面板 -->
    <div class="pitch-config" v-else-if="currentCom.comName === 'interaction-container-indicator'">
      <el-button type="primary" class="pitch-config-button" @click="editIndicator">编辑指标</el-button>
    </div>
    <!-- 其他面板 -->
    <div class="pitch-config" v-else-if="currentCom.comName !== 'interaction-container-dynamicpanel'">
      <el-button type="primary" class="pitch-config-button" @click="updateScreen(true)">
        {{ ['interaction-container-affixPanel', 'interaction-container-popup'].includes(currentCom.comName) ? '编辑面板内容' : '编辑列表项' }}
      </el-button>
    </div>
    <!-- 原动态面板 普通大屏 -->
    <div class="dynamic-panel-config" v-else>
      <div class="config-control">
        <el-collapse
          v-model="active"
          accordion
          class="w100">
          <el-collapse-item name="1">
            <template slot="title">
              <div class="c-header">
                <span class="name">状态列表</span>
                <div class="btn-box" v-if="active === '1'">
                  <span class="btn el-icon-circle-plus" @click.stop="createScreen"/>
                  <span class="btn el-icon-delete" @click.stop="deleteScreen"/>
                </div>
              </div>
            </template>
            <el-tabs
              class="tab-c"
              type="card"
              v-model="activePanel"
              size="mini">
              <el-tab-pane
                :label="'状态' + (index + 1)"
                :name="item.id + ''"
                v-for="(item, index) in screens"
                :key="index">
                <el-form
                  class="form"
                  label-width="70px"
                  label-position="left"
                  size="mini">
                  <el-form-item label="状态key">
                    <el-input
                      v-model="item.key"
                      placeholder="请输入状态key"
                      @blur="saveScreens()" />
                  </el-form-item>
                </el-form>
                <div class="edit-btn">
                  <el-button
                    v-if="item.id"
                    type="primary"
                    @click="updateScreen(true,item.id)">
                    编辑动态面板
                  </el-button>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <SeatomLoading v-if="loading" />
  </div>
</template>

<script>
import { randomStr, replaceUrl } from '@/utils/base'
import { createScreen, deleteScreen, updateScreen, getScreen, addScene, deleteScene, updateName, orderScene } from '@/api/screen'
import { mapState, mapGetters } from 'vuex'
import emitter from '@/utils/bus'

export default {
  name: 'PitchConfig',
  data () {
    return {
      active: '1',
      activePanel: '',
      screens: [],
      pages: [],
      loading: false,
      isFocusStatusPanel: false,
      focusCurrentComId: ''
    }
  },
  created () {
    const { comName } = this.currentCom;
    if (!this.screens.length &&
    !this.isStatusPanel &&
    !['interaction-container-dynamicpanel'].includes(comName)) {
      this.createScreen()
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      screenFilters: state => state.editor.screenFilters
    }),
    ...mapGetters('editor', ['currentCom']),
    platform () {
      if (this.currentCom.comName === 'interaction-container-flowlayoutpanel') { // 如果是流式面板使用移动端动态面板的配置页面
        return 'mobile'
      } else {
        return this.screenInfo.type
      }
    },
    computedWidth () {
      switch (this.currentCom.comName) {
        case 'interaction-container-loop-pitch': {
          return this.currentCom.config.item.widthUnit === 'px'
            ? this.currentCom.config.item.width
            : this.currentCom.attr.w * (this.currentCom.config.item.width / 100)
        }
        case 'interaction-container-roll-pitch': {
          if (this.platform === 'pc') {
            return this.currentCom.attr.w * (1 / this.currentCom.config.item.xCount)
          } else {
            return (this.screenInfo.config.width * (this.currentCom.attr.w / 24)) * (1 / this.currentCom.config.item.xCount)
          }
        }
        case 'interaction-container-list-pitch' : {
          return this.currentCom.config.item.width
        }
        case 'interaction-container-carousepanel' :
        case 'interaction-form-group':
        case 'interaction-container-affixPanel':
        case 'interaction-container-fold-panel' :
        case 'interaction-container-popoverpanel' :
        case 'interaction-container-mapShadowPanel' :
        case 'interaction-container-dynamicpanel' :
        case 'interaction-container-newdynamicpanel' :
        case 'interaction-container-popup' :
        case 'interaction-container-statusdialogpanel' : {
          if (this.platform === 'pc') {
            return this.currentCom.attr.w
          } else {
            return this.screenInfo.config.width * (this.currentCom.attr.w / 24)
          }
        }
        case 'interaction-container-flowlayoutpanel' : {
          return this.currentCom.attr.w
        }
        default : {
          return 0
        }
      }
    },
    computedHeight () {
      switch (this.currentCom.comName) {
        case 'interaction-container-loop-pitch' : {
          return this.currentCom.config.item.heightUnit === 'px'
            ? this.currentCom.config.item.height
            : this.currentCom.attr.h * (this.currentCom.config.item.height / 100)
        }
        case 'interaction-container-roll-pitch' : {
          if (this.platform === 'pc') {
            return this.currentCom.attr.h * (1 / this.currentCom.config.item.yCount)
          } else {
            return (this.currentCom.attr.h * 10 + ((this.currentCom.attr.h - 1) * 5)) * (1 / this.currentCom.config.item.yCount)
          }
        }
        case 'interaction-container-list-pitch' : {
          return this.currentCom.config.item.height
        }
        case 'interaction-container-fold-panel' : {
          return this.currentCom.config.panel.panelHeight
        }
        case 'interaction-container-carousepanel' :
        case 'interaction-form-group':
        case 'interaction-container-affixPanel':
        case 'interaction-container-popoverpanel' :
        case 'interaction-container-mapShadowPanel' :
        case 'interaction-container-dynamicpanel' :
        case 'interaction-container-newdynamicpanel' :
        case 'interaction-container-popup' :
        case 'interaction-container-statusdialogpanel' : {
          if (this.platform === 'pc') {
            return this.currentCom.attr.h
          } else {
            return this.currentCom.attr.h * 10 + ((this.currentCom.attr.h - 1) * 5)
          }
        }
        case 'interaction-container-flowlayoutpanel' : {
          return this.currentCom.attr.h
        }
        default : {
          return 0
        }
      }
    },
    computedComp () {
      switch (this.currentCom.comName) {
        case 'interaction-container-loop-pitch' :
        case 'interaction-container-roll-pitch' :
        case 'interaction-container-list-pitch' :
        case 'interaction-container-popup' : {
          return 'pitch'
        }
        case 'interaction-container-popoverpanel' :
        case 'interaction-container-mapShadowPanel' :
        case 'interaction-container-fold-panel' :
        case 'interaction-container-carousepanel':
        case 'interaction-container-affixPanel' :
        case 'interaction-form-group':
        case 'interaction-container-statusdialogpanel' : {
          return 'dialog'
        }
        case 'interaction-container-dynamicpanel' :
        case 'interaction-container-flowlayoutpanel' :
        case 'interaction-container-newdynamicpanel' : {
          return 'dynamic'
        }
        default : {
          return ''
        }
      }
    },
    // 是否是状态面板，新版动态面板与状态弹框面板是状态面板
    isStatusPanel () {
      const arr = ['interaction-container-newdynamicpanel', 'interaction-container-statusdialogpanel', 'interaction-container-flowlayoutpanel']
      return arr.includes(this.currentCom.comName)
    },
    // 是否为置顶页面
    isTopPage () {
      return this.pages.findIndex(p => p.sceneId === this.activePanel) <= 0
    }
  },
  watch: {
    'currentCom.id': {
      handler (val) {
        if (val) {
          this.screens = _.cloneDeep(this.currentCom.config.screens || [])
          this.pages = _.cloneDeep(this.currentCom.config.pages || [])
          if (this.screens.length) {
            if (this.currentCom.comName === 'interaction-container-dynamicpanel') {
              this.activePanel = this.screens[0].id + ''
            } else if (this.isStatusPanel) {
              if (this.pages.length) {
                this.activePanel = this.pages[0].sceneId;
              }
            }
          }
        }
      },
      immediate: true
    },
    activePanel: {
      handler (val) {
        if (val) {
          emitter.emit('DynamicPanelState', { id: this.currentCom.id, state: val })
        }
      },
      immediate: true
    }
  },
  methods: {
    async createScreen () {
      const screen = {
        name: '子组件编辑面板',
        projectId: this.screenInfo.projectId,
        workspaceId: this.screenInfo.workspaceId,
        level: 0,
        type: this.platform,
        templateId: 1,
        config: {
          height: this.computedHeight,
          width: this.computedWidth,
          scaleType: 'no_scale',
          backgroundImage: replaceUrl('/public/system/common/bqVm7QL3vf4_0lTdqODg_w==.jpg')
        },
        isDynamicScreen: true,
        relationCompId: this.currentCom.id,
        parentId: this.screenInfo.id
      }
      this.loading = true;
      const isStatusPanel = this.isStatusPanel
      const res = await createScreen(screen)
      if (res && res.success) {
        this.screens.push({
          id: res.data.id,
          key: 'key_' + randomStr(4)
        })
        if (this.currentCom.comName === 'interaction-container-dynamicpanel') {
          this.activePanel = res.data.id + ''
        }
        this.saveScreens(isStatusPanel)
      }
    },
    async deleteScreen () { // 删除
      if (this.screens.length) {
        const index = this.screens.findIndex(item => item.id === +this.activePanel)
        const screen = this.screens[index]
        if (screen) {
          const isStatusPanel = this.isStatusPanel
          const res = await deleteScreen({ id: screen.id })
          if (res && res.success) {
            this.screens.splice(index, 1)
            this.$nextTick(() => {
              const target = index > 0 ? this.screens[index - 1].id : this.screens[0]?.id || ''
              this.activePanel = target + ''
              this.saveScreens(isStatusPanel)
            })
          }
        }
      }
    },
    async createPage () { // 新增场景页面
      if (!this.screens.length) { // 没有创建大屏，先创建大屏
        this.loading = true;
        const screen = {
          name: '子组件编辑面板',
          projectId: this.screenInfo.projectId,
          workspaceId: this.screenInfo.workspaceId,
          level: 0,
          type: this.platform,
          templateId: 1,
          config: {
            height: this.computedHeight,
            width: this.computedWidth,
            scaleType: 'no_scale',
            backgroundImage: replaceUrl('/public/system/common/bqVm7QL3vf4_0lTdqODg_w==.jpg')
          },
          isDynamicScreen: true,
          relationCompId: this.currentCom.id,
          parentId: this.screenInfo.id,
          screenType: 'scene'
        }
        const res = await createScreen(screen)
        if (res && res.success) {
          const id = res.data.id;
          this.screens.push({ id });
          this.saveScreens();
          this.getScreenInfo(id);
        }
      } else { // 创建场景
        const params = {
          id: this.screens[0].id
        }
        const isStatusPanel = this.isStatusPanel
        this.loading = true;
        const res = await addScene({}, params);
        if (res && res.success) {
          const screenInfo = res.data || {}
          this.updateCurrentPages(screenInfo, true)
          this.saveScreens(isStatusPanel)
        } else {
          this.loading = false;
        }
        this.loading = false;
      }
    },
    async deletePage () { // 删除场景页面
      if (this.screens.length) {
        this.$confirm('此操作将永久删除该状态, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.loading = true;
          const isStatusPanel = this.isStatusPanel
          const res = await deleteScene({ sceneId: this.activePanel }, { id: this.screens[0].id })
          if (res && res.success) {
            const index = this.pages.findIndex(p => p.sceneId === this.activePanel);
            if (index > -1) {
              this.pages.splice(index, 1);
              this.saveScreens(isStatusPanel)
              if (this.pages.length) {
                this.activePanel = this.pages[this.pages.length - 1].sceneId;
              }
            }
          }
          this.loading = false;
        }).catch(() => {
          this.loading = false;
        });
      }
    },
    async topPage () { // 置顶场景页面
      const page = this.pages.find(p => p.sceneId === this.activePanel)
      const index = this.pages.findIndex(p => p.sceneId === this.activePanel)
      if (index > 0) {
        const data = {
          id: this.screens[0].id,
          sceneId: page.sceneId,
          targetIndex: 0
        }
        const res = await orderScene(data)
        if (res && res.success) {
          this.pages.splice(index, 1)
          this.pages.unshift(page)
          this.saveScreens(true)
        }
      }
    },
    handlePageNameFocus () {
      this.isFocusStatusPanel = this.isStatusPanel
      this.focusCurrentComId = this.currentCom.id
    },
    async updatePageName (item) { // 更新场景页面名称
      const res = await updateName(item, { id: this.screens[0].id });
      if (res && res.success) {
        const screenInfo = res.data || {}
        this.updateCurrentPages(screenInfo, false)
        this.saveScreens(this.isFocusStatusPanel, {
          id: this.focusCurrentComId
        })
      } else {
        this.loading = false;
      }
    },
    // 更新pages
    updateCurrentPages (screenInfo, reset) {
      const pages = screenInfo.sceneConfig.map(item => {
        return {
          sceneId: item.sceneId,
          sceneName: item.sceneName,
          pageId: item.pageList[0].pageId,
          pageName: item.pageList[0].pageName
        }
      })
      this.pages = pages;

      if (reset) {
        this.activePanel = this.pages[this.pages.length - 1].sceneId;
      }
    },
    async getScreenInfo (id, reset = true) { // 获取场景大屏下的场景页面
      const isStatusPanel = this.isStatusPanel
      const screenInfo = (await getScreen({ id: id })).data;

      this.updateCurrentPages(screenInfo, reset)
      this.saveScreens(isStatusPanel)
    },
    async saveScreens (isStatusPanel = false, params = {}) {
      const payload = {
        id: this.currentCom ? this.currentCom.id : '',
        keyValPairs: [
          { key: 'config.screens', value: _.cloneDeep(this.screens) }
        ],
        ...params
      }
      if (isStatusPanel || this.isStatusPanel) {
        payload.keyValPairs.push({ key: 'config.pages', value: _.cloneDeep(this.pages) })
      }
      this.$store.dispatch('editor/updateScreenCom', payload).finally(() => {
        this.loading = false;
      })
    },
    async updateScreen (enter, screenId) {
      const data = {
        'config.width': this.computedWidth,
        'config.height': this.computedHeight
      }
      await updateScreen(data, { id: screenId || this.screens[0].id })
      if (enter) this.enterSubScreen(screenId)
    },
    enterSubScreen (screenId) { // 切到普通大屏
      const dataResponse = this.currentCom.dataConfig.dataResponse
      const routeData = this.$router.resolve({
        path: `/screen/edit/${screenId || this.screens[0].id}`,
        query: {
          screenId: this.screenInfo.id,
          comp: this.computedComp,
          type: dataResponse.sourceType,
          cid: this.currentCom.id
        }
      })
      window.open(routeData.href, `screen_${screenId || this.screens[0].id}`)
    },
    enterScenePage () { // 切到场景大屏
      const dataResponse = this.currentCom.dataConfig.dataResponse
      const routeData = this.$router.resolve({
        path: `/screen/edit/${this.screens[0].id}`,
        query: {
          screenId: this.screenInfo.id,
          comp: this.computedComp,
          type: dataResponse.sourceType,
          cid: this.currentCom.id,
          sceneId: this.activePanel
        }
      })
      window.open(routeData.href, `screen_${this.screens[0].id}`)
    },
    editIndicator () {
      this.$emit('editIndicator', this.currentCom.id)
    }
  }
}
</script>

<style lang="scss" scoped>
.pitch-config {
  padding: 4px 8px;
  width: 100%;
  &-button {
    width: 100%;
  }
}
.dynamic-panel-config {
  .el-icon-download {
    transform: rotate(180deg);
  }
  .tab-c {
    ::v-deep {
      .el-tabs__nav-wrap::after {
        display: none;
      }
      .el-tabs__nav-next,
      .el-tabs__nav-prev {
        line-height: 35px;
      }
      .el-tabs__item {
        height: 35px;
        line-height: 35px;
        font-size: 12px;
      }
    }
  }
  .c-header {
    width: 250px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-box {
      span {
        font-size: 14px;
        margin-left: 10px;
      }
    }
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
      color: var(--control-text-color);
    }
    .el-collapse-item__content {
      padding: 8px 0;
    }
    .el-tabs--card > .el-tabs__header {
      border-bottom: unset;
      .el-tabs__item {
        height: 28px;
        line-height: 28px;
        border-radius: 4px 4px 0 0;
      }
      .el-tabs__nav-next, .el-tabs__nav-prev {
        line-height: 28px;
      }
      .el-tabs__nav-wrap.is-scrollable {
        padding: 0 20px;
        box-sizing: border-box;
      }
    }
  }
  .w100 {
    width: 100%;
  }
  .edit-btn {
    margin-top: 20px;
    button {
      width: 100%;
    }
  }
}
</style>
