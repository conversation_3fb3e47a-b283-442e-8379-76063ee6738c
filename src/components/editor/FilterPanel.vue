<template>
  <div class="filter-panel">
    <div class="f-title">
      <span>数据过滤器
        <!-- <font class="sub-t pointer" @click="create">新建</font> -->
      </span>
      <i class="el-icon-close pointer" @click="close"></i>
    </div>
    <div class="f-content">
      <virtual-list style="height: 100%; overflow-y: auto;padding-right:10px;"
        :keeps="30"
        :data-key="'id'"
        :data-sources="list"
        :data-component="itemComponent"
        @delItem="delItem"
      />
      <div ref="con" style="height:0;overflow:hidden;"></div>
    </div>
  </div>
</template>

<script>
import VirtualList from 'vue-virtual-scroll-list'
import FilterItemWrapper from './my-data/FilterItemWrapper';
import { mapState } from 'vuex';
export default {
  name: 'FilterPanel', // 过滤器面板
  props: {},
  components: {
    'virtual-list': VirtualList
  },
  data () {
    return {
      list: [],
      itemComponent: FilterItemWrapper
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      screenFilters: state => state.editor.screenFilters
    })
  },
  watch: {
    screenFilters: {
      handler: function (val) {
        setTimeout(() => {
          const list = Object.values(val).filter(item => item.type !== 'system');
          this.list = _.cloneDeep(list);
        }, 0)
      },
      deep: true,
      immediate: true
    }
  },
  beforeCreate () {
    this.$store.commit('editor/updateEditPanelSelect', { type: 'tool', value: false })
  },
  methods: {
    delItem (item, index) {
      if (item.id) {
        this.$confirm('是否删除数据过滤器，可能导致相关组件不可用。', { title: '提示', type: 'warning' }).then(async () => {
          this.$store.dispatch('editor/deleteScreenFilter', item.id);
          this.$message.success('删除成功');
        }).catch(() => {})
      } else {
        this.list.splice(index, 1);
      }
    },
    create () {
      const list = this.list.filter(item => !item.id);
      if (list.length) {
        this.$refs.con.scrollIntoView();
        return
      }
      const filter = {
        callbackKeys: [],
        name: '新建过滤器',
        content: 'return data;',
        enable: false,
        editable: true,
        show: true
      }
      this.list.push(filter);
      this.$nextTick(() => {
        this.$refs.con.scrollIntoView();
      })
    },
    close () {
      this.$store.commit('editor/updateDrawerSelect', { type: 'filter', value: false })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-panel {
  position: relative;
  height: 100%;
  padding-top: 60px;
  .f-title {
    position: absolute;
    top: 0;
    width: 500px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #fafafa;
    background: #22242b;
    padding: 20px 16px;
    z-index: 999;
    .sub-t {
      font-size: 12px;
      color: #2681ff;
      margin-left: 10px;
    }
  }
  .f-content {
    height: 100%;
    padding: 0 16px 16px;
    overflow: auto;
    overflow-x: hidden;
  }
  .pointer {
    cursor: pointer;
  }
}
</style>
