<template>
  <div class="condition-page" :class="{'show': show}">
    <div class="f-title">
      <span>自定义条件编辑</span>
      <i class="el-icon-close" @click="close"></i>
    </div>
    <div class="f-content">
      <el-form label-width="100px" label-position="left" size="mini">
        <el-form-item label="判断类型">
          <el-radio-group v-model="event.conditionType">
            <el-radio label="all">满足全部条件</el-radio>
            <el-radio label="one">满足任一条件</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label-width="0px">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="addCondition">添加条件</el-button>
        </el-form-item>
        <el-form-item label-width="0px">
          <div class="filter-wrap">
            <div class="filter-item" v-for="(item, index) in event.conditions" :key="item.id">
              <div class="f-item-title">
                <div class="title-left">
                  <span class="collapse pointer" :class="item.show ? 'el-icon-arrow-down' : 'el-icon-arrow-right'" @click="toggle(item)"></span>
                  <span v-if="!item.editable" class="name">{{ item.name }}</span>
                  <el-input v-else v-model="item.name" v-select class="name" @blur="onBlur(item)"></el-input>
                  <span class="ico el-icon-edit pointer" @click="editName(item)"></span>
                </div>
                <span class="ico el-icon-delete pointer" @click="deleteCondition(index)"></span>
              </div>
              <div class="f-item-content" :class="{collapsed: !item.show}">
                <div class="content-wrap">
                  <el-form label-width="80px" label-position="left" size="mini">
                    <el-form-item label="类型">
                      <el-select v-model="item.type" class="w100">
                        <el-option value="field" label="字段"></el-option>
                        <el-option value="custom" label="自定义条件"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="设置条件" v-if="item.type === 'field'">
                      <div class="con-box">
                        <el-select v-model="item.field" class="input" placeholder="请选择" @change="reset(item)">
                          <el-option v-for="opt in fieldOpt" :key="opt.name" :value="opt.name" :label="opt.label"></el-option>
                        </el-select>
                        <el-select v-model="item.compare" class="compare">
                          <el-option :value="opt.value" :label="opt.label" v-for="opt in operator(item.field)" :key="opt.value"></el-option>
                        </el-select>
                        <template v-if="showExpectedInput(item)">
                          <el-date-picker v-model="item.expected" type="date" placeholder="选择日期" style="width: 130px;" v-if="getFieldType(item.field)=='date'"></el-date-picker>
                          <el-input v-model="item.expected" class="input" placeholder="预期值" v-else></el-input>
                        </template>
                      </div>
                    </el-form-item>
                    <el-form-item label-width="0px" v-if="item.type === 'custom'">
                      <CodeEditor v-model="item.code" />
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import CodeEditor from '../data-source/CodeEditor';
import { randomStr } from '@/utils/base';
import { mapState, mapGetters } from 'vuex';
import dataUtil from '@/utils/data';
import { COMMON_EVENTS } from '@/common/constants'
export default {
  name: 'Condition', // 自定义事件条件
  props: {
    event: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  components: {
    CodeEditor
  },
  data () {
    return {
      show: false
    }
  },
  computed: {
    ...mapState({
      screenComs: state => state.editor.screenComs,
      comsData: state => state.editor.comsData,
      screenFilters: state => state.editor.screenFilters
    }),
    ...mapGetters('editor', ['currentCom']),
    fieldOpt () { // 字段名下拉项
      if (this.currentCom) {
        if (this.event.trigger) {
          if (COMMON_EVENTS.includes(this.event.trigger)) {
            const tempOptions = []
            const filterData = this.getFilterData();
            filterData && Object.keys(filterData[0]).forEach(item => {
              if (typeof item !== 'object') {
                tempOptions.push({ name: item, label: `${item}`, type: typeof item });
              } else if (Array.isArray(item)) {
                tempOptions.push({ name: item, label: `${item}`, type: 'array' });
              }
            });
            return tempOptions
          } else {
            const event = this.currentCom.events.find(item => item.name === this.event.trigger);
            if (event) {
              let params = event.params;
              params = params.map(item => {
                return {
                  ...item,
                  label: `${item.description} (${item.name})`
                }
              }).filter(item => item.type !== 'object')
              return params
            }
          }
        }
      }
      return []
    },
    operator () {
      const vm = this;
      return function (field) {
        const opts = vm.fieldOpt.find(item => item.name === field);
        let result = [];
        let type = 'string';
        if (opts) type = opts.type || 'string';
        switch (type) {
          case 'string':
            result = [
              { label: '等于', value: '==' },
              { label: '不等于', value: '!=' },
              { label: '包含', value: 'contain' },
              { label: '不包含', value: 'notContain' },
              { label: '开头匹配', value: 'matchOnStart' },
              { label: '结尾匹配', value: 'matchOnEnd' },
              { label: '不为空', value: 'notNull' },
              { label: '为空', value: 'null' }
            ]
            break
          case 'number':
            result = [
              { label: '等于', value: '==' },
              { label: '不等于', value: '!=' },
              { label: '大于', value: '>' },
              { label: '小于', value: '<' },
              { label: '大于等于', value: '>=' },
              { label: '小于等于', value: '<=' },
              { label: '不为空', value: 'notNull' },
              { label: '为空', value: 'null' }
            ]
            break
          case 'date':
            result = [
              { label: '等于', value: '==' },
              { label: '不等于', value: '!=' },
              { label: '大于', value: '>(date)' },
              { label: '小于', value: '<(date)' },
              { label: '大于等于', value: '>=(date)' },
              { label: '小于等于', value: '<=(date)' },
              { label: '不为空', value: 'notNull' },
              { label: '为空', value: 'null' }
            ]
            break
        }
        return result
      }
    }
  },
  directives: {
    select: {
      inserted: function (el) {
        el.querySelector('input').select()
      }
    }
  },
  methods: {
    reset (item) {
      item.compare = '==';
      item.expected = '';
    },
    getFieldType (name) {
      return (this.fieldOpt.find(item => item.name === name) || {}).type || 'string';
    },
    showExpectedInput (item) {
      if (!item) return true;
      if (item.compare === 'null' || item.compare === 'notNull') return false;
      return true;
    },
    showCondition () {
      this.show = true;
    },
    close () {
      this.show = false;
    },
    toCondition (index) {
      this.show = true;
      this.$nextTick(() => {
        this.event.conditions.forEach((item, idx) => {
          if (index === idx) {
            item.show = true;
          } else {
            item.show = false;
          }
        })
      })
    },
    addCondition () {
      const condition = {
        id: 'condition_' + randomStr(10), // id 格式：condition_hdjlpsx5om
        name: '条件' + (this.event.conditions.length + 1), // 名称
        type: 'field', // 'field', 'custom'类型
        field: '', // 字段名
        compare: '==', // 比较符'==', '!=', '<', '>', '<=', '>=', 'include', 'exclude'
        expected: '', // 预期值
        code: '// 事件抛出的参数变量可直接用变量名调用\n// 回调参数调用使用callbackArgs.xxx(xxx为回调参数名)调用\nreturn true',
        show: true,
        editable: true
      }
      this.event.conditions.push(condition)
    },
    onBlur (item) { // 过滤器名称 blur
      this.$set(item, 'editable', !item.editable);
    },
    editName (item) {
      this.$set(item, 'editable', !item.editable);
    },
    deleteCondition (index) { // 删除
      this.event.conditions.splice(index, 1);
    },
    toggle (item) {
      this.$set(item, 'show', !item.show);
      // console.log(item)
    },
    save (item) {
      this.$set(item, 'show', !item.show);
    },
    // 获取过滤器以后的数据
    getFilterData () {
      const comCfg = this.screenComs[this.currentCom.id];
      let filter = [];
      if (!_.isEmpty(comCfg)) {
        const filters = comCfg.dataConfig.dataResponse.filters;
        if (!filters.enable) {
          filter = [];
        } else {
          filter = _.filter(filters.list, { enable: true }).map(({ id }) => this.screenFilters[id]);
        }
      }
      const sourceData = this.comsData[this.currentCom.id];
      const filterData = dataUtil.filterData(sourceData || [], filter);
      return filterData;
    }
  }
}
</script>

<style lang="scss" scoped>
.condition-page {
  position: fixed;
  top: 0;
  bottom: 0;
  right: -400px;
  width: 400px;
  z-index: 999;
  padding-top: 130px;
  background: #22242b;
  transition: right .5s ease-in-out;
  overflow: auto;
  &.show {
    right: 0;
  }
  .f-title {
    position: fixed;
    top: 70px;
    width: 400px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #fafafa;
    background: #22242b;
    padding: 20px 16px;
    z-index: 999;
  }
  .f-content {
    padding: 0 16px 16px;
    .filter-wrap {
      .filter-item {
        position: relative;
        margin-bottom: 10px;
        &:last-child {
          margin: 0;
        }
        .f-item-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 32px;
          background-color: #2d2f38;
          border: 1px solid #393b4a;
          border-bottom: none;
          padding-right: 10px;
          .title-left {
            width: 200px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            .name {
              color: #fafafa;
              font-size: 12px;
              width: 140px;
              margin-left: 6px;
              display: inline-block;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .ico {
              margin-left: 5px;
              &:hover {
                color: var(--seatom-main-color);
              }
            }
            .r-oprate {
              font-size: 12px;
              color: #999;
              .ico {
                font-size: 14px;
                margin-left: 10px;
                &:hover {
                  color: var(--seatom-main-color);
                }
              }
            }
          }
        }
        .f-item-content {
          transition: height .2s linear;
          border: 1px solid #393b4a;
          border-top: none;
          overflow: hidden;
          &.collapsed {
            height: 0;
          }
          .content-wrap {
            padding: 16px 16px 0;
          }
        }
      }
    }
  }
  .con-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .input {
      flex: 1;
    }
    .compare {
      width: 80px;
      margin: 0 5px;
    }
  }
  .btn-wrap {
    text-align: right;
    margin-bottom: 10px;
  }
  .w100 {
    width: 100%;
  }
  .pointer {
    cursor: pointer;
  }
  ::v-deep {
    .CodeMirror {
      height: 238px;
    }
  }
}
</style>
