<template>
  <div class="comp-interaction config-control">
    <el-collapse v-model="active" class="w100">
      <el-collapse-item title="联动" name="1">
        <template slot="title">
          <div class="c-header">
            <span class="name">联动</span>
            <div class="btn-box" v-if="active.includes('1')">
              <span class="btn" @click.stop></span>
              <span class="btn" @click.stop></span>
              <span class="btn el-icon-circle-plus" @click.stop="addLinkage"></span>
              <span class="btn el-icon-delete" @click.stop="delLinkage"></span>
            </div>
          </div>
        </template>
        <el-tabs class="tab-c" type="card" v-model="activeTab3" size="mini">
          <el-tab-pane
            :label="'联动' + (index + 1)"
            :name="item.id"
            v-for="(item, index) in linkAge"
            :key="item.id">
            <el-form
              v-if="activeTab3 == item.id"
              class="form"
              label-width="80px"
              label-position="left"
              size="mini">
              <el-form-item label="触发机制">
                <el-select
                  v-model="item.trigger"
                  @change="val => triggerChange(item, val)"
                  class="w100"
                  placeholder="请选择触发机制">
                  <el-option
                    v-for="opt in triggerOpt"
                    :key="opt.value"
                    :value="opt.value"
                    :label="opt.label" />
                </el-select>
              </el-form-item>
              <el-form-item label="联动组件">
                <CompSelect
                  :value="getLinkageComIds(item.linkageComList)"
                  multiple
                  :options="globalComs(true)"
                  @change="val => linkCompChange(val, item)" />
              </el-form-item>
              <el-form-item label="联动设置">
                <el-dropdown ref="drop" trigger="click">
                  <div class="add-config" >
                    <span class="el-icon-plus"></span>
                  </div>
                  <el-dropdown-menu slot="dropdown">
                    <el-cascader-panel
                      class="cascader-panel"
                      v-model="fieldValue"
                      v-if="linkFieldOptions(item).length"
                      :options="linkFieldOptions(item)"
                      @change="value => fieldValueChange(item, value)">
                      <template slot-scope="{ data }">
                        <span>{{ data.label }}</span>
                        <span class="cascade-des" :title="data.description" v-if="data.description">
                          {{ data.description }}
                        </span>
                      </template>
                    </el-cascader-panel>
                    <div class="input-field-wrap">
                      <div class="tips">输入联动字段，如 data.name</div>
                      <div class="tips">回调字段，如 ${data.name}</div>
                      <div class="tips">常量值，如 #{100}</div>
                      <el-input v-model.trim="inputFieldValue"/>
                      <el-button
                        class="s-btn"
                        size="mini"
                        type="primary"
                        @click.stop="fieldInputConfirm(item)">
                        确定
                      </el-button>
                    </div>
                  </el-dropdown-menu>
                </el-dropdown>
                <span v-if="isShowTips(item)" class="event-tips">
                  <span style="color: red">*</span>
                  获取数据源数组第一项数据
                </span>
              </el-form-item>
              <el-tabs
                v-model="item.activeTab"
                class="link-tab"
                type="card"
                @tab-remove="name => handleTabsRemove(item, name)">
                <el-tab-pane
                  v-for="config in item.linkageConfig"
                  :key="config.id"
                  :label="config.fieldValue"
                  closable
                  :name="config.id">
                  <el-form class="form" label-position="top" size="mini">
                    <el-form-item
                      v-for="comp in config.componentList"
                      :key="comp.componentId">
                      <div class="full" slot="label">
                        <span class="linkage-name" :class="{ active: linkedComs.includes(comp.componentId) }">
                          {{comp.componentName}}
                          <i v-if="hasEmptyItem(comp)" class="el-icon-warning-outline icon-red"></i>
                        </span>
                        <span class="linkage-tool btn" @click="editLinkage(comp)">
                          <span class="over" v-if="comp.componentId === currentLinkAge">
                            <i class="over el-icon-check"></i>
                            完成
                          </span>
                          <span v-else>
                            <i class="el-icon-edit"></i>
                            编辑
                          </span>
                        </span>
                      </div>
                      <div class="source-container" v-if="comp.componentId === currentLinkAge">
                        <!-- 联动类型：前后端 -->
                        <el-select v-model="comp.sourceType" @change="val => changeSourceType(val, comp)" class="w100 mr5 mw70">
                          <el-option
                            v-for="opt in linkTypeOpt"
                            :disabled="isDisableSource(opt, comp.componentId)"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value"/>
                        </el-select>
                        <!-- 联动字段(前端来源) -->
                        <el-select
                          v-model="comp.fieldValue"
                          @change="changeStaticField(comp)"
                          v-if="comp.sourceType === 'static'"
                          class="w100 mr5">
                          <el-option
                            v-for="opt in linkCompFields(comp)"
                            :key="opt.name"
                            :label="opt.name"
                            :value="opt.name">
                          </el-option>
                        </el-select>
                        <!-- 联动字段(后端来源) -->
                        <template v-if="comp.sourceType === 'server'">
                          <!-- dmc类型 -->
                          <el-select
                            v-model="comp.fieldId"
                            @change="changeServerField(comp, 'dmc')"
                            v-if="isSourceType(comp.componentId, 'dmc')"
                            class="w100 mr5">
                            <el-option
                              v-for="opt in currentFields(comp.componentId)"
                              :key="opt.fid"
                              :label="opt.name"
                              :value="opt.fid">
                            </el-option>
                          </el-select>
                          <!-- api类型 -->
                          <div
                            class="flex-container w100 mr5"
                            v-else-if="isSourceType(comp.componentId, 'api')">
                            <div class="input-container mr5">
                              <el-input
                                v-model="comp.fieldValue"
                                @blur="saveLinkage()"
                                placeholder="字段名" />
                            </div>
                            <!-- query/body参数 -->
                            <div class="select-container">
                              <el-select
                                v-model="comp.fieldRequestType"
                                @change="saveLinkage()">
                                <el-option
                                  v-for="opt in apiOptions"
                                  :key="opt.value"
                                  :label="opt.label"
                                  :value="opt.value" />
                              </el-select>
                            </div>
                          </div>
                          <!-- 其它类型 -->
                          <el-select
                            v-model="comp.fieldId"
                            @change="changeServerField(comp, 'mysql')"
                            v-else
                            class="w100 mr5">
                            <el-option
                              v-for="opt in currentFields(comp.componentId)"
                              :key="opt.name"
                              :label="opt.name"
                              :value="opt.name"/>
                          </el-select>
                        </template>
                        <!-- 匹配类型 -->
                        <el-select v-model="comp.compare" @change="saveLinkage()" class="w200">
                          <el-option
                            v-for="opt in linkageConditions[comp.fieldType || 'string']"
                            :key="opt.value"
                            :label="opt.text"
                            :value="opt.value"/>
                        </el-select>
                      </div>
                      <div class="source-container simple" v-else>
                        <div class="w100 mr5 flex-0">{{ getSourceType(comp.sourceType) || '--' }}</div>
                        <div class="w100 mr5" v-if="comp.sourceType === 'static'">{{ comp.fieldValue || '--' }}</div>
                        <template v-if="comp.sourceType === 'server'">
                          <!-- dmc类型 -->
                          <div class="w100 mr5" :title="comp.fieldValue" v-if="isSourceType(comp.componentId, 'dmc')">{{ comp.fieldValue || '--' }}</div>
                          <!-- api类型 -->
                          <div
                            class="flex-container w100 mr5 no-padding"
                            v-else-if="isSourceType(comp.componentId, 'api')">
                            <div class="input-container mr5" :title="comp.fieldValue">{{ comp.fieldValue || '--' }}</div>
                            <!-- query/body参数 -->
                            <div class="select-container">{{ getApiOpt(comp.fieldRequestType) || '--' }}</div>
                          </div>
                          <!-- 其它类型 -->
                          <div class="w100 mr5" :title="comp.fieldId" v-else>{{ comp.fieldId || '--' }}</div>
                        </template>
                        <div class="w200">{{ getCondition(comp.compare, linkageConditions[comp.fieldType || 'string']) || '--' }}</div>
                      </div>
                    </el-form-item>
                  </el-form>
                  <div class="no-data" v-if="config.componentList.length == 0">暂无数据</div>
                </el-tab-pane>
              </el-tabs>
              <el-form-item label="联动方式" label-width="80px">
                <el-radio-group v-model="item.linkMode" @change="saveLinkage()">
                  <el-radio label="multiple">组合联动
                    <el-tooltip class="item" effect="dark" placement="top-end">
                      <div slot="content" class="field-content">
                        当前组件触发联动时，会收集被联动组件的所有已联动过的参数执行联动逻辑。
                      </div>
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  </el-radio>
                  <el-radio label="single">单独联动
                    <el-tooltip class="item" effect="dark" placement="top-end">
                      <div slot="content" class="field-content">
                        当前组件触发联动时，仅把当前的联动字段作为参数执行联动逻辑。
                      </div>
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <template v-if="item.linkMode === 'multiple'">
                <el-form-item label-width="80px">
                  <div slot="label">
                    <span>组合条件 </span>
                    <el-tooltip class="item" effect="dark" placement="top">
                      <div slot="content" class="field-content">
                        您可以进行多选组件，与当前组件完成组合联动。如果您未做出选择，则系统将收集所有可能的组件联动信息。
                      </div>
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  </div>
                  <div class="add-config" @click="addCombinedCom(item)">
                    <span class="el-icon-plus"></span>
                  </div>
                </el-form-item>
                <div class="multiple-comp">
                  <div
                    class="comp-item"
                    v-for="(com, index) in (item.coms || [])"
                    :key="com.id"
                    @click="clickLinkComp(com)">
                    <div class="comp-name">{{ com.name }}</div>
                    <div class="operator">
                      <el-tooltip class="item" effect="dark" content="删除组件" placement="top">
                        <span class="el-icon-close" @click.stop="deleteLinkCom(item.coms, index)"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </template>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <div class="no-data" v-if="linkAge.length == 0">暂无数据</div>
      </el-collapse-item>
      <el-collapse-item title="下钻" name="2">
        <template slot="title">
          <div class="c-header">
            <span class="name">下钻</span>
            <div class="btn-box" v-if="active.includes('2')">
              <span class="btn" @click.stop></span>
              <span class="btn" @click.stop></span>
              <span class="btn el-icon-circle-plus" @click.stop="addDrillDown" v-if="!drillDown.length"></span>
              <span class="btn el-icon-delete" @click.stop="delDrillDown"></span>
            </div>
          </div>
        </template>
        <el-tabs class="tab-c" type="card" v-model="activeTab4" size="mini">
          <el-tab-pane
            :label="'下钻' + (index + 1)"
            :name="item.id"
            v-for="(item, index) in drillDown"
            :key="item.id">
            <el-form
              v-if="activeTab4 == item.id"
              class="form"
              label-width="80px"
              label-position="left"
              size="mini">
              <el-form-item label="下钻模式" v-if="drillSourceOpt.includes(sourceType)">
                <el-select v-model="item.drillType" @change="saveDrillDown()" class="w100" placeholder="请选择">
                  <el-option v-for="opt in drillOpts" :key="opt.value" :value="opt.value"
                             :label="opt.name"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="触发机制">
                <el-select v-model="item.trigger" @change="drillTriggerChange(item)" class="w100" placeholder="请选择触发机制">
                  <el-option v-for="opt in triggerOpt" :key="opt.value" :value="opt.value"
                             :label="opt.label"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="触发字段">
                <el-select v-model="item.fieldValue" @change="saveDrillDown()" class="w100" placeholder="请选择">
                  <el-option v-for="opt in fields(item)" :key="opt.name" :value="opt.name"
                             :label="opt.name"></el-option>
                </el-select>
              </el-form-item>
              <!-- 父级下钻模式 新版本已不显示 为了兼容暂时保留 -->
              <el-form-item label="父级设置" v-if="item.linkType === 'parent'">
                <el-form-item label="父级字段" label-width="80px">
                  <el-select class="select w100" v-model="item.parentField" @change="saveDrillDown()">
                    <el-option v-for="opt in fieldOptions" :key="opt.id" :label="opt.name"
                               :value="opt.name"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="父默认值" label-width="80px">
                  <el-input v-model="item.parentFieldVal" @input="saveDrillDown()"></el-input>
                </el-form-item>
              </el-form-item>
              <!-- 层级下钻 -->
              <div v-if="item.linkType === 'links'">
                <el-form-item label="下钻字段">
                  <div class="link-row">
                    <div class="links-data">
                      <div class="links-item" v-for="(link, idx) in item.links" :key="link.id">
                        <el-select class="select" v-model="link.fieldValue" @change="saveDrillDown()">
                          <el-option
                            v-for="opt in fieldOptions"
                            :key="opt.id"
                            :label="opt.name"
                            :value="opt.name">
                          </el-option>
                        </el-select>
                        <span class="icon el-icon-close" @click="delLink(item, idx)"></span>
                      </div>
                    </div>
                  </div>
                  <el-button type="plain" size="mini" @click="addLinks(item)">新增级联字段</el-button>
                </el-form-item>
                <el-form-item label="数值字段">
                  <el-select class="w100" v-model="item.sums" multiple @change="saveDrillDown()">
                    <el-option
                      v-for="opt in fieldOptions"
                      :key="opt.id"
                      :label="opt.name"
                      :value="opt.name">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="排序">
                  <div class="sort-row">
                    <el-select v-model="item.sortField" placeholder="排序字段" clearable @change="saveDrillDown()">
                      <el-option
                        v-for="opt in fieldOptions"
                        :key="opt.id"
                        :label="opt.name"
                        :value="opt.name">
                      </el-option>
                    </el-select>
                    <el-select v-model="item.sortType" placeholder="排序方式" @change="saveDrillDown()">
                      <el-option label="正序" value="asc"></el-option>
                      <el-option label="倒序" value="desc"></el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </div>
            </el-form>
          </el-tab-pane>
          <div class="no-data" v-if="drillDown.length == 0">暂无数据</div>
        </el-tabs>
      </el-collapse-item>
      <el-collapse-item title="自定义事件" name="3">
        <template slot="title">
          <div class="c-header">
            <span class="name">自定义事件</span>
            <div class="btn-box" v-if="active.includes('3')">
              <span class="btn" @click.stop></span>
              <span class="btn" @click.stop></span>
              <span class="btn el-icon-circle-plus" @click.stop="addEvt()"></span>
              <span class="btn el-icon-delete" @click.stop="deleteEvt"></span>
            </div>
          </div>
        </template>
        <el-tabs class="tab-c" type="card" v-model="activeTab" size="mini">
          <el-tab-pane
            :label="'事件' + (index + 1)"
            :name="item.id"
            v-for="(item, index) in events"
            :key="item.id">
            <el-form
              v-if="activeTab == item.id"
              class="form"
              label-width="80px"
              label-position="left"
              size="mini">
              <el-form-item label="触发机制">
                <el-select v-model="item.trigger" class="w100" placeholder="请选择触发机制">
                  <el-option v-for="opt in triggerOpt" :key="opt.value" :value="opt.value"
                             :label="opt.label"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="事件类型">
                <el-radio-group v-model="item.eventType" @change="val => eventTypeChange(val, item)">
                  <el-radio label="compEvt">控制其他组件</el-radio>
                  <el-radio label="globalEvt">仅发生动作</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="条件">
                <div class="condition-wrap">
                  <div class="con-wrap">
                    <div class="con-item" v-for="(con, idx) in item.conditions" :key="con.id"
                         @click="toCondition(index, idx)">
                      <div class="con-name">{{ con.name }}</div>
                      <span class="icon arrow el-icon-arrow-right"></span>
                      <span class="icon delete el-icon-delete"
                            @click.stop="deleteCondition(item.conditions, idx)"></span>
                    </div>
                  </div>
                  <div class="btn-w">
                    <el-button class="w100" type="primary" size="mini" @click="addCondition(index)">添加条件</el-button>
                  </div>
                </div>
              </el-form-item>
              <!-- 加key防止将动作更新到组件问题 -->
              <el-form-item key="component" label="组件" v-if="item.eventType === 'compEvt' && item.action !== 'jumpEvent'">
                <CompSelect
                  v-model="item.componentId"
                  multiple
                  :options="globalComs()"
                  @change="val => componentIdChange(val, item)" />
              </el-form-item>
              <el-form-item key="action" label="动作">
                <el-select v-model="item.action" class="w100" @change="val => actionChange(val, item)" placeholder="请选择">
                  <el-option
                    v-for="opt in actions(item)"
                    :key="opt._id"
                    :value="opt.name"
                    :label="opt.description">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="item.action === 'updateConfig'">
                <el-button type="plain" @click="editConfig(item)">编辑组件配置</el-button>
              </el-form-item>
              <el-collapse v-show="item.action === 'animationEvent'" v-model="aniActive" class="w100">
                <el-collapse-item title="动画" name="1">
                  <template slot="title">
                    <div class="c-header">
                      <span class="name">动画</span>
                      <div class="btn-box">
                        <span class="btn" @click.stop></span>
                        <span class="btn" @click.stop></span>
                        <span class="btn el-icon-circle-plus"
                              @click.stop="addAnimation(item, item.eventAnimation)"></span>
                        <span class="btn el-icon-delete" @click.stop="delAnimation(item, item.eventAnimation)"></span>
                      </div>
                    </div>
                  </template>
                  <el-tabs class="tab-c" type="card" v-model="item.activeAnimation" size="mini">
                    <el-tab-pane
                      :label="'动画' + (index + 1)"
                      :name="index + ''"
                      v-for="(animation, index) in item.eventAnimation"
                      :key="animation.value">
                      <el-form
                        class="form"
                        label-width="80px"
                        label-position="left"
                        size="mini">
                        <el-form-item label="动画类型">
                          <el-select v-model="animation.value" class="w100">
                            <el-option v-for="opt in animationOpt" :key="opt.value" :value="opt.value"
                                       :label="opt.label"></el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item label="动画时长">
                          <el-input-number
                            v-model="animation.duration"
                            controls-position="right"
                            :step="500"
                            :min="1000"
                            class="w100">
                          </el-input-number>
                        </el-form-item>
                        <el-form-item label="动画延时">
                          <el-input-number
                            v-model="animation.delay"
                            controls-position="right"
                            :step="500"
                            :min="0"
                            class="w100">
                          </el-input-number>
                        </el-form-item>
                      </el-form>
                    </el-tab-pane>
                  </el-tabs>
                  <div class="no-data" v-if="!item.eventAnimation.length">暂无数据</div>
                </el-collapse-item>
              </el-collapse>
              <template v-if="item.componentId.length > 1 && (item.action == 'show' || item.action == 'hide')">
                <el-form-item label="是否关联">
                  <el-checkbox v-model="item.isConnect" @change="val => isConnectChange(val, item)"></el-checkbox>
                </el-form-item>
                <el-form-item label="关联组件" v-if="item.isConnect">
                  <el-select v-model="item.connectCompId" multiple class="w100 comp-select">
                    <el-option
                      v-for="opt in connectOpt(item)"
                      :key="opt.value"
                      :value="opt.value"
                      :label="opt.label">
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="showFieldMap(item) && item.fieldMap.length">
                <el-form-item label="字段映射" style="margin-bottom:10px;"></el-form-item>
                <el-table class="mapping-t" :data="item.fieldMap" size="mini" style="margin-bottom:20px;">
                  <el-table-column label="字段" prop="source" width="70" align="center"></el-table-column>
                  <el-table-column label="映射" prop="target" align="center" width="100">
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.target" clearable size="mini">
                        <el-option
                          v-for="opt in fields(item)"
                          :key="opt.id"
                          :value="opt.name"
                          :label="opt.name + `${opt.description ? ` (${opt.description})` : ''}`">
                        </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="说明" prop="description"></el-table-column>
                  <el-table-column label="状态" align="center" width="60">
                    <template slot-scope="scope">
                      <span v-if="isMatch(scope.row, item) === 0">未匹配</span>
                      <span v-if="isMatch(scope.row, item) === 1" class="el-icon-check t-icon green"></span>
                      <span v-if="isMatch(scope.row, item) === 2" class="el-icon-close t-icon red"></span>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
              <template v-if="item.trigger !== '' && item.action === 'jumpEvent'">
                <el-form-item label="页内跳转">
                  <el-switch
                    v-model="item.jumpInfo.jumpIsLocal"
                    active-color="#409EFF"
                    inactive-color="#5F5F5F"
                  >
                  </el-switch>
                </el-form-item>
                <el-form-item label="跳转链接">
                  <el-input
                    type="textarea"
                    class="jump-url-item"
                    :autosize="{ minRows: 2, maxRows: 5}"
                    placeholder="请输入跳转链接"
                    v-model="item.jumpInfo.jumpUrl">
                  </el-input>
                  <span v-if="isShowTips(item)" class="event-tips"><span style="color: red">*</span> 获取数据源数组第一项数据</span>
                  <div class="jump-event-value" v-for="(ite, idx) in item.jumpInfo.paramsList" :key="idx">
                    <div class="jump-input-width" v-if="item.jumpInfo.mappingName">
                      <el-input v-model="item.jumpInfo.mappingName[idx]"></el-input>
                    </div>
                    <span class="jump-span" v-if="item.jumpInfo.mappingName">=></span>
                    <el-cascader
                      v-model="item.jumpInfo.paramsList[idx]"
                      :options="jumpOptions(item.trigger)"
                      clearable>
                    </el-cascader>
                    <div style="display: flex;">
                      <i v-if="idx !== item.jumpInfo.paramsList.length - 1"
                         style="display: block;width: 14px;height: 14px;"></i>
                      <i class="el-icon-minus" v-if="item.jumpInfo.paramsList.length !== 1"
                         @click="minusJumpEvent(item, idx)"></i>
                      <i class="el-icon-plus" v-if="idx === item.jumpInfo.paramsList.length - 1"
                         @click="addJumpEvent(item)"></i>
                    </div>
                  </div>
                </el-form-item>
              </template>
              <template v-if="item.trigger !== '' && item.action === 'anchorLocation'">
                <el-form-item label="定位组件">
                  <CompSelect
                    v-model="item.locatedCompId"
                    :multiple="false"
                    :flat="false"
                    :disableBranchNodes="true"
                    :options="globalComs(false, false)" />
                </el-form-item>
              </template>
              <template v-if="item.action === 'executeScrpit'">
                <el-form-item label="脚本">
                  <div class="condition-wrap">
                    <div class="con-wrap">
                      <div class="con-item" v-for="(con, idx) in item.scripts" :key="con.idx"
                          @click="addScript(index)">
                        <div class="con-name">{{ con.name }}</div>
                        <span class="icon arrow el-icon-arrow-right"></span>
                        <span class="icon delete el-icon-delete"
                              @click.stop="deleteScript(item.scripts, idx)"></span>
                      </div>
                    </div>
                    <div class="btn-w">
                      <el-button class="w100" type="primary" size="mini" @click="addScript(index)">{{item.scripts.length ? '编辑脚本' : '添加脚本'}}</el-button>
                    </div>
                  </div>
                </el-form-item>
              </template>
              <el-collapse v-show="item.action === 'sendRequest'" v-model="sendActive" class="w100">
                <el-collapse-item title="请求" name="1">
                  <template slot="title">
                    <div class="c-header">
                      <span class="name">请求</span>
                      <div class="btn-box">
                        <span class="btn" @click.stop></span>
                        <span class="btn" @click.stop></span>
                        <span class="btn el-icon-circle-plus"
                              @click.stop="addSend(item, item.eventSend)"></span>
                        <span class="btn el-icon-delete" @click.stop="delSend(item, item.eventSend)"></span>
                      </div>
                    </div>
                  </template>
                  <div class="config-control config-control-send">
                    <div class="config-title nowrap">执行顺序</div>
                    <el-radio-group v-model="item.executionOrder" size="mini">
                      <el-radio-button label="await">并行</el-radio-button>
                      <el-radio-button label="all">串行</el-radio-button>
                    </el-radio-group>
                  <!-- <el-form-item label="请求错误弹窗">
                    <el-switch
                      v-model="send.jumpInfo.jumpIsLocal"
                      active-color="#409EFF"
                      inactive-color="#5F5F5F"
                    />
                  </el-form-item> -->
                  </div>
                  <el-tabs class="tab-c" type="card" v-model="item.activeSend" size="mini" v-show="item.executionOrder === 'await'">
                    <el-tab-pane
                      :label="'请求' + (index + 1)"
                      :name="index + ''"
                      v-for="(send, index) in item.eventSend"
                      :key="send.reqId">
                      <el-form class="form" label-width="80px" label-position="left" size="mini">
                        <el-form-item label="编辑请求">
                          <el-button type="plain" @click="editSendReq(send)">编辑请求配置</el-button>
                        </el-form-item>
                        <el-form-item label="返回后动作">
                          <el-select v-model="send.action" class="w100" @change="val => actionChange(val, item)">
                            <el-option
                              v-for="opt in sendReqActions"
                              :key="opt._id"
                              :value="opt.name"
                              :label="opt.description">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <template v-if="send.action === 'sendReqJumpEvent'">
                          <el-form-item label="页内跳转">
                            <el-switch
                              v-model="send.jumpInfo.jumpIsLocal"
                              active-color="#409EFF"
                              inactive-color="#5F5F5F"
                            >
                            </el-switch>
                          </el-form-item>
                          <el-form-item label="跳转链接">
                            <el-input
                              type="textarea"
                              class="jump-url-item"
                              :autosize="{ minRows: 2, maxRows: 5}"
                              placeholder="请输入跳转链接"
                              v-model="send.jumpInfo.jumpUrl">
                            </el-input>
                            <span v-if="isShowTips(item)" class="event-tips"><span style="color: red">*</span> 获取数据源数组第一项数据</span>
                            <div class="jump-event-value" v-for="(ite, idx) in send.jumpInfo.paramsList" :key="idx">
                              <div class="jump-input-width" v-if="send.jumpInfo.mappingName">
                                <el-input v-model="send.jumpInfo.mappingName[idx]"></el-input>
                              </div>
                              <span class="jump-span" v-if="send.jumpInfo.mappingName">=></span>
                              <el-cascader
                                v-model="send.jumpInfo.paramsList[idx]"
                                :options="jumpOptions(item.trigger)"
                                clearable>
                              </el-cascader>
                              <div style="display: flex;">
                                <i v-if="idx !== send.jumpInfo.paramsList.length - 1"
                                  style="display: block;width: 14px;height: 14px;"></i>
                                <i class="el-icon-minus" v-if="send.jumpInfo.paramsList.length !== 1"
                                  @click="minusJumpEvent(send, idx)"></i>
                                <i class="el-icon-plus" v-if="idx === send.jumpInfo.paramsList.length - 1"
                                  @click="addJumpEvent(send)"></i>
                              </div>
                            </div>
                          </el-form-item>
                        </template>
                        <template v-if="send.action === 'sendReqTips'">
                          <el-form-item label="弹窗标题">
                            <el-input v-model="send.alert.title" />
                          </el-form-item>
                          <el-form-item label="弹窗内容">
                            <el-input
                              type="textarea"
                              v-model="send.alert.content"
                              :rows="2" />
                          </el-form-item>
                          <el-form-item label="按钮文本">
                            <el-input v-model="send.alert.buttonText" />
                          </el-form-item>
                        </template>
                        <template v-if="send.action === 'sendReqExecuteScrpit'">
                          <!-- <el-form-item label="脚本">
                            <div class="condition-wrap">
                              <div class="con-wrap">
                                <div class="con-item" v-for="(con, idx) in send.scripts" :key="con.idx"
                                    @click="addScript(index)">
                                  <div class="con-name">{{ con.name }}</div>
                                  <span class="icon arrow el-icon-arrow-right"></span>
                                  <span class="icon delete el-icon-delete"
                                        @click.stop="deleteScript(send.scripts, idx)"></span>
                                </div>
                              </div>
                              <div class="btn-w">
                                <el-button class="w100" type="primary" size="mini" @click="addScript(index)">{{send.scripts.length ? '编辑脚本' : '添加脚本'}}</el-button>
                              </div>
                            </div>
                          </el-form-item> -->
                        </template>
                        <template v-if="send.action === 'updateComp'">
                          <el-form-item key="component" label="组件" v-if="item.action !== 'jumpEvent'">
                            <CompSelect
                              v-model="send.componentId"
                              multiple
                              :options="sendGlobalComs" />
                          </el-form-item>
                        </template>
                      </el-form>
                    </el-tab-pane>
                  </el-tabs>
                  <el-steps direction="vertical" :key="'stepkey'" v-show="item.executionOrder === 'all'">
                    <template v-for="(send, index) in item.eventSend">
                      <el-step :title="'请求' + (index + 1)" :key="send.reqId">
                        <el-form class="form" label-width="80px" label-position="left" size="mini" slot="description">
                          <el-form-item label="编辑请求">
                            <el-button type="plain" @click="editSendReq(send)">编辑请求配置</el-button>
                          </el-form-item>
                        </el-form>
                      </el-step>
                    </template>
                    <el-step title="执行后动作" v-if="item.eventSend && item.eventSend.length">
                      <el-form class="form" label-width="80px" label-position="left" size="mini" slot="description">
                        <el-form-item label="返回后动作">
                          <el-select v-model="item.sendReqAllAfter.action" class="w100" @change="val => actionChange(val, item)">
                            <el-option
                              v-for="opt in sendReqActions"
                              :key="opt._id"
                              :value="opt.name"
                              :label="opt.description" />
                          </el-select>
                        </el-form-item>
                        <template v-if="item.sendReqAllAfter.action === 'sendReqJumpEvent'">
                          <el-form-item label="页内跳转">
                            <el-switch
                              v-model="item.sendReqAllAfter.jumpInfo.jumpIsLocal"
                              active-color="#409EFF"
                              inactive-color="#5F5F5F" />
                          </el-form-item>
                          <el-form-item label="跳转链接">
                            <el-input
                              type="textarea"
                              class="jump-url-item"
                              :autosize="{ minRows: 2, maxRows: 5}"
                              placeholder="请输入跳转链接"
                              v-model="item.sendReqAllAfter.jumpInfo.jumpUrl">
                            </el-input>
                            <span v-if="isShowTips(item)" class="event-tips"><span style="color: red">*</span> 获取数据源数组第一项数据</span>
                            <div class="jump-event-value" v-for="(ite, idx) in item.sendReqAllAfter.jumpInfo.paramsList" :key="idx">
                              <div class="jump-input-width" v-if="item.sendReqAllAfter.jumpInfo.mappingName">
                                <el-input v-model="item.sendReqAllAfter.jumpInfo.mappingName[idx]"></el-input>
                              </div>
                              <span class="jump-span" v-if="item.sendReqAllAfter.jumpInfo.mappingName">=></span>
                              <el-cascader
                                style="width: unset"
                                v-model="item.sendReqAllAfter.jumpInfo.paramsList[idx]"
                                :options="jumpOptions(item.trigger)"
                                clearable>
                              </el-cascader>
                              <div style="display: flex;">
                                <i v-if="idx !== item.sendReqAllAfter.jumpInfo.paramsList.length - 1"
                                  style="display: block;width: 14px;height: 14px;"></i>
                                <i class="el-icon-minus" v-if="item.sendReqAllAfter.jumpInfo.paramsList.length !== 1"
                                  @click="minusJumpEvent(item.sendReqAllAfter, idx)"></i>
                                <i class="el-icon-plus" v-if="idx === item.sendReqAllAfter.jumpInfo.paramsList.length - 1"
                                  @click="addJumpEvent(item.sendReqAllAfter)"></i>
                              </div>
                            </div>
                          </el-form-item>
                        </template>
                        <template v-if="item.sendReqAllAfter.action === 'sendReqTips'">
                          <el-form-item label="弹窗标题">
                            <el-input v-model="item.sendReqAllAfter.alert.title" />
                          </el-form-item>
                          <el-form-item label="弹窗内容">
                            <el-input
                              type="textarea"
                              v-model="item.sendReqAllAfter.alert.content"
                              :rows="2" />
                          </el-form-item>
                          <el-form-item label="按钮文本">
                            <el-input v-model="item.sendReqAllAfter.alert.buttonText" />
                          </el-form-item>
                        </template>
                        <template v-if="item.sendReqAllAfter.action === 'sendReqExecuteScrpit'">
                        </template>
                        <template v-if="item.sendReqAllAfter.action === 'updateComp'">
                          <el-form-item key="component" label="组件" v-if="item.action !== 'jumpEvent'">
                            <CompSelect
                              v-model="item.sendReqAllAfter.componentId"
                              multiple
                              :options="sendGlobalComs" />
                          </el-form-item>
                        </template>
                      </el-form>
                    </el-step>
                  </el-steps>
                  <div class="no-data" v-if="!item.eventSend || !item.eventSend.length">暂无数据</div>
                </el-collapse-item>
              </el-collapse>
            </el-form>
            <!-- 条件设置 -->
            <Condition ref="con" :event="item"/>
            <!-- 添加脚本 -->
            <ExeScript ref="exeSc" :event="item"/>
          </el-tab-pane>
        </el-tabs>
        <div class="no-data" v-if="events.length == 0">暂无数据</div>
      </el-collapse-item>
      <el-collapse-item title="回调参数" name="4" v-if="callbackOpt.length">
        <template slot="title">
          <div class="c-header">
            <span class="name">回调参数</span>
            <div class="btn-box" v-if="active.includes('4')">
              <span class="btn" @click.stop></span>
              <span class="btn" @click.stop></span>
              <span class="btn el-icon-circle-plus" @click.stop="addCallback"></span>
              <span class="btn el-icon-delete" @click.stop="delCallback"></span>
            </div>
          </div>
        </template>
        <el-tabs class="tab-c" type="card" v-model="activeTab2" size="mini">
          <el-tab-pane
            :label="'回调' + (index + 1)"
            :name="item.id"
            v-for="(item, index) in callbackParams"
            :key="item.id">
            <el-form
              v-if="activeTab2 == item.id"
              class="form"
              label-width="80px"
              label-position="left"
              size="mini">
              <el-form-item label="字段值">
                <el-select v-model="item.fieldValue" class="w100" @change="saveCallbackParams" placeholder="请输入字段值">
                  <el-option v-for="opt in callbackOpt" :key="opt.name" :value="opt.name"
                             :label="opt.description"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="变量名">
                <el-input v-model.trim="item.variableName" class="w100" @input="saveCallbackParams" placeholder="请输入变量名"></el-input>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <div class="no-data" v-if="callbackParams.length == 0">暂无数据</div>
      </el-collapse-item>
    </el-collapse>
    <ConfigPanel
      v-if="showConfig"
      :comConfig.sync="comConfig"
      @close="closeConfigPanel" />
    <SendReqPanel
      v-if="showSendReq"
      :sendReqConfig.sync="sendReqConfig"
      @close="closeSendReqPanel"
      :jumpOptions="jumpOptions(sendReqConfig.trigger)" />
    <AddCombinedCom
      ref="combined"
      @update="saveLinkage" />
  </div>
</template>

<script>
import Condition from './Condition'
import ConfigPanel from './ConfigPanel'
import ExeScript from './executeScript'
import CompSelect from './CompSelect.vue'
import SendReqPanel from './SendReqPanel'
import AddCombinedCom from './AddCombinedCom'
import { mapState, mapGetters } from 'vuex'
import { randomStr, uuid, getNodeByParam, isPanel, isFormComp } from '@/utils/base'
import animationOpt from '@/common/animationClassData'
import canvasBus from '@/utils/canvasBus'
import { getFieldList, getData } from '@/api/datastorage'
import dataUtil from '@/utils/data'
import { numberCondition, stringCondition, dateCondition, booleanCondition, COMMON_EVENTS, drillSourceOpt } from '@/common/constants'
import { getFilters } from '@/api/filter'

export default {
  name: 'CompInteraction', // 组件交互
  props: {},
  inject: ['getLayerTree', 'callbackManager'],
  components: {
    Condition,
    ConfigPanel,
    ExeScript,
    CompSelect,
    SendReqPanel,
    AddCombinedCom
  },
  data () {
    return {
      active: ['1', '2', '3', '4'],
      aniActive: '1',
      sendActive: '1',
      activeTab: '',
      activeTab2: '',
      activeTab3: '',
      activeTab4: '',
      activeAnimation: '0',
      linkAge: [], // 联动配置
      linkedComs: [], // 共同联动的组件id集合
      drillDown: [], // 下钻配置
      links: [], // 层级设置
      fieldValue: [], // 联动字段
      inputFieldValue: '', // 联动字段（input可输入）
      callbackParams: [], // 回调参数数组
      events: [], // 自定义事件
      addConfig: true, // 精简/高级-联动设置
      defaultActions: [ // 组件默认动作
        { name: 'show', description: '显示', params: [], _id: '1' },
        { name: 'hide', description: '隐藏', params: [], _id: '2' },
        { name: 'showHideSwitch', description: '显隐切换', params: [], _id: '3' },
        { name: 'animationEvent', description: '动画', params: [], _id: '4' },
        { name: 'updateConfig', description: '更新组件配置', params: [], _id: '5' }
      ],
      sendReqActions: [ // 发送请求-返回后动作
        { name: 'sendReqNormal', description: '无动作', params: [], _id: '11' },
        { name: 'sendReqTips', description: '弹窗提示', params: [], _id: '12' },
        { name: 'sendReqJumpEvent', description: '链接跳转', params: [], _id: '13' },
        // { name: 'sendReqExecuteScrpit', description: '执行脚本', params: [], _id: '14' },
        { name: 'updateComp', description: '更新组件数据', params: [], _id: '15' }
        // { name: 'sendReqLinkage', description: '触发联动', params: [], _id: '16' }
      ],
      onlyActions: [ // 仅发生动作
        { name: 'jumpEvent', description: '链接跳转', params: [], _id: '6' }, // 勿删正在调试中
        {
          name: 'switchScenePage',
          description: '切换场景页面',
          params: [
            {
              name: 'pageName',
              type: 'string',
              description: '场景页面名称, 优先级高于pageData'
            },
            {
              name: 'pageData',
              type: 'object',
              description: '场景页面data, 里面必须有pageName属性'
            }
          ],
          _id: '7'
        },
        { name: 'executeScrpit', description: '执行脚本', params: [], _id: '8' },
        { name: 'sendRequest', description: '发送请求', params: [], _id: '9' },
        { name: 'anchorLocation', description: '锚点定位', params: [], _id: '17' }
      ],
      resetCallbacks: { name: 'clearCallbackValue', description: '重置回调参数', params: [], _id: '10' },
      defTriggerOpt: [ // 默认事件类型
        { value: 'dataChange', label: '当请求完成或数据变化时' },
        { value: 'compClick', label: '点击整体组件时' },
        { value: 'compHover', label: 'hover整体组件时' },
        { value: 'compDbclick', label: '双击整体组件时' },
        { value: 'compMouseleave', label: '鼠标离开整体组件时' }
      ],
      animationOpt: animationOpt.attention,
      // 保存
      allFields: {},
      otherScreenComsData: {}, // 其他大屏组件的数据 用于父子面板联动
      otherScreenFilters: {}, // 其他大屏的过滤器信息
      showConfig: false, // 显示更新组件配置弹窗
      showSendReq: false, // 显示发送请求配置
      comConfig: null,
      sendReqConfig: null,
      // api模式对应select
      apiOptions: [
        {
          label: 'query参数',
          value: 'query'
        },
        {
          label: 'body参数',
          value: 'body'
        }
      ],
      linkTypeOpt: [{
        label: '前端',
        value: 'static'
      }, {
        label: '后端',
        value: 'server'
      }],
      linkageConditions: {
        string: stringCondition,
        number: numberCondition,
        date: dateCondition,
        boolean: booleanCondition
      },
      currentLinkAge: '', // 当前正在编辑的联动项
      drillOpts: [
        { name: '前端', value: 'static' },
        { name: '后端', value: 'server' }
      ],
      drillSourceOpt
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      childScreens: state => state.editor.childScreens,
      parentScreen: state => state.editor.parentScreen,
      childScreensComps: state => state.editor.childScreensComps,
      parentScreenComps: state => state.editor.parentScreenComps,
      sceneId: state => state.editor.sceneId,
      pageId: state => state.editor.pageId,
      screenComs: state => state.editor.screenComs,
      comsData: state => state.editor.comsData,
      screenFilters: state => state.editor.screenFilters,
      parentScreenInfo: state => state.editor.parentScreenInfo,
      parentScreenComs: state => state.editor.parentScreenComs,
      childScreenComs: state => state.editor.childScreenComs,
      layerMap: state => state.editor.layerMap,
      screenGroupData: state => state.editor.screenGroupData,
      containerData: state => state.datacontainer.containerData
    }),
    ...mapGetters('editor', ['currentCom', 'currentConfigId']),
    sourceType () {
      if (this.currentCom) {
        return this.currentCom.dataConfig.dataResponse.sourceType
      }
      return null
    },
    callbackOpt () { // 回调参数下拉项
      const callbacks = this.currentCom.callbacks || []
      return callbacks.map(item => {
        return {
          ...item,
          description: item.description ? (item.name + `（${item.description}）`) : item.name
        }
      })
    },
    getScreenNameById () { // 根据大屏id获取大屏name
      return function (id) {
        let screensOpt = [];
        // this.screenGroupData.forEach(project => {
        //   const screens = project.screens;
        //   screensOpt.push(...screens);
        // })
        screensOpt = this.screenGroupData
        const screen = screensOpt.find(s => s.id === id);
        if (screen) {
          return screen.name
        }
        return '引用大屏'
      }
    },
    layerTree () {
      return this.getLayerTree()
    },
    loadedNodes () {
      const loadedNodes = this.layerTree.data.children;
      if (this.screenInfo.screenType === 'scene') {
        if (this.pageId) {
          return loadedNodes.filter((node) => {
            return (
              node.pageId === this.pageId ||
              (node.sceneId === this.sceneId && !node.pageId)
            );
          });
        } else {
          return loadedNodes.filter((node) => {
            return node.sceneId === this.sceneId && !node.pageId;
          });
        }
      } else {
        return loadedNodes;
      }
    },
    panelComs () {
      return function (id) {
        return this.childScreenComs[id]
      }
    },
    globalComs () { // 全局组件 包含当前大屏/上级大屏/子面板的组件
      return function (noGroup, pDisable = true) {
        // 递归设置分组数据
        const self = this;
        /**
         * layers: 图层对象 screenComs: 组件集合 pName: 上级名称 sceneId: 场景id
         */
        function setGroupComp (layers, screenComs, pName = '', sceneId = '') {
          const result = [];
          layers = layers || [];
          if (sceneId) {
            layers = _.filter(layers, { sceneId });
          }
          layers.forEach(l => {
            if (l.type === 'com') {
              const currCom = _.cloneDeep(screenComs[l.id]);
              if (currCom) {
                const item = {
                  label: currCom.alias,
                  value: l.id,
                  dataConfig: currCom.dataConfig,
                  actions: currCom.actions,
                  callbackParams: currCom.interactionConfig.callbackParams,
                  linkAge: currCom.interactionConfig.linkAge,
                  comType: currCom.comType,
                  controlConfig: currCom.controlConfig,
                  config: currCom.config,
                  attr: currCom.attr,
                  show: currCom.show,
                  type: 'com',
                  customLabel: pName ? pName + '/' + currCom.alias : currCom.alias,
                  screenId: currCom.screenId,
                  sourceType: currCom.dataConfig.dataResponse.sourceType
                }
                if (self.currentConfigId === currCom.id) { // 普通组件不能控制自己，有子组件或面板类不能控制自己但能控制子级的组件
                  if (!currCom.children.length && !isPanel(currCom.comName)) return
                  item.isDisabled = true;
                }
                setPanelChildComs(item, screenComs);
                // 若有子组件，将子组件加入到父组件的children中
                const subComs = Object.values(screenComs).filter(c => c.parent === l.id && c.id !== self.currentConfigId);
                let childArr = [];
                if (subComs.length) {
                  childArr = subComs.map(subCom => {
                    return {
                      label: subCom.alias,
                      value: subCom.id,
                      dataConfig: subCom.dataConfig,
                      actions: subCom.actions,
                      callbackParams: subCom.interactionConfig.callbackParams,
                      linkAge: subCom.interactionConfig.linkAge,
                      comType: subCom.comType,
                      controlConfig: subCom.controlConfig,
                      config: subCom.config,
                      attr: subCom.attr,
                      show: subCom.show,
                      type: 'subCom',
                      customLabel: item.customLabel + '/' + subCom.alias,
                      screenId: subCom.screenId,
                      sourceType: currCom.dataConfig.dataResponse.sourceType
                    }
                  })
                }
                if (childArr.length) {
                  item.children = childArr;
                }
                result.push(item)
              }
            } else {
              const item = {
                label: l.groupName,
                value: l.id,
                type: 'group',
                isDisabled: !!noGroup,
                customLabel: pName ? pName + '/' + l.groupName : l.groupName
              }
              if (l.children && l.children.length) {
                item.children = setGroupComp(l.children, screenComs, item.customLabel)
              }
              result.push(item)
            }
          })

          return result
        }

        // 如果是面板类组件，则展子面板的组件
        function setPanelChildComs (item, screenComs) {
          if (item.value === self.screenInfo.relationCompId) return;
          if (isPanel(item.comType)) {
            const currCom = screenComs[item.value];
            if (currCom) {
              const screens = currCom.config.screens || [];
              const comps = Object.keys(self.childScreensComps).length ? self.childScreensComps : self.parentScreenComps;
              const children = screens.map(s => {
                const components = self.childScreenComs[s.id] || comps;
                if (components) {
                  const childComs = setGroupComp(self.layerMap[s.id], components, item.customLabel);
                  return {
                    label: s.key || self.getScreenNameById(s.id),
                    value: uuid('key'),
                    isDisabled: true,
                    children: childComs
                  }
                }
              })
              // 只有动态面板需要显示状态一级，其他面板只有一个状态 无需显示
              if (['interaction-container-dynamicpanel', 'interaction-container-newdynamicpanel', 'interaction-container-referpanel'].includes(currCom.comType)) {
                item.children = children;
              } else {
                item.children = children[0]?.children || [];
              }
            }
          }
        }

        let opt = [];
        const childScreens = []
        const parentScreen = []
        if (this.childScreens.length) {
          const children = this.childScreens.map(item => {
            return {
              label: item.name,
              value: item.id,
              isDisabled: true,
              children: setGroupComp(item.layerTree, this.childScreensComps),
              comType: ''
            }
          })
          childScreens.push({
            label: '子屏列表',
            value: uuid('key'),
            isDisabled: true,
            children,
            comType: ''
          })
        } else if (this.screenInfo.screenType === 'child' && this.parentScreen.length) {
          parentScreen.push({
            label: '父屏',
            value: this.screenInfo.parentId,
            isDisabled: true,
            children: setGroupComp(this.parentScreen[0].layerTree, this.parentScreenComps),
            comType: ''
          })
        }
        opt = setGroupComp(this.loadedNodes, this.screenComs);

        if (this.screenInfo.type === 'mobile') { // 移动端去掉模块组件
          opt = opt.filter(item => !item.value.startsWith('interaction-container-modulepanel'));
        }

        // 如果是子面板，则push父面板的组件选项
        if (this.screenInfo.isDynamicScreen) { // 动态面板类 显示上级大屏的组件
          const parentComs = _.cloneDeep(this.parentScreenComs);
          // delete parentComs[this.screenInfo.relationCompId]; // 删除当前面板
          const children = setGroupComp(this.layerMap[this.screenInfo.parentId], parentComs, '上级大屏');

          const pOpt = {
            label: '上级大屏',
            value: uuid('key'),
            isDisabled: pDisable,
            children: children,
            comType: ''
          }
          opt.push(pOpt);
        }
        return parentScreen.concat(childScreens).concat(opt)
      }
    },
    sendGlobalComs () { // 发送请求-对应api数据源
      function treeFilter (tree) {
        return tree.map(node => ({ ...node })).filter(node => {
          node.children = node.children && treeFilter(node.children)
          return node.sourceType === 'api' || (node.children && node.children.length)
        })
      }
      const opt = this.globalComs();
      return treeFilter(opt)
    },
    allComs () {
      return this.globalComs();
    },
    connectOpt () { // 关联组件下拉项
      return function (item) {
        let result = [];
        const comIds = item.componentId;
        if (comIds.length) {
          comIds.forEach(id => {
            const com = this.getGlobalComById(id);
            if (com) {
              result.push(com)
            }
          })
        }
        result = _.cloneDeep(result).map(item => { // 非当前大屏组件添加前缀
          const isCurr = this.isCurrScreenCom(item.value);
          if (!isCurr) {
            item.label = item.customLabel;
          }
          return item
        })
        return result
      }
    },
    triggerOpt () { // 组件事件类型下拉项
      const events = this.currentCom.events
      const options = events.map(opt => {
        return {
          value: opt.name,
          label: opt.description,
          params: opt.params,
          default: opt.default,
          immediate: opt.immediate
        }
      })
      if (this.currentCom.type === 'com') {
        return [...this.defTriggerOpt, ...options]
      }
      // 子组件只支持一个默认通用事件
      return [...this.defTriggerOpt.slice(0, 1), ...options]
    },
    actions () { // 组件动作下拉项
      return function (item) {
        if (!item.componentId.length) {
          return []
        }
        let actions = [...this.defaultActions];
        if (item.eventType === 'globalEvt') { // 仅发生动作
          // 组件控制自身 去掉显示和显隐切换
          actions = actions.filter(act => !['show', 'showHideSwitch'].includes(act.name));
          const com = this.screenComs[item.triggerId];
          // 若为子组件 去掉动画（子组件无法触发自身的动画）
          if (com && com.type === 'subCom') {
            actions = actions.filter(act => !['animationEvent'].includes(act.name));
          }
          if (this.screenInfo.screenType === 'scene') {
            actions.push(...this.onlyActions)
          } else {
            // 当前为子面板且上级大屏为场景页面
            const { screenId } = this.$route.query;
            if (screenId && this.parentScreenInfo.screenType === 'scene') {
              actions.push(...this.onlyActions)
            } else {
              // 非场景大屏 去除切换场景页面动作
              const acts = this.onlyActions.filter(item => item.name !== 'switchScenePage')
              actions.push(...acts)
            }
          }
          if (this.screenInfo.type === 'mobile') { // 移动端去掉【切换场景页面】动作
            _.remove(actions, item => item.name === 'switchScenePage');
          }
          if (this.screenInfo.type !== 'mobile') { // 非移动端去掉【锚点定位】动作
            _.remove(actions, item => item.name === 'anchorLocation');
          }
        }

        // 有分组只显示显隐和动画
        const hasGroup = (item.componentId.filter(id => id.startsWith('groups_'))).length > 0;
        if (hasGroup) {
          return this.defaultActions.slice(0, 4);
        } else if (item.componentId.length === 1) { // 读取组件内部的action
          const obj = this.getGlobalComById(item.componentId[0]);
          if (obj) {
            const dialogs = [ // 弹窗类组件只显示【自身动作】和【更新组件配置】
              'interaction-container-carousepanel',
              'interaction-container-popoverpanel',
              'interaction-container-mapShadowPanel',
              'interaction-container-statusdialogpanel'
            ]

            if (dialogs.includes(obj.comType)) {
              const updateConfig = this.defaultActions.find(item => item.name === 'updateConfig');
              return [...obj.actions, updateConfig]
            }

            if (obj.comType === 'interaction-container-datacontainer') { // 数据容器，动作为空
              return []
            }

            if (obj.callbackParams && obj.callbackParams.length) { // 存在回调参数 则加入重置回调参数动作
              if (obj.callbackParams.some(item => !!item.fieldValue && !!item.variableName)) { // fix bug
                actions.push(this.resetCallbacks)
              }
            }

            // 若为子组件 去掉动画（子组件无法触发自身的动画）
            if (obj.type === 'subCom') {
              actions = actions.filter(act => !['animationEvent'].includes(act.name));
            }

            if (obj.actions && obj.actions.length) {
              actions.push(...obj.actions)
            }
            return actions
          }
        } else { // 大于等于2个组件 去掉[更新组件配置]选项
          actions = actions.filter(item => item.name !== 'updateConfig')
        }
        return actions
      }
    },
    jumpOptions () { // 跳转链接 参数项
      return function (trigger) {
        const jumpOptions = []
        if (COMMON_EVENTS.includes(trigger)) {
          jumpOptions.push(...this.defaultDeepOptions())
        } else if (trigger === 'validatePass' || trigger === 'validateFail') {
          if (this.currentCom.config.screens.length > 0) {
            const panelId = this.currentCom.config.screens[0].id
            const panelConfig = this.panelComs(panelId)
            const options = Object.values(panelConfig)
              .filter(panelConfig)
              .map(item => {
                return {
                  value: item.config.rules.itemId || item.id,
                  label: item.alias
                }
              })
            jumpOptions.push(...options)
          }
        } else {
          if (this.currentCom.events.length > 0) {
            this.currentCom.events.forEach(item => {
              if (item.name === trigger) {
                const options = this.deepOptions(_.cloneDeep(item.params))
                jumpOptions.push(...options)
              }
            })
          }
        }
        jumpOptions.push(..._.uniqWith(this.callbackOptions(), _.isEqual))
        return jumpOptions
      }
    },
    fields () { // 下钻/自定义事件 字段下拉项
      return function (item) {
        if (item.trigger) {
          if (COMMON_EVENTS.includes(item.trigger)) {
            const opts = this.defaultDeepOptions()
            return opts.map(item => {
              return {
                name: item.value
              }
            })
          } else {
            const triggers = this.triggerOpt
            const trigger = triggers.find(a => a.value === item.trigger)
            if (trigger) {
              return trigger.params || []
            }
          }
        }
        return []
      }
    },
    isMatch () { // 匹配状态 0：未匹配 1：匹配成功 2：匹配失败
      return function (item, parent) {
        if (item.target === '' || item.target === undefined) {
          return 0
        }
        return item.target &&
        this.fields(parent).findIndex(opt => opt.name === item.target) > -1 ? 1 : 2
      }
    },
    showFieldMap () { // 是否显示字段映射 （组件数量大于1时隐藏）
      return function (item) {
        if (Array.isArray(item.componentId) && item.componentId.length > 1) {
          return false
        }
        return true
      }
    },
    getLinkageComIds () { // 被联动组件id集合getter
      return function (linkageComList) {
        const ids = linkageComList.map(item => item.componentId)
        return ids
      }
    },
    linkFieldOptions () { // 控制组件的事件回调参数options
      return function (item, callbackParam = true) {
        if (item.trigger) {
          const options = []
          if (COMMON_EVENTS.includes(item.trigger)) {
            options.push(...this.defaultDeepOptions())
          } else if (item.trigger === 'validatePass' || item.trigger === 'validateFail') {
            if (this.currentCom.config.screens.length > 0) {
              const panelId = this.currentCom.config.screens[0].id
              const panelConfig = this.panelComs(panelId)
              const formOptions = Object.values(panelConfig)
                .filter(item => isFormComp(item.comType))
                .map(item => {
                  return {
                    value: item.config.rules.itemId || item.id,
                    label: item.alias
                  }
                })
              options.push(...formOptions)
            }
          } else {
            const events = this.currentCom.events
            const evt = events.find(e => e.name === item.trigger)
            if (evt) {
              evt.params.forEach(p => {
                options.push({
                  label: p.name,
                  value: p.name,
                  type: p.type,
                  description: p.description,
                  disabled: ['object', 'Object'].includes(p.type)
                })
              })
            }
          }
          if (callbackParam) {
            options.push(..._.uniqWith(this.callbackOptions(), _.isEqual))
          }
          return options
        }
        return []
      }
    },
    linkCompFields () { // 被联动组件的field option
      return function (item) {
        if (item.componentId) {
          const com = this.getGlobalComById(item.componentId);
          if (!_.isEmpty(com)) {
            const sourceType = com.dataConfig.dataResponse.sourceType
            let comData = [];
            let screenFilters = {};
            // 数据容器数据源特殊处理
            if (sourceType === 'datacontainer') {
              const containerCom = this.getGlobalComById(com.dataConfig.dataResponse.source.datacontainer.data.dataContainerComId);
              if (containerCom) {
                comData = this.containerData[containerCom.value]?.data || [];
              }
            } else {
              const curId = (sourceType === 'dialog' || sourceType === 'inherit') ? this.screenInfo?.relationCompId : item.componentId
              const isCurrScreenCom = this.isCurrScreenCom(item.componentId);
              if (isCurrScreenCom) {
                comData = this.comsData[curId]
                screenFilters = this.screenFilters || {}
              } else {
                comData = this.otherScreenComsData[curId]
                screenFilters = this.otherScreenFilters[com.screenId] || {}
              }
            }

            if (comData) {
              let result = []
              try {
                const keys = []
                let filter = []
                const filters = com.dataConfig.dataResponse.filters
                if (!filters.enable) {
                  filter = []
                } else {
                  filter = _.filter(filters.list, { enable: true }).map(({ id }) => screenFilters[id])
                }
                const sourceData = _.cloneDeep(comData)
                const filterData = dataUtil.filterData(sourceData || [], filter)
                const fieldMapping = com.dataConfig.fieldMapping
                filterData.forEach(item => {
                  const key = Object.keys(item)
                  keys.push(...key)
                })
                const uniKeys = Array.from(new Set(keys))
                result = uniKeys.map((key, idx) => {
                  const type = fieldMapping.find(item => (item.target || item.source) === key)?.type || 'string'
                  return {
                    id: idx,
                    name: key,
                    type
                  }
                })
              } catch (e) {}
              return result
            }
          }
        }
        return []
      }
    },
    currentFields () { // 当前被联动组件的dmc field
      return function (componentId) {
        return this.allFields[componentId] || []
      }
    },
    fieldOptions () { // 下钻 字段下拉项
      let result = []
      try {
        const keys = []
        const comCfg = this.screenComs[this.currentCom.id]
        let filter = []
        if (!_.isEmpty(comCfg)) {
          const filters = comCfg.dataConfig.dataResponse.filters
          if (!filters.enable) {
            filter = []
          } else {
            filter = _.filter(filters.list, { enable: true }).map(({ id }) => this.screenFilters[id])
          }
        }
        const sourceType = comCfg.dataConfig.dataResponse.sourceType;
        let sourceData = [];
        // 数据容器-数据源特殊处理
        if (sourceType === 'datacontainer') {
          const dataContainerComId = comCfg.dataConfig.dataResponse.source.datacontainer.data.dataContainerComId;
          sourceData = this.containerData[dataContainerComId].data || [];
        } else {
          sourceData = this.comsData[this.currentCom.id]
        }
        const filterData = dataUtil.filterData(sourceData || [], filter)
        filterData.forEach(item => {
          const key = Object.keys(item)
          keys.push(...key)
        })
        const uniKeys = Array.from(new Set(keys))
        result = uniKeys.map((key, idx) => {
          return {
            id: idx,
            name: key
          }
        })
      } catch (e) {}
      return result
    },
    isShowTips () {
      return function (item) {
        if (item.trigger && (COMMON_EVENTS.includes(item.trigger))) {
          return true
        }
        return false
      }
    },
    isSourceType () { // 根据组件id判断是否是当前sourceType
      return function (id, sourceType) {
        const com = this.getGlobalComById(id);
        if (!_.isEmpty(com)) {
          const _sourceType = com.dataConfig.dataResponse.sourceType;
          if (_sourceType === 'datacontainer') { // 数据容器源特殊处理
            const dataContainerComId = com.dataConfig.dataResponse.source.datacontainer.data.dataContainerComId;
            const containerCom = this.getGlobalComById(dataContainerComId);
            if (!_.isEmpty(containerCom)) {
              const _sourceType2 = containerCom.dataConfig.dataResponse.sourceType;
              return _sourceType2 === sourceType
            }
          } else {
            return _sourceType === sourceType
          }
        }
        return false
      }
    },
    isDisableSource () { // 是否禁用联动来源
      return (opt, id) => {
        const com = this.getGlobalComById(id);
        if (!_.isEmpty(com)) {
          const sourceType = com.dataConfig.dataResponse.sourceType;
          if (sourceType === 'datacontainer') { // 数据容器源特殊处理
            const containerCom = this.getGlobalComById(com.dataConfig.dataResponse.source.datacontainer.data.dataContainerComId);
            if (!_.isEmpty(containerCom)) {
              const _sourceType = containerCom.dataConfig.dataResponse.sourceType;
              if (opt.value === 'server') {
                return ['static', 'json', 'csv_file', 'dialog', 'inherit'].includes(_sourceType);
              }
            }
          } else {
            if (opt.value === 'server') {
              return ['static', 'json', 'csv_file', 'dialog', 'inherit'].includes(sourceType);
            }
          }
          return false;
        }
        return false;
      }
    },
    isCurrScreenCom () { // 是否是当前大屏的组件
      return function (id) {
        return !!this.screenComs[id]
      }
    }
  },
  watch: {
    currentConfigId: {
      handler: function (id) {
        if (id) {
          const com = this.currentCom
          if (com) {
            const linkAge = _.cloneDeep(com.interactionConfig.linkAge || [])
            this.linkAge = linkAge.map(item => {
              return {
                id: 'link_' + randomStr(10),
                ...item,
                activeTab: item.linkageConfig.length ? item.linkageConfig[0].id : ''
              }
            })
            this.initAllFields();
            const drilldown = _.cloneDeep(com.interactionConfig.drillDown || []);
            this.drillDown = drilldown.map(item => {
              return {
                id: 'drill_' + randomStr(10),
                ...item
              }
            })
            const callbackParams = _.cloneDeep(com.interactionConfig.callbackParams || []);
            this.callbackParams = callbackParams.map(item => {
              return {
                id: 'callback_' + randomStr(10),
                ...item
              }
            })
            const events = _.cloneDeep(com.interactionConfig.events || [])
            this.events = events.map(item => {
              let eventType = 'compEvt'
              if (item.componentId.length === 1 && item.componentId[0] === id) {
                eventType = 'globalEvt'
              }
              return {
                ...item,
                eventType,
                conditions: (item.conditions || []).map(cond => {
                  return {
                    ...cond,
                    show: false,
                    editable: false
                  }
                })
              }
            })
          }
        }
        this.events.length && (this.activeTab = this.events[0].id)
        this.callbackParams.length && (this.activeTab2 = this.callbackParams[0].id)
        this.linkAge.length && (this.activeTab3 = this.linkAge[0].id)
        this.drillDown.length && (this.activeTab4 = this.drillDown[0].id)
      },
      immediate: true
    },
    events: {
      handler: function (val) {
        this.saveEvents()
      },
      deep: true
    }
  },
  mounted () {
    canvasBus.on('event_add_show', this.eventAddShow)
    this.$once('hook:beforeDestroy', () => {
      canvasBus.off('event_add_show', this.eventAddShow)
    })
  },
  methods: {
    hasEmptyItem (comp) {
      return !(comp.fieldValue || comp.fieldId);
    },
    getGlobalComById (id) { // 根据组件id获取全局组件
      return getNodeByParam(this.allComs, 'value', id);
    },
    editLinkage (comp, isAdd) {
      if (!isAdd && this.currentLinkAge === comp.componentId) {
        this.currentLinkAge = '';
      } else {
        this.currentLinkAge = comp.componentId;
        if (!this.allFields[comp.componentId] && comp.sourceType !== 'static') {
          this.getFieldList(comp.componentId);
        }
      }
    },
    getSourceType (value) {
      return this.linkTypeOpt.find(source => source.value === value)?.label || '';
    },
    getField (fields, fid) {
      return (fields || []).find(item => item.fid === fid)?.name || '';
    },
    getApiOpt (value) {
      return this.apiOptions.find(opt => opt.value === value)?.label || '';
    },
    getCondition (value, conditions = []) {
      return conditions.find(con => con.value === value)?.text || '';
    },
    changeServerField (comp, type) {
      const { componentId, fieldId } = comp
      const allFields = this.currentFields(componentId)
      let fieldValue, fieldType;
      if (type === 'dmc') {
        fieldValue = (allFields.find(item => item.fid === fieldId) || {}).name
        fieldType = (allFields.find(item => item.fid === fieldId) || {}).data_type
      } else if (type === 'mysql') {
        fieldValue = fieldId
        fieldType = (allFields.find(item => item.name === fieldId) || {}).data_type
      }
      comp.fieldValue = fieldValue
      comp.fieldType = fieldType
      this.saveLinkage()
    },
    changeStaticField (comp) {
      const allFields = this.linkCompFields(comp)
      const fieldType = (allFields.find(item => item.name === comp.fieldValue) || {}).type
      comp.fieldType = fieldType
      this.saveLinkage()
    },
    changeSourceType (val, comp) {
      comp.fieldId = '';
      comp.fieldValue = '';
      comp.compare = 'equal';
      if (val === 'server' && !this.allFields[comp.componentId]) {
        this.getFieldList(comp.componentId);
      }
      this.saveLinkage();
    },
    initAllFields () {
      let componentList = []
      // const vm = this
      this.linkAge.forEach(item => {
        const { linkageComList } = item
        linkageComList && (componentList = componentList.concat(linkageComList))
      })
      componentList.forEach(async comp => {
        const com = this.getGlobalComById(comp.componentId);
        if (com) {
          const sourceType = com.dataConfig.dataResponse.sourceType;
          const isCurrScreenCom = this.isCurrScreenCom(comp.componentId);
          if (!isCurrScreenCom) { // 如果不是当前大屏组件，需要请求getData接口
            this.getOtherScreenComData(comp.componentId, com.dataConfig.dataResponse.sourceType);
            this.getOtherScreenFilters(com.screenId)
          }
          if (['static', 'csv_file', 'json', 'dialog', 'inherit'].includes(sourceType)) {
            // 数据类型为static和csv, json时，不支持后端联动
          }
          // else {
          // if (!vm.allFields[comp.componentId]) {
          //   vm.getFieldList(comp.componentId)
          // }
          // }
        }
      })
    },
    async getOtherScreenComData (componentId) { // 获取其它大屏组件 static/csv/api/mysql数据
      const com = this.getGlobalComById(componentId);
      if (com) {
        const sourceType = com.dataConfig.dataResponse.sourceType;
        try {
          const res = await getData({
            componentId,
            type: sourceType,
            workspaceId: this.screenInfo.workspaceId
          })
          if (res && res.success) {
            const data = res.data || []
            this.$set(this.otherScreenComsData, componentId, data);
          }
        } catch (e) {}
      }
    },
    async getOtherScreenFilters (otherScreenId) {
      if (otherScreenId && !this.otherScreenFilters[otherScreenId]) {
        try {
          const res = (await getFilters({ screenId: otherScreenId }))
          if (res && res.success) {
            const data = res.data || []
            const screenFilters = _.keyBy(data, 'id')
            this.$set(this.otherScreenFilters, otherScreenId, screenFilters);
          }
        } catch (e) {
        }
      }
    },
    getFieldParams (part) {
      const params = [];
      const vm = this;
      part.forEach(id => {
        const com = vm.getGlobalComById(id);
        if (!_.isEmpty(com)) {
          let sourceType = com.dataConfig.dataResponse.sourceType || ''
          let tbid;
          let tbName;
          if (sourceType === 'datacontainer') { // 数据容器数据源特殊处理
            const containerCom = vm.getGlobalComById(com.dataConfig.dataResponse.source.datacontainer.data.dataContainerComId)
            if (containerCom) {
              sourceType = containerCom.dataConfig.dataResponse.sourceType;
            }
            tbid = (containerCom.dataConfig.dataResponse.source.dmc || { data: {} }).data.tbId
            tbName = (containerCom.dataConfig.dataResponse.source[sourceType] || { data: {} }).data.tbName
          } else {
            tbid = (com.dataConfig.dataResponse.source.dmc || { data: {} }).data.tbId
            tbName = (com.dataConfig.dataResponse.source[sourceType] || { data: {} }).data.tbName
          }
          if (!tbid && !tbName) return false;
          let data = {
            type: sourceType,
            tbid,
            needCalculate: 1,
            workspaceId: vm.screenInfo.workspaceId
          }
          if (sourceType !== 'dmc') {
            data = {
              type: sourceType,
              tbName,
              componentId: id,
              workspaceId: vm.screenInfo.workspaceId
            }
          }
          params.push(data)
        }
      })
      return params;
    },
    getFieldList: (function () {
      const ids = [];
      let timer = null;
      return function (componentId) {
        clearTimeout(timer);
        if (componentId) ids.push(componentId);
        if (ids.length === 1) {
          timer && clearTimeout(timer);
          const part = ids.splice(0, 1);
          this.handleFields(part);
        } else {
          timer = setTimeout(() => {
            const part = ids.splice(0);
            this.handleFields(part);
          }, 100)
        }
      }
    })(),
    async handleFields (part) {
      const vm = this;
      const params = vm.getFieldParams(part);
      if (!params.length) return;
      const res = await getFieldList(params);
      res && res.length && res.forEach((list, index) => {
        let fields = [];
        list && list.data.forEach(item => {
          const type = item.type;
          item.fields.forEach(item => {
            item.data_type = type;
          });
          item.fields && (fields = fields.concat(item.fields))
        })
        this.$set(vm.allFields, part[index], fields)
      })
    },
    addLinkage () { // 新增联动
      const link = {
        id: 'link_' + randomStr(10),
        triggerId: this.currentCom.id,
        trigger: '',
        sourceModule: 'seniorModule', // 联动模式-默认高级
        linkMode: 'multiple',
        linkageComList: [],
        linkageConfig: [],
        appConfig: true,
        activeTab: ''
      }
      this.setLinkDefValue(link);
      this.linkAge.push(link)
      this.activeTab3 = link.id
      this.saveLinkage()
    },
    setLinkDefValue (link) { // 设置联动默认值
      const currEvents = this.currentCom.events;
      if (currEvents.length) {
        let simEvt = null
        for (const event of currEvents) {
          if (event.default) {
            simEvt = event;
            break;
          }
        }
        if (!simEvt) {
          for (const event of currEvents) {
            if (/click/i.test(event.name)) {
              simEvt = event;
              break;
            }
          }
        }
        if (!simEvt) {
          simEvt = currEvents[0];
        }
        link.trigger = simEvt.name;
        link.immediate = !!simEvt.immediate;

        // 默认事件参数
        const currParams = simEvt.params;
        if (currParams.length) {
          let paramsEvt = null;
          for (const param of currParams) {
            if (param.default) {
              paramsEvt = param;
              break;
            }
          }
          if (!paramsEvt) {
            for (const param of currParams) {
              if (/name/i.test(param.name)) {
                paramsEvt = param;
                break;
              }
            }
          }
          if (!paramsEvt) {
            paramsEvt = currParams[0];
          }
          this.handleTabsAdd(link, paramsEvt.name)
        }
      }
    },
    // 删除联动
    delLinkage () {
      if (this.linkAge.length) {
        const index = this.linkAge.findIndex(item => item.id === this.activeTab3);
        if (index > -1) {
          this.linkAge.splice(index, 1)
          this.saveLinkage()
          if (this.linkAge.length) {
            if (this.linkAge[index]) {
              this.activeTab3 = this.linkAge[index].id;
            } else {
              this.activeTab3 = this.linkAge[index - 1].id;
            }
          } else {
            this.activeTab3 = ''
          }
        }
      }
    },
    triggerChange (item, eName) { // 联动触发机制改变 清空联动设置
      item.linkageConfig = [];
      const evt = this.triggerOpt.find(item => item.value === eName);
      if (evt) {
        item.immediate = !!evt.immediate;
      }
      this.saveLinkage();
    },
    linkCompChange (comIds, item) { // 被联动组件change
      const linkageComList = item.linkageComList;
      const linkIds = linkageComList.map(com => com.componentId);
      const linkageConfig = item.linkageConfig;
      const currId = _.xor(comIds, linkIds)[0];
      if (comIds.length) {
        this.editLinkage({
          componentId: comIds[comIds.length - 1]
        }, true)
      }
      // 删除组件
      if (comIds.length < linkIds.length) {
        const index = linkageComList.findIndex(item => item.componentId === currId);
        if (index > -1) {
          linkageComList.splice(index, 1);
        }

        if (linkageConfig.length) {
          linkageConfig.forEach(config => {
            const index = config.componentList.findIndex(com => com.componentId === currId);
            if (index > -1) {
              config.componentList.splice(index, 1);
            }
          })
        }
      }
      // 增加组件
      if (comIds.length > linkIds.length) {
        const currCom = this.getGlobalComById(currId);
        if (currCom) {
          linkageComList.push({
            componentId: currCom.value,
            componentName: currCom.label
          })
          currCom.componentId = currCom.value;
          linkageConfig.forEach(config => {
            const sourceType = this.getLinkageType(currCom);
            const fieldMapping = this.getFieldMapping(currCom);
            const fieldValue = config.fieldValue;
            let fieldId = ''; let fieldValueComp = ''; let fieldType = '';
            let mappingField = null;
            if (sourceType === 'static') {
              mappingField = this.linkCompFields(currCom).find(item => item.name === fieldValue);
              if (mappingField) {
                fieldValueComp = mappingField.name;
                fieldType = fieldMapping.find(item => item.target === fieldValue)?.type;
              }
            } else {
              mappingField = this.currentFields(currCom.componentId).find(item => item.name === fieldValue);
              if (mappingField) {
                fieldType = mappingField.data_type;
                fieldId = mappingField.fid || mappingField.name; // 兼容dmc与mysql等数据来源
                fieldValueComp = mappingField.name;
              }
            }
            config.componentList.push({
              componentId: currCom.value,
              componentName: currCom.label,
              fieldId,
              fieldType,
              compare: 'equal',
              sourceType,
              fieldValue: fieldValueComp,
              fieldRequestType: 'query'
            })
          })
        }
      }
      this.initAllFields()
      this.saveLinkage()
    },
    delComp (item, index) { // 删除联动组件
      const compId = item.linkageComList[index].componentId
      item.linkageComList.splice(index, 1)
      if (item.linkageConfig.length) {
        item.linkageConfig.forEach(config => {
          config.componentList = config.componentList.filter(com => com.componentId !== compId)
        })
      }
      this.saveLinkage()
    },
    fieldValueChange (item, value) { // 联动设置-列表切换
      if (value && value.length) {
        const fieldValue = value.join('.')
        this.handleTabsAdd(item, fieldValue)
        this.fieldValue = []
      }
      const drops = this.$refs.drop
      if (drops && drops.length) {
        drops.forEach(item => {
          item.hide()
        })
      }
    },
    fieldInputConfirm (item) { // 联动设置-列表-确定
      if (this.inputFieldValue) {
        this.handleTabsAdd(item, this.inputFieldValue)
        this.inputFieldValue = ''
      }
      const drops = this.$refs.drop
      if (drops && drops.length) {
        drops.forEach(item => {
          item.hide()
        })
      }
    },
    getLinkageType (comp) { // 获取联动组件 数据来源
      const com = this.getGlobalComById(comp.componentId);
      if (com) {
        let sourceType = com.dataConfig.dataResponse.sourceType;
        // 数据容器数据源特殊处理
        if (sourceType === 'datacontainer') {
          const containerCom = this.getGlobalComById(com.dataConfig.dataResponse.source.datacontainer.data.dataContainerComId);
          if (containerCom) {
            sourceType = containerCom.dataConfig.dataResponse.sourceType;
          }
        }
        if (['static', 'csv_file', 'json', 'dialog', 'inherit'].includes(sourceType)) return 'static';
        return 'server';
      }
      return 'static'
    },
    getFieldMapping (comp) { // 获取联动组件的字段映射关系
      const com = this.getGlobalComById(comp.componentId);
      if (com) {
        const mappings = com.dataConfig.fieldMapping;
        return mappings;
      }
      return [];
    },
    handleTabsAdd (item, fieldValue) { // 新增联动设置
      const index = item.linkageConfig.findIndex(config => config.fieldValue === fieldValue)
      let fieldType = (this.linkFieldOptions(item).find(opt => opt.value === fieldValue) || {}).type || 'string'
      if (fieldValue && fieldValue.startsWith('#{')) {
        // 常量
        const value = fieldValue.replace(/[#{}]/g, '');
        if (isNaN(Number(value))) {
          fieldType = 'string';
        } else {
          fieldType = 'number';
        }
      }
      const linkageComList = item.linkageComList
      if (index === -1) {
        const linkConfig = {
          id: 'linkage_' + randomStr(10),
          name: '',
          fieldType,
          fieldValue,
          componentList: linkageComList.map(com => {
            const sourceType = this.getLinkageType(com);
            const fieldMapping = this.getFieldMapping(com);
            let fieldId = ''; let fieldValueComp = ''; let fieldType = '';
            let mappingField = null;
            if (sourceType === 'static') { // 前端联动，根据映射字段自动选择相同字段
              mappingField = this.linkCompFields(com).find(item => item.name === fieldValue);
              if (mappingField) {
                fieldValueComp = mappingField.name;
                fieldType = fieldMapping.find(item => item.target === fieldValue)?.type;
              }
            } else { // 后端联动，根据字段名称匹配
              mappingField = this.currentFields(com.componentId).find(item => item.name === fieldValue);
              if (mappingField) {
                fieldType = mappingField.data_type;
                fieldId = mappingField.fid || mappingField.name; // 兼容dmc与mysql等数据源类型
                fieldValueComp = mappingField.name;
              }
            }
            return {
              componentId: com.componentId,
              componentName: com.componentName,
              compare: 'equal',
              sourceType,
              fieldId,
              fieldType,
              fieldValue: fieldValueComp,
              fieldRequestType: 'query'
            }
          })
        }
        item.linkageConfig.push(linkConfig)
        item.activeTab = linkConfig.id;
        this.saveLinkage()
      } else {
        item.activeTab = item.linkageConfig[index].id;
      }
    },
    handleTabsRemove (item, name) { // 删除联动设置
      const index = item.linkageConfig.findIndex(config => config.id === name);
      if (index > -1) {
        item.linkageConfig.splice(index, 1);
        if (item.linkageConfig.length) {
          if (item.linkageConfig[index]) {
            item.activeTab = item.linkageConfig[index].id;
          } else {
            item.activeTab = item.linkageConfig[index - 1].id;
          }
        } else {
          item.activeTab = '';
        }
        this.saveLinkage()
      }
    },
    addCombinedCom (item) {
      this.$refs.combined.showDialog(item);
    },
    deleteLinkCom (coms, index) {
      coms.splice(index, 1);
      this.saveLinkage();
    },
    clickLinkComp ({ id }) { // hover组合条件组件时，高亮被联动的组件
      const com = getNodeByParam(this.globalComs(), 'value', id);
      const result = [];
      if (com) {
        const linkAge = com.linkAge;
        linkAge.forEach(link => {
          const linkageComList = link.linkageComList;
          linkageComList.forEach(c => {
            result.push(c.componentId)
          })
        })
      }
      this.linkedComs = Array.from(new Set(result));
    },
    saveLinkage () { // 保存联动数据
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'interactionConfig.linkAge', value: _.cloneDeep(this.linkAge) }
        ]
      })
    },
    addDrillDown () { // 新增下钻
      const drill = {
        id: 'drill_' + randomStr(10),
        triggerId: this.currentCom.id,
        trigger: '',
        fieldValue: '',
        linkType: 'links',
        parentField: '',
        parentFieldVal: '',
        drillType: 'static',
        links: [],
        sums: [],
        openType: 'default',
        width: 500,
        height: 400
      }
      const currEvents = this.currentCom.events;
      if (currEvents.length) {
        let simEvt = null
        for (const event of currEvents) {
          if (event.default) {
            simEvt = event;
            break;
          }
        }
        if (!simEvt) {
          for (const event of currEvents) {
            if (/click/i.test(event.name)) {
              simEvt = event;
              break;
            }
          }
        }
        if (!simEvt) {
          simEvt = currEvents[0];
        }
        drill.trigger = simEvt.name;
        // 默认事件参数
        const currParams = simEvt.params;
        if (currParams.length) {
          let paramsEvt = null;
          for (const param of currParams) {
            if (param.default) {
              paramsEvt = param;
              break;
            }
          }
          if (!paramsEvt) {
            for (const param of currParams) {
              if (/name/i.test(param.name)) {
                paramsEvt = param;
                break;
              }
            }
          }
          if (!paramsEvt) {
            paramsEvt = currParams[0];
          }
          drill.fieldValue = paramsEvt.name;
        }
      }
      this.drillDown.push(drill)
      this.activeTab4 = drill.id;
      this.saveDrillDown()
    },
    delDrillDown () {
      if (this.drillDown.length) {
        const index = this.drillDown.findIndex(item => item.id === this.activeTab4);
        if (index > -1) {
          this.drillDown.splice(index, 1)
          this.saveDrillDown()
          if (this.drillDown.length) {
            if (this.drillDown[index]) {
              this.activeTab4 = this.drillDown[index].id;
            } else {
              this.activeTab4 = this.drillDown[index - 1].id;
            }
          } else {
            this.activeTab4 = '';
          }
        }
      }
    },
    addLinks (item) { // 新增级联字段
      item.links.push({
        id: 'links_' + randomStr(10),
        fieldValue: ''
      })
      this.saveDrillDown();
    },
    delLink (item, index) { // 删除级联字段
      item.links.splice(index, 1);
      this.saveDrillDown();
    },
    saveDrillDown: _.debounce(function () {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'interactionConfig.drillDown', value: _.cloneDeep(this.drillDown) }
        ]
      })
    }, 300),
    drillTriggerChange (item) { // 下钻触发机制change 清空下钻字段和父级字段
      item.fieldValue = '';
      item.parentField = '';
      this.saveDrillDown();
    },
    addCallback () { // 新增回调参数
      const callback = {
        id: 'callback_' + randomStr(10),
        name: '',
        fieldValue: '', // 字段值
        variableName: '' // 变量名
      }
      this.callbackParams.push(callback)
      this.saveCallbackParams()
      this.activeTab2 = callback.id
    },
    delCallback () { // 删除回调参数
      if (this.callbackParams.length) {
        const index = this.callbackParams.findIndex(item => item.id === this.activeTab2);
        if (index > -1) {
          this.callbackParams.splice(index, 1)
          this.saveCallbackParams()
          if (this.callbackParams.length) {
            if (this.callbackParams[index]) {
              this.activeTab2 = this.callbackParams[index].id;
            } else {
              this.activeTab2 = this.callbackParams[index - 1].id;
            }
          } else {
            this.activeTab2 = '';
          }
        }
      }
    },
    saveCallbackParams: _.debounce(function () {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'interactionConfig.callbackParams', value: _.cloneDeep(this.callbackParams) }
        ]
      })
    }, 300),
    eventAddShow (lineEventComId) {
      this.$store.commit('editor/updateEditPanelSelect', { type: 'config', value: true })
      const index = this.events.findIndex(item => {
        return !item.trigger && item.componentId.length === 1 && item.componentId.includes(lineEventComId)
      })
      index === -1 ? this.addEvt(lineEventComId) : this.activeTab = this.events[index].id
    },
    addEvt (lineEventComId = null) { // 新增事件
      // if(!this.checked()) return
      let prevCompId = lineEventComId ? [lineEventComId] : []
      if (this.events.length > 0 && !lineEventComId) {
        const compIds = this.events[this.events.length - 1].componentId
        if (compIds.length === 1 && compIds[0] === this.currentCom.id) { // 仅发生动作 清空被控制组件
          prevCompId = []
        } else {
          prevCompId = [...compIds]
        }
      }
      const evt = {
        id: 'event_' + randomStr(10), // id 格式：:"event_nlk1jh96m"
        name: '事件', // 名称
        trigger: '', // 事件类型
        eventType: 'compEvt',
        triggerId: this.currentCom.id, // 触发事件的组件id
        conditionType: 'all',
        conditions: [],
        scripts: [],
        componentId: prevCompId, // 接受事件的组件 id
        locatedCompId: '', // 被定位的组件id
        isConnect: false,
        connectCompId: [],
        action: '', // 动作
        fieldMap: [], // 字段映射
        jumpInfo: {
          jumpIsLocal: false,
          jumpUrl: '',
          paramsList: [['']],
          mappingName: ['']
        },
        executionOrder: 'await', // 发送请求的执行顺序
        sendReqErrorTips: false, // 发送请求错误弹窗
        sendReqAllAfter: { // 发送请求并行后执行动作
          action: 'sendReqNormal', // 动作
          jumpInfo: { // 跳转链接
            jumpIsLocal: false,
            jumpUrl: '',
            paramsList: [['']],
            mappingName: ['']
          },
          componentId: [], // 被联动组件
          scripts: [], // 执行函数
          alert: { // 弹窗提示
            content: '这是一段内容',
            title: '标题名称',
            buttonText: '确定'
          }
        },
        eventAnimation: [],
        eventSend: [],
        aniActive: '1',
        activeAnimation: '0',
        activeSend: '0',
        comConfig: {
          treeData: null,
          configObj: null,
          attr: null,
          show: true,
          updateConfig: {
            configObj: {},
            attr: {}
          }
        }
      }
      const currEvents = this.currentCom.events;
      if (currEvents.length) {
        let simEvt = null
        for (const event of currEvents) {
          if (event.default) {
            simEvt = event;
            break;
          }
        }
        if (!simEvt) {
          for (const event of currEvents) {
            if (/click/i.test(event.name)) {
              simEvt = event;
              break;
            }
          }
        }
        if (!simEvt) {
          simEvt = currEvents[0];
        }
        evt.trigger = simEvt.name;
      }
      this.events.push(evt)
      this.activeTab = evt.id;
    },
    deleteEvt () { // 删除事件
      if (this.events.length) {
        const index = this.events.findIndex(item => {
          return item.id === this.activeTab
        })
        if (index > -1) {
          this.events.splice(index, 1)
          if (this.events.length) {
            if (this.events[index]) {
              this.activeTab = this.events[index].id
            } else {
              this.activeTab = this.events[index - 1].id
            }
          } else {
            this.activeTab = ''
          }
        }
      }
    },
    eventTypeChange (val, item) {
      this.$nextTick(() => {
        if (val === 'compEvt') {
          item.componentId = []
        } else {
          item.componentId = [this.currentConfigId]
        }
        item.action = ''
        item.fieldMap = []
      })
    },
    addCondition (index) { // 添加条件
      this.$refs.con[index].showCondition()
    },
    deleteCondition (list, index) {
      list.splice(index, 1)
    },
    addScript (index) { // 添加执行脚本
      this.$refs.exeSc[index].showScript()
    },
    deleteScript (list, index) {
      list.splice(index, 1)
    },
    toCondition (index, idx) {
      this.$refs.con[index].toCondition(idx)
    },
    componentIdChange (val, item) { // 组件change
      item.action = ''
      item.fieldMap = []
      if (val.length < 2) { // 组件数量小于2 关联组件清空
        item.isConnect = false
        item.connectCompId = []
      }
      // 组件删除 关联组件同步删除
      item.connectCompId = item.connectCompId.filter(opt => val.includes(opt))
    },
    isConnectChange (val, item) {
      if (!val) {
        item.connectCompId = []
      }
    },
    actionChange (val, item) { // 动作change
      switch (val) {
        case 'show':
          break
        case 'hide':
          break
        case 'jumpEvent':
          item.componentId = [this.currentConfigId]
          break
        case 'updateConfig': {
          const comId = item.componentId[0]
          const com = item.eventType === 'compEvt' ? this.getGlobalComById(comId) : this.currentCom;
          if (com) {
            item.comConfig = {
              treeData: JSON.parse(com.controlConfig),
              configObj: _.cloneDeep(com.config),
              attr: _.cloneDeep(com.attr),
              show: com.show,
              updateConfig: {
                configObj: {},
                attr: {}
              }
            }
          }
          break
        }
        default:
          item.isConnect = false
          item.connectCompId = []
          break
      }
      const action = this.actions(item).find(item => item.name === val)
      if (action) {
        item.fieldMap = (action.params || []).map(v => {
          return {
            ...v,
            source: v.name,
            target: ''
          }
        })
      } else {
        item.fieldMap = []
      }
    },
    editConfig (item) { // 编辑组件配置
      this.comConfig = item.comConfig
      this.showConfig = true
    },
    editSendReq (item) { // 发送请求-编辑发送请求
      this.sendReqConfig = item
      this.showSendReq = true
    },
    closeConfigPanel () {
      this.comConfig = null
      this.showConfig = false
    },
    closeSendReqPanel () { // 发送请求-关闭发送请求
      this.showSendReq = false
    },
    saveEvents () {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'interactionConfig.events', value: _.cloneDeep(this.events) }
        ]
      })
    },
    addAnimation (item, eventAnimation) {
      const callback = {
        value: null,
        duration: 1000,
        delay: null
      }
      if (eventAnimation.some(item => !item.value)) {
        this.$message.error('动画类型不能为空')
        return
      }
      eventAnimation.push(callback)
      item.activeAnimation = eventAnimation.length - 1 + ''
    },
    delAnimation (item, eventAnimation) {
      if (eventAnimation.length && ~~item.activeAnimation !== 0) {
        eventAnimation.splice(item.activeAnimation, 1)
        item.activeAnimation = item.activeAnimation - 1 + ''
      } else {
        eventAnimation.splice(item.activeAnimation, 1)
        item.activeAnimation = '0'
      }
    },
    addSend (item, eventSend) { // 发送请求-新增发送请求
      const sendReq = {
        source: '',
        type: '',
        trigger: item.trigger,
        reqId: 'req' + randomStr(10),
        data: {
          api: {
            baseUrl: '',
            body: [['', []]],
            headers: '{}',
            method: '',
            needCookie: false,
            params: [['', []]],
            path: '',
            sourceId: ''
          }
        },
        action: 'sendReqNormal', // 动作
        jumpInfo: {
          jumpIsLocal: false,
          jumpUrl: '',
          paramsList: [['']],
          mappingName: ['']
        },
        componentId: [], // 被联动组件
        scripts: [],
        alert: { // 弹窗提示
          content: '这是一段内容',
          title: '标题名称',
          buttonText: '确定'
        }
      }
      eventSend.push(sendReq)
      item.activeSend = eventSend.length - 1 + ''
    },
    delSend (item, eventSend) {
      if (eventSend.length && ~~item.activeSend !== 0) {
        eventSend.splice(item.activeSend, 1)
        item.activeSend = item.activeSend - 1 + ''
      } else {
        eventSend.splice(item.activeSend, 1)
        item.activeSend = '0'
      }
    },
    addJumpEvent (item) {
      item.jumpInfo.mappingName.push('')
      item.jumpInfo.paramsList.push([''])
    },
    minusJumpEvent (item, idx) {
      if (item.jumpInfo.paramsList.length === 1) {
        return
      }
      item.jumpInfo.paramsList.splice(idx, 1)
    },
    callbackOptions () { // 获取系统回调参数
      const coms = Object.values(this.screenComs)
      const callbacks = []
      const tempOptions = []
      coms.forEach(item => {
        const callbackParams = item.interactionConfig.callbackParams
        if (callbackParams && callbackParams.length) {
          const callbackObjs = callbackParams.map(call => {
            return {
              comName: item.alias,
              ...call
            }
          })
          callbacks.push(...callbackObjs)
        }
      })
      callbacks.forEach(item => {
        if (item.variableName) {
          tempOptions.push({
            value: '${' + item.variableName + '}',
            label: `\${${item.variableName}}`,
            description: `回调参数 [${item.comName}]`
          })
        }
      })
      return tempOptions
    },
    defaultDeepOptions () {
      const tempOptions = []
      const filterData = this.getFilterData()
      filterData.length && Object.keys(filterData[0]).forEach(item => {
        tempOptions.push({ value: item, label: item })
      })
      return tempOptions
    },
    deepOptions (paramsArr) {
      const tempOptions = []
      paramsArr.forEach(item => {
        if (item.type !== 'object') {
          tempOptions.push({ value: item.name, label: item.name, description: item.description })
        } else {
          const tempfields = []
          const filterData = this.getFilterData()
          if (filterData.length > 0) {
            Object.keys(filterData[0]).forEach(key => {
              tempfields.push({ value: key, label: key })
            })
          }
          tempOptions.push({
            value: item.name,
            label: item.name,
            description: item.description,
            children: tempfields
          })
        }
      })
      return tempOptions
    },
    // 获取过滤器以后的数据
    getFilterData () {
      const comCfg = this.screenComs[this.currentCom.id]
      let filter = []
      if (!_.isEmpty(comCfg)) {
        const filters = comCfg.dataConfig.dataResponse.filters
        if (!filters.enable) {
          filter = []
        } else {
          filter = _.filter(filters.list, { enable: true }).map(({ id }) => this.screenFilters[id])
        }
      }
      const sourceData = this.comsData[this.currentCom.id]
      const filterData = dataUtil.filterData(sourceData || [], filter)
      return filterData
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-container {
  display: flex;
}
.icon-red {
  color: #FF475D;
}
.comp-interaction {
  position: relative;
  padding: 0;
  .c-header {
    width: 284px; // 修改 Collapse 样式后，调整位置
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-box {
      span {
        font-size: 20px;
        margin-left: 16px;
        color: #FFFFFF;
      }
    }
  }
  .mapping-t {
    line-height: 1;
    .t-icon {
      font-size: 16px;
      &.green {
        color: #00a755;
      }
      &.red {
        color: #ef5350;
      }
    }
  }
  ::v-deep > .el-collapse {
    border: none;
    .el-collapse-item {
      &:not(:first-child) {
        margin-top: 1px;
      }
    }
    .el-collapse-item__header {
      height: 55px;
      line-height: 55px;
      padding: 12px 16px;
      background: #1E2227;
      border: none;
      font-size: 14px;
      color: #FFFFFF;
    }
    .el-collapse-item__content {
      padding: 16px;
    }
  }
  ::v-deep {
    .el-radio {
      margin-right: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
    .el-input__inner {
      border-width: 0 0 1px;
    }
    // .el-collapse {
    //   border: none;
    // }
    // .el-collapse-item {
    //   &:not(:first-child) {
    //     margin-top: 1px;
    //   }
    // }
    // .el-collapse-item__header {
    //   height: 55px;
    //   line-height: 55px;
    //   padding: 12px 16px;
    //   background: #1E2227;
    //   border: none;
    //   font-size: 14px;
    //   color: #FFFFFF;
    // }
    // .el-collapse-item__content {
    //   padding: 16px;
    // }
  }
  ::v-deep .el-tabs--border-card {
    background: unset;
    border: unset;
    box-shadow: unset;
    width: 100%;
  }
  ::v-deep .el-tabs__item {
    font-size: 12px;
  }
  ::v-deep .el-tabs--card > .el-tabs__header {
    border-bottom: unset;
    .el-tabs__item {
      height: 30px;
      line-height: 28px;
      border-radius: 4px 4px 0 0;
      color: rgba(255, 255, 255, 0.7);
      border: 1px solid rgba(204, 219, 255, 0.32);
      &.is-active {
        background: #3D85FF;
        color: #FFFFFF;
        font-weight: 600;
        border-color: #3D85FF;
      }
      &:not(:first-child) {
        margin-left: 3px;
      }
    }
    .el-tabs__nav {
      border: none;
    }
    .el-tabs__nav-next, .el-tabs__nav-prev {
      line-height: 28px;
    }
    .el-tabs__nav-wrap.is-scrollable {
      padding: 0 20px;
      box-sizing: border-box;
    }
  }
  ::v-deep .el-radio .el-radio__label {
    font-size: 12px;
  }
  .form {
    ::v-deep .el-form-item__label {
      width: 100%;
      font-size: 12px;
      // color: var(--control-text-color);
      color: rgba(230, 235, 255, 0.9);
    }
  }
  .condition-wrap {
    padding: 12px;
    border: 1px solid #393b4a;
    .con-wrap {
      .con-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 28px;
        padding: 0 5px;
        border: 1px solid #393b4a;
        margin-bottom: 6px;
        cursor: pointer;
        &:hover {
          .icon {
            &.arrow {
              display: none;
            }
            &.delete {
              display: inline;
            }
          }
        }
        .con-name {
          flex: 1;
          font-size: 12px;
          color: var(--control-text-color);
          padding-right: 15px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .icon {
          font-size: 12px;
          &.delete {
            display: none;
          }
        }
      }
    }
  }
  ::v-deep .el-form-item--mini {
    margin-bottom: 15px;
    // .el-switch__core {
    //   height: 16px;
    //   &:after {
    //     width: 12px;
    //     height: 12px;
    //   }
    // }
    // .el-switch.is-checked .el-switch__core::after {
    //   margin-left: -15px;
    // }
    .jump-url-item {
      .el-textarea__inner {
        resize: none;
        color: #bfbfbf;
      }
    }
    .jump-event-value {
      display: flex;
      justify-content: space-between;
      margin-top: 4px;
      align-items: center;
      .jump-input-width {
        width: 64px;
        .el-input__inner {
          padding: 0 2px;
        }
      }
      .jump-span {
        color: #bfbfbf;
      }
      .el-cascader--mini {
        width: 96px;
      }
      i {
        margin-right: 2px;
        cursor: pointer;
      }
    }
    .el-radio {
      color: #bfbfbf;
    }
  }
  .link-tab {
    margin-bottom: 10px;
    ::v-deep {
      .el-tabs__header {
        margin-bottom: 0;
        .el-tabs__nav-wrap {
          .el-tabs__item {
            border: 1px solid #3A3F4A;
            background-color: transparent;
            color: rgba(255, 255, 255, 0.7);
            padding: 0 12px;
            &.is-active {
              color: #3D85FF;
              border-color: #3D85FF;
            }
          }
        }

      }
      .el-tabs__content {
        .el-tab-pane {
          padding: 16px;
          border: 1px solid #393b4a;
          .el-form-item {
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(204, 219, 255, 0.16);
          }
        }
      }
    }
    .full {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #FFFFFF;
      font-weight: 600;
    }
    .no-data {
      margin-bottom: 0;
    }
  }
  .link-comp-wrap {
    display: flex;
    align-content: flex-start;
    flex-wrap: wrap;
    width: 100%;
    min-height: 85px;
    padding: 8px;
    background: rgba(204, 219, 255, 0.06);
    border: 1px solid #393b4a;
    border-radius: 2px;
    position: relative;
    .comp-item {
      display: flex;
      align-items: center;
      height: 22px;
      padding: 0 8px;
      background: #191D25;
      border: 1px solid rgba(204, 219, 255, 0.32);
      border-radius: 11px;;
      margin-right: 6px;
      margin-bottom: 6px;
      .c-name {
        color: #fff;
        font-size: 12px;
        margin-right: 3px;
      }
      .el-icon {
        cursor: pointer;
      }
    }
    .add-item {
      height: 22px;
      line-height: 22px;
      cursor: pointer;
    }
  }
  .add-config {
    width: 25px;
    height: 25px;
    line-height: 23px;
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
  }
  .multiple-comp {
    .comp-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 16px;
      font-size: 12px;
      line-height: 20px;
      color: #fff;
      border: 1px solid rgba(204, 219, 255, 0.32);
      border-radius: 4px;
      margin-bottom: 4px;
      &.active {
        .comp-name {
          color: red;
        }
      }
      &:hover {
        background-color: #202737;
        .operator {
          display: block;
        }
      }
      .comp-name {
        flex: 1;
        max-width: 220px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .operator {
        display: none;
        span {
          font-size: 15px;
          margin-left: 8px;
          cursor: pointer;
        }
      }
    }
  }
  .link-row {
    display: flex;
    align-items: flex-start;
    .links-data {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      .links-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        &:hover {
          .icon {
            display: inline-block;
          }
        }
        .select {
          width: 100%;
        }
        .icon {
          display: none;
          cursor: pointer;
          margin-left: 5px;
        }
      }
    }
  }
  .sort-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 5px;
  }
  .comp-select {
    ::v-deep {
      .el-select__tags {
        .el-tag {
          .el-select__tags-text {
            display: inline-block;
            max-width: 145px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: middle;
          }
        }
      }
    }
  }
  ::v-deep {
    .el-collapse-item__wrap, .el-tabs__content {
      overflow: visible;
    }
  }
  .w100 {
    width: 100%;
  }
  .w200 {
    width: 200px;
  }
  .mt10 {
    margin-top: 10px;
  }
  .mr5 {
    margin-right: 5px;
  }
  .no-data {
    text-align: center;
    color: #fafafa;
    margin-bottom: 10px;
  }
  .linkage-name {
    &.active {
      color: var(--seatom-main-color);
    }
  }
  .linkage-tool {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-left: 10px;
    cursor: pointer;
    .over {
      color: #3D85FF;
    }
  }
  .source-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: var(--control-text-color);
    font-size: 12px;
    .input-container,
    .select-container {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .tool-tip {
        margin-left: 1%;
      }
    }
    .mw70 {
      max-width: 70px;
    }
    &.simple {
      div {
        background: rgba(204, 219, 255, 0.06);
        border-radius: 4px;
        padding: 0 15px;
        height: 28px;
        line-height: 28px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .no-padding {
        padding: 0;
        background: none;
      }
    }
  }
  .warning-info {
    font-size: 12px;
    color: var(--control-text-color);
    .text-red {
      color: #F56C6C;
    }
  }
}
.config-control {
  padding-bottom: 16px;
  margin-bottom: 10px;
  .config-title {
    width: 100px;
  }
}
.config-control-send {
  border-bottom: 1px solid #393b4a;
}
::v-deep {
  .cascader-panel {
    border: none;
    border-bottom: 1px solid #3a465a;
    border-radius: 0;
    margin-bottom: 10px;
    .el-scrollbar {
      width: 100%;
    }
    .el-cascader-menu__wrap {
      height: auto;
      max-height: 300px;
    }
    .el-cascader-node__label {
      padding: 0;
    }
    .cascade-des {
      font-size: 12px;
      color: #999;
      margin-left: 5px;
    }
  }
  .input-field-wrap {
    position: relative;
    width: 100%;
    padding: 0 10px;
    .tips {
      font-size: 12px;
      color: #a9aaac;
      padding-bottom: 5px;
    }
    .el-input {
      input {
        padding-right: 45px;
      }
    }
    .s-btn {
      position: absolute;
      right: 15px;
      bottom: 3px;
      padding: 4px 5px;
    }
  }
  .event-tips {
    font-size: 12px;
    color: #a9aaac;
    margin-left: 12px;
  }
  .el-step {
    .el-step__description {
      padding-right: 10px;
      margin-top: 0;
    }
    &.is-vertical .el-step__main {
      padding-left: 8px;
    }
    .el-step__icon {
      width: 18px;
      height: 18px;
      font-size: 12px;
      background-color: #2681ff;
    }
    .el-step__icon.is-text {
      border: none;
    }
    .el-step__icon-inner {
      font-weight: 400;
    }
    &.is-vertical .el-step__line {
      width: 2px;
      top: 0;
      bottom: 0;
      left: 8px;
      background-color: #2e343c;
    }
    &.is-vertical .el-step__title {
      font-size: 14px;
      line-height: 18px;
    }
    &.is-vertical .el-step__head {
      width: 18px;
    }
    &.error .el-step__icon {
      background: #F56C6C;
    }
  }
  .el-textarea__inner {
    color: var(--control-text-color);
  }

}
</style>
