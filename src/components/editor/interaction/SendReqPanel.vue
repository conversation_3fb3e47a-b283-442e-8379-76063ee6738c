<template>
  <div class="send-panel">
    <div class="page-title">
      <span class="icon el-icon-back" @click="$emit('close')"></span>
      <span class="title">请求类型</span>
    </div>
    <div class="page-content">
      <div class="content-block">
        <div class="config-control">
            <div class="config-title nowrap">数据源</div>
            <div class="width-div">
              <el-button size="mini" @click="useCurData" :disabled="!containSource.includes(compSourceType)" type="plain">使用当前数据源数据</el-button>
              <!-- <el-radio-group v-model="sendReqConfig.source" @change="originChange" size="mini">
                <el-radio-button :disabled="!containSource.includes(compSourceType)" label="current">当前数据源</el-radio-button>
                <el-radio-button label="other">其他数据源</el-radio-button>
              </el-radio-group> -->
            </div>
        </div>
      </div>
      <el-steps direction="vertical" :key="sourceType || 'stepkey'">
        <el-step title="配置数据">
          <div class="content-block" slot="description">
            <div class="config-control">
              <div class="config-title nowrap">数据源类型</div>
              <div class="width-div">
                <el-select v-model="sourceType" size="mini" class="w100">
                  <el-option
                    v-for="opt in sourceTypeOpt"
                    :key="opt.value"
                    :value="opt.value"
                    :label="opt.label">
                  </el-option>
                </el-select>
              </div>
            </div>
            <template>
              <div class="config-control">
                <div class="config-title nowrap">数据源</div>
                <div class="width-div">
                  <el-select
                    class="w100"
                    v-model="sourceData.sourceId"
                    clearable
                    @change="(val) => sourceChange('api', val)"
                    size="mini">
                    <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                  </el-select>
                </div>
              </div>
            </template>
          </div>
        </el-step>
        <el-step title="配置请求参数" v-if="sourceType === 'api'">
          <div class="content-block" slot="description">
            <div class="config-control">
              <div class="config-title nowrap">Base URL</div>
              <div class="width-div">
                <el-input v-model="sourceData.baseUrl" readonly size="mini" class="w100"></el-input>
              </div>
            </div>
            <div class="config-control">
              <div class="config-title nowrap">请求方式</div>
              <div class="width-div">
                <el-select v-model="sourceData.method" size="mini" class="w100">
                  <el-option v-for="opt in methodOpt" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                </el-select>
              </div>
            </div>
            <div class="eidtor-wrap">
              <div class="editor-title">请求头(JSON格式)</div>
              <CodeEditor v-model="sourceData.headers" :options="editorOpt" key="api-code"/>
            </div>
            <div class="config-control">
              <div class="config-title nowrap">路径</div>
              <div class="width-div">
                <el-input v-model="sourceData.path" size="mini"></el-input>
              </div>
            </div>
            <div class="config-control">
              <div class="config-title nowrap">Params</div>
              <div class="width-div">
                <div class="jump-event-value" v-for="(param, idx) in sourceData.params" :key="idx">
                  <div class="jump-input-width">
                    <el-input size="mini" v-model="param[0]"></el-input>
                  </div>
                  <span class="jump-span"> : </span>
                  <el-dropdown ref="drop" trigger="click">
                    <div class="add-config">
                      <!-- <span class="el-icon-plus"></span> -->
                      <el-input size="mini" :value="param[1][0]" clearable @clear="paramClear(param)"></el-input>
                    </div>
                    <el-dropdown-menu slot="dropdown">
                      <el-cascader-panel
                        size="mini"
                        class="cascader-panel"
                        v-model="param[1]"
                        :options="jumpOpts"
                        @change="paramValueChange()">
                        <template slot-scope="{ data }">
                          <span>{{ data.label }}</span>
                          <span class="cascade-des" :title="data.description" v-if="data.description">
                            {{ data.description }}
                          </span>
                        </template>
                      </el-cascader-panel>
                      <div class="input-field-wrap">
                        <div class="tips">输入对应字段，如 data.name</div>
                        <div class="tips">回调字段，如 ${data.name}</div>
                        <div class="tips">常量值，如 #{100}</div>
                        <el-input size="mini" v-model.trim="inputParamValue" />
                        <el-button
                          class="s-btn"
                          size="mini"
                          type="primary"
                          @click.stop="inputConfirm(param, 'inputParamValue')">
                          确定
                        </el-button>
                      </div>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <div style="display: flex;">
                    <i v-if="idx !== sourceData.params.length - 1"
                         style="display: block;width: 14px;height: 14px;"></i>
                    <i class="el-icon-minus cursor-pointer" @click="minusParam(sourceData.params, idx)" v-if="sourceData.params.length !== 1"></i>
                    <i class="el-icon-plus cursor-pointer" @click="addParam(sourceData.params)" v-if="idx === sourceData.params.length - 1"></i>
                  </div>
                </div>
              </div>
            </div>
          <div class="config-control" v-if="['POST', 'PUT', 'PATCH'].includes(sourceData.method)">
            <div class="config-title nowrap">Body</div>
            <div class="width-div">
              <div class="jump-event-value" v-for="(param, idx) in sourceData.body" :key="idx">
                  <div class="jump-input-width">
                    <el-input size="mini" v-model="param[0]"></el-input>
                  </div>
                  <span class="jump-span"> : </span>
                  <el-dropdown ref="drop" trigger="click">
                    <div class="add-config">
                      <el-input size="mini" :value="param[1][0]" clearable @clear="paramClear(param)"></el-input>
                    </div>
                    <el-dropdown-menu slot="dropdown">
                      <el-cascader-panel
                        size="mini"
                        class="cascader-panel"
                        v-model="param[1]"
                        :options="jumpOpts"
                        @change="paramValueChange()">
                        <template slot-scope="{ data }">
                          <span>{{ data.label }}</span>
                          <span class="cascade-des" :title="data.description" v-if="data.description">
                            {{ data.description }}
                          </span>
                        </template>
                      </el-cascader-panel>
                      <div class="input-field-wrap">
                        <div class="tips">输入对应字段，如 data.name</div>
                        <div class="tips">回调字段，如 ${data.name}</div>
                        <div class="tips">常量值，如 #{100}</div>
                        <el-input size="mini" v-model.trim="inputBodyValue" />
                        <el-button
                          class="s-btn"
                          size="mini"
                          type="primary"
                          @click.stop="inputConfirm(param, 'inputBodyValue')">
                          确定
                        </el-button>
                      </div>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <div style="display: flex;">
                    <i v-if="idx !== sourceData.body.length - 1"
                         style="display: block;width: 14px;height: 14px;"></i>
                    <i class="el-icon-minus cursor-pointer" @click="minusParam(sourceData.body, idx)" v-if="sourceData.body.length !== 1"></i>
                    <i class="el-icon-plus cursor-pointer" @click="addParam(sourceData.body)" v-if="idx === sourceData.body.length - 1"></i>
                  </div>
                </div>
            </div>
          </div>
            <div class="config-control">
              <!-- <div class="config-title nowrap">
                <el-checkbox v-model="sourceData.needCookie" size="mini" class="mr5"></el-checkbox>
                需要cookie
              </div> -->
            </div>
          </div>
        </el-step>
      </el-steps>
    </div>
  </div>
</template>

<script>
import CodeEditor from '../data-source/CodeEditor'
import { mapState, mapGetters } from 'vuex'
import { datastorageList } from '@/api/datastorage'

const sourceTypeOpt = [
  // { value: 'static', label: '静态数据' },
  // { value: 'csv_file', label: 'CSV文件' },
  // { value: 'json', label: 'JSON文件' },
  { value: 'api', label: 'API数据' }
  // { value: 'mysql', label: 'MySQL数据库' },
  // { value: 'postgresql', label: 'PostgreSQL数据库' },
  // { value: 'oracle', label: 'Oracle数据库' },
  // { value: 'dmc', label: 'DMC数据库' },
  // { value: 'mongodb', label: 'MongoDB数据库' },
  // { value: 'websocket', label: '实时数据' },
  // { value: 'dashboard', label: '仪表盘数据' }
]

const methodOpt = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
  { value: 'PATCH', label: 'PATCH' }
]

const containSource = ['api'] // 可以选择当前数据源的数据源类型

export default {
  name: 'SendReqPanel', // 发送请求配置
  props: {
    sendReqConfig: {
      type: Object,
      default () {
        return {
          source: '',
          type: '',
          reqId: '',
          data: {
            api: {
              baseUrl: '',
              body: [['', []]],
              headers: '{}',
              method: '',
              needCookie: false,
              params: [['', []]],
              path: '',
              sourceId: ''
            }
          }
        }
      }
    },
    jumpOptions: { // 选项参数
      type: Array,
      default: () => {
        return []
      }
    }
  },
  components: {
    CodeEditor
  },
  data () {
    return {
      sourceTypeOpt: sourceTypeOpt,
      containSource: containSource,
      methodOpt: methodOpt,
      sourceList: [], // 数据源列表
      editorOpt: {
        mode: 'application/json',
        lint: true
      },
      inputParamValue: '', // params参数
      inputBodyValue: '' // body参数
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    ...mapGetters('editor', ['currentCom']),
    sourceType: { // 数据源类
      get () {
        return this.sendReqConfig.type
      },
      set (val) {
        this.sendReqConfig.type = val
      }
    },
    sourceData () { // 当前发送请求数据
      // const sourceType = this.sendReqConfig.type
      return this.sendReqConfig.data[this.sourceType]
    },
    compSourceType () { // 当前组件数据源类型
      return this.currentCom.dataConfig.dataResponse.sourceType
    },
    jumpOpts () {
      return this.jumpOptions.map(item => {
        const { children, ...opt } = item
        return {
          ...opt,
          disabled: !!children
        }
      })
    }
  },
  watch: {
    sourceType: { // 数据源类型change 设置静态数据或请求数据源列表
      handler: function (val) {
        this.getDatasourceList(val)
      },
      immediate: true
    }
  },
  created () {
    this.initData()
  },
  methods: {
    initData () {
      if (!this.sendReqConfig.source) {
        if (this.containSource.includes(this.compSourceType)) {
          this.useCurData()
        } else {
          this.sendReqConfig.source = 'other'
          this.sendReqConfig.type = 'api'
        }
      }
    },
    useCurData () { // 使用当前数据源数据
      const { baseUrl, headers, method, path, sourceId } = this.currentCom.dataConfig.dataResponse.source[this.compSourceType].data
      this.sendReqConfig.source = 'current'
      this.sendReqConfig.type = this.compSourceType
      _.merge(this.sendReqConfig.data[this.compSourceType], { baseUrl, headers, method, path, sourceId })
    },
    async getDatasourceList (sourceType) { // 获取数据源列表
      const data = {
        workspaceId: this.screenInfo.workspaceId,
        type: sourceType
      }
      const res = await datastorageList(data)
      if (res && res.success) {
        if (res.data.length === 0) {
          return;
        }
        this.sourceList = res.data.map(item => {
          return {
            ...item,
            id: item.id + ''
          }
        })
      }
    },
    sourceChange (type, val) { // 数据源更改
      if (type === 'api') {
        const source = this.sourceList.find(item => item.id === val)
        if (source && source.config) {
          this.sendReqConfig.data.api.baseUrl = source.config.baseUrl
        } else {
          this.sendReqConfig.data.api.baseUrl = ''
        }
      }
      // this.commitUpdate(type)
    },
    originChange (type) { // 数据源来源更改
    },
    addParam (item) {
      item.push(['', []])
    },
    minusParam (item, idx) {
      if (item.length === 1) {
        return
      }
      item.splice(idx, 1)
    },
    paramClear (item) { // 清除param参数
      if (item[1] && item[1][0]) {
        this.$set(item, 1, [])
      }
    },
    inputConfirm (item, type) { // param参数-确定
      if (this[type]) {
        this.$set(item[1], 0, this[type])
        this[type] = ''
      }
      const drops = this.$refs.drop
      if (drops && drops.length) {
        drops.forEach(item => {
          item.hide()
        })
      }
    },
    paramValueChange () { // 参数-列表切换
      const drops = this.$refs.drop
      if (drops && drops.length) {
        drops.forEach(item => {
          item.hide()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.send-panel {
  position: fixed;
  top: 70px;
  right: 0;
  width: 332px;
  height: calc(100% - 70px);
  background: #1c2025;
  overflow: auto;
  overflow-x: hidden;
  z-index: 100;
  display: flex;
  flex-direction: column;
  .page-title {
    position: relative;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    span.icon {
      float: left;
      font-size: 16px;
      margin: 12px;
      cursor: pointer;
    }
    span.title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .page-content {
    flex: 1;
    padding: 20px 0;
    overflow: auto;
  }
  .content-block {
    .config-control {
      padding: 4px 0 4px 8px;
      margin-bottom: 10px;
      .config-title {
        width: 100px;
      }
    }
    .eidtor-wrap {
      margin-bottom: 10px;
    }
    .flex-row {
      display: flex;
      justify-content: space-between;
      .ml10 {
        margin-left: 10px;
      }
      .table-name {
        flex: none;
        width: 110px;
        line-height: 28px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .field-box {
      width: 100%;
      min-height: 85px;
      background: #0c0b0b;
      padding-bottom: 8px;
      border-radius: 8px;
      .field-div {
        &.add-field {
          & i {
            color:#2681ff;
          }
          color:#2681ff;
          border-color:#2681ff;
        }
        display: inline-block;
        cursor: pointer;
        margin-left: 8px;
        margin-top: 8px;
        padding: 0px 8px;
        background: #191D25;
        line-height: 22px;
        border-radius: 16px;
        border: 1px solid rgba(76, 82, 95, 0.32);
        ::v-deep {
          .el-input__inner {
            height: 22px;
            border: none;
            padding-right: 20px;
            background: none;
          }
          .el-input__icon {
            width: 13px;
            height: 22px;
            line-height: 22px;
            cursor: pointer;
          }
          .el-select {
            max-width: 105px;
          }
        }
      }
    }
  }
  ::v-deep {
    .mapping-t {
      line-height: 1;
      .t-icon {
        font-size: 16px;
        &.green {
          color: #00a755;
        }
        &.red {
          color: #ef5350;
        }
      }
    }
    .el-input-number--mini {
      width: 90px;
    }
    .CodeMirror {
      height: 238px;
    }
    .el-steps {
      height: calc(100% - 100px);
    }
    .el-step {
      .el-step__description {
        padding-right: 10px;
        margin-top: 0;
      }
      &.is-vertical .el-step__main {
        padding-left: 8px;
      }
      .el-step__icon {
        width: 18px;
        height: 18px;
        font-size: 12px;
        background-color: #2681ff;
      }
      .el-step__icon.is-text {
        border: none;
      }
      .el-step__icon-inner {
        font-weight: 400;
      }
      &.is-vertical .el-step__line {
        width: 2px;
        top: 0;
        bottom: 0;
        left: 8px;
        background-color: #2e343c;
      }
      &.is-vertical .el-step__title {
        font-size: 14px;
        line-height: 18px;
      }
      &.is-vertical .el-step__head {
        width: 18px;
      }
      &.error .el-step__icon {
        background: #F56C6C;
      }
    }
  }
  // .add-config {
  //   width: 25px;
  //   height: 25px;
  //   line-height: 23px;
  //   font-size: 12px;
  //   border: 1px solid #ddd;
  //   border-radius: 3px;
  //   text-align: center;
  //   cursor: pointer;
  // }
  .result-btn {
    text-align: center;
    margin-top: 20px;
    padding-bottom: 30px;
  }
  .w45_px {
    width: 45px !important;
  }
  .w100 {
    width: 100%;
  }
  .pb10 {
    padding-bottom: 10px;
  }
  .tips {
    padding-left: 8px;
    font-size: 12px;
    color: #999;
  }
}
::v-deep {
  .cascade-des {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
  }
  // .el-switch__core {
  //   height: 16px;
  //   &:after {
  //     width: 12px;
  //     height: 12px;
  //   }
  // }
  // .el-switch.is-checked .el-switch__core::after {
  //   margin-left: -15px;
  // }
  .jump-url-item {
    .el-textarea__inner {
      resize: none;
      color: #bfbfbf;
    }
  }
  .jump-event-value {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    align-items: center;
    .jump-input-width {
      // width: 64px;
      .el-input__inner {
        padding: 0 2px;
      }
    }
    .jump-span {
      color: #bfbfbf;
    }

    // i {
    //   margin-right: 2px;
    //   cursor: pointer;
    // }
  }
  .el-radio {
    color: #bfbfbf;
  }
  .cascader-panel {
    border: none;
    border-bottom: 1px solid #3a465a;
    border-radius: 0;
    margin-bottom: 10px;
    .el-scrollbar {
      width: 100%;
    }
    .el-cascader-menu__wrap {
      height: auto;
      max-height: 300px;
    }
    .el-cascader-node__label {
      padding: 0;
    }
    .cascade-des {
      font-size: 12px;
      color: #999;
      margin-left: 5px;
    }
  }
  .input-field-wrap {
    position: relative;
    width: 100%;
    padding: 0 10px;
    .tips {
      font-size: 12px;
      color: #a9aaac;
      padding-bottom: 5px;
    }
    .el-input {
      input {
        padding-right: 45px;
      }
    }
    .s-btn {
      position: absolute;
      right: 15px;
      bottom: 3px;
      padding: 4px 5px;
    }
  }
}
.cursor-pointer {
  cursor: pointer;
}
</style>
<style lang="scss">
.popper__arrow {
  display: none !important;
}
// .param-cascader {
//   .cascade-des {
//     font-size: 12px;
//     color: #999;
//     margin-left: 5px;
//   }
// }
</style>
