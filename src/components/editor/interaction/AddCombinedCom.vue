<template>
  <el-dialog
    class="add-combined-com"
    :title="title"
    :visible.sync="show"
    top="0px"
    width="500px"
    append-to-body>
    <div class="com-tips">您可以进行多选组件，与当前组件完成组合联动</div>
    <div class="refer-list" style="margin: 0;">
      <div class="refer-item" v-for="item in comList" :key="item.id">
        <el-checkbox v-model="item.check"></el-checkbox>
        <div class="refer-icon">
          <img :src="item.icon">
        </div>
        <div class="refer-name">{{ item.alias }}</div>
      </div>
      <EmptyBlock
        v-if="comList.length === 0"
        emptyText="暂无数据"
        style="margin-top: 50px;" />
    </div>
    <div slot="footer">
      <el-button type="plain" size="mini" @click="submit">确定</el-button>
      <el-button type="text" size="mini" @click="closeDialog">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import EmptyBlock from '@/components/editor/public-manage/datacontainer/EmptyBlock'
import { isPanel } from '@/utils/base'
import { mapState } from 'vuex'
export default {
  name: 'AddCombinedCom', // 添加组合联动组件
  inject: ['getLayerTree'],
  components: { EmptyBlock },
  data () {
    return {
      title: '联动设置',
      show: false,
      linkAge: null,
      comList: [{}]
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      screenComs: state => state.editor.screenComs,
      parentScreenComs: state => state.editor.parentScreenComs,
      childScreenComs: state => state.editor.childScreenComs,
      layerMap: state => state.editor.layerMap
    }),
    layerTree () {
      return this.getLayerTree()
    }
  },
  methods: {
    showDialog (data) {
      this.show = true;
      this.linkAge = data;
      this.getCombinedComs();
    },
    closeDialog () {
      this.show = false;
    },
    getCombinedComs () {
      const vm = this;
      const linkedComs = this.linkAge.linkageComList; // 当前被联动组件
      function hasSameIdObject (arr1, arr2) {
        for (let i = 0; i < arr1.length; i++) {
          for (let j = 0; j < arr2.length; j++) {
            if (arr1[i].componentId === arr2[j].componentId) {
              return true; // 存在id相同的对象
            }
          }
        }
        return false;
      }
      function getCombinedComs (layers, screenComs, folderName) {
        const result = [];
        layers.forEach(l => {
          if (l.type === 'com') {
            const currCom = _.cloneDeep(screenComs[l.id]);
            if (currCom) {
              if (currCom.id !== vm.linkAge.triggerId) { // 排除当前组件
                const linkAge = currCom.interactionConfig.linkAge;
                if (linkAge.length) {
                  const flag = linkAge.some(link => {
                    const linkageComList = link.linkageComList;
                    return hasSameIdObject(linkedComs, linkageComList)
                  })
                  if (flag) {
                    result.push({
                      ...currCom,
                      alias: (folderName ? (folderName + ' / ') : '') + currCom.alias
                    })
                  }
                }
                if (isPanel(currCom.comType)) {
                  const screens = currCom.config.screens || [];
                  screens.forEach(s => {
                    const components = vm.childScreenComs[s.id];
                    if (components) {
                      const r = getCombinedComs(vm.layerMap[s.id], components);
                      result.push(...r.map(t => {
                        return {
                          ...t,
                          alias: (folderName ? (folderName + ' / ') : '') + currCom.alias + ' / ' + t.alias
                        }
                      }))
                    }
                  })
                }
              }
            }
          } else {
            if (l.children && l.children.length) {
              const r = getCombinedComs(l.children, screenComs, folderName);
              result.push(...r);
            }
          }
        })
        return result
      }

      const result = getCombinedComs(this.layerTree.data.children, this.screenComs);
      if (this.screenInfo.isDynamicScreen) {
        const parentComs = _.cloneDeep(this.parentScreenComs);
        const r = getCombinedComs(this.layerMap[this.screenInfo.parentId], parentComs, '上级大屏');
        result.push(...r);
      }
      const coms = this.linkAge.coms || [];
      this.comList = result.map(item => {
        return {
          check: coms.findIndex(c => c.id === item.id) > -1,
          ...item
        }
      });
    },
    submit () {
      const result = this.comList.filter(item => item.check);
      if (result.length) {
        const data = result.map(item => {
          return {
            id: item.id,
            name: item.alias
          }
        })
        this.$set(this.linkAge, 'coms', data);
      } else {
        this.$message.warn('请至少选择一个组件');
        return
      }
      this.$emit('update');
      this.closeDialog();
    }
  }
}
</script>

<style lang="scss">
.add-combined-com {
  .com-tips {
    font-size: 14px;
    color:rgba(255, 255, 255, 0.7);
  }
  .refer-list {
    padding: 12px 5px;
    height: 440px;
    margin-top: 30px;
    overflow: auto;
    .refer-item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      height: 50px;
      padding: 8px 16px 8px 8px;
      border-radius: 8px;
      &:hover {
        background: rgba(204, 219, 255, 0.1);
      }
      .refer-icon {
        flex: none;
        width: 55px;
        height: 36px;
        border: 1px solid #3D85FF;
        margin-left: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .refer-name {
        flex: 1;
        font-family: 'PingFang SC';
        color: #fff;
        padding: 0 8px;
      }
    }
  }
}
</style>
