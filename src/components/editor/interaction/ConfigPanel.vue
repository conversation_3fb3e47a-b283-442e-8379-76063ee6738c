<template>
  <div class="config-panel">
    <div class="page-title">
      <span class="icon el-icon-back" @click="$emit('close')"></span>
      <span class="title">组件样式</span>
    </div>
    <div class="page-content">
      <div class="basic-attributes">
        <div class="attributes">
          <div class="title basic-info">基本属性</div>
          <div class="info">
            <div class="info-name">位置尺寸</div>
            <div class="info-content">
              <div class="content-position-xy">
                <el-input-number
                  v-model="comConfig.attr.x"
                  @change="val => inputChange('x', val)"
                  controls-position="right"
                  :min="0"
                  size="mini"
                  id="id-input-position-X"
                >
                </el-input-number>
                <i class="icon-placeholder"></i>
                <el-input-number
                  v-model="comConfig.attr.y"
                  @change="val => inputChange('y', val)"
                  controls-position="right"
                  :min="0"
                  size="mini"
                  id="id-input-position-Y"
                >
                </el-input-number>
              </div>
              <div class="content-position-xy">
                <el-input-number
                  v-model="comConfig.attr.w"
                  @change="val => inputChange('w', val)"
                  controls-position="right"
                  :min="0"
                  size="mini"
                  id="id-input-position-W"
                >
                </el-input-number>
                <i class="icon-placeholder"></i>
                <!-- 此处有循环赋值问题 <i class="icon-placeholder pointer" :class="[isRelation ? 'el-icon-lock' : 'el-icon-unlock']" @click="isRelation = !isRelation"></i> -->
                <el-input-number
                  v-model="comConfig.attr.h"
                  @change="val => inputChange('h', val)"
                  controls-position="right"
                  :min="0"
                  size="mini"
                  id="id-input-position-H"
                >
                </el-input-number>
              </div>
            </div>
          </div>
          <div class="info">
            <div class="info-name">透明度</div>
            <div class="info-content contant-opacity">
              <el-slider
                v-model="opacityVal"
                :min="0"
                :max="1"
                :step="0.05"
                @change="changeOpacity"
              >
              </el-slider>
              <el-input-number
                v-model="opacityVal"
                controls-position="right"
                :min="0"
                :max="1"
                :step="0.05"
                size="mini"
                @change="changeOpacity"
              >
              </el-input-number>
            </div>
          </div>
          <div class="info">
            <div class="info-name" style="margin-top: 0;">默认隐藏</div>
            <div class="info-content contant-opacity">
              <el-checkbox v-model="isHideDefault" style="margin-left: 4px;">
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>
      <ConfigTree
        :treeData="comConfig.treeData"
        :configObj="comConfig.configObj"
        @change="handleConfigTreeChange">
      </ConfigTree>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfigPanel', // 更新组件配置
  props: {
    comConfig: {
      type: Object,
      default () {
        return {
          treeData: {},
          configObj: {},
          attr: {},
          show: true,
          updateConfig: { // 增量更新
            configObj: {},
            attr: {}
          }
        }
      }
    }
  },
  data () {
    return {
      opacityVal: 0
    }
  },
  computed: {
    isHideDefault: {
      get: function () {
        return !this.comConfig.show;
      },
      set: function (value) {
        this.comConfig.show = !value;
        _.set(this.comConfig.updateConfig, 'show', !value);
      }
    }
  },
  created () {
    this.opacityVal = this.comConfig.attr.opacity;
  },
  mounted () {
    this.addInputSuffix()
  },
  methods: {
    handleConfigTreeChange ({ path, value }) {
      _.set(this.comConfig.configObj, path, value);
      _.set(this.comConfig.updateConfig.configObj, path, value);
    },
    changeOpacity (val) {
      _.set(this.comConfig, 'attr.opacity', this.opacityVal);
      _.set(this.comConfig.updateConfig.attr, 'opacity', this.opacityVal);
    },
    inputChange (path, value) {
      _.set(this.comConfig.updateConfig.attr, path, value);
    },
    addInputSuffix () {
      const suffixList = ['X', 'Y', 'W', 'H'];
      suffixList.forEach(item => {
        const span = document.createElement('span');
        const innerspan = document.createElement('span');
        const textspan = document.createElement('span');
        // 添加elementUI 内置 class
        span.setAttribute('class', 'el-input__suffix');
        innerspan.setAttribute('class', 'el-input__suffix-inner');

        span.append(innerspan);
        innerspan.append(textspan);
        textspan.append(item);
        this.$nextTick(() => {
          if (document.getElementById('id-input-position-' + item)) {
            document.getElementById('id-input-position-' + item).lastElementChild.prepend(span);
          }
        });
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.config-panel {
  position: fixed;
  top: 70px;
  right: 0;
  width: 332px;
  height: calc(100% - 70px);
  background: #1c2025;
  overflow: auto;
  overflow-x: hidden;
  z-index: 100;
  display: flex;
  flex-direction: column;
  .page-title {
    position: relative;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    span.icon {
      float: left;
      font-size: 16px;
      margin: 12px;
      cursor: pointer;
    }
    span.title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .page-content {
    flex: 1;
    padding: 20px 0;
    overflow: auto;
  }
}
.basic-attributes {
  display: inline-block;
  padding-top: 0;
  padding: 0 10px;
  .title {
    line-height: 30px;
    display: flex;
    justify-items: center;
    font-size: 14px;
    justify-content: space-between;
    .name-max-width {
      max-width: 300px;
    }
    .update {
      font-size: 12px;
      cursor: pointer;
    }
  }
  .basic-info {
    font-size: 13px;
    border-top: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    border-color: var(--control-border-color);
  }
  .desc {
    display: flex;
    padding-bottom: 6px;
    color: #999;
    .name-max-width {
      max-width: 128px;
    }
  }
  .name-max-width {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .theme-wrap {
    .theme-img {
      width: 100%;
      height: 180px;
      padding: 10px 20px;
      background-color: #0e0e0e;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .theme-btn {
      text-align: center;
      padding: 10px 0;
    }
  }
  .info {
    display: flex;
    padding: 10px 0;
    .info-name {
      flex: none;
      width: 80px;
      line-height: 24px;
      margin-top: 8px;
    }
    .info-content {
      flex: 1;
      .icon-placeholder {
        display: block;
        width: 16px;
        margin-left: 4px;
        line-height: 16px;
        text-align: center;
      }
      ::v-deep .el-slider {
        flex: 1;
      }
      ::v-deep .el-input-number--mini {
        width: 100%;
        margin-left: 4px;
        line-height: 24px;
        .el-input-number__decrease {
          top: 12px;
          height: 11px;
          line-height: 11px;
        }
        .el-input__inner {
          padding-left: 2px;
          padding-right: 18px;
          height: 24px;
          line-height: 24px;
        }
        .el-input-number__increase {
          height: 12px;
          line-height: 12px;
        }
        .el-input__suffix {
          right: 6px;
        }
      }
      .content-position-xy {
        display: flex;
        align-items: center;
        height: 24px;
        margin-top: 8px;
        ::v-deep .el-input-number__decrease {
          display: none;
        }
        ::v-deep .el-input-number__increase {
          display: none;
        }
      }
    }
    .contant-opacity {
      display: flex;
      line-height: 24px;
      align-items: center;
      ::v-deep .el-input-number--mini {
        width: 70px;
        margin-left: 12px;
        .el-input__inner {
          padding-right: 30px;
        }
      }
    }
  }
}
</style>
