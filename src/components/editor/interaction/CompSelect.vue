<template>
  <div class="comp-select">
    <Treeselect
      class="tree-select"
      v-model="comIds"
      :multiple="$attrs.multiple"
      @input="emitChange"
      :disabled="$attrs.disabled"
      :options="options"
      :normalizer="normalizer"
      :disable-branch-nodes="disableBranchNodes"
      placeholder="请选择"
      :clearable="false"
      :clearOnSelect="false"
      :backspaceRemoves="false"
      :deleteRemoves="false"
      :alwaysOpen="false"
      :showCount="true"
      :flat="flat"
      :defaultExpandLevel="defaultExpandLevel"
      value-consists-of="LEAF_PRIORITY"
      noChildrenText="暂无数据"
      noOptionsText="暂无数据"
      noResultsText="未找到数据">
      <div slot="value-label" slot-scope="{ node }">
        {{label(node)}}
      </div>
    </Treeselect>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
export default {
  name: 'CompSelect', // 组件选择组件（业务组件）
  inheritAttrs: true,
  components: {
    Treeselect
  },
  props: {
    value: {
      type: [Array, String]
    },
    options: {
      type: Array,
      default () {
        return {}
      }
    },
    defaultExpandLevel: { // 展开层级 Infinity 无限
      type: [Number],
      default: 0
    },
    flat: {
      type: Boolean,
      default: true
    },
    disableBranchNodes: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {}
  },
  computed: {
    comIds: {
      get: function () {
        return this.value
      },
      set: function (val) {
        this.$emit('input', val)
      }
    },
    label () {
      return (node) => {
        return node.raw.customLabel || node.raw.label || (this.value ? '该组件已被删除...' : '')
      }
    }
  },
  methods: {
    normalizer (node) {
      return {
        id: node.value,
        label: node.label,
        children: node.children
      }
    },
    emitChange (val) {
      this.$emit('change', val)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .tree-select {
    &.vue-treeselect--open-above .vue-treeselect__menu {
      bottom: 8px !important;
    }
    &.vue-treeselect--open-below .vue-treeselect__menu {
      top: 8px !important;
    }
    .vue-treeselect__control {
      border-width: 0 0 1px;
      border-bottom-color: rgba(204, 219, 255, 0.16);
      border-radius: 4px 4px 0 0;
      background: #24272e;
    }
    .vue-treeselect__input-container {
      padding-top: 0 !important;
    }
    .vue-treeselect__menu {
      min-height: 100px;
      border: 1px solid #3a4659;
      border-radius: 4px;
      background: #1f2430;
      color: rgba(255, 255, 255, 0.7);
      .vue-treeselect__option--highlight {
        background: #23334f;
      }
    }
    .vue-treeselect__placeholder {
      font-size: 12px;
    }
    .vue-treeselect__multi-value {
      margin-bottom: 0;
    }
    .vue-treeselect__multi-value-item {
      height: 22px;
      line-height: 22px;
      padding: 1px 3px;
      background: #191D25;
      border: 1px solid rgba(204, 219, 255, 0.32);
      border-radius: 16px;
      color: rgba(255, 255, 255, 0.7);
    }
    .vue-treeselect__value-remove {
      border: none;
      color: rgba(255, 255, 255, 0.7);
      // border-left: 1px solid #409eff;
    }
    .vue-treeselect__option--disabled .vue-treeselect__label-container {
      color: rgb(129 132 139);
    }
    .vue-treeselect__checkbox--disabled {
      opacity: 0.1;
    }
    .vue-treeselect__input {
      color: #fff;
    }
    .vue-treeselect__single-value {
      color: #fff;
    }
  }
}
</style>
