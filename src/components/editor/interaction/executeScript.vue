<template>
  <div class="script-page" :class="{'show': show}">
    <div class="f-title">
      <span>自定义执行脚本编辑</span>
      <i class="el-icon-close" @click="close"></i>
    </div>
    <div class="f-content">
      <el-form label-width="100px" label-position="left" size="mini">
        <el-form-item label-width="0px">
          <div class="filter-wrap">
            <div class="filter-item" v-for="(item, index) in event.scripts" :key="index">
              <div class="f-item-title">
                <div class="title-left">
                  <span v-if="!editable" class="name">{{ item.name }}</span>
                  <el-input v-else v-model="item.name" v-select class="name" @blur="onBlur"></el-input>
                  <span class="ico el-icon-edit pointer" @click="editName"></span>
                </div>
                <span class="ico el-icon-delete pointer" @click="deleteScript(index)"></span>
              </div>
              <div class="f-item-content">
                <div class="content-wrap">
                  <el-form label-width="80px" label-position="left" size="mini">
                    <el-form-item label-width="0px">
                        <span v-if="isShowTips(event)" class="event-tips"><span style="color: red">*</span> 获取数据源数组第一项数据</span>
                        <div class="func-tit">
                          <span>function hanleScript(<span>{{parmasStr}}</span>){</span>
                        </div>
                        <CodeEditor ref="codeEditor" v-model="item.code" />
                        <div class="func-tit">
                          <span>}</span>
                        </div>
                  </el-form-item>
                    <!-- <div class="btn-wrap">
                      <el-button type="primary" plain size="mini" @click="toggle(item)">取消</el-button>
                      <el-button type="primary" size="mini" @click="save(item)">保存</el-button>
                    </div> -->
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import CodeEditor from '../data-source/exeScEditor';
import { mapState, mapGetters } from 'vuex';
import dataUtil from '@/utils/data';
export default {
  name: 'ExecuteScript',
  props: {
    event: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  components: {
    CodeEditor
  },
  data () {
    return {
      editable: false,
      show: false,
      params: [],
      parmasStr: ''
    }
  },

  directives: {
    select: {
      inserted: function (el) {
        el.querySelector('input').select()
      }
    }
  },

  computed: {
    ...mapState({
      screenComs: state => state.editor.screenComs,
      screenFilters: state => state.editor.screenFilters,
      comsData: state => state.editor.comsData
    }),
    ...mapGetters('editor', ['currentCom']),
    isShowTips () {
      return function (item) {
        if (item.trigger && (item.trigger === 'compClick' || item.trigger === 'compHover' || item.trigger === 'compDbclick' || item.trigger === 'compMouseleave')) {
          return true
        }
        return false
      }
    }
  },

  created () {
  },

  methods: {
    showScript () {
      if (this.event.scripts.length === 0) {
        this.addScript();
      }
      this.getParams();
      this.show = true;
    },
    close () {
      this.show = false;
    },
    onBlur () { // 过滤器名称 blur
      this.editable = !this.editable;
    },
    editName () {
      this.editable = !this.editable;
    },
    deleteScript (index) { // 删除
      this.event.scripts.splice(index, 1);
      this.close();
    },
    addScript () {
      const script = {
        name: '脚本', // 名称
        code: '// 请填写脚本代码'
      }
      this.event.scripts.push(script)
    },
    getParams () {
      const trigger = this.event.trigger;
      this.params = [];
      if (trigger === 'dataChange' || trigger === 'compClick' || trigger === 'compHover' || trigger === 'compDbclick' || trigger === 'compMouseleave') {
        this.params.push(...this.defaultDeepOptions())
      } else {
        if (this.currentCom.events.length > 0) {
          // this.currentCom.events
          const event = this.currentCom.events.find(item => item.name === trigger);
          if (event) {
            this.params.push(...event.params.map(value => value.name))
          }
        }
      }
      this.parmasStr = this.params.toString();
    },
    defaultDeepOptions () {
      const tempOptions = []
      const filterData = this.getFilterData()
      filterData.length && tempOptions.push(...Object.keys(filterData[0]))
      return tempOptions
    },
    getFilterData () {
      const comCfg = this.screenComs[this.currentCom.id]
      let filter = []
      if (!_.isEmpty(comCfg)) {
        const filters = comCfg.dataConfig.dataResponse.filters
        if (!filters.enable) {
          filter = []
        } else {
          filter = _.filter(filters.list, { enable: true }).map(({ id }) => this.screenFilters[id])
        }
      }
      const sourceData = this.comsData[this.currentCom.id]
      const filterData = dataUtil.filterData(sourceData || [], filter)
      return filterData
    }
  }
}
</script>

<style  lang="scss" scoped>
.script-page {
  position: fixed;
  top: 0;
  bottom: 0;
  right: -400px;
  width: 400px;
  z-index: 999;
  padding-top: 130px;
  background: #22242b;
  transition: right .5s ease-in-out;
  overflow: auto;
  &.show {
    right: 0;
  }
  .f-title {
    position: fixed;
    top: 70px;
    width: 400px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #fafafa;
    background: #22242b;
    padding: 20px 16px;
    z-index: 999;
  }
  .f-content {
    padding: 0 16px 16px;
    margin-top: 20px;
    .filter-wrap {
      .filter-item {
        position: relative;
        margin-bottom: 10px;
        &:last-child {
          margin: 0;
        }
        .f-item-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 32px;
          background-color: #2d2f38;
          border: 1px solid #393b4a;
          border-bottom: none;
          padding-right: 10px;
          .title-left {
            width: 200px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            .name {
              color: #fafafa;
              font-size: 12px;
              width: 140px;
              margin-left: 6px;
              display: inline-block;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .ico {
              margin-left: 5px;
              &:hover {
                color: var(--seatom-main-color);
              }
            }
            .r-oprate {
              font-size: 12px;
              color: #999;
              .ico {
                font-size: 14px;
                margin-left: 10px;
                &:hover {
                  color: var(--seatom-main-color);
                }
              }
            }
          }
        }
        .f-item-content {
          transition: height .2s linear;
          border: 1px solid #393b4a;
          border-top: none;
          overflow: hidden;
          &.collapsed {
            height: 0;
          }
          .content-wrap {
            padding: 16px 16px 0;
          }
        }
        .func-tit {
          font-size: 12px;
          color: #999;
          line-height: 30px;
        }
      }
    }
  }

  .btn-wrap {
    text-align: right;
    margin-bottom: 10px;
  }

  .w100 {
    width: 100%;
  }
  .pointer {
    cursor: pointer;
  }

  ::v-deep {
    .CodeMirror {
      height: 400px;
    }
  }

}

</style>
