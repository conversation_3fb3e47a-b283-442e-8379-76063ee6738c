<template>
  <div class="mobile-setting">
    <div class="page-title">
      <span>页面概览</span>
      <span class="icon el-icon-setting" title="页面设置" @click.stop="showScenePanel()"></span>
      </div>
    <div class="setting-content">
      <div class="page-list" v-if="sceneData.length">
        <draggable v-model="sceneData[0].pageList" group="page" handle=".move" :animation="400" @change="pageSortChange">
          <div
            class="page-item"
            v-for="(item, index) in sceneData[0].pageList"
            :key="item.pageId">
            <div
              class="page-item-v"
              :class="{ 'active': item.pageId === pageId }"
              @click="clickPage(item)"></div>
            <div class="page-item-des">
              <div class="page-name">
                <div class="p-name">
                  <el-input
                    v-if="showEdit && editId === item.pageId"
                    v-focus
                    class="name-input"
                    size="mini"
                    v-model="item.pageName"
                    @blur="nameBlur(item)"
                    @click.native.stop>
                  </el-input>
                  <span v-else>{{ item.pageName }}</span>
                </div>
                <span class="p-edit el-icon-edit" @click="rename(item)"></span>
              </div>
              <div class="page-edit">
                <span class="icon move el-icon-rank"></span>
                <!-- <hz-icon class="icon" name="append-data"></hz-icon> -->
                <!-- <hz-icon class="icon" name="bg" @click.native.stop="showPagebgPanel({item, pageIndex: index})"></hz-icon> -->
                <span class="icon el-icon-setting" title="设置" @click.stop="showPagebgPanel({page: item, pageIndex: index})"></span>
                <hz-icon class="icon" name="trash" @click="delPage(item)"></hz-icon>
              </div>
            </div>
          </div>
        </draggable>
        <div class="add-page" @click="addPage" v-if="showButton">
          <span class="el-icon-plus"></span>
        </div>
      </div>
    </div>
    <SceneSetting v-if="showScene" @close="closeScenePanel" />
    <PageBg
      v-if="showPageBg"
      :page="page"
      :pageIndex="pageIndex"
      :sceneIndex="sceneIndex"
      @close="closePagePanel" />
    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import SceneSetting from './SceneSetting';
import PageBg from './PageBg';
import draggable from 'vuedraggable';
import { orderPage } from '@/api/screen';
import { mapState } from 'vuex';
export default {
  name: 'MobileSetting',
  components: {
    SceneSetting,
    PageBg,
    draggable
  },
  data () {
    return {
      sceneData: [],
      showEdit: false,
      editId: '',
      showScene: false,
      showPageBg: false,
      page: null,
      pageIndex: 0,
      sceneIndex: 0,
      loading: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      sceneId: state => state.editor.sceneId,
      pageId: state => state.editor.pageId
    }),
    sceneConfig () {
      return _.cloneDeep(this.screenInfo.sceneConfig)
    },
    showButton () {
      const { comp, sceneId } = this.$route.query;
      if (comp === 'dynamic' && !!sceneId) {
        return false
      }
      return true
    }
  },
  watch: {
    sceneConfig: {
      handler: function (sceneConfig) {
        const { query } = this.$route;
        if (query.comp === 'dynamic' && !!query.sceneId) { // 兼容移动端新版动态面板
          const index = sceneConfig.findIndex(item => item.sceneId === query.sceneId);
          if (index > -1) {
            this.sceneIndex = index;
            this.sceneData = [sceneConfig[index]]
            return
          }
        }
        this.sceneData = _.cloneDeep(sceneConfig || []);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addPage () { // 新增页面
      this.loading = true;
      this.$store.dispatch('editor/addScenePage', { sceneId: this.sceneId }).then(() => {
        this.loading = false;
        this.$nextTick(() => {
          const page = this.sceneData[0].pageList[this.sceneData[0].pageList.length - 1];
          this.$store.commit('editor/updatePageId', page.pageId);
        })
      })
    },
    rename (item) {
      this.showEdit = true;
      this.editId = item.pageId;
    },
    nameBlur (item) { // 修改场景、页面名称
      const params = {
        sceneId: this.sceneData[0].sceneId,
        sceneName: this.sceneData[0].sceneName,
        pageId: item.pageId,
        pageName: item.pageName
      }
      this.loading = true;
      this.$store.dispatch('editor/updateName', params).then(() => {
        this.showEdit = false;
        this.editId = null;
        this.loading = false;
      })
    },
    clickPage (page) {
      this.$store.commit('editor/updatePageId', page.pageId);
    },
    async pageSortChange (e) { // 页面排序
      const { element, newIndex } = e.moved;
      const pageId = element.pageId;
      const data = {
        id: this.screenInfo.id,
        pageId: pageId,
        sceneId: this.sceneData[0].sceneId,
        targetIndex: newIndex
      }
      const res = await orderPage(data);
      if (res && res.success) {
        this.$store.dispatch('editor/getSceneConfig', { id: this.screenInfo.id })
      }
    },
    delPage (page) {
      if (this.sceneData[0].pageList.length === 1) {
        this.$alert('请至少保留一个页面！', { title: '提示', type: 'warning' });
        return
      }
      this.$confirm('确认删除该页面？', '提示', { type: 'warning' }).then(() => {
        this.loading = true;
        this.$store.dispatch('editor/deletePage', { sceneId: this.sceneData[0].sceneId, pageId: page.pageId }).then(() => {
          this.loading = false;
        })
      }).catch(e => {})
    },
    showScenePanel () {
      this.showScene = true;
    },
    closeScenePanel () {
      this.showScene = false;
    },
    showPagebgPanel ({ page, pageIndex }) {
      this.page = page;
      this.pageIndex = pageIndex;
      this.showPageBg = true;
    },
    closePagePanel () {
      this.showSceneBg = false;
      this.showPageBg = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-setting {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .page-title {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    padding: 0 10px;
    .icon {
      float: right;
      font-size: 14px;
      margin-top: 13px;
      cursor: pointer;
    }
  }
  .setting-content {
    flex: 1;
    overflow: auto;
    padding: 48px;
    background-color: #13161A;
    overflow: auto;
    .page-list {
      .page-item {
        margin-bottom: 10px;
        .page-item-v {
          width: 100%;
          height: 340px;
          background: #313237;
          &.active {
            border: 2px solid #1F71FF;
          }
        }
        .page-item-des {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 10px;
          .page-name {
            display: flex;
            align-items: center;
            .p-name {
              width: 60px;
              font-size: 14px;
              color: #fff;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .p-edit {
              font-size: 16px;
              color: #0092E0;
              cursor: pointer;
              margin-left: 10px;
            }
          }
          .page-edit {
            .icon {
              font-size: 16px;
              color: #fff;
              margin-left: 5px;
              cursor: pointer;
            }
          }
        }
      }
      .add-page {
        width: 100%;
        height: 126px;
        background: rgba(49, 50, 55, 0.58);
        border: 1px dashed #3D85FF;
        display: flex;
        cursor: pointer;
        span {
          font-size: 16px;
          color: #3D85FF;
          margin: auto;
        }
      }
    }
  }
  .name-input {
    width: 60px;
    ::v-deep {
      .el-input__inner {
        height: 20px;
        line-height: 20px;
        padding: 0 10px;
        background-color: #2d2e38;
      }
    }
  }
}
</style>
