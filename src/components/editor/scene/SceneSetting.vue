<template>
  <div class="scene-setting">
    <div class="page-title">
      <span class="icon el-icon-back" @click="$emit('close')"></span>
      <span class="title">页面设置</span>
    </div>
    <div class="page-content">
      <el-form label-width="90px" label-position="left" size="mini">
        <el-form-item label="屏幕大小" v-if="!isDynamicScreen">
          <el-select v-model="screenType" class="full-w">
            <el-option v-for="opt in screenList" :key="opt.id" :value="opt.value" :label="opt.label"></el-option>
          </el-select>
          <div class="content-wrap">
            <div class="input-row">
              <el-input v-model.number.trim="width" @blur="val => changeSize('width', val)" class="item">
                <template slot="suffix">W</template>
              </el-input>
              <el-input v-model.number.trim="height" @blur="val => changeSize('height', val)" class="item">
                <template slot="suffix">H</template>
              </el-input>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="背景色">
          <div class="input-row">
            <el-color-picker v-model="backgroundColor" show-alpha></el-color-picker>
            <el-input v-model="backgroundColor" class="item2"></el-input>
          </div>
        </el-form-item>
        <el-form-item label="背景图">
          <UploadImage v-model="backgroundImage" />
        </el-form-item>
        <el-form-item label="平铺方式">
          <el-select v-model="backgroundRepeat" class="full-w">
            <el-option v-for="opt in repeatOpt" :key="opt.id" :value="opt.value" :label="opt.label"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="!isDynamicScreen">
          <template v-if="platform !== 'mobile'">
            <el-form-item label="栅格间距">
              <el-input-number v-model="gridSpace" controls-position="right" class="full-w" :min="1"></el-input-number>
            </el-form-item>
            <el-form-item label="缩放设置">
              <el-radio-group v-model="scaleType" class="scale-radio">
                <el-radio-button v-for="opt in scaleOpt" :key="opt.value" :label="opt.value">
                  <el-tooltip placement="bottom" :content="opt.label">
                    <hz-icon :name="opt.icon"></hz-icon>
                  </el-tooltip>
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </template>
          <el-form-item label="封面">
            <UploadImage v-model="thumbnail" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="record" :loading="loading">{{ loading ? '截屏中' : '截屏' }}</el-button>
          </el-form-item>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script>
import UploadImage from '../data-source/UploadImage';
import { recordScreen } from '@/api/screen';
import { mapState } from 'vuex';
import { screenList, repeatOpt, scaleOpt } from '@/common/constants';
import { replaceUrl } from '@/utils/base';
export default {
  name: 'SceneSetting', // 场景设置
  props: {},
  components: {
    UploadImage
  },
  data () {
    return {
      screenList: screenList,
      repeatOpt: repeatOpt,
      scaleOpt: scaleOpt,
      width: 1920,
      height: 1080,
      loading: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    isDynamicScreen () { // 是否为动态面板创建的大屏
      return this.screenInfo.isDynamicScreen
    },
    platform () {
      return this.screenInfo.type
    },
    screenType: {
      get: function () {
        const keyMap = ['1920*1080', '1366*768', '1024*768'];
        if (keyMap.includes(`${this.width}*${this.height}`)) {
          return `${this.width}*${this.height}`
        } else {
          return 'diy'
        }
      },
      set: function (val) {
        if (val !== 'diy') {
          const [width, height] = val.split('*');
          this.width = +width;
          this.height = +height;
        } else {
          this.width = 1440;
          this.height = 768;
        }
        this.$store.dispatch('editor/updateScreenInfo', [
          { key: 'config.width', value: this.width },
          { key: 'config.height', value: this.height }
        ]);
      }
    },
    backgroundColor: {
      get: function () {
        return this.screenInfo.config.backgroundColor
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundColor', value: val }]);
      }
    },
    backgroundImage: {
      get: function () {
        return replaceUrl(this.screenInfo.config.backgroundImage)
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundImage', value: val }]);
      }
    },
    backgroundRepeat: {
      get: function () {
        return this.screenInfo.config.backgroundRepeat
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundRepeat', value: val }]);
      }
    },
    gridSpace: {
      get: function (val) {
        return this.screenInfo.config.gridSpace
      },
      set: function (val) {
        if (val !== this.gridSpace) {
          this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.gridSpace', value: val }]);
        }
      }
    },
    scaleType: {
      get: function (val) {
        return this.screenInfo.config.scaleType
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.scaleType', value: val }]);
      }
    },
    thumbnail: {
      get: function (val) {
        return replaceUrl(this.screenInfo.config.thumbnail)
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.thumbnail', value: val }]);
      }
    }
  },
  watch: {
    screenInfo: {
      handler: function (info) {
        this.width = info.config.width;
        this.height = info.config.height;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async record () {
      try {
        this.loading = true;
        const res = await recordScreen({ screenId: this.screenInfo.id, width: this.width, height: this.height });
        if (res && res.success) {
          this.thumbnail = replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.screenshotUrl) + '?t=' + Date.now();
        }
        this.loading = false;
      } catch (e) {
        this.loading = false;
      }
    },
    changeSize (type) {
      if (this[type] === '') {
        this[type] = this.screenInfo.config[type];
        return
      }
      this.$store.dispatch('editor/updateScreenInfo', [{ key: `config.${type}`, value: this[type] }]);
    }
  }
}
</script>

<style lang="scss" scoped>
.scene-setting {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #1c2025;
  overflow: auto;
  overflow-x: hidden;
  z-index: 100;
  .page-title {
    position: relative;
    font-size: 14px;
    font-weight: normal;
    color: #d8d8d8;
    height: 40px;
    line-height: 40px;
    background: #2d2f38;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    span.icon {
      float: left;
      font-size: 16px;
      margin: 12px;
      cursor: pointer;
    }
    span.title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .page-content {
    padding: 20px 16px;
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
      color: var(--control-text-color);
    }
    .el-radio__label {
      font-size: 12px;
      color: #bfbfbf;
    }
    .el-upload-dragger {
      width: 200px;
      height: 90px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
      }
      .uploaded-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        object-fit: contain;
      }
    }
  }
  .single-line ::v-deep .el-radio {
    display: block;
    line-height: 26px;
  }
  .full-w {
    width: 100%;
  }
  .content-wrap {
    padding-top: 15px;
  }
  .input-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item {
      width: 100px;
    }
    .item1 {
      width: 100px;
      margin-left: 10px
    }
    .item2 {
      flex: 1;
      margin-left: 10px;
    }
  }
  .radio-group {
    .el-radio {
      margin-right: 20px;
    }
  }
  .scale-radio ::v-deep {
    .el-radio-button--mini .el-radio-button__inner {
      padding: 7px 12px;
    }
    .el-radio-button:first-child .el-radio-button__inner,
    .el-radio-button:last-child .el-radio-button__inner {
      border-radius: 0;
    }
  }
}
</style>
