<template>
  <div class="page-setting">
    <div class="page-title">
      <span class="icon el-icon-back" @click="$emit('close')"></span>
      <span class="title">设置背景</span>
    </div>
    <div class="page-content">
      <el-form label-width="90px" label-position="left" size="mini">
        <el-form-item label="背景色">
          <div class="input-row">
            <el-color-picker v-model="backgroundColor" show-alpha></el-color-picker>
            <el-input v-model="backgroundColor" class="item2"></el-input>
          </div>
        </el-form-item>
        <el-form-item label="背景图">
          <UploadImage v-model="pageBackground" />
        </el-form-item>
        <el-form-item label="平铺方式">
          <el-select v-model="backgroundRepeat" class="full-w">
            <el-option v-for="opt in repeatOpt" :key="opt.id" :value="opt.value" :label="opt.label"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import UploadImage from '../data-source/UploadImage';
import { mapState } from 'vuex';
import { repeatOpt } from '@/common/constants';
export default {
  name: 'PageSetting', // 页面设置
  props: {
    page: {
      type: Object,
      default () {
        return {}
      }
    },
    pageIndex: Number,
    sceneIndex: Number
  },
  components: {
    UploadImage
  },
  data () {
    return {
      backgroundColor: '',
      pageBackground: '',
      backgroundRepeat: 'fill',
      repeatOpt: repeatOpt
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  watch: {
    backgroundColor (val) {
      this.$store.dispatch('editor/updateScreenInfo', [{ key: `sceneConfig.${this.sceneIndex}.pageList.${this.pageIndex}.backgroundColor`, value: val }]);
    },
    pageBackground (val) {
      this.$store.dispatch('editor/updateScreenInfo', [{ key: `sceneConfig.${this.sceneIndex}.pageList.${this.pageIndex}.pageBackground`, value: val }]);
    },
    backgroundRepeat (val) {
      this.$store.dispatch('editor/updateScreenInfo', [{ key: `sceneConfig.${this.sceneIndex}.pageList.${this.pageIndex}.backgroundRepeat`, value: val }]);
    }
  },
  created () {
    this.pageBackground = this.page.pageBackground;
    this.backgroundColor = this.page.backgroundColor;
    this.backgroundRepeat = this.page.backgroundRepeat;
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.page-setting {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #1c2025;
  overflow: auto;
  overflow-x: hidden;
  z-index: 100;
  .page-title {
    position: relative;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    span.icon {
      float: left;
      font-size: 16px;
      margin: 12px;
      cursor: pointer;
    }
    span.title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .page-content {
    padding: 20px 16px;
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
      color: var(--control-text-color);
    }
    .el-radio__label {
      font-size: 12px;
      color: #bfbfbf;
    }
    .el-upload-dragger {
      width: 200px;
      height: 90px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
      }
      .uploaded-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        object-fit: contain;
      }
    }
  }
  .single-line ::v-deep .el-radio {
    display: block;
    line-height: 26px;
  }
  .full-w {
    width: 100%;
  }
  .content-wrap {
    padding-top: 15px;
  }
  .input-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item {
      width: 100px;
    }
    .item1 {
      width: 100px;
      margin-left: 10px
    }
    .item2 {
      flex: 1;
      margin-left: 10px;
    }
  }
  .radio-group {
    .el-radio {
      margin-right: 20px;
    }
  }
  .scale-radio ::v-deep {
    .el-radio-button--mini .el-radio-button__inner {
      padding: 7px 12px;
    }
    .el-radio-button:first-child .el-radio-button__inner,
    .el-radio-button:last-child .el-radio-button__inner {
      border-radius: 0;
    }
  }
}
</style>
