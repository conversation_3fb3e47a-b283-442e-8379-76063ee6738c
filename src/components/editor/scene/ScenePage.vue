<template>
  <div class="scene-page">
    <div class="page-title">
      <span class="p-title">场景页面</span>
      <span class="icon el-icon-setting" title="页面设置" @click.stop="showScenePanel()"></span>
    </div>
    <div class="page-content">
      <div class="scroll-content">
        <div class="config-control">
          <el-collapse class="scene-collapse w100" v-model="active" accordion @change="sceneChange">
            <draggable v-model="sceneData" group="scene" handle=".move" :animation="400" @change="sceneSortChange">
              <el-collapse-item
                v-for="(scene, index) in sceneData"
                :name="scene.sceneId"
                :title="scene.sceneName"
                :class="{ 'active': scene.sceneId === sceneId && !pageId }"
                :key="scene.sceneId">
                <div class="c-header" slot="title">
                  <el-input
                    v-if="showEdit && editId === scene.sceneId"
                    v-focus
                    class="name-input"
                    size="mini"
                    v-model="scene.sceneName"
                    @blur="nameBlur({ sceneId: scene.sceneId, sceneName: scene.sceneName })"
                    @click.native.stop>
                  </el-input>
                  <span v-else class="c-title" :title="scene.sceneName"><i class="el-icon el-icon-menu"></i> {{ scene.sceneName }}</span>
                  <div class="setting">
                    <span class="icon move el-icon-rank" style="font-size:16px;" v-if="showButton"></span>
                    <hz-icon class="icon" name="bg" @click.native.stop="showScenebgPanel(scene, index)"></hz-icon>
                    <el-dropdown @command="val => sceneHandler(scene, val)" szie="mini" v-if="showButton">
                      <span class="el-icon-more" @click.stop></span>
                      <el-dropdown-menu class="scene-dropdown" slot="dropdown">
                        <el-dropdown-item command="rename">重命名</el-dropdown-item>
                        <el-dropdown-item command="delete" v-if="handleSceneDelete(scene)">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
                <div class="page-list" v-if="scene.pageList && scene.pageList.length">
                    <draggable v-model="scene.pageList" group="page" handle=".move" :animation="400" @change="pageSortChange">
                        <div
                          class="page-item"
                          :class="{ 'active': page.pageId === pageId }"
                          v-for="(page, index2) in scene.pageList"
                          :key="page.id"
                          @mouseenter="hoverPage(page)"
                          @mouseleave="hoverPageId=''"
                          @click="clickPage(page)">
                          <div v-if="page.pageId === hoverPageId && coeditTipText" class="coedit-tip">
                            {{ coeditTipText }}
                          </div>
                          <div class="page-t">
                            <el-input
                              v-if="showEdit && editId === page.pageId"
                              v-focus
                              class="name-input"
                              size="mini"
                              v-model="page.pageName"
                              @blur="nameBlur({ sceneId: scene.sceneId, sceneName: scene.sceneName, pageId: page.pageId, pageName: page.pageName })"
                              @click.native.stop>
                            </el-input>
                            <span :title="page.pageName" v-else>
                              <i class="el-icon el-icon-document"></i> {{ page.pageName }}
                            </span>
                          </div>
                          <div class="setting">
                            <span class="icon move el-icon-rank" style="font-size:16px;" v-if="showButton"></span>
                            <hz-icon class="icon" name="bg" @click.native.stop="showPagebgPanel({page, pageIndex: index2, sceneIndex: index})"></hz-icon>
                            <el-dropdown @command="val => pageHandler(page, scene.pageList, val)" szie="mini" v-if="showButton">
                              <span class="el-icon-more" @click.stop></span>
                              <el-dropdown-menu class="scene-dropdown" slot="dropdown">
                                <el-dropdown-item command="rename">重命名</el-dropdown-item>
                                <el-dropdown-item command="delete" v-if="handlePageDelete(scene,page)">删除</el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                          </div>
                        </div>
                    </draggable>
                </div>
              </el-collapse-item>
            </draggable>
          </el-collapse>
        </div>
      </div>
    </div>
    <div class="fix-button" v-if="showButton">
      <el-button type="primary" icon="el-icon-film" size="mini" @click="addScene">添加场景</el-button>
      <el-button type="default" icon="el-icon-document" size="mini" @click="addPage">添加页面</el-button>
    </div>
    <SceneSetting v-if="showScene" @close="closeScenePanel" />
    <SceneBg
      v-if="showSceneBg"
      :scene="scene"
      :index="sceneIndex"
      @close="closePagePanel" />
    <PageBg
      v-if="showPageBg"
      :page="page"
      :pageIndex="pageIndex"
      :sceneIndex="sceneIndex"
      @close="closePagePanel" />
    <seatom-loading v-if="loading" append-to-body></seatom-loading>
  </div>
</template>

<script>
import SceneSetting from './SceneSetting';
import SceneBg from './PageSetting';
import PageBg from './PageBg';
import draggable from 'vuedraggable';
import { orderPage, orderScene } from '@/api/screen';
import { mapState } from 'vuex';
import { find } from 'lodash'
export default {
  name: 'ScenePage', // 场景页面
  components: {
    SceneSetting,
    SceneBg,
    PageBg,
    draggable
  },
  data () {
    return {
      active: '',
      sceneData: [],
      showScene: false,
      showSceneBg: false,
      showPageBg: false,
      scene: null,
      sceneIndex: 0,
      page: null,
      pageIndex: 0,
      showEdit: false,
      editId: null,
      loading: false,
      newPageOption: false, // 是否为新增页面操作
      hoverPageId: '',
      coeditTipText: ''
    }
  },
  created () {

  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      sceneId: state => state.editor.sceneId,
      pageId: state => state.editor.pageId,
      coeditData: state => state.editor.coeditData
    }),
    screenType () {
      return this.screenInfo.screenType
    },
    sceneConfig () {
      return this.screenInfo.sceneConfig
    },
    showButton () {
      const { comp, sceneId } = this.$route.query;
      if (comp === 'dynamic' && !!sceneId) {
        return false
      }
      return true
    }
  },
  watch: {
    sceneConfig: {
      handler: function (val) {
        this.sceneData = _.cloneDeep(val || []);
        const index = this.sceneData.findIndex(scene => {
          return scene.pageList.findIndex(page => page.pageId === this.pageId) > -1
        });
        if (index > -1) {
          this.active = this.sceneData[index].sceneId;
        }
      },
      immediate: true,
      deep: true
    },
    sceneId: {
      handler: function (val) {
        this.active = val
      }
    }
  },
  methods: {
    //  处理场景大屏页面删除按钮显隐逻辑
    handlePageDelete (scene, page) {
      if (scene.pageList.length <= 1) {
        return false
      }
      const { coeditId = '' } = this.screenInfo
      if (coeditId) {
        const { coeditUsers = [] } = this.coeditData
        if (coeditUsers.length === 0) {
          return true
        }
        // 筛选当前页面是否正在编辑
        const currPage = coeditUsers.filter(item => {
          return item.pageId === page.pageId
        })
        return !currPage.length
      } else {
        return true
      }
    },
    // 处理场景大屏场景删除按钮显隐逻辑
    handleSceneDelete (scene) {
      // 隐藏删除逻辑：非协同屏只有一个场景不可删除，协同屏当前编辑页面所在场景屏不可删除
      if (this.sceneData.length <= 1) {
        return false
      }
      const { coeditId = '' } = this.screenInfo
      if (coeditId) {
        // 筛选当前场景中是否存在正在编辑的页面
        const hasCurrPageUse = scene.pageList.filter(item => {
          return find(this.coeditData.coeditUsers, ['pageId', item.pageId])
        })
        return !hasCurrPageUse.length
      } else {
        return true
      }
    },
    showScenePanel () {
      this.showScene = true;
    },
    closeScenePanel () {
      this.showScene = false;
    },
    showScenebgPanel (scene, index) {
      this.scene = scene;
      this.sceneIndex = index;
      this.showSceneBg = true;
    },
    showPagebgPanel ({ page, pageIndex, sceneIndex }) {
      this.page = page;
      this.pageIndex = pageIndex;
      this.sceneIndex = sceneIndex;
      this.showPageBg = true;
    },
    closePagePanel () {
      this.showSceneBg = false;
      this.showPageBg = false;
    },
    sceneHandler (item, val) {
      const index = this.sceneData.findIndex(scene => scene.id === item.id);
      switch (val) {
        case 'up':
          this.upClick(index, this.sceneData);
          break;
        case 'down':
          this.downClick(index, this.sceneData);
          break;
        case 'copy':
          break;
        case 'rename':
          this.showEdit = true;
          this.editId = item.sceneId;
          break;
        case 'delete':
          this.$confirm('删除场景将同步删除场景下的页面，确认删除？', '提示', { type: 'warning' }).then(() => {
            this.delScene(item);
          }).catch(e => {})
          break;
        default:
          break;
      }
    },
    pageHandler (item, list, val) {
      const index = list.findIndex(page => page.id === item.id);
      switch (val) {
        case 'up':
          this.upClick(index, list);
          break;
        case 'down':
          this.downClick(index, list);
          break;
        case 'copy':
          break;
        case 'rename':
          this.showEdit = true;
          this.editId = item.pageId;
          break;
        case 'delete':
          this.$confirm('确认删除该页面？', '提示', { type: 'warning' }).then(() => {
            this.delPage(item);
          }).catch(e => {})
          break;
        default:
          break;
      }
    },
    upClick (index, arr) {
      const newArr = this.swapItems(arr, index, index - 1)
      arr = newArr
    },
    downClick (index, arr) {
      const newArr = this.swapItems(arr, index, index + 1)
      arr = newArr
    },
    swapItems (arr, index1, index2) {
      arr[index1] = arr.splice(index2, 1, arr[index1])[0];
      return arr;
    },
    sceneChange (sceneId) {
      if (sceneId) {
        const scene = this.sceneData.find(item => item.sceneId === sceneId);
        if (scene) {
          this.$store.commit('editor/updateSceneId', scene.sceneId);
          this.$store.commit('editor/updatePageId', null);
        }
      } else {
        this.$store.commit('editor/updatePageId', null);
      }
      // 协同场景
      const { coeditId } = this.screenInfo
      if (coeditId) {
        // 协同场景大屏
        this.$emit('sceneChange')
      }
    },
    hoverPage (page) {
      this.hoverPageId = page.pageId
      const { coeditUsers = [] } = this.coeditData
      const editObj = coeditUsers.find(item => {
        return item.pageId === this.hoverPageId
      })
      if (editObj) {
        this.coeditTipText = `${editObj.userName}编辑中`
      } else {
        this.coeditTipText = ''
      }
    },
    clickPage (page) {
      if (this.pageId === page.pageId) return;
      const { coeditId } = this.screenInfo
      if (coeditId) {
        // 协同场景大屏
        this.$emit('pageChange', page)
      } else {
        // 普通场景大屏
        this.$store.commit('editor/updatePageId', page.pageId);
      }
    },
    addScene () { // 新增场景
      this.loading = true;
      this.$store.dispatch('editor/addScreenScene').then(() => {
        this.loading = false;
        this.$emit('editEnd')
      })
    },
    addPage () { // 新增页面
      this.loading = true;
      this.$store.dispatch('editor/addScenePage', { sceneId: this.sceneId }).then(() => {
        this.loading = false;
        this.$nextTick(() => {
          const scene = this.sceneConfig.find(item => item.sceneId === this.sceneId);
          if (scene) {
            const page = scene.pageList[scene.pageList.length - 1];
            this.$store.commit('editor/updatePageId', page.pageId);
            this.editId = page.pageId;
            this.showEdit = true;
            this.newPageOption = true
            if (!this.active) {
              this.active = scene.sceneId;
            }
          }
        })
      })
    },
    delScene (scene) { // 删除场景
      this.loading = true;
      this.$store.dispatch('editor/deleteScene', { sceneId: scene.sceneId }).then(() => {
        this.loading = false;
      })
    },
    delPage (page) {
      const scene = this.sceneConfig.find(item => {
        return item.pageList.findIndex(item2 => item2.pageId === page.pageId) > -1;
      })
      if (scene) {
        this.loading = true;
        this.$store.dispatch('editor/deletePage', { sceneId: scene.sceneId, pageId: page.pageId }).then(() => {
          this.loading = false;
        })
      }
    },
    nameBlur (params) { // 修改场景、页面名称
      this.loading = true;
      this.$store.dispatch('editor/updateName', { ...params }).then(() => {
        this.showEdit = false;
        this.editId = null;
        this.loading = false;
        if (this.newPageOption) {
          // 协同场景下
          this.$emit('editEnd')
          this.newPageOption = false
        }
      })
    },
    async sceneSortChange (e) { // 场景排序
      const { element, newIndex } = e.moved;
      const sceneId = element.sceneId;
      const data = {
        id: this.screenInfo.id,
        sceneId: sceneId,
        targetIndex: newIndex
      }
      const res = await orderScene(data);
      if (res && res.success) {
        this.$store.dispatch('editor/getSceneConfig', { id: this.screenInfo.id })
      }
    },
    async pageSortChange (e) { // 页面排序
      const { element, newIndex } = e.moved;
      const pageId = element.pageId;
      const scene = this.sceneData.find(scene => {
        return scene.pageList.findIndex(page => page.pageId === pageId) > -1
      });
      const data = {
        id: this.screenInfo.id,
        pageId: pageId,
        sceneId: scene.sceneId,
        targetIndex: newIndex
      }
      const res = await orderPage(data);
      if (res && res.success) {
        this.$store.dispatch('editor/getSceneConfig', { id: this.screenInfo.id })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.scene-page {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 60px;
  overflow: auto;
  overflow-x: hidden;
  .page-title {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    padding: 0 10px;
    .icon {
      float: right;
      font-size: 14px;
      margin-top: 13px;
      cursor: pointer;
    }
  }
  .page-content {
    flex: 1;
    overflow: hidden;
    .scroll-content {
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
    }
    .config-control {
      padding: 0;
    }
    .scene-collapse {
      ::v-deep {
        .el-collapse-item.active {
          .el-collapse-item__header {
            background-color: var(--seatom-main-color);
          }
        }
        .el-collapse-item__header {
          position: relative;
          padding-left: 30px;
          height: 40px;
          line-height: 40px;
          .el-collapse-item__arrow {
            position: absolute;
            left: 8px;
            margin: 0;
          }
        }
        .el-collapse-item__content {
          padding-bottom: 10px;
        }
        .el-collapse-item__wrap {
          overflow: visible;
        }
      }
    }
    .c-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 10px;
      &:hover {
        .setting {
          display: block;
        }
      }
      .c-title {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .page-list {
      .page-item {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 30px;
        padding: 0 10px 0 30px;
        background: #2d2e38;
        cursor: pointer;
        &:hover {
          background-color: #40434c;
          .setting {
            display: block;
          }
        }
        &.active {
          background: var(--seatom-main-color);
          .setting {
            display: block;
          }
        }
        .coedit-tip {
          min-width: 100px;
          position: absolute;
          right: 80px;
          color: #fff;
          padding: 6px 8px;
          border-radius: 4px;
          bottom: -38px;
          z-index: 1;
          background: rgba(31, 36, 48, 1);
          box-shadow: 0px 8px 24px -6px rgba(4, 8, 16, 0.56), 0px 1px 3px rgba(4, 8, 16, 0.3), 0px 0px 1px rgba(4, 8, 16, 0.32);
        }
        .page-t {
          flex: 1;
          color: #fff;
          font-size: 12px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .setting {
          display: none;
        }
      }
    }
    .setting {
      span {
        font-size: 14px;
        margin-left: 5px;
        cursor: pointer;
      }
      .icon {
        font-size: 18px;
        color: #fff;
        vertical-align: middle;
      }
    }
  }
  .fix-button {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 15px 0;
    text-align: center;
    border-top: 1px solid hsla(0, 0%, 100%, .1);
    button:first-child {
      margin-right: 20px;
    }
  }
  .w100 {
    width: 100%;
  }
}

.scene-dropdown {
  .el-dropdown-menu__item {
    line-height: 22px;
    font-size: 12px;
    color: #fafafa;
  }
}

.name-input {
  width: 150px;
  ::v-deep {
    .el-input__inner {
      background-color: #2d2e38;
    }
  }
}

</style>
