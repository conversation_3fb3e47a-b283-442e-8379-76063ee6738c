<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-12-08 18:09:47
 * @LastEditors: WangNing
 * @LastEditTime: 2022-12-09 00:13:20
 * @FilePath: /seatom/src/components/editor/scene/SceneSelect.vue
 * @Description:
-->
<template>
  <div class="scene-select-dialog">
    <el-dialog
      title="提示"
      :visible="true"
      width="400px"
      top="0"
      @close="$emit('cancelScene')"
    >
        所有页面均已存在人员编辑中，是否创建新页面并进入?
        <el-form ref="selectForm" :model="sceneParams" :rules="rules">
            <el-form-item prop="selectSceneId">
                <el-select v-model="sceneParams.selectSceneId" size="mini" class="scene-selector">
                    <el-option
                    v-for="item in screenList"
                    :key="item.sceneId"
                    :label="item.sceneName"
                    :value="item.sceneId"
                    ></el-option>
                </el-select>
            </el-form-item>
        </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="sceneConfirm">确定</el-button>
        <el-button class="poper-cancel" size="mini" @click="$emit('cancelScene')"
          >取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  data () {
    return {
      sceneParams: {
        selectSceneId: ''
      },
      rules: {
        selectSceneId: [
          { required: true, message: '请选择场景', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    ...mapState({
      coeditData: (state) => state.editor.coeditData,
      screenInfo: (state) => state.editor.screenInfo
    }),
    screenList () {
      const { sceneConfig } = this.screenInfo
      return sceneConfig.map(item => {
        return {
          sceneId: item.sceneId,
          sceneName: item.sceneName
        }
      })
    }
  },
  methods: {
    sceneConfirm () {
      this.$refs.selectForm.validate((valid) => {
        if (valid) {
          this.$emit('sceneConfirm', this.sceneParams.selectSceneId)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.scene-select-dialog {
    .scene-selector {
        width: 100%;
    }
}
</style>
