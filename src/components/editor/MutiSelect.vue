<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-11-09 17:32:25
 * @LastEditors: WangNing
 * @LastEditTime: 2022-12-08 15:49:51
 * @FilePath: /seatom/src/components/editor/MutiSelect.vue
 * @Description: 产品定义的下拉框组件，目前实现很抽象，产品要求多选后不要在下拉框内回显但要求已选中的要在选项中置灰，且点击添加按钮，已选项在选项内置灰
-->
<template>
  <div class="muti-select-container">
      <div class="select-wrapper">
        <el-select
          class="outer-select input-theme"
          size="mini"
          v-model="selectUsers"
          popper-class="poper-theme"
          placeholder="请选择"
          multiple
          filterable
          :clearable="false"
        >
          <el-option
            v-for="item in cumputedUserList"
            :key="item.userId"
            :label="item.userName"
            :value="item.userId"
          ></el-option>
        </el-select>
        <el-select
          class="input-theme inner-select"
          popper-class="poper-theme"
          v-model="selectRoleType"
          size="mini"
        >
          <el-option label="查看者" value="viewers"></el-option>
          <el-option label="协同者" value="collaborators"></el-option>
        </el-select>
      </div>
      <el-button type="primary" size="mini" @click="addUser" style="margin-left: 4px">添加</el-button>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
export default {
  name: 'MutiSelect',
  props: {
    // 所有用户
    userList: {
      type: Array,
      default: () => [],
      require: true
    },
    // 已选中列表，用于回显筛选
    selectdUserList: {
      type: Array,
      default: () => [],
      require: true
    }
  },
  data () {
    return {
      selectUsers: [],
      selectRoleType: 'viewers', // 所选角色
      selectedUserMap: {}, // 已经选择过的用户列表映射
      tempSelectUsersList: [] // 暂存已存储和已添加用户列表
    }
  },
  watch: {
    selectdUserList: {
      handler: function (val) {
        // this.selectUsers = val.map(item => item.userId)
        this.tempSelectUsersList = val
        this.selectedUserMap = {}
        this.tempSelectUsersList.forEach((item) => {
          this.selectedUserMap[item.userId] = item
        })
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    cumputedUserList () {
      // 已选中的不允许被选
      const result = []
      this.userList.forEach((item) => {
        if (this.selectedUserMap[item.userId]) {
          result.push({
            ...item,
            selected: true
          })
        } else {
          result.push({
            ...item,
            selected: false
          })
        }
      })
      return result
    }
  },
  methods: {
    // 过滤出新添加的项目
    filterNewSelected () {
      let newSelected = cloneDeep(this.selectUsers)
      // 选中的列表中过滤出之前保存过的选中项
      newSelected = newSelected.filter(item => {
        return !this.selectedUserMap[item]
      })
      return newSelected
    },
    // 批量添加用户
    addUser () {
      const newSelected = this.filterNewSelected()
      const selectdUserList = this.userList.filter(item => {
        return newSelected.includes(item.userId)
      })
        .map(item => {
          return {
            userId: item.userId,
            role: this.selectRoleType,
            userName: item.userName
          }
        })
      this.$emit('selectdUserChange', selectdUserList)
    }
  }
}
</script>

<style lang="scss" scoped>
.muti-select-container {
  display: flex;
  .select-wrapper {
    flex: 3;
    position: relative;
    font-size: 0;
    .outer-select {
      width: 100%;
      padding-right: 100px;
      ::v-deep {
        .el-input--mini .el-input__inner {
          height: 31px;
        }
        .el-icon-arrow-up:before {
          content: '';
        }
      }
    }
    .inner-select {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 100px;
      outline: none;
      border: none;
      margin-right: 0;
      background: transparent;
      ::v-deep {
        .el-input__inner {
          background-color: transparent;
        }
      }
    }
  }
  ::v-deep .el-button--primary {
    height: 32px;
    border-radius: 4px;
    background: var(--seatom-main-color);
    color: var(--seatom-type-900);
  }
}
</style>
