<template>
  <div class="import-screen">
    <el-dialog :visible.sync="importShow" title="大屏导入" width="500px" top="0" :close-on-click-modal="false" :before-close="close" :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'">
      <div class="dialog-content">
        <el-form :model="form" :rules="rules" label-width="90px" size="mini" ref="form">
          <el-form-item label="选择文件：">
            <el-upload
              ref="upload"
              size="mini"
              class="upload-demo"
              :class="[$route.name.includes('edit') ? '' : 'upload-theme']"
              multiple
              drag
              action=""
              accept=".tgz"
              :auto-upload="false"
              :file-list="fileList"
              :show-file-list="false"
              :on-change="handleChange">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">点击上传tgz文件，不能超过100M</div>
            </el-upload>
          </el-form-item>
          <el-form-item label="重命名：" v-if="fileList.length">
            <div class="file-list">
              <div class="file-item" v-for="file in fileList" :key="file.uid">
                <div class="file-name">
                  <div class="loading" v-if="loading">
                    <div class="inner" :style="{ width: file.percent + '%' }"></div>
                    <span class="percent" v-text="file.percent < 100 ? (file.percent + '%') : '上传成功'"></span>
                  </div>
                  <el-input size="mini" v-model="file.filename" placeholder="请输入大屏名称"></el-input>
                </div>
                <div class="file-oprate" v-if="!loading">
                  <span class="el-icon-close" @click.stop="deleteFile(file.uid)" title="删除"></span>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="" prop="origin">
            <el-checkbox v-model="form.origin">组件还原为默认数据</el-checkbox>
          </el-form-item>
          <el-form-item label="" prop="autoUpgrade">
            <el-checkbox v-model="form.autoUpgrade">组件升级到最新版本</el-checkbox>
          </el-form-item>
          <el-form-item label="分组：" prop="projectId">
            <el-select class="input-theme" popper-class="poper-theme" v-model="form.projectId" filterable>
              <el-option v-for="opt in manage" :value="opt.id" :label="opt.name" :key="opt.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button size="mini" type="text" @click="close">取消</el-button>
        <el-button size="mini" type="primary" @click="submit" :loading="loading">导入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { importScreen } from '@/api/screen'
export default {
  name: 'ImportScreen', // 导入大屏
  props: {
    value: Boolean,
    manage: {
      type: Array,
      default () {
        return []
      }
    },
    active: { // 当前分组
      type: [Number, String],
      default: 0
    }
  },
  data () {
    return {
      fileList: [],
      form: {
        name: '',
        projectId: '',
        origin: false,
        autoUpgrade: false
      },
      rules: {
        name: [
          { required: true, message: '请输入大屏名称', trigger: 'change' },
          { max: 30, message: '最大输入30个字', trigger: 'change' }
        ],
        projectId: [
          { required: true, message: '请选择分组', trigger: 'change' }
        ]
      },
      loading: false
    }
  },
  computed: {
    importShow: {
      get: function () {
        return this.value
      },
      set: function (val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    importShow: {
      handler: function (val) {
        if (val) {
          if (this.active === 0 || this.active === -1) {
            // 过滤掉全部应用和共享给我的
            this.form.projectId = this.manage.length ? this.manage[0].id : '';
            // if (this.form.projectId === 0) { this.form.projectId = 1 }
          } else {
            this.form.projectId = +this.active;
            if (this.form.projectId === 0) { this.form.projectId = this.manage.length ? this.manage[0].id : '' }
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleChange (file, fileList) {
      const list = _.cloneDeep(this.fileList);
      this.fileList = fileList.map(item => {
        const index = list.findIndex(file => file.uid === item.uid);
        let filename;
        let current;
        if (index >= 0) {
          filename = list[index].filename
          current = list[index]
        } else {
          filename = item.name.split('.tgz')[0];
          current = item
        }
        return {
          filename,
          percent: 0,
          ...current
        }
      });
    },
    deleteFile (uid) {
      const index = this.fileList.findIndex(file => file.uid === uid);
      if (index >= 0) {
        this.fileList.splice(index, 1);
      }
    },
    close () {
      this.fileList = [];
      this.importShow = false;
      this.$refs.form.resetFields();
    },
    submit () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          if (!this.fileList.length) {
            this.$message.error('请上传文件');
            return
          }
          this.loading = true;
          const promiseArr = this.fileList.map(item => {
            const data = new FormData();
            data.append('file', item.raw);
            data.append('workspaceId', this.$route.params.workspaceId);
            data.append('projectId', this.form.projectId);
            data.append('name', item.filename);
            data.append('timestamp', +new Date());
            data.append('origin', this.form.origin);
            data.append('autoUpgrade', this.form.autoUpgrade);
            return importScreen(data, {}, process => {
              const { loaded, total } = process;
              const percent = Math.floor(loaded / total * 100);
              item.percent = percent;
            })
          })
          try {
            await Promise.all(promiseArr);
            this.$message({
              html: true,
              text: '导入成功',
              duration: 1500,
              showClose: true,
              type: 'success'
            });
            this.$emit('refresh');
            this.close();
          } catch (e) {}
          this.loading = false;
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.import-screen {
  ::v-deep {
    .dialog-content {
      max-height: 400px;
      overflow: auto;
    }
    .el-form-item__label {
      color: #fafafa;
      font-size: 12px;
    }
    .el-upload-dragger {
      width: 300px;
      height: 140px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        font-size: 12px;
      }
    }
    .el-upload-dragger .el-icon-upload {
      font-size: 50px;
      margin: 20px 0 16px;
    }
  }
  .file-list {
    .file-item {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      &:hover {
        .file-oprate span {
          display: block;
        }
      }
      .file-name {
        position: relative;
        width: 300px;
        margin-right: 10px;
        .loading {
          position: absolute;
          width: 100%;
          height: 100%;
          font-size: 12px;
          background: rgb(41 47 60 / 0%);
          z-index: 10;
          .inner {
            height: 100%;
            background: rgb(28 184 255 / 69%);
            transition: width .3s linear;
          }
          .percent {
            position: absolute;
            right: 6px;
            top: 0;
          }
        }
      }
      .file-oprate {
        span {
          display: none;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
