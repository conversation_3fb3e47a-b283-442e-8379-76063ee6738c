<template>
  <el-dialog :visible.sync="show" title="终端交互" class="terminal" :width="isConnect ? '560px' : '475px'" :close-on-click-modal="false" top="0px">
    <div class="terminal-content">
      <div class="terminal-btn" v-if="!isConnect">
        <el-button type="primary" size="medium" @click="open">开启终端交互</el-button>
      </div>
      <div class="terminal-wrap" v-else>
        <div class="terminal-code">
          <div class="terminal-title">控制码：</div>
          <div class="terminal-input">{{ password }}</div>
        </div>
        <div class="terminal-qrcode">
          <VueQrcode :value="shareUrl" :options="options" />
        </div>
        <div class="terminal-help">
          <div class="main-title">终端交互 设备实例扫码</div>
          <div class="sub-title">通过手机或iPad可直接扫描上方二维码空间控制端界面，输入对应控制码即可远程操控</div>
        </div>
        <div class="terminal-media">
          <div class="terminal-media-device">电脑</div>
          <div class="terminal-media-device">手机</div>
          <div class="terminal-media-device">pad</div>
        </div>
      </div>
      <SeatomLoading v-if="loading" />
    </div>
  </el-dialog>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
import { screensocket, updateScreensocket } from '@/api/screen';
export default {
  name: 'Terminal', // 终端交互弹窗
  components: {
    VueQrcode
  },
  data () {
    return {
      show: false,
      isConnect: false,
      password: '',
      options: {
        width: 260,
        height: 260,
        margin: 1
      },
      loading: false
    }
  },
  props: {
    screenId: [String, Number]
  },
  computed: {
    shareUrl () {
      return `${window.location.origin}/screen/control/${this.screenId}`
    }
  },
  methods: {
    showDialog () {
      this.show = true;
      this.getScreensocket();
    },
    async open () {
      const data = {
        isConnect: true
      }
      this.loading = true;
      const res = await updateScreensocket(data, { screenId: this.screenId });
      if (res && res.success) {
        const data = res.data;
        this.isConnect = data.isConnect;
        this.password = data.password;
      }
      this.loading = false;
    },
    async getScreensocket () {
      const params = {
        screenId: +this.screenId
      }
      this.loading = true;
      const res = await screensocket(params);
      if (res && res.success) {
        const data = res.data;
        this.isConnect = data.isConnect;
        this.password = data.password;
      }
      this.loading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.terminal {
  .terminal-btn {
    padding: 20px 0;
    text-align: center;
    button {
      width: 315px;
      height: 50px;
    }
  }
  .terminal-wrap {
    width: 260px;
    margin: 0 auto;
    .terminal-code {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .terminal-title {
        width: 100px;
        font-size: 16px;
        font-weight: bold;
        color: rgba(255, 255, 255, 0.7);
      }
      .terminal-input {
        width: 266px;
        height: 32px;
        line-height: 32px;
        font-weight: bold;
        font-size: 18px;
        color: #fff;
        padding: 0 8px;
        background: rgba(255, 255, 255, 0.05);
      }
    }
    .terminal-qrcode {
      width: 100%;
      height: 260px;
      margin-bottom: 15px;
    }
    .terminal-help {
      text-align: center;
      margin-bottom: 30px;
      .main-title {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 15px;
      }
      .sub-title {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.32);
      }
    }
    .terminal-media {
      display: flex;
      justify-content: center;
      padding-bottom: 20px;
      .terminal-media-device {
        width: 70px;
        height: 30px;
        line-height: 28px;
        color: #fff;
        font-size: 14px;
        text-align: center;
        border: 1px solid #565f72;
        border-radius: 15px;
        & + .terminal-media-device {
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
