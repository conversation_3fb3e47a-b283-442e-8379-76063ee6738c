<!--
 * @Author: your name
 * @Date: 2021-09-10 11:36:28
 * @LastEditTime: 2023-01-18 18:51:34
 * @LastEditors: chenxingyu
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /seatom/src/components/editor/RenameDialog.vue
-->
<template>
<el-dialog
  title="重命名"
  :visible.sync="show"
  width="30%"
  top="0"
  :close-on-click-modal="false"
  @open="openDialog">
  <el-input v-focus v-model.trim="renameValue" size="mini"></el-input>
  <span slot="footer" class="dialog-footer">
    <el-button @click="show = false" size="mini">取 消</el-button>
    <el-button type="primary" @click="reNameClick" size="mini">确 定</el-button>
  </span>
</el-dialog>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import canvasBus from '@/utils/canvasBus';
export default {
  name: 'RenameDialog',
  inject: ['getLayerTree'],
  data () {
    return {
      show: false,
      renameValue: '',
      renameType: '',
      renameGroupId: ''
    }
  },
  computed: {
    ...mapState('editor', ['currentSelectId', 'screenInfo']),
    ...mapGetters('editor', ['getComDataById']),
    layerTree () {
      return this.getLayerTree();
    }
  },
  directives: {
    focus: {
      inserted: function (el) {
        el.querySelector('input').focus()
      }
    }
  },
  methods: {
    showDialog () {
      this.show = true;
    },
    openDialog () {
      const selectedNode = [];
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNode.push(item);
      })
      if (selectedNode.length === 1 && selectedNode[0].data.type === 'group') {
        this.renameValue = selectedNode[0].data.groupName
        this.renameGroupId = selectedNode[0].data.id;
        this.renameType = 'group';
      } else {
        this.renameValue = this.getComDataById(this.currentSelectId).alias;
        this.renameType = 'com'
      }
    },
    reNameClick () {
      if (!this.check()) return false;
      if (this.renameType === 'com') {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentSelectId,
          keyValPairs: [
            { key: 'alias', value: this.renameValue }
          ]
        }).then(() => {
          canvasBus.emit('select_node', null);
          this.$message.success('重命名成功')
        });
      } else if (this.renameType === 'group') {
        const renameLayer = this.renameGroup(this.layerTree.data.children);
        const copyTree = _.cloneDeep(this.layerTree.loadedNodes);
        copyTree.forEach(node => {
          delete node.tree;
          delete node.parent;
          if (node.children) this.formatCopyrTree(node.children);
        })
        localStorage.setItem('originTree', JSON.stringify(copyTree));

        const successCallback = () => {
          canvasBus.emit('select_node', null);
          this.$message.success('重命名成功');
          this.recoverTree();
        }

        // 调接口更新tree
        if (!!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene') {
          this.$store
            .dispatch('editor/updateScreenSelectedLayers', {
              screenId: this.screenInfo.id,
              layers: [renameLayer],
              onlyUpdateKeys: ['groupName']
            })
            .then(() => {
              successCallback()
            });
        } else {
          this.$store
            .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
            .then(() => {
              successCallback()
            });
        }
      }
      this.renameType = '';
      this.renameGroupId = '';
      this.show = false;
    },
    renameGroup (node) {
      let renameLayer
      node.forEach(item => {
        if (item.id === this.renameGroupId) {
          item.groupName = this.renameValue;
          renameLayer = item
        }
        if (item.children) {
          return this.renameGroup(item.children);
        }
      })
      return renameLayer
    },
    check () {
      if (!this.renameValue.trim()) {
        this.$message.error('名称不能为空');
        return false;
      }
      return true;
    },
    formatCopyrTree (nodes) {
      nodes.forEach(node => {
        delete node.tree;
        delete node.parent;
        if (node.children) this.formatCopyrTree(node.children);
      })
    },
    /* 对图层右键操作后不改变原有树文件夹的展开收起情况 */
    recoverTree () {
      let originTree = localStorage.getItem('originTree');
      originTree = JSON.parse(originTree);
      let i = 0; let j = 0;
      while (i < originTree.length) { // !(j === this.layerTree.loadedNodes.length && i === this.originTree.length)) { // j > i) {
        const tree = this.layerTree.loadedNodes;
        if (tree[j].data.id === originTree[i].data.id && tree[j].data.type === 'group') {
          tree[j].collapse = originTree[i].collapse;
          this.layerTree.collapseChildNodes(tree[j].data, tree[j].collapse);
          this.recursionChangeCollapse(tree[j].children, originTree[i].children);
          i++;
          j++;
        } else if (tree[j].data.id !== originTree[i].data.id) { // && tree[j].data.type === 'group') {
          // tree[j].collapse = true;
          // this.layerTree.collapseChildNodes(tree[j].data, tree[j].collapse);
          j++;
        } else if (tree[j].data.id === originTree[i].data.id) {
          i++;
          j++;
        }
      }
    },
    recursionChangeCollapse (tree, originTree) {
      tree.forEach((node, index) => {
        node.collapse = originTree[index].collapse;
        this.layerTree.collapseChildNodes(node.data, node.collapse);
        if (node.children) this.recursionChangeCollapse(node.children, originTree[index].children);
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
