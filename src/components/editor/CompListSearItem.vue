<template>
  <div
    class="components-item"
    :draggable="draggable"
    :title="title"
    @dragstart="handleDragStart"
    @click.stop="handleClick">
    <p class="components-from-text" v-if="!!compPanelType[0]">{{compPanelType[0].title}} / {{title}} / </p>
    <div class="flex-wp">
      <div class="components-item-img" :style="{ backgroundImage: backgroundImage }"></div>
      <div class="components-info">
        <p>{{title}}</p>
        <p>{{version}}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { compPanelMenu } from '@/common/constants'
import { replaceUrl } from '@/utils/base'
export default {
  name: 'CompListSearItem',
  props: {
    draggable: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: '//img.alicdn.com/tfs/TB17NeSDSf2gK0jSZFPXXXsopXa-160-116.png'
    },
    version: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    data: Object
  },
  created () {
  },
  computed: {
    backgroundImage: function () {
      let icon = this.icon || '//img.alicdn.com/tfs/TB17NeSDSf2gK0jSZFPXXXsopXa-160-116.png';
      icon = replaceUrl(icon)
      return 'url(' + icon + ')';
    },
    compPanelType () {
      return compPanelMenu.filter(item => {
        return item.type === this.type.split('-')[0]
      })
    }
  },
  methods: {
    handleClick () {
      this.$emit('click', this.data);
    },
    handleDragStart (e) {
      const dragData = {
        type: 'com',
        data: this.data
      };
      e.dataTransfer.setData('application/json', JSON.stringify(dragData));
    }
  }

}
</script>

<style lang="scss" scoped>
.components-item {
  width: 100%;
  margin: 0;
  padding: 5px;
  padding-bottom: 10px;
  border-bottom: 1px solid #2f343c;
  cursor: pointer;
  color: #bcc9d4;
  vertical-align: top;
  user-select: none;
  box-sizing: border-box;
  overflow: hidden;
  &:hover {
    background: #252b31;
  }
  .components-from-text {
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .flex-wp {
    display: flex;
    .components-item-img {
      width: 80px;
      height: 45px;
      background-clip: content-box;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      background-color: #17191c;
      position: relative;
      text-align: center;
      box-sizing: content-box;
      transition: border-color .2s;
    }
    .components-info {
      flex: 1;
      margin-left: 5px;
    }
  }
}

</style>
