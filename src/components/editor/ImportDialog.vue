<!--
 * @Author: jiashuangxi
 * @Date: 2022-01-04 11:09:17
 * @LastEditors: chenxingyu
 * @LastEditTime: 2023-01-19 10:41:10
 * @Describe:
-->
<template>
  <el-dialog
    append-to-body
    class="import-dialog"
    :visible.sync="show"
    :title="diag.title"
    :width="diag.widthSet"
    top="0"
    :close-on-click-modal="false"
    :before-close="close"
  >
    <div class="import-content">
      <div
        v-loading="treeLoading"
        element-loading-background="#1F2430"
        class="import-content__left"
      >
        <el-input
          placeholder="搜索"
          size="mini"
          @input="searchTree"
          v-model="filterText"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-tree
          ref="tree"
          class="filter-tree"
          :data="projTree"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ data }">
            <span class="tree-title">
              <i
                v-if="data.attribute == 'dashboard' && data.category != 1"
                class="el-icon-document"
              ></i>
              <i v-else class="el-icon-folder-opened"></i>
              <span class="node-name nowrap" :title="data.name"
                >{{ data.name }}</span
              >
            </span>
          </span>
        </el-tree>
      </div>
      <!-- 选择图表类型 -->
      <div class="import-content__mid" element-loading-background="#1F2430">
        <div class="mid-top">
          <p class="mid-top__title">选择图表（最多支持三个图表）</p>
          <el-input
            class="mid-top__input"
            placeholder="搜索"
            size="mini"
            @input="searchKind"
            v-model="filterKind"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <div class="chart-list" v-if="chartsData && chartsData.length > 0">
          <div class="chart-nodata" v-if="this.chartsNewData.length == 0">
            此图表类型暂无
          </div>
          <div class="chart-list__item">
            <!-- :disabled="disableChart" -->
            <el-checkbox-group
              v-model="chartList"
              :max="diag.maxset"
              ref="checkBox"
              @change="handleCheckedChange($event)"
              style="display: flex;
                      flex-direction: row;
                      flex-wrap: wrap;
                      justify-content: space-between;"
            >
              <el-checkbox
                border
                style="width:135px; height:40px; margin:10px; border-radius:8px; box-shadow: 3px 3px 5px #293246"
                class="chart-el-check"
                @change="handleChange($event, index, item)"
                v-for="(item, index) in chartsNewData"
                :key="item.ct_id"
                :label="item"
                :title="item.name"
                ><span
                  class="loading-mask"
                  v-if="activeIndex == index"
                  v-loading="listLoading"
                ></span
                ><hz-icon :name="item.type" class="hzSet"></hz-icon>&nbsp;<span class="textName">{{
                  item.name
                }}</span></el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="chart-nodata" v-else>无数据</div>
      </div>
      <!-- 导入为 -->
      <div class="import-content__right" v-if="selectHidden">
        <div class="right-top">
          <p class="right-top__title">导入为</p>
          <template v-if="chartList && chartList.length > 0">
            <div
              class="right-top__block"
              v-for="item in selectData"
              :key="item.ct_id"
            >
              <div class="top-title">
                <span class="top-title-name">{{ item.name}}</span
                ><br />
                <span class="top-title-nor">{{
                  compPackgesMap[item.comType].alias
                }}</span>
              </div>
              <el-radio-group  v-model="item.ImportRadio" size="mini">
                <el-radio label="ImportSet">导入</el-radio>
                <el-radio label="ReplaceSet">替换</el-radio>
                <el-select
                  class="select-right"
                  v-model="item.comType"
                  v-if="
                    item.comList.length !== 0 &&
                      item.ImportRadio === 'ReplaceSet'
                  "
                  placeholder="请选择"
                  size="mini"
                >
                  <el-option
                    v-for="i in item.comList"
                    :key="i.name"
                    :label="i.alias"
                    :value="i.name"
                  >
                  </el-option>
                </el-select>
              </el-radio-group>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button
        type="light-blue"
        size="medium"
        @click="submit"
        :loading="loading"
        >确定</el-button
      >
      <el-button type="text" size="medium" @click="closeDialog">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getImportTree, getImportDashboard, getCheckChartData } from '@/api/screen';
import { createDatastorage, getData } from '@/api/datastorage';
import { mapState, mapGetters } from 'vuex';
import { uuid } from '@/utils/base'
import dataUtil from '@/utils/data'
import canvasBus from '@/utils/canvasBus'
export default {
  name: 'ImportDialog',
  inject: ['getLayerTree'],
  components: {},
  props: { importDash: String },
  data () {
    return {
      // 选择图表是返回的index
      activeIndex: -1,
      // 树型加载
      treeLoading: false,
      listLoading: false,
      // 选择时-导入为默认为状态
      selectHidden: false,
      // 获取仪表盘树型-列表数据-选择操作
      disableChart: false,
      // 当前选择数据
      currentObj: {},
      // 导入为-列表数据
      selectKind: [],
      // 选择当前图表类型数据
      chartList: [],
      // 搜索数据
      filterText: '',
      // 搜索选择图表类型
      filterKind: '',
      ImportName: '',
      // 弹出框显示
      show: false,
      projTree: [],
      // 选择图表类型数据
      chartsData: [],
      chartsNewData: [],
      arr: [],
      tableData: {},
      comTypeChart: '',
      defaultProps: {
        label: 'name',
        children: 'subs'
      },
      screenId: '',
      loading: false,
      isPublic: false,
      // 弹出框
      diag: {
        title: '从仪表盘导入',
        widthSet: '1000px',
        maxset: 3
      },
      // 表单数据
      form: {
        dsh_id: ''
      },
      selectData: [],
      fieldMapping: []
    };
  },
  watch: {
    filterText (val) {
      if (val.length === 0) {
        this.searchTree();
      }
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      compPackgesMap: state => state.editor.compPackgesMap
    }),
    ...mapGetters('editor', [
      'getComDataById',
      'ctxMenuList',
      'cursnapshotData'
    ]),
    // 树型列表数据
    layerTree () {
      return this.getLayerTree();
    }
  },
  methods: {
    // 选择图表类型-改变时操作
    async handleChange (value, index, item) {
      this.activeIndex = index;
      this.listLoading = true;
      if (value) {
        const res = await getCheckChartData({
          dsh_id: this.form.dsh_id,
          ct_id: item.ct_id
        });
        if (res && res.success) {
          const selectItem = {
            ct_id: item.ct_id,
            comType: res.data.comType,
            name: item.name,
            comList: res.data.comList,
            ImportRadio: 'ImportSet'
          };
          this.selectData.push(selectItem);
        } else {
          this.chartList = this.chartList.filter(skitem => {
            return skitem.ct_id !== item.ct_id;
          });
          this.$message.error(res.message);
        }
      } else {
        this.selectData = this.selectData.filter(sitem => {
          return sitem.ct_id !== item.ct_id;
        });
      }
      this.listLoading = false
    },

    // 选择图表类型文件
    handleCheckedChange (item) {
      this.selectKind = item;
    },
    // 树节点进行筛选时执行
    filterNode (value, data, node) {
      if (!value) {
        node.expanded = false;
        return true;
      };
      // const val = value.toLowerCase();
      return this.chooseNode(value, data, node);
      // return data.name.indexOf(value) !== -1;
    },
    chooseNode (value, data, node) {
      if (data.name.indexOf(value) !== -1) {
        return true;
      }
      const level = node.level;
      if (level === 1) {
        return false;
      }
      let parentData = node.parent;
      let index = 0;
      while (index < level - 1) {
        if (parentData.data.name.indexOf(value) !== -1) {
          return true;
        }
        parentData = parentData.parent;
        index++;
      }
      return false;
    },
    // 节点被点击时的回调
    handleNodeClick (data) {
      this.tableData = data;
      this.chartList = [];
      this.selectData = [];
      this.form.dsh_id = data.dsh_id;
      if (this.form.dsh_id !== undefined) {
        this.getImportDash();
      }
    },
    // 树型-搜索
    searchTree: _.debounce(function () {
      this.$refs.tree.filter(this.filterText);
      // this.getImportInfo();
    }, 500),
    searchKind: _.debounce(function () {
      // this.getImportInfo();
      this.arr = [];
      this.chartsNewData = _.cloneDeep(this.chartsData);
      for (let i = 0; i < this.chartsData.length; i++) {
        if (this.chartsData[i].name.indexOf(this.filterKind) >= 0) {
          this.arr.push(this.chartsData[i]);
          this.chartsNewData = this.arr;
        }
      }
      if (this.arr.length === 0) {
        this.chartsNewData = [];
      }
      return this.arr;
    }, 500),

    // 显示弹出框
    showImport () {
      this.show = true;
      // 数据源时-选择弹出框
      if (this.importDash) {
        this.diag.maxset = 1;
        this.diag.widthSet = '600px';
        this.selectHidden = false;
      } else {
        this.selectHidden = true;
      }
      this.chartsData = [];
      this.selectData = [];
      this.getImportInfo();
    },

    // 关闭
    close () {
      this.show = false;
      this.$nextTick(() => {
        this.isPublic = false;
      });
      this.$emit('close');
    },

    // 获取仪表盘树型-列表数据
    async getImportInfo () {
      this.treeLoading = true;
      try {
        const res = await getImportTree();
        if (res && res.success) {
          this.projTree = res.data.proj;
          this.treeLoading = false;
        }
      } catch (e) {
        this.projTree = [];
      }
    },

    // 获取仪表盘树型-列表数据
    async getImportDash () {
      try {
        const res = await getImportDashboard({ dsh_id: this.form.dsh_id });
        if (res && res.success) {
          this.chartsData = this.generateChartsMap(res.data.meta.charts);
          this.chartsNewData = this.chartsData;
        }
      } catch (e) {}
    },

    // 生成所需的chart结构
    generateChartsMap (chartsMap) {
      const charts = [];
      for (const chart of chartsMap) {
        const chartInfo = _.cloneDeep(chart.children[0].meta);
        chartInfo.check = false;
        charts.push(chartInfo);
      }
      return charts;
    },

    // 关闭弹出框
    closeDialog () {
      this.show = false;
      this.filterText = '';
    },

    // 确定操作
    async submit () {
      // 如果选择图表类型为空时
      if (this.selectData.length === 0) {
        this.$message.error('请选择图表类型');
        return;
      }
      for (let index = 0; index < this.selectData.length; index++) {
        const selectItem = this.selectData[index];
        const data = {
          workspaceId: this.screenInfo.workspaceId,
          type: 'dashboard',
          name: selectItem.comType + new Date().getTime(),
          description: '',
          config: {
            dsh_id: this.form.dsh_id,
            ct_id: selectItem.ct_id,
            name: selectItem.name
          }
        };
        const res = await createDatastorage(data);
        const sourceId = res.data.id;
        if (res) {
          const data1 = {
            id: uuid(selectItem.comType),
            name: selectItem.comType,
            version: this.compPackgesMap[selectItem.comType].version,
            attr: {
              w: this.compPackgesMap[selectItem.comType].width,
              h: this.compPackgesMap[selectItem.comType].height,
              x: 0 + index * 500,
              y: 0
            }
          };
          const source = {
            data: {
              name: res.data.config.name,
              sourceId: sourceId,
              isLimit: false,
              limitNum: 20
            }
          };
          this.createCom(data1, source);
        }
      }
      this.show = false;
      this.filterText = '';
    },
    async createCom (data, source) {
      if (!data) return false;
      const res = await this.$store.dispatch('editor/createScreenComp', data);
      await this.$store.dispatch('editor/updateScreenCom', {
        id: data.id,
        keyValPairs: [
          { key: 'dataConfig.dataResponse.sourceType', value: 'dashboard' },
          { key: 'dataConfig.dataResponse.source.dashboard', value: source }
        ]
      })
      if (!res) return false
      const newData = this.getComDataById(data.id)
      const comData = await getData({ workspaceId: this.screenInfo.workspaceId, type: 'dashboard', componentId: newData.id })
      const newfieldMapping = _.cloneDeep(newData.dataConfig.fieldMapping)
      if (comData.success) {
        this.fieldMapping = dataUtil.fieldAutoMapping(newfieldMapping, comData.data);
        await this.$store.dispatch('editor/updateScreenCom', {
          id: data.id,
          keyValPairs: [
            { key: 'dataConfig.fieldMapping', value: this.fieldMapping }
          ]
        })
      }
      if (newData) {
        const layer = {
          id: newData.id,
          type: newData.type
        };
        if (this.screenInfo.screenType === 'scene') {
          layer.sceneId = data.sceneId;
          layer.pageId = data.pageId;
        }
        this.layerTree.addChild(layer)

        if (!!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene') {
          await this.$store.dispatch('editor/insertScreenLayers', {
            screenId: this.screenInfo.id,
            layers: [layer]
          })
        } else {
          await this.$store.dispatch('editor/updateScreenLayers', this.layerTree.data.children)
        }

        this.$store.commit('editor/updateCurrentSelectId', newData.id)
        this.layerTree.select(newData.id)
        canvasBus.emit('select_node', newData.id)
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.import-dialog {
  ::v-deep .el-tree-node.is-current {
    background: rgba(61, 133, 255, 0.1);
  }
  .hzSet {
    width: 32px;
    height: 32px;
    font-size: 16px;
    vertical-align: middle;
  }
  .textName {
    position: relative;
    top: 6px;
    display: inline-block;
    width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .import-content {
    display: flex;
    height: 324px;
    overflow: hidden;
    &__left {
      width: 256px;
      padding-right: 10px;
      margin-right: 10px;
      border-right: 1px solid #293246;
      ::v-deep .el-tree {
        background: none;
      }
      .filter-tree {
        margin-top: 14px;
        max-height: 284px;
        .el-tree-node:focus > .el-tree-node__content {
          background-color: #3d85ff !important;
        }
        .tree-title {
          color: #fff;
          font-size: 12px;
          .node-name {
            padding-left: 5px;
          }
        }
        .el-icon-document {
          font-size: 16px;
        }
        .el-icon-folder-opened {
          font-size: 16px;
        }
        .nowrap {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          display: inline-block;
          width: 146px;
          vertical-align: sub;
        }
        .icon-red {
          color: #ad5419;
        }
      }
      ::v-deep .el-input .el-input__inner {
        border-top: 0px;
        border-left: 0px;
        border-right: 0px;
        background-color: rgba(204, 219, 255, 0.06);
      }
    }
    &__mid {
      width: 316px;
      position: relative;

      .mid-top {
        height: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        &__title {
          font-size: 12px;
          color: #fff;
          margin: 0;
        }
        &__input {
          width: 138px;
          ::v-deep .el-input__inner {
            border-top: 0px;
            border-left: 0px;
            border-right: 0px;
            background-color: rgba(204, 219, 255, 0.06);
          }
        }
      }
      .chart-list {
        width: 316px;
        height: 290px;
        overflow: auto;
        ::v-deep .el-checkbox {
          &.is-bordered {
            margin-bottom: 10px;
          }
          &.is-bordered + .el-checkbox.is-bordered {
            margin-left: 0px;
          }
          .el-checkbox {
            &__inner {
              display: none;
            }
            &__label {
              padding: 0;
              font-size: 12px;
              color: #fff;
              position: absolute;
              bottom: 50%;
              left: 50%;
              transform: translate(-50%, 50%);
                .loading-mask{
                  top: 3px;
                  left: -4px;
                  .el-loading-mask{
                    background-color: rgba(0,0,0,0);
                    .el-loading-spinner{
                      .circular{
                        width: 40px;
                        height: 40px;

                      }
                    }
                  }
                }
            }
          }
        }
      }
      .chart-nodata {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
        color: #fff;
      }
    }
    &__right {
      width: 365px;
      padding-left: 10px;
      margin-left: 10px;
      border-left: 1px solid #293246;
      .right-top {
        &__title {
          font-size: 12px;
          color: #fff;
          margin-bottom: 10px;
        }
        &__block {
          height: 70px;
          ::v-deep .el-radio {
            margin-bottom: 10px;
            display: block;
            &__label {
              font-size: 12px;
              color: #fff;
            }
          }
          .right-top__block {
            height: 60px;
          }
          .top-title {
            width: 120px;
            float: left;
            margin-right: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &-name {
              font-size: 12px;
              color: #fff;
            }
            &-nor {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
            }
          }
          .select-right {
            position: relative;
            bottom: 31px;
            left: 65px;
            width: 80%;
            ::v-deep .el-input .el-input__inner {
              border-top: 0;
              border-left: 0;
              border-right: 0;
              height: 28px;
              line-height: 28px;
              background-color: rgba(204, 219, 255, 0.06);
            }
          }
        }
      }
    }
  }
}
</style>
