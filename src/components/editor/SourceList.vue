<template>
  <div class="source_list">
   <div class="source_title">
     <span class="source_title_span" v-for="(item, index) in items" :key="item" :style="active == index ? { color: '#2567c5'} : null" @click="handleClickTitle(index)">{{item}}</span>
     <span class="el-icon-close" @click.stop="$store.commit('editor/updateEditPanelSelect', { type: 'tool', value: false })"></span>
   </div>
   <div class="source_container">
     <div class="container_comp">
        <component :is="this.createOb[this.active]"></component>
     </div>
   </div>
  </div>
</template>

<script>
import ImgList from './source-list/ImgList'
import ThemeList from './source-list/ThemeList'
export default {
  name: 'SourceList',
  data () {
    return {
      items: ['图片素材', '功能主题'],
      active: 0
    };
  },
  components: {
    ThemeList,
    ImgList
  },
  computed: {
    createOb () {
      return {
        0: ImgList,
        1: ThemeList
      }
    }
  },
  beforeCreate () {
    this.$store.commit('editor/updateDrawerSelect', { type: 'filter', value: false })
  },
  methods: {
    handleClickTitle (index) {
      this.active = index
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/style/mixins';
.source_list{
  position: relative;
  height: 100%;
  color: white;
  .source_title{
    position: relative;
    height: 40px;
    line-height: 40px;
    margin: 10px;
    .source_title_span{
      display: inline-block;
      width: 68px;
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      font-family: "思源黑体Medium", "PingFang SC", "Helvetica Neue", Arial, sans-serif !important;
      color: white;
      margin-left: 10px;
      cursor: pointer;
    }
    .el-icon-close{
      width: 24px;
      height: 40px;
      float: right;
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .source_container{
    width: 100%;
    height: calc(100% - 60px);
    .container_comp{
      width: 100%;
      height: 100%;
      background: #22242b;
    }
  }
}
</style>
