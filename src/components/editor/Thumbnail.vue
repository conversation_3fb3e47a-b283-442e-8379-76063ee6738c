<template>
  <div class="thumbnail">
    <el-dialog :visible.sync="show" title="封面" width="400px" :close-on-click-modal="false" :before-close="closeDialog" top="0">
      <el-form label-width="100px">
        <el-form-item label="封面">
          <UploadImage v-model="thumbnail" style="width: 200px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="record" :loading="loading">{{ loading ? '截屏中' : '截屏' }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import UploadImage from '@/components/editor/data-source/UploadImage';
import { recordScreen } from '@/api/screen';
import { mapState } from 'vuex';
import { replaceUrl } from '@/utils/base';
export default {
  name: 'Thumbnail', // 封面
  components: {
    UploadImage
  },
  data () {
    return {
      show: false,
      loading: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    thumbnail: {
      get: function (val) {
        return this.screenInfo.config.thumbnail
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.thumbnail', value: val }]);
      }
    }
  },
  methods: {
    showDialog () {
      this.show = true;
    },
    closeDialog () {
      this.show = false;
    },
    async record () {
      try {
        this.loading = true;
        const res = await recordScreen({ screenId: this.screenInfo.id, width: this.width, height: this.height });
        if (res && res.success) {
          this.thumbnail = replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.screenshotUrl) + '?t=' + Date.now();
        }
        this.loading = false;
      } catch (e) {
        this.loading = false;
      }
    }
  }
}
</script>
