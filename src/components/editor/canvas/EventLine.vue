<template>
    <svg class="event-line">
      <defs>
        <marker
          id="arrowEnd"
          orient="auto"
          markerUnits="strokeWidth"
          markerWidth="8"
          markerHeight="8"
          refX="2"
          refY="4">
          <path
            :d="arrowD"
            fill="#3D85FF"
            stroke-width="0"/>
        </marker>
      </defs>
      <path
        :d="pathD"
        marker-end="url(#arrowEnd)"
        stroke="#3D85FF"
        fill="transparent"
        stroke-width="2px">
      </path>
    </svg>
</template>

<script>
export default {
  data () {
    return {}
  },
  props: {
    start: {
      type: Object,
      default () {
        return {
          x: 0,
          y: 0
        };
      }
    },
    end: {
      type: Object,
      default () {
        return {
          x: 0,
          y: 0
        };
      }
    },
    width: {
      type: Number
    },
    height: {
      type: Number
    }
  },
  computed: {
    pathD () {
      return `M${this.start.x} ${this.start.y},Q${((this.end.x / 2) > (this.end.x - this.start.x)) ? this.end.x : this.end.x / 2} ${this.start.y}, ${this.end.x} ${this.end.y}`
    },
    arrowD () {
      return 'M8,4 L0,8 L0,0'
    }
  }

}
</script>

<style  lang="scss" scoped>
.event-line {
  width: 100%;
  height: 100%;
  z-index: -10;
}

</style>
