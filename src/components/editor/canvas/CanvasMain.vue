<template>
  <div class="canvas-main" :class="{ pb35: platform === 'mobile' }">
    <div
      ref="canvasWrap"
      id="canvas-wp"
      class="canvas-panel-wrap">
      <div
        class="screen-shot"
        @click.self="handleScreenShotSelfClick"
        :style="{
          width: shotWidth + 'px',
          height: shotHeight + 'px'
        }"
      >
        <SeatomRuler :scale="scale" :translate="rulerTranslate" :size="rulerSize"/>
        <CanvasPanel id="screenshoter" ref="canvasPanel"  v-if="platform === 'pc'" />
        <MobileCanvasPanel ref="canvasPanel" v-else-if="platform === 'mobile'"/>
      </div>
    </div>
    <CanvasThumbnail @canvasScoll="handleCanvasScoll" :translate="rulerTranslate" v-if="platform === 'pc'"/>
    <div class="edit-slider">
      <div class="slider-tips" v-clickoutside="close">
        <span @click="toggle"><hz-icon class="mr-4" name="shortcut"></hz-icon>快捷键</span>
        <ShortcutTips v-show="show"/>
      </div>
      <SeatomSlider v-model="scale" v-if="platform === 'pc'"/>
    </div>
  </div>
</template>

<script>
import CanvasPanel from './CanvasPanel'
import MobileCanvasPanel from './MobileCanvasPanel'
import ShortcutTips from '../ShortcutTips'
import CanvasThumbnail from './CanvasThumbnail'
import { mapState } from 'vuex'
import canvasBus from '@/utils/canvasBus'
import { getFilters } from '@/api/filter'
import _ from 'lodash'
import { getComponentInfo } from '@/api/component'
import dataUtil from '@/utils/data'
import { getData } from '@/api/datastorage'

export default {
  name: 'CanvasMain',

  components: {
    CanvasPanel,
    MobileCanvasPanel,
    ShortcutTips,
    CanvasThumbnail
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      editPanelState: state => state.editor.editPanelState
    }),
    platform () {
      return this.screenInfo.type
    },
    scale: {
      get () {
        return this.screenInfo.config.scale
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.scale', value: val }])
      }
    },
    width: {
      get () {
        return this.screenInfo.config.width
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.width', value: val }])
      }
    },
    height: {
      get () {
        return this.screenInfo.config.height
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.height', value: val }])
      }
    },
    rulerSize () {
      return {
        width: Math.max(this.shotWidth - 20, 0),
        height: Math.max(this.shotHeight - 20, 0)
      }
    }
  },

  data () {
    return {
      shotWidth: 0,
      shotHeight: 0,
      rulerTranslate: {
        x: 0,
        y: 0
      },
      show: false
    }
  },

  watch: {
    'scale' () {
      this.updateShotSize()
      this.domObserver()// 监听视口的变化
    },
    '$route.query': {
      async handler (val) {
        if (val?.comp === 'pitch') {
          const res = await getData({
            componentId: this.$route.query.cid,
            type: val.type,
            workspaceId: this.screenInfo.workspaceId
          })
          const parentFiltersRes = await getFilters({ screenId: this.$route.query.screenId })
          const parentFilters = _.keyBy(parentFiltersRes.data, 'id')
          const dataResponse = (await getComponentInfo({ cid: this.$route.query.cid }, { id: this.$route.query.screenId })).data.dataConfig.dataResponse
          const filter = _.filter(dataResponse.filters.list, { enable: true })
            .map(({ id }) => parentFilters[id])
          const filterData = dataUtil.filterData(res.data, filter)
          this.$store.commit('editor/setInheritData', filterData?.[0]?.data?.[0] || [filterData[0]])
        }
      },
      deep: true,
      immediate: true
    }
  },

  mounted () {
    this.updateShotSize()
    this.$refs.canvasWrap.addEventListener('scroll', this.handleScroll)
    this.domObserver() // 监听视口的变化
    if (this.platform === 'mobile') { // 移动端编辑器标尺高度动态调整
      const canvasPanel = this.$refs.canvasPanel?.$el;
      if (canvasPanel) {
        const ob = new ResizeObserver(_.throttle(entries => {
          entries.forEach(entry => {
            const { height } = entry.contentRect;
            this.shotHeight = height + 380;
          })
        }, 300))
        ob.observe(canvasPanel)
      }
    }
  },

  beforeDestroy () {
    canvasBus.off()
    const canvasWrap = this.$refs.canvasWrap
    this.myObserver.unobserve(canvasWrap)
  },

  methods: {
    handleScreenShotSelfClick (e) {
      canvasBus.emit('canvas_click')
    },
    updateShotSize () {
      const canvasWrap = this.$refs.canvasWrap
      const scaleWidth = this.width * this.scale
      const scaleHeight = this.height * this.scale
      const clientWidth = canvasWrap.clientWidth
      const clientHeight = canvasWrap.clientHeight
      const left = 80
      const top = 80
      const margin = 300
      if (clientWidth < scaleWidth + left) {
        this.shotWidth = scaleWidth + left + margin
      } else {
        this.shotWidth = clientWidth
      }
      if (clientHeight < scaleHeight + top) {
        this.shotHeight = scaleHeight + top + margin
      } else {
        this.shotHeight = clientHeight
      }
    },
    handleScroll () {
      const canvasWrap = this.$refs.canvasWrap
      this.rulerTranslate.x = canvasWrap.scrollLeft
      this.rulerTranslate.y = canvasWrap.scrollTop
      canvasBus.emit('thumbnail_scoll', this.rulerTranslate)
    },
    toggle () {
      this.show = !this.show
    },
    close () {
      if (this.show) {
        this.show = false
      }
    },
    handleCanvasScoll ({ canvasX, canvasY }) { // 鸟瞰图驱动
      const canvasWrap = this.$refs.canvasWrap
      canvasWrap.scrollLeft = canvasX
      canvasWrap.scrollTop = canvasY
    },
    domObserver () {
      this.myObserver = new ResizeObserver(_.throttle(entries => {
        entries.forEach(entry => {
          const { width: screenW, height: screenH } = entry.contentRect
          this.screenW = screenW
          this.screenH = screenH
          // console.log(this.$refs.canvasWrap.getBoundingClientRect())
          canvasBus.emit('handle-select', { screenW, screenH, ...this.rulerSize })
        })
      }, 300))
      const canvasWrap = this.$refs.canvasWrap
      this.myObserver.observe(canvasWrap)
    }
  }
}
</script>

<style lang="scss" scoped>
.canvas-main {
  flex: 1;
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  user-select: none;
  padding-bottom: 30px;
  &.pb35 {
    padding-bottom: 35px;
  }
  .canvas-panel-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
  }
  .edit-slider {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #222528;
    box-shadow: 0 -1px #000;
    user-select: none;
    z-index: 1;
    padding-left: 20px;
  }
  .slider-tips {
    display: block;
    height: 30px;
    line-height: 30px;
    width: 65px;
    color: #fff;
    cursor: pointer;
    position: relative;
    font-size: 14px;
  }
  .mr-4 {
    margin-right: 4px;
  }
  .thumbnail {
    width: 192px;
    height: 108px;
    right: 5px;
    position: absolute;
    background: #0a0028;
    bottom: 35px;
  }
}
</style>
