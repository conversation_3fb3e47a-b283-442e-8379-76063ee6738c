<template>
  <div
    :key="key"
    class="canvas-panel mobile-canvas"
    :style="panelStyle"
    ref="canvasPanel"
    @click.self="handlePanelSelfClick"
    @dragover.prevent="handleDragOver"
    @drop.prevent="handleDrop"
    v-contextmenu:contextmenu>
    <grid-layout
      ref="gridlayout"
      :layout.sync="layout"
      :col-num="24"
      :row-height="10"
      :is-draggable="true"
      :is-resizable="true"
      :is-mirrored="false"
      :vertical-compact="true"
      :margin="[5, 5]"
      :use-css-transforms="true"
      @layout-updated="layoutUpdatedEvent">
      <grid-item
        :class="{
          isActive: isActive(item)
        }"
        :ref="item.i"
        v-for="item in layout"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        :key="item.i"
        :static="item.lock"
        @click.native.stop="handleCompClick(item)"
        @resized="resizedEvent"
        v-contextmenu:contextmenu>
        <EditCompNode
          v-if="item.i !== 'drop' && getComDataById(item.i)"
          :id="item.i"
          :platform="'mobile'"
          :type="getComDataById(item.i).type" />
      </grid-item>
    </grid-layout>
    <!-- 右键菜单（暂时用 div 解决报错问题 https://github.com/vuejs/vue/issues/11838)-->
    <div style="display:none;">
      <v-contextmenu
        ref="contextmenu"
        @contextmenu="handleCtxMenu">
        <v-contextmenu-item
          v-for="menu in ctxMenuList"
          v-show="menu.show && !isModule"
          :key="menu.key"
          :class="[{'contextmenu-disable': (!cancelGroupShow && menu.key === 'cancelGroup') ||
          (menu.key === 'group' && groupShow)}]"
          @click="handleCtxClick(menu.key)">
          {{ menu.label }}
        </v-contextmenu-item>
      </v-contextmenu>
    </div>
  </div>
</template>

<script>
import VueGridLayout from '@/lib/vue-grid-layout.common';
import EditCompNode from '@/components/editor/canvas/EditCompNode';
import { mapState, mapGetters } from 'vuex';
import { uuid } from '@/utils/base';
import canvasBus from '@/utils/canvasBus';
import '@/components/plugins/shortcut';
import Rect from '@/utils/Rect';

const mouseXY = { x: null, y: null };
const DragPos = { x: null, y: null, w: 1, h: 1, i: null };

export default {
  name: 'MobileCanvasPanel',

  inject: ['getLayerTree'],

  components: {
    EditCompNode,
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem
  },

  data () {
    return {
      key: 0,
      // componentList: [], // 子组件 VueDragResize 实例数组
      comStartStyle: null,

      isShowArea: false, // 是否显示选中区域框
      areaStart: { x: 0, y: 0 },
      areaWidth: 0,
      areaHeight: 0,

      isShowLine: false, // 是否显示连线
      lineStart: { x: 0, y: 0 },
      lineEnd: { x: 0, y: 0 },

      handleRect: { // 拖拽及缩放的处理矩形
        show: false,
        w: 0,
        h: 0,
        x: 0,
        y: 0,
        r: 0
      },

      lineEventComId: null, // 事件连线组件ID

      isClick: true,
      layout: []
    };
  },

  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      sceneId: state => state.editor.sceneId,
      pageId: state => state.editor.pageId,
      currentSelectId: state => state.editor.currentSelectId,
      screenComs: state => state.editor.screenComs
    }),
    ...mapGetters('editor', ['getComDataById', 'ctxMenuList']),
    layerTree () {
      return this.getLayerTree();
    },
    comLayerArr () {
      let nodes = this.layerTree.data.children || [];
      nodes = nodes.filter(item => !item.id.startsWith('interaction-container-modulepanel'))
      if (nodes && nodes.length) {
        const compNodes = [];
        nodes.forEach(n => {
          let comData;
          if (n.type === 'com') {
            comData = this.getComDataById(n.id);
          } else {
            comData = this.getComDataById(n.comId);
          }
          if (comData) {
            compNodes.push({
              id: comData.id,
              type: 'com',
              attr: comData?.attr,
              sceneId: comData?.sceneId,
              pageId: comData?.pageId
            })
          }
        });
        const { screenType } = this.screenInfo;
        if (screenType === 'scene') {
          if (this.pageId) {
            return compNodes.filter(node => node.pageId === this.pageId || (node.sceneId === this.sceneId && !node.pageId));
          } else {
            return compNodes.filter(node => node.sceneId === this.sceneId && !node.pageId);
          }
        } else {
          return compNodes
        }
      }
      return [];
    },
    componentList () {
      return Object.values(this.screenComs)
    },
    comIdMap () {
      return _.keyBy(this.componentList, 'id');
    },
    scale () {
      return this.screenInfo.config.scale;
    },
    isMutiSelect () {
      return this.currentSelectNodes && this.currentSelectNodes.length > 1;
    },
    currentSelectNodes () {
      return this.componentList.filter(d => d.enabled);
    },
    handleRectLock () {
      return _.every(this.currentSelectNodes, { lock: true });
    },
    panelStyle () {
      const {
        width,
        height,
        scale,
        backgroundColor,
        backgroundImage,
        backgroundRepeat,
        globalFilterParams,
        skeleton
      } = this.screenInfo.config;
      const style = {
        width: width + 'px',
        transform: `scale(${scale})`,
        'background-blend-mode': 'screen, overlay'
      };

      const setBackground = (image, repeat = 'fill') => {
        let cssText = '';
        if (!image) return '';
        switch (repeat) {
          case 'fill':
            cssText += `url(${image}) left top/100% 100% no-repeat`
            break;
          case 'contain':
            cssText += `url(${image}) center/contain no-repeat`
            break;
          case 'cover':
            cssText += `url(${image}) left top/cover no-repeat`
            break;
          case 'repeat':
            cssText += `url(${image}) left top/auto repeat`
            break;
          default:
            break;
        }
        return cssText
      }

      const { screenType, sceneConfig } = this.screenInfo;
      if (screenType === 'scene') {
        style.height = height + 'px';
        const scene = sceneConfig.find(item => item.sceneId === this.sceneId);
        if (scene) {
          let page = {};
          if (this.pageId) {
            page = scene.pageList.find(p => p.pageId === this.pageId);
          }
          const target = [{ ...page, backgroundImage: page.pageBackground }, { ...scene, backgroundImage: scene.sceneBackground }, { backgroundColor, backgroundImage, backgroundRepeat }].find(item => {
            return item.backgroundColor || item.backgroundImage
          })
          if (skeleton && skeleton.url) {
            if (target) {
              if (target.backgroundImage) {
                style.background = `${setBackground(target.backgroundImage, target.backgroundRepeat)},${setBackground(skeleton.url, 'fill')}`;
              } else {
                style.background = `${target.backgroundColor} ${setBackground(skeleton.url, 'fill')}`;
              }
            } else {
              style.background = `${setBackground(skeleton.url, 'fill')}`;
            }
          } else {
            if (target) {
              if (target.backgroundImage) {
                style.background = `${setBackground(target.backgroundImage, target.backgroundRepeat)}`;
              } else {
                style.background = `${target.backgroundColor}`;
              }
            } else {
              style.background = '';
            }
          }
        }
      } else {
        if (!this.screenInfo.isDynamicScreen) {
          style.minHeight = height + 'px';
        } else {
          style.height = height + 'px';
        }
        // 混合模式显示骨骼
        if (skeleton && skeleton.url) {
          if (backgroundImage) {
            style.background = `${setBackground(backgroundImage, backgroundRepeat)},${setBackground(skeleton.url, 'fill')}`;
          } else {
            style.background = `${backgroundColor} ${setBackground(skeleton.url, 'fill')}`;
          }
        } else {
          if (backgroundImage) {
            style.background = `${setBackground(backgroundImage, backgroundRepeat)}`;
          } else {
            style.background = `${backgroundColor}`;
          }
        }
      }

      if (globalFilterParams.enable) {
        style.filter = `hue-rotate(${globalFilterParams.hue}deg)
          saturate(${globalFilterParams.saturate}%)
          brightness(${globalFilterParams.brightness}%)
          contrast(${globalFilterParams.contrast}%)
          opacity(${globalFilterParams.opacity}%)
          grayscale(${globalFilterParams.grayscale}%)`
      }
      return style;
    },
    wh () {
      const {
        width,
        height
      } = this.screenInfo.config;
      return {
        width,
        height
      }
    },
    cancelGroupShow () {
      // const selectedNodes = this.layerTree.loadedNodes.filter(item => item.selected).map(item => item.data.id);
      // console.log(selectedNodes.findIndex(id => id.includes('groups_')) > -1, '=====')
      // return selectedNodes.findIndex(id => id.includes('groups_')) > -1
      const selectedNode = []; let show = false;
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNode.push(item.data.id);
      })
      if (selectedNode.length) {
        let sum = 0;
        selectedNode.forEach(item => {
          if (item.includes('groups_')) sum++;
        })
        if (sum > 0) {
          show = true
        }
      }
      return show;
    },
    groupShow () {
      let selectedNodes = []; let show = false;
      const selectedId = this.currentSelectNodes.map(item => item.id);
      if (selectedId.length) {
        this.layerTree.loadedNodes.forEach(item => {
          if (selectedId.includes(item.data.id)) {
            selectedNodes.push(item);
            // item.selected = true;
          }
          if (item.children) selectedNodes = this.findSelectOfTrue(item.children, selectedNodes, selectedId);
        })
      }
      // this.layerTree.loadedNodes.forEach(item => {
      //   if (item.selected) selectedNodes.push(item);
      // })
      if (selectedNodes.length > 1) {
        for (let i = 1; i < selectedNodes.length; i++) {
          // 判断所选组件是否是同一层级
          if (!(selectedNodes[0]?.parent?.data?.id === selectedNodes[i]?.parent?.data?.id)) {
            show = true; break;
          }
          // 判断在场景大屏中是否是同一场景或页面
          if (!(selectedNodes[0]?.data?.pageId === selectedNodes[i]?.data?.pageId &&
          selectedNodes[0]?.data?.sceneId === selectedNodes[i]?.data?.sceneId)) {
            show = true; break;
          }
        }
      } else if (selectedNodes.length < 1) show = true;
      else show = false;
      return show;
    },
    renameShow () {
      // const selectedNodes = [];
      // this.layerTree.loadedNodes.forEach(item => {
      //   if (item.selected) selectedNodes.push(item);
      // })
      // return selectedNodes.length !== 1;
      const selectedId = this.currentSelectNodes.map(item => item.id);
      return selectedId.length !== 1;
    },
    isActive () {
      return function (item) {
        if (_.isArray(this.currentSelectId)) {
          return this.currentSelectId.includes(item.i)
        }
        return this.currentSelectId === item.i
      }
    },
    isModule () {
      if (_.isArray(this.currentSelectId)) {
        return this.currentSelectId.findIndex(id => id.startsWith('interaction-container-modulepanel')) > -1
      }
      return (this.currentSelectId || '').startsWith('interaction-container-modulepanel');
    }
  },

  watch: {
    comLayerArr: {
      handler: function (layers) {
        if (!layers) return;
        const vm = this;
        function transLayerToNodes (layers) {
          const nodes = layers.map(item => {
            const com = vm.getComDataById(item.id);
            return {
              x: com.attr.x,
              y: com.attr.y,
              w: com.attr.w,
              h: com.attr.h,
              i: item.id,
              lock: com.attr.lock,
              type: item.type
            }
          })
          return nodes
        }
        const result = transLayerToNodes(layers);
        this.layout = result;
      },
      immediate: true,
      deep: true
    },
    currentSelectId () {
      this.hideCtxMenu();
    },
    wh: {
      handler: function (val) {
        this.key++;
        this.$nextTick(() => {
          this.$forceUpdate();
        })
      },
      deep: true
    }
  },

  created () {
    canvasBus.on('canvas_click', this.clearSelect);
    canvasBus.on('create_com', this.createCom);
    canvasBus.on('select_node', this.selectNode);
    canvasBus.on('align_comp', this.alignComp);
  },

  mounted () {
    this.shortcut();

    // document.addEventListener('dragover', this.handleDragEvent, false);
    // document.addEventListener('dragend', this.handleDragEnd, false);

    // this.$once('hook:beforeDestroy', () => {
    //   document.removeEventListener('dragover', this.handleDragEvent);
    //   document.removeEventListener('dragend', this.handleDragEnd);
    // })
  },

  beforeDestroy () {
    this.$shortcut.unbind('⌘+right, ctrl+right')
    this.$shortcut.unbind('⌘+left, ctrl+left')
    this.$shortcut.unbind('⌘+up, ctrl+up')
    this.$shortcut.unbind('⌘+down, ctrl+down')
    this.$shortcut.unbind('left')
    this.$shortcut.unbind('right')
    this.$shortcut.unbind('up')
    this.$shortcut.unbind('down')

    canvasBus.off('canvas_click', this.clearSelect);
    canvasBus.off('create_com', this.createCom);
    canvasBus.off('select_node', this.selectNode);
    canvasBus.off('align_comp', this.alignComp);
  },

  methods: {
    updateHandleRect () {
      const { currentSelectNodes } = this;
      const len = currentSelectNodes.length;
      if (len <= 0) {
        this.handleRect.x = 0;
        this.handleRect.y = 0;
        this.handleRect.w = 0;
        this.handleRect.h = 0;
        this.handleRect.r = 0;
        this.handleRect.show = false;
      } else {
        const newRect = new Rect(currentSelectNodes.map(com => ({
          x: com.left,
          y: com.top,
          w: com.width,
          h: com.height
        })));
        this.handleRect.x = newRect.x;
        this.handleRect.y = newRect.y;
        this.handleRect.w = newRect.width;
        this.handleRect.h = newRect.height;
        if (len === 1) {
          this.handleRect.r = currentSelectNodes[0].rotate;
        } else {
          this.handleRect.r = 0;
        }
        this.handleRect.show = true;
      }
    },
    handlePanelSelfClick (e) {
      if (!this.isClick) return;
      this.clearSelect();
    },
    /* 勿删，否则无法触发拖拽结束事件！！！！ */
    handleDragOver (e) {},
    async handleDrop (e) {
      let dragData = e.dataTransfer.getData('application/json');
      try {
        dragData = JSON.parse(dragData); const maxH = this.layout.length ? Math.max(...this.layout.map(item => {
          return item.y + item.h
        })) : 0;
        if (!dragData || dragData.type !== 'com') return;
        if (dragData.type === 'indicator') {
          const copyData = {
            originId: dragData.componentId,
            newData: {
              id: uuid('interaction-container-indicator'),
              name: 'interaction-container-indicator',
              version: '4.0.0',
              indicatorContainer: false,
              attr: {
                x: 0,
                y: maxH
              }
            }
          }
          if (this.screenInfo.screenType === 'scene') {
            copyData.newData.pageId = this.pageId;
            copyData.newData.sceneId = this.sceneId;
          }
          await this.$store.dispatch('editor/copyScreenComp', copyData);
          const layer = {
            id: copyData.newData.id,
            type: 'com'
          }
          this.layerTree.addChild(layer);

          if (!!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene') {
            await this.$store.dispatch('editor/insertScreenLayers', {
              screenId: this.screenInfo.id,
              layers: [layer]
            })
          } else {
            await this.$store.dispatch('editor/updateScreenLayers', this.layerTree.data.children)
          }
        } else if (dragData.type === 'com') {
          const compData = dragData.data;
          const { config } = this.screenInfo
          const { width, height } = compData;
          const comW = width < config.width ? width : config.width;
          const comH = width < config.width ? height : comW * (height / width);
          const data = {
            id: uuid(compData.name),
            name: compData.name,
            version: compData.version,
            attr: {
              x: 0,
              y: maxH,
              w: _.ceil((comW / config.width) * 24, 2),
              h: _.ceil((comH + 5) / 15, 2)
            }
          };
          this.createCom(data);
        }
      } catch (err) {
        console.error(err);
      }
    },
    async createCom (data) {
      if (!data) return false;
      if (this.screenInfo.screenType === 'scene') {
        data.sceneId = this.sceneId;
        data.pageId = this.pageId;
      }
      const maxH = this.layout.length ? Math.max(...this.layout.map(item => {
        return item.y + item.h
      })) : 0;
      const comData = {
        ...data,
        attr: data.attr || {
          x: 0,
          y: maxH,
          w: 24,
          h: 18
        }
      }
      const res = await this.$store.dispatch('editor/createScreenComp', comData);
      if (!res) return false;
      const newData = this.getComDataById(data.id);
      if (newData) {
        const layer = {
          id: newData.id,
          type: newData.type
        }
        if (this.screenInfo.screenType === 'scene') {
          layer.sceneId = data.sceneId;
          layer.pageId = data.pageId;
        }
        this.layerTree.addChild(layer);

        if (!!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene') {
          await this.$store.dispatch('editor/insertScreenLayers', {
            screenId: this.screenInfo.id,
            layers: [layer]
          })
        } else {
          await this.$store.dispatch('editor/updateScreenLayers', this.layerTree.data.children)
        }

        this.hideCtxMenu();
        this.selectNode(newData.id);
        this.layerTree.select(newData.id);
        return true;
      }
      return false;
    },
    hideCtxMenu () {
      this.$refs.contextmenu && this.$refs.contextmenu.hide();
    },
    handleCompClick (com) {
      this.hideCtxMenu()
      this.selectNode(com.i)
      this.layerTree.clearSelect();
      if (com.i.startsWith('interaction-container-modulepanel')) {
        const node = this.screenComs[com.i];
        const groupId = node.config.groupId;
        const groupNode = this.layerTree.getNodeById(groupId);
        if (groupNode) {
          this.layerTree.select(groupNode.data.id);
          return
        }
      }
      this.layerTree.select(com.i)
    },
    handleCtxMenu (vnode, e) {
      const compInstance = vnode.componentInstance;
      if (compInstance) { // 右键组件
        const nodeId = compInstance.$props.i;
        if (nodeId) {
          const comData = this.getComDataById(nodeId);
          if (!comData.attr.lock) {
            if (!this.isSelect(nodeId)) {
              this.selectNode(nodeId);
              this.layerTree.select(nodeId);
            }
          } else {
            this.hideCtxMenu();
          }
        } else {
          if (this.handleRectLock) {
            this.hideCtxMenu();
          }
        }
      } else { // 右键空白
        // 记录右键位置
        const rectInfo = this.$refs.canvasPanel.getBoundingClientRect();
        const left = (e.clientX - rectInfo.x) / this.scale;
        const top = (e.clientY - rectInfo.y) / this.scale;
        this.$store.commit('editor/setMenuPosition', { top, left });
        // 清空选择
        this.selectNode(null);
        this.layerTree.clearSelect();
      }
    },
    handleCtxClick (key) {
      canvasBus.emit('ctx_click', key);
    },
    hideArea () {
      this.isShowArea = false;
      this.areaWidth = 0;
      this.areaHeight = 0;
    },
    isSelect (nodeId) {
      return _.some(this.currentSelectNodes, { id: nodeId });
    },
    selectNode (nodeId, clearOther = true) {
      if (_.isNil(nodeId) || clearOther) { // Checks if value is null or undefined.
        // 如果未传参数，则表示清空选中
        this.$store.commit('editor/updateCurrentSelectId', null)
      }

      if (!_.isNil(nodeId)) {
        if (_.isArray(nodeId) && nodeId.length === 1) {
          nodeId = nodeId[0]
        }
        this.$store.commit('editor/updateCurrentSelectId', nodeId)
      }
    },
    /* 递归选中方法分组下的子节点 */
    findSelectedNode (node) {
      const { comIdMap } = this
      node.children.forEach(item => {
        if (item.children) {
          this.findSelectedNode(item)
        } else {
          const com = comIdMap[item.data.id];
          if (com) {
            // com.select();
          }
        }
      })
    },
    /* 递归找出canvasPanel所用的分组下的子节点 */
    findcomOfGroup (node) {
      node.children.forEach(item => {
        if (item.children) {
          // const node = this.layerTree.loadedNodes.find(p => p.data.id === item.id)
          this.findcomOfGroup(item)
        } else {
          this.compOfGroup.push(item);
        }
      })
    },
    /* 递归用于groupShow计算属性 */
    findSelectOfTrue (node, list, selectedId) {
      node.forEach(item => {
        if (selectedId.includes(item.data.id)) {
          list.push(item);
          // item.selected = true;
        }
        if (item.children) this.findSelectOfTrue(item.children, list, selectedId);
      })
      return list;
    },
    clearSelect () {
      this.selectNode(null)
      this.layerTree.clearSelect()
      this.hideCtxMenu()
    },
    selectMuti () {
      const r = {
        x: this.areaStart.x,
        y: this.areaStart.y,
        x1: this.areaWidth + this.areaStart.x,
        y1: this.areaHeight + this.areaStart.y
      };
      const selectCompIds = this.componentList
        .filter(com => !com.lock)
        .filter(com => {
          const r1 = {
            x: com.left,
            y: com.top,
            x1: com.width + com.left,
            y1: com.height + com.top
          };
          if (r1.x1 < r.x || r1.x > r.x1 || r1.y1 < r.y || r1.y > r.y1) { // 判断矩形是否相交
            return false;
          }
          return true;
        })
        .map(com => com.id);
      this.selectNode(selectCompIds);
      this.layerTree.select(selectCompIds);
      this.hideArea();
    },
    async alignComp (key) {
      const { x, y, w, h } = this.handleRect;
      const { currentSelectNodes } = this;
      const len = currentSelectNodes.length;
      let updateParams;
      switch (key) {
        case 'top':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.y', value: Math.round(y) }
            ]
          }));
          break;

        case 'verticalCenter':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.x', value: Math.round(x + w / 2 - n.width / 2) }
            ]
          }));
          break;

        case 'bottom':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.y', value: Math.round(y + h - n.height) }
            ]
          }));
          break;
        case 'left':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.x', value: Math.round(x) }
            ]
          }));
          break;
        case 'horizontally':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.y', value: Math.round(y + h / 2 - n.height / 2) }
            ]
          }));
          break;
        case 'right':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.x', value: Math.round(x + w - n.width) }
            ]
          }));
          break;
        case 'horizontal':
          {
            const sumDis = _.sumBy(currentSelectNodes, 'width');
            // if (w > sumDis) {
            const gap = (w - sumDis) / (len - 1);
            const sortCom = _.sortBy(currentSelectNodes, ['left']);
            let curX = x;
            for (let i = 1; i < len; i++) {
              const com = sortCom[i];
              const lastCom = sortCom[i - 1];
              curX += lastCom.width + gap;
              if (!updateParams) updateParams = [];
              updateParams.push({
                id: com.id,
                keyValPairs: [
                  { key: 'attr.x', value: Math.round(curX) }
                ]
              });
            }
            // }
          }
          break;
        case 'vertical':
          {
            const sumDis = _.sumBy(currentSelectNodes, 'height');
            // if (h > sumDis) {
            const gap = (h - sumDis) / (len - 1);
            const sortCom = _.sortBy(currentSelectNodes, ['top']);
            let curY = y;
            for (let i = 1; i < len; i++) {
              const com = sortCom[i];
              const lastCom = sortCom[i - 1];
              curY += lastCom.height + gap;
              if (!updateParams) updateParams = [];
              updateParams.push({
                id: com.id,
                keyValPairs: [
                  { key: 'attr.y', value: Math.round(curY) }
                ]
              });
            }
            // }
          }
          break;
      }
      if (updateParams) {
        await this.$store.dispatch('editor/updateScreenCom', updateParams);
        this.updateHandleRect();
      }
    },
    shortcut () {
      this.$shortcut.bind('⌘+right, ctrl+right', { func: this.move, params: 'tenfoldRight' })
      this.$shortcut.bind('⌘+left, ctrl+left', { func: this.move, params: 'tenfoldLeft' })
      this.$shortcut.bind('⌘+up, ctrl+up', { func: this.move, params: 'tenfoldUp' })
      this.$shortcut.bind('⌘+down, ctrl+down', { func: this.move, params: 'tenfoldDown' })
      this.$shortcut.bind('right', { func: this.move, params: 'right' })
      this.$shortcut.bind('left', { func: this.move, params: 'left' })
      this.$shortcut.bind('up', { func: this.move, params: 'up' })
      this.$shortcut.bind('down', { func: this.move, params: 'down' })
    },
    async move (direction) {
      let { x: left, y: top, w, h } = this.handleRect;
      const { config: { height, width, gridSpace } } = this.screenInfo;
      const stepMutiple = 10;

      switch (direction) {
        case 'tenfoldLeft':
          left -= gridSpace * stepMutiple;
          break;
        case 'tenfoldRight':
          left += gridSpace * stepMutiple;
          break;
        case 'tenfoldUp':
          top -= gridSpace * stepMutiple;
          break;
        case 'tenfoldDown':
          top += gridSpace * stepMutiple;
          break;
        case 'right':
          left += gridSpace;
          break;
        case 'left':
          left -= gridSpace;
          break;
        case 'up':
          top -= gridSpace;
          break;
        case 'down':
          top += gridSpace;
          break;
      }
      left = _.clamp(left, 0, Math.max(width - w, 0));
      top = _.clamp(top, 0, Math.max(height - h, 0));
      const ox = left - this.handleRect.x;
      const oy = top - this.handleRect.y;
      const updateParams = this.currentSelectNodes.map(n => {
        return {
          id: n.id,
          keyValPairs: [
            { key: 'attr.x', value: Math.round(n.left + ox) },
            { key: 'attr.y', value: Math.round(n.top + oy) }
          ]
        };
      });
      await this.$store.dispatch('editor/updateScreenCom', updateParams);
      this.handleRect.x = left;
      this.handleRect.y = top;
    },
    handleDragEvent: _.throttle(function (e) {
      mouseXY.x = e.clientX;
      mouseXY.y = e.clientY;

      const parentRect = this.$refs.canvasPanel.getBoundingClientRect();
      let mouseInGrid = false;
      if (((mouseXY.x > parentRect.left) && (mouseXY.x < parentRect.right)) && ((mouseXY.y > parentRect.top) && (mouseXY.y < parentRect.bottom))) {
        mouseInGrid = true;
      }
      if (mouseInGrid === true && (this.layout.findIndex(item => item.i === 'drop')) === -1) {
        this.layout.push({
          x: (this.layout.length * 2) % (this.colNum || 12),
          y: this.layout.length + (this.colNum || 12), // puts it at the bottom
          w: 1,
          h: 1,
          i: 'drop'
        });
      }
      const index = this.layout.findIndex(item => item.i === 'drop');
      if (index !== -1) {
        try {
          this.$refs.gridlayout.$children[this.layout.length].$refs.item.style.display = 'none';
        } catch {
        }
        const el = this.$refs.gridlayout.$children[index];
        el.dragging = { top: mouseXY.y - parentRect.top, left: mouseXY.x - parentRect.left };
        const newPos = el.calcXY(mouseXY.y - parentRect.top, mouseXY.x - parentRect.left);
        if (mouseInGrid === true) {
          this.$refs.gridlayout.dragEvent('dragstart', 'drop', newPos.x, newPos.y, 1, 6);
          DragPos.i = String(index);
          DragPos.x = this.layout[index].x;
          DragPos.y = this.layout[index].y;
        }
        if (mouseInGrid === false) {
          this.$refs.gridlayout.dragEvent('dragend', 'drop', newPos.x, newPos.y, 1, 6);
          setTimeout(() => {
            this.layout = this.layout.filter(obj => obj.i !== 'drop');
          }, 500)
        }
      }
    }, 300),
    handleDragEnd (e) {
      // const parentRect = this.$refs.canvasPanel.getBoundingClientRect();
      // let mouseInGrid = false;
      // if (((mouseXY.x > parentRect.left) && (mouseXY.x < parentRect.right)) && ((mouseXY.y > parentRect.top) && (mouseXY.y < parentRect.bottom))) {
      //   mouseInGrid = true;
      // }
      // if (mouseInGrid === true) {
      //   console.log(`Dropped element props:\n${JSON.stringify(DragPos, ['x', 'y', 'w', 'h'], 2)}`);
      //   this.$refs.gridlayout.dragEvent('dragend', 'drop', DragPos.x, DragPos.y, 1, 6);
      //   this.layout = this.layout.filter(obj => obj.i !== 'drop');
      //   // UNCOMMENT below if you want to add a grid-item
      //   /*
      //   this.layout.push({
      //       x: DragPos.x,
      //       y: DragPos.y,
      //       w: 1,
      //       h: 1,
      //       i: DragPos.i,
      //   });
      //   this.$refs.gridLayout.dragEvent('dragend', DragPos.i, DragPos.x,DragPos.y,1,1);
      //   try {
      //       this.$refs.gridLayout.$children[this.layout.length].$refs.item.style.display="block";
      //   } catch {
      //   }
      //   */
      // }
    },
    layoutUpdatedEvent (newLayout) {
      const updateParams = newLayout.map(n => {
        return {
          id: n.i,
          keyValPairs: [
            { key: 'attr.x', value: n.x },
            { key: 'attr.y', value: n.y },
            { key: 'attr.w', value: n.w },
            { key: 'attr.h', value: n.h }
          ]
        };
      });
      this.$store.dispatch('editor/updateScreenCom', updateParams);
    },
    resizedEvent (i, newH, newW, newHPx, newWPx) {
      // console.log({ i, newH, newW, newHPx, newWPx })
    }
  }
};
</script>

<style lang="scss" scoped>
.canvas-panel {
  position: absolute;
  top: 80px;
  left: 80px;
  transform-origin: 0 0;
  transition: 0.2s all ease-in-out;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: rgba(0, 0, 0, 0.5) 0 0 30px 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  .icon-container {
    position: absolute;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: auto;
    .event-btn {
      position: absolute;
      background: #FF9029;
      right: 0;
      top: 0;
      width: 16px;
      height: 16px;
      // transform-origin: right top;
      cursor: pointer;
      z-index: 10000;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      transition: 0.2s;
      .shan {
        height: 16px;
        width: 16px;
        display: inline-block;
        background: url('../../../assets/img/svg/shandian.svg');
        // color: #e5a029;
        font-size: 10px;

      }
      .text {
        color: #fff;
        font-weight: bolder;
      }
      &:hover {
        // transform: scale(1.2);
        width: 20px;
        height: 20px;
        z-index: 10020;
      }
    }
  }
  .isActive {
    border: 1px solid #2681ff !important;
  }
  ::v-deep {
    .vue-grid-item {
      border: 1px dashed #fff;
      // overflow: hidden;
    }
  }
}
.contextmenu-disable {
    pointer-events: none;
    opacity: 0.5;
  }
</style>
