<template>
  <div
    class="canvas-panel"
    :style="panelStyle"
    ref="canvasPanel"
    @click.self="handlePanelSelfClick"
    @dragover.prevent="handleDragOver"
    @dragenter.prevent
    @drop.prevent="handleDrop"
    @mousedown.stop.prevent="handleMouseDown"
    v-contextmenu:contextmenu>
    <!-- 粒子效果容器 -->
    <Particles
      :id="`${screenInfo.id}_edit_particles`"
      :key="ParticlesKey"
      :options="computedParticlesConfig"
      v-if="screenInfo.config.backgroundParticlesType !== 'STYLE_NONE' && screenInfo.config.backgroundParticlesType"
    />

    <VueDragResize
      v-for="(item, index) in comLayerArr"
      :key="item.id"
      :id="item.id"
      :w="item.attr.w"
      :h="item.attr.h"
      :x="item.attr.x"
      :y="item.attr.y"
      :r="item.attr.deg"
      :lock="item.attr.lock"
      :scale="scale"
      :zIndex="comLayerArr.length - index"
      :parent="true"
      :parentEl="$refs.canvasPanel"
      :handleRectCom="$refs.handleRectRef"
      @clickComp="handleCompClick"
      @elementDblClick="handleCompDblClick"
      @mutiSelect="handleMutiSelect"
      @sizeChange="handleSizeChange"
      v-contextmenu:contextmenu>
      <EditCompNode
        v-if="getComDataById(item.id)"
        :id="item.id"
        :type="item.type"
      />
      <!-- 小图标放置在此div容器中 -->
      <div class="icon-container">
        <div class="icon-mark">
          <div
            v-if="getComDataById(item.id).interactionConfig.events && getComDataById(item.id).interactionConfig.events.length"
            class="event-btn" @click.stop="jumpToEvent(item.id)">
            <span class="shan" v-if="getComDataById(item.id).interactionConfig.events.length === 1">
            </span>
            <span class="text" v-if="getComDataById(item.id).interactionConfig.events.length > 1">
              {{ getComDataById(item.id).interactionConfig.events.length }}
            </span>
          </div>
          <div
            v-if="getComDataById(item.id).interactionConfig.linkAge && getComDataById(item.id).interactionConfig.linkAge.length"
            class="event-btn linkAge-btn" @click.stop="jumpToEvent(item.id)">
            <span class="shan" v-if="getComDataById(item.id).interactionConfig.linkAge.length === 1">
            </span>
            <span class="text" v-if="getComDataById(item.id).interactionConfig.linkAge.length > 1">
              {{ getComDataById(item.id).interactionConfig.linkAge.length }}
            </span>
          </div>
          <div
            v-if="getComDataById(item.id).interactionConfig.drillDown && getComDataById(item.id).interactionConfig.drillDown.length"
            class="event-btn drillDown-btn" @click.stop="jumpToEvent(item.id)">
            <span class="shan" v-if="getComDataById(item.id).interactionConfig.drillDown.length === 1">
            </span>
            <span class="text" v-if="getComDataById(item.id).interactionConfig.drillDown.length > 1">
              {{ getComDataById(item.id).interactionConfig.drillDown.length }}
            </span>
          </div>
        </div>
        <!-- <i v-if="getComDataById(item.id).interactionConfig.events.length">⚡️</i> -->

      </div>
    </VueDragResize>
    <!-- 专用于处理各种拖拽的矩形 -->
    <VueDragResize
      ref="handleRectRef"
      v-show="handleRect.show"
      :isHandleRect="true"
      :w="handleRect.w"
      :h="handleRect.h"
      :x="handleRect.x"
      :y="handleRect.y"
      :r="handleRect.r"
      :lock="handleRectLock"
      :scale="scale"
      :parent="true"
      :parentEl="$refs.canvasPanel"
      :isMutiSelect="isMutiSelect"
      @dragStart="handleStart('drag', $event)"
      @dragging="handleHanding('drag', $event)"
      @dragEnd="handleEnd('drag', $event)"
      @rotateStart="handleStart('rotate', $event)"
      @rotating="handleHanding('rotate', $event)"
      @rotateStop="handleEnd('rotate', $event)"
      @resizeStart="handleStart('resize', $event)"
      @resizing="handleHanding('resize', $event)"
      @resizeStop="handleEnd('resize', $event)"
      @connectionStart="handlEconnectionStart"
      @connectionMove="handlEconnectionMove"
      @connectionEnd="handlEconnectionEnd"
      v-contextmenu:contextmenu>
    </VueDragResize>
    <EventLine v-show="isShowLine" :start="lineStart" :end="lineEnd"></EventLine>
    <SelectArea v-show="isShowArea" :start="areaStart" :width="areaWidth" :height="areaHeight"></SelectArea>
    <MarkLine :handleRectCom="$refs.handleRectRef" :curComponentRect="handleRect"
              @updateCurRectPos="handleUpdateCurRectPos"></MarkLine>
    <!-- 右键菜单（暂时用 div 解决报错问题 https://github.com/vuejs/vue/issues/11838)-->
    <div style="display:none;">
      <v-contextmenu
        ref="contextmenu"
        @contextmenu="handleCtxMenu">
        <v-contextmenu-item
          v-for="menu in ctxMenuList"
          v-show="menu.show"
          :key="menu.key"
          :class="[{'contextmenu-disable': menu.key === 'cancelGroup' ||
          (menu.key === 'group' && groupShow) ||
          (menu.key === 'rename' && renameShow)}]"
          @click="handleCtxClick(menu.key)">
          {{ menu.label }}
        </v-contextmenu-item>
      </v-contextmenu>
    </div>
  </div>
</template>

<script>
import VueDragResize from '@/components/editor/canvas/VueDragResize'
import EditCompNode from '@/components/editor/canvas/EditCompNode'
import SelectArea from '@/components/editor/canvas/SelectArea'
import MarkLine from '@/components/editor/canvas/MarkLine'
import EventLine from '@/components/editor/canvas/EventLine'
import { mapState, mapGetters } from 'vuex'
import { uuid, getFormData, replaceUrl } from '@/utils/base'
import { getOffsetX, getOffsetY } from '@/utils/dom'
import canvasBus from '@/utils/canvasBus'
import '@/components/plugins/shortcut'
import { CLICKTIME } from '@/common/constants'
import Rect from '@/utils/Rect'
import emitter from '@/utils/bus'
import { commonUpload } from '@/api/common';
import * as PARTICLES_THEME from '@/common/particlesTheme'
import compActions from '@/common/compActions'
import {
  updateScreen
} from '@/api/screen'
import Vue from 'vue'

export default {
  name: 'CanvasPanel',

  inject: ['getLayerTree'],

  components: {
    VueDragResize,
    EditCompNode,
    SelectArea,
    MarkLine,
    EventLine
  },

  data () {
    return {
      componentList: [], // 子组件 VueDragResize 实例数组
      comStartStyle: null,

      isShowArea: false, // 是否显示选中区域框
      areaStart: { x: 0, y: 0 },
      areaWidth: 0,
      areaHeight: 0,

      PARTICLES_THEME,

      isShowLine: false, // 是否显示连线
      lineStart: { x: 0, y: 0 },
      lineEnd: { x: 0, y: 0 },

      handleRect: { // 拖拽及缩放的处理矩形
        show: false,
        w: 0,
        h: 0,
        x: 0,
        y: 0,
        r: 0
      },

      lineEventComId: null, // 事件连线组件ID

      isClick: true,
      bcComponentData: {},
      compOfGroup: [],
      judgeSelectedNode: false
    }
  },

  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      screenComs: state => state.editor.screenComs,
      comsData: state => state.editor.comsData,
      sceneId: state => state.editor.sceneId,
      pageId: state => state.editor.pageId,
      snapshotIndex: state => state.editor.snapshotIndex
    }),
    ...mapGetters('editor', ['getComDataById', 'ctxMenuList', 'cursnapshotData', 'currentCom']),
    layerTree () {
      return this.getLayerTree()
    },
    comLayerArr () {
      const nodes = this.layerTree.data.children
      if (nodes && nodes.length) {
        let compNodes = nodes.map(n => {
          if (n.children && !n.isDelete) {
            this.findcomOfGroup(n)
            const compList = this.compOfGroup.map(item => {
              return {
                ...item,
                attr: this.getComDataById(item.id)?.attr,
                sceneId: this.getComDataById(item.id)?.sceneId,
                pageId: this.getComDataById(item.id)?.pageId
              }
            }).filter(item => !item.isDelete)
            this.compOfGroup = [];
            return compList;
          } else {
            return {
              ...n,
              attr: this.getComDataById(n.id)?.attr,
              sceneId: this.getComDataById(n.id)?.sceneId,
              pageId: this.getComDataById(n.id)?.pageId
            }
          }
        }).filter(item => !item.isDelete)
        compNodes = compNodes.flat()
        const { screenType } = this.screenInfo
        if (screenType === 'scene') {
          if (this.pageId) {
            return compNodes.filter(node => node.pageId === this.pageId || (node.sceneId === this.sceneId && !node.pageId))
          } else {
            return compNodes.filter(node => node.sceneId === this.sceneId && !node.pageId)
          }
        } else {
          return compNodes
        }
      }
      return []
    },
    comIdMap () {
      return _.keyBy(this.componentList, 'id')
    },
    scale () {
      return this.screenInfo.config.scale
    },
    isMutiSelect () {
      return this.currentSelectNodes && this.currentSelectNodes.length > 1
    },
    currentSelectNodes () {
      return this.componentList.filter(d => d.enabled)
    },
    handleRectLock () {
      return _.every(this.currentSelectNodes, { lock: true })
    },
    panelStyle () {
      const {
        width,
        height,
        scale,
        backgroundColor,
        backgroundImage,
        backgroundRepeat,
        globalFilterParams,
        skeleton
      } = this.screenInfo.config

      const style = {
        width: width + 'px',
        height: height + 'px',
        transform: `scale(${scale})`,
        'background-blend-mode': 'screen, overlay'
      }

      const setBackground = (image, repeat = 'fill') => {
        let cssText = ''
        if (!image) return cssText
        switch (repeat) {
          case 'fill':
            cssText += ` url(${replaceUrl(image)}) left top/100% 100% no-repeat`
            break
          case 'contain':
            cssText += ` url(${replaceUrl(image)}) center/contain no-repeat`
            break
          case 'cover':
            cssText += ` url(${replaceUrl(image)}) left top/cover no-repeat`
            break
          case 'repeat':
            cssText += ` url(${replaceUrl(image)}) left top/auto repeat`
            break
          default:
            break
        }
        return cssText
      }

      const { screenType, sceneConfig } = this.screenInfo
      if (screenType === 'scene') {
        const scene = sceneConfig.find(item => item.sceneId === this.sceneId)
        if (scene) {
          let page = {}
          if (this.pageId) {
            page = scene.pageList.find(p => p.pageId === this.pageId)
          }
          const target = [{ ...page, backgroundImage: page.pageBackground }, {
            ...scene,
            backgroundImage: scene.sceneBackground
          }, { backgroundColor, backgroundImage, backgroundRepeat }].find(item => {
            return item.backgroundColor || item.backgroundImage
          })
          if (skeleton && skeleton.url) {
            if (target) {
              if (target.backgroundImage) {
                style.background = `${setBackground(target.backgroundImage, target.backgroundRepeat)},${setBackground(skeleton.url, 'fill')}`
              } else {
                style.background = `${target.backgroundColor} ${setBackground(skeleton.url, 'fill')}`
              }
            } else {
              style.background = `${setBackground(skeleton.url, 'fill')}`
            }
          } else {
            if (target) {
              if (target.backgroundImage) {
                style.background = `${setBackground(target.backgroundImage, target.backgroundRepeat)}`
              } else {
                style.background = `${target.backgroundColor}`
              }
            } else {
              style.background = ''
            }
          }
        }
      } else {
        // 混合模式显示骨骼
        if (skeleton && skeleton.url) {
          if (backgroundImage) {
            style.background = `${setBackground(backgroundImage, backgroundRepeat)},${setBackground(skeleton.url, 'fill')}`
          } else {
            style.background = `${backgroundColor} ${setBackground(skeleton.url, 'fill')}`
          }
        } else {
          if (backgroundImage) {
            style.background = `${setBackground(backgroundImage, backgroundRepeat)}`
          } else {
            style.background = `${backgroundColor}`
          }
        }
      }

      if (globalFilterParams.enable) {
        style.filter = `hue-rotate(${globalFilterParams.hue}deg)
          saturate(${globalFilterParams.saturate}%)
          brightness(${globalFilterParams.brightness}%)
          contrast(${globalFilterParams.contrast}%)
          opacity(${globalFilterParams.opacity}%)
          grayscale(${globalFilterParams.grayscale}%)`
      }
      return style
    },
    ParticlesKey () {
      let key = uuid();
      const { backgroundParticlesCustom, backgroundParticlesType } = this.screenInfo.config;
      if (backgroundParticlesCustom && backgroundParticlesType) {
        key = uuid();
      }
      return key;
    },
    computedParticlesConfig () {
      const custom = this.screenInfo.config.backgroundParticlesCustom
      const type = this.screenInfo.config.backgroundParticlesType
      const temp = PARTICLES_THEME[this.screenInfo.config.backgroundParticlesType]
      if (type === 'STYLE_NONE') return {}
      if (!custom) return temp
      if (_.isEmpty(custom)) {
        return {}
      }
      temp.particles.number.value = custom.number
      temp.particles.move.speed.min = custom.speed[0]
      temp.particles.move.speed.max = custom.speed[1]
      temp.particles.opacity.value.min = custom.opacity[0]
      temp.particles.opacity.value.max = custom.opacity[1]
      temp.particles.size.value.min = custom.size[0]
      temp.particles.size.value.max = custom.size[1]
      type === 'STYLE_METEOR'
        ? temp.particles.stroke.color.value = [...new Set(custom.color.map(item => item.value))]
        : temp.particles.color.value = [...new Set(custom.color.map(item => item.value))]
      return temp
    },
    groupShow () {
      let selectedNodes = []; let show = false;
      const selectedId = this.currentSelectNodes.map(item => item.id);
      if (selectedId.length) {
        this.layerTree.loadedNodes.forEach(item => {
          if (selectedId.includes(item.data.id)) {
            selectedNodes.push(item);
            // item.selected = true;
          }
          if (item.children) selectedNodes = this.findSelectOfTrue(item.children, selectedNodes, selectedId);
        })
      }
      // this.layerTree.loadedNodes.forEach(item => {
      //   if (item.selected) selectedNodes.push(item);
      // })
      if (selectedNodes.length > 1) {
        for (let i = 1; i < selectedNodes.length; i++) {
          // 判断所选组件是否是同一层级
          if (!(selectedNodes[0]?.parent?.data?.id === selectedNodes[i]?.parent?.data?.id)) {
            show = true; break;
          }
          // 判断在场景大屏中是否是同一场景或页面
          if (!(selectedNodes[0]?.data?.pageId === selectedNodes[i]?.data?.pageId &&
          selectedNodes[0]?.data?.sceneId === selectedNodes[i]?.data?.sceneId)) {
            show = true; break;
          }
        }
      } else if (selectedNodes.length < 1) show = true;
      else show = false;
      return show;
    },
    renameShow () {
      // const selectedNodes = [];
      // this.layerTree.loadedNodes.forEach(item => {
      //   if (item.selected) selectedNodes.push(item);
      // })
      // return selectedNodes.length !== 1;
      const selectedId = this.currentSelectNodes.map(item => item.id);
      return selectedId.length !== 1;
    }
  },

  watch: {
    currentSelectNodes () {
      this.updateCurSelectId()
      this.updateHandleRect()
    }
  },

  created () {
    canvasBus.on('canvas_click', this.clearSelect)
    canvasBus.on('create_com', this.createCom)
    canvasBus.on('select_node', this.selectNode)
    canvasBus.on('align_comp', this.alignComp)
    canvasBus.on('updateEventComId', this.updateEventComId)
  },

  mounted () {
    this.shortcut()
  },

  beforeDestroy () {
    this.$shortcut.unbind('⌘+right, ctrl+right')
    this.$shortcut.unbind('⌘+left, ctrl+left')
    this.$shortcut.unbind('⌘+up, ctrl+up')
    this.$shortcut.unbind('⌘+down, ctrl+down')
    this.$shortcut.unbind('left')
    this.$shortcut.unbind('right')
    this.$shortcut.unbind('up')
    this.$shortcut.unbind('down')
    this.$shortcut.unbind('⌘+z, ctrl+z')
    this.$shortcut.unbind('⌘+y, ctrl+y')

    canvasBus.off('canvas_click', this.clearSelect)
    canvasBus.off('create_com', this.createCom)
    canvasBus.off('select_node', this.selectNode)
    canvasBus.off('align_comp', this.alignComp)
    canvasBus.off('updateEventComId', this.updateEventComId)
  },

  methods: {
    updateCurSelectId () {
      const { currentSelectNodes } = this
      const len = currentSelectNodes.length
      if (len <= 0) {
        this.$store.commit('editor/updateCurrentSelectId', null)
      } else if (len === 1) {
        this.$store.commit('editor/updateCurrentSelectId', currentSelectNodes[0].id)
      } else {
        this.$store.commit('editor/updateCurrentSelectId', currentSelectNodes.map(com => com.id))
      }
    },
    updateHandleRect () {
      const { currentSelectNodes } = this
      const len = currentSelectNodes.length
      if (len <= 0) {
        this.handleRect.x = 0
        this.handleRect.y = 0
        this.handleRect.w = 0
        this.handleRect.h = 0
        this.handleRect.r = 0
        this.handleRect.show = false
      } else {
        const newRect = new Rect(currentSelectNodes.map(com => ({
          x: com.left,
          y: com.top,
          w: com.width,
          h: com.height
        })))
        this.handleRect.x = newRect.x
        this.handleRect.y = newRect.y
        this.handleRect.w = newRect.width
        this.handleRect.h = newRect.height
        if (len === 1) {
          this.handleRect.r = currentSelectNodes[0].rotate
        } else {
          this.handleRect.r = 0
        }
        this.handleRect.show = true
      }
    },
    handleUpdateCurRectPos ({ x, y, w, h, r }) {
      if (!_.isNil(x)) this.handleRect.x = x
      if (!_.isNil(y)) this.handleRect.y = y
      if (!_.isNil(w)) this.handleRect.w = w
      if (!_.isNil(h)) this.handleRect.h = h
      if (!_.isNil(r)) this.handleRect.r = r
    },
    handlePanelSelfClick (e) {
      if (!this.isClick) return
      this.clearSelect()
    },
    handleDragOver (e) { /* 勿删，否则无法触发拖拽结束事件！！！！ */ },
    async handleDrop (e) {
      let dragData = e.dataTransfer.getData('application/json')
      let files = e.dataTransfer.files || []
      const { scale } = this
      try {
        if (files.length) {
          // 拖拽的是文件
          files = Array.prototype.filter.call(files, item => item.type && item.type.startsWith('image/')) // 只处理图片文件
          if (!files.length) return false;
          this.imageFiles = files;
          const reader = this.fileReader = new FileReader();
          reader.onload = () => {
            if (reader.result) {
              this.imageData = reader.result;
              // emitter.on('after_createCom', this.handleCreateComAfter)
              emitter.emit('drop_image', {
                x: Math.max(getOffsetX(e), 0) / scale,
                y: Math.max(getOffsetY(e), 0) / scale
              })
            }
          }
          reader.readAsDataURL(files[0]);
        } else {
          dragData = JSON.parse(dragData)
          if (!dragData) return

          if (dragData.type === 'com') {
            const compData = dragData.data
            const data = {
              id: uuid(compData.name),
              name: compData.name,
              version: compData.version,
              attr: {
                w: compData.width,
                h: compData.height,
                x: Math.floor(Math.max(getOffsetX(e), 0) / scale),
                y: Math.floor(Math.max(getOffsetY(e), 0) / scale)
              }
            }
            if (dragData.data.isActive) {
              data.isActive = dragData.data.isActive
              data.result = dragData.data.result
            }
            this.createCom(data)
          } else if (dragData.type === 'indicator') {
            const copyData = {
              originId: dragData.componentId,
              newData: {
                id: uuid('interaction-container-indicator'),
                name: 'interaction-container-indicator',
                version: '4.0.0',
                indicatorContainer: false,
                attr: {
                  x: Math.floor(Math.max(getOffsetX(e), 0) / scale),
                  y: Math.floor(Math.max(getOffsetY(e), 0) / scale)
                }
              }
            }
            if (this.screenInfo.screenType === 'scene') {
              copyData.newData.pageId = this.pageId;
              copyData.newData.sceneId = this.sceneId;
            }
            await this.$store.dispatch('editor/copyScreenComp', copyData);
            const layer = {
              id: copyData.newData.id,
              type: 'com'
            }
            if (this.screenInfo.screenType === 'scene') {
              layer.sceneId = this.sceneId
              layer.pageId = this.pageId
            }
            if (!!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene') {
              await this.$store.dispatch('editor/insertScreenLayers', {
                screenId: this.screenInfo.id,
                layers: [layer]
              })
            } else {
              const layers = this.layerTree.data.children || []
              layers.push(layer)
              await this.$store.dispatch('editor/updateScreenLayers', layers)
            }
            // this.layerTree.addChild(layer)
          }
        }
      } catch (err) {
        console.error(err)
      }
    },
    handleCreateComAfter ({ id }) {
      const formData = getFormData(this.imageData);
      this.imageData = null;
      commonUpload(formData).then(res => {
        if (res.success && res.data) {
          const imageUrl = replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.url);
          this.$store.dispatch('editor/updateScreenCom', {
            id,
            keyValPairs: [{
              key: 'config.url',
              value: imageUrl
            }]
          }).then(async () => {
            // try {
            //   const res = await getData({
            //     componentId: id,
            //     type: 'static',
            //     workspaceId: this.screenInfo.workspaceId
            //   })
            //   this.$store.commit('editor/updateComData', { componentId: id, data: res.data })
            // } catch (e) {
            //   console.warn(e)
            // }
          })
          this.$message.success('添加图片成功')
          this.imageFiles.splice(0, 1);
          if (this.imageFiles.length) {
            this.fileReader.readAsDataURL(this.imageFiles[0]);
          } else {
            this.fileReader = null;
            this.imageFiles = null;
          }
        }
      }).catch(e => {
        console.warn(e);
      })
    },
    async createCom (data) {
      if (!data) return false
      if (this.screenInfo.screenType === 'scene') {
        data.sceneId = this.sceneId
        data.pageId = this.pageId
      }
      const res = await this.$store.dispatch('editor/createScreenComp', data)
      if (!res) return false
      const newData = this.getComDataById(data.id)
      if (newData) {
        const layer = {
          id: newData.id,
          type: newData.type
        }
        if (this.screenInfo.screenType === 'scene') {
          layer.sceneId = data.sceneId
          layer.pageId = data.pageId
        }

        if (!!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene') {
          await this.$store.dispatch('editor/insertScreenLayers', {
            screenId: this.screenInfo.id,
            layers: [layer]
          })
        } else {
          const layers = this.layerTree.data.children || []
          layers.push(layer)
          await this.$store.dispatch('editor/updateScreenLayers', layers)
        }
        // this.layerTree.addChild(layer)

        this.hideCtxMenu()
        this.selectNode(newData.id)
        this.layerTree.select(newData.id)

        emitter.emit('after_createCom', data)
        if (this.imageData) {
          this.handleCreateComAfter(data)
        }
        Vue.prototype.$socket && Vue.prototype.$socket.io.emit('linkage', {
          room: this.screenInfo.parentId || this.screenInfo.id,
          msg: {
            type: 'update',
            currId: this.screenInfo.id
          }
        })
        return true
      }
      return false
    },
    hideCtxMenu () {
      this.$refs.contextmenu && this.$refs.contextmenu.hide()
    },
    handleMouseDown (e) {
      this.hideArea()
      const { scale } = this
      const rect = this.$el.getBoundingClientRect()
      const startX = e.clientX
      const startY = e.clientY
      const startTime = +new Date()
      this.areaStart.x = (startX - rect.x) / scale
      this.areaStart.y = (startY - rect.y) / scale
      this.isShowArea = true

      const move = e => {
        const curX = e.clientX
        const curY = e.clientY
        this.areaWidth = Math.abs(curX - startX) / scale
        this.areaHeight = Math.abs(curY - startY) / scale
        if (curX < startX) {
          this.areaStart.x = (curX - rect.x) / scale
        }
        if (curY < startY) {
          this.areaStart.y = (curY - rect.y) / scale
        }
      }

      const up = e => {
        document.removeEventListener('mousemove', move)
        document.removeEventListener('mouseup', up)
        const curTime = +new Date()
        // 根据时间差判断是否是点击还是拖拽
        if (curTime - startTime < CLICKTIME) {
          this.isClick = true
        } else {
          this.isClick = false
        }
        if (e.clientX === startX && e.clientY === startY) {
          this.hideArea()
          return
        }
        this.selectMuti()
      }

      document.addEventListener('mousemove', move)
      document.addEventListener('mouseup', up)
    },
    handleCompClick (com) {
      this.hideCtxMenu()
      this.selectNode(com.id)
      this.layerTree.clearSelect();
      this.layerTree.select(com.id)
    },
    handleCompDblClick (com) {
      const type = com.id.split('_')[0];
      const comInfo = this.getComDataById(com.id);
      const comData = this.comsData[com.id];
      compActions.dblclick && compActions.dblclick[type] && compActions.dblclick[type].handler(comInfo, comData);
    },
    handleMutiSelect (com) {
      this.layerTree.select(this.currentSelectNodes.map(com => com.id))
    },
    handleSizeChange () {
      this.updateHandleRect()
    },
    handleStart (type, e) {
      this.comStartStyle = this.currentSelectNodes.map(d => ({
        left: d.left,
        top: d.top,
        width: d.width,
        height: d.height,
        id: d.id,
        rotate: d.rotate
      }))
    },
    handleHanding (type, { offsetLeft, offsetTop, offsetWidth, offsetHeight, rotate }) {
      const { currentSelectNodes, comStartStyle } = this
      currentSelectNodes.forEach((com, i) => {
        const startStyle = comStartStyle[i]
        if (type !== 'rotate') {
          com.left = startStyle.left + offsetLeft
          com.top = startStyle.top + offsetTop
          com.width = startStyle.width + offsetWidth
          com.height = startStyle.height + offsetHeight
        } else {
          com.rotate = rotate
        }
      })
    },
    handleEnd (type, e) {
      const snapeshotData = _.cloneDeep(this.comStartStyle)
      this.comStartStyle = null
      const { currentSelectNodes } = this
      const rectCom = this.$refs.handleRectRef
      if (rectCom) { // 同步处理矩形的尺寸，避免数据不一致导致点选时出现差错
        this.handleRect.x = rectCom.left
        this.handleRect.y = rectCom.top
        this.handleRect.w = rectCom.width
        this.handleRect.h = rectCom.height
        this.handleRect.r = rectCom.rotate
      }
      const updateParams = currentSelectNodes.map(n => {
        return {
          id: n.id,
          keyValPairs: [
            { key: 'attr.x', value: Math.round(n.left) },
            { key: 'attr.y', value: Math.round(n.top) },
            { key: 'attr.w', value: Math.round(n.width) },
            { key: 'attr.h', value: Math.round(n.height) },
            { key: 'attr.deg', value: n.rotate }
          ]
        }
      })
      // 判断组件是否要存储初始状态
      if (!this.cursnapshotData || this.cursnapshotData.length !== snapeshotData.length || _.difference(_.map(this.cursnapshotData, 'id'), _.map(snapeshotData, 'id')).length) {
      // if (this.snapshotIndex === -1) {
        const snapeshotParams = snapeshotData.map(item => {
          return {
            id: item.id,
            keyValPairs: [
              { key: 'attr.x', value: Math.round(item.left) },
              { key: 'attr.y', value: Math.round(item.top) },
              { key: 'attr.w', value: Math.round(item.width) },
              { key: 'attr.h', value: Math.round(item.height) },
              { key: 'attr.deg', value: item.rotate }
            ]
          }
        })
        this.$store.commit('editor/recordSnapshot', snapeshotParams)
      }
      this.$store.commit('editor/recordSnapshot', updateParams)
      this.$store.dispatch('editor/updateScreenCom', updateParams)
      if (currentSelectNodes.length && currentSelectNodes.length === 1 && this.currentCom.comType === 'interaction-container-flowlayoutpanel') {
        if (this.currentCom.config.screens.length && this.currentCom.config.screens.length === 1) {
          updateScreen({
            'config.width': rectCom.width,
            'config.height': rectCom.height
          }, { id: this.currentCom.config.screens[0].id })
        }
      }
    },
    handleCtxMenu (vnode, e) {
      const compInstance = vnode.componentInstance
      if (compInstance) { // 右键组件
        const nodeId = compInstance.$props.id
        if (nodeId) {
          const comData = this.getComDataById(nodeId)
          if (!comData.attr.lock) {
            if (!this.isSelect(nodeId)) {
              this.selectNode(nodeId)
              this.layerTree.select(nodeId)
            }
          } else {
            this.hideCtxMenu()
          }
        } else {
          if (this.handleRectLock) {
            this.hideCtxMenu()
          }
        }
      } else { // 右键空白
        // 记录右键位置
        const rectInfo = this.$refs.canvasPanel.getBoundingClientRect()
        const left = (e.clientX - rectInfo.x) / this.scale
        const top = (e.clientY - rectInfo.y) / this.scale
        this.$store.commit('editor/setMenuPosition', { top, left })
        // 清空选择
        this.selectNode(null)
        this.layerTree.clearSelect()
      }
    },
    handleCtxClick (key) {
      canvasBus.emit('ctx_click', key);
      if (key !== 'rename' && key !== 'delete') canvasBus.emit('select_node', null);
      if (key === 'copy') this.layerTree.clearSelect();
    },
    hideArea () {
      this.isShowArea = false
      this.areaWidth = 0
      this.areaHeight = 0
    },
    isSelect (nodeId) {
      return _.some(this.currentSelectNodes, { id: nodeId })
    },
    selectNode (nodeId, clearOther = true) {
      const { currentSelectNodes, comIdMap } = this
      if (_.isNil(nodeId) || clearOther) { // Checks if value is null or undefined.
        // 如果未传参数，则表示清空选中
        currentSelectNodes.forEach(n => n.deselect())
      }
      if (!_.isNil(nodeId)) {
        if (!_.isArray(nodeId)) {
          nodeId = [nodeId]
        }
        nodeId.forEach(id => {
          let com;
          if (id.includes('groups_')) {
            const node = this.layerTree.loadedNodes.find(p => p.data.id === id)
            if (node.children) {
              this.findSelectedNode(node);
              com = null;
            } else com = comIdMap[id]
          } else {
            com = comIdMap[id]
          }
          if (com) {
            com.select()
          }
        })
      }
    },
    /* 递归选中方法分组下的子节点 */
    findSelectedNode (node) {
      const { comIdMap } = this
      node.children.forEach(item => {
        if (item.children) {
          this.findSelectedNode(item)
        } else {
          const com = comIdMap[item.data.id];
          if (com) {
            com.select();
          }
        }
      })
    },
    /* 递归找出canvasPanel所用的分组下的子节点 */
    findcomOfGroup (node) {
      node.children.forEach(item => {
        if (item.children) {
          // const node = this.layerTree.loadedNodes.find(p => p.data.id === item.id)
          this.findcomOfGroup(item)
        } else {
          item.type === 'com' && this.compOfGroup.push(item);
        }
      })
    },
    /* 递归用于groupShow计算属性 */
    findSelectOfTrue (node, list, selectedId) {
      node.forEach(item => {
        if (selectedId.includes(item.data.id)) {
          list.push(item);
          // item.selected = true;
        }
        if (item.children) this.findSelectOfTrue(item.children, list, selectedId);
      })
      return list;
    },
    clearSelect () {
      this.selectNode(null)
      this.layerTree.clearSelect()
      this.hideCtxMenu()
    },
    selectMuti () {
      const r = {
        x: this.areaStart.x,
        y: this.areaStart.y,
        x1: this.areaWidth + this.areaStart.x,
        y1: this.areaHeight + this.areaStart.y
      }
      const selectCompIds = this.componentList
        .filter(com => !com.lock)
        .filter(com => {
          const r1 = {
            x: com.left,
            y: com.top,
            x1: com.width + com.left,
            y1: com.height + com.top
          }
          if (r1.x1 < r.x || r1.x > r.x1 || r1.y1 < r.y || r1.y > r.y1) { // 判断矩形是否相交
            return false
          }
          return true
        })
        .map(com => com.id)
      this.selectNode(selectCompIds)
      this.layerTree.select(selectCompIds)
      this.hideArea()
    },
    async alignComp (key) {
      const { x, y, w, h } = this.handleRect
      const { currentSelectNodes } = this
      const len = currentSelectNodes.length
      let updateParams
      switch (key) {
        case 'top':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.y', value: Math.round(y) }
            ]
          }))
          break

        case 'verticalCenter':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.x', value: Math.round(x + w / 2 - n.width / 2) }
            ]
          }))
          break

        case 'bottom':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.y', value: Math.round(y + h - n.height) }
            ]
          }))
          break
        case 'left':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.x', value: Math.round(x) }
            ]
          }))
          break
        case 'horizontally':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.y', value: Math.round(y + h / 2 - n.height / 2) }
            ]
          }))
          break
        case 'right':
          updateParams = currentSelectNodes.map(n => ({
            id: n.id,
            keyValPairs: [
              { key: 'attr.x', value: Math.round(x + w - n.width) }
            ]
          }))
          break
        case 'horizontal': {
          const sumDis = _.sumBy(currentSelectNodes, 'width')
          // if (w > sumDis) {
          const gap = (w - sumDis) / (len - 1)
          const sortCom = _.sortBy(currentSelectNodes, ['left'])
          let curX = x
          for (let i = 1; i < len; i++) {
            const com = sortCom[i]
            const lastCom = sortCom[i - 1]
            curX += lastCom.width + gap
            if (!updateParams) updateParams = []
            updateParams.push({
              id: com.id,
              keyValPairs: [
                { key: 'attr.x', value: Math.round(curX) }
              ]
            })
          }
          // }
        }
          break
        case 'vertical': {
          const sumDis = _.sumBy(currentSelectNodes, 'height')
          // if (h > sumDis) {
          const gap = (h - sumDis) / (len - 1)
          const sortCom = _.sortBy(currentSelectNodes, ['top'])
          let curY = y
          for (let i = 1; i < len; i++) {
            const com = sortCom[i]
            const lastCom = sortCom[i - 1]
            curY += lastCom.height + gap
            if (!updateParams) updateParams = []
            updateParams.push({
              id: com.id,
              keyValPairs: [
                { key: 'attr.y', value: Math.round(curY) }
              ]
            })
          }
          // }
        }
          break
      }
      if (updateParams) {
        await this.$store.dispatch('editor/updateScreenCom', updateParams)
        this.updateHandleRect()
      }
    },
    updateEventComId (id) {
      this.lineEventComId = id
    },
    shortcut () {
      this.$shortcut.bind('⌘+right, ctrl+right', { func: this.move, params: 'tenfoldRight' })
      this.$shortcut.bind('⌘+left, ctrl+left', { func: this.move, params: 'tenfoldLeft' })
      this.$shortcut.bind('⌘+up, ctrl+up', { func: this.move, params: 'tenfoldUp' })
      this.$shortcut.bind('⌘+down, ctrl+down', { func: this.move, params: 'tenfoldDown' })
      this.$shortcut.bind('right', { func: this.move, params: 'right' })
      this.$shortcut.bind('left', { func: this.move, params: 'left' })
      this.$shortcut.bind('up', { func: this.move, params: 'up' })
      this.$shortcut.bind('down', { func: this.move, params: 'down' })
      this.$shortcut.bind('⌘+z, ctrl+z', { func: this.undo })
      this.$shortcut.bind('⌘+y, ctrl+y', { func: this.redo })
    },
    async move (direction) {
      let { x: left, y: top, w, h } = this.handleRect
      const { config: { height, width, gridSpace } } = this.screenInfo
      const stepMutiple = 10

      switch (direction) {
        case 'tenfoldLeft':
          left -= gridSpace * stepMutiple
          break
        case 'tenfoldRight':
          left += gridSpace * stepMutiple
          break
        case 'tenfoldUp':
          top -= gridSpace * stepMutiple
          break
        case 'tenfoldDown':
          top += gridSpace * stepMutiple
          break
        case 'right':
          left += gridSpace
          break
        case 'left':
          left -= gridSpace
          break
        case 'up':
          top -= gridSpace
          break
        case 'down':
          top += gridSpace
          break
      }
      left = _.clamp(left, 0, Math.max(width - w, 0))
      top = _.clamp(top, 0, Math.max(height - h, 0))
      const ox = left - this.handleRect.x
      const oy = top - this.handleRect.y
      const updateParams = this.currentSelectNodes.map(n => {
        return {
          id: n.id,
          keyValPairs: [
            { key: 'attr.x', value: Math.round(n.left + ox) },
            { key: 'attr.y', value: Math.round(n.top + oy) }
          ]
        }
      })
      // 如果为第一步 需要保存第一次的状态
      // if (this.snapshotIndex === -1) {
      const comStyle = this.currentSelectNodes.map(d => ({
        left: d.left,
        top: d.top,
        width: d.width,
        height: d.height,
        id: d.id,
        rotate: d.rotate
      }))
      const snapeshotData = _.cloneDeep(comStyle)
      if (!this.cursnapshotData || this.cursnapshotData.length !== snapeshotData.length || _.difference(_.map(this.cursnapshotData, 'id'), _.map(snapeshotData, 'id')).length) {
        const snapeshotParams = this.currentSelectNodes.map(n => {
          return {
            id: n.id,
            keyValPairs: [
              { key: 'attr.x', value: Math.round(n.left) },
              { key: 'attr.y', value: Math.round(n.top) }
            ]
          }
        })
        this.$store.commit('editor/recordSnapshot', snapeshotParams)
      }
      this.$store.commit('editor/recordSnapshot', updateParams)
      await this.$store.dispatch('editor/updateScreenCom', updateParams)
      this.handleRect.x = left
      this.handleRect.y = top
    },
    undo () {
      this.$store.commit('editor/undo')
    },
    redo () {
      this.$store.commit('editor/redo')
    },
    jumpToEvent (id) {
      this.selectNode(id)
      this.$nextTick(() => {
        emitter.emit('jumpToEvent')
      })
    },
    handlEconnectionStart ({ lineStartX, lineStartY }) {
      this.lineStart.x = lineStartX
      this.lineStart.y = lineStartY
      this.lineEventComId = null // 开始拖拽时把选中组件清空
    },
    handlEconnectionMove ({ curX, curY, isShowLine }) {
      this.lineEnd.x = curX
      this.lineEnd.y = curY
      this.isShowLine = true
    },
    handlEconnectionEnd () {
      this.isShowLine = false
      this.isClick = false
      if (this.lineEventComId) {
        emitter.emit('jumpToEvent')
        this.$nextTick(() => {
          canvasBus.emit('event_add_show', this.lineEventComId)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.canvas-panel {
  position: absolute;
  top: 80px;
  left: 80px;
  transform-origin: 0 0;
  transition: 0.2s all ease-in-out;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: rgba(0, 0, 0, 0.5) 0 0 30px 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  .icon-container {
    position: absolute;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: auto;
    .event-btn {
      // position: absolute;
      background: #FF9029;
      // right: 0;
      // top: 0;
      width: 16px;
      height: 16px;
      // transform-origin: right top;
      cursor: pointer;
      z-index: 10000;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      transition: 0.2s;
      .shan {
        height: 16px;
        width: 16px;
        display: inline-block;
        background: url('../../../assets/img/svg/shandian.svg');
        // color: #e5a029;
        font-size: 10px;

      }
      .text {
        color: #fff;
        font-weight: bolder;
      }
      &:hover {
        // transform: scale(1.2);
        width: 20px;
        height: 20px;
        z-index: 10020;
      }
    }

    .linkAge-btn {
      background: #00B1B8;
    }

    .drillDown-btn {
      background: #913DFF;
    }

    .icon-mark {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
  }

}
.contextmenu-disable {
    pointer-events: none;
    opacity: 0.5;
  }
</style>
