<template>
  <div v-if="show">
    <el-dialog
      title="添加事件"
      :visible.sync="show"
      width="30%"
      top="0"
      :close-on-click-modal="false">
      <el-form>
        <el-form-item label="事件名称">
          <el-select v-model="event.trigger" class="w100">
            <el-option v-for="opt in triggerOpt" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="条件">
        </el-form-item>
        <el-form-item label="组件">
          {{lineCom.alias}}
        </el-form-item>
        <el-form-item label="动作">
          <el-select v-model="event.action" class="w100">
            <el-option v-for="opt in actions" :key="opt._id" :value="opt.name" :label="opt.description"></el-option>
          </el-select>
        </el-form-item>
        <el-collapse v-show="event.action === 'animationEvent'" v-model="aniActive" class="w100">
          <el-collapse-item title="动画" name="1">
            <template slot="title">
              <div class="c-header">
                <span class="name">动画</span>
                <div class="btn-box">
                  <span class="btn" @click.stop></span>
                  <span class="btn" @click.stop></span>
                  <span class="btn el-icon-circle-plus" @click.stop="addAnimation(event, event.eventAnimation)"></span>
                  <span class="btn el-icon-delete" @click.stop="delAnimation(event, event.eventAnimation)"></span>
                </div>
              </div>
            </template>
            <el-tabs class="tab-c" type="card" v-model="event.activeAnimation" size="mini">
              <el-tab-pane
                v-for="(animation, index) in event.eventAnimation"
                :label="'动画' + (index + 1)"
                :name="index + ''"
                :key="animation.value">
                <el-form
                  class="form"
                  label-width="100px"
                  label-position="left"
                  size="mini">
                </el-form>
                <el-form-item label="动画类型">
                  <el-select v-model="animation.value" class="w100">
                    <el-option v-for="opt in animationOpt" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="动画时长">
                  <el-input-number
                    v-model="animation.duration"
                    controls-position="right"
                    :step="500"
                    :min="1000"
                    class="w100"
                  ></el-input-number>
                </el-form-item>
                <el-form-item label="动画延时">
                  <el-input-number
                    v-model="animation.delay"
                    controls-position="right"
                    :step="500"
                    :min="0"
                    class="w100"
                  ></el-input-number>
                </el-form-item>
              </el-tab-pane>
            </el-tabs>
            <div class="no-data" v-if="!event.eventAnimation.length">暂无数据</div>
          </el-collapse-item>
        </el-collapse>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="light-blue" size="medium" @click="show = false">取 消</el-button>
        <el-button  type="light-blue" size="medium" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import canvasBus from '@/utils/canvasBus';
import { mapState, mapGetters } from 'vuex';
import { randomStr } from '@/utils/base';
import animationOpt from '@/common/animationClassData'
export default {
  data () {
    return {
      show: false,
      aniActive: '1',
      event: {}, // 添加单个自定义事件
      defaultActions: [ // 组件默认动作
        { name: 'show', description: '显示', params: [], _id: '1' },
        { name: 'hide', description: '隐藏', params: [], _id: '2' },
        { name: 'showHideSwitch', description: '显隐切换', params: [], _id: '3' },
        { name: 'animationEvent', description: '动画', params: [], _id: '4' }
        // { name: 'jumpEvent', description: '链接跳转', params: [], _id: '5' } // 勿删正在调试中
      ],
      defTriggerOpt: [ // 默认事件类型
        { value: 'dataChange', label: '当请求完成或数据变化时' }
      ],
      animationOpt: animationOpt.attention,
      lineEventComId: null // 接受事件的组件 id
    }
  },
  created () {
    canvasBus.on('event_add_show', this.eventAddShow);
  },
  computed: {
    ...mapState({
      screenComs: state => state.editor.screenComs
    }),
    ...mapGetters('editor', ['currentCom', 'currentConfigId', 'getComDataById']),
    triggerOpt () { // 组件事件类型下拉项
      const events = this.currentCom?.events;
      const options = events.map(opt => {
        return {
          value: opt.name,
          label: opt.description,
          params: opt.params
        }
      });
      return [...this.defTriggerOpt, ...options];
    },
    lineCom () { // 关联组件
      return this.getComDataById(this.lineEventComId);
    },
    actions () {
      return [...this.defaultActions, ...this.lineCom.actions]
    }

  },
  methods: {
    eventAddShow (lineEventComId) {
      this.show = true;
      this.lineEventComId = lineEventComId;
      this.addEvt();
    },
    addEvt () {
      if (!this.currentCom) return
      const evt = {
        id: 'event_' + randomStr(10), // id 格式：:"event_nlk1jh96m"
        name: '事件', // 名称
        trigger: '', // 事件类型
        triggerId: this.currentCom.id, // 触发事件的组件id
        conditions: [],
        componentId: [this.lineEventComId], // 接受事件的组件 id
        isConnect: false,
        connectCompId: [],
        action: '', // 动作
        fieldMap: [], // 字段映射,
        jumpInfo: {
          jumpIsLocal: false,
          jumpUrl: '',
          paramsList: [['']],
          mappingName: ['']
        },
        eventAnimation: [],
        aniActive: '1',
        activeAnimation: '0'
      }
      this.event = evt;
    },
    addAnimation (event, eventAnimation) { // 新增动画
      const callback = {
        value: null,
        duration: 1000,
        delay: null
      }
      if (eventAnimation.some(item => !item.value)) {
        this.$message.error('动画类型不能为空');
        return
      }
      eventAnimation.push(callback);
      event.activeAnimation = eventAnimation.length - 1 + '';
    },
    delAnimation (event, eventAnimation) {
      if (eventAnimation.length) {
        eventAnimation.splice(event.activeAnimation, 1);
        // this.saveAnimation();
        event.activeAnimation = '0';
      }
    },

    submit () {
      this.show = false
    }
  }
}
</script>

<style  lang="scss" scoped>

</style>
