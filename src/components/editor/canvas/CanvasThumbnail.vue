<template>
  <div class="thumbnail" :class="[`thumbnail-${visible ? 'show' : 'hide'}`]">
    <div class="seatom-thumbnail" :ref="thumbnailRef" @click="handleClick" >
      <div></div>
      <div :ref="selectRef" class="select-div" @click.stop @mousedown="handleMouseDown" @touchstart="handleMouseDown">
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import canvasBus from '@/utils/canvasBus';
export default {
  data () {
    return {
      selectRef: 'selectDiv',
      thumbnailRef: 'seatom-thumbnail',
      visible: true,
      selectStyle: {
        left: 0,
        top: 0
      },
      selectRect: {
        width: 0,
        height: 0
      }
    }
  },
  props: {
    translate: {
      type: Object
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  watch: {
  },
  mounted () {
    this.thumbnailW = this.$refs[this.thumbnailRef].offsetWidth;
    this.thumbnailH = this.$refs[this.thumbnailRef].offsetHeight;
    this.selectDomRef = this.$refs[this.selectRef];
    this.selectDom = this.$refs[this.selectRef];
    canvasBus.on('thumbnail_scoll', this.thumbnailScoll);
    canvasBus.on('handle-select', this.handleSelect);
    canvasBus.on('thumbnail-hide', () => {
      this.visible = !this.visible
    });
  },
  methods: {
    handleMouseDown (e) {
      e.stopPropagation();

      const startY = e.clientY || e.touches[0].clientY // 当前点击的位置
      const startX = e.clientX || e.touches[0].clientX

      const { left, top } = this.selectStyle
      const { config: { width, height, scale } } = this.screenInfo

      const move = (moveEvent) => {
        const currX = moveEvent.clientX || moveEvent.touches[0].clientX // 鼠标移动的距离
        const currY = moveEvent.clientY || moveEvent.touches[0].clientY

        const mX = _.clamp((left + currX - startX), 0, this.thumbnailW - this.selectRect.width);
        const mY = _.clamp((top + currY - startY), 0, this.thumbnailH - this.selectRect.height);

        this.selectDom.style.left = mX + 'px';
        this.selectDom.style.top = mY + 'px';

        this.selectStyle.left = mX;
        this.selectStyle.top = mY;

        // 移动距离 ：图片大小 = 移动距离 ：图片大小
        const canvasX = mX * width / this.thumbnailW * scale;
        const canvasY = mY * height / this.thumbnailH * scale

        this.$emit('canvasScoll', { canvasX, canvasY })
      }

      const up = (e) => {
        if (e instanceof TouchEvent) {
          document.removeEventListener('touchmove', move)
          document.removeEventListener('touchend', up)
        } else {
          document.removeEventListener('mousemove', move)
          document.removeEventListener('mouseup', up)
        }
      }
      if (e instanceof TouchEvent) {
        document.addEventListener('touchmove', move)
        document.addEventListener('touchend', up)
      } else {
        document.addEventListener('mousemove', move)
        document.addEventListener('mouseup', up)
      }
    },
    thumbnailScoll ({ x, y }) {
      const { config: { width, height, scale } } = this.screenInfo

      const thumbnailX = x * this.thumbnailW / width;
      const thumbnailY = y * this.thumbnailH / height;

      const mX = _.clamp(thumbnailX / scale, 0, this.thumbnailW - this.selectRect.width);
      const mY = _.clamp(thumbnailY / scale, 0, this.thumbnailH - this.selectRect.height);

      this.selectDom.style.left = mX + 'px';
      this.selectDom.style.top = mY + 'px';

      this.selectStyle.left = mX;
      this.selectStyle.top = mY;
    },
    handleSelect ({ screenW, screenH, width: canvasW, height: canvasH }) {
      const { config: { scale } } = this.screenInfo
      const moveW = _.clamp(this.thumbnailW - (canvasW - screenW) / 10 / scale, 10 / scale, this.thumbnailW);
      const moveH = _.clamp(this.thumbnailH - (canvasH - screenH) / 10 / scale, 10 / scale, this.thumbnailH);

      this.selectRect.width = moveW;
      this.selectRect.height = moveH;
      this.selectDomRef.style.width = moveW + 'px';
      this.selectDomRef.style.height = moveH + 'px';
    },
    handleClick (e) {
      const offsetX = e.offsetX // 当前点击的位置
      const offsetY = e.offsetY

      const { config: { width, height, scale } } = this.screenInfo

      const mX = _.clamp(offsetX - this.selectRect.width / 2, 0, this.thumbnailW - this.selectRect.width);
      const mY = _.clamp(offsetY - this.selectRect.height / 2, 0, this.thumbnailH - this.selectRect.height);

      this.selectDom.style.left = mX + 'px';
      this.selectDom.style.top = mY + 'px';

      this.selectStyle.left = mX;
      this.selectStyle.top = mY;

      const canvasX = mX * width / this.thumbnailW * scale;
      const canvasY = mY * height / this.thumbnailH * scale

      this.$emit('canvasScoll', { canvasX, canvasY })
    }
  }
}
</script>

<style lang="scss" scoped>
.thumbnail {
  position: absolute;
  right: 5px;
  bottom: 35px;
  background: black; //rgb(8, 9, 10)
  transition: 0.3s all cubic-bezier(0.22, 0.61, 0.36, 1);
  width: 192px;
  height: 108px;

  .seatom-thumbnail {
    position: relative;
    width: 100%;
    height: 100%;
    outline: 1px solid #444;
    user-select: none;

    .select-div {
      position: absolute;
      z-index: 2;
      width: 98px;
      height: 50px;
      border: 1px solid #2681FF;
      cursor: move;
      transform: translate(0, 0);
    }
  }
}
.thumbnail-show {
  width: 192px;
  height: 108px;
  transform: scale(1);
}
.thumbnail-hide {
  width: 0;
  height: 0;
  transform: scale(0);
}
</style>
