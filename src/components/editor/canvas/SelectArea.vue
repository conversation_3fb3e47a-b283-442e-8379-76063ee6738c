<template>
  <div
    :style="{
      transform: `translate(${start.x}px, ${start.y}px)`,
      width: width + 'px',
      height: height + 'px'
    }"
    class="select-area"
  ></div>
</template>

<script>
export default {
  props: {
    start: {
      type: Object,
      default () {
        return {
          x: 0,
          y: 0
        };
      }
    },
    width: {
      type: Number
    },
    height: {
      type: Number
    }
  }
};
</script>

<style lang="scss" scoped>
.select-area {
  border: 1px dashed #70c0ff;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10000;
  background-color: rgba(125, 179, 255, 0.3);
}
</style>
