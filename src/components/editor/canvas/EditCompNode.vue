<template>
  <div
    ref="compWrap"
    class="seatom-edit-comp-node"
    :class="{
      hide: !comCfg.show && comCfg.type === 'com',
      events_none: comCfg.comName != 'interaction-container-modulepanel'
    }"
    :style="nodeStyle"
  >
    <div class="render-box" :style="comStyle">
      <template>
        <component
          ref="compRef"
          class="component"
          :id="id"
          :data-id="id"
          :attr="comCfg.attr"
          :config="comCfg.config"
          :data="comData"
          :parent="type === 'subCom' ? $parent.$refs.compRef : null"
          :seatom_setParentStyle="seatom_setParentStyle"
          v-if="!!compDefObj && comCfg.show"
          @mounted="handleCompMounted"
          @destroyed="handleCompDestroyed"
          @reRender="handleRerender"
          :is="compDefObj"
          :platform="platform"
          :mainScreenId="mainScreenId"
        >
        </component>
        <!-- 子组件，需等父组件创建好了以后再创建 -->
        <template v-if="compMounted">
          <EditCompNode
            ref="childCompRef"
            v-for="childId in comCfg.children"
            :key="childId"
            :id="childId"
            type="subCom"
          />
        </template>
      </template>
    </div>
    <div class="error-popup" v-if="showError" :style="tipStyle">{{ errorText }}</div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { datastorageList } from '@/api/datastorage';
import compMixins from '@/mixins/comp';
import dataUtil from '@/utils/data';
import VueSocket from 'vue-socket.io';
import emitter from '@/utils/bus';
import { transDrillData } from '@/utils/base';
import quarkDom from 'hz-quark/dist/dom'

const compMap = {
  'interaction-container-referpanel': () => import('@/components/refer-panel/ReferPanel'),
  'interaction-container-dynamicpanel': () => import('@/components/refer-panel/DynamicPanel'),
  'interaction-container-newdynamicpanel': () => import('@/components/refer-panel/NewDynamicPanel'),
  'interaction-container-carousepanel': () => import('@/components/refer-panel/DialogPanel'),
  'interaction-form-group': () => import('@/components/refer-panel/FormPanel.vue'),
  'interaction-container-affixPanel': () => import('@/components/refer-panel/NailFixationPanel'),
  'interaction-container-popoverpanel': () => import('@/components/refer-panel/PopoverPanel.vue'),
  'interaction-container-mapShadowPanel': () => import('@/components/refer-panel/MapShadowPanel'),
  'interaction-container-loop-pitch': () => import('@/components/refer-panel/LoopPitch'),
  'interaction-container-roll-pitch': () => import('@/components/refer-panel/RollPitch'),
  'interaction-container-list-pitch': () => import('@/components/refer-panel/ListPitch'),
  'interaction-container-fold-panel': () => import('@/components/refer-panel/FoldPanel'),
  'interaction-container-popup': () => import('@/components/refer-panel/PopupPanel'),
  'interaction-container-modulepanel': () => import('@/components/refer-panel/EditModulePanel'),
  'interaction-container-flowlayoutpanel': () => import('@/components/refer-panel/FlowLayoutPanel.vue'),
  'interaction-container-statusdialogpanel': () => import('@/components/refer-panel/StatusDialogPanel'),
  'interaction-container-indicator': () => import('@/components/refer-panel/IndicatorPanel')
}

export default {
  name: 'EditCompNode',

  provide: function () {
    return {
      permissionMap: () => null // 编辑页面调试为空
    };
  },

  inject: ['callbackManager'],

  components: {},

  data () {
    return {
      compMap,
      compDefObj: null,
      compMounted: false,
      inheritFilter: null,
      socketStartTime: null,
      copyData: [],
      refresh: false,
      showError: false,
      errorText: '',
      tipStyle: {}
    };
  },

  props: {
    id: {
      type: String,
      required: true
    },
    type: {
      type: String,
      validator (v) {
        return ['com', 'subCom', 'group'].indexOf(v) > -1;
      }
    }
  },

  computed: {
    ...mapState('editor', [
      'comsData',
      'screenFilters',
      'screenInfo',
      'inheritData',
      'currentSelectId'
    ]),
    ...mapGetters('editor', ['getComDataById']),
    ...mapGetters('datacontainer', ['getContainerDataById']),
    indicatorData () {
      return this.$store.state.indicator.indicatorData
    },
    bindContainerData () {
      if (this.sourceType === 'datacontainer') {
        const sourceId = this.dataResponse.source.datacontainer.data.dataContainerComId;
        return this.getContainerDataById(sourceId)
      }
      return { loaded: false, data: [] }
    },
    platform () {
      return this.screenInfo.type;
    },
    fieldMapping () {
      if (_.isEmpty(this.comCfg)) return [];
      return this.comCfg.dataConfig.fieldMapping;
    },
    sourceType () {
      if (_.isEmpty(this.comCfg)) return '';
      return this.comCfg.dataConfig.dataResponse.sourceType;
    },
    sourceId () {
      if (_.isEmpty(this.comCfg)) return '';
      return this.comCfg.dataConfig.dataResponse.source[this.sourceType]
        ? this.comCfg.dataConfig.dataResponse.source[this.sourceType].data
          .sourceId
        : '';
    },
    validFilters () {
      if (_.isEmpty(this.comCfg)) return [];
      const filters = this.comCfg.dataConfig.dataResponse.filters;
      if (!filters.enable) return [];
      return _.filter(filters.list, { enable: true }).map(
        ({ id }) => this.screenFilters[id]
      );
    },
    drillDown () {
      if (_.isEmpty(this.comCfg)) return [];
      return this.comCfg.interactionConfig.drillDown;
    },
    comData () {
      const sourceType = this.comCfg.dataConfig.dataResponse.sourceType;
      let originData = [];
      if (sourceType === 'inherit' || sourceType === 'dialog') {
        originData = this.comsData[this.screenInfo?.relationCompId];
      } else if (sourceType === 'datacontainer') {
        originData = this.bindContainerData?.data || [];
      } else if (sourceType === 'static') {
        originData = this.comCfg.staticData;
      } else {
        originData = this.comsData[this.id];
      }

      if (originData) {
        let filterData = dataUtil.filterData(originData, this.validFilters);
        // 面板数据
        if (sourceType === 'inherit') {
          if (this.indicatorData) {
            filterData = this.indicatorData
          } else {
            filterData = this.inheritData;
          }
        }

        let mapData = dataUtil.mapData(filterData, this.fieldMapping);
        if (this.drillDown.length) {
          for (const e of this.drillDown) {
            if (e.linkType === 'parent') {
              // 下钻类型为父级模式
              const parentField = e.parentField || 'parent';
              const parentFieldVal = e.parentFieldVal || null;
              const validFilters = [
                {
                  content: `return data.filter(item => item.${parentField} == '${parentFieldVal}')`,
                  callbackKeys: []
                }
              ];
              const filterData = dataUtil.linkAgeFilterData(
                mapData,
                {},
                validFilters
              );
              mapData = filterData;
            } else if (e.linkType === 'links') {
              let result = transDrillData(originData, e);
              result = dataUtil.mapData(result, this.fieldMapping);
              mapData = result.filter((item) => !item._parentId);
              mapData = mapData.map((item) =>
                _.omit(item, ['_id', '_parentId'])
              );
              const drill = this.drillDown[0];
              if (drill.sortField && drill.sortType) { // 下钻排序
                mapData = mapData.sort((a, b) => {
                  if (drill.sortType === 'asc') {
                    return a[drill.sortField] - b[drill.sortField]
                  } else {
                    return b[drill.sortField] - a[drill.sortField]
                  }
                })
              }
            }
          }
        }
        return mapData.slice(0, 100); // 编辑页面暂时限制100条数据，防止数据量大页面卡死
      }
      return [];
    },
    comCfg () {
      return this.getComDataById(this.id);
    },
    dataResponse () {
      return this.comCfg.dataConfig.dataResponse;
    },
    tips () {
      return this.dataResponse.tips
    },
    comStyle () {
      const { enable3D, flipH, flipV } = this.comCfg.attr;
      const style = {
        position: 'relative',
        width: '100%',
        height: '100%',
        transform: enable3D
          ? `rotateX(${flipH}deg) rotateY(${flipV}deg)`
          : null
      };
      return style;
    },
    nodeStyle () {
      const { attr } = this.comCfg;
      return {
        // width: attr.w + 'px',
        // height: attr.h + 'px',
        opacity: _.defaultTo(attr.opacity, 1),
        perspective: attr.enable3D ? attr.perspective + 'px' : null
      };
    },
    comSize () {
      return {
        w: this.comCfg.attr.w,
        h: this.comCfg.attr.h
      };
    },
    moduleName () {
      if (!this.comCfg) return '';
      const { comName, version } = this.comCfg;
      return `${comName}@${version}`;
    },
    // 主屏ID
    mainScreenId () {
      if (this.screenInfo.isDynamicScreen) {
        return Number(this.screenInfo.parentId)
      }
      return this.screenInfo.id
    },
    currentFilterData () {
      const data = this.comsData[this.id] || [];
      const filterData = dataUtil.filterData(data, this.validFilters);
      return filterData
    }
  },
  watch: {
    comSize (newAttr, oldAttr) {
      if (!_.isEqual(newAttr, oldAttr)) {
        const comp = this.$refs.compRef;
        this.$nextTick(() => {
          const compWrap = this.$refs.compWrap;
          if (comp) {
            if (this.platform === 'pc') {
              comp.resize({ width: newAttr.w, height: newAttr.h });
            } else {
              setTimeout(() => {
                const rect = compWrap.getBoundingClientRect();
                comp.resize({ width: rect.width, height: rect.height });
              }, 0)
            }
          }
        });
      }
    },
    moduleName (newVal) {
      if (newVal) {
        this.compMounted = false;
        this.loadCompDef();
        this.fetchData();
      }
    },
    sourceType: {
      handler (newVal) {
        if (newVal === 'websocket') {
          emitter.emit('updateSocketData', []);
          this.$store.commit('editor/updateComData', {
            componentId: this.id,
            data: []
          });
          this.copyData = [];
          this.createSocket();
        } else if (this.socket) {
          // 断开socket连接
          this.socket.io.disconnect();
        }
      },
      immediate: true
    },
    sourceId: {
      handler (newVal) {
        this.$store.commit('editor/updateComData', {
          componentId: this.id,
          data: []
        });
        if (newVal && this.sourceType === 'websocket') {
          this.copyData = [];
          emitter.emit('updateSocketData', []);
          this.createSocket();
        } else if (this.socket) {
          // 断开socket连接
          this.copyData = [];
          emitter.emit('updateSocketData', []);
          this.socket.io.disconnect();
        }
      }
    },
    tips: {
      handler: function () {
        this.handleTipsInfo();
      },
      deep: true
    },
    comData: {
      handler: function () {
        this.handleTipsInfo();
      },
      deep: true
    }
  },

  created () {
    this.loadCompDef();
    this.fetchData();
    emitter.on('updateSocketDuration', (id) => {
      if (id === this.id) {
        this.$store.commit('editor/updateComData', {
          componentId: this.id,
          data: []
        });
        emitter.emit('updateSocketData', []);
        this.copyData = [];
        this.socket && this.socket.io.close();
        this.socket = null;
        this.socketStartTime = null;
        this.createSocket();
      }
    });

    // 若为数据容器，则将过滤后的数据放到全局 当做公共数据源
    if (this.comCfg.comName === 'interaction-container-datacontainer') {
      this.$watch('currentFilterData', data => {
        if (data) {
          this.$store.dispatch('datacontainer/setContanerData', { id: this.id, data });
        }
      }, { deep: true, immediate: true })
    }
  },

  beforeDestroy () {
    emitter.off('updateSocketDuration');
    this.socket && this.socket.io.close();
  },

  methods: {
    // 显示错误遮罩层
    showErrorFn (show, msg) {
      this.showError = show;
      this.errorText = msg;
    },
    async createSocket () {
      if (this.socket) {
        this.socketStartTime = Date.now();
        this.socket.io.connect();
        return true;
      }
      const id = this.comCfg.dataConfig
        ? this.comCfg.dataConfig.dataResponse.source.websocket.data.sourceId
        : '';
      if (!id) return false;
      const data = {
        id
      };
      const sourceRes = await datastorageList(data);
      const source = sourceRes.success ? sourceRes.data : [];
      const address = source[0] ? source[0].config.host : '';
      let path = source[0] ? source[0].config.path : '';
      const duration = this.comCfg.dataConfig
        ? this.comCfg.dataConfig.dataResponse.source.websocket.data.duration
        : 10;
      const durationType = this.comCfg.dataConfig
        ? this.comCfg.dataConfig.dataResponse.source.websocket.data.durationType
        : 'minute';
      const range = duration * (durationType === 'minute' ? 60 : 1) * 1000;
      if (address) {
        const options = {
          debug: false,
          connection: address
        };
        if (path) {
          if (path.charAt(0) !== '/') path = '/' + path;
          options.options = {
            path
          };
        }
        this.socket = new VueSocket(options);
        this.socket.emitter.addListener(
          'message',
          (data) => {
            let newData = _.cloneDeep(this.comData);
            const now = Date.now();
            if (!this.socketStartTime) {
              this.socketStartTime = now;
            } else {
              if (now - this.socketStartTime > range) {
                // 超出时间窗口范围
                let willRemoveIdx = -1;
                for (let i = 0; i < this.copyData.length; i++) {
                  if (now - this.copyData[i].date > range) {
                    willRemoveIdx = i;
                  } else break;
                }
                if (willRemoveIdx > -1) {
                  // console.log(willRemoveIdx)
                  this.copyData.splice(0, Math.max(willRemoveIdx, 1));
                  newData.splice(0, Math.max(willRemoveIdx, 1));
                }
                this.socketStartTime = this.copyData[0]
                  ? this.copyData[0].date
                  : now;
              }
            }
            if (typeof data === 'object') {
              if (Array.isArray(data)) {
                let copyData = _.cloneDeep(data);
                copyData = copyData.map((item) => {
                  item.date = now;
                  return item;
                });
                this.copyData = this.copyData.concat(copyData);
                newData = newData.concat(data);
              } else {
                const copyData = _.cloneDeep(data);
                copyData.date = now;
                this.copyData.push(copyData);
                newData.push(data);
              }
              if (this.id === this.currentSelectId) {
                emitter.emit('updateSocketData', newData);
              }
              this.$store.commit('editor/updateComData', {
                componentId: this.id,
                data: newData
              });
            }
          },
          this
        );
      }
    },
    handleCompMounted () {
      this.compMounted = true;
    },
    handleCompDestroyed () {
      this.compMounted = false;
    },
    handleRerender () {
      const childRefs = this.$refs.childCompRef;
      if (childRefs && childRefs.length) {
        childRefs.forEach((childComp) => {
          const comp = childComp.$refs.compRef;
          if (comp && _.isFunction(comp.render)) {
            comp.render();
          }
        });
      }
    },
    // 回调函数
    seatom_updateCallbackValue (param = {}) {
      const callbackParams = this.comCfg.interactionConfig.callbackParams;
      const newParam = {};
      if (callbackParams && callbackParams.length) {
        callbackParams.forEach((p) => {
          if (_.has(param, p.fieldValue)) {
            newParam[p.variableName] = param[p.fieldValue];
          }
        });
        this.callbackManager().updateCallbackValue(newParam);
        const message = {
          type: 'callback',
          params: newParam
        };
        this.$socket && this.$socket.emit('chat', message);
      }
    },
    seatom_setParentStyle (style = {}) {
      Object.keys(style).forEach((key) => {
        this.$el.style.setProperty(key, style[key]);
      });
    },
    async loadCompDef () {
      if (!this.moduleName) return;
      // 加载面板类组件
      if (_.has(this.compMap, this.comCfg.comName)) {
        const compDef = await this.compMap[this.comCfg.comName]();
        this.compDefObj = compDef.default;
        return;
      }
      // eslint-disable-next-line
      const res = await System.import(this.moduleName);
      const compDef = res.default;
      (compDef.mixins || (compDef.mixins = [])).push(compMixins);
      this.compDefObj = compDef;
    },
    async fetchData () {
      const sourceType = this.comCfg.dataConfig.dataResponse.sourceType;
      if (sourceType === 'datacontainer') return;
      await this.$store.dispatch('editor/getCompData', {
        componentId: sourceType === 'dialog' || sourceType === 'inherit' ? this.screenInfo?.relationCompId : this.id,
        workspaceId: this.screenInfo.workspaceId,
        callbackManager: this.callbackManager
      });
    },
    handleTipsInfo () {
      const resultData = _.cloneDeep(this.comData);
      const tips = this.dataResponse.tips;
      if (tips && tips.open) { // 提示信息
        const conditions = tips.conditions || [];
        let target = null;
        for (const item of conditions) {
          if (item.type === 'empty') {
            if (resultData.length === 0) {
              target = item;
              break;
            }
          } else if (item.type === 'diy') {
            let flag = false;
            const obj = resultData[0];
            if (obj) {
              const condition = item.condition;
              const val = obj[item.field];
              const expected = item.value;
              switch (condition) {
                // eslint-disable-next-line
                case '==': flag = val == expected;
                  break;
                case '>': flag = val > expected;
                  break;
                case '<': flag = val < expected;
                  break;
                case '>=': flag = val >= expected;
                  break;
                case '<=': flag = val <= expected;
                  break;
                default: flag = false;
                  break;
              }
              if (flag) {
                target = item;
                break;
              }
            }
          }
        }
        if (target) {
          let tipsStyle = '';
          tipsStyle += quarkDom.formatText(target);
          tipsStyle += quarkDom.formatBackground(target.background);
          this.tipStyle = tipsStyle;
          this.showErrorFn(true, target.info);
          return
        }
      }
      this.showError = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.seatom-edit-comp-node {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  // pointer-events: none; // 禁止事件触发
  user-select: none;
  &.hide {
    background: #84717161;
  }
  &.events_none {
    pointer-events: none; // 禁止事件触发
  }
  .render-box {
    .component {
      width: 100%;
      height: 100%;
    }
  }

  .error-popup {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgb(255 255 255 / 80%);
    overflow: hidden;
    font-size: 16px;
    z-index: 100;
  }
}
</style>
