<template>
  <div class="ai-dialog-wrapper">
    <el-dialog title="AI 生成" :visible.sync="visible" width="785px" top="0" custom-class="seatom-dialog">
      <div class="content">
        <p><span class="text-red">* </span>您可以给我一些具有业务属性的描述，我们会根据您的描述帮您生成大屏</p>
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入描述信息"
          class="desc-textarea"
          v-model="desc">
        </el-input>
      </div>
      <div class="select-table">
        <p class="title">您可以选择数据表作为数据辅助：</p>
        <div class="table-tree">
          <div class="left">
            <el-input v-model="search" auto-complete="off" size="mini" @input="searchTree" prefix-icon="el-icon-search" />
            <div class="tree"
              v-loading="treeLoading"
              element-loading-text="加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.8)" >
              <el-tree
                :data="tableData"
                lazy
                :props="defaultProps"
                :load="load"
                v-show="!showSearchTree"
                node-key="folder_id">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <span class="icon">
                    <span v-if="data.tb_id">
                      <el-checkbox size="mini" @change="selectTable(data, $event)" :value="isChecked(data)"></el-checkbox>
                      <hz-icon name="word" class="suffix-icon"></hz-icon>
                    </span>
                    <i v-else-if="node.expanded"  class="el-icon-folder-opened"></i>
                    <i v-else class="el-icon-folder"></i>
                  </span>
                  <span>{{ node.label }}</span>
                </span>
              </el-tree>
              <el-tree
                :data="filterData"
                :props="defaultProps"
                default-expand-all
                v-show="showSearchTree"
                node-key="folder_id">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <span class="icon">
                    <span v-if="data.tb_id">
                      <el-checkbox size="mini" @change="selectTable(data, $event)" :value="isChecked(data)"></el-checkbox>
                      <hz-icon name="word" class="suffix-icon"></hz-icon>
                    </span>
                    <i v-else-if="node.expanded"  class="el-icon-folder-opened"></i>
                    <i v-else class="el-icon-folder"></i>
                  </span>
                  <span>{{ node.label }}</span>
                </span>
              </el-tree>
            </div>
          </div>
          <div class="right">
            <p class="header">
              <span class="num">已选择<span class="tag">{{ selectedTables.length }}</span></span>
              <span class="color-primary" @click="clearTables()">清空</span>
            </p>
            <ul class="table-list">
              <li class="table-item" v-for="item in selectedTables" :key="item.tb_id">
                <hz-icon name="word" class="prefix-icon"></hz-icon>
                <span class="name">{{ item.name }}</span>
                <span class="delete-icon" @click="deleteTable(item)"><i class="el-icon-delete"></i></span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="plain" class="common-cancel" @click="cancel()">取消</el-button>
        <el-button size="mini" type="primary" @click="generate()">AI生成</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getTree, getTbList, searchTb } from '@/api/datastorage';
import { aiCreateScreen } from '@/api/screen';
export default {
  name: 'ai-dialog',
  data () {
    return {
      visible: false,
      desc: '',
      search: '',
      projectId: '',
      showSearchTree: false,
      treeLoading: false,
      selectedTables: [],
      defaultProps: {
        label: 'name',
        children: 'sub_folders'
      },
      tableData: [{
        folder_id: 'dmc',
        name: 'DMC'
      }],
      filterData: [],
      folderList: []
    }
  },
  computed: {
    isChecked () {
      return (data) => {
        return this.selectedTables.findIndex(tb => tb.tb_id === data.tb_id) > -1
      }
    }
  },
  methods: {
    show (projectId) {
      this.projectId = projectId
      this.visible = true
    },
    cancel () {
      this.visible = false
    },
    async generate () {
      const { workspaceId } = this.$route.params
      const projectId = localStorage.getItem('projectId') || this.projectId
      const tableIds = {
        dmc: this.selectedTables.map(item => item.tb_id)
      }
      const res = await aiCreateScreen({
        name: 'AI生成',
        workspaceId,
        projectId,
        tableIds
      })
      if (res.success) {
        const id = res.data.id
        this.$router.push(`/screen/edit/${id}`)
      }
      this.visible = false
    },
    async load (node, resolve) {
      if (node.level === 0) return resolve(this.tableData)
      if (node.level === 1) {
        resolve(await this.getTree(node.data.folder_id))
      } else if (node.level > 0) {
        if (!node.data.tb_id) {
          resolve(await this.getTbList(node.data.folder_id, node.data.type))
        } else {
          resolve([])
        }
      }
    },
    async getTree (type = 'dmc') {
      const data = {
        type
      }
      const res = await getTree(data);
      if (res && res.success) {
        return res.data.folder_list
      }
      return []
    },
    async getTbList (folderId, filterTree) {
      const data = {
        folderId,
        filterTree,
        type: 'dmc'
      }
      const res = await getTbList(data);
      if (res && res.success) {
        return [...res.data.sub_folders, ...res.data.tb_list]
      }
      return []
    },
    selectTable (data, val) {
      if (val) {
        this.selectedTables.push(data)
      } else {
        const index = this.selectedTables.findIndex(tb => tb.tb_id === data.tb_id)
        if (index > -1) {
          this.selectedTables.splice(index, 1)
        }
      }
    },
    clearTables () {
      this.selectedTables = []
    },
    deleteTable (data) {
      const index = this.selectedTables.findIndex(tb => tb.tb_id === data.tb_id)
      if (index > -1) {
        this.selectedTables.splice(index, 1)
      }
    },
    searchTree: _.debounce(function (val) {
      if (val) {
        this.showSearchTree = true
        this.searchTb(val)
      } else {
        this.showSearchTree = false
      }
    }, 500),
    async searchTb (str) {
      const data = {
        type: 'dmc',
        filterStr: str
      }
      this.treeLoading = true
      const res = await searchTb(data)
      this.treeLoading = false
      if (res && res.success) {
        const folderList = res.data.folder_list
        const getList = function (folderList) {
          for (let index = 0; index < folderList.length; index++) {
            if (!folderList[index].sub_folders) {
              folderList[index].sub_folders = []
            }
            if (folderList[index].tb_list) {
              folderList[index].sub_folders = folderList[index].sub_folders.concat(folderList[index].tb_list)
            }
            if (folderList[index].sub_folders && folderList[index].sub_folders.length !== 0) {
              getList(folderList[index].sub_folders)
            }
          }
        }
        getList(folderList)
        this.filterData = [{
          folder_id: 'dmc',
          name: 'DMC',
          sub_folders: folderList
        }]
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.text-red {
  color: #FF5266;
}
.ai-dialog-wrapper {
  .content {
    color: #FFF;
  }
  .desc-textarea {
    width: 100%;
    margin-top: 10px;
  }
  .select-table {
    color: #FFF;
    margin-top: 10px;
    margin-bottom: 5px;
    .title {
      margin: 0 0 8px 0;
    }
    .table-tree {
      height: 100%;
      display: flex;
      border-radius: 8px;
      border: 1px solid rgba(204, 219, 255, 0.16);
      .left {
        min-width: 300px;
        max-width: 500px;
        padding: 16px;
        border-right: 1px solid rgba(204, 219, 255, 0.16);
        background: #1F2430;
        .tree {
          margin-top: 10px;
          .custom-tree-node {
            display: flex;
            padding: 10px 10px 10px 16px;
            align-items: center;
            align-self: stretch;
            color: #FFF;
            .icon {
              margin-right: 4px;
              .suffix-icon {
                margin-left: 6px;
              }
            }
          }
        }
      }
      .right {
        padding: 16px 24px;
        flex: 1;
        background: #191D25;
        overflow: hidden;
        max-height: 470px;
        display: flex;
        flex-direction: column;
        .header {
          display: flex;
          line-height: 28px;
          justify-content: space-between;
          .num {
            display: flex;
            align-items: center;
          }
          .tag {
            display: inline-block;
            margin-left: 5px;
            border-radius: 4px;
            line-height: 13px;
            padding: 2px 0;
            width: 20px;
            background: rgba(204, 219, 255, 0.10);
            text-align: center;
            vertical-align: middle;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.70);
          }
          .color-primary {
            color: #3D85FF;
            cursor: pointer;
          }
        }
        .table-list {
          margin-top: 10px;
          flex: 1;
          overflow: auto;
          .table-item {
            display: flex;
            padding: 8px;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            &:hover {
              border-radius: 4px;
              background-color: rgba(204, 219, 255, 0.10);
              .delete-icon {
                display: inline;
              }
            }
            .name {
              display: inline-block;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              flex: 1;
              margin-right: 10px;
            }
            .delete-icon {
              display: none;
            }
            .prefix-icon {
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.el-tree .el-tree-node__content:hover {
  background-color: rgba(204, 219, 255, 0.10);
  border-radius: 4px;
}
</style>
