<template>
  <div class="refer-screenlist" v-show="!isControlled">
    <div class="config-control">
      <el-collapse v-model="active" accordion class="w100">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="c-header">
              <span class="name">引用列表</span>
              <div class="btn-box" v-if="active === '1'">
                <span class="btn" @click.stop></span>
                <span class="btn" @click.stop></span>
                <span class="btn el-icon-circle-plus" @click.stop="add"></span>
                <span class="btn el-icon-delete" @click.stop="del"></span>
              </div>
            </div>
          </template>
          <el-tabs class="tab-c" type="card" v-model="activePanel" size="mini">
            <el-tab-pane
                :label="'大屏' + (index + 1)"
                :name="index + ''"
                v-for="(item, index) in screens"
                :key="index"
              >
              <el-form
                class="form"
                label-width="90px"
                label-position="right"
                label-suffix="："
                size="mini">
                <el-form-item label="大屏选择">
                  <el-select v-model="item.id" class="w100" placeholder="选择大屏" filterable @change="selectChange">
                    <template slot="prefix">
                      <span class="el-icon-document" title="复制id" @click.stop v-clipboard:copy="item.id + ''" v-clipboard:success="onCopy" v-clipboard:error="onCopyError"></span>
                    </template>
                    <el-option v-for="opt in screensOptCopy" :key="opt.id" :value="opt.id" :label="opt.name" :disabled="opt.disabled"></el-option>
                  </el-select>
                </el-form-item>
              </el-form>
              <div class="tips">提示：所选项目必须公开发布</div>
              <div class="edit-btn">
                <el-button v-if="item.id" type="primary" @click="editScreen(item.id)">编辑引用项目</el-button>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import emitter from '@/utils/bus'
export default {
  name: 'ReferScreenlist', // 引用面板 引用大屏配置
  data () {
    return {
      active: '1',
      activePanel: '0',
      screens: []
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      screenGroupData: state => state.editor.screenGroupData
    }),
    ...mapGetters('editor', ['currentCom']),
    screensOptCopy () {
      const { type, id } = this.screenInfo;
      let screensOpt = [];
      // console.log(this.screenGroupData, 'ssssss');
      // this.screenGroupData.forEach(project => {
      //   const screens = project.screens;
      //   screensOpt.push(...screens);
      // })
      screensOpt = this.screenGroupData
      let screens = screensOpt.map(item => {
        if (item.id === id) { // 去掉当前大屏
          return {
            ...item,
            disabled: true
          }
        }
        return item
      })

      screens = screens.filter(item => { // 公开发布且同一平台的大屏
        return item.isPublic && item.type === type
      })

      if (type === 'mobile') {
        screens = screens.filter(item => item.screenType !== 'scene');
      }

      return screens
    },
    isControlled () {
      return this.currentCom.config.isControlled;
    }
  },
  watch: {
    'currentCom.id': {
      handler: function (val) {
        if (val) {
          this.screens = _.cloneDeep(this.currentCom.config.screens || [])
        }
      },
      immediate: true
    },
    activePanel: {
      handler: function (val) {
        emitter.emit('ReferPanelState', { id: this.currentCom.id, state: this.screens[val].id })
      }
    }
  },
  methods: {
    add () { // 新增
      const screen = {
        id: ''
      }
      this.screens.push(screen);
      this.activePanel = this.screens.length - 1 + '';
      this.saveScreens();
    },
    del () { // 删除
      if (this.screens.length) {
        this.screens.splice(this.activePanel, 1);
        this.activePanel = '0';
        this.saveScreens();
      }
    },
    saveScreens () {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'config.screens', value: _.cloneDeep(this.screens) }
        ]
      });
    },
    editScreen (screenId) {
      const dataResponse = this.currentCom.dataConfig.dataResponse
      const routeData = this.$router.resolve({
        path: `/screen/edit/${screenId}`,
        query: {
          screenId: this.screenInfo.id,
          comp: 'referPanel',
          type: dataResponse.sourceType,
          cid: this.currentCom.id
        }
      })
      window.open(routeData.href, `screen_${screenId}`)
    },
    async selectChange (id) {
      await this.saveScreens();
      emitter.emit('ReferPanelState', { id: this.currentCom.id, state: id })
    },
    onCopy () {
      this.$message.success('复制成功');
    },
    onCopyError () {
      this.$message.warn('复制失败');
    }
  }
}
</script>

<style lang="scss" scoped>
.refer-screenlist {
  .tab-c {
    ::v-deep {
      .el-tabs__nav-wrap::after {
        display: none;
      }
      .el-tabs__nav-next,
      .el-tabs__nav-prev {
        line-height: 35px;
      }
      .el-tabs__item {
        height: 35px;
        line-height: 35px;
        font-size: 12px;
      }
    }
  }
  .c-header {
    width: 250px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-box {
      span {
        font-size: 14px;
        margin-left: 10px;
      }
    }
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
      color: var(--control-text-color);
    }
    .el-collapse-item__content {
      padding: 8px 0;
    }
    .el-tabs--card>.el-tabs__header {
      border-bottom: unset;
      .el-tabs__item {
        height: 28px;
        line-height: 28px;
        border-radius: 4px 4px 0 0;
      }
      .el-tabs__nav-next, .el-tabs__nav-prev {
        line-height: 28px;
      }
      .el-tabs__nav-wrap.is-scrollable {
          padding: 0 20px;
          box-sizing: border-box;
      }
    }
  }
  .w100 {
    width: 100%;
  }
  .tips {
    font-size: 12px;
    color: var(--control-text-color);
  }
  .edit-btn {
    margin-top: 20px;
    button {
      width: 100%;
    }
  }
  .screen-id {
    color: #bdbdbd;
    font-size: 12px;
    line-height: 14px;
    margin-top: 5px;
    user-select: all;
  }
}
</style>
