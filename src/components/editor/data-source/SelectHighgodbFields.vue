<template>
  <div>
    <el-dialog class="create-datasource" :visible.sync="show" title="批量添加字段" append-to-body width="955px" :close-on-click-modal="false" @close="closeDialog" top="0">
      <div style="display: flex;">
      <div
        class="hide-scrollbar"
        element-loading-background="#1F2430"
        v-loading="checkboxLoading" style="width: 65%">
        <el-input
        placeholder="搜索"
        size="mini"
        @input="searchField"
        v-model="filterText">
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
        <!-- <div style="margin: 20px 0 0;">
          <el-checkbox-group v-model="chekedFieldList">
                    <el-checkbox>显示已选</el-checkbox>
          </el-checkbox-group>
        </div> -->
        <el-row class="check-row"  v-show="showSearchField">
            <el-col :span="24" class="check-group">
                <el-checkbox-group v-model="chekedFieldList">
                    <el-checkbox @change="handleCheckedFieldChange($event,index,item)" v-for="(item, index) in searchFieldList" :label="item.name" :key="item.name" :title="item.name">{{item.name}}</el-checkbox>
                </el-checkbox-group>
            </el-col>
        </el-row>
        <el-row
          class="check-row"
          v-show="!showSearchField"
          style="min-height: 150px;">
          <div v-for="(fielditem,index) in fieldList" :key="fielditem.type" v-show="fielditem.fields.length !== 0">
            <el-col :span="24" class="check-title">
              <el-checkbox v-model="fielditem.isIndeterminate" @change="handleCheckAllChange($event,fielditem,index)"><img class="imgStyle" style="vertical-align: -0.25em;" :src="fielditem.iconType" alt="">{{fielditem.type | formatField}}</el-checkbox>
              <div class="bar"></div>
            </el-col>
            <el-col :span="18" class="check-group">
                <el-checkbox-group v-model="chekedFieldList">
                    <el-checkbox
                      @change="handleCheckedFieldChange($event,index,item)"
                      v-for="item in fielditem.fields"
                      :label="item.name"
                      :title="item.name"
                      :key="item.name">
                      {{item.name}}
                    </el-checkbox>
                </el-checkbox-group>
            </el-col>
          </div>
        </el-row>
      </div>
      <div element-loading-background="#1F2430" v-loading="checkboxLoading" class="choosed">
        <div class="header">已选择<span class="choosed_number">{{choosedArry.length}}</span><span class="choosed_clear" @click="clear_all">清空</span></div>
        <div class="choosed_content" v-for="(item, index) in choosedArry" :key="item.name + index"><img class="imgStyle" :src="item.imgSrc" alt=""><span class="chossed_content_name">{{item.name}}</span></div>
      </div>
      </div>
      <div slot="footer">
        <el-button type="light-blue" size="medium" @click="submit" :loading="loading">确定</el-button>
        <el-button type="text" size="medium" @click="closeDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
  </template>
<script>
import { getFieldList } from '@/api/datastorage';
import { mapState, mapGetters } from 'vuex';
export default {
  name: 'SelectOracleFields',
  data () {
    return {
      checkboxLoading: false,
      searchFieldList: [],
      showSearchField: false,
      checkAll: false,
      chekedFields: [],
      chekedFieldList: [],
      fieldList: [],
      isIndeterminate: true,
      radio: 3,
      filterText: '',
      show: false,
      title: '选择工作表',
      dbList: [],
      loading: false,
      loading2: false,
      choosedArry: [],
      imgSrc: require('../../../../public/img/icon/type-date.png'),
      fieldsType: ''
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      currentSelectId: state => state.editor.currentSelectId
    }),
    ...mapGetters('editor', ['currentConfigId'])
  },
  mounted () {
  },
  methods: {
    searchField (filterStr) {
      // this.chekedFields = []
      // this.chekedFieldList = []
      this.searchFieldList = []
      for (let index = 0; index < this.fieldList.length; index++) {
        const arr = this.fieldList[index].fields.filter(item => {
          if (item.name.indexOf(filterStr) !== -1) {
            return item
          }
        })
        this.searchFieldList = [...this.searchFieldList, ...arr]
      }
      this.showSearchField = true
      if (this.filterText === '') {
        this.showSearchField = false
      }
    },
    async getFieldList (tbName) {
      this.checkboxLoading = true
      const data = {
        type: 'highgodb',
        tbName,
        componentId: this.currentConfigId,
        workspaceId: this.screenInfo.workspaceId
      }
      const res = await getFieldList(data);
      if (res && res.success) {
        this.fieldList = res.data.filter(item => item.fields.length);
        this.chekedFields.forEach(it => { delete it.imgSrc })
        this.fieldList.forEach(item => {
          item.fields.forEach(it => {
            it.type = item.type
            it.original_name = it.name
            it.describe = ''
          })
        })
        for (let index = 0; index < this.fieldList.length; index++) {
          // const hasIntersection = _.intersectionWith(this.chekedFields, this.fieldList[index].fields, _.isEqual);
          const hasIntersection = []
          for (let i = 0; i < this.fieldList[index].fields.length; i++) {
            const it = this.fieldList[index].fields[i];
            this.chekedFields.forEach(item => {
              if (it.original_name === item.original_name) {
                hasIntersection.push(item)
              }
            })
            if (this.fieldList[index].type === 'date') {
              this.fieldList[index].iconType = require('../../../../public/img/icon/type-date.png')
            } else if (this.fieldList[index].type === 'string') {
              this.fieldList[index].iconType = require('../../../../public/img/icon/type-string.png')
            } else if (this.fieldList[index].type === 'number') {
              this.fieldList[index].iconType = require('../../../../public/img/icon/type-number.png')
            }
          }
          this.fieldList[index].isIndeterminate = hasIntersection.length === this.fieldList[index].fields.length
          // this.fieldList[index].fields = this.fieldList[index].fields.map(item => {
          //   item.type = this.fieldList[index].type;
          //   return item;
          // })
        }
      }
      this.checkboxLoading = false
    },
    handleCheckAllChange (val, item, index) {
      if (val) {
        if (this.fieldsType === 'dimension') {
          const fieldType = this.fieldList[index].type
          this.chekedFields = this.chekedFields.filter(item => item.type !== fieldType)
          this.chekedFields = [...this.chekedFields, ...this.fieldList[index].fields]
          item.fields.forEach(it => {
            if (!this.chekedFieldList.includes(it.name)) {
              this.chekedFieldList.push(it.name)
            }
          })
          this.fieldList[index].isIndeterminate = true
          const itemFields = _.cloneDeep(item.fields)
          const useItemFields = _.pullAllBy(itemFields, this.choosedArry, 'name')
          this.choosedArry = [...new Set([...this.choosedArry, ...useItemFields])]
        } else if (this.fieldsType === 'numericalValue') {
          const itemFields = _.cloneDeep(item.fields)
          const checkAllArry = _.pullAllBy(itemFields, this.chekedFields, 'original_name')
          this.choosedArry.push(...checkAllArry)
          const fieldType = this.fieldList[index].type
          this.chekedFields = this.chekedFields.filter(item => item.type !== fieldType)
          this.chekedFields = [...this.chekedFields, ...this.fieldList[index].fields]
          item.fields.forEach(it => {
            if (!this.chekedFieldList.includes(it.name)) {
              this.chekedFieldList.push(it.name)
            }
          })
          this.fieldList[index].isIndeterminate = true
        }

        this.choosedArry.forEach(item => {
          switch (item.type) {
            case 'date' :
              item.imgSrc = require('../../../../public/img/icon/type-date.png')
              this.imgSrc = require('../../../../public/img/icon/type-date.png')
              break
            case 'string' :
              item.imgSrc = require('../../../../public/img/icon/type-string.png')
              this.imgSrc = require('../../../../public/img/icon/type-string.png')
              break
            case 'number' :
              item.imgSrc = require('../../../../public/img/icon/type-number.png')
              this.imgSrc = require('../../../../public/img/icon/type-number.png')
              break
          }
        })
      } else {
        if (this.fieldsType === 'dimension') {
          _.pullAllBy(this.chekedFields, this.fieldList[index].fields, 'original_name');
          const fieldFid = []
          this.fieldList[index].fields.forEach(it => {
            fieldFid.push(it.name)
          })
          _.pullAll(this.chekedFieldList, fieldFid)
          this.chekedFieldList = this.chekedFieldList.filter(value => !fieldFid.includes(value))
          this.fieldList[index].isIndeterminate = false
          _.pullAllBy(this.choosedArry, item.fields, 'original_name')
        } else if (this.fieldsType === 'numericalValue') {
          _.pullAllBy(this.chekedFields, this.fieldList[index].fields, 'original_name');
          const fieldFid = []
          this.fieldList[index].fields.forEach(it => {
            fieldFid.push(it.name)
          })
          _.pullAll(this.chekedFieldList, fieldFid)
          this.chekedFieldList = this.chekedFieldList.filter(value => !fieldFid.includes(value))
          this.fieldList[index].isIndeterminate = false
          const tArr = _.cloneDeep(this.fieldList[index].fields)
          for (var i = this.choosedArry.length - 1; i >= 0; i--) {
            const a = this.choosedArry[i];
            for (var j = tArr.length - 1; j >= 0; j--) {
              const b = tArr[j];
              if (a.original_name === b.original_name) {
                this.choosedArry.splice(i, 1);
                tArr.splice(j, 1);
                break;
              }
            }
          }
        }

        this.choosedArry.forEach(item => {
          switch (item.type) {
            case 'date' :
              item.imgSrc = require('../../../../public/img/icon/type-date.png')
              this.imgSrc = require('../../../../public/img/icon/type-date.png')
              break
            case 'string' :
              item.imgSrc = require('../../../../public/img/icon/type-string.png')
              this.imgSrc = require('../../../../public/img/icon/type-string.png')
              break
            case 'number' :
              item.imgSrc = require('../../../../public/img/icon/type-number.png')
              this.imgSrc = require('../../../../public/img/icon/type-number.png')
              break
          }
        })
      }
    },
    handleCheckedFieldChange (value, index, item) {
      switch (item.type) {
        case 'date' :
          item.imgSrc = require('../../../../public/img/icon/type-date.png')
          this.imgSrc = require('../../../../public/img/icon/type-date.png')
          break
        case 'string' :
          item.imgSrc = require('../../../../public/img/icon/type-string.png')
          this.imgSrc = require('../../../../public/img/icon/type-string.png')
          break
        case 'number' :
          item.imgSrc = require('../../../../public/img/icon/type-number.png')
          this.imgSrc = require('../../../../public/img/icon/type-number.png')
          break
      }
      if (!value) {
        _.pullAllBy(this.chekedFields, [item]);
        for (let i = this.choosedArry.length - 1; i >= 0; i--) {
          if (this.choosedArry[i].original_name === item.original_name) {
            this.choosedArry.splice(i, 1)
            break
          }
        }
        if (!this.showSearchField) {
          const hasIntersection = _.intersectionWith(this.chekedFields, this.fieldList[index].fields, _.isEqual);
          this.fieldList[index].isIndeterminate = hasIntersection.length === this.fieldList[index].fields.length
        }
        // this.choosedArry = this.choosedArry.filter(it => {
        //   return it.name !== item.name
        // })
      } else {
        this.chekedFields = [item, ...this.chekedFields]
        this.choosedArry.push(item)
        if (!this.showSearchField) {
          const hasIntersection = _.intersectionWith(this.chekedFields, this.fieldList[index].fields, _.isEqual);
          this.fieldList[index].isIndeterminate = hasIntersection.length === this.fieldList[index].fields.length
        }
      }
    },
    showDialog (mysqlData, type) {
      this.show = true;
      this.fieldsType = type
      this.$nextTick(() => {
        mysqlData.fields.numericalValue.forEach(it => {
          switch (it.type) {
            case 'date' :
              it.imgSrc = require('../../../../public/img/icon/type-date.png')
              this.imgSrc = require('../../../../public/img/icon/type-date.png')
              break
            case 'string' :
              it.imgSrc = require('../../../../public/img/icon/type-string.png')
              this.imgSrc = require('../../../../public/img/icon/type-string.png')
              break
            case 'number' :
              it.imgSrc = require('../../../../public/img/icon/type-number.png')
              this.imgSrc = require('../../../../public/img/icon/type-number.png')
              break
          }
        })
        this.getFieldList(mysqlData.tbName)
        // this.chekedFields = mysqlData.fields.numericalValue || [];
        const dmcDataNum = _.cloneDeep(mysqlData.fields.numericalValue)
        this.choosedArry = dmcDataNum || []
        this.chekedFieldList = this.chekedFields.map(item => {
          return item.name
        })
      })
    },
    selectDimension (dmcData, type) { // 选择维度字段
      this.show = true;
      this.fieldsType = type
      this.$nextTick(() => {
        dmcData.fields.dimension.forEach(it => {
          switch (it.type) {
            case 'date' :
              it.imgSrc = require('../../../../public/img/icon/type-date.png')
              this.imgSrc = require('../../../../public/img/icon/type-date.png')
              break
            case 'string' :
              it.imgSrc = require('../../../../public/img/icon/type-string.png')
              this.imgSrc = require('../../../../public/img/icon/type-string.png')
              break
            case 'number' :
              it.imgSrc = require('../../../../public/img/icon/type-number.png')
              this.imgSrc = require('../../../../public/img/icon/type-number.png')
              break
          }
        })
        this.getFieldList(dmcData.tbName);
        this.chekedFields = dmcData.fields.dimension || [];
        const dmcDataDim = _.cloneDeep(dmcData.fields.dimension)
        this.choosedArry = dmcDataDim || []
        this.chekedFieldList = this.chekedFields.map(item => {
          return item.original_name
        })
      })
    },
    removeLastTwoElements (arr1, arr2) {
      if (arr1.length < 2 || arr2.length === 0) {
        return;
      }
      for (let i = arr1.length - 1; i >= 0; i--) {
        if (arr1[i].name !== arr2[0].name) {
          break;
        }
        arr1.splice(i, 2);
      }
      this.choosedArry = arr1
    },
    resetParams () {
      this.fieldList = [];
      this.chekedFields = []
      this.chekedFieldList = []
      this.filterText = ''
    },
    closeDialog () {
      this.show = false;
      this.resetParams()
      this.showSearchField = false
    },
    clear_all () {
      this.choosedArry = []
      this.chekedFieldList = []
      this.chekedFields = []
      this.fieldList.forEach(it => {
        it.isIndeterminate = false
      })
    },
    submit () {
      this.$emit('update', this.choosedArry);
      this.show = false;
      this.resetParams()
      this.showSearchField = false
    }
  }
}
</script>
  <style lang="scss" scoped>
  .imgStyle {
    vertical-align: -0.15em;
    margin-right: 6px;
  }
  .choosed_content {
    margin: 10px 0 0;
    height: 32px;
    line-height: 32px;
  }
  .chossed_content_name {
    color: #ffffff;
    font-size: 14px;
    font-weight: 400;
  }
  .choosed_clear {
    cursor: pointer;
    margin-left: 55%;
    color: #3D85FF;
    font-size: 14px;
    font-weight: 600;
  }
  .choosed_number {
    color: rgba(255,255,255,0.7);
    display: inline-block;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    border-radius: 4px;
    background: rgba(204,219,255,0.1);
    margin-left: 8px;
    font-size: 12px;
    font-weight: 400;
  }
  .choosed {
    min-height: 150px;
    max-height: 519px;
    overflow: auto;
    padding: 18px;
    margin-left: 20px;
    border-radius: 8px;
    width: 35%;
    background: rgba(204,219,255,0.1);
  }
  .header {
    color: rgba(255,255,255,1);
    font-size: 14px;
    font-weight: 600;
  }
  .hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    ::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
    }
  }
  // ::v-deep .el-input {
  //   width: 65%;
  // }
  .bar {
    margin-top: 13px;
    border-top: 1px solid rgba(204,219,255,0.16);
  }
  .create-datasource {
    .filter-tree{
      margin-top: 34px;
    }
    .check-row {
      // padding: 0px 0px 16px 25px;
      // background: #293246;
      border-radius: 8px;
      max-height: 450px;
      overflow: auto;
    }
    .check-title{
      margin-top: 20px;
      ::v-deep {
        .el-checkbox{
          margin-top: 5px;
          color: rgba(255, 255, 255, 0.7);
        }
        .el-checkbox__inner{
          border: 1px solid rgba(86, 98, 118, 0.64);
          border-radius: 3px;
        }
      }
    }
    .check-group{
      width: 550px;
      margin-bottom: 24px;
      // margin-top: 20px;
      ::v-deep {
        .el-checkbox{
          width: 150px;
          margin-top: 16px;
          line-height: 24px;
          color: rgba(255, 255, 255, 0.7);
          overflow: hidden;
        }
        .el-checkbox__inner{
          border: 1px solid rgba(86, 98, 118, 0.64);
          border-radius: 3px;
        }
      }
    }
    ::v-deep {
      .el-form-item__label {
        color: #fafafa;
        font-size: 12px;
      }
      .el-upload-dragger {
        width: 300px;
        height: 140px;
        border-radius: 0;
        background-color: #181b24;
        border: 1px solid #393b4a;
        .el-upload__text {
          font-size: 12px;
        }
      }
      .el-upload-dragger .el-icon-upload {
        font-size: 50px;
        margin: 20px 0 16px;
      }
    }
  }
  </style>
