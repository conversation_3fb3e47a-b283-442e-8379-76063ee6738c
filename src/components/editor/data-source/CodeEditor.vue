<template>
  <div class="code-editor">
    <codemirror v-model="code" v-on="$listeners" :options="cmOptions" ref="jsonEditor" />
  </div>
</template>
<script>
import { codemirror } from 'vue-codemirror';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/base16-dark.css';
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/addon/hint/javascript-hint.js'
import 'codemirror/mode/sql/sql.js';
// 自动补全
import 'codemirror/addon/hint/show-hint.js'
import 'codemirror/addon/hint/anyword-hint.js'
import 'codemirror/addon/hint/show-hint.css'
// 行注释
import 'codemirror/addon/comment/comment.js'
// 格式化
// import 'codemirror/addon/format/format.js'
// 括号匹配
import 'codemirror/addon/edit/matchbrackets.js'
// 代码折叠
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/fold/comment-fold.js'
// 代码检查
import 'codemirror/addon/lint/lint.css'
import 'codemirror/addon/lint/lint.js'
import 'codemirror/addon/lint/json-lint.js'
// 语法检查
import 'codemirror/addon/lint/javascript-lint.js'

// 导入全局hint
import { JSHINT } from 'jshint'

window.JSHINT = JSHINT

// eslint-disable-next-line
require('script-loader!jsonlint')
export default {
  name: 'CodeEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    options: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  components: {
    codemirror
  },
  data () {
    return {}
  },
  watch: {
    value (val) {}
  },
  computed: {
    code: {
      get: function () {
        return this.value
      },
      set: function (val) {
        this.$emit('input', val);
      }
    },
    cmOptions () {
      return {
        tabSize: 2,
        mode: 'text/javascript',
        theme: 'base16-dark',
        lineNumbers: true,
        line: true,
        lineWrapping: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
        indentUnit: 2, // 缩进单位，默认2
        smartIndent: true,
        matchBrackets: true,
        lint: {
          esversion: 2021
        },
        ...this.options
      }
    },
    jsonEditor () {
      return this.$refs.jsonEditor?.codemirror
    },
    codeMirror () {
      return this.$refs.jsonEditor
    }
  },
  mounted () {
    setTimeout(() => {
      this.jsonEditor && this.jsonEditor.refresh()
    }, 2500)
  },
  methods: {
    setValue (val) {
      this.jsonEditor.setValue(JSON.stringify(JSON.parse(val), null, 2));
    }
  }
};
</script>

<style lang="scss" scoped>
.code-editor {
  border: 1px solid #393b4a;
}
</style>
