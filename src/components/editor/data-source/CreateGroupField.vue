<template>
  <div>
    <el-dialog class="create-datasource" :visible.sync="show" title="分组字段" append-to-body width="600px" :close-on-click-modal="false" @close="closeDialog" top="0">
     <el-form label-position="right" :rules="rules"  ref="groupField" size="mini" :model="groupForm" label-width="88px" label-suffix="：">
      <el-row>
          <el-col :span="24">
              <el-form-item label="字段名称" prop="name" label-width="100px">
                  <el-input v-model="groupForm.name" size="mini"></el-input>
              </el-form-item>
          </el-col>
          <el-col :span="12">
                <el-form-item label="分组方式" prop="param.type" label-width="100px">
                  <el-select v-model="groupForm.param.type" size="mini" style="width:100%">
                      <el-option v-for="opt in groupType" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                  </el-select>
              </el-form-item>
          </el-col>
            <el-col :span="12">
              <el-form-item label="分组字段" prop="param.fid" label-width="100px">
                  <el-select v-model="groupForm.param.fid" size="mini" style="width:100%;" @change="changeField">
                      <el-option v-for="item in fieldList" :key="item.fid" :value="item.fid" :label="item.name"></el-option>
                  </el-select>
              </el-form-item>
          </el-col>
      </el-row>
       <el-row class= "datesource-table" >
          <el-col :span="8" class="table-left" >
              <div class="table-left-title">
                <span>分组</span>
                <i class="el-icon-plus" @click="addGroups"></i>
              </div>
              <div class="field-ul">
                  <div class="field-li" :class="{activeLi: activeIndex === -1}" @click="changeIndex(-1)">{{groupForm.param.default}}</div>
                  <div class="field-li" :class="{activeLi: activeIndex === index}" v-for="(item, index) in groupForm.param.groups" :key="item.index" >
                    <el-col :span="20">
                      <div @click="changeIndex(index)">
                        {{item.name}}
                      </div>
                    </el-col>
                    <el-col :span="4" v-show="index !== 0">
                      <i class="el-icon-delete" @click="deleteGroups(index)"></i>
                    </el-col>
                  </div>
              </div>
          </el-col>
          <el-col :span="16" class="table-right">
               <el-row>
                <div v-if="activeIndex === -1">
                  <el-form-item label="分组名称">
                    <el-input v-model="groupForm.param.default" size="mini"></el-input>
                  </el-form-item>
                </div>
                <div v-else class="table-right-heght">
                  <el-form-item label="分组名称">
                    <el-input v-model="groupForm.param.groups[activeIndex].name" size="mini"></el-input>
                  </el-form-item>
                  <el-form-item label="分组条件" v-if="groupForm.param.groups[activeIndex].conditions.length>1" >
                      <el-select v-model="groupForm.param.groups[activeIndex].logic" size="mini" style="width:100%;">
                          <el-option v-for="opt in logic" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                      </el-select>
                  </el-form-item>
                  <div v-for="(item,index) in groupForm.param.groups[activeIndex].conditions" :key="index">
                    <el-col :span="11">
                      <el-form-item label="" label-width="0px" >
                          <el-select v-model="item.operator" size="mini" style="width:100%;">
                              <el-option v-for="opt in operation" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                          </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="1">
                      <el-form-item label="" label-width="0px"  style="width:100%;">
                          <el-input v-model.trim="item.value" size="mini"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="1" class="delete-style" v-show="index !== 0">
                      <i class="el-icon-delete" @click="deleteConditions(index)"></i>
                    </el-col>
                  </div>
                  <el-col :span="11">
                    <div class="table-right-title" @click="addConditions">
                      <i class="el-icon-plus"></i>
                      <span>添加条件</span>
                    </div>
                  </el-col>
                </div>
               </el-row>
          </el-col>
       </el-row>
      </el-form>
      <div slot="footer">
        <el-button type="light-blue" size="medium" @click="submit" :loading="loading">确定</el-button>
        <el-button type="text" size="medium" @click="closeDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getFieldList } from '@/api/datastorage';
import { createCalculatefield } from '@/api/calculatefield'
import { mapState } from 'vuex';
export default {
  name: 'CreateDataSource', // 创建数据源
  props: {},
  data () {
    return {
      activeIndex: 0,
      filterText: '',
      show: false,
      title: '选择工作表',
      groupForm: {
        fid: '',
        name: '',
        formula: '',
        data_type: 'string',
        field_type: 2,
        param: {
          default: '未分组',
          type: 'condition',
          fid: '',
          groups: [{
            name: '分组1',
            logic: '',
            conditions: [{
              operator: 3,
              value: ''
            }]
          }]
        }
      },
      rules: {
        'param.fid': [
          { required: true, message: '分组字段必填', trigger: 'change' }
        ],
        name: [
          { required: true, message: '字段名称必填', trigger: 'change' }
        ]
      },
      groupType: [
        { value: 'condition', label: '按条件' }
      ],
      operation: [
        { value: 3, label: '等于' }
      ],
      operation1: [
        { value: 1, label: '大于' },
        { value: 2, label: '小于' },
        { value: 3, label: '等于' },
        { value: 4, label: '大于等于' },
        { value: 5, label: '小于等于' }
      ],
      operation2: [
        { value: 6, label: '包含' },
        { value: 7, label: '不包含' },
        { value: 3, label: '等于' }
      ],
      logic: [
        { value: 'and', label: '满足所有条件' },
        { value: 'or', label: '满足任一条件' }
      ],
      loading: false,
      loading2: false,
      fieldList: [],
      tbId: ''
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    changeField (id) {
      const fieldData = this.fieldList.filter(item => {
        return item.fid === id
      })
      if (fieldData[0].data_type === 'number') {
        this.operation = this.operation1
      }
      if (fieldData[0].data_type === 'string') {
        this.operation = this.operation2
      }
      this.groupForm.param = {
        default: '未分组',
        type: 'condition',
        fid: id,
        groups: [{
          name: '分组1',
          logic: '',
          conditions: [{
            operator: '',
            value: ''
          }]
        }]
      }
    },
    async getFieldList (tbid) {
      const data = {
        type: 'dmc',
        tbid,
        needCalculate: 0,
        workspaceId: this.screenInfo.workspaceId
      }
      const res = await getFieldList(data);
      if (res && res.success) {
        res.data.forEach(item => {
          this.fieldList = [...this.fieldList, ...item.fields]
        });
      }
    },
    validateGroups () {
      const groupsIndex = this.groupForm.param.groups.length - 1
      const lastIndex = this.groupForm.param.groups[groupsIndex].conditions.length - 1
      if (!this.groupForm.param.fid) {
        this.$message.error('分组字段不能为空')
        return
      }
      if (!this.groupForm.param.groups[groupsIndex].conditions[lastIndex].value) {
        this.$message.error('条件值不能为空')
        return
      }
      if (!this.groupForm.param.groups[groupsIndex].name) {
        this.$message.error('分组名称不能为空')
      }
    },
    addGroups () {
      this.validateGroups()
      this.groupForm.param.groups.push({
        name: `分组${this.groupForm.param.groups.length + 1}`,
        logic: '',
        conditions: [{
          operator: 3,
          value: ''
        }]
      })
    },
    deleteGroups (index) {
      this.activeIndex = -1
      this.groupForm.param.groups.splice(index, 1)
    },
    deleteConditions (index) {
      this.groupForm.param.groups[this.activeIndex].conditions.splice(index, 1)
    },
    addConditions () {
      const lastIndex = this.groupForm.param.groups[this.activeIndex].conditions.length - 1
      if (!this.groupForm.param.fid) {
        this.$message.error('分组字段不能为空')
        return
      }
      if (!this.groupForm.param.groups[this.activeIndex].conditions[lastIndex].value) {
        this.$message.error('条件值不能为空')
        return
      }
      this.groupForm.param.groups[this.activeIndex].conditions.push({
        operator: 3, // 大于、小于、等于、大于等于、小于等于、包含、不包含
        value: ''
      })
    },
    changeIndex (index) {
      this.activeIndex = index
    },
    showDialog (data, item) {
      this.show = true;
      this.getFieldList(data.tbId)
      this.groupForm = _.cloneDeep(item) || this.groupForm
      this.tbId = data.tbId
    },
    closeDialog () {
      this.show = false;
      this.fieldList = []
      this.groupForm = {
        fid: '',
        name: '',
        formula: '',
        data_type: 'string',
        field_type: 2,
        param: {
          default: '未分组',
          type: 'condition',
          fid: '',
          groups: [{
            name: '分组1',
            logic: '',
            conditions: [{
              operator: '',
              value: ''
            }]
          }]
        }
      }
    },
    async createCalculatefield () {
      const data = {
        workspaceId: this.screenInfo.workspaceId,
        tbId: this.tbId,
        field: this.groupForm
      }
      const res = await createCalculatefield(data);
      if (res && res.success) {
        this.closeDialog()
        this.calfieldlist = res.data
        this.$emit('update', this.calfieldlist);
      } else {
        this.$message.error(res.message)
      }
    },
    submit () {
      this.validateGroups()
      this.$refs.groupField.validate(async valid => {
        if (valid) {
          this.createCalculatefield()
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.create-datasource {
     i {
      cursor: pointer;
     }
    .field-ul {
      height: 250px;
      overflow-y: auto;
      color: rgba(255, 255, 255, 0.7);
      .field-li {
        height: 36px;
        line-height: 36px;
        margin-top: 8px;
        padding-left: 8px;
      }
      .field-li:hover {
        background: rgba(204, 219, 255, 0.06);
      }
      .activeLi {
        background: rgba(204, 219, 255, 0.06);
      }
    }
    .filter-tree{
        margin-top: 34px;
    }
    .datesource-table{
        background: rgba(66, 74, 90, 0.3);
        border-radius: 4px;
        padding: 15px;
        .table-left {
          padding-right: 15px;
          .table-left-title {
            margin-left: -5px;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.7);
            i {
              float: right;
              color: #1F71FF;
            }
          }
        }
        .table-right {
          padding-left: 15px;
          border-left:1px solid rgba(63, 71, 87, 0.5);
          .table-right-heght {
            height: 250px;
            overflow-y: auto;
          }
          .table-right-title {
            cursor: pointer;
            margin-left: 0px;
            margin-bottom: 8px;
            color: #1F71FF;
            .el-icon-plus {
              color: #1F71FF;
            }
          }
          .delete-style {
            cursor: pointer;
            height: 30px;
            line-height: 30px;
          }
        }
    }

  ::v-deep {
    .el-form-item__label {
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
    }
  }
}
</style>
