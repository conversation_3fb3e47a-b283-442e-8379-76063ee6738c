<template>
  <div class="data-select-wrapper" :class="{show}">
    <el-scrollbar style="height: 100%">
      <div class="select-header">
        <span class="title">数据响应结果</span>
        <i class="el-icon-close select-icon" @click="close"></i>
      </div>
      <div class="select-content">
        <p class="data-select-switch">
          <span class="label">开启数据筛选：</span>
          <el-switch v-model="selectEnable"></el-switch>
        </p>
        <div class="conditional-box select-box">
          <span class="label">条件筛选：</span>
          <div class="card" :class="{collapse: collapseCondition}">
            <div class="toolbar right">
              <span class="toolbar-btns">
                <i class="el-icon-circle-plus-outline" @click="addTab('condition')"></i>
                <i class="el-icon-delete" @click="removeConTab"></i>
                <i class="el-icon-arrow-down" :class="{rotate: collapseCondition}" @click="collapseCondition = !collapseCondition"></i>
              </span>
            </div>
            <div class="main">
              <el-tabs v-model="activeConTab" type="card">
                <el-tab-pane
                  v-for="item in conTabsConfig"
                  :key="item.name"
                  :label="item.title"
                  :name="item.name"
                >
                  <div class="form">
                    <div class="field-container">
                      <span class="label mr5" :class="{error: !item.field && needConValidTab}">字段名称</span>
                      <el-select v-if="type === 'dmc'" placeholder="字段名称" size="mini" class="full-select" v-model="item.field" @change="changeField('condition')">
                        <el-option v-for="field in allFields" :key="field.fid" :value="field.fid" :label="field.name">
                          <span class="mr5">
                            <hz-icon :name="getFieldIcon(field.data_type)" v-if="field.data_type !== 'date'"></hz-icon>
                            <i class="el-icon-date icon" v-else></i>
                          </span>
                          <span>{{field.name}}</span>
                        </el-option>
                      </el-select>
                      <el-select v-else placeholder="字段名称" size="mini" class="full-select" v-model="item.field" @change="changeField('condition')">
                        <el-option v-for="field in allFields" :key="field.name" :value="field.name" :label="field.name">
                          <span class="mr5">
                            <hz-icon :name="getFieldIcon(field.type)" v-if="field.type !== 'date'"></hz-icon>
                            <i class="el-icon-date icon" v-else></i>
                          </span>
                          <span>{{field.name}}</span>
                        </el-option>
                      </el-select>
                    </div>
                    <div class="field-container">
                      <span class="label mr5" :class="{error: !item.condition && needConValidTab}">筛选类型</span>
                      <el-select size="mini" class="mini-select" v-model="item.condition" placeholder="筛选类型" @change="changeCondition(item)">
                        <el-option v-for="condition in getCondition(item.field)" :key="condition.text" :value="condition.value" :label="condition.text"></el-option>
                      </el-select>
                      <div class="range" v-if="item.condition === 'range'">
                        <div class="start-wrapper range-select">
                          <el-input placeholder="请输入数值" @change="saveSelect" v-model="item.rangeStart" size="mini" :class="{'error-input': (!item.rangeStart.toString() && needConValidTab) || !validate(item).start}" />
                          <p class="error-info" v-if="item.errorInfo1"> * {{item.errorInfo1}}</p>
                        </div>
                        <span class="label ml5 mr5">~</span>
                        <div class="end-wrapper range-select">
                          <el-input placeholder="请输入数值" @change="saveSelect" v-model="item.rangeEnd" size="mini" :class="{'error-input': (!item.rangeEnd.toString() && needConValidTab) || !validate(item).end}" />
                          <p class="error-info" v-if="item.errorInfo2"> * {{item.errorInfo2}}</p>
                        </div>
                      </div>
                      <div class="range" v-else-if="getType(item.field) === 'string' && noInput.indexOf(item.condition) === -1">
                        <el-input @change="saveSelect" v-model="item.detail" class="range" size="mini" placeholder="请输入" :class="{'error-input': !item.detail && needConValidTab}"></el-input>
                      </div>
                      <div class="range" v-else-if="getType(item.field) === 'number' && noInput.indexOf(item.condition) === -1">
                        <el-input @change="saveSelect" v-model="item.detail" class="range" size="mini" placeholder="请输入" :class="{'error-input': (!item.detail.toString() && needConValidTab) || !validate(item, 'common')}"></el-input>
                        <p class="error-info" v-if="item.errorInfo1"> * {{item.errorInfo1}}</p>
                      </div>
                      <div class="range" v-show="item.condition === 'dateRange'">
                        <el-date-picker
                          style="width: 100%"
                          v-model="item.detail"
                          type="daterange"
                          size="mini"
                          range-separator="~"
                          :class="{error: !item.detail && needConValidTab}"
                          @change="saveSelect"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
                        </el-date-picker>
                      </div>
                      <div class="range" v-show="getType(item.field) === 'date' && item.condition !== 'dateRange' && noInput.indexOf(item.condition) === -1">
                        <el-date-picker @change="saveSelect" value-format="yyyy-MM-dd" v-model="item.detail" size="mini" style="width: 100%;" :class="{error: !item.detail && needConValidTab}"></el-date-picker>
                      </div>
                    </div>
                    <div class="field-container">
                      <span class="label mr5">与或关系</span>
                      <el-select size="mini" class="full-select" v-model="item.composeType" @change="saveSelect">
                        <el-option v-for="opt in composeTypes" :key="opt.name" :value="opt.value" :label="opt.name"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
              <div class="empty-info" v-if="!conTabsConfig.length">暂无条件</div>
            </div>
          </div>
        </div>
        <div class="sort-box select-box">
          <span class="label">排序筛选：</span>
          <div class="card" :class="{collapse: collapseSort}">
            <div class="toolbar right">
              <span class="toolbar-btns">
                <i class="el-icon-circle-plus-outline" @click="addTab('sort')"></i>
                <i class="el-icon-delete" @click="removeSortTab"></i>
                <i class="el-icon-arrow-down" :class="{rotate: collapseSort}" @click="collapseSort = !collapseSort"></i>
              </span>
            </div>
            <div class="main">
              <el-tabs v-model="activeSortTab" type="card">
                <el-tab-pane
                  v-for="item in sortTabsConfig"
                  :key="item.name"
                  :label="item.title"
                  :name="item.name"
                >
                  <div class="form">
                    <div class="field-container">
                      <span class="label mr5" :class="{error: !item.field && needSortValidTab}">字段名称</span>
                      <el-select v-if="type === 'dmc'" placeholder="字段名称" size="mini" class="full-select" v-model="item.field" @change="changeField('sort')">
                        <el-option v-for="field in dimension" :key="field.fid" :value="field.fid" :label="field.name" :disabled="field.fid !== item.field && selectedSortFields.indexOf(field.fid) > -1">
                          <span class="mr5">
                            <hz-icon :name="getFieldIcon(field.data_type)" v-if="field.data_type !== 'date'"></hz-icon>
                            <i class="el-icon-date icon" v-else></i>
                          </span>
                          <span>{{field.name}}</span>
                        </el-option>
                      </el-select>
                      <el-select v-else placeholder="字段名称" size="mini" class="full-select" v-model="item.field" @change="changeField('sort')">
                        <el-option v-for="field in dimension" :key="field.name" :value="field.name" :label="field.name" :disabled="field.name !== item.field && selectedSortFields.indexOf(field.name) > -1">
                          <span class="mr5">
                            <hz-icon :name="getFieldIcon(field.type)" v-if="field.type !== 'date'"></hz-icon>
                            <i class="el-icon-date icon" v-else></i>
                          </span>
                          <span>{{field.name}}</span>
                        </el-option>
                      </el-select>
                    </div>
                    <div class="field-container">
                      <span class="label mr5" :class="{error: !item.sortType && needSortValidTab}">排序</span>
                      <el-select placeholder="排序" size="mini" class="full-select" v-model="item.sortType" @change="saveSelect">
                        <el-option v-for="sort in sortType" :key="sort.text" :value="sort.value" :label="sort.text"></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
              <div class="empty-info" v-if="!sortTabsConfig.length">暂无排序</div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>

  </div>
</template>
<script>
import { numberToChinese, dateFormat } from '@/utils/base';
import { getFieldList } from '@/api/datastorage';
import { mapState, mapGetters } from 'vuex';
const numberCondition = [{
  text: '等于',
  value: 'equal'
}, {
  text: '不等于',
  value: 'unequal'
}, {
  text: '大于',
  value: 'greater'
}, {
  text: '小于',
  value: 'less'
}, {
  text: '大于等于',
  value: 'greaterOrEqual'
}, {
  text: '小于等于',
  value: 'lessOrEqual'
}, {
  text: '区间',
  value: 'range'
}, {
  text: '不为空',
  value: 'notNull'
}, {
  text: '为空',
  value: 'null'
}];
const stringCondition = [{
  text: '等于',
  value: 'equal'
}, {
  text: '不等于',
  value: 'unequal'
}, {
  text: '包含',
  value: 'contain'
}, {
  text: '不包含',
  value: 'notContain'
}, {
  text: '开头匹配',
  value: 'matchOnStart'
}, {
  text: '结尾匹配',
  value: 'matchOnEnd'
}, {
  text: '不为空',
  value: 'notNull'
}, {
  text: '为空',
  value: 'null'
}];
const dateCondition = [{
  text: '等于',
  value: 'equal'
}, {
  text: '不等于',
  value: 'unequal'
}, {
  text: '大于',
  value: 'greater'
}, {
  text: '小于',
  value: 'less'
}, {
  text: '大于等于',
  value: 'greaterOrEqual'
}, {
  text: '小于等于',
  value: 'lessOrEqual'
}, {
  text: '日期范围',
  value: 'dateRange'
}, { // 相对日期开始
  text: '今天',
  value: 'today'
}, {
  text: '昨天',
  value: 'lastday'
}, {
  text: '最近7天',
  value: 'last7Days'
}, {
  text: '最近30天',
  value: 'last30Days'
}, {
  text: '最近90天',
  value: 'last90Days'
}, {
  text: '最近一年',
  value: 'lastYear'
}, { // 相对日期结束
  text: '不为空',
  value: 'notNull'
}, {
  text: '为空',
  value: 'null'
}];
const composeTypes = [{
  name: '与',
  value: 'and'
}, {
  name: '或',
  value: 'or'
}];
export default {
  name: 'DataSelect',
  props: {
    type: {
      type: String,
      default: 'dmc'
    }
  },
  data () {
    return {
      show: false,
      fieldList: [],
      collapseCondition: false,
      collapseSort: false,
      activeConTab: '1',
      activeSortTab: '1',
      conTabsConfig: [],
      sortTabsConfig: [],
      numberCondition,
      stringCondition,
      dateCondition,
      composeTypes,
      needConValidTab: false,
      needSortValidTab: false,
      sortType: [{
        text: '升序',
        value: 'asc'
      }, {
        text: '降序',
        value: 'desc'
      }],
      noInput: ['null', 'notNull', 'today', 'lastday', 'last7Days', 'last30Days', 'last90Days', 'lastYear'],
      conditionType: 'and'
    }
  },
  created () {
    const where = this.currentCom.dataConfig.dataResponse.source[this.type] ? (this.currentCom.dataConfig.dataResponse.source[this.type].data || {}).where : null;
    this.init(where);
  },
  computed: {
    ...mapState('editor', ['screenInfo']),
    ...mapGetters('editor', ['currentCom']),
    selectEnable: { // 是否启用数据筛选
      get: function () {
        return this.currentCom.dataConfig.dataResponse.source[this.type] && this.currentCom.dataConfig.dataResponse.source[this.type].data.where && this.currentCom.dataConfig.dataResponse.source[this.type].data.where.enable;
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: `dataConfig.dataResponse.source.${this.type}.data.where.enable`, value: val }
          ]
        }).then(() => {
          this.$emit('update', {
            path: 'where.enable',
            value: val
          });
        });
      }
    },
    allFields () {
      let fields = [];
      this.fieldList.forEach(list => {
        list.fields && (fields = fields.concat(list.fields));
      })
      return fields;
    },
    dimension () {
      const data = this.currentCom.dataConfig.dataResponse.source[this.type];
      if (!data) return [];
      return data.data.fields.dimension;
    },
    selectedSortFields () {
      return this.sortTabsConfig.map(item => item.field);
    },
    validate () {
      return (item, type = 'range') => {
        // 验证数值类型的输入是否有效
        // if (type === 'range') {
        //   const result = {};
        //   const rangeStart = parseFloat(item.rangeStart);
        //   const rangeEnd = parseFloat(item.rangeEnd);
        //   if (isNaN(rangeStart)) {
        //     item.errorInfo1 = '请输入数字！';
        //     result.start = false;
        //   } else if (rangeStart > rangeEnd) {
        //     item.errorInfo1 = '区间范围有误！';
        //     item.errorInfo2 = '';
        //     result.start = false;
        //     result.end = false;
        //     item.valid = result.start && result.end;
        //     return result;
        //   } else {
        //     item.errorInfo1 = '';
        //     result.start = true;
        //   }
        //   if (isNaN(rangeEnd)) {
        //     item.errorInfo2 = '请输入数字！';
        //     result.end = false;
        //   } else {
        //     item.errorInfo2 = '';
        //     result.end = true;
        //   }
        //   item.valid = result.start && result.end;
        //   return result;
        // } else if (type === 'common') {
        //   let result;
        //   const validVal = parseFloat(item.detail);
        //   if (isNaN(validVal)) {
        //     item.errorInfo1 = '请输入数字！';
        //     result = false;
        //   } else {
        //     item.errorInfo1 = '';
        //     result = true;
        //   }
        //   item.valid = result;
        //   return result;
        // }
        return true
      }
    }
  },
  methods: {
    init (data) {
      if (data) {
        this.conTabsConfig = data.whereCondition.map((item, index) => {
          const obj = {
            name: index + 1 + '',
            title: '条件' + numberToChinese(index + 1),
            field: item.fid || item.field,
            condition: item.compare,
            composeType: item.composeType || 'and',
            detail: '',
            rangeStart: '',
            rangeEnd: '',
            valid: false,
            errorInfo1: '',
            errorInfo2: ''
          }
          if (item.compare === 'range') {
            obj.rangeStart = item.compareValue[0] || '';
            obj.rangeEnd = item.compareValue[1] || '';
          } else if (item.compare === 'dateRange') {
            obj.detail = item.compareValue || '';
          } else {
            obj.detail = item.compareValue[0] || '';
          }
          return obj;
        });
        this.sortTabsConfig = data.orderCondition.map((item, index) => {
          return {
            name: index + 1 + '',
            title: '排序' + numberToChinese(index + 1),
            field: item.fid || item.field,
            sortType: item.orderBy
          }
        });
      }
    },
    getType (field) {
      if (!field) return 'string';
      let type = (this.allFields.find(item => item.fid === field) || {}).data_type;
      if (this.type !== 'dmc') {
        type = (this.allFields.find(item => item.name === field) || {}).type;
      }
      return type;
    },
    getFieldIcon (type) {
      if (type === 'string') {
        return 'field-string';
      } else {
        return 'field-number';
      }
    },
    saveSelect: _.debounce(function () {
      const where = {};
      where.enable = this.selectEnable;
      where.whereCondition = this.conTabsConfig.filter(tab => {
        if (tab.field && tab.condition && (this.noInput.indexOf(tab.condition) > -1 || tab.detail !== '' || (tab.rangeStart && tab.rangeEnd))) return true;
        return false;
      }).map(item => {
        let fieldOpts = this.allFields.find(field => field.fid === item.field) || {};
        if (this.type !== 'dmc') {
          fieldOpts = this.allFields.find(field => field.name === item.field) || {};
        }
        if (item.condition === 'range') {
          item.rangeStart = item.rangeStart || '';
          // item.rangeStart = parseFloat(item.rangeStart) || '';
          // item.rangeEnd = parseFloat(item.rangeEnd) || '';
          item.rangeEnd = item.rangeEnd || '';
        } else if (item.condition === 'dateRange') {
          item.detail = [dateFormat(item.detail[0], 'yyyy-MM-dd HH:mm:ss'), dateFormat(item.detail[1], 'yyyy-MM-dd HH:mm:ss').replace('00:00:00', '23:59:59')]
        } else if (fieldOpts.data_type === 'number') {
          item.detail = item.detail ? item.detail : '';
          // item.detail = (isNaN(parseFloat(item.detail)) ? '' : parseFloat(item.detail));
        }
        const obj = {
          field: fieldOpts.name,
          type: fieldOpts.data_type,
          fid: item.field,
          composeType: item.composeType,
          compare: item.condition,
          compareValue: item.detail !== '' ? (Array.isArray(item.detail) ? item.detail : [item.detail]) : (item.rangeStart && item.rangeEnd ? [item.rangeStart, item.rangeEnd] : [])
        };
        if (this.type !== 'dmc') {
          obj.type = fieldOpts.type;
          delete obj.fid;
        }
        return obj;
      });
      where.orderCondition = this.sortTabsConfig.filter(tab => {
        return tab.field && tab.sortType;
      }).map(item => {
        let fieldOpts = this.dimension.find(field => field.fid === item.field) || {};
        if (this.type !== 'dmc') {
          fieldOpts = this.dimension.find(field => field.name === item.field) || {};
        }
        const obj = {
          field: fieldOpts.name,
          type: fieldOpts.data_type,
          calculation: fieldOpts.calculation || '',
          fid: item.field,
          orderBy: item.sortType
        };
        if (this.type !== 'dmc') {
          obj.type = fieldOpts.type;
          delete obj.fid;
        }
        return obj;
      });
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: `dataConfig.dataResponse.source.${this.type}.data.where`, value: where }
        ]
      }).then(() => {
        this.$emit('update', {
          path: 'where',
          value: where
        });
      });
    }, 1000),
    getActiveConTabData () {
      return this.conTabsConfig.find(config => config.name === this.activeConTab) || {};
    },
    getActiveSortTabData () {
      return this.sortTabsConfig.find(config => config.name === this.activeSortTab) || {};
    },
    getCondition (field) {
      const type = this.getType(field);
      switch (type) {
        case 'date':
          return this.dateCondition;
        case 'string':
          return this.stringCondition;
        case 'number':
          return this.numberCondition;
        default:
          return [];
      }
    },
    changeField (type) {
      this.reset(type);
    },
    changeCondition (item) {
      if (item) {
        item.detail = '';
        item.rangeStart = '';
        item.rangeEnd = '';
        item.errorInfo1 = '';
        item.errorInfo2 = '';
      }
      if (this.noInput.indexOf(item.condition) > -1) {
        this.saveSelect();
      }
    },
    reset (type) {
      if (type === 'condition') {
        const activeConTabData = this.getActiveConTabData();
        activeConTabData.condition = '';
        activeConTabData.composeType = 'and';
        activeConTabData.detail = '';
        activeConTabData.rangeStart = '';
        activeConTabData.rangeEnd = '';
        activeConTabData.valid = true;
        activeConTabData.errorInfo1 = '';
        activeConTabData.errorInfo2 = '';
      } else {
        const activeSortTabData = this.getActiveSortTabData();
        activeSortTabData.sortType = '';
      }
    },
    addTab (type) {
      if (type === 'condition') {
        this.needConValidTab = true;
        const lastTab = this.conTabsConfig[this.conTabsConfig.length - 1] || { name: '0' };
        if (this.conTabsConfig.length && (!lastTab.field || !lastTab.condition || (!lastTab.detail.toString() && !lastTab.rangeStart.toString() && !lastTab.rangeEnd.toString() && this.noInput.indexOf(lastTab.condition) === -1))) {
          this.activeConTab = lastTab.name;
          return false;
        }
        const num = parseInt(lastTab.name) + 1;
        const name = num + '';
        this.conTabsConfig.push({
          name,
          title: '条件' + numberToChinese(num),
          field: '',
          condition: '',
          composeType: 'and',
          detail: '',
          rangeStart: '',
          rangeEnd: '',
          valid: false,
          errorInfo1: '',
          errorInfo2: ''
        });
        this.activeConTab = name;
        this.needConValidTab = false;
      } else if (type === 'sort') {
        this.needSortValidTab = true;
        const lastTab = this.sortTabsConfig[this.sortTabsConfig.length - 1] || { name: '0' };
        if (this.sortTabsConfig.length && (!lastTab.field || !lastTab.sortType)) {
          this.activeSortTab = lastTab.name;
          return false;
        }
        const num = parseInt((this.sortTabsConfig[this.sortTabsConfig.length - 1] || { name: '0' }).name) + 1;
        const name = num + '';
        this.sortTabsConfig.push({
          name,
          title: '排序' + numberToChinese(num),
          field: '',
          sortType: ''
        });
        this.activeSortTab = name;
        this.needSortValidTab = false;
      }
    },
    close () {
      this.show = false;
    },
    open (data) {
      this.show = true;
      this.$nextTick(() => {
        this.getFieldList(data.tbId || data.tbName);
      })
    },
    async getFieldList (tb) {
      const data = {
        type: this.type,
        tbid: tb,
        needCalculate: 1,
        workspaceId: this.screenInfo.workspaceId
      }
      if (this.type === 'mysql') {
        data.tbName = tb;
        data.componentId = this.currentCom.id;
        delete data.tbid;
        delete data.needCalculate;
      }
      if (this.type === 'highgodb') {
        data.tbName = tb;
        data.componentId = this.currentCom.id;
        delete data.tbid;
        delete data.needCalculate;
      }
      if (this.type === 'dmdb') {
        data.tbName = tb;
        data.componentId = this.currentCom.id;
        delete data.tbid;
        delete data.needCalculate;
      }
      if (this.type === 'postgresql') {
        data.tbName = tb;
        data.componentId = this.currentCom.id;
        delete data.tbid;
        delete data.needCalculate;
      }
      if (this.type === 'oracle') {
        data.tbName = tb;
        data.componentId = this.currentCom.id;
        delete data.tbid;
        delete data.needCalculate;
      }
      if (this.type === 'excel') {
        data.tbName = tb;
        data.componentId = this.currentCom.id;
        delete data.tbid;
        delete data.needCalculate;
      }
      const res = await getFieldList(data);
      if (res && res.success) {
        this.fieldList = res.data
        if (this.type !== 'dmc') {
          this.fieldList.forEach(item => {
            item.fields = item.fields.map(field => {
              field.type = item.type;
              return field;
            })
          })
        }
      }
    },
    removeConTab () {
      const tab = this.activeConTab;
      const index = this.conTabsConfig.findIndex(item => item.name === tab);
      if (index > -1) {
        this.conTabsConfig.splice(index, 1);
        this.conTabsConfig.forEach((item, index) => {
          item.name = index + 1 + '';
          item.title = '条件' + numberToChinese(index + 1);
        })
        if (this.conTabsConfig.length) {
          this.activeConTab = (this.conTabsConfig[index - 1] || this.conTabsConfig[index]).name;
          // this.activeConTab = this.conTabsConfig[this.conTabsConfig.length - 1].name;
        }
        this.$nextTick(() => {
          this.saveSelect();
        });
      }
    },
    removeSortTab () {
      const tab = this.activeSortTab;
      const index = this.sortTabsConfig.findIndex(item => item.name === tab);
      if (index > -1) {
        this.sortTabsConfig.splice(index, 1);
        this.sortTabsConfig.forEach((item, index) => {
          item.name = index + 1 + '';
          item.title = '排序' + numberToChinese(index + 1);
        })
        if (this.sortTabsConfig.length) {
          this.activeSortTab = (this.sortTabsConfig[index - 1] || this.sortTabsConfig[index]).name;
        }
        this.$nextTick(() => {
          this.saveSelect();
        });
      }
    }
    // removeAll (type) {
    //   if (type === 'condition') {
    //     this.conTabsConfig = [];
    //   } else if (type === 'sort') {
    //     this.sortTabsConfig = [];
    //   }
    //   this.$nextTick(() => {
    //     this.saveSelect();
    //   });
    // }
  }
}
</script>
<style lang="scss" scoped>
  .data-select-wrapper {
    transition: right .5s ease-in-out;
    position: fixed;
    width: 630px;
    top: 0;
    bottom: 0;
    right: -630px;
    z-index: 999;
    padding-top: 70px;
    background-color: #1F2430;
    &.show {
      right: 0;
    }
    .select-header {
      height: 60px;
      color: rgba(255, 255, 255, 0.7);
      font-size: 18px;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        font-weight: 600;
      }
      .select-icon {
        cursor: pointer;
      }
    }
    .select-content {
      padding: 20px 27px;
      .data-select-switch {
        display: flex;
        align-items: center;
        // ::v-deep .el-switch .el-switch__core{
        //   background-color: #1F2430;
        // }
        // ::v-deep .el-switch.is-checked .el-switch__core{
        //   background-color: #409EFF;
        // }
      }
      .label {
        font-size: 16px;
        color: rgba(255, 255, 255, 1);
      }
      .select-box {
        display: flex;
        align-items: flex-start;
        padding: 25px 0;
        &.conditional-box {
          .card {
            height: 260px;
          }
        }
        .label {
          text-indent: 2em;
        }
        .card {
          flex: 1;
          height: 217px;
          padding: 0 15px;
          transition: height 0.5s;
          overflow: hidden;
          border: 1px solid #333F59;
          .toolbar {
            line-height: 48px;
            border-bottom: 1px solid #333F59;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            &.right {
              justify-content: flex-end;
            }
            .toolbar-btns {
              i {
                margin-left: 8px;
                cursor: pointer;
                transition: transform 0.3s ease-in-out;
                &.rotate {
                  transform: rotate(180deg);
                }
              }
            }
          }
          .main{
            padding-top: 20px;
            .empty-info {
              color: rgba(255, 255, 255, 0.7);
              font-size: 16px;
              text-align: center;
            }
            ::v-deep .el-tabs--card{
              &>.el-tabs__header {
                border-bottom-color: transparent;
                .el-tabs__nav {
                  border: none;
                  background-color: transparent;
                }
              }
              .el-tabs__item {
                height: 29px;
                line-height: 29px;
                font-size: 12px;
                padding-left: 10px;
                padding-right: 10px;
                border-radius: 8px 4px 0 0;
                border: 1px solid rgb(57, 59, 74);
                &:not(:first-child) {
                  margin-left: 2px;
                }
              }
              .is-active {
                background-color: #3D85FF;
                color: #fff;
                font-weight: 600;
              }
            }
            .field-container {
              margin-bottom: 15px;
              display: flex;
              align-items: center;
              .label {
                text-indent: 0em;
                font-size: 14px;
                font-weight: bold;
                display: inline-block;
                min-width: 70px;
                text-align: right;
                color: rgba(255, 255, 255, 0.7);
                &.error {
                  color: #F56C6C;
                }
              }
              .full-select {
                flex: 1;
              }
              .mini-select {
                width: 100px;
                flex-shrink: 0;
                margin-right: 5px;
              }
              .range {
                flex: 1;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                position: relative;
                .label {
                  text-indent: 0;
                  min-width: 0;
                }
                .range-select {
                  width: 50%;
                  position: relative;
                }
                .error-info {
                  color: #F56C6C;
                  position: absolute;
                  bottom: -16px;
                  left: 0;
                }
              }
              ::v-deep .el-range-input {
                background-color: transparent;
              }
              ::v-deep .el-range-separator {
                color: #606266;
                font-size: 18px;
              }
            }
          }
          &.collapse{
            height: 48px;
          }
        }
      }
    }
    .error-input {
      ::v-deep .el-input__inner {
        border-color: #F56C6C;
      }
    }
  }
  .mr5 {
    margin-right: 5px;
  }
  .ml5 {
    margin-left: 5px;
  }
  .error {
    ::v-deep &.el-input__inner{
      border-color: #F56C6C;
    }
    ::v-deep .el-input__inner{
      border-color: #F56C6C;
    }
  }
</style>
<style>
  .el-select-dropdown__item.selected .icon{
    color: #409EFF;
  }
</style>
