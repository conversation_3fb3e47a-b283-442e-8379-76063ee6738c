<!--
 * @Author: jiashuangxi
 * @Date: 2022-01-04 11:09:17
 * @LastEditors: jiashuangxi
 * @LastEditTime: 2022-01-11 17:42:44
 * @Describe:
-->
<template>
  <el-dialog
    append-to-body
    class="import-dialog"
    :visible.sync="show"
    :title="diag.title"
    :width="diag.widthSet"
    top="0"
    :close-on-click-modal="false"
    :before-close="close"
  >
    <div class="import-content">
      <div
        v-loading="treeLoading"
        element-loading-background="#1F2430"
        class="import-content__left"
      >
        <el-input
          placeholder="搜索"
          size="mini"
          @input="searchTree"
          v-model="filterText"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-tree
          ref="tree"
          class="filter-tree"
          :data="projTree"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ data }">
            <span class="tree-title ">
              <i
                v-if="data.attribute == 'dashboard' && data.category != 1"
                class="el-icon-document"
              ></i>
              <i v-else class="el-icon-folder-opened"></i>
              <span class="nowrap" :title="data.name">&nbsp;{{ data.name }}</span>
            </span>
          </span>
        </el-tree>
      </div>
      <!-- 选择图表类型 -->
      <div class="import-content__mid"  element-loading-background="#1F2430">
        <div class="mid-top">
          <p class="mid-top__title">选择图表类型</p>
          <el-input
            class="mid-top__input"
            placeholder="搜索"
            size="mini"
            @input="searchKind"
            v-model="filterKind"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <div>
          <div class="chart-list" v-if="chartsData && chartsData.length > 0">
            <div class="chart-nodata" v-if="this.chartsNewData.length == 0">
            此图表类型暂无
          </div>
            <div class="chart-list__item">
              <!-- :disabled="disableChart" -->
              <el-checkbox-group  v-model="chartList" :max="1"
                  @change="handleCheckedChange($event)"
                  style="display: flex;
                      flex-direction: row;
                      flex-wrap: wrap;
                      justify-content: space-between;"
                  >
                <el-checkbox
                  border
                  style="width:126px; height:40px; margin:10px; border-radius:8px; box-shadow: 3px 3px 5px #293246"
                  @change="handleChange($event, index, item)"
                  v-for="(item,index) in chartsNewData"
                  :key="item.ct_id"
                  :label="item"
                  :title="item.name"
                  ><span class="loading-mask"
                  v-if="activeIndex == index"
                  v-loading="listLoading"></span>
                  <hz-icon :name="item.type" class="hzSet"></hz-icon>&nbsp;<span class="textName">{{ item.name }}</span></el-checkbox
                >
              </el-checkbox-group>
            </div>
          </div>
          <div class="chart-nodata" v-else>无数据</div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button
        type="light-blue"
        size="medium"
        @click="submit"
        :loading="loading"
        >确定</el-button
      >
      <el-button type="text" size="medium" @click="closeDialog">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getImportTree, getImportDashboard, getCheckChartData } from '@/api/screen';
import { createDatastorage } from '@/api/datastorage';
import { mapState, mapGetters } from 'vuex';
export default {
  name: 'CreateDashBoard',
  components: {},
  props: { importDash: String },
  data () {
    return {
      // 选择图表是返回的index
      activeIndex: -1,
      // 树型加载
      treeLoading: false,
      listLoading: false,
      // 选择时-导入为默认为状态
      selectHidden: false,
      // 获取仪表盘树型-列表数据-选择操作
      disableChart: false,
      // 当前选择数据
      currentObj: {},
      // 导入为-列表数据
      selectKind: [],
      // 选择当前图表类型数据
      chartList: [],
      // 搜索数据
      filterText: '',
      // 搜索选择图表类型
      filterKind: '',
      ImportName: '',
      // 弹出框显示
      show: false,
      projTree: [],
      // 选择图表类型数据
      chartsData: [],
      chartsNewData: [],
      arr: [],
      tableData: {},
      comTypeChart: '',
      defaultProps: {
        label: 'name',
        children: 'subs'
      },
      screenId: '',
      loading: false,
      isPublic: false,
      // 弹出框
      diag: {
        title: '从仪表盘导入',
        widthSet: '800px',
        maxset: 3
      },
      // 表单数据
      form: {
        name: '',
        ImportRadio: 'ImportSet',
        dsh_id: '',
        type: 'dashboard',
        ct_id: '',
        comType: ''
      }
    };
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    }
  },
  computed: {
    ...mapState({
      screenInfo: (state) => state.editor.screenInfo,
      compPackgesMap: state => state.editor.compPackgesMap
    }),
    ...mapGetters('editor', ['getComDataById', 'ctxMenuList', 'cursnapshotData'])
  },
  methods: {
    // 选择图表类型-改变时操作
    async handleChange (value, index, item) {
      this.activeIndex = index;
      this.listLoading = true
      // console.log(value, item)
      if (value) {
        this.form.name = item.name;
        this.form.ct_id = item.ct_id;
        this.form.comType = item.comType;
        this.ImportName = item.name;
        const res = await getCheckChartData({ dsh_id: this.form.dsh_id, ct_id: item.ct_id });
        if (res && res.success) {
          this.comTypeChart = res.data.comType;
        } else {
          this.chartList = this.chartList.filter(skitem => {
            return skitem.ct_id !== item.ct_id
          })
          this.$message.error(res.message);
        }
      }
      this.listLoading = false
    },

    // 选择图表类型文件
    handleCheckedChange (item) {
      this.selectKind = item;
    },

    // 树节点进行筛选时执行
    filterNode (value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 节点被点击时的回调
    handleNodeClick (data) {
      this.tableData = data;
      this.chartList = [];
      this.form.dsh_id = data.dsh_id;
      if (this.form.dsh_id !== undefined) {
        this.getImportDash();
      }
    },
    // 树型-搜索
    searchTree: _.debounce(function () {
      // this.getImportInfo();
    }, 500),
    searchKind: _.debounce(function () {
      // this.getImportInfo();
      this.arr = [];
      this.chartsNewData = _.cloneDeep(this.chartsData);
      for (let i = 0; i < this.chartsData.length; i++) {
        if (this.chartsData[i].name.indexOf(this.filterKind) >= 0) {
          this.arr.push(this.chartsData[i]);
          this.chartsNewData = this.arr;
        }
      }
      if (this.arr.length === 0) {
        this.chartsNewData = [];
      }
      return this.arr;
    }, 500),

    // 显示弹出框
    showImport () {
      this.show = true;
      // 数据源时-选择弹出框
      if (this.importDash) {
        this.diag.maxset = 1;
        this.diag.widthSet = '600px';
        this.selectHidden = false;
      } else {
        this.selectHidden = true;
      }
      this.chartsData = [];
      this.selectKind = [];
      this.getImportInfo();
    },

    // 关闭
    close () {
      this.show = false;
      this.$nextTick(() => {
        this.isPublic = false;
      });
      this.$emit('close');
    },

    // 获取仪表盘树型-列表数据
    async getImportInfo () {
      this.treeLoading = true;
      try {
        const res = await getImportTree();
        if (res && res.success) {
          this.projTree = res.data.proj;
          this.treeLoading = false;
        }
      } catch (e) {
        this.projTree = [];
      }
    },

    // 获取仪表盘树型-列表数据
    async getImportDash () {
      try {
        const res = await getImportDashboard({ dsh_id: this.form.dsh_id });
        if (res && res.success) {
          this.chartsData = this.generateChartsMap(res.data.meta.charts);
          this.chartsNewData = this.chartsData;
        }
      } catch (e) {}
    },

    // 生成所需的chart结构
    generateChartsMap (chartsMap) {
      const charts = [];
      for (const chart of chartsMap) {
        const chartInfo = _.cloneDeep(chart.children[0].meta);
        chartInfo.check = false;
        charts.push(chartInfo);
      }
      return charts;
    },

    // 关闭弹出框
    closeDialog () {
      this.show = false;
      this.filterText = '';
    },

    // 确定操作
    async submit () {
      // 如果选择图表类型为空时
      // return
      if (this.chartList.length === 0) {
        this.$message.error('请选择图表类型');
        return false;
      };
      // 数据源-创建
      const data = {
        workspaceId: this.screenInfo.workspaceId,
        type: this.form.type,
        name: this.comTypeChart + new Date().getTime(),
        description: '',
        config: { dsh_id: this.form.dsh_id, ct_id: this.form.ct_id, name: this.ImportName }
      };
      const res = await createDatastorage(data);
      this.$emit('update', res.data);
      this.show = false;
      this.filterText = '';
    }
  }
};
</script>

<style lang="scss" scoped>
.import-dialog {
  ::v-deep .el-tree-node.is-current {
    background: rgba(61, 133, 255, 0.1);
  }
  .hzSet {
    width: 32px;
    height: 32px;
    // line-height: 32px;
    font-size: 16px;
    vertical-align: middle;
  }
  .textName {
    position: relative;
    top: 6px;
    display: inline-block;
    width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .import-content {
    display: flex;
    height: 324px;
    overflow: hidden;
    &__left {
      width: 256px;
      padding-right: 10px;
      margin-right: 10px;
      border-right: 1px solid #293246;
      ::v-deep .el-tree {
        background: none;
      }
      .filter-tree {
        margin-top: 14px;
        max-height: 284px;
        .el-tree-node:focus > .el-tree-node__content {
          background-color: #3d85ff !important;
        }
        .tree-title {
          color: #fff;
          font-size: 12px;
        }
        .el-icon-document {
          font-size: 16px;
        }
        .el-icon-folder-opened {
          font-size: 16px;
        }
        .nowrap {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          display: inline-block;
          width: 146px;
          vertical-align: sub;
        }
        .icon-red {
          color: #ad5419;
        }
      }
      ::v-deep .el-input .el-input__inner{
        border-top: 0px;
        border-left: 0px;
        border-right: 0px;
        background-color: rgba(204, 219, 255, 0.06);
      }
    }
    &__mid {
      width: 352px;
      position: relative;

      .mid-top {
        height: 28px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        margin-bottom: 10px;
        &__title {
          font-size: 12px;
          color: #fff;
        }
        &__input {
          width: 160px;
          ::v-deep .el-input__inner{
            border-top: 0px;
            border-left: 0px;
            border-right: 0px;
            background-color: rgba(204, 219, 255, 0.06);
          }
        }
      }
      .chart-list {
        width: 316px;
        height: 290px;
        overflow: auto;
        ::v-deep .el-checkbox {
          &.is-bordered {
            margin-bottom: 10px;
          }
          &.is-bordered+.el-checkbox.is-bordered {
            margin-left: 0px;
          }
          .el-checkbox{
            &__inner {
              display: none;
            }
            &__label {
              padding: 0;
              font-size: 12px;
              color: #fff;
              position: absolute;
              bottom: 50%;
              left: 50%;
              transform: translate(-50%, 50%);
                .loading-mask{
                  top: 3px;
                  left: -4px;
                  .el-loading-mask{
                    background-color: rgba(0,0,0,0);
                    .el-loading-spinner{
                      .circular{
                        width: 40px;
                        height: 40px;

                      }
                    }
                  }
              }
            }
          }
        }
      }
      .chart-nodata {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        font-size: 12px;
        color: #fff;
      }

    }
    &__right {
      width: 386px;
      padding-left: 10px;
      margin-left: 10px;
      border-left: 1px solid #293246;
      .right-top {
        &__title {
          font-size: 12px;
          color: #fff;
          margin-bottom: 10px;
        }
        &__block {
          ::v-deep .el-radio {
            margin-bottom: 10px;
            display: block;
            &__label {
              font-size: 12px;
              color: #fff;
            }
          }
          .top-title {
            float: left;
            margin-right: 18px;
            &-name {
              font-size: 12px;
              color: #fff;
            }
            &-nor {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
            }
          }
        }
      }

    }
  }
}
</style>
