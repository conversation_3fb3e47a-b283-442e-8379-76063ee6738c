
<template>
  <div class="filter-preview" :class="{'show': show}" @mouseleave="closeTest">
    <div class="filter-title">
      <span>过滤器输入数据</span>
      <span class="icon el-icon-close" @click="closeTest"></span>
    </div>
    <div class="code-wrap">
      <CodeEditor ref="code1" v-if="show" :options="options" />
    </div>
    <div class="filter-title">
      <span>过滤器输出数据</span>
    </div>
    <div class="code-wrap">
      <CodeEditor ref="code2" v-if="show" :options="options" />
    </div>
  </div>
</template>

<script>
import CodeEditor from './CodeEditor';
import dataUtil from '@/utils/data';
export default {
  name: 'FilterPreview', // 过滤器测试结果
  props: {
    sourceData: String
  },
  components: {
    CodeEditor
  },
  data () {
    return {
      show: false,
      options: {
        readOnly: true
      }
    }
  },
  methods: {
    closeTest () {
      this.show = false;
    },
    getResult (filters) {
      this.show = true;
      this.$nextTick(() => {
        const prevFilters = filters.slice(0, filters.length - 1);
        const sourceData = JSON.parse(this.sourceData);
        if (prevFilters.length) {
          try {
            const result = dataUtil.filterData(sourceData, prevFilters);
            this.$refs.code1.setValue(JSON.stringify(result.slice(0, 100)));
          } catch (e) {}
        } else {
          this.$refs.code1.setValue(JSON.stringify(sourceData.slice(0, 100)));
        }
        try {
          const result = dataUtil.filterData(sourceData, filters);
          this.$refs.code2.setValue(JSON.stringify(result.slice(0, 100)));
        } catch (e) {}
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-preview {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 520px;
  width: 520px;
  z-index: 999;
  padding: 0 16px;
  padding-top: 70px;
  background: #22242b;
  overflow: auto;
  display: none;
  &.show {
    display: block;
  }
  .filter-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #999;
    margin-top: 20px;
    margin-bottom: 16px;
    .icon {
      font-size: 16px;
    }
  }
}
</style>
