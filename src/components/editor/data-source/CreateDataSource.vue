<template>
  <div>
    <el-dialog
      class='create-datasource'
      :visible.sync="show"
      :title="title"
      append-to-body
      :width="curStep === 0?'650px':'unset'"
      :close-on-click-modal="false"
      @close="closeDialog"
      :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
      top="0"
    >
     <div :style="{width:'100%',display:'flex',justifyContent:'center'}">
       <!-- <el-steps :active="curStep" finish-status="success" v-if="form.type === 'excel'" :style="{width:'304px'}">
        <el-step title="上传文件"></el-step>
        <el-step title="预览数据"></el-step>
      </el-steps> -->
      <div class="steps">
        <div class="first">
          <div :class="['icon',curStep === 0?'cur':'']" v-if="curStep === 0">
            1
          </div>
          <div v-else>
            <hz-icon name="compelete" :style="{fontSize:'24px'}"></hz-icon>
          </div>
          <div class="content">
            <span :class="['step-text',curStep === 0?'cur':'']">上传文件</span>
            <div :class="['line',curStep === 0?'cur':'next']"></div>
          </div>
        </div>
        <div class="second">
          <div :class="['icon',curStep === 1?'cur':'next']">
            2
          </div>
          <span :class="['step-text',curStep === 1?'cur':'']">预览数据</span>
        </div>
      </div>
     </div>
      <el-form
        ref="form"
        size="mini"
        :model="form"
        :rules="rules"
        label-width="110px"
        label-suffix="："
        v-show="curStep === 0"
      >
        <el-form-item label="类型" prop="type">
          <el-select class="input-theme" popper-class="poper-theme" v-model="form.type" size="mini" style="width: 100%">
            <el-option
              v-for="opt in sourceTypeOpt"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据源名称" prop="name">
          <el-input class="input-theme" v-model="form.name" size="mini"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            class="input-theme"
            type="textarea"
            :rows="4"
            size="mini"
          ></el-input>
        </el-form-item>
        <template v-if="form.type === 'csv_file'">
          <el-form-item label="上传文件" required key="file">
            <el-upload
              ref="upload"
              size="mini"
              class="upload-demo"
              :class="[$route.name.includes('edit') ? '' : 'upload-theme']"
              drag
              :action="uploadUrl"
              accept=".csv"
              :headers="uploadHeaders"
              :auto-upload="true"
              :file-list="fileList"
              :on-change="handleChange"
              :on-remove="removeFile"
              :before-upload="beforeUpload"
              :on-success="uploadSuccess"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">点击上传CSV文件</div>
            </el-upload>
          </el-form-item>
        </template>
        <template v-if="form.type === 'json'">
          <el-form-item label="上传文件" required key="file2">
            <el-upload
              ref="upload"
              size="mini"
              class="upload-demo"
              :class="[$route.name.includes('edit') ? '' : 'upload-theme']"
              drag
              :action="uploadUrl"
              accept=".json"
              :headers="uploadHeaders"
              :auto-upload="true"
              :file-list="fileList"
              :on-change="handleChange"
              :on-remove="removeFile"
              :before-upload="beforeUpload"
              :on-success="uploadSuccess"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">点击上传JSON文件</div>
            </el-upload>
          </el-form-item>
        </template>
        <template v-if="form.type === 'api'">
          <el-form-item
            label="Base URL"
            prop="config.baseUrl"
            key="config.baseUrl"
          >
            <el-input class="input-theme" v-model="form.config.baseUrl" size="mini"></el-input>
          </el-form-item>
        </template>
        <template v-if="form.type === 'mysql'">
          <el-form-item label="连接地址" prop="config.host" key="config.host">
            <el-input class="input-theme" v-model="form.config.host" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="config.port" key="config.port">
            <el-input
              class="input-theme"
              v-model="form.config.port"
              size="mini"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="用户名"
            prop="config.username"
            key="config.username"
          >
            <el-input class="input-theme" v-model="form.config.username" size="mini"></el-input>
          </el-form-item>
          <el-form-item
            label="密码"
            key="config.password"
          >
            <input class="input-theme" type="password" autocomplete="new-password" hidden />
            <el-input
              class="input-theme"
              v-model="form.config.password"
              size="mini"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item label="数据库名" key="config.database">
            <el-row :gutter="5">
              <el-col :span="8">
                <el-button
                  type="plain"
                  size="mini"
                  :disabled="!canFetch"
                  :loading="loading2"
                  @click="getDatastorageList"
                >
                  {{ loading2 ? "获取数据库中" : "获取数据库列表" }}
                </el-button>
              </el-col>
              <el-col :span="10">
                <el-form-item prop="config.database" label-width="0px">
                  <el-select
                    class="input-theme"
                    popper-class="poper-theme"
                    v-model="form.config.database"
                    placeholder="请选择数据库"
                  >
                    <el-option
                      v-for="opt in dbList"
                      :key="opt.value"
                      :value="opt.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
        </template>
        <template v-if="form.type === 'dmdb'">
          <el-form-item label="连接地址" prop="config.host" key="config.host">
            <el-input class="input-theme" v-model="form.config.host" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="config.port" key="config.port">
            <el-input
              class="input-theme"
              v-model="form.config.port"
              size="mini"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="用户名"
            prop="config.username"
            key="config.username"
          >
            <el-input class="input-theme" v-model="form.config.username" size="mini"></el-input>
          </el-form-item>
          <el-form-item
            label="密码"
            prop="config.password"
            key="config.password"
          >
            <input class="input-theme" type="password" autocomplete="new-password" hidden />
            <el-input
              class="input-theme"
              v-model="form.config.password"
              size="mini"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item label="数据库名" key="config.database">
            <el-row :gutter="5">
              <el-col :span="8">
                <el-button
                  type="plain"
                  size="mini"
                  :disabled="!canFetch"
                  :loading="loading2"
                  @click="getDatastorageList"
                >
                  {{ loading2 ? "获取数据库中" : "获取数据库列表" }}
                </el-button>
              </el-col>
              <el-col :span="10">
                <el-form-item prop="config.database" label-width="0px">
                  <el-select
                    class="input-theme"
                    popper-class="poper-theme"
                    v-model="form.config.database"
                    placeholder="请选择数据库"
                  >
                    <el-option
                      v-for="opt in dbList"
                      :key="opt.value"
                      :value="opt.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
        </template>
        <template v-if="form.type === 'postgresql'">
          <el-form-item label="连接地址" prop="config.host" key="config.host">
            <el-input class="input-theme" v-model="form.config.host" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="config.port" key="config.port">
            <el-input
              class="input-theme"
              v-model="form.config.port"
              size="mini"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="用户名"
            prop="config.username"
            key="config.username"
          >
            <el-input class="input-theme" v-model="form.config.username" size="mini"></el-input>
          </el-form-item>
          <el-form-item
            label="密码"
            prop="config.password"
            key="config.password"
          >
            <input type="password" autocomplete="new-password" hidden />
            <el-input
              class="input-theme"
              v-model="form.config.password"
              size="mini"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item
            label="数据库名"
            prop="config.database"
            key="config.database"
          >
            <el-input
              class="input-theme"
              v-model="form.config.database"
              placeholder="请输入数据库名称"
            ></el-input>
          </el-form-item>
        </template>
        <template v-if="form.type === 'oracle'">
          <el-form-item label="连接地址" prop="config.host" key="config.host">
            <el-input class="input-theme" v-model="form.config.host" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="config.port" key="config.port">
            <el-input
              class="input-theme"
              v-model="form.config.port"
              size="mini"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="用户名"
            prop="config.username"
            key="config.username"
          >
            <el-input class="input-theme" v-model="form.config.username" size="mini"></el-input>
          </el-form-item>
          <el-form-item
            label="密码"
            prop="config.password"
            key="config.password"
          >
            <input type="password" autocomplete="new-password" hidden />
            <el-input
              class="input-theme"
              v-model="form.config.password"
              size="mini"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item
            label="sid或服务名"
            prop="config.sid"
            key="config.sid"
          >
            <el-input class="input-theme" v-model="form.config.sid" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="数据库名" key="config.database">
            <el-row :gutter="5">
              <el-col :span="8">
                <el-button
                  type="plain"
                  size="mini"
                  :disabled="!canFetch"
                  :loading="loading2"
                  @click="getDatastorageList"
                >
                  {{ loading2 ? "获取数据库中" : "获取数据库列表" }}
                </el-button>
              </el-col>
              <el-col :span="10">
                <el-form-item prop="config.database" label-width="0px">
                  <el-select
                    class="input-theme"
                    popper-class="poper-theme"
                    v-model="form.config.database"
                    placeholder="请选择数据库"
                  >
                    <el-option
                      v-for="opt in dbList"
                      :key="opt.value"
                      :value="opt.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
        </template>
        <template v-if="form.type === 'websocket'">
          <el-form-item label="连接地址" prop="config.host" key="config.host">
            <el-input class="input-theme" v-model="form.config.host" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="地址后缀" prop="config.path" key="config.path">
            <el-input class="input-theme" v-model="form.config.path" size="mini"></el-input>
          </el-form-item>
        </template>
        <template v-if="form.type === 'dmc'">
          <el-form-item label="连接地址" prop="config.host" key="config.host">
            <el-input class="input-theme" v-model="form.config.host" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="config.port" key="config.port">
            <el-input
              class="input-theme"
              v-model="form.config.port"
              size="mini"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="用户名"
            prop="config.username"
            key="config.username"
          >
            <el-input class="input-theme" v-model="form.config.username" size="mini"></el-input>
          </el-form-item>
          <el-form-item
            label="密码"
            prop="config.password"
            key="config.password"
          >
            <input type="password" autocomplete="new-password" hidden />
            <el-input
              class="input-theme"
              v-model="form.config.password"
              size="mini"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item label="数据库名" key="config.database">
            <el-row>
              <el-col :span="8">
                <el-button
                  type="primary"
                  size="mini"
                  :disabled="!canFetch"
                  :loading="loading2"
                  @click="getDatastorageList"
                >
                  {{ loading2 ? "获取数据库中" : "获取数据库列表" }}
                </el-button>
              </el-col>
              <el-col :span="10">
                <el-form-item prop="config.database" label-width="0px">
                  <el-select
                    class="input-theme"
                    popper-class="poper-theme"
                    v-model="form.config.database"
                    placeholder="请选择数据库"
                  >
                    <el-option
                      v-for="opt in dbList"
                      :key="opt.value"
                      :value="opt.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
          <!-- <el-form-item>
          <el-button type="primary" size="mini" style="width:116px;" :disabled="!form.config.database">测试连接</el-button>
        </el-form-item> -->
        </template>
        <template v-if="form.type === 'mongodb'">
          <el-form-item label="连接地址" prop="config.host" key="config.host">
            <el-input class="input-theme" v-model="form.config.host" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="config.port" key="config.port">
            <el-input
              class="input-theme"
              v-model="form.config.port"
              size="mini"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="用户名"
            prop="config.username"
            key="config.username"
          >
            <el-input class="input-theme" v-model="form.config.username" size="mini"></el-input>
          </el-form-item>
          <el-form-item
            label="密码"
            prop="config.password"
            key="config.password"
          >
            <input type="password" autocomplete="new-password" hidden />
            <el-input
              class="input-theme"
              v-model="form.config.password"
              size="mini"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item
            label="数据库名"
            prop="config.database"
            key="config.database"
          >
            <el-input
              class="input-theme"
              v-model="form.config.database"
              placeholder="请输入数据库名称"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="验证数据库"
            prop="config.authSource"
            key="config.authSource"
          >
            <el-input
              class="input-theme"
              v-model="form.config.authSource"
              placeholder="请输入验证数据库名称"
            ></el-input>
          </el-form-item>
        </template>
        <template v-if="form.type === 'excel'">
          <el-form-item label="上传文件" required key="file3">
            <el-upload
              ref="upload"
              size="mini"
              class="excel-upload-demo"
              :class="[$route.name.includes('edit') ? '' : 'upload-theme']"
              drag
              :action="uploadUrl"
              accept=".xls, .xlsx"
              :auto-upload="true"
              :file-list="fileList"
              :on-change="handleChange"
              :on-remove="removeFile"
              :before-upload="beforeUpload"
              :show-file-list="false"
              :on-success="uploadSuccess">
              <!-- <i class="el-icon-upload"></i>
              <div class="el-upload__text">点击上传Excel文件</div> -->
              <div class="button-div">
                <hz-icon name="upload" :style="{fontSize:'16px'}"></hz-icon>
                <span>上传Excel文件</span>
              </div>
            </el-upload>
            <ul class="fileList">
              <li v-for="(item,index) in fileList" :key="index">
                <div class="fileItem">
                  <div class="content">
                    <i class="el-icon-paperclip" :style="{fontSize:'16px'}"></i>
                    <span>{{item.name}}</span>
                  </div>
                  <div class="op">
                    <i class="el-icon-delete" @click="deleteFile(item)"></i>
                  </div>
                </div>
              </li>
            </ul>
          </el-form-item>
        </template>
        <template v-if="form.type === 'highgodb'">
          <el-form-item label="连接地址" prop="config.host" key="config.host">
            <el-input class="input-theme" v-model="form.config.host" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="端口" prop="config.port" key="config.port">
            <el-input
              class="input-theme"
              v-model="form.config.port"
              size="mini"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="用户名"
            prop="config.username"
            key="config.username"
          >
            <el-input class="input-theme" v-model="form.config.username" size="mini"></el-input>
          </el-form-item>
          <el-form-item
            label="密码"
            prop="config.password"
            key="config.password"
          >
            <input type="password" autocomplete="new-password" hidden />
            <el-input
              class="input-theme"
              v-model="form.config.password"
              size="mini"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item
            label="数据库名"
            prop="config.database"
            key="config.database"
          >
            <el-input
              class="input-theme"
              v-model="form.config.database"
              placeholder="请输入数据库名称"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="模式名称"
            prop="config.schema"
            key="config.schema"
          >
            <el-input
              class="input-theme"
              v-model="form.config.schema"
              placeholder="请输入模式(schema)名称"
            ></el-input>
          </el-form-item>
        </template>
      </el-form>
      <el-table :data="tableData" :height="400" size="mini" v-if="curStep === 1" class="preview-table" style="min-width:500px;max-width:1200px">
        <el-table-column :label="el.name" :prop="el.name" v-for="(el, idx) in tableHead" :key="idx" align="center" width="200">
          <div slot="header" :style="{display:'flex',alignItems:'center',gap:'4px',padding:'2px 16px',justifyContent:'center'}" slot-scope="{ column }">
            <el-select v-model="fieldsType[column.property]" class="select-type" size="mini">
              <div slot="prefix" :style="{height:'100%',display:'flex',alignItems:'center'}">
                <hz-icon :name="`${fieldsType[column.property]}type`" :style="{fontSize:'16px'}"></hz-icon>
              </div>
              <el-option v-for="item in dataType" :key="item.id" :label="item.label" :value="item.id">
                <div :style="{display:'flex',alignItems:'center',gap:'8px'}">
                  <hz-icon :name="`${item.id}type`" :style="{fontSize:'16px'}"></hz-icon>
                  <span>{{item.label}}</span>
                </div>
              </el-option>
            </el-select>
            <span>{{el.name}}</span>
          </div>
        </el-table-column>
      </el-table>
      <div slot="footer" v-if="form.type !== 'excel'">
        <el-button
          type="light-blue"
          size="medium"
          @click="submit"
          :loading="loading"
          >确定</el-button
        >
        <el-button type="text" size="medium" @click="closeDialog"
          >取消</el-button
        >
      </div>
      <div slot="footer" v-else>
        <el-button type="text" size="medium" @click="handleLeftClick">
          {{curStep === 0? '取消' : '上一步'}}
        </el-button>
        <el-button
          type="light-blue"
          size="medium"
          @click="handleRightClick"
          :loading="loading3"
        >
          {{curStep == 0? '下一步' : '确定'}}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createDatastorage,
  updateDatastorage,
  getDbList,
  previewData
} from '@/api/datastorage';
import { replaceUrl, getHttpHeaders } from '@/utils/base';
import { mapState } from 'vuex';
export default {
  name: 'CreateDataSource', // 创建数据源
  props: {
    type: {
      type: String,
      default: 'mysql'
    }
  },
  data () {
    return {
      show: false,
      title: '创建数据源',
      form: {
        id: '',
        type: 'api',
        name: '',
        description: '',
        config: {
          encode: 'auto',
          filePath: '',
          baseUrl: '',
          host: '', // 连接地址
          path: '', // 地址后缀
          port: '', // 端口号
          username: '', // 用户名
          password: '', // 密码
          database: '', // 数据库
          sid: '', // 实例或服务名称id for oracle
          authSource: '' // 验证数据库名称 for mongo
        }
      },
      uploadUrl: replaceUrl(process.env.VUE_APP_SERVER_URL + '/api/common/upload'),
      uploadHeaders: getHttpHeaders(),
      fileList: [],
      rules: {
        type: [
          { required: true, message: '数据源类型必填', trigger: 'change' }
        ],
        name: [
          { required: true, message: '数据源名称必填', trigger: 'change' }
        ],
        'config.encode': [
          { required: true, message: '编码必填', trigger: 'change' }
        ],
        'config.host': [
          { required: true, message: '连接地址必填', trigger: 'change' }
        ],
        'config.port': [
          { required: true, message: '端口必填', trigger: 'change' }
        ],
        'config.username': [
          { required: true, message: '用户名必填', trigger: 'change' }
        ],
        'config.password': [
          { required: true, message: '密码必填', trigger: 'change' }
        ],
        'config.sid': [
          { required: true, message: '实例或sid必填', trigger: 'change' }
        ],
        'config.authSource': [
          { required: true, message: '验证数据库必填', trigger: 'change' }
        ]
      },
      sourceTypeOpt: [
        { value: 'api', label: 'API数据' },
        { value: 'csv_file', label: 'CSV文件' },
        { value: 'excel', label: 'Excel文件' },
        { value: 'mysql', label: 'MySQL数据库' },
        { value: 'dmdb', label: '达梦数据库' },
        { value: 'postgresql', label: 'PostgreSQL数据库' },
        { value: 'oracle', label: 'Oracle数据库' },
        { value: 'mongodb', label: 'MongoDB数据库' },
        { value: 'websocket', label: '实时数据' },
        { value: 'json', label: 'JSON文件' },
        { value: 'highgodb', label: '瀚高数据库' }
      ],
      dbList: [],
      loading: false,
      loading2: false,
      loading3: false,
      curStep: 0,
      dataType: [
        { id: 'string', label: '文本' },
        { id: 'date', label: '日期' },
        { id: 'number', label: '数值' }
      ],
      tableData: [],
      fieldsType: {}
    };
  },
  computed: {
    ...mapState({
      screenInfo: (state) => state.editor.screenInfo
    }),
    canFetch () {
      const { host, port, username, password } = this.form.config;
      return !!host && !!port && !!username && !!password;
    },
    tableHead () {
      const arr = []
      if (this.tableData.length) {
        const data = this.tableData[0]
        Object.keys(data).forEach(el => {
          let type = 'number'
          if (/^-?[0-9]*.?[0-9]*$/.test(data[el])) {
            // 数值类型
            type = 'number'
          } else if (/([\u4e00-\u9fa5]|[\ufe30-\uffa0])/.test(data[el])) {
            // 字符串类型
            type = 'string'
          } else {
            type = 'date'
          }
          arr.push({
            name: el,
            type
          })
        })
      }
      return arr
    }
  },
  methods: {
    showDialog (type = 'api') {
      this.form.type = type;
      this.title = '创建数据源';
      this.show = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    closeDialog () {
      this.show = false;
      this.form = {
        id: '',
        type: 'api',
        name: '',
        description: '',
        config: {
          encode: 'auto',
          fileName: '',
          filePath: '',
          baseUrl: '',
          host: '', // 连接地址
          path: '', // 地址后缀
          port: '', // 端口号
          username: '', // 用户名
          password: '', // 密码
          database: '', // 数据库
          sid: '',
          authSource: ''
        }
      };
      this.fileList = [];
      this.curStep = 0
    },
    showEdit (item) {
      this.show = true;
      this.$nextTick(() => {
        this.form = {
          ...this.form,
          ...item,
          config: item.config
            ? {
              ...this.form.config,
              ...item.config
            }
            : this.form.config
        };
        if (item.type === 'csv_file' || item.type === 'json') {
          this.fileList = [
            { name: item.config.fileName, url: item.config.filePath }
          ];
        }
        this.title = '编辑数据源';
      });
    },
    handleChange (file, fileList) {
      if (file.name.includes('.xlsx') || file.name.includes('.xls')) {
        this.form.name = file.name.split('.')[0];
      }
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]; // 这一步，是 展示最后一次选择的csv文件
      }
    },
    removeFile (file, fileList) {
      this.form.config.fileName = '';
      this.form.config.filePath = '';
    },
    beforeUpload (file) {
      // const size = file.size;
      // if (size > 1024 * 1024 * 10) {
      //   this.$message.error('文件大小不能超过10M');
      //   this.fileList = [];
      //   return false
      // }
      return true;
    },
    async getDatastorageList () {
      // 获取数据库列表
      const sid = this.form.config.sid;
      const authSource = this.form.config.authSource;
      let data = {
        type: this.form.type,
        host: this.form.config.host,
        port: this.form.config.port,
        username: this.form.config.username,
        password: this.form.config.password
      };
      try {
        this.loading2 = true;
        if (this.form.type === 'oracle') {
          data = Object.assign(data, { sid: sid });
        }
        if (this.form.type === 'mongodb') {
          data = Object.assing(data, { authSource: authSource });
        }
        const res = await getDbList(data);
        if (res && res.success) {
          this.dbList = res.data.map((item) => {
            return {
              value: item,
              label: item
            };
          });
        } else {
          this.$message.error('数据库连接失败：' + res.message);
        }
        this.loading2 = false;
      } catch (e) {
        this.loading2 = false;
      }
    },
    submit () {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const data = {
            workspaceId:
              this.screenInfo.workspaceId || this.$route.params.workspaceId,
            type: this.form.type,
            name: this.form.name,
            description: this.form.description,
            config: {}
          };
          if (this.form.type === 'csv_file') {
            if (!this.form.config.filePath) {
              this.$message.error('请上传csv文件');
              return;
            }
            data.config.encode = this.form.config.encode;
            data.config.fileName = this.form.config.fileName;
            data.config.filePath = this.form.config.filePath;
          }
          if (this.form.type === 'json') {
            if (!this.form.config.filePath) {
              this.$message.error('请上传json文件');
              return;
            }
            data.config.fileName = this.form.config.fileName;
            data.config.filePath = this.form.config.filePath;
          }
          if (this.form.type === 'excel') {
            if (!this.form.config.filePath) {
              this.$message.error('请上传excel文件');
              return;
            }
            data.config.encode = this.form.config.encode;
            data.config.fileName = this.form.config.fileName;
            data.config.filePath = this.form.config.filePath;
          }
          if (this.form.type === 'api') {
            data.config.baseUrl = this.form.config.baseUrl;
          }
          if (this.form.type === 'mysql') {
            if (!this.form.config.database) {
              this.$message.error('请选择数据库');
              return;
            }
            data.config.host = this.form.config.host;
            data.config.port = this.form.config.port;
            data.config.username = this.form.config.username;
            data.config.password = this.form.config.password;
            data.config.database = this.form.config.database;
          }
          if (this.form.type === 'dmdb') {
            if (!this.form.config.database) {
              this.$message.error('请选择数据库');
              return;
            }
            data.config.host = this.form.config.host;
            data.config.port = this.form.config.port;
            data.config.username = this.form.config.username;
            data.config.password = this.form.config.password;
            data.config.database = this.form.config.database;
          }
          if (this.form.type === 'postgresql') {
            if (!this.form.config.database) {
              this.$message.error('请填写数据库');
              return;
            }
            data.config.host = this.form.config.host;
            data.config.port = this.form.config.port;
            data.config.username = this.form.config.username;
            data.config.password = this.form.config.password;
            data.config.database = this.form.config.database;
          }
          if (this.form.type === 'oracle') {
            if (!this.form.config.database) {
              this.$message.error('请填写数据库');
              return;
            }
            data.config.host = this.form.config.host;
            data.config.port = this.form.config.port;
            data.config.username = this.form.config.username;
            data.config.password = this.form.config.password;
            data.config.sid = this.form.config.sid;
            data.config.database = this.form.config.database;
          }
          if (this.form.type === 'mongodb') {
            if (!this.form.config.database) {
              this.$message.error('请选择数据库');
              return;
            }
            data.config.host = this.form.config.host;
            data.config.port = this.form.config.port;
            data.config.username = this.form.config.username;
            data.config.password = this.form.config.password;
            data.config.database = this.form.config.database;
            data.config.authSource = this.form.config.authSource;
          }
          if (this.form.type === 'websocket') {
            data.config.host = this.form.config.host;
            data.config.path = this.form.config.path;
          }
          if (this.form.type === 'highgodb') {
            if (!this.form.config.database) {
              this.$message.error('请选择数据库');
              return;
            }
            data.config.host = this.form.config.host;
            data.config.port = this.form.config.port;
            data.config.username = this.form.config.username;
            data.config.password = this.form.config.password;
            data.config.database = this.form.config.database;
            data.config.schema = this.form.config.schema;
          }
          this.loading = true;
          let res;
          try {
            if (!this.form.id) {
              res = await createDatastorage(data, {});
            } else {
              res = await updateDatastorage(data, { id: this.form.id });
            }
            this.loading = false;
            if (res && res.success) {
              this.$emit('update', res.data);
              this.closeDialog();
            } else {
              this.$message.warn(res.message);
            }
          } catch (e) {
            this.loading = false;
          }
        }
      });
    },
    async uploadSuccess (response, file, fileList) {
      // csv文件上传成功 提交表单
      // console.log(response, file, fileList)
      if (response && response.success) {
        this.form.config.fileName = file.name;
        this.form.config.filePath = response.data.url;
      }
    },
    handleLeftClick () {
      if (this.curStep === 0) {
        this.closeDialog()
      } else {
        this.curStep = 0
      }
    },
    async handleRightClick () {
      if (this.curStep === 0) {
        // 预览接口
        this.$refs.form.validate(valid => {
          if (valid) {
            if (!this.fileList.length) {
              this.$message.warn('请上传Excel文件')
              return
            }
            this.loading3 = true
            previewData({
              filePath: this.form.config.filePath,
              type: 'excel'
            }).then(res => {
              this.loading3 = false
              this.tableData = res.data
              this.curStep = 1
              const data = this.tableData[0]
              Object.keys(data).forEach(el => {
                let type = ''
                if (/^-?[0-9]*.?[0-9]*$/.test(data[el])) {
                  // 数值类型
                  type = 'number'
                } else if (/([\u4e00-\u9fa5]|[\ufe30-\uffa0])/.test(data[el])) {
                  // 字符串类型
                  type = 'string'
                } else {
                  type = 'date'
                }
                this.$set(this.fieldsType, el, type)
                // this.fieldsType[el] = type
              })
            })
          }
        })
      } else {
        // 类型编辑接口
        const data = {
          workspaceId:
              this.screenInfo.workspaceId || this.$route.params.workspaceId,
          type: this.form.type,
          name: this.form.name,
          description: this.form.description,
          config: {}
        }
        data.config.encode = this.form.config.encode;
        data.config.fileName = this.form.config.fileName;
        data.config.filePath = this.form.config.filePath;
        data.config.fieldsType = this.fieldsType
        this.loading = true;
        let res;
        try {
          if (!this.form.id) {
            res = await createDatastorage(data, {});
          } else {
            res = await updateDatastorage(data, { id: this.form.id });
          }
          this.loading = false;
          if (res && res.success) {
            this.$emit('update', res.data);
            this.closeDialog();
          } else {
            this.$message.warn(res.message);
          }
        } catch (e) {
          this.loading = false;
        }
      }
    },
    handleSelectChange (v, n) {
      // this.$set(this.fieldsType, n, v)
      // this.$forceUpdate()
    },
    deleteFile (item) {
      this.fileList = this.fileList.filter(el => {
        return el.uid !== item.uid
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.create-datasource {
  ::v-deep {
    .el-form-item__label {
      color: #fafafa;
      font-size: 12px;
    }
    .el-upload-dragger {
      width: 300px;
      height: 140px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        font-size: 12px;
      }
    }
    .el-upload-dragger .el-icon-upload {
      font-size: 50px;
      margin: 20px 0 16px;
    }
  }
  ::v-deep {
    .select-type{
      .el-input__inner{
        width: 0px;
      }
    }
    .preview-table{
      .el-table__header-wrapper{
        height: 48px;
        display: flex;
        align-items: center;
      }
    }
  }
  .steps{
    margin-bottom: 24px;
    width:304px;
    height: 24px;
    display: flex;
    align-items: flex-start;
    .icon{
      display: flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      padding: 2px 0px;
      border-radius: 12px;
    }
    .icon.cur{
      background: var(--dark-primary-900, #3D85FF);
      color: var(--dark-general-100, #FFF)
    }
    .icon.next{
      border: 1px solid var(--dark-mono-a-500, rgba(204, 219, 255, 0.32));
    }
    .step-text{
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
    .step-text.cur{
      color: var(--dark-primary-900, #3D85FF);
    }
    .line{
      width: 80px;
      height: 2px;
      background: rgba(204, 219, 255, 0.25);
    }
    .first{
      display: flex;
      width: 200px;
      align-items: center;
      gap: 8px;
      .content{
        display: flex;
        padding-right: 0px;
        align-items: center;
        gap: 16px;
        align-self: stretch;
      }
    }
    .second{
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  ::v-deep{
    .excel-upload-demo{
      .el-upload-dragger{
        width: 100%;
        height: 100%;
        border-radius: 0px !important;
        .button-div{
          height:34px;
          display: flex;
          padding: 5px 10px;
          gap:4px;
          align-items: center;
          border-radius: 4px;
          border: 1px solid var(--seatom-mono-a-500, rgba(204, 219, 255, 0.32));
        }
      }
    }
  }
  .fileList{
    list-style: none;
    .fileItem{
      cursor:pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .content{
        padding: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
        span{
          color: var(--seatom-type-900, #FFF);
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
      }
      .op{
        display:none;
        .el-icon-delete{
          padding:8px;
          border-radius: 8px;
          background: var(--seatom-mono-a100) ;
        }
      }
    }
    .fileItem:hover{
      border-radius: 8px;
      background: var(--seatom-mono-a100);
      .op{
        display: block;
      }
    }
  }
}
</style>
