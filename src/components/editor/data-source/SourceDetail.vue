<script>
import { datastorageList } from '@/api/datastorage'
export default {
  name: 'SourceDetail',
  components: {
    JsonEditor: () => import('@/components/editor/data-source/JsonEditor')
  },
  props: {
    type: String,
    jsonData: {
      required: false
    },
    source: Object
  },
  data () {
    return {
      json: this.jsonData,
      sourceList: [],
      dataContainerList: []
    }
  },
  watch: {
    type () {
      this.render()
    },
    jsonData: {
      deep: true,
      handler (val) {
        this.json = val
      }
    }
  },
  created () {
    this.getDatasourceList(this.type)
  },
  methods: {
    staticDataChange (json) {
      this.$emit('staticDataChange', json)
    },
    sourceChange (type, val) {
      this.$emit('sourceChange', type, val)
    },
    createData () {
      this.$refs.create.showDialog(this.type)
    },
    createDashData (type) {
      this.$refs.createDash.showImport(type);
    },
    async getDatasourceList (sourceType) { // 获取数据源列表
      const data = {
        workspaceId: this.screenInfo?.workspaceId,
        type: sourceType
      }
      const res = await datastorageList(data)
      if (res && res.success) {
        if (res.data.length === 0) {
          this.sourceList = []
          return;
        }
        this.sourceList = res.data.map(item => {
          return {
            ...item,
            id: item.id + ''
          }
        })
      }
    }
  },
  render () {
    switch (this.type) {
      case 'static':
        return (<div class="editor-wrap">
          <json-editor vModel={this.json} modes={['tree', 'code']} vOn:json-change={val => this.staticDataChange(val)}></json-editor>
        </div>)
      case 'csv_file':
      case 'json':
      case 'api':
      case 'mysql':
      case 'postgresql':
      case 'dmdb':
      case 'oracle':
      case 'mongodb':
      case 'websocket':
      case 'excel':
        return (<div class="config-control">
          <div class="config-title nowrap">数据源</div>
          <div class="flex-row">
            <el-select
              vModel={this.source[this.type].data.sourceId}
              clearable
              vOn:change={(val) => this.sourceChange(this.type, val)} size="mini">
              {this.sourceList.map(opt => {
                return (<el-option key={opt.id} value={opt.id} label={opt.name}></el-option>)
              })}
            </el-select>
            <el-button vOn:click={this.createData} class="ml10" type="plain" size="mini">新建</el-button>
          </div>
        </div>)
      case 'datacontainer':
        return (<div class="config-control">
          <div class="config-title nowrap">数据源</div>
          <div class="flex-row">
            <el-select
              class="w100"
              vModel={this.source[this.type].data.sourceId}
              clearable
              vOn:change={(val) => this.sourceChange(this.type, val)}
              size="mini">
              {this.dataContainerList.map(opt => {
                return (<el-option key={opt.id} value={opt.id} label={opt.name}></el-option>)
              })}
            </el-select>
            <el-button vOn:click={this.createData} class="ml10" type="plain" size="mini">新建</el-button>
          </div>
        </div>)
      case 'dashboard':
        return (
          <div class="config-control">
            <div class="config-title nowrap">数据源</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap" title={this.source.dashboard.data.name}>{ this.source.dashboard.data.name }</div>
              <el-button vOn:click={this.createDashData('dashboard')} class="ml10" type="plain" size="mini">选择</el-button>
            </div>
          </div>)
      default:
        return ''
    }
  }
}
</script>
<style lang="scss" scoped>
.w100 {
  width: 100%;
}
.flex-row {
  display: flex;
  justify-content: space-between;
  .ml10 {
    margin-left: 10px;
  }
  .table-name {
    flex: none;
    width: 110px;
    line-height: 28px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
