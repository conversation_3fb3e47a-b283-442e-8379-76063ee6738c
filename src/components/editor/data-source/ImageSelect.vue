<template>
  <div class="image-select" v-clickoutside="close">
    <div class="select-header" :class="{ 'has-val': !!value }" @click.stop="toggle">
      <img class="selected-img" :src="selectVal" />
      <span class="icon arrow el-icon-arrow-right" :class="{ show: show }"></span>
      <span class="icon delete el-icon-circle-close" @click.stop="deleteImg"></span>
    </div>
    <div class="dropdown" :class="{ _hide: !show }">
      <div class="img-list">
        <div
        class="img-item"
        :class="{ active: selectVal == item.value }"
        v-for="(item, idx) in options" :key="idx"
        @click.stop="selectThis(item)">
          <img class="img" :src="item.value" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageSelect', // 图片下拉选择组件
  props: {
    value: String,
    options: {
      type: Array,
      default () {
        return [];
      }
    }
  },
  data () {
    return {
      show: false
    }
  },
  computed: {
    selectVal: {
      get: function () {
        return this.value;
      },
      set: function (val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    toggle () {
      this.show = !this.show;
    },
    close () {
      this.show = false;
    },
    selectThis (item) {
      this.selectVal = item.value;
      this.close();
    },
    deleteImg () {
      this.$emit('input', '');
      this.show = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.image-select {
  position: relative;
  .select-header {
    width: 210px;
    height: 90px;
    display: flex;
    align-items: center;
    border: 1px solid #393b4a;
    padding: 5px 10px;
    cursor: pointer;
    &.has-val:hover {
      .arrow {
        display: none !important;
      }
      .delete {
        opacity: 1;
        display: inline !important;
      }
    }
    .selected-img {
      flex: 1;
      height: 100%;
      object-fit: contain;
    }
    .icon {
      margin-left: 10px;
      font-size: 20px;
      color: #fff;
      transition: transform .25s linear;
      &.show {
        transform: rotate(90deg);
      }
      &.delete {
        opacity: 0;
        display: none;
        transition: opacity .25s linear;
      }
    }
  }
  .dropdown {
    position: absolute;
    top: 100px;
    left: 0;
    width: 100%;
    z-index: 999;
    background: #202530;
    transition: height 0.25s linear;
    &._hide {
      height: 0;
      overflow: hidden;
    }
    &::before {
      content: '';
      display: block;
      position: absolute;
      left: 50px;
      top: -10px;
      width: 0;
      height: 0;
      border: 5px solid transparent;
      border-bottom-color: #3a4659;
    }
  }
  .img-list {
    height: 100%;
    padding: 8px 8px 0;
    border: 1px solid #3a4659;
    border-radius: 4px;
    overflow-x: hidden;
    overflow-y: auto;
    .img-item {
      display: inline-block;
      width: calc((100% - 10px) / 2);
      height: 65px;
      &:nth-child(2n + 1) {
        margin-right: 10px;
      }
      &.active {
        border: 1px solid var(--seatom-sub-main-color);
      }
      .img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}
</style>
