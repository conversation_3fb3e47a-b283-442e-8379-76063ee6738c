<template>
  <div>
    <el-dialog :visible.sync="show" width="550px" class="create-datasource" top="0" append-to-body :title="title" :close-on-click-modal="false">
      <el-input
        placeholder="搜索"
        size="mini"
        @input="searchTree"
        v-model="filterText">
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
      <div class="tree-container"
        element-loading-background="#1F2430"
        v-loading="treeLoading">
        <el-tree
          class="filter-tree"
          :data="data"
          :props="props"
          default-expand-all
          :filter-node-method="filterNode"
          ref="tree">
          <span slot-scope="{ node }" class="table-name">
            <i class="el-icon-document icon-red"></i>
            <span>{{node.label}}</span>
          </span>
        </el-tree>
      </div>
      <div slot="footer">
        <el-button type="light-blue" size="medium" @click="submit" :loading="loading">确定</el-button>
        <el-button type="text" size="medium" @click="closeDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getTree } from '@/api/datastorage';
import { mapGetters } from 'vuex'
export default {
  data () {
    return {
      filterText: '',
      data: [],
      props: {
        label: 'TABLE_NAME'
      },
      loading: false,
      treeLoading: false,
      show: false,
      title: '选择工作表'
    }
  },
  computed: {
    currentSelectId () {
      return this.$store.state.editor.currentSelectId;
    },
    ...mapGetters('editor', ['currentConfigId'])
  },
  created () {

  },
  methods: {
    getTableList (type) {
      this.treeLoading = true;
      const params = {
        type: type,
        componentId: this.currentConfigId
      };
      const vm = this;
      getTree(params).then(res => {
        vm.treeLoading = false;
        if (res.success && res.data) {
          vm.data = res.data;
        }
      }).catch(err => {
        vm.treeLoading = false;
        console.error(err);
      });
    },
    filterNode (value, data) {
      if (!value) return true;
      return data.TABLE_NAME.indexOf(value) !== -1;
    },
    searchTree (str) {
      this.$refs.tree.filter(str);
    },
    showDialog (type = 'mysql') {
      this.show = true;
      this.$nextTick(() => {
        this.getTableList(type);
      })
    },
    closeDialog () {
      this.show = false;
    },
    submit () {
      const node = this.$refs.tree.getCurrentNode();
      const tbName = node ? node.TABLE_NAME : '';
      this.$emit('update', tbName);
      this.show = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-container {
  padding: 10px 0;
  .table-name {
    font-size: 16px;
    span {
      margin-left: 5px;
      color: rgba(255,255,255, 0.5);
    }
    .icon-red {
      color: #ad5419;
    }
  }
}

</style>
