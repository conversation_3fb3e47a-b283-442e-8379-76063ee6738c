<template>
  <div class="sql-editor">
    <textarea v-model="value" ref="txt" class="txt"></textarea>
  </div>
</template>

<script>
import CodeMirror from 'codemirror';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/panda-syntax.css';
import 'codemirror/mode/sql/sql.js';

import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/show-hint.css';
import './sql-hint.css';
import 'codemirror/addon/hint/sql-hint.js';
import 'codemirror/addon/display/placeholder.js'

export default {
  name: 'SqlEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    suggestions: {
      type: Array,
      default: () => ([])
    },
    placeholder: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      editor: null,
      dico: [
        { className: 'sql', text: 'SELECT' },
        { className: 'sql', text: 'FROM' },
        { className: 'sql', text: 'WHERE' },
        { className: 'sql', text: 'INNER' },
        { className: 'sql', text: 'JOIN' },
        { className: 'sql', text: 'UNION' },
        { className: 'sql', text: 'EXEC' },
        { className: 'sql', text: 'INSERT' },
        { className: 'sql', text: 'INTO' },
        { className: 'sql', text: 'VALUES' },
        { className: 'sql', text: 'UPDATE' },
        { className: 'sql', text: 'DELETE' },
        { className: 'sql', text: 'GROUP' },
        { className: 'sql', text: 'BY' },
        { className: 'sql', text: 'HAVING' },
        { className: 'sql', text: 'IS' },
        { className: 'sql', text: 'DISTINCT' },
        { className: 'sql', text: 'OUTER' },
        { className: 'sql', text: 'TOP' },
        { className: 'sql', text: 'EXISTS' },
        { className: 'sql', text: 'WHEN' },
        { className: 'sql', text: 'CASE' },
        { className: 'sql', text: 'CAST' },
        { className: 'sql', text: 'IN' },
        { className: 'sql', text: 'NULL' },
        { className: 'sql', text: 'BETWEEN' },
        { className: 'sql', text: 'LIKE' },
        { className: 'sql', text: 'REGEXP' },
        { className: 'sql', text: 'RLIKE' },
        { className: 'sql', text: 'NOT' },
        { className: 'sql', text: 'AND' },
        { className: 'sql', text: 'OR' },
        { className: 'sql', text: 'XOR' },
        { className: 'sql', text: 'CONCAT' },
        { className: 'sql', text: 'DIV' },
        { className: 'sql', text: 'MOD' }
      ]
    }
  },
  watch: {
    suggestions (val) {
      this.$nextTick(() => {
        this.editor.refresh();
      })
    }
  },
  mounted () {
    this.init();
  },
  methods: {
    init () {
      if (this.editor) return;
      this.editor = CodeMirror.fromTextArea(this.$refs.txt, {
        tabSize: 4,
        mode: 'text/x-sql',
        theme: 'panda-syntax',
        lineNumbers: true,
        line: true,
        lineWrapping: true,
        hintOptions: {
          completeSingle: false,
          hint: this.hint
        },
        extraKeys: {
          Ctrl: editor => {
            editor.showHint();
          }
        }
      });

      this.editor.setOption('placeholder', this.placeholder)

      this.editor.on('inputRead', editor => {
        editor.showHint();
      });

      this.editor.on('change', (editor, change) => {
        const value = editor.getValue();
        this.$emit('input', value)
      })

      this.editor.on('focus', (cm, event) => {
        this.$emit('focus', event);
      })
    },
    /*
    Return a list of suggestion base on the searchString (the current word that user is typing).
    Each suggestion is an object {text, displayText, className}. See https://codemirror.net/doc/manual.html#addon_show-hint
    - keywords start with the searchString appears first in the suggestion list
    */
    suggest (searchString) {
      /*
      we will score which suggesion should appears first, the higer the score, the higer is the appearance order
      */
      let token = searchString;
      const options = [...this.dico, ...this.suggestions]
      if (searchString.startsWith('.')) token = searchString.substring(1);
      else token = searchString.toLowerCase();
      const resu = [];
      const N = options.length;

      // init scoring: only retains and score suggestions which contain the searchString
      for (let i = 0; i < N; i++) {
        const keyword = options[i].text.toLowerCase();
        let suggestion = null;
        // the base score of all the suggestion is N-i (it means we respect the order in the dico)
        if (keyword.startsWith(token)) {
          // add N to the score of keywords which begin with the token to make them raise up in the suggestion list
          suggestion = Object.assign({ score: N + (N - i) }, options[i]);
        } else if (keyword.includes(token)) {
          suggestion = Object.assign({ score: N - i }, options[i]);
        }
        if (suggestion) resu.push(suggestion);
      }

      // case suggestion for '.'
      if (searchString.startsWith('.')) {
        // raise score of columns, decrease the score of sql keyword
        resu.forEach(s => {
          if (s.className === 'column') s.score += N;
          else if (s.className === 'sql') s.score -= N;
          return s;
        });
      }

      return resu.sort((a, b) => b.score - a.score);
    },
    /*
    [hint implementation for codemirror](https://codemirror.net/doc/manual.html#addon_show-hint):
    take an editor instance and options object, and return a {list, from, to} object, where list is an array of strings or objects (the completions), and from and to give the start and end of the token that is being completed as {line, ch} objects.
     */
    hint (editor) {
      const cur = editor.getCursor();
      const token = editor.getTokenAt(cur);
      const searchString = token.string;
      const getChineseHint = (cur, searchString, end) => {
        if (!/[\u4e00-\u9fa5]/g.test(searchString)) return null;
        const prevToken = editor.getTokenAt({ ...cur, ch: cur.ch - 1 });
        if (/[\u4e00-\u9fa5]/g.test(prevToken.string)) {
          return getChineseHint({ ...cur, ch: cur.ch - 1 }, prevToken.string + searchString, end)
        }
        return {
          list: this.suggest(searchString),
          from: CodeMirror.Pos(cur.line, cur.ch - 1),
          to: CodeMirror.Pos(cur.line, end)
        }
      }

      // 判断是中文字符
      const obj = getChineseHint(cur, searchString, cur.ch)
      if (obj) return obj;

      return {
        list: this.suggest(searchString),
        from: CodeMirror.Pos(cur.line, token.start),
        to: CodeMirror.Pos(cur.line, token.end)
      };
    }
  }
};
</script>

<style scoped>
.txt {
  width: 100%;
  height: 100%;
}
</style>
