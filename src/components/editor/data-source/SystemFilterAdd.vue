<template>
  <el-dialog :visible.sync="show" :title="title" :close-on-click-modal="false" append-to-body width="700px" top="0" :before-close="close">
    <div class="filter-content">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
        <el-form-item label="名称：" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="分类：" prop="name">
          <el-select class="w100" v-model="form.type" placeholder="请选择分类">
            <el-option v-for="opt in typeOpt" :value="opt.value" :label="opt.label" :key="opt.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参数字段：" prop="params">
          <el-form label-width="100px" size="mini" class="params-wrap">
            <div class="column-wrap">
              <div class="column-item" v-for="(item, index) in form.params" :key="index">
                <el-form-item label="参数类型：">
                  <el-select class="w100" v-model="item.source" @change="val => sourceChange(val, item)">
                    <el-option value="field" label="字段"></el-option>
                    <el-option value="callback" label="回调"></el-option>
                    <el-option value="form" label="表单"></el-option>
                    <el-option value="cascade" label="级联"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="表单类型：" v-if="item.source === 'form'">
                  <el-select class="w100" v-model="item.formType">
                    <el-option value="input" label="输入框"></el-option>
                    <el-option value="select" label="下拉框"></el-option>
                    <el-option value="textarea" label="文本域"></el-option>
                    <el-option value="js" label="js代码"></el-option>
                  </el-select>
                  <el-form-item v-if="item.formType === 'select'" label="选项：" label-width="70px" style="margin-top:10px;margin-bottom:0px;">
                    <div class="option-wrap">
                      <div class="option-item" v-for="(option, idx) in item.options" :key="idx">
                        <div class="opt-item">
                          <span class="opt-label">标签：</span>
                          <el-input v-model="option.label" class="opt-content" size="mini"></el-input>
                        </div>
                        <div class="opt-item">
                          <span class="opt-label">值：</span>
                          <el-input v-model="option.value" class="opt-content" size="mini"></el-input>
                        </div>
                        <span class="del-option el-icon-remove-outline" @click="deleteOption(item.options, idx)"></span>
                      </div>
                    </div>
                    <el-button size="mini" type="plain" @click="addOption(item.options)">新增</el-button>
                  </el-form-item>
                </el-form-item>
                <el-form-item label="参数名称：">
                  <el-input class="w100" v-model="item.name"></el-input>
                </el-form-item>
                <el-form-item label="参数key ：">
                  <el-input class="w100" v-model="item.key"></el-input>
                </el-form-item>
                <el-form-item label="单/多选：" v-if="item.source === 'field' || item.source === 'callback'">
                  <el-select class="w100" v-model="item.type">
                    <el-option value="string" label="单选"></el-option>
                    <el-option value="array" label="多选"></el-option>
                  </el-select>
                </el-form-item>
                <span class="close el-icon-circle-close" @click="deleteItem(index)"></span>
              </div>
            </div>
          </el-form>
          <el-button type="text" @click="addParams"><i class="el-icon-circle-plus"></i> 新增字段</el-button>
        </el-form-item>
        <el-form-item label="代码：" prop="content">
          <div class="func-wrap">
            <div class="func-tit">
              <span>function filter(<span>data</span>,<span> callbackArgs</span>,<span> fieldSetting</span>){</span>
            </div>
            <div class="code-mirr">
              <CodeEditor ref="code" v-model="form.content" />
            </div>
            <div class="func-tit">
              <span>}</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="描述：" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="描述信息"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="plain" size="mini" @click="submit">确认</el-button>
      <el-button type="text" size="mini" @click="close">取消</el-button>
    </div>
    <SeatomLoading v-if="loading" />
  </el-dialog>
</template>

<script>
import CodeEditor from './CodeEditor';
import { createSystemfilter, updateSystemfilter } from '@/api/filter';
import { randomStr, Encrypt } from '@/utils/base';
import { filterType } from '@/common/constants'

export default {
  name: 'SystemFilterAdd', // 新增内置过滤器
  components: {
    CodeEditor
  },
  data () {
    return {
      show: false,
      title: '新增内置过滤器',
      form: {
        id: '',
        name: '',
        type: '',
        params: [],
        content: 'return data;',
        description: ''
      },
      rules: {
        name: [
          { required: true, message: '名称必填', trigger: 'change' }
        ],
        type: [
          { required: true, message: '分类必选', trigger: 'change' }
        ],
        description: [
          { min: 0, max: 256, message: '长度超出限制', trigger: 'change' }
        ]
      },
      typeOpt: filterType.slice(0, filterType.length - 1),
      loading: false
    }
  },
  methods: {
    showDialog () {
      this.show = true;
      this.form.id = '';
    },
    showEdit (data) {
      this.show = true;
      this.$nextTick(() => {
        this.form.id = data.id;
        this.form.name = data.name;
        this.form.type = data.type;
        this.form.params = _.cloneDeep(data.params);
        this.form.content = data.content;
        this.form.description = data.description;
      })
    },
    close () {
      this.show = false;
      this.resetForm();
    },
    resetForm () {
      this.$refs.form.resetFields();
    },
    addParams () {
      this.form.params.push({
        name: '',
        key: '',
        source: 'field',
        formType: '',
        type: 'string',
        options: []
      })
    },
    deleteItem (index) {
      this.form.params.splice(index, 1);
    },
    sourceChange (type, item) {
      item.formType = '';
      item.options = [];
    },
    addOption (options) {
      const obj = {
        id: 'option_' + randomStr(5),
        label: '',
        value: ''
      }
      options.push(obj);
    },
    deleteOption (options, index) {
      options.splice(index, 1);
    },
    async submit () {
      if (!this.form.id) {
        const data = {
          name: this.form.name,
          type: this.form.type,
          params: this.form.params,
          content: Encrypt(this.form.content),
          description: this.form.description
        }
        try {
          this.loading = true;
          const res = await createSystemfilter(data);
          if (res && res.success) {
            this.$emit('refresh');
            this.close()
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      } else {
        const data = {
          name: this.form.name,
          type: this.form.type,
          params: this.form.params,
          content: Encrypt(this.form.content),
          description: this.form.description
        }
        try {
          this.loading = true;
          const res = await updateSystemfilter(data, { id: this.form.id });
          if (res && res.success) {
            this.$emit('refresh');
            this.close()
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-content {
  max-height: 60vh;
  padding-right: 10px;
  overflow: auto;
}
::v-deep .el-dialog .el-dialog__body {
  padding: 15px 20px 10px 20px
}
.func-wrap {
  .func-tit {
    font-size: 12px;
    color: #999;
    line-height: 30px;
  }
}
.column-wrap {
  .column-item {
    position: relative;
    flex: none;
    border: 1px solid #434b55;
    padding: 10px 50px 0 0;
    margin-bottom: 10px;
    &:hover {
      .close {
        display: block;
      }
    }
    .close {
      display: none;
      position: absolute;
      top: 5px;
      right: 5px;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.params-wrap {
  ::v-deep {
    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
.option-wrap {
  .option-item {
    position: relative;
    display: flex;
    padding: 5px 10px;
    border: 1px solid #393b4a;
    margin-bottom: 10px;
    .opt-item {
      flex: 1;
      display: flex;
      align-items: center;
      .opt-label {
        width: 50px;
        font-size: 12px;
        color: #9a9c9f;
        text-align: right;
      }
      .opt-content {
        flex: 1;
      }
    }
    .del-option {
      position: absolute;
      top: 50%;
      right: -20px;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }
}
.w100 {
  width: 100%;
}
</style>
