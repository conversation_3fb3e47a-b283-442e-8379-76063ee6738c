<template>
  <div>
    <el-dialog class="create-datasource" :visible.sync="show" title="计算字段" append-to-body width="600px" :close-on-click-modal="false" @close="closeDialog" top="0">
     <el-form label-position="right"  ref="form" size="mini" :model="form" :rules="rules" label-width="88px" label-suffix="：">
      <el-row>
        <el-col :span="24">
            <el-form-item label="字段名称" prop="field.name">
                <el-input v-model="form.field.name" size="mini"></el-input>
            </el-form-item>
        </el-col>
        <el-col :span="24">
            <el-form-item label="字段类型" prop="field.data_type">
                <el-select v-model="form.field.data_type" size="mini" style="width:100%;">
                    <el-option v-for="opt in fieldType" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                </el-select>
            </el-form-item>
        </el-col>
          <el-col :span="24">
            <div class="eidtor-wrap">
              <el-form-item label="" label-width="0" prop="field.formula">
                 <codemirror ref="code" :options="cmOptions" v-model="form.field.formula" @blur="getfile" />
              </el-form-item>
            </div>
        </el-col>
      </el-row>
       <el-row class= "datesource-table" >
          <el-col :span="12" class="table-left" >
            <div class="table-left-title" >
              函数
            </div>
            <el-input v-model="filterText" size="mini" style="width:100%;margin-top:15px">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
            <div class="field-ul">
              <div class="field-li" v-for="(item,index) in numCalculationList" :key="index" @click="setFormula(item)">
                <el-tooltip effect="light" placement="left-start">
                  <div slot="content" class="field-content">
                    <span><span class="title">用法：</span>{{item.usage}}</span>
                    <br/>
                    <span><span class="title">说明：</span>{{item.desc}}</span>
                    <br/>
                    <span><span class="title">示例：</span>{{item.demo}}</span>
                  </div>
                  <span>
                    {{item.name}}
                  </span>
                </el-tooltip>
              </div>
            </div>
          </el-col>
          <el-col :span="12" class="table-right">
            <div class="table-right-title" >
              字段名
            </div>
            <el-input v-model="fieldName" size="mini" style="width:100%;margin-top:15px">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
            <div class="field-ul">
              <div class="field-li" v-for="item in filterFieldName" :key="item.fid" @click="setField(item)">{{item.name}}</div>
            </div>
          </el-col>
       </el-row>
      </el-form>
      <div slot="footer">
        <el-button type="light-blue" size="medium" @click="submit" :loading="loading">确定</el-button>
        <el-button type="text" size="medium" @click="closeDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFunctionList, getFieldList } from '@/api/datastorage';
import { createCalculatefield } from '@/api/calculatefield'
import { codemirror } from 'vue-codemirror';
import { mapState } from 'vuex';
export default {
  name: 'CreateDataSource', // 创建数据源
  props: {},
  components: {
    codemirror
  },
  data () {
    return {
      fieldName: '',
      numCalculation: [],
      cmOptions: {
        tabSize: 2,
        mode: 'text/javascript',
        theme: 'base16-dark',
        lineNumbers: false,
        line: true,
        lineWrapping: true
      },
      filterText: '',
      show: false,
      title: '选择工作表',
      form: {
        workspaceId: '',
        tbId: '',
        field: {
          name: '',
          formula: '',
          data_type: '',
          field_type: 1,
          fid: ''
        }
      },
      fileList: [],
      rules: {
        'field.data_type': [
          { required: true, message: '字段类型必填', trigger: 'change' }
        ],
        'field.name': [
          { required: true, message: '字段名称必填', trigger: 'blur' }
        ],
        'field.formula': [
          { required: true, message: '公式必填', trigger: 'blur' }
        ]
      },
      fieldType: [
        { value: 'date', label: '日期' },
        { value: 'number', label: '数值' },
        { value: 'string', label: '字符串' }
      ],
      loading: false,
      loading2: false,
      fieldList: [],
      calfieldlist: {}
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    numCalculationList: function () {
      if (!this.filterText) return this.numCalculation
      return this.numCalculation.filter(item => {
        return item.name.indexOf(this.filterText) !== -1
      })
    },
    filterFieldName: function () {
      if (!this.fieldName) return this.fieldList
      return this.fieldList.filter(item => {
        return item.name.indexOf(this.fieldName) !== -1
      })
    }
  },
  methods: {
    async getFieldList (tbid) {
      const data = {
        type: 'dmc',
        tbid,
        needCalculate: 0,
        workspaceId: this.screenInfo.workspaceId
      }
      const res = await getFieldList(data);
      if (res && res.success) {
        res.data.forEach(item => {
          this.fieldList = [...this.fieldList, ...item.fields]
        });
      }
    },
    async getFunctionList () {
      const data = {
        type: 'dmc'
      }
      const res = await getFunctionList(data);
      if (res && res.success) {
        this.numCalculation = res.data
      }
    },
    getFileAndFormula () {
    },
    getfile () {
    },
    setFormula (item) {
      const str = item.name
      const codemirror = this.$refs.code.codemirror
      codemirror.focus()
      if (this.defaultFormula === '') {
        codemirror.replaceRange(`${str}()`, { ch: 0, line: 0 })
        codemirror.setCursor({ ch: str.length + 1, line: 0 })
      } else {
        const obj = codemirror.getCursor()
        codemirror.replaceRange(`${str}()`, { ch: obj.ch, line: 0 })
        codemirror.setCursor({ ch: obj.ch + str.length + 1, line: 0 })
      }
    },
    setField (item) {
      const str = item.name
      const codemirror = this.$refs.code.codemirror
      codemirror.focus()
      if (this.defaultFormula === '') {
        codemirror.replaceRange(`[${str}]`, { ch: 0, line: 0 })
        codemirror.setCursor({ ch: str.length + 2, line: 0 })
      } else {
        const obj = codemirror.getCursor()
        codemirror.replaceRange(`[${str}]`, { ch: obj.ch, line: 0 })
        codemirror.setCursor({ ch: obj.ch + str.length + 2, line: 0 })
      }
    },
    closeDialog () {
      this.form.field.name = ''
      this.form.field.formula = ''
      this.form.field.data_type = ''
      this.form.field.fid = null
      this.fieldList = []
      this.show = false;
    },
    showDialog (data, item) {
      this.show = true;
      this.form.tbId = data.tbId
      this.form.workspaceId = this.screenInfo.workspaceId
      this.form.field = _.cloneDeep(item) || this.form.field
      this.getFieldList(data.tbId)
      this.getFunctionList()
    },
    async createCalculatefield () {
      const data = this.form
      const res = await createCalculatefield(data);
      if (res && res.success) {
        this.closeDialog()
        this.calfieldlist = res.data
        this.$emit('update', this.calfieldlist);
      } else if (res.code === 602) {
        this.$message.error('字段计算公式填写错误')
      } else {
        this.$message.error(res.message)
      }
    },
    submit () {
      const flag = this.fieldList.some(item => {
        return item.name === this.form.field.name
      })
      if (flag) {
        this.$message.error('计算字段名称重复')
        return
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          this.createCalculatefield()
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.create-datasource {
  .eidtor-wrap {
    height: 75px;
    margin-bottom: 15px;
    ::v-deep {
    .CodeMirror {
      height: 75px;
    }
  }
  }
  .field-ul {
    height: 150px;
    overflow-y: auto;
    margin-top: 8px;
    color: rgba(255, 255, 255, 0.7);
    .field-li {
      height: 36px;
      line-height: 36px;
      padding-left: 8px;
    }
    .field-li:hover {
      background: rgba(204, 219, 255, 0.06);
    }
  }
  .filter-tree{
      margin-top: 34px;
  }
  .datesource-table{
      background: rgba(66, 74, 90, 0.3);
      border-radius: 4px;
      padding: 15px;
      .table-left {
        padding-right: 15px;
        .table-left-title {
          margin-left: -5px;
          margin-bottom: 8px;
          color: rgba(255, 255, 255, 0.7);
        }
      }
      .table-right {
        padding-left: 15px;
        border-left:1px solid rgba(63, 71, 87, 0.5);
        .table-right-title {
          margin-left: -5px;
          margin-bottom: 8px;
          color: rgba(255, 255, 255, 0.7);
        }
      }
  }
  ::v-deep {
    .el-form-item__label {
      color: #fafafa;
      font-size: 12px;
    }
    .el-upload-dragger {
      width: 300px;
      height: 140px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        font-size: 12px;
      }
    }
    .el-upload-dragger .el-icon-upload {
      font-size: 50px;
      margin: 20px 0 16px;
    }

  }
}
</style>
