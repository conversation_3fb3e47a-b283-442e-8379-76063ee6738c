<template>
  <div class="filter-item">
    <div class="f-item-title">
      <span v-if="item.id" class="el-icon-rank move"></span>
      <el-checkbox
        v-if="item.id"
        v-model="item.enable"
        @change="(val) => enableChange(val, item)"
        class="checkbox"
      ></el-checkbox>
      <div class="title-right">
        <div class="filter-name">
          <span
            class="collapse"
            :class="item.show ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
            @click="toggle(item)"
          ></span>
          <span v-if="!item.editable" class="name" :title="item.name">{{
            item.name
          }}</span>
          <el-input
            v-else
            v-model="item.name"
            v-select
            class="name"
            @blur="onBlur(item)"
          ></el-input>
          <span class="ico el-icon-edit" @click="editName(item)"></span>
          <span v-if="!item.id" class="text"><i class="dot"></i> 未保存</span>
        </div>
        <div class="r-oprate">
          <span class="use-info"></span>
          <span
            class="ico el-icon-delete"
            @click="deleteFilter(item, index)"
          ></span>
        </div>
      </div>
    </div>
    <div class="f-item-content" :class="{ collapsed: !item.show }">
      <div class="content-wrap">
        <template v-if="item.type === 'custom' || item.type === 'template'">
          <div class="form-control" v-if="!isIndicator">
            <div class="form-title">回调字段</div>
            <div class="form-content">
              <div class="tag-wrap">
                <div
                  class="tag-item"
                  v-for="(callItem, idx) in item.callbackKeys"
                  :key="idx"
                >
                  <span class="tag">{{ callItem }}</span>
                  <span
                    class="el-icon-close"
                    @click="delCallback(item, idx)"
                  ></span>
                </div>
                <el-input
                  v-if="item.showInput"
                  v-model="callVal"
                  v-select
                  class="tag-input"
                  size="mini"
                  @blur="callkeyBlur(item)"
                ></el-input>
                <el-button type="plain" size="mini" @click="addCallback(item)"
                  >添加回调</el-button
                >
              </div>
            </div>
          </div>
          <div class="func-wrap">
            <div class="func-tit">
              <span
                >function filter(<span>data</span>,<span v-if="!isIndicator"> callbackArgs<span>,</span></span
                ><span> fieldSetting</span>){</span
              >
            </div>
            <div class="code-mirr">
              <CodeEditor v-model="content" />
            </div>
            <div class="func-tit">
              <span>}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div
            class="form-control"
            v-for="item in systemParams"
            :key="item.key"
          >
            <div class="form-title">{{ item.name }}</div>
            <div class="form-content">
              <div
                class="select-row"
                v-if="item.source === 'field' || item.source === 'callback'"
              >
                <el-select
                  class="select"
                  v-model="item.value"
                  multiple
                  :multiple-limit="item.type === 'array' ? 0 : 1"
                >
                  <template v-if="item.source === 'field'">
                    <el-option
                      v-for="(opt, idx) in prevFiledOptions"
                      :key="idx + 'field'"
                      :value="opt.name"
                      :label="`${opt.name}`"
                    ></el-option>
                  </template>
                  <el-option
                    v-for="(opt, idx) in callbacksOpt"
                    :key="idx + 'callback'"
                    :value="opt.variableName"
                    :label="`${opt.variableName}`"
                    v-else
                  ></el-option>
                </el-select>
              </div>
              <div class="row-wrap" v-else-if="item.source === 'form'">
                <div class="select-row" v-if="item.formType === 'select'">
                  <el-select v-model="item.value[0]">
                    <el-option
                      v-for="option in item.options"
                      :key="option.id"
                      :value="option.value"
                      :label="option.label"
                    ></el-option>
                  </el-select>
                </div>
                <div class="select-row" v-else-if="item.formType === 'textarea'">
                  <el-input type="textarea" v-model="item.value[0]" :rows="4"></el-input>
                </div>
                <div class="select-row" v-else-if="item.formType === 'js'">
                  <CodeEditor v-model="item.value[0]" />
                </div>
                <div class="select-row" v-else>
                  <el-input v-model="item.value[0]"></el-input>
                </div>
              </div>
              <div class="row-wrap" v-else-if="item.source === 'cascade'">
                <div
                  class="select-row"
                  v-for="itemOpt in item.options"
                  :key="itemOpt.id"
                >
                  <el-select class="select" v-model="itemOpt.value">
                    <el-option
                      v-for="(opt, idx) in prevFiledOptions"
                      :key="idx + 'field'"
                      :value="opt.name"
                      :label="`${opt.name}`"
                    ></el-option>
                  </el-select>
                  <span
                    class="icon el-icon-circle-close"
                    @click="deleteCascade(item.options, itemOpt.id)"
                  ></span>
                </div>
                <div class="cas-tips">
                  <el-button
                    size="mini"
                    type="plain"
                    @click="addCascade(item.options)"
                    >新增字段</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </template>
        <div class="btn-wrap">
          <el-button
            type="plain"
            size="mini"
            @click="filterTest(item, index)"
            >测试</el-button
          >
          <div class="r-btn">
            <el-button
              type="plain"
              plain
              size="mini"
              @click="cancelFilter(item)"
              >取消</el-button
            >
            <el-dropdown trigger="click" size="small" style="margin-left: 10px;" @command="handleSave($event, item, index)" v-if="item.type === 'custom' && !item.id">
              <el-button
                type="primary"
                size="mini"
                :loading="loading"
                >保存</el-button
              >
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="save">仅保存</el-dropdown-item>
                <el-dropdown-item command="saveTemplate">保存并生成自定义类</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              v-else
              type="primary"
              size="mini"
              @click="saveFilter(item, index)"
              :loading="loading"
              >保存</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CodeEditor from './CodeEditor';
import { mapState, mapGetters } from 'vuex';
import dataUtil from '@/utils/data';
import { randomStr, Encrypt, handleDecrypt } from '@/utils/base';
import CallbackManager from '@/lib/CallbackManager';
import { createFilter, updateFilter, createSystemfilter } from '@/api/filter';
import { parse } from '@babel/parser';
import traverse from '@babel/traverse';
import generate from '@babel/generator';
export default {
  name: 'FilterItem',
  props: ['item', 'index', 'filterList', 'sourceData'],
  components: {
    CodeEditor
  },
  data () {
    return {
      callVal: '',
      loading: false,
      content: ''
    };
  },
  computed: {
    ...mapState({
      screenInfo: (state) => state.editor.screenInfo,
      screenComs: (state) => state.editor.screenComs
    }),
    ...mapGetters('editor', ['currentCom']),
    isIndicator () {
      return !!this.$store.state.editor.indicatorId
    },
    systemParams () {
      return this.item.systemParams || [];
    },
    prevFiledOptions () {
      let result = [];
      try {
        const keys = [];
        const sourceData = JSON.parse(this.sourceData);
        let prevFilters = this.filterList.slice(0, this.index);
        prevFilters = prevFilters.filter((filter) => filter.enable);
        const filterData = dataUtil.filterData(sourceData || [], prevFilters);
        filterData.forEach((item) => {
          const key = Object.keys(item);
          keys.push(...key);
        });
        const uniKeys = Array.from(new Set(keys));
        result = uniKeys.map((key, idx) => {
          return {
            id: idx,
            name: key
          };
        });
      } catch (e) {}
      return result;
    },
    callbacksOpt () {
      // 回调字段下拉项
      const options = [];
      const coms = Object.values(this.screenComs);
      coms.forEach((item) => {
        const callbackParams = item.interactionConfig.callbackParams;
        if (callbackParams && callbackParams.length) {
          options.push(...callbackParams);
        }
      });
      return options;
    }
  },
  directives: {
    select: {
      inserted: function (el) {
        el.querySelector('input').select();
      }
    }
  },
  created () {
    if (this.item) {
      this.content = handleDecrypt(this.item.content) || this.item.content;
    }
  },
  methods: {
    enableChange (val, item) {
      this.$emit('enableChange', val, item);
    },
    toggle (item) {
      this.$set(item, 'show', !item.show);
    },
    onBlur (item) {
      // if (!item.id) {
      //   if (item.name !== '新建过滤器') {
      //     this.saveFilter(item);
      //   }
      // } else {
      //   this.saveFilter(item);
      // }
      item.editable = false;
    },
    editName (item) {
      this.$set(item, 'editable', true);
    },
    deleteFilter (item, index) {
      this.$emit('deleteFilter', item, index);
    },
    delCallback (item, idx) {
      item.callbackKeys.splice(idx, 1);
    },
    callkeyBlur (item) {
      if (this.callVal) {
        item.callbackKeys.push(this.callVal);
        this.callVal = '';
      }
      item.showInput = false;
    },
    addCallback (item) {
      this.$set(item, 'showInput', true);
    },
    addCascade (options) {
      // 新增级联参数
      const opt = {
        id: 'cascade_' + randomStr(5),
        value: ''
      };
      options.push(opt);
    },
    deleteCascade (options, id) {
      // 删除级联参数
      const index = options.findIndex((item) => item.id === id);
      if (index > -1) {
        options.splice(index, 1);
      }
    },
    // 校验
    filterTest (item, index) {
      if (this.checkFilter(item, index)) {
        item.content = this.content;
        this.$emit('filterTest', item, index);
      }
    },
    cancelFilter (item) {
      item.show = false;
    },
    // 数据过滤器执行
    checkFilterData (
      data = [],
      validFilters = [],
      callbackManager = new CallbackManager({})
    ) {
      let newData = _.cloneDeep(data);
      const filterFuncs = validFilters.map((f) => {
        // eslint-disable-next-line
        return new Function("data", "callbackArgs", "fieldSetting", f.content);
      });
      for (let i = 0; i < filterFuncs.length; i++) {
        const fieldSetting = {};
        const systemParams = validFilters[i].systemParams || [];
        if (systemParams.length) {
          systemParams.forEach((item) => {
            if (item.source === 'callback') {
              validFilters[i].callbackKeys.push(...item.value);
            }
            if (['field', 'callback', 'form'].includes(item.source)) {
              fieldSetting[item.key] = item.value;
            } else if (item.source === 'cascade') {
              const options = item.options;
              fieldSetting[item.key] = options.map((obj) => obj.value);
            }
          });
        }
        const callbackArgs =
          callbackManager && callbackManager.pick(validFilters[i].callbackKeys);
        newData = filterFuncs[i](newData, callbackArgs, fieldSetting);
      }
      return newData;
    },
    // 过滤器语法检测函数
    checkFilter (item, index) {
      let isAjax = false;
      const originSend = window.XMLHttpRequest.prototype.send;
      const originFetch = window.fetch;
      try {
        // 判断是否死循环 __count 执行大于 10000次 则抛出错误
        const prefix = `
          let __count = 0
          const __detectInfiniteLoop = () => {
            if (__count > 10000) {
              throw new Error('Infinite Loop detected')
            }
            __count += 1
          }
        `;
        const detector = parse('__detectInfiniteLoop()');
        // allowReturnOutsideFunction 允许函数外面写return
        // errorRecovery 是否出现错误后，不停止解析
        const ast = parse(this.content, {
          allowReturnOutsideFunction: true,
          errorRecovery: true
        });
        // 找到 while/for/do..while 的位置,并插入 detector
        traverse(ast, {
          ForStatement: function (path) {
            path.node.body.body.push(...detector.program.body);
          },
          WhileStatement: function (path) {
            path.node.body.body.push(...detector.program.body);
          },
          DoWhileStatement: function (path) {
            path.node.body.body.push(...detector.program.body);
          }
        })
        const newCode = prefix + generate(ast).code
        const cloneItem = _.cloneDeep(item)
        cloneItem.content = newCode
        const prevFilters = this.filterList.slice(0, index).filter(filter => filter.enable);
        prevFilters.forEach(item => { item.content = handleDecrypt(item.content) || item.content })
        // const filters = [...prevFilters, cloneItem]
        try {
          // 判断过滤器内是否有发送请求 - 重写 send 和 fetch 方法
          window.XMLHttpRequest.prototype.send = () => {
            isAjax = true;
            this.$message.error('Cannot send request');
            return false;
          };
          window.fetch = () => {
            isAjax = true;
            this.$message.error('Cannot send request');
            return false;
          }
          // const sourceData = JSON.parse(this.sourceData);
          // const result = this.checkFilterData(sourceData, filters);
          window.fetch = originFetch;
          window.XMLHttpRequest.prototype.send = originSend;
          if (isAjax) {
            return false;
          }
          // if (result instanceof Array) {
          //   // if (result.length) {
          //   //   const data = result.some(item => item.constructor !== Object)
          //   //   if (data) {
          //   //     this.$message.error('data in wrong format')
          //   //     return false
          //   //   }
          //   // }
          // } else {
          //   this.$message.error('data in wrong format')
          //   return false
          // }
          return true && !isAjax
        } catch (error) {
          window.fetch = originFetch;
          window.XMLHttpRequest.prototype.send = originSend;
          this.$message.error(error.message);
          return false;
        }
      } catch (error) {
        window.fetch = originFetch;
        window.XMLHttpRequest.prototype.send = originSend;
        this.$message.error(error.message);
        return false;
      }
    },
    handleSave (command, item, index) {
      if (command === 'save') {
        this.saveFilter(item, index)
      } else if (command === 'saveTemplate') {
        this.saveFilter(item, index, true)
      }
    },
    // 保存
    saveFilter: _.debounce(async function (item, index, saveTemplate = false) {
      this.loading = true;
      if (!this.checkFilter(item, index)) {
        this.loading = false;
        return;
      }
      item.content = this.content;
      if (item.type === 'system') {
        const systemParams = item.systemParams;
        // 字段/回调类型的参数 校验value必填
        const fieldList = systemParams.filter(
          (item) => item.source === 'field' || item.source === 'callback'
        );
        if (fieldList.length) {
          if (!fieldList.every((item) => item.value && item.value.length > 0)) {
            this.$message.warn('请选择参数字段/回调字段！');
            this.loading = false;
            return;
          }
        }
        // 级联类型参数 校验options非空且value必填
        const cascadeList = systemParams.filter(
          (item) => item.source === 'cascade'
        );
        if (cascadeList.length) {
          const isOk = cascadeList.every((item) => {
            return (
              item.options.length && item.options.every((opt) => !!opt.value)
            );
          });
          if (!isOk) {
            this.$message.warn('请填写级联字段！');
            this.loading = false;
            return;
          }
        }
      }
      if (!item.id) {
        const data = {
          componentId: this.currentCom.id,
          screenId: this.currentCom.screenId,
          callbackKeys: item.callbackKeys,
          name: item.name,
          content: Encrypt(item.content),
          enable: item.enable,
          type: item.type,
          systemParams: item.systemParams
        };
        try {
          const res = await createFilter(data, {});
          let res2 = {};
          if (item.type === 'custom' && saveTemplate) {
            const templateData = {
              name: item.name,
              type: '4',
              params: [],
              content: Encrypt(item.content),
              description: ''
            }
            res2 = await createSystemfilter(templateData);
          }
          if (res && res.success) {
            this.$store
              .dispatch('editor/getScreenFilters', {
                screenId: this.currentCom.screenId
              })
              .then(() => {
                this.$message.success('新增成功');
                this.$emit('refresh', res2.success);
              });
          }
        } catch (e) {
          this.loading = false;
        }
      } else {
        const data = {
          id: item.id,
          componentId: this.currentCom.id,
          screenId: this.currentCom.screenId,
          callbackKeys: item.callbackKeys,
          name: item.name,
          content: Encrypt(item.content),
          enable: item.enable,
          type: item.type,
          systemParams: item.systemParams
        };
        const res = await updateFilter(data, {});
        if (res && res.success) {
          this.$store
            .dispatch('editor/getScreenFilters', {
              screenId: this.currentCom.screenId
            })
            .then(() => {
              this.$message.success('修改成功');
              this.$emit('refresh');
            });
        } else {
          this.loading = false;
          this.$message.warn(res.message);
        }
        this.loading = false;
      }
    }, 300)
  }
};
</script>

<style lang="scss" scoped>
.filter-item {
  position: relative;
  margin-bottom: 10px;
  .f-item-title {
    display: flex;
    height: 40px;
    background-color: #2d2f38;
    border: 1px solid #393b4a;
    border-bottom: none;
    .move {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 20px;
      height: 40px;
      padding-left: 10px;
      font-size: 18px;
      color: #999;
      cursor: move;
    }
    .checkbox {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .title-right {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      border-left: 1px solid #393b4a;
      .filter-name {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #fafafa;
        .collapse {
          cursor: pointer;
        }
        .name {
          width: 140px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-left: 6px;
        }
        .ico {
          margin-left: 5px;
          &:hover {
            color: var(--seatom-main-color);
          }
        }
        .text {
          font-size: 12px;
          margin-left: 10px;
        }
        .dot {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: red;
        }
      }
      .r-oprate {
        font-size: 12px;
        color: #999;
        .use-info {
          em {
            color: var(--seatom-main-color);
            font-style: normal;
          }
        }
        .ico {
          font-size: 14px;
          margin-left: 10px;
          &:hover {
            color: var(--seatom-main-color);
          }
        }
      }
    }
  }
  .f-item-content {
    border: 1px solid #393b4a;
    border-top: none;
    overflow-x: hidden;
    overflow-y: auto;
    &.collapsed {
      height: 0;
      overflow: hidden;
    }
    .content-wrap {
      padding: 16px;
    }
  }
  .form-control {
    display: flex;
    margin-bottom: 10px;
    .form-title {
      width: 80px;
      font-size: 12px;
      color: #fafafa;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .form-content {
      flex: 1;
      .row-wrap {
        .select-row {
          &:hover {
            .icon {
              display: inline-block;
            }
          }
          .icon {
            display: none;
            cursor: pointer;
          }
        }
        .select + .select {
          margin-top: 10px;
        }
      }
      .select-row {
        margin-bottom: 10px;
        .select {
          width: 240px;
          margin-right: 10px;
        }
      }
    }
  }
  .tag-wrap {
    margin-right: 10px;
    .tag-item {
      display: inline-block;
      background: #393b4a;
      color: #fafafa;
      line-height: 24px;
      font-size: 12px;
      border: none;
      padding: 2px 7px;
      margin-bottom: 6px;
      margin-right: 6px;
    }
    .tag-input {
      width: 70px;
      margin-right: 6px;
    }
  }
  .func-wrap {
    .func-tit {
      font-size: 12px;
      color: #999;
      line-height: 30px;
    }
  }
  .btn-wrap {
    display: flex;
    justify-content: space-between;
  }
}
</style>
