<template>
  <div @keydown.stop :class="{'jsoneditor-box': isTemp}">
    <div class="jsoneditor-vue" :style="{ height: height }"></div>
  </div>
</template>

<script>
import JsonEditor from 'jsoneditor';
import 'jsoneditor/dist/jsoneditor.css';
require('brace/theme/monokai');
export default {
  name: 'JsonEditor',
  props: {
    value: [String, Number, Object, Array],
    expandedOnStart: {
      type: Boolean,
      default: true
    },
    mode: {
      type: String,
      default: 'tree'
    },
    modes: {
      type: Array,
      default: function () {
        return ['tree', 'code', 'form', 'text', 'view'];
      }
    },
    lang: {
      type: String,
      default: 'zh-CN'
    },
    height: {
      type: [Number, String],
      default: '300px'
    },
    isTemp: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      immediate: true,
      async handler (val) {
        if (!this.internalChange) {
          await this.setEditor(val);

          this.error = false;
          this.expandAll();
        }
      },
      deep: true
    }
  },
  data () {
    return {
      editor: null,
      error: false,
      json: this.value,
      internalChange: false,
      expandedModes: ['tree', 'view', 'form'],
      isFocus: false
    };
  },
  mounted () {
    const self = this;

    const options = {
      mode: this.mode,
      modes: this.modes, // allowed modes
      search: false,
      navigationBar: false,
      enableSort: false,
      enableTransform: false,
      language: this.lang,
      theme: 'ace/theme/monokai',
      onChange () {
        try {
          const json = self.editor.get();
          self.json = json;
          self.error = false;
          // self.$emit('json-change', json);
          self.internalChange = true;
          self.$emit('input', json);
          self.$nextTick(function () {
            self.internalChange = false;
          });
        } catch (e) {
          self.error = true;
          self.$emit('has-error', e);
        }
      },
      onModeChange () {
        self.expandAll();
      },
      onBlur () {
        if (!this.isFocus) return
        try {
          const json = self.editor.get();
          self.json = json;
          self.error = false;
          self.$emit('json-blur', json);
          self.$emit('json-change', json);
        } catch (e) {
          self.error = true;
          self.$emit('has-error', e);
        }
      },
      onFocus () {
        this.isFocus = true
      },
      onNodeName ({ path, type, size }) {
        if (type === 'array') {
          return `数组${size}`
        }
        if (type === 'object') {
          return `对象${size}`
        }
      }
    };

    this.editor = new JsonEditor(
      this.$el.querySelector('.jsoneditor-vue'),
      options,
      this.value
    );
  },
  methods: {
    expandAll () {
      if (
        this.expandedOnStart &&
        this.expandedModes.includes(this.editor.getMode())
      ) {
        this.editor.expandAll();
      }
    },

    async setEditor (value) {
      if (this.editor) this.editor.set(value);
    }
  }
};
</script>

<style lang="scss" scoped>
.jsoneditor-box {
  height: 100%;
  ::v-deep {
    div.jsoneditor,
    div.jsoneditor-menu {
      border-color: #16181a;
    }
    div.jsoneditor-menu {
      background-color: #16181a;
    }
    div.ace_gutter {
      background-color: #252b33;
    }
    .ace-monokai {
      background-color: #181d24;
    }
  }
}
::v-deep {
  div.jsoneditor,
  div.jsoneditor-menu {
    border-color: #4b4b4b;
  }
  div.jsoneditor-menu {
    background-color: #4b4b4b;
  }
  div.jsoneditor-tree,
  div.jsoneditor textarea.jsoneditor-text {
    background-color: #272822;
    color: #ffffff;
  }
  div.jsoneditor-field,
  div.jsoneditor-value {
    color: #ffffff;
  }
  table.jsoneditor-search div.jsoneditor-frame {
    background: #808080;
  }

  tr.jsoneditor-highlight,
  tr.jsoneditor-selected {
    background-color: #808080;
  }

  div.jsoneditor-field[contenteditable=true]:focus,
  div.jsoneditor-field[contenteditable=true]:hover,
  div.jsoneditor-value[contenteditable=true]:focus,
  div.jsoneditor-value[contenteditable=true]:hover,
  div.jsoneditor-field.jsoneditor-highlight,
  div.jsoneditor-value.jsoneditor-highlight {
    background-color: #808080;
    border-color: #808080;
  }

  div.jsoneditor-field.highlight-active,
  div.jsoneditor-field.highlight-active:focus,
  div.jsoneditor-field.highlight-active:hover,
  div.jsoneditor-value.highlight-active,
  div.jsoneditor-value.highlight-active:focus,
  div.jsoneditor-value.highlight-active:hover {
    background-color: #b1b1b1;
    border-color: #b1b1b1;
  }

  div.jsoneditor-tree button:focus {
    background-color: #868686;
  }

  div.jsoneditor-readonly {
    color: #acacac;
  }
  div.jsoneditor td.jsoneditor-separator {
    color: #acacac;
  }
  div.jsoneditor-value.jsoneditor-string {
    color: #00ff88;
  }
  div.jsoneditor-value.jsoneditor-object,
  div.jsoneditor-value.jsoneditor-array {
    color: #bababa;
  }
  div.jsoneditor-value.jsoneditor-number {
    color: #ff4040;
  }
  div.jsoneditor-value.jsoneditor-boolean {
    color: #ff8048;
  }
  div.jsoneditor-value.jsoneditor-null {
    color: #49a7fc;
  }
  div.jsoneditor-value.jsoneditor-invalid {
    color: white;
  }
  .jsoneditor-poweredBy {
    display: none;
  }
  .jsoneditor-statusbar {
    color: #bebebe;
    background-color: #4b4b4b;
    border-top: 1px solid #4b4b4b;
  }
  .jsoneditor-contextmenu .jsoneditor-menu {
    background: #272822;
    border: 1px solid #4b4b4b;
  }
  .jsoneditor-contextmenu .jsoneditor-menu button {
    color: #fff;
  }
  .jsoneditor-contextmenu .jsoneditor-menu button:hover, .jsoneditor-contextmenu .jsoneditor-menu button:focus {
    background-color: #737373;
  }
  .jsoneditor-contextmenu .jsoneditor-menu li ul li button:hover, .jsoneditor-contextmenu .jsoneditor-menu li ul li button:focus {
    background-color: #737373;
}
}
</style>
