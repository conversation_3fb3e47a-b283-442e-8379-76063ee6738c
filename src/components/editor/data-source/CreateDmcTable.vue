<template>
  <div>
    <el-dialog class="create-datasource" :visible.sync="show" :title="title" append-to-body width="550px" :close-on-click-modal="false" @close="closeDialog" top="0">
      <el-input
        placeholder="搜索"
        size="mini"
        @input="searchTree"
        v-model="filterText">
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
      <div
        element-loading-background="#1F2430"
        v-loading="treeLoading">
        <el-tree
          ref="tree"
          v-show="showSearchTree"
          class="filter-tree"
          :props="props"
          :data="filterTb"
          default-expand-all
          :filter-node-method="filterNode"
          @node-click="handleNodeClick">
          <span slot-scope="{ data }">
            <span class="tree-title">
                <i v-if="!data.tb_id"  class="el-icon-folder-opened"></i>
                <i v-else  class="el-icon-document icon-red"></i>

                {{data.name}}
            </span>
          </span>
        </el-tree>
        <div
          v-show="!showSearchTree">
          <el-tree
            class="filter-tree"
            :props="props"
            lazy
            :load="loadNode"
            @node-click="handleNodeClick">
            <span slot-scope="{ data }">
              <span class="tree-title">
                <i v-if="data.tb_id"  class="el-icon-document icon-red"></i>
                <i v-else  class="el-icon-folder-opened"></i>
                {{data.name}}
              </span>
            </span>
          </el-tree>
        </div>
      </div>
      <div slot="footer">
        <el-button type="light-blue" size="medium" @click="submit" :loading="loading">确定</el-button>
        <el-button type="text" size="medium" @click="closeDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTree, getTbList, searchTb } from '@/api/datastorage';
import { mapState } from 'vuex';
export default {
  name: 'CreateDmcTable',
  props: {},
  data () {
    return {
      treeLoading: false,
      showSearchTree: false,
      filterTb: [],
      tableData: {},
      childrenNode: [],
      folderList: [],
      filterText: '',
      props: {
        label: 'name',
        children: 'sub_folders'
      },
      count: 1,
      show: false,
      title: '选择工作表',
      fileList: [],
      dbList: [],
      loading: false,
      loading2: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    filterNode (value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    searchTree: _.debounce(function (filterStr) {
      this.searchTb(filterStr)
      this.showSearchTree = true
      if (this.filterText === '') {
        this.showSearchTree = false
      }
    }, 500),
    handleNodeClick (data) {
      this.tableData = data
    },
    async searchTb (filterStr) { // 获取数据源列表
      const data = {
        type: 'dmc',
        filterStr
      }
      this.treeLoading = true
      const res = await searchTb(data);
      if (res && res.success) {
        if (this.filterText === filterStr) {
          const folderList = res.data.folder_list
          const getList = function (folderList) {
            for (let index = 0; index < folderList.length; index++) {
              if (!folderList[index].sub_folders) {
                folderList[index].sub_folders = []
              }
              if (folderList[index].tb_list) {
                folderList[index].sub_folders = folderList[index].sub_folders.concat(folderList[index].tb_list)
              }
              if (folderList[index].sub_folders && folderList[index].sub_folders.length !== 0) {
                getList(folderList[index].sub_folders)
              }
            }
          }
          getList(folderList)
          this.filterTb = folderList
          this.treeLoading = false
        } else {
          this.filterTb = []
        }
      }
    },
    async getTree () { // 获取数据源列表
      const data = {
        type: 'dmc'
      }
      const res = await getTree(data);
      if (res && res.success) {
        this.treeLoading = false
        this.folderList = res.data.folder_list
      }
      this.treeLoading = false
    },
    async getTbList (folderId, filterTree) {
      const data = {
        folderId,
        filterTree,
        type: 'dmc'
      }
      const res = await getTbList(data);
      if (res && res.success) {
        this.childrenNode = [...res.data.sub_folders, ...res.data.tb_list]
      }
    },
    async loadNode (node, resolve) {
      if (node.level === 0) {
        this.treeLoading = true
        await this.getTree()
        return resolve(this.folderList);
      }
      if (!node.data.tb_id) {
        await this.getTbList(node.data.folder_id, node.data.type)
        return resolve(this.childrenNode);
      } else {
        return resolve([]);
      }
    },
    showDialog (type) {
      this.show = true;
      this.$nextTick(() => {
        this.getTree()
      })
    },
    closeDialog () {
      this.show = false;
      this.showSearchTree = false;
      this.folderList = [];
      this.filterText = ''
    },
    submit () {
      this.$emit('update', this.tableData);
      this.show = false;
      this.showSearchTree = false;
      this.folderList = [];
      this.filterText = ''
    }
  }
};
</script>

<style lang="scss" scoped>
.create-datasource {
    .filter-tree{
      margin-top: 14px;
      .tree-title {
        color:rgba(255, 255, 255, 0.7);
        padding-left: 10px;
      }
      .icon-red {
        color: #ad5419;
      }
    }
  ::v-deep {
    .el-form-item__label {
      color: #fafafa;
      font-size: 12px;
    }
    .el-upload-dragger {
      width: 300px;
      height: 140px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        font-size: 12px;
      }
    }
    .el-upload-dragger .el-icon-upload {
      font-size: 50px;
      margin: 20px 0 16px;
    }
  }
}
</style>
