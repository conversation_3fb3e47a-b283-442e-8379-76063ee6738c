<template>
  <div class="permission-data" :class="{'hide': !show}">
    <div class="page-title">
      <span class="icon el-icon-back" @click="show = false"></span>
      <span class="title">请求类型</span>
    </div>
    <div class="steps-content">
      <el-steps direction="vertical" :key="sourceType || 'stepkey'">
      <el-step title="配置数据">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">数据源类型</div>
            <div class="width-div">
              <el-select v-model="sourceType" size="mini" class="w100">
                <el-option
                  v-for="opt in sourceTypeOpt"
                  :key="opt.value"
                  :value="opt.value"
                  :label="opt.label">
                </el-option>
              </el-select>
            </div>
          </div>
          <template v-if="sourceType === 'api'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.api.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('api', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
        </div>
      </el-step>
      <el-step title="配置请求参数" v-if="sourceType === 'api'">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">Base URL</div>
            <div class="width-div">
              <el-input v-model="source.api.data.baseUrl" readonly size="mini" class="w100"></el-input>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">请求方式</div>
            <div class="width-div">
              <el-select v-model="source.api.data.method" @change="commitUpdate('api')" size="mini" class="w100">
                <el-option v-for="opt in methodOpt" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
              </el-select>
            </div>
          </div>
          <!-- <div class="config-control">
            <div class="config-title nowrap">
              动态参数
              <el-tooltip placement="top-start" content="如需在header或body中使用动态参数，可在此处设置参数并引用">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>
            <div class="width-div">
              <el-button type="plain" size="mini" @click="editDynamicParams()">编辑</el-button>
            </div>
          </div> -->
          <div class="eidtor-wrap">
            <div class="editor-title">请求头(JSON格式)</div>
            <CodeEditor v-model="source.api.data.headers" @blur="commitUpdate('api')" :options="editorOpt" key="api-code"/>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">路径</div>
            <div class="width-div">
              <el-input v-model="source.api.data.path" @blur="commitUpdate('api')" size="mini"></el-input>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">参数</div>
            <div class="width-div">
              <el-input v-model="source.api.data.params" @blur="commitUpdate('api')" size="mini"></el-input>
            </div>
          </div>
          <div class="eidtor-wrap"
               v-if="source.api.data.method === 'POST' || source.api.data.method === 'PUT' || source.api.data.method === 'PATCH'">
            <div class="editor-title">Body(JSON格式)</div>
            <CodeEditor v-model="source.api.data.body" @blur="commitUpdate('api')" :options="editorOpt" key="api-code2"/>
          </div>
        </div>
      </el-step>
    </el-steps>
    </div>
    <!-- 创建数据源 -->
    <CreateDataSource ref="create" @update="updateDatastorage" :type="sourceType"/>
    <!-- 动态参数 -->
    <DynamicParams ref="dynamicParams" @updateApiFunc="updateApiFunc" />
    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import CodeEditor from './CodeEditor'
import CreateDataSource from './CreateDataSource'
import DynamicParams from './DynamicParams'
import { datastorageList } from '@/api/datastorage'
import { sendCustomReq } from '@/api/common'
import { mapState } from 'vuex'
const sourceTypeOpt = [
  // { value: 'static', label: '静态数据' },
  // { value: 'csv_file', label: 'CSV文件' },
  // { value: 'json', label: 'JSON文件' },
  { value: 'api', label: 'API数据' }
  // { value: 'mysql', label: 'MySQL数据库' },
  // { value: 'postgresql', label: 'PostgreSQL数据库' },
  // { value: 'oracle', label: 'Oracle数据库' },
  // { value: 'dmc', label: 'DMC数据库' },
  // { value: 'mongodb', label: 'MongoDB数据库' },
  // { value: 'websocket', label: '实时数据' },
  // { value: 'dashboard', label: '仪表盘数据' }
]
const methodOpt = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
  { value: 'PATCH', label: 'PATCH' }
]
export default {
  name: 'PermissionData',
  components: {
    CreateDataSource,
    DynamicParams,
    CodeEditor
  },
  props: ['value'],
  data () {
    return {
      show: false,
      sourceTypeOpt: sourceTypeOpt,
      methodOpt: methodOpt,
      editorOpt: {
        mode: 'application/json'
      },
      sourceList: [], // 数据源列表
      loading: false,
      source: {
        api: {
          data: {
            sourceId: '',
            baseUrl: '',
            method: '',
            headers: '{}',
            path: '',
            params: '',
            body: '{}',
            needCookie: false
          }
        }
      }
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    sourceType: { // 数据源类
      get () {
        return this.screenInfo?.permissionDataConfig?.dataResponse?.sourceType
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{
          key: 'permissionDataConfig.dataResponse.sourceType',
          value: val
        }]).then(() => {
          this.getData()
        })
      }
    }
  },
  watch: {
    sourceType: { // 数据源类型change 设置静态数据或请求数据源列表
      handler: function (val) {
        this.getDatasourceList(val)
      },
      immediate: true
    }
  },
  created () {
    this.initData()
  },
  methods: {
    initData () {
      const { baseUrl, headers, method, path, sourceId } = this.screenInfo.permissionDataConfig.dataResponse.source[this.sourceType].data
      _.merge(this.source[this.sourceType].data, { baseUrl, headers, method, path, sourceId })
      this.$emit('fristLoading', true)
      this.getData()
    },
    showSetting () {
      this.show = true;
    },
    async getDatasourceList (sourceType) { // 获取数据源列表
      const data = {
        workspaceId: this.screenInfo.workspaceId,
        type: sourceType
      }
      const res = await datastorageList(data)
      if (res && res.success) {
        if (res.data.length === 0) {
          return;
        }
        this.sourceList = res.data.map(item => {
          return {
            ...item,
            id: item.id + ''
          }
        })
      }
    },
    sourceChange (type, val) { // 选择数据源 带入baseUrl
      if (type === 'api') {
        const source = this.sourceList.find(item => item.id === val)
        if (source && source.config) {
          this.source.api.data.baseUrl = source.config.baseUrl
        } else {
          this.source.api.data.baseUrl = ''
        }
      }
      this.commitUpdate(type)
    },
    commitUpdate: _.debounce(function (type) { // 提交更新
      this.$store.dispatch('editor/updateScreenInfo', [{
        key: `permissionDataConfig.dataResponse.source.${type}`,
        value: _.cloneDeep(this.source[type])
      }]).then(() => {
        this.getData()
      })
    }, 300),
    createData () { // 创建数据源
      this.$refs.create.showDialog()
    },
    editDynamicParams () { // 编辑动态参数
      this.$refs.dynamicParams.showDialog(this.source.api.data.func);
    },
    updateApiFunc (func) {
      this.source.api.data.func = func;
      this.commitUpdate('api');
    },
    async updateDatastorage (data) { // 数据源创建成功 刷新数据源列表
      const sourceType = data.type
      const sourceId = data.id + ''
      if (sourceType === this.sourceType) {
        await this.getDatasourceList(this.sourceType)
        this.source[sourceType].data.sourceId = sourceId
        this.sourceChange(sourceType, sourceId)
      }
    },
    async getData () { // 获取数据
      this.loading = true
      const params = this.source[this.sourceType].data;
      const { baseUrl } = params
      if (!baseUrl) {
        // this.$message.warn('请选择数据源')
        this.loading = false
        return
      }
      try {
        const res = await sendCustomReq(params)
        this.loading = false
        if (res && res.success) {
          const data = res.data.data || []
          // this.sourceData = JSON.stringify(data)
          this.$emit('input', JSON.stringify(data))
        } else {
          // this.sourceData = JSON.stringify([]);
          this.$emit('input', JSON.stringify([]))
        }
      } catch (error) {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.permission-data {
  // padding-top: 16px;
  // padding-left: 4px;
  position: absolute;
  width: 400px;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 90;
  background: #1c1f25;
  box-shadow: -1px 0 #000;
  transition: transform 0.25s linear;
  overflow: hidden;
  &.hide {
    transform: translateX(-400px);
  }
  .page-title {
    position: relative;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    span.icon {
      float: left;
      font-size: 16px;
      margin: 12px;
      cursor: pointer;
    }
    span.title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .steps-content {
    padding: 16px 4px;
    // padding-left: 4px;
    height: calc(100% - 40px);
    overflow: auto;
  }
  .content-block {
    .config-control {
      padding: 4px 0 4px 8px;
      margin-bottom: 10px;
      .config-title {
        width: 100px;
      }
    }
    .eidtor-wrap {
      margin-bottom: 10px;
    }
    .flex-row {
      display: flex;
      justify-content: space-between;
      .ml10 {
        margin-left: 10px;
      }
      .table-name {
        flex: none;
        width: 110px;
        line-height: 28px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .field-box {
      width: 100%;
      min-height: 85px;
      background: #0c0b0b;
      padding-bottom: 8px;
      border-radius: 8px;
      .field-div {
        &.add-field {
          & i {
            color:#2681ff;
          }
          color:#2681ff;
          border-color:#2681ff;
        }
        display: inline-block;
        cursor: pointer;
        margin-left: 8px;
        margin-top: 8px;
        padding: 0px 8px;
        background: #191D25;
        line-height: 22px;
        border-radius: 16px;
        border: 1px solid rgba(76, 82, 95, 0.32);
        ::v-deep {
          .el-input__inner {
            height: 22px;
            border: none;
            padding-right: 20px;
            background: none;
          }
          .el-input__icon {
            width: 13px;
            height: 22px;
            line-height: 22px;
            cursor: pointer;
          }
          .el-select {
            max-width: 105px;
          }
        }
      }
    }
  }
  ::v-deep {
    .mapping-t {
      line-height: 1;
      .t-icon {
        font-size: 16px;
        &.green {
          color: #00a755;
        }
        &.red {
          color: #ef5350;
        }
      }
    }
    .el-input-number--mini {
      width: 90px;
    }
    .CodeMirror {
      height: 238px;
    }
    .el-steps--vertical {
      height: unset;
    }
    .el-step {
      .el-step__description {
        padding-right: 10px;
        margin-top: 0;
      }
      &.is-vertical .el-step__main {
        padding-left: 8px;
      }
      .el-step__icon {
        width: 18px;
        height: 18px;
        font-size: 12px;
        background-color: #2681ff;
      }
      .el-step__icon.is-text {
        border: none;
      }
      .el-step__icon-inner {
        font-weight: 400;
      }
      &.is-vertical .el-step__line {
        width: 2px;
        top: 0;
        bottom: 0;
        left: 8px;
        background-color: #2e343c;
      }
      &.is-vertical .el-step__title {
        font-size: 14px;
        line-height: 18px;
      }
      &.is-vertical .el-step__head {
        width: 18px;
      }
      &.error .el-step__icon {
        background: #F56C6C;
      }
    }
  }
  .w100 {
    width: 100%;
  }
}
</style>
