<template>
  <div class="upload-image">
    <div class="upload-wraper" @click="showDialog">
      <img v-if="thumbnail" :src="thumbnail" class="uploaded-img">
      <div class="del" v-if="!!thumbnail" @click.stop="delPic">
        <span class="icon el-icon-close"></span>
      </div>
      <span v-if="!thumbnail" class="tips">{{ tips }}</span>
    </div>
    <select-resources-dialog
      v-if="isResourcesDialog"
      :isAdmin="true"
      :isOpenDialog="isResourcesDialog"
      :resourceType="resourceType"
      :openFolderName="locateFolderName"
      @selectedFile="selectedFile"
      @closeRescourcesDialog="closeDialog">
    </select-resources-dialog>
  </div>
</template>

<script>
import SelectResourcesDialog from '@/components/resources-control/SelectResourcesDialog.vue'
export default {
  name: 'UploadImage',
  props: {
    value: String,
    resourceType: {
      type: String,
      default: 'picture'
    },
    locateFolderName: {
      type: String,
      default: '页面背景'
    },
    tips: {
      type: String,
      default: '选择/上传图片'
    }
  },
  components: {
    SelectResourcesDialog
  },
  data () {
    return {
      isResourcesDialog: false
    }
  },
  computed: {
    thumbnail: {
      get: function () {
        return this.value
      },
      set: function (val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    selectedFile (data) {
      this.thumbnail = data.url;
    },
    showDialog () {
      this.isResourcesDialog = true;
    },
    closeDialog () {
      this.isResourcesDialog = false;
    },
    delPic () {
      this.thumbnail = '';
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-wraper {
  position: relative;
  width: 100%;
  height: 90px;
  border: 1px solid #393b4a;
  cursor: pointer;
  .uploaded-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  .del {
    position: absolute;
    top: 5px;
    right: 5px;
  }
  .tips {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 12px;
  }
}
</style>
