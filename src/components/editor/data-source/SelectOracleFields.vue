<template>
  <div>
    <el-dialog
      class="create-datasource"
      :visible.sync="show"
      title="批量添加字段"
      append-to-body
      width="832px"
      :close-on-click-modal="false"
      @close="closeDialog"
      top="0"
    >
      <el-input
        placeholder="搜索"
        size="mini"
        @input="searchField"
        v-model="filterText"
      >
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
      <div element-loading-background="#1F2430" v-loading="checkboxLoading">
        <div style="margin: 15px 0"></div>
        <el-row class="check-row" v-show="showSearchField">
          <el-col :span="24" class="check-group">
            <el-checkbox-group v-model="chekedFieldList">
              <el-checkbox
                @change="handleCheckedFieldChange($event, index, item)"
                v-for="(item, index) in searchFieldList"
                :key="item.name"
                :label="item.name"
                :title="item.name"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
          </el-col>
        </el-row>
        <el-row class="check-row" v-show="!showSearchField">
          <div
            v-for="(fielditem, index) in fieldList"
            :key="fielditem.type"
            v-show="fielditem.fields.length !== 0"
          >
            <el-col :span="6" class="check-title">
              <el-checkbox
                v-model="fielditem.isIndeterminate"
                @change="handleCheckAllChange($event, index)"
                >{{ fielditem.type | formatField }}</el-checkbox
              >
            </el-col>
            <el-col :span="18" class="check-group">
              <el-checkbox-group v-model="chekedFieldList">
                <el-checkbox
                  @change="handleCheckedFieldChange($event, index, item)"
                  v-for="item in fielditem.fields"
                  :label="item.name"
                  :key="item.name"
                  :title="item.name"
                >
                  {{ item.name }}
                </el-checkbox>
              </el-checkbox-group>
            </el-col>
          </div>
        </el-row>
      </div>
      <div slot="footer">
        <el-button
          type="light-blue"
          size="medium"
          @click="submit"
          :loading="loading"
          >确定</el-button>
        <el-button type="text" size="medium" @click="closeDialog"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getFieldList } from '@/api/datastorage';
import { mapState, mapGetters } from 'vuex';
export default {
  name: 'SelectOracleFields',
  data () {
    return {
      checkboxLoading: false,
      searchFieldList: [],
      showSearchField: false,
      checkAll: false,
      chekedFields: [],
      chekedFieldList: [],
      fieldList: [],
      isIndeterminate: true,
      radio: 3,
      filterText: '',
      show: false,
      dbList: [],
      loading: false,
      loading2: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      currentSelectId: state => state.editor.currentSelectId
    }),
    ...mapGetters('editor', ['currentConfigId'])
  },
  methods: {
    searchField (filterStr) {
      // this.chekedFields = []
      // this.chekedFieldList = []
      this.searchFieldList = []
      for (let index = 0; index < this.fieldList.length; index++) {
        const arr = this.fieldList[index].fields.filter(item => {
          if (item.name.indexOf(filterStr) !== -1) {
            return item
          }
        })
        this.searchFieldList = [...this.searchFieldList, ...arr]
      }
      this.showSearchField = true
      if (this.filterText === '') {
        this.showSearchField = false
      }
    },
    async getFieldList (tbName) {
      this.checkboxLoading = true
      const data = {
        type: 'oracle',
        tbName,
        componentId: this.currentConfigId,
        workspaceId: this.screenInfo.workspaceId
      }
      const res = await getFieldList(data);
      if (res && res.success) {
        this.fieldList = res.data.filter(item => item.fields.length);
        for (let index = 0; index < this.fieldList.length; index++) {
          const hasIntersection = _.intersectionWith(this.chekedFields, this.fieldList[index].fields, _.isEqual);
          this.fieldList[index].isIndeterminate = hasIntersection.length === this.fieldList[index].fields.length
          this.fieldList[index].fields = this.fieldList[index].fields.map(item => {
            item.type = this.fieldList[index].type;
            return item;
          })
        }
      }
      this.checkboxLoading = false
    },
    handleCheckAllChange (val, index) {
      if (val) {
        const fieldType = this.fieldList[index].type
        this.chekedFields = this.chekedFields.filter(item => item.type !== fieldType)
        this.chekedFields = [...this.chekedFields, ...this.fieldList[index].fields]
        this.chekedFieldList = this.chekedFields.map(item => item.name)
        this.fieldList[index].isIndeterminate = true
      } else {
        _.pullAllBy(this.chekedFields, this.fieldList[index].fields);
        this.chekedFieldList = []
        this.chekedFieldList = this.chekedFields.map(item => item.name)
        this.fieldList[index].isIndeterminate = false
      }
    },
    handleCheckedFieldChange (value, index, item) {
      if (!value) {
        _.pullAllBy(this.chekedFields, [item]);
        if (!this.showSearchField) {
          const hasIntersection = _.intersectionWith(this.chekedFields, this.fieldList[index].fields, _.isEqual);
          this.fieldList[index].isIndeterminate = hasIntersection.length === this.fieldList[index].fields.length
        }
      } else {
        this.chekedFields = [item, ...this.chekedFields]
        if (!this.showSearchField) {
          const hasIntersection = _.intersectionWith(this.chekedFields, this.fieldList[index].fields, _.isEqual);
          this.fieldList[index].isIndeterminate = hasIntersection.length === this.fieldList[index].fields.length
        }
      }
    },
    showDialog (mysqlData) {
      this.show = true;
      this.$nextTick(() => {
        this.getFieldList(mysqlData.tbName)
      })
    },
    resetParams () {
      this.fieldList = [];
      this.chekedFields = []
      this.chekedFieldList = []
      this.filterText = ''
    },
    closeDialog () {
      this.show = false;
      this.resetParams()
      this.showSearchField = false
    },
    submit () {
      this.$emit('update', this.chekedFields);
      this.show = false;
      this.resetParams()
      this.showSearchField = false
    }
  }
}
</script>
<style lang="scss" scoped>
.create-datasource {
  .filter-tree{
    margin-top: 34px;
  }
  .check-row {
    padding: 0px 0px 16px 25px;
    background: #293246;
    border-radius: 8px;
    max-height: 450px;
    overflow: auto;
  }
  .check-title{
    margin-top: 20px;
    ::v-deep {
      .el-checkbox{
        margin-top: 16px;
        color: rgba(255, 255, 255, 0.7);
      }
      .el-checkbox__inner{
        border: 1px solid rgba(86, 98, 118, 0.64);
        border-radius: 3px;
      }
    }
  }
  .check-group{
    width: 550px;
    margin-top: 20px;
    ::v-deep {
      .el-checkbox{
        width: 150px;
        margin-top: 16px;
        line-height: 24px;
        color: rgba(255, 255, 255, 0.7);
        overflow: hidden;
      }
      .el-checkbox__inner{
        border: 1px solid rgba(86, 98, 118, 0.64);
        border-radius: 3px;
      }
    }
  }
  ::v-deep {
    .el-form-item__label {
      color: #fafafa;
      font-size: 12px;
    }
    .el-upload-dragger {
      width: 300px;
      height: 140px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        font-size: 12px;
      }
    }
    .el-upload-dragger .el-icon-upload {
      font-size: 50px;
      margin: 20px 0 16px;
    }
  }
}
</style>
