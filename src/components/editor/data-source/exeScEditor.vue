<template>
  <div class="code-editor">
    <codemirror v-model="code" v-on="$listeners" :options="cmOptions" ref="jsonEditor" />
  </div>
</template>
<script>
import { codemirror } from 'vue-codemirror';
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/addon/hint/javascript-hint.js'
import 'codemirror/addon/hint/show-hint.js'
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/base16-dark.css';
export default {
  name: 'CodeEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    options: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  components: {
    codemirror
  },
  data () {
    return {}
  },
  watch: {
    value (val) {}
  },
  computed: {
    code: {
      get: function () {
        return this.value
      },
      set: function (val) {
        this.$emit('input', val);
      }
    },
    cmOptions () {
      return {
        tabSize: 2,
        mode: 'text/javascript',
        theme: 'base16-dark',
        lineNumbers: true,
        line: true,
        timer: null,
        ...this.options
      }
    },
    jsonEditor () {
      return this.$refs.jsonEditor.codemirror
    },
    codeMirror () {
      return this.$refs.jsonEditor
    }
  },
  mounted () {
    this.timer = setTimeout(() => {
      this.codeMirror.refresh()
      this.jsonEditor.focus()
    }, 2500);
  },
  beforeDestroy () {
    this.timer && clearTimeout(this.timer);
  },
  methods: {
    setValue (val) {
      this.jsonEditor.setValue(JSON.stringify(JSON.parse(val), null, 2));
    }
  }
};
</script>

<style lang="scss" scoped>
.code-editor {
  border: 1px solid #393b4a;
}
</style>
