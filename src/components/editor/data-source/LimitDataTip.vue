<template>
  <el-dialog
    :visible.sync="show"
    title="提示"
    :close-on-click-modal="false"
    append-to-body
    width="550px"
    top="0"
    class="limit-data-tip">
    <div class="d-title">检测到组件数据量过大，是否开启条数限制？</div>
    <div class="d-form">
      <div class="config-control">
        <div class="config-title nowrap">
          <el-checkbox v-model="form.isLimit" class="mr5"></el-checkbox>
          限制条目数：
        </div>
        <div class="width-div">
          <el-input-number v-model="form.limitNum" :min="0" controls-position="right" size="mini" class="w100"></el-input-number>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="plain" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'LimitDataTip', // 组件数据量过大 提醒
  props: {},
  data () {
    return {
      show: false,
      form: {
        isLimit: true,
        limitNum: 100
      },
      limitOption: [
        { id: 0, value: 10, label: '10' },
        { id: 1, value: 20, label: '20' },
        { id: 2, value: 50, label: '50' },
        { id: 3, value: 100, label: '100' },
        { id: 4, value: 200, label: '200' },
        { id: 5, value: 500, label: '500' }
      ]
    }
  },
  methods: {
    showDialog () {
      this.form.isLimit = true;
      this.form.limitNum = 100;
      this.show = true;
    },
    closeDialog () {
      this.show = false;
    },
    submit () {
      this.$emit('update', this.form);
      this.closeDialog();
    }
  }
}
</script>

<style lang="scss" scoped>
.d-title {
  font-size: 14px;
  color: #fff;
}
.config-control {
  padding: 4px 0 4px 8px;
  margin-top: 20px;
  margin-bottom: 10px;
  .config-title {
    width: 100px;
  }
}
</style>
