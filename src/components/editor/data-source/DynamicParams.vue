<template>
  <el-dialog class="dynamic-params" :title="title" :visible.sync="show" width="600px" append-to-body top="0">
    <div class="func-code">function&nbsp;()&nbsp;{</div>
    <CodeEditor v-model="func" />
    <div class="func-code">}</div>
    <div slot="footer">
      <el-button type="text" @click="submit">确定</el-button>
      <el-button type="text" @click="closeDialog">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import CodeEditor from './CodeEditor.vue'
export default {
  name: 'DynamicParams',
  components: { CodeEditor },
  data () {
    return {
      title: '动态参数',
      show: false,
      func: ''
    }
  },
  methods: {
    showDialog (func) {
      this.func = func;
      this.show = true;
    },
    closeDialog () {
      this.show = false;
    },
    submit () {
      this.$emit('updateApiFunc', this.func);
      this.closeDialog();
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-params {
  .func-code {
    color: #bfbfbf;
    line-height: 24px;
  }
}
</style>
