<template>
  <div class="comp-data">
    <el-steps direction="vertical" :key="'stepkey'">
      <el-step title="权限数据配置">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">配置权限</div>
            <el-button size="mini" @click="setData" type="plain">配置权限数据</el-button>
          </div>
          <CodeEditor ref="editor" :options="cmOptions" />
        </div>
      </el-step>
      <el-step title="权限字段配置">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">
              ID字段
            </div>
            <div class="width-div">
              <el-select v-model="mappingId" @change="mappingChange('mappingId')" size="mini">
                <el-option v-for="opt in fieldOptions" :key="opt.id" :value="opt.name" :label="opt.name"></el-option>
              </el-select>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">
              名称字段
            </div>
            <div class="width-div">
              <el-select v-model="mappingLabel" @change="mappingChange('mappingLabel')" size="mini">
                <el-option v-for="opt in fieldOptions" :key="opt.id" :value="opt.name" :label="opt.name"></el-option>
              </el-select>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">
              是否可见字段
            </div>
            <div class="width-div">
              <el-select v-model="mappingPermission" @change="mappingChange('mappingPermission')" size="mini">
                <el-option v-for="opt in fieldOptions" :key="opt.id" :value="opt.name" :label="opt.name"></el-option>
              </el-select>
            </div>
          </div>
        </div>
      </el-step>
      <el-step title="字段组件关系配置">
        <div class="content-block" slot="description">
          <template v-for="(item, idx) in permissionData">
            <div class="config-control" :key="item.id">
              <div class="key-select">
                <el-select v-model="item.id" size="small">
                  <el-option v-for="opt in permissionOptions" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
              </div>
              <div class="width-div">
                <CompSelect
                  v-model="item.componentsId"
                  :options="globalComs()"
                  multiple />
              </div>
              <div style="display: flex;">
                <i style="display: block;width: 14px;height: 14px;" v-if="idx !== permissionData.length - 1" />
                <i class="el-icon-minus cursor-pointer" @click="minusParam(idx)" v-if="permissionData.length !== 1" />
                <i class="el-icon-plus cursor-pointer" @click="addParam" v-if="idx === permissionData.length - 1" />
              </div>
            </div>
          </template>
        </div>
      </el-step>
      <el-step title="无权限样式配置">
        <div slot="description">
          <div class="content-block">
              <div class="config-control">
                <div class="config-title nowrap">
                  匹配类型：
                </div>
                <div class="width-div">
                  <el-select v-model="tipsType" size="mini">
                    <el-option v-for="opt in tipsOptions" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                  </el-select>
                </div>
              </div>
              <template v-if="tipsType === 'tips'">
                <div class="config-control">
                  <div class="config-title nowrap">提示内容</div>
                  <div class="width-div">
                    <el-input v-model="tipsInfo" size="mini"></el-input>
                  </div>
                </div>
              </template>
              <template v-if="tipsType === 'tips'">
                <div class="config-control">
                  <ConfigTree
                    :treeData="comCtlConfigTreeData"
                    :configObj="comCtlConfigObj"
                    @change="handleConfigTreeChange"
                  />
                </div>
              </template>
          </div>
        </div>
      </el-step>
    </el-steps>
    <!-- 配置权限数据 -->
    <PermissionData @fristLoading="isFirst = true" v-model="sourceData" ref="permission" />
  </div>
</template>

<script>
import CodeEditor from './CodeEditor';
import PermissionData from './PermissionData'
import CompSelect from '../interaction/CompSelect'
import { uuid, isPanel } from '@/utils/base'
import { mapState } from 'vuex'

const options = [{
  value: 'hide',
  label: '隐藏'
}, {
  value: 'tips',
  label: '提示'
}, {
  value: 'disable',
  label: '禁用'
}]
const controlConfig = {
  font: {
    name: '文本样式',
    type: 'font',
    isNecessary: true,
    default: {
      fontSize: 14,
      color: '#FFFFFF',
      fontFamily: 'Microsoft YaHei',
      fontWeight: 'bold'
    }
  },
  background: {
    name: '背景',
    type: 'group',
    children: {
      show: {
        name: '显示',
        type: 'switch',
        default: true
      },
      type: {
        name: '类型',
        type: 'select',
        options: [
          {
            value: 'image',
            label: '图片'
          },
          {
            value: 'gradient',
            label: '渐变'
          },
          {
            value: 'pure',
            label: '纯色'
          }
        ],
        default: 'image'
      },
      pure: {
        name: '纯色',
        type: 'color',
        default: 'rgba(32, 188, 255, 0.15)',
        showInPanel: {
          conditions: [
            [
              'background.type',
              '$eq',
              'pure'
            ]
          ]
        }
      },
      gradient: {
        name: '渐变色',
        type: 'suite',
        children: {
          start: {
            name: '起始色',
            type: 'color',
            default: 'rgba(31, 113, 255, 0.6)'
          },
          end: {
            name: '结束色',
            type: 'color',
            default: 'rgba(31, 113, 255, 0.1)'
          },
          deg: {
            name: '渐变角度',
            type: 'slider',
            min: 0,
            max: 360,
            suffix: 'deg',
            default: 90
          }
        },
        showInPanel: {
          conditions: [
            [
              'background.type',
              '$eq',
              'gradient'
            ]
          ]
        }
      },
      image: {
        name: '图片',
        type: 'group',
        children: {
          url: {
            name: '地址',
            type: 'image',
            default: '',
            locateFolderName: '组件背景-短横幅'
          },
          size: {
            name: '尺寸适配',
            type: 'select',
            options: [
              {
                value: 'contain',
                label: '自适应'
              },
              {
                value: 'cover',
                label: '原比例充满'
              },
              {
                value: '100% 100%',
                label: '拉伸充满'
              },
              {
                value: 'auto',
                label: '原始大小'
              }
            ],
            default: 'contain'
          },
          positionX: {
            name: '水平对齐',
            type: 'select',
            options: [
              {
                value: 'center',
                label: '居中对齐'
              },
              {
                value: 'left',
                label: '左对齐'
              },
              {
                value: 'right',
                label: '右对齐'
              }
            ],
            default: 'center'
          },
          positionY: {
            name: '垂直对齐',
            type: 'select',
            options: [
              {
                value: 'center',
                label: '居中对齐'
              },
              {
                value: 'top',
                label: '上对齐'
              },
              {
                value: 'bottom',
                label: '下对齐'
              }
            ],
            default: 'center'
          },
          repeat: {
            name: '重复方式',
            type: 'select',
            options: [
              {
                value: 'no-repeat',
                label: '不重复'
              },
              {
                value: 'repeat',
                label: '水平垂直重复'
              },
              {
                value: 'repeat-x',
                label: '水平重复'
              },
              {
                value: 'repeat-y',
                label: '垂直重复'
              }
            ],
            default: 'no-repeat'
          }
        },
        showInPanel: {
          conditions: [
            [
              'background.type',
              '$eq',
              'image'
            ]
          ]
        }
      }
    }
  }
}
export default {
  name: 'CompPermission',
  components: {
    PermissionData,
    CodeEditor,
    CompSelect
  },
  inject: ['getLayerTree'],
  data () {
    return {
      cmOptions: {
        readOnly: true
      },
      sourceData: '[]',
      permissionData: [],
      isFirst: false,
      tipsOptions: options,
      comCtlConfigTreeData: controlConfig
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      pageId: state => state.editor.pageId,
      screenComs: state => state.editor.screenComs,
      childScreenComs: state => state.editor.childScreenComs,
      layerMap: state => state.editor.layerMap,
      screenGroupData: state => state.editor.screenGroupData
    }),
    mappingId: {
      get () {
        return this.screenInfo?.permissionDataConfig?.mappingRelation?.id
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{
          key: 'permissionDataConfig.mappingRelation.id',
          value: val
        }])
      }
    },
    mappingLabel: {
      get () {
        return this.screenInfo?.permissionDataConfig?.mappingRelation?.label
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{
          key: 'permissionDataConfig.mappingRelation.label',
          value: val
        }])
      }
    },
    mappingPermission: {
      get () {
        return this.screenInfo?.permissionDataConfig?.mappingRelation?.permission
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{
          key: 'permissionDataConfig.mappingRelation.permission',
          value: val
        }])
      }
    },
    comCtlConfigObj () {
      const { background, font } = this.screenInfo.permissionDataConfig.tips
      return {
        background,
        font
      }
    },
    tipsType: {
      get () {
        return this.screenInfo.permissionDataConfig.tips.type
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{
          key: 'permissionDataConfig.tips.type',
          value: val
        }])
      }
    },
    tipsInfo: {
      get () {
        return this.screenInfo.permissionDataConfig.tips.info
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{
          key: 'permissionDataConfig.tips.info',
          value: val
        }])
      }
    },
    fieldOptions () { // 字段映射集合
      let result = []
      try {
        const keys = []
        const sourceData = JSON.parse(this.sourceData)
        sourceData.forEach(item => {
          const key = Object.keys(item)
          keys.push(...key)
        })
        const uniKeys = Array.from(new Set(keys))
        result = uniKeys.map((key, idx) => {
          return {
            id: idx,
            name: key
          }
        })
      } catch (error) {}
      return result
    },
    permissionOptions () {
      const sourceData = JSON.parse(this.sourceData)
      const result = sourceData.map(item => {
        return {
          [this.mappingId]: item[this.mappingId],
          [this.mappingLabel]: item[this.mappingLabel],
          [this.mappingPermission]: item[this.mappingPermission]
        }
      })
      return result
    },
    layerTree () {
      return this.getLayerTree()
    },
    loadedNodes () {
      const loadedNodes = this.layerTree.data.children;
      if (this.screenInfo.screenType === 'scene') {
        if (this.pageId) {
          return loadedNodes.filter((node) => {
            return (
              node.pageId === this.pageId ||
              (node.sceneId === this.sceneId && !node.pageId)
            );
          });
        } else {
          return loadedNodes.filter((node) => {
            return node.sceneId === this.sceneId && !node.pageId;
          });
        }
      } else {
        return loadedNodes;
      }
    },
    getScreenNameById () { // 根据大屏id获取大屏name
      return function (id) {
        let screensOpt = [];
        // this.screenGroupData.forEach(project => {
        //   const screens = project.screens;
        //   screensOpt.push(...screens);
        // })
        screensOpt = this.screenGroupData
        const screen = screensOpt.find(s => s.id === id);
        if (screen) {
          return screen.name
        }
        return '引用大屏'
      }
    },
    globalComs () {
      return function (noGroup) {
        // 递归设置分组数据
        const self = this;
        function setGroupComp (layers, screenComs, pName) {
          const result = [];
          (layers || []).forEach(l => {
            // if (l.id === self.currentConfigId) return;
            if (l.id.includes('interaction-container-referpanel')) return; // 权限字段不支持切换面板
            if (l.type === 'com') {
              const currCom = _.cloneDeep(screenComs[l.id]);
              if (currCom) {
                const item = {
                  label: currCom.alias,
                  value: l.id,
                  dataConfig: currCom.dataConfig,
                  actions: currCom.actions,
                  callbackParams: currCom.interactionConfig.callbackParams,
                  comType: currCom.comType,
                  controlConfig: currCom.controlConfig,
                  config: currCom.config,
                  attr: currCom.attr,
                  show: currCom.show,
                  type: 'com',
                  customLabel: pName ? pName + '/' + currCom.alias : currCom.alias
                }
                setPanelChildComs(item, screenComs);
                // 若有子组件，将子组件加入到父组件的children中
                const subComs = Object.values(screenComs).filter(c => c.parent === l.id && c.id !== self.currentConfigId);
                let childArr = [];
                if (subComs.length) {
                  childArr = subComs.map(subCom => {
                    return {
                      label: subCom.alias,
                      value: subCom.id,
                      dataConfig: subCom.dataConfig,
                      actions: subCom.actions,
                      callbackParams: subCom.interactionConfig.callbackParams,
                      comType: subCom.comType,
                      controlConfig: subCom.controlConfig,
                      config: subCom.config,
                      attr: subCom.attr,
                      show: subCom.show,
                      type: 'subCom',
                      customLabel: item.customLabel + '/' + subCom.alias
                    }
                  })
                }
                if (childArr.length) {
                  item.children = childArr;
                }
                result.push(item)
              }
            } else {
              const item = {
                label: l.groupName,
                value: l.id,
                type: 'group',
                isDisabled: !!noGroup,
                customLabel: pName ? pName + '/' + l.groupName : l.groupName
              }
              if (l.children && l.children.length) {
                item.children = setGroupComp(l.children, screenComs, item.customLabel)
              }
              result.push(item)
            }
          })

          return result
        }

        // 如果是面板类组件，则展子面板的组件
        function setPanelChildComs (item, screenComs) {
          if (isPanel(item.comType)) {
            const currCom = screenComs[item.value];
            if (currCom) {
              const screens = currCom.config.screens || [];
              const children = screens.map(s => {
                const components = self.childScreenComs[s.id];
                if (components) {
                  const childComs = setGroupComp(self.layerMap[s.id], components, item.customLabel);
                  return {
                    label: s.key || self.getScreenNameById(s.id),
                    value: uuid('key'),
                    isDisabled: true,
                    children: childComs
                  }
                }
                return {
                  label: s.key || self.getScreenNameById(s.id),
                  value: uuid('key'),
                  isDisabled: true,
                  children: []
                }
              })
              // 只有动态面板需要显示状态一级，其他面板只有一个状态 无需显示
              if (['interaction-container-dynamicpanel', 'interaction-container-referpanel'].includes(currCom.comType)) {
                item.children = children;
              } else {
                item.children = children[0]?.children || [];
              }
            }
          }
        }

        let opt = [];

        opt = setGroupComp(this.loadedNodes, this.screenComs);

        // 如果是子面板，则push父面板的组件选项
        if (this.screenInfo.isDynamicScreen) { // 动态面板类 显示上级大屏的组件
          const parentComs = _.cloneDeep(this.parentScreenComs);
          delete parentComs[this.screenInfo.relationCompId]; // 删除当前面板
          const children = setGroupComp(this.layerMap[this.screenInfo.parentId], parentComs, '上级大屏');

          const pOpt = {
            label: '上级大屏',
            value: uuid('key'),
            isDisabled: true,
            children: children,
            comType: ''
          }
          opt.push(pOpt);
        }
        return opt
      }
    }
  },
  watch: {
    sourceData: {
      handler: function () {
        this.$nextTick(() => {
          if (!this.isFirst) {
            this.clearParam()
          }
          this.isFirst = false
        })
        this.getResult();
      }
      // immediate: true
    }
  },
  created () {
    this.permissionData = _.cloneDeep(this.screenInfo.permissionDataConfig.userPermissionList)
    this.$watch(
      'permissionData',
      _.debounce(function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{
          key: 'permissionDataConfig.userPermissionList',
          value: _.cloneDeep(val)
        }])
      }, 200), {
        deep: true
      }
    )
  },
  methods: {
    setData () {
      this.$refs.permission.showSetting();
    },
    getResult: _.debounce(function () {
      this.$refs.editor.setValue(this.sourceData);
    }, 300),
    mappingChange: _.debounce(function (type) {
      if (type !== this[type]) {
        this.permissionData = [{
          id: '',
          componentsId: []
        }]
      }
    }, 300),
    clearParam () {
      this.mappingId = '';
      this.mappingLabel = '';
      this.mappingPermission = '';
      this.permissionData = [{
        id: '',
        componentsId: []
      }]
    },
    minusParam (idx) {
      if (this.permissionData.length === 1) {
        return
      }
      this.permissionData.splice(idx, 1)
    },
    addParam () {
      this.permissionData.push({
        id: '',
        componentsId: []
      })
    },
    handleConfigTreeChange ({ path, value }) {
      this.$store.dispatch('editor/updateScreenInfo', [{
        key: `permissionDataConfig.tips.${path}`,
        value: _.cloneDeep(value)
      }])
    }
  }
}
</script>

<style lang="scss" scoped>
.comp-data {
  max-height: 100%;
  overflow-y: auto;
  padding-top: 16px;
  padding-left: 4px;
  .content-block {
    .config-control {
      padding: 4px 0 4px 8px;
      margin-bottom: 10px;
      .config-title {
        width: 100px;
      }
    }
    .eidtor-wrap {
      margin-bottom: 10px;
    }
    .flex-row {
      display: flex;
      justify-content: space-between;
      .ml10 {
        margin-left: 10px;
      }
      .table-name {
        flex: none;
        width: 110px;
        line-height: 28px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .field-box {
      width: 100%;
      min-height: 85px;
      background: #0c0b0b;
      padding-bottom: 8px;
      border-radius: 8px;
      .field-div {
        &.add-field {
          & i {
            color:#2681ff;
          }
          color:#2681ff;
          border-color:#2681ff;
        }
        display: inline-block;
        cursor: pointer;
        margin-left: 8px;
        margin-top: 8px;
        padding: 0px 8px;
        background: #191D25;
        line-height: 22px;
        border-radius: 16px;
        border: 1px solid rgba(76, 82, 95, 0.32);
        ::v-deep {
          .el-input__inner {
            height: 22px;
            border: none;
            padding-right: 20px;
            background: none;
          }
          .el-input__icon {
            width: 13px;
            height: 22px;
            line-height: 22px;
            cursor: pointer;
          }
          .el-select {
            max-width: 105px;
          }
        }
      }
    }
  }
  ::v-deep {
    .mapping-t {
      line-height: 1;
      .t-icon {
        font-size: 16px;
        &.green {
          color: #00a755;
        }
        &.red {
          color: #ef5350;
        }
      }
    }
    .el-input-number--mini {
      width: 90px;
    }
    .CodeMirror {
      height: 238px;
    }
    .el-step {
      .el-step__description {
        padding-right: 10px;
        margin-top: 0;
      }
      &.is-vertical .el-step__main {
        width: calc(100% - 18px);
        padding-left: 8px;
      }
      .el-step__icon {
        width: 18px;
        height: 18px;
        font-size: 12px;
        background-color: #2681ff;
      }
      .el-step__icon.is-text {
        border: none;
      }
      .el-step__icon-inner {
        font-weight: 400;
      }
      &.is-vertical .el-step__line {
        width: 2px;
        top: 0;
        bottom: 0;
        left: 8px;
        background-color: #2e343c;
      }
      &.is-vertical .el-step__title {
        font-size: 14px;
        line-height: 18px;
      }
      &.is-vertical .el-step__head {
        width: 18px;
      }
      &.error .el-step__icon {
        background: #F56C6C;
      }
    }
  }
  .key-select {
    width: 140px;
    margin-right: 8px;
  }
  .w100 {
    width: 100%;
  }
  .cursor-pointer {
    cursor: pointer;
  }
}
</style>
