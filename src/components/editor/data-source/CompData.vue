<template>
  <div class="comp-data">
    <!-- step加key解决步骤条数据刷新bug -->
    <el-steps direction="vertical" :key="sourceType || 'stepkey'">
      <el-step title="配置数据">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">数据源类型</div>
            <div class="width-div">
              <el-select v-model="sourceType" size="mini" class="w100">
                <el-option
                  v-for="opt in computedSourceTypeOpt"
                  :key="opt.value"
                  :value="opt.value"
                  :label="opt.label">
                </el-option>
              </el-select>
            </div>
          </div>
          <template v-if="sourceType === 'static'">
            <div class="eidtor-wrap">
              <JsonEditor
                v-model="jsonData"
                :modes="['tree', 'code']"
                @json-change="val => staticDataChange(val, currentCom.id)"/>
            </div>
          </template>
          <template v-if="sourceType === 'csv_file'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.csv_file.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('csv_file', val)" size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'dashboard'">
            <div class="config-control">
            <div class="config-title nowrap">数据源</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap" :title="source.dashboard.data.name">{{ source.dashboard.data.name }}</div>
              <el-button @click="createDashData('dashboard')" class="ml10" type="plain" size="mini">选择</el-button>
          </div>
            </div>
          </template>
          <template v-if="sourceType === 'json'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.json.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('json', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'api'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.api.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('api', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'mysql'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.mysql.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('mysql', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'dmdb'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.dmdb.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('dmdb', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'postgresql'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.postgresql.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('postgresql', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'highgodb'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.highgodb.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('highgodb', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'oracle'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.oracle.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('oracle', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'mongodb'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.mongodb.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('mongodb', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'websocket'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.websocket.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('websocket', val)"
                  size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
            <div class="config-control">
              <div class="config-title nowrap">时间窗口</div>
              <div class="width-div flex-row">
                <el-input v-model="source.websocket.data.duration" @change="changeSocketDuration" :min="1" type="number" size="mini" />
                <el-select
                  v-model="source.websocket.data.durationType"
                  @change="changeSocketDuration"
                  size="mini"
                  class="ml10">
                  <el-option v-for="opt in timeOption" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                </el-select>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'excel'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.excel.data.sourceId"
                  clearable
                  @change="(val) => sourceChange('excel', val)" size="mini">
                  <el-option v-for="opt in sourceList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
                <el-button @click="createData" class="ml10" type="plain" size="mini">新建</el-button>
              </div>
            </div>
          </template>
          <template v-if="sourceType === 'datacontainer'">
            <div class="config-control">
              <div class="config-title nowrap">数据源</div>
              <div class="width-div flex-row">
                <el-select
                  v-model="source.datacontainer.data.dataContainerComId"
                  clearable
                  @change="(val) => sourceChange('datacontainer', val)"
                  size="mini"
                  class="w100">
                  <el-option v-for="opt in dataContainerList" :key="opt.id" :value="opt.id" :label="opt.name"></el-option>
                </el-select>
              </div>
            </div>
          </template>
        </div>
      </el-step>
      <el-step title="配置请求参数" v-if="sourceType === 'api'">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">Base URL</div>
            <div class="width-div">
              <el-input v-model="source.api.data.baseUrl" readonly size="mini" class="w100"></el-input>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">请求方式</div>
            <div class="width-div">
              <el-select v-model="source.api.data.method" @change="commitUpdate('api')" size="mini" class="w100">
                <el-option v-for="opt in methodOpt" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
              </el-select>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">
              动态参数
              <el-tooltip placement="top-start" content="如需在header或body中使用动态参数，可在此处设置参数并引用">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>
            <div class="width-div">
              <el-button type="plain" size="mini" @click="editDynamicParams()">编辑</el-button>
            </div>
          </div>
          <div class="eidtor-wrap">
            <div class="editor-title">请求头(JSON格式)</div>
            <CodeEditor v-model="source.api.data.headers" @blur="commitUpdate('api')" :options="editorOpt" key="api-code"/>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">路径</div>
            <div class="width-div">
              <el-input v-model="source.api.data.path" @blur="commitUpdate('api')" size="mini"></el-input>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">参数</div>
            <div class="width-div">
              <el-input v-model="source.api.data.params" @blur="commitUpdate('api')" size="mini"></el-input>
            </div>
          </div>
          <div class="eidtor-wrap"
               v-if="source.api.data.method === 'POST' || source.api.data.method === 'PUT' || source.api.data.method === 'PATCH'">
            <div class="editor-title">Body(JSON格式)</div>
            <CodeEditor v-model="source.api.data.body" @blur="commitUpdate('api')" :options="editorOpt" key="api-code2"/>
          </div>
          <!-- <div class="config-control">
            <div class="config-title width-auto nowrap">
              <el-checkbox v-model="source.api.data.reqFromBack" @change="commitUpdate('api')" size="mini"
                           class="mr5"></el-checkbox>
              后端发起请求
            </div>
          </div> -->
          <div class="config-control">
            <div class="config-title width-auto nowrap">
              <el-checkbox v-model="source.api.data.needCookie" @change="commitUpdate('api')" size="mini"
                           class="mr5"></el-checkbox>
              需要cookie
            </div>
          </div>
        </div>
      </el-step>
      <el-step title="配置查询语句" v-if="sourceType === 'mongodb'">
        <div class="slot-title" slot="title">
          <span class="c-title">配置字段</span>
        </div>
        <div class="content-block db-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">工作表名称</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap" :title="source.mongodb.data.tbName">{{ source.mongodb.data.tbName }}</div>
              <el-button @click="addMongoDBField('mongodbTable')" class="ml10" type="plain" size="mini">选择</el-button>
            </div>
          </div>
          <div class="eidtor-wrap">
            <CodeEditor v-model="source.mongodb.data.sql" :options="sqlOpt" @blur="commitUpdate('mongodb')" key="mongodb-code"/>
          </div>
          <div class="row-btn pl0">
            <el-button type="primary" size="mini" @click="confirmConfig" :disabled="!needUpdate">确认配置</el-button>
          </div>
        </div>
      </el-step>
      <el-step title="配置字段" v-if="sourceType === 'mysql'">
        <div class="slot-title" slot="title">
          <span class="c-title">配置字段</span>
        </div>
        <div class="content-block db-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">工作表名称</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap" :title="source.mysql.data.tbName">{{ source.mysql.data.tbName }}</div>
              <el-button @click="addMysqlField('mysqlTable')" class="ml10" type="plain" size="mini">选择</el-button>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">高级模式</div>
            <div class="width-div flex-row">
              <el-checkbox v-model="source.mysql.data.advanced" @change="commitUpdate('mysql')"></el-checkbox>
            </div>
          </div>
          <template v-if="!source.mysql.data.advanced">
            <div class="config-control">
              <div class="config-title nowrap w45_px">维度</div>
              <div class="width-div flex-row">
                <div class="field-box">
                  <span class="field-div" size="mini" :key="item.fid"
                        v-for="(item, index) in source.mysql.data.fields.dimension">
                    {{ item.name }}
                    <i
                      class="el-icon-close el-input__icon"
                      @click="deleteMysqlField(index,'dimension')"
                      slot="suffix">
                    </i>
                  </span>
                  <span class="field-div add-field" v-if="source.mysql.data.tbName" @click="addMysqlField('dimension')" size="mini">
                    <i class="el-icon-plus el-input__icon">
                    </i>
                    字段
                  </span>
                </div>
              </div>
            </div>
            <div class="config-control">
              <div class="config-title nowrap w45_px">数值</div>
              <div class="width-div flex-row">
                <div class="field-box">
                  <div class="field-div" :key="index" v-for="(item, index) in source.mysql.data.fields.numericalValue">
                    <span class="nowrap">{{ item.name }}</span>
                    <el-select v-model="item.calculation" size="mini" v-show="item.type === 'number'"
                              @change="commitUpdate('mysql')">
                      <el-option v-for="opt in numCalculation" :key="opt.value" :value="opt.value"
                                :label="opt.label"></el-option>
                    </el-select>
                    <el-select v-model="item.calculation" size="mini" v-show="item.type !== 'number'"
                              @change="commitUpdate('mysql')">
                      <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                                :label="opt.label"></el-option>
                    </el-select>
                    <i
                      class="el-icon-close el-input__icon"
                      @click="deleteMysqlField(index,'numericalValue')"
                      slot="suffix">
                    </i>
                  </div>
                  <span class="field-div add-field" v-if="source.mysql.data.tbName" @click="addMysqlField('numericalValue')"
                        size="mini">
                      <i class="el-icon-plus el-input__icon">
                      </i>
                      字段
                    </span>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="eidtor-wrap">
              <SqlEditor v-model="source.mysql.data.sql" :suggestions="suggestion.list" @focus="sqlEditorFocus" key="mysql-code"/>
            </div>
          </template>
          <div class="row-btn" :class="{ pl0: source.mysql.data.advanced }">
            <el-button type="primary" size="mini" @click="confirmConfig(source.mysql.data.sql)" :disabled="!needUpdate">确认配置</el-button>
          </div>
        </div>
      </el-step>
      <el-step title="配置字段" v-if="sourceType === 'dmdb'">
        <div class="slot-title" slot="title">
          <span class="c-title">配置字段</span>
        </div>
        <div class="content-block db-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">工作表名称</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap" :title="source.dmdb.data.tbName">{{ source.dmdb.data.tbName }}</div>
              <el-button @click="addDMdbField('dmdbTable')" class="ml10" type="plain" size="mini">选择</el-button>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap w45_px">维度</div>
            <div class="width-div flex-row">
              <div class="field-box">
                <span class="field-div" size="mini" :key="item.fid"
                      v-for="(item, index) in source.dmdb.data.fields.dimension">
                  {{ item.name }}
                  <i
                    class="el-icon-close el-input__icon"
                    @click="deleteDMdbField(index,'dimension')"
                    slot="suffix">
                  </i>
                </span>
                <span class="field-div add-field" v-if="source.dmdb.data.tbName" @click="addDMdbField('dimension')" size="mini">
                  <i class="el-icon-plus el-input__icon">
                  </i>
                  字段
                </span>
              </div>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap w45_px">数值</div>
            <div class="width-div flex-row">
              <div class="field-box">
                <div class="field-div" :key="index" v-for="(item, index) in source.dmdb.data.fields.numericalValue">
                  <span class="nowrap">{{ item.name }}</span>
                  <el-select v-model="item.calculation" size="mini" v-show="item.type === 'number'"
                             @change="commitUpdate('dmdb')">
                    <el-option v-for="opt in numCalculation" :key="opt.value" :value="opt.value"
                               :label="opt.label"></el-option>
                  </el-select>
                  <el-select v-model="item.calculation" size="mini" v-show="item.type !== 'number'"
                             @change="commitUpdate('dmdb')">
                    <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                               :label="opt.label"></el-option>
                  </el-select>
                  <i
                    class="el-icon-close el-input__icon"
                    @click="deleteDMdbField(index,'numericalValue')"
                    slot="suffix">
                  </i>
                </div>
                <span class="field-div add-field" v-if="source.dmdb.data.tbName" @click="addDMdbField('numericalValue')"
                      size="mini">
                    <i class="el-icon-plus el-input__icon">
                    </i>
                    字段
                  </span>
              </div>
            </div>
          </div>
          <div class="row-btn">
            <el-button type="primary" size="mini" @click="confirmConfig" :disabled="!needUpdate">确认配置</el-button>
          </div>
        </div>
      </el-step>
      <el-step title="配置字段" v-if="sourceType === 'excel'">
        <div class="slot-title" slot="title">
          <span class="c-title">配置字段</span>
        </div>
        <div class="content-block db-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">工作表名称</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap" :title="source.excel.data.tbName">{{ source.excel.data.tbName }}</div>
              <el-button @click="addExcelField('excelTable')" class="ml10" type="plain" size="mini">选择</el-button>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap w45_px">维度</div>
            <div class="width-div flex-row">
              <div class="field-box">
                <span class="field-div" size="mini" :key="item.fid"
                      v-for="(item, index) in source.excel.data.fields.dimension">
                  {{ item.name }}
                  <i
                    class="el-icon-close el-input__icon"
                    @click="deleteExcelfield(index,'dimension')"
                    slot="suffix">
                  </i>
                </span>
                <span class="field-div add-field" @click="addExcelField('dimension')" size="mini">
                  <i class="el-icon-plus el-input__icon">
                  </i>
                  字段
                </span>
              </div>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap w45_px">数值</div>
            <div class="width-div flex-row">
              <div class="field-box">
                <div class="field-div" :key="index" v-for="(item, index) in source.excel.data.fields.numericalValue">
                  <span class="nowrap">{{ item.name }}</span>
                  <el-select v-model="item.calculation" size="mini" @change="commitUpdate('excel')">
                    <el-option v-for="opt in numCalculation" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                  </el-select>
                  <i
                    class="el-icon-close el-input__icon"
                    @click="deleteExcelfield(index,'numericalValue')"
                    slot="suffix">
                  </i>
                </div>
                <span class="field-div add-field" @click="addExcelField('numericalValue')"
                      size="mini">
                    <i class="el-icon-plus el-input__icon">
                    </i>
                    字段
                  </span>
              </div>
            </div>
          </div>
          <div class="row-btn">
            <el-button type="primary" size="mini" @click="confirmConfig" :disabled="!needUpdate">确认配置</el-button>
          </div>
        </div>
      </el-step>
      <el-step title="配置字段" v-if="sourceType === 'postgresql'">
        <div class="slot-title" slot="title">
          <span class="c-title">配置字段</span>
        </div>
        <div class="content-block db-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">工作表名称</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap"
                   :title="source.postgresql.data.tbName">{{ source.postgresql.data.tbName }}
              </div>
              <el-button @click="addPostgresqlField('postgresqlTable')" class="ml10" type="plain" size="mini">选择
              </el-button>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">高级模式</div>
            <div class="width-div flex-row">
              <el-checkbox v-model="source.postgresql.data.advanced" @change="commitUpdate('postgresql')"></el-checkbox>
            </div>
          </div>
          <template v-if="!source.postgresql.data.advanced">
            <div class="config-control">
              <div class="config-title nowrap w45_px">维度</div>
              <div class="width-div flex-row">
                <div class="field-box">
                  <span class="field-div" size="mini" :key="item.fid"
                        v-for="(item, index) in source.postgresql.data.fields.dimension">
                    {{ item.name }}
                    <i
                      class="el-icon-close el-input__icon"
                      @click="deletePostgresqlField(index,'dimension')"
                      slot="suffix">
                    </i>
                  </span>
                  <span class="field-div add-field" v-if="source.postgresql.data.tbName" @click="addPostgresqlField('dimension')"
                        size="mini">
                    <i class="el-icon-plus el-input__icon">
                    </i>
                    字段
                  </span>
                </div>
              </div>
            </div>
            <div class="config-control">
              <div class="config-title nowrap w45_px">数值</div>
              <div class="width-div flex-row">
                <div class="field-box">
                  <div class="field-div" :key="index"
                      v-for="(item, index) in source.postgresql.data.fields.numericalValue">
                    <span class="nowrap">{{ item.name }}</span>
                    <el-select v-model="item.calculation" size="mini" v-show="item.type === 'number'"
                              @change="commitUpdate('postgresql')">
                      <el-option v-for="opt in numCalculation" :key="opt.value" :value="opt.value"
                                :label="opt.label"></el-option>
                    </el-select>
                    <el-select v-model="item.calculation" size="mini" v-show="item.type !== 'number'"
                              @change="commitUpdate('postgresql')">
                      <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                                :label="opt.label"></el-option>
                    </el-select>
                    <i
                      class="el-icon-close el-input__icon"
                      @click="deletePostgresqlField(index,'numericalValue')"
                      slot="suffix">
                    </i>
                  </div>
                  <span class="field-div add-field" v-if="source.postgresql.data.tbName"
                        @click="addPostgresqlField('numericalValue')"
                        size="mini">
                      <i class="el-icon-plus el-input__icon">
                      </i>
                      字段
                    </span>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="eidtor-wrap">
              <SqlEditor v-model="source.postgresql.data.sql" :suggestions="suggestion.list" @focus="sqlEditorFocus" key="postgresql-code"/>
            </div>
          </template>
          <div class="row-btn" :class="{ pl0: source.postgresql.data.advanced }">
            <el-button type="primary" size="mini" @click="confirmConfig(source.postgresql.data.sql)" :disabled="!needUpdate">确认配置</el-button>
          </div>
        </div>
      </el-step>
      <el-step title="配置字段" v-if="sourceType === 'highgodb'">
        <div class="slot-title" slot="title">
          <span class="c-title">配置字段</span>
        </div>
        <div class="content-block db-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">工作表名称</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap"
                   :title="source.highgodb.data.tbName">{{ source.highgodb.data.tbName }}
              </div>
              <el-button @click="addHighgodbField('highgodbTable')" class="ml10" type="plain" size="mini">选择
              </el-button>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">高级模式</div>
            <div class="width-div flex-row">
              <el-checkbox v-model="source.highgodb.data.advanced" @change="commitUpdate('highgodb')"></el-checkbox>
            </div>
          </div>
          <template v-if="!source.highgodb.data.advanced">
            <div class="config-control">
              <div class="config-title nowrap w45_px">维度</div>
              <div class="width-div flex-row">
                <div class="field-box">
                  <span class="field-div" size="mini" :key="item.fid"
                        v-for="(item, index) in source.highgodb.data.fields.dimension">
                    {{ item.name }}
                    <span v-show="item.type === 'date' && item.field_type === 0" class="field_name_type">{{item.calculation === 'caculate_year'?'(按年)':item.calculation === 'caculate_quarter'?'(按季)':item.calculation === 'caculate_month'? '(按月)':item.calculation === 'caculate_week'?'(按周)':item.calculation === 'caculate_day'?'(按日)':item.calculation === 'caculate_hour'?'(按时)':item.calculation === 'caculate_min'?'(按分)':item.calculation === 'caculate_second'?'(按秒)':''}}</span>
                    <i
                      class="el-icon-close el-input__icon"
                      @click="deleteHighgodbField(index,'dimension')"
                      slot="suffix">
                    </i>
                  </span>
                  <span class="field-div add-field" v-if="source.highgodb.data.tbName" @click="addHighgodbField('dimension')"
                        size="mini">
                    <i class="el-icon-plus el-input__icon">
                    </i>
                    字段
                  </span>
                </div>
              </div>
            </div>
            <div class="config-control">
              <div class="config-title nowrap w45_px">数值</div>
              <div class="width-div flex-row">
                <div class="field-box">
                  <div class="field-div" :key="index" v-for="(item, index) in source.highgodb.data.fields.numericalValue">
                    <span class="nowrap">{{ item.name }}</span>
                    <el-select v-model="item.calculation" size="mini" v-show="item.type === 'number'"
                              @change="commitUpdate('highgodb')">
                      <el-option v-for="opt in numCalculation" :key="opt.value" :value="opt.value"
                                :label="opt.label"></el-option>
                    </el-select>
                    <el-select v-model="item.calculation" size="mini" v-show="item.type !== 'number'"
                              @change="commitUpdate('highgodb')">
                      <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                                :label="opt.label"></el-option>
                    </el-select>
                    <span class="field_name_type">{{item.calculation === 'max'?'(最大值)':item.calculation === 'min'?'(最小值)':item.calculation === 'avg'? '(平均值)':item.calculation === 'count'?'(计数)':item.calculation === 'count_distinct'?'(去重计数)':item.calculation === 'sum'?'(求和)':''}}</span>
                    <i
                      class="el-icon-close el-input__icon"
                      @click="deleteHighgodbField(index,'numericalValue')"
                      slot="suffix">
                    </i>
                  </div>
                  <span class="field-div add-field" v-if="source.highgodb.data.tbName"
                        @click="addHighgodbField('numericalValue')"
                        size="mini">
                      <i class="el-icon-plus el-input__icon">
                      </i>
                      字段
                    </span>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="eidtor-wrap">
              <SqlEditor v-model="source.highgodb.data.sql" :suggestions="suggestion.list" @focus="sqlEditorFocus" key="highgodb-code"/>
            </div>
          </template>
          <div class="row-btn" :class="{ pl0: source.highgodb.data.advanced }">
            <el-button type="primary" size="mini" @click="confirmConfig(source.highgodb.data.sql)" :disabled="!needUpdate">确认配置</el-button>
          </div>
        </div>
      </el-step>
      <el-step title="配置字段" v-if="sourceType === 'oracle'">
        <div class="slot-title" slot="title">
          <span class="c-title">配置字段</span>
        </div>
        <div class="content-block db-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">工作表名称</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap" :title="source.oracle.data.tbName">{{ source.oracle.data.tbName }}</div>
              <el-button @click="addOracleField('oracleTable')" class="ml10" type="plain" size="mini">选择</el-button>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap">高级模式</div>
            <div class="width-div flex-row">
              <el-checkbox v-model="source.oracle.data.advanced" @change="commitUpdate('oracle')"></el-checkbox>
            </div>
          </div>
          <template v-if="!source.oracle.data.advanced">
            <div class="config-control">
              <div class="config-title nowrap w45_px">维度</div>
              <div class="width-div flex-row">
                <div class="field-box">
                  <span class="field-div" size="mini" :key="item.fid"
                        v-for="(item, index) in source.oracle.data.fields.dimension">
                    {{ item.name }}
                    <i
                      class="el-icon-close el-input__icon"
                      @click="deleteOracleField(index,'dimension')"
                      slot="suffix">
                    </i>
                  </span>
                  <span class="field-div add-field" v-if="source.oracle.data.tbName" @click="addOracleField('dimension')"
                        size="mini">
                    <i class="el-icon-plus el-input__icon">
                    </i>
                    字段
                  </span>
                </div>
              </div>
            </div>
            <div class="config-control">
              <div class="config-title nowrap w45_px">数值</div>
              <div class="width-div flex-row">
                <div class="field-box">
                  <div class="field-div" :key="index" v-for="(item, index) in source.oracle.data.fields.numericalValue">
                    <span class="nowrap">{{ item.name }}</span>
                    <el-select v-model="item.calculation" size="mini" v-show="item.type === 'number'"
                              @change="commitUpdate('oracle')">
                      <el-option v-for="opt in numCalculation" :key="opt.value" :value="opt.value"
                                :label="opt.label"></el-option>
                    </el-select>
                    <el-select v-model="item.calculation" size="mini" v-show="item.type !== 'number'"
                              @change="commitUpdate('oracle')">
                      <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                                :label="opt.label"></el-option>
                    </el-select>
                    <i
                      class="el-icon-close el-input__icon"
                      @click="deleteOracleField(index,'numericalValue')"
                      slot="suffix">
                    </i>
                  </div>
                  <span class="field-div add-field" v-if="source.oracle.data.tbName" @click="addOracleField('numericalValue')"
                        size="mini">
                      <i class="el-icon-plus el-input__icon">
                      </i>
                      字段
                    </span>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="eidtor-wrap">
              <SqlEditor v-model="source.oracle.data.sql" :suggestions="suggestion.list" @focus="sqlEditorFocus" key="oracle-code"/>
            </div>
          </template>
          <div class="row-btn" :class="{ pl0: source.oracle.data.advanced }">
            <el-button type="primary" size="mini" @click="confirmConfig(source.oracle.data.sql)" :disabled="!needUpdate">确认配置</el-button>
          </div>
        </div>
      </el-step>
      <el-step title="配置字段" v-if="sourceType === 'dmc'">
        <div class="slot-title" slot="title">
          <span class="c-title">配置字段</span>
        </div>
        <div class="content-block db-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">工作表名称</div>
            <div class="width-div flex-row">
              <div class="table-name nowrap" :title="source.dmc.data.tbName">{{ source.dmc.data.tbName }}</div>
              <el-button @click="addDmcField('dmcTable')" class="ml10" type="plain" size="mini">选择</el-button>
            </div>
          </div>
          <div class="config-control" v-if="source.dmc.data.tbName">
            <div class="config-title nowrap">添加字段</div>
            <div class="width-div flex-row">
              <el-button @click="addDmcField('calculationField')" type="plain" size="mini">计算字段</el-button>
              <el-button @click="addDmcField('groupField')" type="plain" size="mini">分组字段</el-button>
            </div>
          </div>
          <div class="config-control" v-if="source.dmc.data.tbName">
            <div class="width-div flex-row">
              <div class="field-box" style="width:100%">
                <span class="field-div" size="mini" :key="item.name" v-for="(item, index) in calfieldList">
                  {{ item.name }}
                  <i
                    @click="editCalfield(item)"
                    class="el-icon-edit el-input__icon"
                    slot="suffix">
                  </i>
                  <i
                    @click="deleteCalfield(item, index)"
                    class="el-icon-close el-input__icon"
                    slot="suffix">
                  </i>
                </span>
              </div>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap w45_px">维度</div>
            <div class="width-div flex-row">
              <div class="field-box">
                <span class="field-div" size="mini" :key="item.fid"
                      v-for="(item, index) in source.dmc.data.fields.dimension">
                  {{ item.name }}
                  <el-select v-model="item.calculation" size="mini" v-show="item.data_type === 'date' && item.field_type === 0"
                            @change="commitUpdate('dmc')">
                    <el-option v-for="opt in dateCalculation" :key="opt.value" :value="opt.value"
                              :label="opt.label"></el-option>
                  </el-select>
                  <i
                    class="el-icon-close el-input__icon"
                    @click="deleteDmcField(index,'dimension')"
                    slot="suffix">
                  </i>
                </span>
                <span class="field-div add-field" v-if="source.dmc.data.tbName" @click="addDmcField('dimension')" size="mini">
                  <i class="el-icon-plus el-input__icon">
                  </i>
                  字段
                </span>
              </div>
            </div>
          </div>
          <div class="config-control">
            <div class="config-title nowrap w45_px">数值</div>
            <div class="width-div flex-row">
              <div class="field-box">
                <div class="field-div" :key="index" v-for="(item, index) in source.dmc.data.fields.numericalValue">
                  <span class="nowrap">{{ item.name }}</span>
                  <el-select v-model="item.calculation" size="mini" v-show="item.data_type === 'number'"
                             @change="commitUpdate('dmc')">
                    <el-option v-for="opt in numCalculation" :key="opt.value" :value="opt.value"
                               :label="opt.label" ></el-option>
                  </el-select>
                  <el-select v-model="item.calculation" size="mini" v-show="item.data_type !== 'number' && item.data_type !== 'date'"
                             @change="commitUpdate('dmc')">
                    <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                               :label="opt.label" ></el-option>
                  </el-select>
                   <el-select v-model="item.calculation" size="mini" v-show="item.data_type === 'date' && item.field_type === 0"
                            @change="commitUpdate('dmc')">
                      <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                                :label="opt.label"></el-option>
                   </el-select>
                  <i
                    class="el-icon-close el-input__icon"
                    @click="deleteDmcField(index,'numericalValue')"
                    slot="suffix">
                  </i>
                </div>
                <span class="field-div add-field" v-if="source.dmc.data.tbName" @click="addDmcField('numericalValue')"
                      size="mini">
                    <i class="el-icon-plus el-input__icon">
                    </i>
                    字段
                  </span>
              </div>
            </div>
          </div>
          <div class="row-btn">
            <el-button type="primary" size="mini" @click="confirmConfig" :disabled="!needUpdate">确认配置</el-button>
          </div>
        </div>
      </el-step>
      <el-step title="后端数据筛选"
        v-if="['dmc', 'mysql', 'postgresql', 'dmdb', 'oracle', 'excel', 'highgodb'].includes(sourceType)"
        >
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">
              数据筛选：
            </div>
            <div class="width-div">
              <el-button @click="openDataSelect" type="plain" size="mini" plain>
                <span v-if="selectList">已添加{{ selectList }}个筛选条件</span>
                <span v-else>添加筛选条件</span>
              </el-button>
            </div>
          </div>
        </div>
      </el-step>
      <el-step title="前端数据处理">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">
              <el-checkbox v-if="!filtersEnable" v-model="filtersEnable" class="mr5"></el-checkbox>
              数据过滤器：
            </div>
            <div class="width-div">
              <el-button @click="filterConfig" type="plain" size="mini" plain>
                <span v-if="!!filterNum">已启用{{ filterNum }}个过滤器</span>
                <span v-else>添加过滤器</span>
              </el-button>
            </div>
          </div>
        </div>
      </el-step>
      <el-step title="显示条目数"
        v-if="['csv_file', 'api', 'mysql', 'dmdb','dmc', 'postgresql', 'oracle', 'dashboard', 'excel', 'highgodb'].includes(sourceType)">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">
              <el-checkbox v-model="source[sourceType].data.isLimit" @change="commitUpdate(sourceType)"
                            class="mr5"></el-checkbox>
              开启：
            </div>
            <div class="width-div">
              <el-input-number v-model="source[sourceType].data.limitNum" :min="0" controls-position="right"
                                @change="commitUpdate(sourceType)" size="mini" class="w100">
              </el-input-number>
            </div>
          </div>
        </div>
      </el-step>
      <el-step title="映射字段" :status="mappingStatus" :class="{error: mappingStatus==='error'}">
        <div class="content-block pb10" slot="description">
          <el-table class="mapping-t" :data="fieldMapping.length ? fieldMapping : noList" size="mini">
            <el-table-column label="字段" width="70" align="center">
              <template slot-scope="scope">
                <span style="color: #F56C6C;" v-if="!isOptional(scope.row.source)">*</span>
                <span style="margin-left: 5px">{{ scope.row.source }}</span>
              </template>
            </el-table-column>
            <el-table-column label="映射" prop="target" align="center" class="map" width="80">
              <template slot-scope="scope">
                <el-select
                  v-if="fieldMapping.length"
                  v-model="scope.row.target"
                  size="mini"
                  clearable
                  :title="scope.row.target"
                  @change="saveFieldMapping">
                  <el-option v-for="opt in fieldOptions" :key="opt.id" :value="opt.name" :label="opt.name"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="说明" prop="description" show-overflow-tooltip></el-table-column>
            <el-table-column label="状态" align="center" width="60">
              <template slot-scope="scope">
                <span v-if="isMatch(scope.row) === 0">未匹配</span>
                <span v-if="isMatch(scope.row) === 1" class="el-icon-check t-icon green"></span>
                <span v-if="isMatch(scope.row) === 2" class="el-icon-close t-icon red"></span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-step>
      <el-step title="数据更新" v-if="sourceType !== 'websocket'">
        <div class="content-block" slot="description">
          <div class="config-control">
            <div class="config-title nowrap">
              <el-checkbox v-model="autoUpdateEnable" size="mini" class="mr5"></el-checkbox>
              自动更新
            </div>
            <div class="width-div" v-if="autoUpdateEnable">
              每
              <el-input-number v-model="autoUpdateTime" class="mlr5" controls-position="right" size="mini"
                               :min="1"></el-input-number>
              秒请求一次
            </div>
          </div>
          <div class="tips" v-if="autoUpdateEnable"><font color="#f56c6c">*</font>提示：自动更新设置仅在预览页面生效</div>
        </div>
      </el-step>
      <el-step title="无数据提示" v-if="currentCom.type === 'com'">
        <div slot="description">
          <div class="content-block">
            <div class="config-control">
              <div class="config-title nowrap">
                <el-checkbox v-model="tips.open" size="mini" class="mr5" @change="saveTipsShow"></el-checkbox>
                开启
              </div>
            </div>
            <div class="config-control">
              <template v-if="tips.open">
                <el-tabs v-model="activeTab" type="card" size="mini" editable class="tab-b w100" @edit="handleTipsEdit">
                  <el-tab-pane :name="i + ''" :label="'条件' + (i + 1)" v-for="(item, i) in tips.conditions" :key="i">
                    <div class="config-control">
                      <div class="config-title nowrap">
                        匹配类型：
                      </div>
                      <div class="width-div">
                        <el-select v-model="item.type" size="mini" class="w100" @change="value => handleConfigTreeChange('type', value, i)">
                          <el-option label="数据为空" value="empty"></el-option>
                          <el-option label="自定义条件" value="diy"></el-option>
                        </el-select>
                      </div>
                    </div>
                    <template v-if="item.type == 'diy'">
                      <div class="config-control">
                        <div class="config-title nowrap">
                          匹配字段
                          <el-tooltip content="匹配数据第一项">
                            <span class="el-icon-info"></span>
                          </el-tooltip>
                        </div>
                        <div class="width-div">
                          <el-select v-model="item.field" size="mini" class="w100" @change="value => handleConfigTreeChange('field', value, i)">
                            <el-option v-for="opt in fieldOptions" :key="opt.id" :value="opt.name" :label="opt.name"></el-option>
                          </el-select>
                        </div>
                      </div>
                      <div class="config-control">
                        <div class="config-title nowrap">
                          满足条件：
                        </div>
                        <div class="width-div flex-row">
                          <el-select v-model="item.condition" size="mini" style="width:70px;flex:none;margin-right:5px;" @change="value => handleConfigTreeChange('condition', value, i)">
                            <el-option label="等于" value="=="></el-option>
                            <el-option label="大于" value=">"></el-option>
                            <el-option label="小于" value="<"></el-option>
                            <el-option label="大于等于" value=">="></el-option>
                            <el-option label="小于等于" value="<="></el-option>
                          </el-select>
                          <el-input v-model="item.value" size="mini" @change="value => handleConfigTreeChange('value', value, i)"></el-input>
                        </div>
                      </div>
                    </template>
                    <div class="config-control">
                      <div class="config-title nowrap">
                        提示信息：
                      </div>
                      <div class="width-div">
                        <el-input v-model="item.info" size="mini" placeholder="请输入提示信息" @change="value => handleConfigTreeChange('info', value, i)"></el-input>
                      </div>
                    </div>
                    <div class="config-control">
                      <ConfigTree
                        :treeData="tipsControlConfig"
                        :configObj="item"
                        @change="({path, value}) => handleConfigTreeChange(path, value, i)" />
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </template>
            </div>
          </div>
        </div>
      </el-step>
    </el-steps>
    <!-- 查看响应结果 -->
    <div class="result-btn">
      <el-button type="primary" size="mini" @click="getResult">查看数据响应结果</el-button>
    </div>
    <!-- 创建数据源 -->
    <template v-if="['csv_file', 'excel', 'json', 'api', 'mysql', 'dmdb', 'postgresql', 'oracle', 'mongodb', 'websocket', 'highgodb'].includes(sourceType)">
      <CreateDataSource ref="create" @update="updateDatastorage" :type="sourceType"/>
    </template>
    <template v-if="sourceType === 'dmc'">
      <!-- 选择dmc工作表 -->
      <CreateDmcTable ref="createDmcTable" @update="getDmcTable"/>
      <!-- 新增dmc计算字段 -->
      <CreateCalculationField ref="createCalculationField" @update="getCaField"/>
      <!-- 新增dmc分组字段 -->
      <CreateGroupField ref="createGroupField" @update="getCaField"/>
      <!-- 选择dmc维度、数值字段 -->
      <SelectAllField ref="selectAllField" @update="getSelectAllField"/>
    </template>
    <!-- mysql -->
    <template v-if="sourceType === 'mysql'">
      <CreateMySqlTable ref="createMySqlTable" @update="getMysqlTable" />
      <SelectMysqlFields ref="selectMysqlFields" @update="getSelectMysqlFields"/>
    </template>
    <!-- highgodb数据库 -->
    <template v-if="sourceType === 'highgodb'">
      <SetFormat ref="setFormat" @update="setNumFormat"/>
      <SetDate ref="setDate" @update="setDateFormat"/>
      <SetOtherName ref="setOtherName" @update="setName"/>
      <CreateHighgodbTable ref="createHighgodbTable" @update="getHighgodbTable" />
      <SelectHighgodbFields ref="selectHighgodbFields" @update="getSelectHighgodbFields"/>
    </template>
    <!-- 达梦数据库 -->
    <template v-if="sourceType === 'dmdb'">
      <CreateDMdbTable ref="createDMdbTable" @update="getDMdbTable" />
      <SelectDMdbFields ref="selectDMdbFields" @update="getSelectDMdbFields"/>
    </template>
    <!-- postgresql -->
    <template v-if="sourceType === 'postgresql'">
      <CreatePostgresqlTable ref="createPostgresqlTable" @update="getPostgresqlTable"/>
      <SelectPostgresqlFields ref="selectPostgresqlFields" @update="getSelectPostgresqlFields"/>
    </template>
    <!-- oracle -->
    <template v-if="sourceType === 'oracle'">
      <CreateOracleTable ref="createOracleTable" @update="getOracleTable"/>
      <SelectOracleFields ref="selectOracleFields" @update="getSelectOracleFields"/>
    </template>
    <!-- 数据筛选 -->
    <template v-if="['dmc', 'bdp', 'mysql', 'postgresql','dmdb', 'oracle', 'excel', 'highgodb'].includes(sourceType)">
      <DataSelect ref="dataSelect" @update="updateDataSelect" :type="sourceType" />
    </template>
    <!-- excel -->
    <template v-if="sourceType === 'excel'">
      <CreateExcelTable ref="createExcelTable" @update="getExcelTable" />
      <SelectExcelFields ref="selectExcelFields" @update="getSelectExcelFields"/>
    </template>
    <!-- 从仪表盘导入 -->
    <CreateDashBoard v-if="sourceType === 'dashboard'" ref="createDash"  @update="getDashList" />
    <!-- mongodb -->
    <CreateMongoDBTable v-if="sourceType === 'mongodb'" ref="createMongoDBTable" @update="getMongoDBTable" />
    <!-- 动态参数 -->
    <DynamicParams v-if="sourceType === 'api'" ref="dynamicParams" @updateApiFunc="updateApiFunc" />
    <!-- 过滤器配置 -->
    <FilterConfig ref="filter" :sourceData="sourceData" />
    <!-- 数据量过大提醒 -->
    <LimitDataTip ref="limit" @update="updateLimitData" />
    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>
<script>
import { datastorageList, postData, getFieldList } from '@/api/datastorage'
import { calculatefieldList, deleteCalfield } from '@/api/calculatefield'
import { mapState, mapGetters } from 'vuex'
import dataUtil from '@/utils/data'
import { transDrillData } from '@/utils/base'
import emitter from '@/utils/bus'
import { dateCalculation, numCalculation, stringCalculation, sourceTypeOpt, methodOpt, limitOption, timeOption, tipsControlConfig } from '@/common/constants'

export default {
  name: 'CompData',
  props: {},
  inject: ['callbackManager'],
  components: {
    CreateDataSource: () => import('./CreateDataSource'),
    CreateDmcTable: () => import('./CreateDmcTable'),
    CreateCalculationField: () => import('./CreateCalculationField'),
    CreateGroupField: () => import('./CreateGroupField'),
    SelectAllField: () => import('./SelectAllField'),
    DataSelect: () => import('./DataSelect.vue'),
    CreateMySqlTable: () => import('./CreateMySqlTable'),
    CreateHighgodbTable: () => import('./CreateHighgodbTable'),
    SelectHighgodbFields: () => import('./SelectHighgodbFields'),
    CreateDMdbTable: () => import('./CreateDMdbTable'),
    SelectDMdbFields: () => import('./SelectDMdbFields'),
    SelectMysqlFields: () => import('./SelectMysqlFields'),
    CreateMongoDBTable: () => import('./CreateMongoDBTable'),
    CreatePostgresqlTable: () => import('./CreatePostgresqlTable'),
    SelectPostgresqlFields: () => import('./SelectPostgresqlFields'),
    CreateOracleTable: () => import('./CreateOracleTable'),
    SelectOracleFields: () => import('./SelectOracleFields'),
    CreateDashBoard: () => import('./CreateDashBoard'),
    CreateExcelTable: () => import('./CreateExcelTable'),
    SelectExcelFields: () => import('./SelectExcelFields'),
    CodeEditor: () => import('./CodeEditor'),
    SqlEditor: () => import('./SqlEditor'),
    JsonEditor: () => import('./JsonEditor'),
    FilterConfig: () => import('./FilterConfig'),
    LimitDataTip: () => import('./LimitDataTip'),
    DynamicParams: () => import('./DynamicParams')
  },
  data () {
    return {
      dateCalculation,
      currentCompId: '',
      calfieldList: [],
      stringCalculation,
      numCalculation,
      tbid: '',
      dmc: {
        data: {
          name: '',
          sql: ''
        }
      },
      noList: [{ source: '任意' }],
      sourceList: [], // 数据源列表
      sourceData: '[]',
      jsonData: [],
      source: {
        api: {
          data: {
            sourceId: '',
            baseUrl: '',
            method: '',
            headers: '{}',
            path: '',
            params: '',
            body: '{}',
            reqFromBack: false,
            needCookie: false,
            isLimit: false,
            limitNum: 20
          }
        },
        csv_file: {
          data: {
            sourceId: '',
            isLimit: false,
            limitNum: 20
          }
        },
        dashboard: {
          data: {
            name: '',
            sourceId: '',
            isLimit: false,
            limitNum: 20
          }
        },
        json: {
          data: {
            sourceId: ''
          }
        },
        mysql: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            advanced: false,
            sql: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        highgodb: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            advanced: false,
            sql: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        dmdb: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        mongodb: {
          data: {
            sourceId: '',
            tbName: '',
            sql: '',
            isLimit: false,
            limitNum: 20
          }
        },
        postgresql: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            advanced: false,
            sql: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        oracle: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            advanced: false,
            sql: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        websocket: {
          data: {
            duration: 10,
            durationType: 'minute',
            sourceId: ''
          }
        },
        dmc: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        excel: {
          data: {
            tbName: '',
            sourceId: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        datacontainer: {
          data: {
            dataContainerComId: ''
          }
        }
      },
      needUpdate: true,
      fieldType: '',
      sourceTypeOpt,
      methodOpt,
      limitOption,
      timeOption,
      loading: false,
      editorOpt: {
        mode: 'application/json',
        lint: true
      },
      sqlOpt: {
        mode: 'text/x-sparksql',
        lint: true
      },
      activeTab: '0',
      tipsControlConfig,
      tips: {
        open: false,
        conditions: []
      },
      suggestion: {
        tbName: '',
        list: []
      }
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      currentSelectId: state => state.editor.currentSelectId,
      comsData: state => state.editor.comsData,
      screenFilters: state => state.editor.screenFilters,
      dataMappingErrorCompIds: state => state.editor.dataMappingErrorCompIds,
      inheritData: state => state.editor.inheritData,
      indicatorData: state => state.indicator.indicatorData,
      screenComs: state => state.editor.screenComs,
      containerData: state => state.datacontainer.containerData
    }),
    ...mapGetters('editor', ['getComDataById', 'currentCom', 'currentConfigId']),
    validFilters () {
      if (_.isEmpty(this.currentCom)) return []
      const filters = this.currentCom.dataConfig.dataResponse.filters
      if (!filters.enable) return []
      return _.filter(filters.list, { enable: true }).map(({ id }) => this.screenFilters[id])
    },
    drillDown () {
      if (_.isEmpty(this.currentCom)) return []
      return this.currentCom.interactionConfig.drillDown
    },
    fieldOptions () { // 字段映射集合
      let result = []
      try {
        const keys = []
        const sourceData = JSON.parse(this.sourceData)
        let filterData = dataUtil.filterData(sourceData || [], this.validFilters)

        if (this.drillDown.length) {
          this.drillDown.forEach(e => {
            if (e.linkType === 'links') {
              filterData = transDrillData(filterData, e);
              filterData = filterData.map(item => _.omit(item, ['_id', '_parentId']));
            }
          })
        }
        filterData.forEach(item => {
          const key = Object.keys(item)
          keys.push(...key)
        })
        const uniKeys = Array.from(new Set(keys))
        result = uniKeys.map((key, idx) => {
          return {
            id: idx,
            name: key
          }
        })
      } catch (e) {}
      return result
    },
    fieldMapping () { // 字段映射
      return _.cloneDeep(this.currentCom.dataConfig.fieldMapping)
    },
    mappingStatus () {
      return this.dataMappingErrorCompIds.findIndex(item => item === this.currentCom.id) > -1 ? 'error' : 'wait'
    },
    isMatch () { // 匹配状态 0：未匹配 1：匹配成功 2：匹配失败
      return function (item) {
        if (item.target === '' || item.target === undefined) {
          return 0
        }
        return item.target &&
        this.fieldOptions.findIndex(opt => opt.name === item.target) > -1 ? 1 : 2
      }
    },
    isOptional () { // 判断字段是否必填
      return function (source) {
        const fields = this.currentCom.dataConfig.fields
        const matchField = fields.find(field => field.name === source)
        return matchField && matchField.optional
      }
    },
    sourceType: { // 数据源类
      get: function () {
        return this.currentCom?.dataConfig.dataResponse.sourceType
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'dataConfig.dataResponse.sourceType', value: val }
          ]
        }).then(() => {
          this.getData()
        })
      }
    },
    filtersEnable: { // 是否启用过滤器
      get: function () {
        return this.currentCom.dataConfig.dataResponse.filters.enable
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'dataConfig.dataResponse.filters.enable', value: val }
          ]
        }).then(() => {
          this.$store.dispatch('editor/updateDataMappingErrorCompIds', {
            componentId: this.currentCom.id,
            data: this.comsData[this.currentCom.id]
          })
        })
      }
    },
    filterNum () { // 启用的过滤器数量
      const filters = this.currentCom.dataConfig.dataResponse.filters.list;
      const enableFilters = filters.filter(item => item.enable);
      return enableFilters.length
    },
    selectList () {
      const where = this.currentCom.dataConfig.dataResponse.source[this.sourceType] ? this.currentCom.dataConfig.dataResponse.source[this.sourceType].data.where : null
      if (where) {
        return where.whereCondition.length + where.orderCondition.length
      } else {
        return 0
      }
    },
    autoUpdateEnable: {
      get: function () {
        return this.currentCom.dataConfig.dataResponse.autoUpdate.enable
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'dataConfig.dataResponse.autoUpdate.enable', value: val }
          ]
        })
      }
    },
    autoUpdateTime: {
      get: function () {
        return this.currentCom.dataConfig.dataResponse.autoUpdate.time
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'dataConfig.dataResponse.autoUpdate.time', value: val }
          ]
        })
      }
    },
    dataContainerList () { // 数据容器列表
      const coms = Object.values(this.screenComs);
      const list = coms.filter(item => item.comName === 'interaction-container-datacontainer');
      const result = list.map(item => {
        return {
          id: item.id,
          name: item.alias
        }
      })
      return result
    },
    islocalLogin () {
      // 是否为本地登录
      return !!window.localStorage.getItem('localLogin')
    },
    computedSourceTypeOpt () {
      let opts = _.cloneDeep(this.sourceTypeOpt);
      if (this.currentCom.comName === 'interaction-container-datacontainer') { // 数据容器不能再选择数据容器数据源
        opts = opts.filter(item => item.value !== 'datacontainer');
      }
      if (this.islocalLogin) { // 本地登陆 去掉dmc和dashboard数据源
        opts = opts.filter(item => !['dmc', 'dashboard'].includes(item.value));
      }
      // 指标编辑页只显示静态数据和父数据源
      if (this.$route.name === 'screen/indicator/edit') {
        return [
          { value: 'static', label: '静态数据' },
          { value: 'inherit', label: '父组件数据源' }
        ]
      }
      // pitch和dialog组件 加入父数据源选项
      const comp = this.$route.query.comp;
      if (comp === 'pitch') {
        opts.push({ value: 'inherit', label: '父组件数据源' });
      } else if (comp === 'dialog') {
        opts.push({ value: 'dialog', label: '父组件数据源' })
      }
      return opts
    },
    sqlOptions () {
      return {
        sourceType: this.sourceType,
        mysql: this.source.mysql.data,
        oracle: this.source.oracle.data,
        postgresql: this.source.postgresql.data
      }
    }
  },
  watch: {
    sourceType: { // 数据源类型change 设置静态数据或请求数据源列表
      handler: function (val) {
        this.sourceData = '[]'
        this.getDatasourceList(val)
      },
      immediate: true
    },
    // 子组件ID 变化时执行
    currentConfigId: {
      handler: function (val) {
        this.$nextTick(() => {
          this.source = _.cloneDeep(this.currentCom.dataConfig.dataResponse.source)
          this.sourceData = JSON.stringify(this.comsData[this.currentConfigId] || [])
          this.jsonData = JSON.parse(this.sourceData)
          const tips = _.cloneDeep(this.currentCom.dataConfig.dataResponse.tips || {});
          this.tips = Object.assign({}, tips);
          this.getData()
        })
      },
      immediate: true
    },
    validFilters (val) {
      this.$store.dispatch('editor/updateDataMappingErrorCompIds', {
        componentId: this.currentCom.id,
        data: this.comsData[this.currentCom.id]
      })
    }
  },
  mounted () {
    this.$nextTick(function () {
      this.currentCompId = this.currentCom.id; // fix bug
      this.calculatefieldList()
    })
  },
  created () {
    emitter.on('updateSocketData', data => {
      // 监听实时数据变化，更新数据响应结果
      this.sourceData = JSON.stringify(data)
    })
  },
  beforeDestroy () {
    emitter.off('updateSocketData')
  },
  methods: {
    onHover (index) {
      this.source.dmc.data.fields.numericalValue.forEach(it => {
        it.isShow = false
      })
      this.source.dmc.data.fields.numericalValue[index].isShow = true
    },
    YoYopen (index) {
      for (const it of document.getElementsByClassName('el-popover-self')) {
        it.style['background-color'] = '#1f2430'
        it.style.border = '1px solid #3a4659'
        it.style.color = 'rgba(255, 255, 255, 0.7)'
        it.style['padding-left'] = '0px'
        it.style['padding-right'] = '0px'
        for (const item of it.children) {
          for (const i of item.children) {
            i.style.height = '34px'
            i.style['line-height'] = '34px'
            i.style['padding-left'] = '20px'
          }
        }
      }
      // document.getElementsByClassName('el-popover-self')[index].style['background-color'] = '#1f2430'
      // document.getElementsByClassName('el-popover-self')[index].style.border = '1px solid #3a4659'
      // document.getElementsByClassName('el-popover-self')[index].style.color = 'rgba(255, 255, 255, 0.7)'
      // document.getElementsByClassName('el-popover-self')[index].style['padding-left'] = '0px'
      // document.getElementsByClassName('el-popover-self')[index].style['padding-right'] = '0px'

      // for (const item of document.getElementsByClassName('el-popover-self')[index].children) {
      //   item.style.height = '34px'
      //   item.style['line-height'] = '34px'
      //   item.style['padding-left'] = '20px'
      //   console.log(item.style.height, 'item');
      // }
    },
    YoYClick (item, it, idx) {
      this.categoryArr[this.status].isHighlighted = false;
      this.categoryArr[idx].isHighlighted = true;
      this.status = idx;
      this.popShow = false
      item.YoY = it.YoY
      item.yoyQoqType = it.yoyQoqType
      const popoverRef = this.$refs.popoverRef
      popoverRef.forEach(it => {
        it.doClose()
      })
      this.commitUpdate('dmc', undefined, false)
    },
    setNumFormat (unitAdv, params, index, sourceType) {
      switch (sourceType) {
        case 'bdp':
          this.source.bdp.data.fields.numericalValue[index].formatter = params
          this.commitUpdate('bdp')
          break
        case 'dmc':
          this.source.dmc.data.fields.numericalValue[index].formatter = params
          this.commitUpdate('dmc')
          break
        case 'oracle':
          this.source.oracle.data.fields.numericalValue[index].formatter = params
          this.commitUpdate('oracle')
          break
        case 'mysql':
          this.source.mysql.data.fields.numericalValue[index].formatter = params
          this.commitUpdate('mysql')
          break
        case 'highgodb':
          this.source.highgodb.data.fields.numericalValue[index].formatter = params
          this.commitUpdate('highgodb')
          break
        case 'excel':
          this.source.excel.data.fields.numericalValue[index].formatter = params
          this.commitUpdate('excel')
          break
        case 'postgresql':
          this.source.postgresql.data.fields.numericalValue[index].formatter = params
          this.commitUpdate('postgresql')
          break
      }
      // this.source.dmc.data.fields.numericalValue[index].unit_adv = unitAdv
      // this.source.dmc.data.fields.numericalValue[index].formatter = params
      // this.commitUpdate('dmc')
    },
    setDateFormat (params, index) {
      this.source.highgodb.data.fields.dimension[index].dateFormatter = params
      this.commitUpdate('highgodb')
    },
    setName (name, describe, index, type, dataType) {
      switch (dataType) {
        case 'dmc':
          if (type === 'numericalValue') {
            this.source.dmc.data.fields.numericalValue[index].name = name
            this.source.dmc.data.fields.numericalValue[index].describe = describe
          } else if (type === 'dimension') {
            this.source.dmc.data.fields.dimension[index].name = name
            this.source.dmc.data.fields.dimension[index].describe = describe
          }
          this.commitUpdate('dmc')
          break;
        case 'oracle':
          if (type === 'numericalValue') {
            this.source.oracle.data.fields.numericalValue[index].name = name
            this.source.oracle.data.fields.numericalValue[index].describe = describe
          } else if (type === 'dimension') {
            this.source.oracle.data.fields.dimension[index].name = name
            this.source.oracle.data.fields.dimension[index].describe = describe
          }
          this.commitUpdate('oracle')
          break;
        case 'mysql':
          if (type === 'numericalValue') {
            this.source.mysql.data.fields.numericalValue[index].name = name
            this.source.mysql.data.fields.numericalValue[index].describe = describe
          } else if (type === 'dimension') {
            this.source.mysql.data.fields.dimension[index].name = name
            this.source.mysql.data.fields.dimension[index].describe = describe
          }
          this.commitUpdate('mysql')
          break;
        case 'highgodb':
          if (type === 'numericalValue') {
            this.source.highgodb.data.fields.numericalValue[index].name = name
            this.source.highgodb.data.fields.numericalValue[index].describe = describe
          } else if (type === 'dimension') {
            this.source.highgodb.data.fields.dimension[index].name = name
            this.source.highgodb.data.fields.dimension[index].describe = describe
          }
          this.commitUpdate('highgodb')
          break
        case 'excel':
          if (type === 'numericalValue') {
            this.source.excel.data.fields.numericalValue[index].name = name
            this.source.excel.data.fields.numericalValue[index].describe = describe
          } else if (type === 'dimension') {
            this.source.excel.data.fields.dimension[index].name = name
            this.source.excel.data.fields.dimension[index].describe = describe
          }
          this.commitUpdate('excel')
          break;
        case 'postgresql':
          if (type === 'numericalValue') {
            this.source.postgresql.data.fields.numericalValue[index].name = name
            this.source.postgresql.data.fields.numericalValue[index].describe = describe
          } else if (type === 'dimension') {
            this.source.postgresql.data.fields.dimension[index].name = name
            this.source.postgresql.data.fields.dimension[index].describe = describe
          }
          this.commitUpdate('postgresql')
      }
    },
    sortChangeDimension (type, e) {
      this.getSelectAllFieldChange(this.source.dmc.data.fields.dimension, type)
    },
    sortChangenNmericalValue (type, e) {
      this.getSelectAllFieldChange(this.source.dmc.data.fields.numericalValue, type)
    },
    displayFormat (item, index, type) {
      for (let i = 0; i < this.$refs.configSelect.length; i++) {
        this.$refs.configSelect[i].blur()
      }
      this.$refs.setFormat.showDialog(item, index, type)
    },
    displayDate (item, index) {
      for (let i = 0; i < this.$refs.configSelect.length; i++) {
        this.$refs.configSelect[i].blur()
      }
      this.$refs.setDate.showDialog(item, index)
    },
    openOtherName (item, index, type, dataType) {
      for (let i = 0; i < this.$refs.configSelect.length; i++) {
        this.$refs.configSelect[i].blur()
      }
      this.$refs.setOtherName.showDialog(item, index, type, dataType)
    },
    onOptionHover (isHovered, index) {
      this.isOptionHovered = isHovered
      this.$nextTick(() => {
        if (!this.isOptionHovered) {
          this.$refs.selectwd.forEach(it => {
            it.hover = false
          })
          // this.$refs.selectwd[index].hover = false
        }
      });
    },
    onOptionNumHover (isNumHovered, index) {
      this.isNumOptionHovered = isNumHovered
      this.$nextTick(() => {
        if (!this.isNumOptionHovered) {
          this.$refs.selectNum.forEach(it => {
            it.hover = false
          })
          // this.$refs.selectNum[index].hover = false
        }
      });
    },
    changeSocketDuration (val) {
      this.commitUpdate('websocket').then(() => {
        emitter.emit('updateSocketDuration', this.currentSelectId)
      })
    },
    editCalfield (item) {
      if (item.field_type === 1) {
        this.$refs.createCalculationField.showDialog(this.source.dmc.data, item)
      } else {
        this.$refs.createGroupField.showDialog(this.source.dmc.data, item)
      }
    },
    async deleteCalfield (item, index) {
      const data = {
        workspaceId: this.screenInfo.workspaceId,
        tbId: this.source.dmc.data.tbId,
        name: item.name,
        fid: item.fid
      }
      const res = await deleteCalfield(data)
      if (res && res.success) {
        this.calculatefieldList()
      } else {
        this.$message.error(res.message)
      }
    },
    async calculatefieldList () {
      if (!this.source.dmc.data.tbId) {
        this.calfieldList = []
        return
      }
      const data = {
        workspaceId: this.screenInfo.workspaceId,
        tbId: this.source.dmc.data.tbId
      }
      const res = await calculatefieldList(data)
      if (res && res.success) {
        this.calfieldList = res.data
        this.comUpdate()
      }
    },
    deleteDmcField (index, type) {
      this.source.dmc.data.fields[type].splice(index, 1)
      this.commitUpdate('dmc')
    },
    deleteMysqlField (index, type) {
      this.source.mysql.data.fields[type].splice(index, 1)
      this.commitUpdate('mysql')
    },
    deleteHighgodbField (index, type) {
      this.source.highgodb.data.fields[type].splice(index, 1)
      this.commitUpdate('highgodb')
    },
    deleteDMdbField (index, type) {
      this.source.dmdb.data.fields[type].splice(index, 1)
      this.commitUpdate('dmdb')
    },
    deleteExcelfield (index, type) {
      this.source.excel.data.fields[type].splice(index, 1)
      this.commitUpdate('excel')
    },
    async saveFieldMapping () { // 保存映射字段
      const fieldMapping = this.currentCom.dataConfig.fieldMapping
      if (!_.isEqual(fieldMapping, this.fieldMapping)) {
        await this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'dataConfig.fieldMapping', value: this.fieldMapping }
          ]
        })
        if (this.sourceType !== 'dialog' && this.sourceType !== 'inherit') {
          await this.$store.dispatch('editor/updateDataMappingErrorCompIds', {
            componentId: this.currentCom.id,
            data: this.comsData[this.currentCom.id]
          })
        }
      }
    },
    deletePostgresqlField (index, type) {
      this.source.postgresql.data.fields[type].splice(index, 1)
      this.commitUpdate('postgresql')
    },
    deleteOracleField (index, type) {
      this.source.oracle.data.fields[type].splice(index, 1)
      this.commitUpdate('oracle')
    },
    async targetChange () { // 修改映射字段
      await this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'dataConfig.fieldMapping', value: this.fieldMapping }
        ]
      })
      await this.$store.dispatch('editor/updateDataMappingErrorCompIds', {
        componentId: this.currentCom.id,
        data: this.comsData[this.currentCom.id]
      })
    },
    async getDatasourceList (sourceType) { // 获取数据源列表
      const data = {
        workspaceId: this.screenInfo.workspaceId,
        type: sourceType
      }
      const res = await datastorageList(data)
      if (res && res.success) {
        if (res.data.length === 0) {
          this.sourceList = []
          return;
        }
        this.sourceList = res.data.map(item => {
          return {
            ...item,
            id: item.id + ''
          }
        })
      }
    },
    createData () { // 创建数据源
      this.$refs.create.showDialog(this.sourceType)
    },
    createDashData (type) { // 仪表盘数据源
      this.$refs.createDash.showImport(type);
    },
    editDynamicParams () { // 编辑动态参数
      this.$refs.dynamicParams.showDialog(this.source.api.data.func);
    },
    updateApiFunc (func) {
      this.source.api.data.func = func;
      this.commitUpdate('api');
    },
    addDmcField (type) { // 添加dmc字段
      this.fieldType = type
      switch (type) {
        case 'dmcTable' :
          this.$refs.createDmcTable.showDialog()
          break
        case 'calculationField' :
          this.$refs.createCalculationField.showDialog(this.source.dmc.data)
          break
        case 'groupField' :
          this.$refs.createGroupField.showDialog(this.source.dmc.data)
          break
        case 'dimension' :
          this.$refs.selectAllField.selectDimension(this.source.dmc.data)
          break
        case 'numericalValue' :
          this.$refs.selectAllField.showDialog(this.source.dmc.data)
          break
      }
    },
    addMysqlField (type) { // 添加mysql字段
      const vm = this
      this.fieldType = type
      switch (type) {
        case 'mysqlTable':
          vm.$refs.createMySqlTable.showDialog()
          break
        case 'dimension':
          vm.$refs.selectMysqlFields.showDialog(vm.source.mysql.data)
          break
        case 'numericalValue':
          vm.$refs.selectMysqlFields.showDialog(vm.source.mysql.data)
          break
      }
    },
    addHighgodbField (type) { // 添加gbase8s字段
      const vm = this
      this.fieldType = type
      switch (type) {
        case 'highgodbTable':
          vm.$refs.createHighgodbTable.showDialog()
          break
        case 'dimension':
          vm.$refs.selectHighgodbFields.selectDimension(vm.source.highgodb.data, type)
          break
        case 'numericalValue':
          vm.$refs.selectHighgodbFields.showDialog(vm.source.highgodb.data, type)
          break
      }
    },
    addDMdbField (type) { // 添加达梦数据库字段
      const vm = this
      this.fieldType = type
      switch (type) {
        case 'dmdbTable':
          vm.$refs.createDMdbTable.showDialog()
          break
        case 'dimension':
          vm.$refs.selectDMdbFields.showDialog(vm.source.dmdb.data)
          break
        case 'numericalValue':
          vm.$refs.selectDMdbFields.showDialog(vm.source.dmdb.data)
          break
      }
    },
    addExcelField (type) { // 添加excel字段
      const vm = this
      this.fieldType = type
      switch (type) {
        case 'excelTable':
          vm.$refs.createExcelTable.showDialog('excel')
          break
        case 'dimension':
          vm.$refs.selectExcelFields.showDialog(vm.source.excel.data, 'excel')
          break
        case 'numericalValue':
          vm.$refs.selectExcelFields.showDialog(vm.source.excel.data, 'excel')
          break
      }
    },
    addMongoDBField (type) { // 添加mongodb字段
      const vm = this
      this.fieldType = type
      switch (type) {
        case 'mongodbTable':
          vm.$refs.createMongoDBTable.showDialog()
          break
      }
    },
    addPostgresqlField (type) { // 添加postgresql字段
      const vm = this
      this.fieldType = type
      switch (type) {
        case 'postgresqlTable':
          vm.$refs.createPostgresqlTable.showDialog()
          break
        case 'dimension':
          vm.$refs.selectPostgresqlFields.showDialog(vm.source.postgresql.data)
          break
        case 'numericalValue':
          vm.$refs.selectPostgresqlFields.showDialog(vm.source.postgresql.data)
          break
      }
    },
    addOracleField (type) { // 添加oracle字段
      const vm = this
      this.fieldType = type
      switch (type) {
        case 'oracleTable':
          vm.$refs.createOracleTable.showDialog()
          break
        case 'dimension':
          vm.$refs.selectOracleFields.showDialog(vm.source.oracle.data)
          break
        case 'numericalValue':
          vm.$refs.selectOracleFields.showDialog(vm.source.oracle.data)
          break
      }
    },
    initFieldMapping () {
      this.currentCom.dataConfig.fieldMapping.map(item => {
        item.target = ''
        return item
      })
    },
    getDashList (data) { // 获取dashboard数据
      this.source.dashboard.data.sourceId = data.id
      this.source.dashboard.data.name = data.config.name
      this.commitUpdate('dashboard')
      // this.calculatefieldList()
    },
    getDmcTable (data) { // 获取dmc表数据
      this.source.dmc.data.tbId = data.tb_id
      this.source.dmc.data.tbName = data.name
      this.source.dmc.data.fields = {
        numericalValue: [],
        dimension: []
      }
      this.calculatefieldList()
      this.commitUpdate('dmc')
    },
    getMysqlTable (data) { // 获取mysql表数据
      this.source.mysql.data.tbName = data
      this.source.mysql.data.fields = {
        numericalValue: [],
        dimension: []
      }
      // this.calculatefieldList()
      this.commitUpdate('mysql')
    },
    getDMdbTable (data) { // 获取达梦数据库表数据
      this.source.dmdb.data.tbName = data
      this.source.dmdb.data.fields = {
        numericalValue: [],
        dimension: []
      }
      // this.calculatefieldList()
      this.commitUpdate('dmdb')
    },
    getExcelTable (data) { // 获取excel表数据
      this.source.excel.data.tbName = data
      this.source.excel.data.fields = {
        numericalValue: [],
        dimension: []
      }
      // this.calculatefieldList()
      this.commitUpdate('excel')
    },
    getMongoDBTable (data) { // 获取mongodb表数据
      this.source.mongodb.data.tbName = data
      this.source.mongodb.data.fields = {
        numericalValue: [],
        dimension: []
      }
      this.commitUpdate('mongodb')
    },
    getPostgresqlTable (data) { // 获取postgresql表数据
      this.source.postgresql.data.tbName = data
      this.source.postgresql.data.fields = {
        numericalValue: [],
        dimension: []
      }
      this.commitUpdate('postgresql')
    },
    getOracleTable (data) { // 获取oracle表数据
      this.source.oracle.data.tbName = data
      this.source.oracle.data.fields = {
        numericalValue: [],
        dimension: []
      }
      this.commitUpdate('oracle')
    },
    getHighgodbTable (data) { // 获取gbase8s表数据
      this.source.highgodb.data.tbName = data
      this.source.highgodb.data.fields = {
        numericalValue: [],
        dimension: []
      }
      this.commitUpdate('highgodb')
    },
    getSelectAllField (data, type) { // 获取dmc表字段
      if (this.fieldType === 'numericalValue') {
        const numericalValue = data.map(item => {
          if (item.data_type === 'date') {
            return {
              calculation: '',
              ...item
            }
          } else {
            return {
              calculation: 'count',
              ...item
            }
          }
        })
        this.source.dmc.data.fields.numericalValue = [...this.source.dmc.data.fields.numericalValue, ...numericalValue]
      } else {
        this.source.dmc.data.fields.dimension = [...data]
      }
      this.commitUpdate('dmc')
    },
    getSelectMysqlFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.mysql.data.fields.numericalValue = [...this.source.mysql.data.fields.numericalValue, ...data]
      } else {
        this.source.mysql.data.fields.dimension = [...this.source.mysql.data.fields.dimension, ...data]
      }
      this.commitUpdate('mysql')
    },
    getSelectHighgodbFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.highgodb.data.fields.numericalValue = [...data]
        this.source.highgodb.data.fields.numericalValue.forEach(item => {
          if (item.type === 'date' || item.type === 'string') {
            item.calculation = 'count_distinct'
          } else {
            item.calculation = 'sum'
          }
          if (!item.formatter) {
            item.formatter = {
              num: {
                digit: 2,
                unit: 1
              }
            }
          }
          if (!item.YoY || !item.yoyQoqType) {
            item.YoY = ''
            item.yoyQoqType = ''
          }
        })
      } else {
        this.source.highgodb.data.fields.dimension = [...data]
      }
      this.commitUpdate('highgodb')
    },
    getSelectDMdbFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.dmdb.data.fields.numericalValue = [...this.source.dmdb.data.fields.numericalValue, ...data]
      } else {
        this.source.dmdb.data.fields.dimension = [...this.source.dmdb.data.fields.dimension, ...data]
      }
      this.commitUpdate('dmdb')
    },
    getSelectExcelFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.excel.data.fields.numericalValue = [...this.source.excel.data.fields.numericalValue, ...data]
      } else {
        this.source.excel.data.fields.dimension = [...this.source.excel.data.fields.dimension, ...data]
      }
      this.commitUpdate('excel')
    },
    getSelectPostgresqlFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.postgresql.data.fields.numericalValue = [...this.source.postgresql.data.fields.numericalValue, ...data]
      } else {
        this.source.postgresql.data.fields.dimension = [...this.source.postgresql.data.fields.dimension, ...data]
      }
      this.commitUpdate('postgresql')
    },
    getSelectOracleFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.oracle.data.fields.numericalValue = [...this.source.oracle.data.fields.numericalValue, ...data]
      } else {
        this.source.oracle.data.fields.dimension = [...this.source.oracle.data.fields.dimension, ...data]
      }
      this.commitUpdate('oracle')
    },
    getCaField () { // 获取计算字段
      this.calculatefieldList()
    },
    async updateDatastorage (data) { // 数据源创建成功 刷新数据源列表
      const sourceType = data.type
      const sourceId = data.id + ''
      if (sourceType === this.sourceType) {
        await this.getDatasourceList(this.sourceType)
        this.source[sourceType].data.sourceId = sourceId
        this.sourceChange(sourceType, sourceId)
      }
    },
    sourceChange (type, val) { // 选择数据源 带入baseUrl
      if (type === 'api') {
        const source = this.sourceList.find(item => item.id === val)
        if (source && source.config) {
          this.source.api.data.baseUrl = source.config.baseUrl
        } else {
          this.source.api.data.baseUrl = ''
        }
      }
      this.commitUpdate(type)
    },
    staticDataChange: function (json, compId) { // 静态数据change
      try {
        this.$store.dispatch('editor/updateScreenCom', {
          id: compId,
          keyValPairs: [
            { key: 'staticData', value: json }
          ]
        }).then(() => {
          this.getData(false)
        })
      } catch (e) {
        this.$message.warn('内容错误')
      }
    },
    sqlEditorFocus () {
      const source = this.source[this.sourceType].data;
      if (source.advanced && source.tbName && (this.suggestion.tbName !== source.tbName)) {
        const params = {
          type: this.sourceType,
          typeName: source.tbName
        }
        this.suggestion.tbName = source.tbName;
        this.getFieldList(params)
      }
    },
    async getFieldList (params) { // 获取mysql, oracle, postgresql工作表字段
      const data = {
        type: params.type,
        tbName: params.typeName,
        componentId: this.currentConfigId,
        workspaceId: this.screenInfo.workspaceId
      }
      const res = await getFieldList(data);
      if (res && res.success) {
        const data = res.data;
        const result = [];
        data.forEach(item => {
          item.fields.forEach(field => {
            result.push({
              className: item.type,
              text: field.name
            })
          })
        })
        this.suggestion.list = result;
      }
    },
    comUpdate (type) { // 提交更新
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: []
      }).then(() => {
        // this.getData()
      })
    },
    async confirmConfig (sql) {
      if (['mysql', 'oracle', 'postgresql'].includes(this.sourceType)) { // 先校验sql再保存
        const result = await postData({}, [{
          componentId: this.currentConfigId,
          workspaceId: this.screenInfo.workspaceId,
          params: { targetSql: sql },
          sourceType: this.sourceType
        }])
        if (!result[0].success) {
          this.$message.error(result[0].message)
          return
        }
      }
      await this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCompId,
        keyValPairs: [
          { key: `dataConfig.dataResponse.source.${this.sourceType}`, value: _.cloneDeep(this.source[this.sourceType]) }
        ]
      })
      this.getData()
    },
    commitUpdate: _.debounce(function (type) { // 提交更新
      return this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCompId,
        keyValPairs: [
          { key: `dataConfig.dataResponse.source.${type}`, value: _.cloneDeep(this.source[type]) }
        ]
      }).then(() => {
        if (!['dmc', 'mysql', 'postgresql', 'dmdb', 'oracle'].includes(type)) {
          this.getData()
        }
        this.checkSource(type)
      })
    }, 300),
    checkSource (type) { // 判断是否使用相同数据源达到5个
      const screenComs = Object.values(this.screenComs).filter(it => it.comType !== 'interaction-container-datacontainer')
      const sourceId = this.source[type].data[type === 'dmc' ? 'tbId' : 'sourceId']
      const coms = screenComs.filter(item => {
        return item.dataConfig.dataResponse.sourceType === type &&
                item.dataConfig.dataResponse.source[type].data[type === 'dmc' ? 'tbId' : 'sourceId'] === sourceId &&
                !['datacontainer'].includes(type)
      })
      const comsLen = coms.length
      if (comsLen > 2) {
        emitter.emit('hints', {
          type: 'source',
          data: coms
        })
      }
    },
    updateDataSelect ({ path, value }) {
      if (!path) return false
      _.set(this.source[this.sourceType].data, path, value)
      this.getData()
    },
    async getData (updateStatic = true) { // 获取static/csv/api/mysql后台数据
      if (this.sourceType === 'websocket') return true;
      const componentId = (this.sourceType === 'dialog' || this.sourceType === 'inherit')
        ? this.screenInfo?.relationCompId : this.currentConfigId
      const _var = await dataUtil.getApiParams(this.currentCom.dataConfig.dataResponse, this.callbackManager());
      try {
        this.loading = true
        const result = await postData({}, [{
          componentId,
          workspaceId: this.screenInfo.workspaceId,
          params: { _var },
          sourceType: this.sourceType
        }])
        this.loading = false
        const res = result[0]
        if (res && res.success) {
          const data = res.data || []
          // 数据量过大提醒弹窗
          if (['csv_file', 'api', 'mysql', 'dmc', 'dmdb', 'postgresql', 'oracle', 'excel', 'highgodb'].includes(this.sourceType)) {
            const isLimit = this.source[this.sourceType].data.isLimit
            if (!isLimit && data.length > 100) {
              this.$refs.limit.showDialog()
              return
            }
          }
          await this.$store.dispatch('editor/updateDataMappingErrorCompIds', { componentId, data: res.data })
          this.$store.commit('editor/updateComData', { componentId, data: res.data })
          if (this.sourceType === 'dialog') {
            this.sourceData = JSON.stringify(res.data || [])
          } else if (this.sourceType === 'inherit') {
            if (this.indicatorData) {
              this.sourceData = JSON.stringify(this.indicatorData)
            } else {
              this.sourceData = JSON.stringify(this.inheritData)
            }
          } else if (this.sourceType === 'datacontainer') {
            // TODO 待修改
            const comId = this.source.datacontainer.data.dataContainerComId;
            if (comId) {
              const cdata = this.containerData[comId]?.data || [];
              this.sourceData = JSON.stringify(cdata);
              this.autoMapping()
            }
          } else {
            this.sourceData = JSON.stringify(this.comsData[this.currentConfigId] || [])
            this.autoMapping()
          }
          if (this.sourceType === 'static') {
            if (!updateStatic) return
            this.$nextTick(() => {
              this.jsonData = JSON.parse(this.sourceData)
            })
          }
        } else {
          this.sourceData = JSON.stringify([]);
          this.$store.commit('editor/updateComData', { componentId, data: [] })
        }
      } catch (e) {}
    },
    updateLimitData ({ isLimit, limitNum }) { // 更新限制组件数据量
      const source = _.cloneDeep(this.source[this.sourceType])
      source.data.isLimit = isLimit
      source.data.limitNum = limitNum
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: `dataConfig.dataResponse.source.${this.sourceType}`, value: source }
        ]
      }).then(() => {
        this.source = _.cloneDeep(this.currentCom.dataConfig.dataResponse.source)
        this.getData()
      })
    },
    autoMapping () { // 自动映射
      let comData = [];
      if (this.sourceType === 'datacontainer') {
        const comId = this.source.datacontainer.data.dataContainerComId;
        comData = this.containerData[comId]?.data || [];
      } else {
        comData = this.comsData[this.currentConfigId];
      }
      if (!_.isEmpty(comData) && this.fieldMapping.length) {
        // 过滤器过滤后的数据再去自动映射
        let filterData = dataUtil.filterData(comData, this.validFilters);
        if (this.drillDown.length) {
          this.drillDown.forEach(e => {
            if (e.linkType === 'links') {
              filterData = transDrillData(filterData, e)
              filterData = filterData.map(item => _.omit(item, ['_id', '_parentId'])); // 去掉_id, _parentId
            }
          })
        }
        const origin = _.cloneDeep(this.fieldMapping);
        const fieldMapping = dataUtil.fieldAutoMapping(this.fieldMapping, filterData);
        if (!_.isEqual(origin, fieldMapping)) {
          this.saveFieldMapping()
        }
      }
    },
    filterConfig () { // 打开过滤器面板
      this.$refs.filter.showFilter()
    },
    openDataSelect () {
      this.$refs.dataSelect.open(this.source[this.sourceType].data)
    },
    getResult () { // 查看响应结果
      this.filterConfig()
    },
    handleTipsEdit (targetName, action) {
      const { conditions } = this.tips
      if (action === 'add') {
        conditions.push({
          type: 'empty',
          field: '',
          condition: '==',
          value: '0',
          info: '暂无数据',
          background: {
            show: true,
            type: 'pure',
            pure: '#fff',
            gradient: {
              type: 'linear-gradient',
              deg: 0,
              start: '#0000ff',
              startScale: 0,
              end: '#ffc0cb',
              endScale: 100,
              shape: 'ellipse'
            },
            image: {
              url: '',
              size: '100% 100%',
              positionX: 'center',
              positionY: 'center',
              repeat: 'no-repeat'
            }
          },
          font: {
            fontSize: 14,
            color: '#999',
            fontFamily: '系统自带字体',
            fontWeight: 'bold'
          }
        })
        this.activeTab = (conditions.length - 1) + '';
      }
      if (action === 'remove') {
        if (conditions.length <= 1) {
          this.$message.warn('请至少保留一项');
          return
        }
        if (conditions[+targetName]) {
          conditions.splice(+targetName, 1);
          if (+targetName > 0) {
            this.activeTab = (+targetName - 1) + '';
          } else {
            this.activeTab = '0'
          }
        }
      }
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'dataConfig.dataResponse.tips', value: _.cloneDeep(this.tips) }
        ]
      })
    },
    handleConfigTreeChange (path, value, index) {
      _.set(this.tips.conditions[index], path, value);
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCompId,
        keyValPairs: [
          { key: 'dataConfig.dataResponse.tips', value: _.cloneDeep(this.tips) }
        ]
      })
    },
    saveTipsShow (val) {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'dataConfig.dataResponse.tips.open', value: val }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.comp-data {
  padding: 15px 8px;
  .slot-title {
    display: flex;
    justify-content: space-between;
    align-items: top;
  }
  .content-block {
    padding: 6px 0;
    overflow: hidden;
    &.db-block {
      border: 1px solid rgba(204, 219, 255, 0.16);
      border-radius: 8px;
      padding: 8px;
      margin: 8px 0 16px;
    }
    .config-control {
      padding: 4px 0;
      margin-bottom: 10px;
      .config-title {
        &.width-auto {
          width: auto;
        }
        width: 80px;
      }
    }
    .eidtor-wrap {
      margin-bottom: 10px;
    }
    .flex-row {
      display: flex;
      justify-content: space-between;
      .ml10 {
        margin-left: 10px;
      }
      .table-name {
        flex: none;
        width: 110px;
        line-height: 28px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .field-box {
      width: 100%;
      min-height: 85px;
      background: #0c0b0b;
      padding-bottom: 8px;
      border-radius: 8px;
      .field-div {
        &.add-field {
          & i {
            color:#2681ff;
          }
          color:#2681ff;
          border-color:#2681ff;
        }
        display: inline-block;
        cursor: pointer;
        margin-left: 8px;
        margin-top: 8px;
        padding: 0px 8px;
        background: #191D25;
        line-height: 22px;
        border-radius: 16px;
        border: 1px solid rgba(76, 82, 95, 0.32);
        .el-input__icon {
          width: 13px;
          height: 22px;
          line-height: 22px;
          cursor: pointer;
        }
        ::v-deep {
          .el-input__inner {
            height: 22px;
            border: none;
            padding-right: 20px;
            background: none;
          }
          .el-select {
            max-width: 105px;
          }
        }
      }
    }
    .row-btn {
      padding-left: 45px;
      &.pl0 {
        padding-left: 0;
      }
    }
  }
  ::v-deep {
    .mapping-t {
      line-height: 1;
      .t-icon {
        font-size: 16px;
        &.green {
          color: #00a755;
        }
        &.red {
          color: #ef5350;
        }
      }
    }
    .el-input-number--mini {
      width: 90px;
    }
    .CodeMirror {
      height: 238px;
    }
    .el-step {
      .el-step__description {
        padding-right: 0;
        margin-top: 0;
        overflow: hidden;
      }
      &.is-vertical .el-step__main {
        padding-left: 8px;
      }
      .el-step__icon {
        width: 18px;
        height: 18px;
        font-size: 12px;
        background-color: #2681ff;
      }
      .el-step__icon.is-text {
        border: none;
      }
      .el-step__icon-inner {
        font-weight: 400;
      }
      &.is-vertical .el-step__line {
        width: 2px;
        top: 0;
        bottom: 0;
        left: 8px;
        background-color: #2e343c;
      }
      &.is-vertical .el-step__title {
        font-size: 14px;
        line-height: 18px;
      }
      &.is-vertical .el-step__head {
        width: 18px;
      }
      &.error .el-step__icon {
        background: #F56C6C;
      }
    }
  }
  .result-btn {
    text-align: center;
    margin-top: 20px;
    padding-bottom: 30px;
  }
  .w45_px {
    width: 45px !important;
  }
  .w100 {
    width: 100%;
  }
  .pb10 {
    padding-bottom: 10px;
  }
  .tips {
    padding-left: 8px;
    font-size: 12px;
    color: #999;
    margin-bottom: 10px;
  }
  .tab-b {
    width: 287px;
    ::v-deep {
      .el-tabs__nav-next,
      .el-tabs__nav-prev {
        line-height: 30px;
      }
      .el-tabs__item {
        height: 30px;
        line-height: 30px;
        font-size: 12px;
      }
      .el-tabs__new-tab {
        line-height: 16px;
        margin: 5px 0 8px 10px;
      }
      .el-tabs__header {
        min-height: 30px;
        border-bottom: 1px solid #393b4a;
      }
    }
  }
}
</style>
