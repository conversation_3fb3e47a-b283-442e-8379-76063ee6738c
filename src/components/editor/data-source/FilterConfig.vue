<template>
  <div class="filter-config" :class="{'show': show}">
    <div class="f-title">
      <span>数据响应结果</span>
      <i class="el-icon-close" @click="closeFilter"></i>
    </div>
    <template v-if="show">
      <div class="f-content">
        <el-form size="mini" label-width="100px" label-position="left">
          <el-form-item label="选择过滤器：">
            <div class="flex-row">
              <el-cascader
                class="flex-1"
                placeholder="请选择过滤器"
                :options="filterOptions"
                :props="{value: 'id', label: 'name'}"
                filterable
                :disabled="!!newList.length"
                v-model="selectedFilter"
                @change="filterSelect"
                popper-class="filter-cascader">
                <template slot-scope="{ data }">
                  <span class="filter-txt">
                    <el-tooltip effect="dark" :content="data.name" placement="left" v-if="data.isSys">
                      <span>【内置】{{ data.name }}</span>
                    </el-tooltip>
                    <span v-else>{{ data.name }}</span>
                  </span>
                  <span v-if="data.description">
                    <el-tooltip effect="dark" :content="data.description" placement="left" v-if="data.filterType ==='system' && data.description">
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  </span>
                </template>
              </el-cascader>
              <el-button type="plain" icon="el-icon-plus" plain @click="createFilter" :disabled="!!newList.length">新建过滤器</el-button>
            </div>
          </el-form-item>
          <el-form-item label-width="0px">
            <div class="filter-wrap" v-if="filterList.length || newList.length">
              <draggable v-model="filterList" group="people" handle=".move" :animation="400" @change="sortChange">
                <FilterItem
                  v-for="(item, index) in filterList"
                  :key="item.key"
                  :item="item"
                  :index="index"
                  :filterList="filterList"
                  :sourceData="sourceData"
                  @enableChange="enableChange"
                  @deleteFilter="deleteFilter"
                  @filterTest="filterTest"
                  @refresh="refreshFilter" />
              </draggable>
              <FilterItem
                v-for="item in newList"
                :key="item.key"
                :item="item"
                :index="filterList.length"
                :filterList="filterList"
                :sourceData="sourceData"
                @deleteFilter="deleteNewFilter"
                @filterTest="filterTest"
                @refresh="refreshFilter" />
            </div>
          </el-form-item>
          <el-form label-position="top" v-if="!!fieldMapping.length">
            <el-form-item class="table-wrap">
              <span slot="label">数据响应结果应为列表，列表元素包含如下字段</span>
              <el-table class="mapping-t" :data="fieldMapping" border size="mini">
                <el-table-column label="字段" prop="source" width="100"></el-table-column>
                <el-table-column label="映射" prop="target" width="100">
                  <span>--</span>
                </el-table-column>
                <el-table-column label="说明" prop="description" show-overflow-tooltip></el-table-column>
              </el-table>
            </el-form-item>
          </el-form>
          <el-form-item label="数据响应结果"></el-form-item>
          <el-form-item label-width="0px">
            <CodeEditor ref="editor" :options="cmOptions" />
            <span v-if="resultData.length > 100" class="editor-tip"><font color="red">*</font> 为保证加载性能，仅展示前100条数据</span>
          </el-form-item>
        </el-form>
      </div>
      <!-- 过滤结果测试 -->
      <FilterPreview ref="preview" :sourceData="sourceData" />
    </template>
    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import CodeEditor from './CodeEditor';
import draggable from 'vuedraggable';
import FilterItem from './FilterItem';
import FilterPreview from './FilterPreview';
import { mapState, mapGetters } from 'vuex';
import { createFilter, updateFilter, deleteFilter, orderFilter } from '@/api/filter';
import dataUtil from '@/utils/data';
import { filterType } from '@/common/constants'
import { v4 as uuidv4 } from 'uuid';
export default {
  name: 'FilterConfig',
  props: {
    sourceData: String
  },
  components: {
    CodeEditor,
    draggable,
    FilterItem,
    FilterPreview
  },
  data () {
    return {
      show: false,
      result: '',
      cmOptions: {
        readOnly: true
      },
      selectedFilter: [], // 选中的过滤器
      filterList: [],
      newList: [],
      resultData: [],
      loading: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      comsData: state => state.editor.comsData,
      screenFilters: state => state.editor.screenFilters,
      systemFilters: state => state.editor.systemFilters
    }),
    ...mapGetters('editor', ['currentCom']),
    allFilters () {
      const options = Object.values(this.screenFilters);
      const sysFilters = Object.values(this.systemFilters).map(item => {
        if (item.type !== '4') {
          return {
            ...item,
            filterType: 'system',
            isSys: true
          }
        }
        return {
          ...item,
          filterType: 'template'
        }
      })
      const list = [...sysFilters, ...options].filter(item => {
        return this.filterList.findIndex(filter => filter.id === item.id) === -1;
      })
      return list
    },
    filterOptions () { // 全部过滤器 过滤掉已选择的
      const list2 = filterType.map(item => {
        const type = item.value;
        const filters = this.allFilters.filter(item2 => item2.type === type);
        return {
          name: item.label,
          id: item.value,
          children: filters
        }
      })
      list2.push({
        name: '已创建的过滤器',
        id: '1000',
        children: this.allFilters.filter(item => {
          return item.type === 'custom' || item.type === 'system' || item.type === 'template'
        })
      })
      return list2
    },
    filtersEnable: { // 是否启用过滤器
      get: function () {
        return this.currentCom.dataConfig.dataResponse.filters.enable
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'dataConfig.dataResponse.filters.enable', value: val }
          ]
        }).then(() => {
          this.$store.dispatch('editor/updateDataMappingErrorCompIds', {
            componentId: this.currentCom.id,
            data: this.comsData[this.currentCom.id]
          })
        });
      }
    },
    fieldMapping () { // 字段映射
      return _.cloneDeep(this.currentCom.dataConfig.fieldMapping)
    },
    comFilters () { // 组件过滤器 filters.list
      return this.currentCom.dataConfig.dataResponse.filters.list
    }
  },
  watch: {
    show (val) {
      if (!val) {
        this.$refs.preview && this.$refs.preview.closeTest();
      }
    },
    filtersEnable: {
      handler: function (val) {
        this.getResult(this.filterList);
      },
      immediate: true
    },
    filterList: {
      handler: function (val) {
        this.getResult(val);
      },
      deep: true
    },
    sourceData: {
      handler: function (val) {
        this.getResult(this.filterList);
      },
      immediate: true
    },
    comFilters: {
      handler: function (val) {
        this.getSavedFilters();
      },
      deep: true
    }
  },
  directives: {
    select: {
      inserted: function (el) {
        el.querySelector('input').select()
      }
    }
  },
  methods: {
    showFilter () {
      this.show = true;
      this.getSavedFilters();
    },
    closeFilter () {
      this.show = false;
    },
    createFilter () {
      const filter = {
        key: Math.random(),
        callbackKeys: [],
        name: '新建过滤器',
        content: 'return data;',
        type: 'custom',
        enable: true,
        editable: true,
        show: true
      };
      this.newList.push(filter);
    },
    async deleteFilter (item, index) { // 删除过滤器
      this.$confirm('确认删除该过滤器？', { type: 'warning', title: '提示' }).then(async () => {
        if (!item.id) {
          this.filterList.splice(index, 1);
        } else {
          this.loading = true;
          const res = await deleteFilter({}, { componentId: this.currentCom.id, id: item.id });
          if (res && res.success) {
            this.$store.dispatch('editor/getComponentInfo', this.currentCom.id);
            this.loading = false;
            this.$message.success('删除成功');
          }
        }
      }).catch(() => {})
    },
    deleteNewFilter () { // 删除未保存的过滤器
      this.newList = [];
    },
    filterTest (item, index) { // 过滤器测试
      let prevFilters = this.filterList.slice(0, index);
      prevFilters = prevFilters.filter(filter => filter.enable);
      this.$refs.preview.getResult([...prevFilters, item]);
    },
    async enableChange (val, item) { // 是否启用 change
      const data = {
        id: item.id,
        componentId: this.currentCom.id,
        screenId: this.currentCom.screenId,
        callbackKeys: item.callbackKeys,
        name: item.name,
        content: item.content,
        enable: item.enable
      }
      this.loading = true;
      try {
        const res = await updateFilter(data, {});
        if (res && res.success) {
          this.$store.dispatch('editor/getComponentInfo', this.currentCom.id);
          this.loading = false;
          this.$message.success(val ? '已开启' : '已关闭');
        }
      } catch (e) {
        this.loading = false;
      }
    },
    getResult: _.debounce(function (filterList) {
      try {
        const sourceData = JSON.parse(this.sourceData);
        if (this.filtersEnable) {
          const list = filterList.filter(item => item.enable);
          /* eslint-disable-next-line */
          this.resultData = dataUtil.filterData(sourceData, list);
        } else {
          this.resultData = sourceData;
        }
        this.$refs.editor.setValue(JSON.stringify(this.resultData.slice(0, 100)));
      } catch (e) {}
    }, 300),
    getSavedFilters () { // 获取组件的过滤器
      const _list = this.comFilters;
      if (_list && _list.length) {
        const filterList = _list.map(item => {
          return {
            ...this.screenFilters[item.id],
            enable: item.enable,
            show: false,
            key: Math.random()
          }
        })
        this.filterList = _.cloneDeep(filterList);
      } else {
        this.filterList = [];
      }
    },
    async filterSelect (val) { // 从已有过滤器选中一个
      if (val.length < 2) {
        return
      }
      const id = val[1];
      const filter = this.allFilters.find(item => item.id === id);
      if (filter) {
        if (filter.filterType === 'system' || filter.filterType === 'template') { // 添加内置过滤器
          const sysFilter = {
            key: Math.random(),
            callbackKeys: [],
            name: filter.name + '_' + uuidv4().slice(0, 6),
            content: filter.content,
            type: filter.filterType,
            systemParams: (() => {
              const data = filter.params.map(item => {
                if (item.source === 'form') {
                  return {
                    ...item,
                    value: ['']
                  }
                }
                return {
                  ...item,
                  value: []
                }
              })
              return _.cloneDeep(data)
            })(),
            enable: true,
            editable: false,
            show: true
          };
          if (filter.params && filter.params.length) {
            sysFilter.type = 'system'
          }
          this.newList.push(sysFilter);
          this.selectedFilter = [];
          return
        }
        const res = await createFilter(
          {
            ...filter,
            componentId: this.currentCom.id,
            screenId: this.currentCom.screenId,
            enable: true
          },
          { screenId: this.currentCom.screenId });
        if (res && res.success) {
          this.filterList.push(_.cloneDeep({
            ...filter,
            enable: true,
            editable: false,
            show: false,
            key: Math.random()
          }));
          this.$message.success('添加成功');
        }
      }
      this.selectedFilter = [];
      this.$store.dispatch('editor/getComponentInfo', this.currentCom.id);
    },
    async sortChange (e) { // 排序
      const { element, newIndex } = e.moved;
      const data = {
        componentId: this.currentCom.id,
        targetIndex: newIndex,
        id: element.id,
        name: element.name,
        enable: element.enable
      }
      const res = await orderFilter(data);
      if (res && res.success) {
        this.$store.dispatch('editor/getComponentInfo', this.currentCom.id);
      }
    },
    refreshFilter (updateAll) {
      this.$store.dispatch('editor/getComponentInfo', this.currentCom.id);
      if (updateAll) this.$store.dispatch('editor/getSystemFilters');
      this.loading = false;
      this.newList = [];
      this.getSavedFilters();
    }
  }
}
</script>

<style lang="scss" scoped>
.divider {
  height: 1px;
  background-color: #DCDFE6;
  margin: 10px 0;
}
.filter-config {
  position: fixed;
  top: 0;
  bottom: 0;
  right: -520px;
  width: 520px;
  z-index: 999;
  padding-top: 130px;
  background: #22242b;
  transition: right .5s ease-in-out;
  overflow: auto;
  &.show {
    right: 0;
  }
  .f-title {
    position: absolute;
    top: 70px;
    width: 520px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #fafafa;
    background: #22242b;
    padding: 20px 16px;
    z-index: 999;
  }
  .f-content {
    padding: 5px 16px 16px;
  }
  .flex-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .flex-1 {
      flex: 1;
      margin-right: 10px;
    }
  }
  .filter-wrap {
    background: rgb(24, 27, 36);
    padding: 18px 16px 6px;
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
      color: #d2d2d2;
    }
  }
  .table-wrap {
    ::v-deep {
      .el-form-item__label {
        padding-bottom: 0;
      }
      .mapping-t {
        color: #d2d2d2;
        border: 1px solid var(--control-border-color);
        border-right: none;
        border-bottom: none;
        th {
          padding: 0px;
          background-color: #22242b;
        }
        & td, & th.is-leaf {
          border-right: 1px solid var(--control-border-color);
          border-bottom: 1px solid var(--control-border-color);
        }
        &::after {
          background-color: var(--control-border-color);
        }
      }
    }
  }
}
.editor-tip {
  font-size: 12px;
  color: #a3a3a3;
}
</style>
