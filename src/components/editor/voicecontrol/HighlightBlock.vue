<template>
  <div class="hightlight-block-wrapper">
    <div class="highlight-block" v-if="false"></div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'HighlightBlock',
  data () {
    return {}
  },
  computed: {
    ...mapState({
      linkComps: state => state.view.linkComps,
      screenData: state => state.view.screenData
    }),
    showHightlight () {
      const comps = this.linkComps.comps;
      return !!comps.length
    },
    blockStyle () {
      const comps = this.linkComps.comps;
      if (!comps.length) return {};

      const components = this.screenData.components;
      const coms = Object.values(components).filter(item => comps.includes(item.id));

      const top = []; const right = []; const bottom = []; const left = [];
      coms.forEach(item => {
        const { x, y, w, h } = item.attr;
        top.push(y);
        right.push(x + w);
        bottom.push(y + h);
        left.push(x)
      })
      const minX = Math.min(...left);
      const minY = Math.min(...top);
      const maxX = Math.max(...right);
      const maxY = Math.max(...bottom);

      const _width = maxX - minX;
      const _height = maxY - minY;

      return {
        left: minX + 'px',
        top: minY + 'px',
        width: _width + 'px',
        height: _height + 'px'
      }
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.highlight-block {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10000;
  background-color: rgba(22, 28, 40, 0.82);
}
</style>
