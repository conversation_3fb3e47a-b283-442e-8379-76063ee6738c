<template>
  <div class="voice-control">
    <div class="voice-button" @click="showPanel" v-if="!show">
      <div class="loading-mask" v-if="!modelLoaded">
        <div class="spinner"></div>
      </div>
      <div class="loading-info" v-if="!modelLoaded">
        <div class="word">正在初始化语音模型...</div>
      </div>
      <div class="tips-info" v-if="!voiceConfig.voiceWeak">
        <div class="word">{{ voiceConfig.weakWord }}</div>
      </div>
    </div>
    <div class="voice-control-panel">
      <div class="voice-panel" :class="{ show: show }" v-if="mode === 0">
        <span class="close-panel el-icon-close" @click="closePanel"></span>
        <span class="switch-mode" @click="switchMode">切换问答模式</span>
        <div class="text-input">
          <p class="tips" v-if="state.status === 0">{{ voiceConfig.voiceWeak ? voiceConfig.weakWord || '休息中，通过 "你好伏羲" 呼出我哦' : '休息中，请点击麦克风再次唤醒' }}</p>
          <p class="tips" v-if="state.status === 1">声音收集中</p>
          <p class="tips" v-if="state.status === 2">未检测到您的声音，请继续呼唤我</p>
          <p class="tips" v-if="state.status === 3">对不起，我没能理解您的意思</p>
          <p class="tips" v-if="state.status === 4">指令识别成功，开始执行</p>
          <p class="tips" v-if="state.status === 5">指令识别中</p>
          <p class="tips" v-if="state.status === 6">{{ voiceTxt }}</p>
        </div>
        <div class="voice-status"></div>
        <div class="voice-func">
          <div class="voice-bo"></div>
          <div class="voice-btn" :class="{ recording: isRecording }"  @click="startRecorder">
            <div class="voice-percent" :style="{ height: voicePercent + '%' }"></div>
          </div>
          <div class="voice-bo"></div>
        </div>
      </div>
      <div class="text-panel" :class="{ show: show }" v-if="mode === 1">
        <div class="text-panel-wrapper">
          <div class="text-c-h"></div>
          <span class="close-icon el-icon-close" @click="closePanel"></span>
          <span class="switch-mode" @click="switchMode">切换语音模式</span>
          <div class="text-talk-wrap">
            <div class="chat-container" ref="containter">
              <div class="chat-message-row" :class="{ 'flex-end': item.type == 0, 'flex-start': item.type == 1 }" v-for="(item, index) in wordsList" :key="index">
                <div class="chat-avatar" v-if="item.type === 1">
                  <img :src="avatar">
                </div>
                <div class="chat-message" :class="{ 'chat-reply': item.type == 1 }">
                  <p>{{ item.words }}</p>
                </div>
                <div class="chat-avatar" v-if="item.type === 0">
                  <img :src="userAvatar">
                </div>
              </div>
            </div>
          </div>
          <div class="text-bottom">
            <el-input class="ipt" placeholder="请输入" size="mini" v-model="words" @keyup.native.enter="submit"></el-input>
            <el-button class="btn" type="primary" size="mini" @click="submit" :loading="loading">确认</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { voicecontrol } from '@/api/screen'
import Recorder from 'js-audio-recorder'
import { mapActions } from 'vuex'
import { MIN_RECORD_VOL } from '@/common/constants'
import * as tf from '@tensorflow/tfjs'
import * as SpeechCommands from '@tensorflow-models/speech-commands'

export default {
  name: 'voicecontrol',
  props: {
    screenId: {
      type: [Number, String],
      default: ''
    },
    voiceConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      show: false,
      modelLoaded: true, // 语音模型加载完成状态
      mode: 0, // 模式：1语音 2文本
      words: '',
      wordsList: [],
      avatar: require('@/assets/img/ai.png'),
      userAvatar: require('@/assets/img/user.png'),
      loading: false,
      recorder: null,
      isRecording: false,
      voicePercent: 0,
      voiceTxt: '',
      state: {
        status: 0
      },
      soundMap: new Map(),
      rustpotterService: null,
      testing: false,
      timer: null,
      recognizer: null
    }
  },
  async mounted () {
    if (this.voiceConfig.voiceWeak) {
      this.initTensorflow();
    }
    this.initRecorder();
  },
  beforeDestroy () {
    this.recognizer && this.recognizer.stopListening()
  },
  methods: {
    ...mapActions('event', [
      'pushFnQueue'
    ]),
    async initTensorflow () {
      window.console.log('=======>', '正在初始化语音模型...')
      this.modelLoaded = false
      const model = window.location.origin + '/model/model.json';
      const layerModel = await tf.loadLayersModel(model);
      layerModel.summary();
      const metadata = window.location.origin + '/model/metadata.json';
      const suppressionTimeMillis = 1000;
      this.recognizer = SpeechCommands.create('BROWSER_FFT', null, model, metadata);
      await this.recognizer.ensureModelLoaded();
      window.console.log('=======>', '语音模型初始化完成！！！')
      this.modelLoaded = true
      this.recognizer.listen(
        (res) => {
          const words = this.recognizer.wordLabels();
          let result = [];
          words.forEach((word, index) => {
            result.push([word, res.scores[index]])
          })
          result = result.sort((a, b) => b[1] - a[1])
          window.console.log(`匹配值：${result[0][0]}, p:${result[0][1]}`)
          if (!this.show) {
            this.showPanel();
          }
          this.startRecorder();
        },
        {
          includeSpectrogram: true,
          suppressionTimeMillis,
          probabilityThreshold: 0.99
        })
        .catch((err) => {
          throw Error('ERROR: Failed to start streaming display: ' + err.message)
        });
    },
    initRecorder () {
      Recorder.getPermission().then(() => {
        // console.log('给权限了');
        this.recorder = new Recorder({
          // 采样位数，支持 8 或 16，默认是16
          sampleBits: 16,
          // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值
          sampleRate: 16000,
          // 声道，支持 1 或 2， 默认是1
          numChannels: 1,
          // 是否边录边转换，默认是false
          compiling: false
        })

        let startTime = null
        const noSoundDuration = 10 * 1000 // 10s后返回休息状态
        this.recorder.onprogress = async (params) => {
          // console.log('录音时长(秒)', params.duration);
          // console.log('录音大小(字节)', params.fileSize);
          // console.log(params.duration, '录音音量百分比(%)', params.vol);
          this.voicePercent = params.vol;
          if (params.vol >= MIN_RECORD_VOL) {
            startTime = +new Date();
          }
          if (params.vol < MIN_RECORD_VOL) {
            if (!startTime && params.duration > 3) {
              this.state.status = 2;
              await this.stopRecorder();
              this.playSound('no_sound');
              this.isRecording = false;
              this.timer = setTimeout(() => {
                this.state.status = 0
              }, noSoundDuration)
              return
            }
            if (startTime && params.duration > 2) {
              const end = +new Date();
              if (end - startTime > 1000 * 2) {
                this.isRecording = false;
                this.state.status = 5;
                this.stopRecorder();
                this.playSound('please_wait');
                this.getVoiceResponse();
                startTime = null
              }
            }
          }
        }
      }, (error) => {
        console.warn(`${error.name} : ${error.message}`);
      });
    },
    // 开始录音
    async startRecorder () {
      if (this.state.status === 5) {
        return;
      }
      if (this.isRecording) {
        this.isRecording = false;
        this.state.status = 5
        await this.stopRecorder()
        this.getVoiceResponse()
        return
      };
      this.clearTimer();
      this.state.status = 1;
      this.playSound('yes_sir');
      this.isRecording = true;
      this.recorder.start()
    },
    // 结束录音
    stopRecorder () {
      this.recorder.stop()
      this.voicePercent = 0
    },
    async getVoiceResponse () {
      const file = this.recorder.getWAVBlob();
      const size = file.size;
      const baseStr = await this.blobToBase64(file)
      const base64 = baseStr.split(',')[1]
      const formData = new FormData()
      formData.append('file', file)
      formData.append('speech', base64)
      formData.append('screenId', this.screenId)
      formData.append('len', size)
      formData.append('type', 'voice')
      // const data = {
      //   speech: base64,
      //   screenId: this.screenId,
      //   len: size,
      //   type: 'voice'
      // }
      voicecontrol(formData).then(async res => {
        if (res.code === 200) {
          this.stopSound();
          const aVoiceTxt = []
          const data = res.data;
          const eventCollection = data.eventCollection || [];
          if (eventCollection.length) {
            let events = [];
            if (eventCollection[0].events === 'exit') { // 退出高亮遮罩层遮罩层
              this.$store.commit('view/clearLinkComps');
            } else {
              events = eventCollection.map(item => {
                aVoiceTxt.push(item.eventWords + (item.alias || '') + (Object.values(item.params)[0] || ''))
                return {
                  comId: item.comId,
                  type: 'emit_event',
                  data: {
                    event: {
                      action: item.events,
                      componentId: [item.comId],
                      isConnect: false,
                      connectCompId: []
                    },
                    params: item.params || {},
                    source: 'voice'
                  }
                }
              })
            }
            this.state.status = 4;
            this.playSound('do_it');
            this.state.status = 6;
            this.voiceTxt = aVoiceTxt.join('；') ? ('操控指令：' + aVoiceTxt.join('；')) : data.str;
            this.pushFnQueue(events);
          }
        } else {
          this.state.status = 3
          this.stopSound();
          this.playSound('dont_understand');
        }
        this.timer = setTimeout(() => {
          this.state.status = 0;
        }, 10000)
      })
        .catch(e => {
          this.stopSound();
          this.state.status = 0;
        })
    },
    blobToBase64 (blob) {
      return new Promise((resolve, reject) => {
        const fileReader = new FileReader();
        fileReader.onload = (e) => {
          resolve(e.target.result)
        }
        fileReader.readAsDataURL(blob)
        fileReader.onerror = () => {
          reject(new Error('ERROR: blobToBase64'))
        }
      })
    },
    showPanel () {
      this.show = true;
      if (!this.voiceConfig.voiceWeak) {
        this.startRecorder();
      }
    },
    closePanel () {
      this.show = false;
      this.stopRecorder();
      this.state.status = 0;
      this.isRecording = false;
    },
    playSound (fileName) {
      return new Promise((resolve, reject) => {
        let audio;
        if (this.soundMap.has(fileName)) {
          audio = this.soundMap.get(fileName);
        } else {
          audio = new Audio(`../mp3/${fileName}.mp3`);
          this.soundMap.set(fileName, audio);
        }
        audio.play();
        audio.onended = () => {
          resolve();
          audio.onended = null;
        }
      })
    },
    stopSound () {
      this.soundMap.forEach(audio => {
        audio.pause();
        audio.currentTime = 0;
      })
    },
    submit () {
      if (!this.words.length) return;
      const data = {
        speech: this.words,
        screenId: this.screenId,
        type: 'text'
      }
      this.wordsList.push({
        words: this.words,
        type: 0
      })

      if (this.words.includes('数量')) {
        this.wordsList.push({
          words: '1209',
          type: 1
        })
        return
      }

      this.wordsList.push({
        words: '数据正在准备中，请稍后......',
        type: 1
      })

      this.scrollToBottom();
      this.loading = true;
      this.words = '';
      voicecontrol(data).then(async res => {
        if (res.success) {
          let words;
          if (res.code === 202) {
            words = {
              type: 1,
              words: `查询不到${data.speech.substr(2)}`
            }
          }
          if (res.code === 200) {
            words = {
              type: 1,
              words: `${res.data.str}，执行成功`
            }
            const data = res.data;
            const eventCollection = data.eventCollection || [];
            if (eventCollection.length) {
              if (eventCollection[0].events === 'exit') { // 退出高亮遮罩层遮罩层
                this.$store.commit('view/clearLinkComps');
              } else {
                const events = eventCollection.map(item => {
                  return {
                    comId: item.comId,
                    type: 'emit_event',
                    data: {
                      event: {
                        action: item.events,
                        componentId: [item.comId],
                        isConnect: false,
                        connectCompId: []
                      },
                      params: item.params || {},
                      source: 'voice'
                    }
                  }
                })
                this.pushFnQueue(events);
              }
            }
          }
          if (words) {
            this.wordsList.splice(this.wordsList.length - 1, 1, words);
          }
          this.scrollToBottom();
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    scrollToBottom () {
      this.$nextTick(() => {
        const containter = this.$refs.containter;
        if (containter) {
          containter.lastChild.scrollIntoView();
        }
      })
    },
    switchMode () {
      this.mode === 0 ? (this.mode = 1) : (this.mode = 0);
    },
    clearTimer () {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
    }
  }
}
</script>

<style lang="scss">
.voice-control {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
  .el-loading-spinner {
    margin-top: -10px;
  }
  .voice-button {
    cursor: pointer;
    position: relative;
    width: 45px;
    height: 45px;
    background-image: url(../../../assets/img/voice-button.png);
    background-size: 100% 100%;
    &:hover {
      .tips-info {
        display: block;
      }
    }
    .loading-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-image: url(../../../assets/img/voice-loading-border.png);
      background-size: 100% 100%;
      .spinner {
        width: 20px;
        height: 20px;
        background-image: url(../../../assets/img/voice-spinner.png);
        background-size: contain;
        animation: rotating 2s linear infinite;
      }
    }
    .loading-info {
      padding: 2px;
      position: absolute;
      width: 180px;
      right: 100%;
      margin-right: 16px;
      top: 50%;
      transform: translateY(-50%);
      background-image: linear-gradient(#8AA6E9 100%, #39486A 100%);
      border-radius: 8px;
      .word {
        line-height: 1.3333;
        border-radius: 6px;
        font-size: 14px;
        padding: 8px 16px;
        color: #fff;
        background-image: linear-gradient(135deg, rgba(106, 124, 165, 0.8) 0%, rgba(30, 38, 58, 0.8) 100%);
        backdrop-filter: blur(4px);
      }
    }
    .tips-info {
      display: none;
      padding: 2px;
      position: absolute;
      width: 180px;
      right: 100%;
      margin-right: 16px;
      top: 50%;
      transform: translateY(-50%);
      background-image: linear-gradient(#8AA6E9 100%, #39486A 100%);
      border-radius: 8px;
      .word {
        line-height: 1.3333;
        border-radius: 6px;
        font-size: 14px;
        padding: 8px 16px;
        color: #fff;
        background-image: linear-gradient(135deg, rgba(106, 124, 165, 0.8) 0%, rgba(30, 38, 58, 0.8) 100%);
        backdrop-filter: blur(4px);
      }
    }
  }
  .voice-control-panel {
    .voice-panel {
      position: fixed;
      width: 300px;
      min-height: 108px;
      right: 10px;
      bottom: 10px;
      padding: 15px 15px 8px;
      background: linear-gradient(135deg, rgba(106, 124, 165, 0.8) 0%, rgba(30, 38, 58, 0.8) 100%);
      backdrop-filter: blur(4px);
      border-radius: 8px;
      transform: translateX(320px);
      transition: transform .6s cubic-bezier(0.22, 0.61, 0.36, 1);
      &.show {
        transform: translateX(0);
      }
      .close-panel {
        position: absolute;
        top: 8px;
        right: 8px;
        font-size: 12px;
        padding: 4px;
        cursor: pointer;
        &:hover {
          color: #fff;
          background: rgba(204, 219, 255, 0.16);
          border-radius: 50%;
        }
      }
      .switch-mode {
        position: absolute;
        bottom: 4px;
        right: 10px;
        color: #a4a4a4;
        cursor: pointer;
        padding: 0 3px;
        border: 1px solid #b3b3b3;
        border-radius: 3px;
      }
      .text-input {
        width: 268px;
        min-height: 22px;
        font-family: 'PingFang SC';
        font-style: normal;
        font-size: 14px;
        color: #fff;
        font-weight: 400;
        line-height: 22px;
        padding-left: 5px;
        input {
          width: 100%;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          background-color: rgba(0, 0, 0, 0);
          &::placeholder {
            color: rgba(255, 255, 255, 0.7);
          }
        }
      }
      .voice-func {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 5px;
        gap: 4px;
        width: 236px;
        height: 48px;
        flex: none;
        order: 1;
        flex-grow: 0;
        margin: 12px auto 0;
        .voice-bo {
          width: 85px;
          height: 14px;
          background-image: url(../../../assets/img/voice-wave.png);
          background-size: 100% 100%;
        }
        .voice-btn {
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          width: 48px;
          height: 48px;
          background-image: url(../../../assets/img/voice-button.png);
          background-size: 100% 100%;
          border-radius: 50%;
          overflow: hidden;
          cursor: pointer;
          &.recording {
            background-image: url(../../../assets/img/recording.png);
          }
          .voice-percent {
            width: 100%;
            height: 0;
            background: rgba(60, 146, 255, 0.5);
          }
        }
      }
    }
    .text-panel {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      width: 300px;
      background: linear-gradient(135deg, rgba(106, 124, 165, 1) 0%, rgba(30, 38, 58, 1) 100%);
      transform: translateX(320px);
      transition: transform .6s cubic-bezier(0.22, 0.61, 0.36, 1);
      &.show {
        transform: translateX(0);
      }
      .text-panel-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .close-icon {
          position: absolute;
          color: #fff;
          font-size: 16px;
          top: 5px;
          left: 5px;
          cursor: pointer;
        }
        .text-c-h {
          flex: none;
          height: 35px;
          line-height: 35px;
          text-align: center;
          color: #fff;
          font-size: 14px;
        }
        .switch-mode {
          position: absolute;
          top: 5px;
          right: 10px;
          color: #a4a4a4;
          cursor: pointer;
          padding: 0 3px;
          border: 1px solid #b3b3b3;
          border-radius: 3px;
        }
        .text-talk-wrap {
          flex: auto;
          overflow: auto;
          .chat-container {
            padding: 0 10px 10px;
            .chat-message-row {
              display: flex;
              align-items: center;
              margin-bottom: 15px;
              &.flex-start {
                justify-content: flex-start;
              }
              &.flex-end {
                justify-content: flex-end;
              }
              .chat-avatar {
                flex: none;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .chat-message {
                background-color: #eff2f7;
                border-radius: .3rem;
                padding: 6px 10px;
                margin: 0 5px;
                p {
                  margin: 0;
                }
              }
              .chat-reply {
                background-color: #e0f2ff;
              }
            }
          }
        }
        .text-bottom {
          display: flex;
          align-items: center;
          height: 40px;
          line-height: 40px;
          padding: 0 10px;
          .ipt {
            margin-right: 10px;
          }
        }
      }
    }
  }
}
</style>
