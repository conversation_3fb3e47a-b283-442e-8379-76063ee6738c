<template>
  <div class="instruct-config">
    <div class="page-title">
      <span class="el-icon-close close-panel" @click="closePanel"></span>
      配置指令
    </div>
    <div class="page-content">
      <el-collapse class="comp-wrapper" v-model="currentComId" accordion @change="currComChange" v-if="comList.length">
        <el-collapse-item v-for="item in comList" :key="item.id" :title="compName(item)" :name="item.id">
          <div class="com-instruct" v-if="item.id == currentComId" v-loading="loading2" element-loading-background="rgba(45, 47, 56, 1)">
            <el-tabs class="com-tab">
              <el-tab-pane label="组件词">
                <el-form label-width="80px" label-position="left" size="mini" @submit.native.prevent>
                  <el-form-item label="别名">
                    <el-input placeholder="输入别名，按回车添加" v-model="word" @keyup.native.enter="addWord(comConfig.aliasList)" @blur="addWord(comConfig.aliasList)"></el-input>
                    <div class="tag-wrapper">
                      <div class="tag-item" v-for="(word, idx) in comConfig.aliasList" :key="'word' + idx">
                        <span class="tag-text">{{ word }}</span>
                        <span class="tag-close el-icon-close" @click="removeTag(comConfig.aliasList, idx)"></span>
                      </div>
                    </div>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
            </el-tabs>
            <el-tabs class="com-tab" v-model="activeTab" @edit="(target, action) => handleEventEdit(target, action, comConfig.eventsConfig)" @tab-click="tabClick">
              <el-tab-pane :label="'事件配置' + (index + 1)" :name="index + ''" v-for="(event, index) in comConfig.eventsConfig" :key="'event' + index">
                <el-form label-width="80px" label-position="left" size="mini">
                  <el-form-item label="关联事件">
                    <el-select class="w100" placeholder="请选择" disabled v-model="event.relatedEvents" @change="relatedEventChange(event)">
                      <el-option v-for="opt in eventOptions(item)" :key="opt.value" :label="opt.label" :value="opt.value"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="事件词">
                    <el-select class="w100" placeholder="请选择" multiple v-model="event.eventWords" clearable>
                      <el-option v-for="opt in eventWordsList(event.relatedEvents)" :key="opt.id" :label="opt.label" :value="opt.value"></el-option>
                    </el-select>
                  </el-form-item>
                  <template v-if="eventParamsOpts(item, event.relatedEvents).length">
                    <el-form-item label="事件参数">
                      <el-select class="w100" placeholder="请选择" v-model="event.params" clearable>
                        <el-option v-for="opt in eventParamsOpts(item, event.relatedEvents)" :key="opt.name" :label="opt.name + '（' + opt.description + '）'" :value="opt.name"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="词源类型">
                      <el-select class="w100" placeholder="请选择" v-model="event.type" clearable>
                        <el-option v-for="opt in wordSourceList" :key="opt.id" :label="opt.label" :value="opt.value"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="数据字段" v-if="event.type === 'datasource'">
                      <el-select class="w100" placeholder="请选择" v-model="event.field" clearable key="datasource">
                        <el-option v-for="opt in sourceKeyOpts(item)" :key="opt.id" :value="opt.name" :label="opt.name"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="特征词" v-if="event.type === 'ai'">
                      <el-select class="w100" v-model="event.aiValueList" multiple filterable allow-create placeholder="选择或创建特征词" key="ai">
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item>
                      <el-switch v-model="event.isComponentWord"></el-switch> <span class="switch-tip">同时绑定为组件词</span>
                    </el-form-item>
                  </template>
                </el-form>
              </el-tab-pane>
            </el-tabs>
            <div class="btn-wrapper">
              <el-button type="primary" size="mini" :loading="loading3" @click="submit(item)">保存配置</el-button>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <SeatomLoading v-if="loading1" />
  </div>
</template>

<script>
import { createInitControl, findVoiceConfig, updateVoiceConfig, getVoiceconfig } from '@/api/screen'
import { postData } from '@/api/datastorage'
import { getFilters } from '@/api/filter'
import { mapState, mapGetters } from 'vuex'
import dataUtil from '@/utils/data'
const words = ['人名', '时间', ' 手机号', '年龄', '民族', '性别', '城市', '地点', '车牌', '职务', '机构', '天气', '药品', '症状']
export default {
  name: 'InstuctConfig', // 指令配置
  data () {
    return {
      currentComId: '',
      activeTab: '0',
      activeTab2: '0',
      word: '',
      comList: [],
      comConfig: {
        screenId: '', // 大屏id
        comId: '', // 组件id
        comName: '普通柱状图', // 组件名称
        aliasList: [], // 别名
        eventsConfig: [
          {
            relatedEvents: '', // 关联事件
            eventWords: [], // 事件词
            params: '', // 事件参数
            type: '', // 词源类型
            field: '', // 数据源字段
            valueList: [],
            isComponentWord: false
          }
        ]
      },
      eventWordMap: {
        open: [],
        focus: [],
        defocus: []
      },
      wordSourceList: [
        { id: 1, label: '数据源数据', value: 'datasource' },
        { id: 2, label: 'AI抽取', value: 'ai' }
      ],
      options: words.map(item => ({ label: item, value: item })),
      defaultEvents: [ // 默认事件类型
        { value: 'dataChange', label: '当请求完成或数据变化时' },
        { value: 'compClick', label: '点击整体组件时' },
        { value: 'compHover', label: 'hover整体组件时' },
        { value: 'compDbclick', label: '双击整体组件时' },
        { value: 'compMouseleave', label: '鼠标离开整体组件时' }
      ],
      screenComData: {},
      screenFilterMap: {},
      loading1: false,
      loading2: false,
      loading3: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      screenComs: state => state.editor.screenComs,
      screenFilters: state => state.editor.screenFilters,
      comsData: state => state.editor.comsData
    }),
    ...mapGetters('datacontainer', ['getContainerDataById']),
    compName () {
      return function (item) {
        return item.parentComName ? item.parentComName + ' / ' + item.alias : item.alias
      }
    },
    eventOptions () {
      return function (com) {
        const events = com.events
        const options = events.map(opt => {
          return {
            value: opt.name,
            label: opt.description,
            params: opt.params
          }
        })
        if (com.type === 'com') {
          return [...this.defaultEvents, ...options]
        }
        // 子组件只支持一个默认通用事件
        return [...this.defaultEvents.slice(0, 1), ...options]
      }
    },
    eventWordsList () {
      return function (eventName) {
        if (!eventName) return [];
        let words = [];
        if (['dataChange', 'compClick', 'compDbclick'].includes(eventName)) {
          words = this.eventWordMap.open;
        } else if (['compHover'].includes(eventName)) {
          words = this.eventWordMap.focus;
        } else if (['compMouseleave'].includes(eventName)) {
          words = this.eventWordMap.defocus;
        } else {
          words = this.eventWordMap.open;
        }
        return (words || []).map((item, index) => {
          return {
            id: index,
            label: item,
            value: item
          }
        })
      }
    },
    eventParamsOpts () { // 事件参数
      return function (com, value) {
        const event = this.eventOptions(com).find(item => item.value === value);
        if (event) {
          return event.params || []
        }
        return []
      }
    },
    getFilterData () {
      return function (com) {
        const isCurrScreen = this.screenInfo.id === com.screenId;
        let comData = [];
        const { source, sourceType } = com.dataConfig.dataResponse;
        if (sourceType === 'datacontainer') {
          const dataContainerComId = source.datacontainer.data.dataContainerComId;
          const containerData = this.getContainerDataById(dataContainerComId);
          comData = containerData.data || [];
        } else if (isCurrScreen) {
          comData = this.comsData[com.id] || [];
        } else {
          comData = this.screenComData[com.id] || [];
        }

        let filter = []
        const filters = com.dataConfig.dataResponse.filters
        if (!filters.enable) {
          filter = []
        } else {
          filter = _.filter(filters.list, { enable: true }).map(({ id }) => {
            if (isCurrScreen) {
              return this.screenFilters[id]
            }
            const list = this.screenFilterMap[com.screenId] || [];
            return list.find(item => item.id === id);
          })
        }

        const filterData = dataUtil.filterData(comData, filter);
        return filterData
      }
    },
    sourceKeyOpts () {
      return function (com) {
        if (com) {
          const keys = []
          const filterData = this.getFilterData(com);
          const fieldMapping = com.dataConfig.fieldMapping
          filterData.forEach(item => {
            const key = Object.keys(item)
            keys.push(...key)
          })
          const uniKeys = Array.from(new Set(keys))
          const result = uniKeys.map((key, idx) => {
            const type = fieldMapping.find(item => (item.target || item.source) === key)?.type || 'string'
            return {
              id: idx,
              name: key,
              type
            }
          })
          return result
        }
        return []
      }
    }
  },
  mounted () {
    this.getVoiceConfigList();
    this.getVocieOptions();
  },
  beforeDestroy () {
    this.comList = [];
    this.$store.commit('editor/initVoiceConfigShow', { show: false })
  },
  methods: {
    async getVoiceConfigList () {
      const params = {
        screenId: this.screenInfo.id
      }
      this.loading1 = true;
      const res = await createInitControl(params);
      if (res && res.success) {
        this.comList = res.data;
      }
      this.loading1 = false;
    },
    addWord (words) {
      const word = this.word.replace(/\s/g, '');
      if (word) {
        words.push(word);
      }
      this.word = '';
    },
    removeTag (words, index) {
      words.splice(index, 1);
    },
    submit (item) {
      const filterData = this.getFilterData(item);
      this.comConfig.eventsConfig.forEach(event => { // 给事件配置设置valueList
        if (event.type === 'datasource' && event.field) {
          event.valueList = filterData.map(v => v[event.field])
        } else {
          event.valueList = [];
        }
        if (event.type !== 'ai') {
          event.aiValueList = [];
        }
      })
      const data = {
        ...this.comConfig
      }
      this.loading3 = true;
      updateVoiceConfig(data, { comId: item.id }).then((res) => {
        if (res.success) {
          this.$message.success('保存成功')
        }
      }).finally(() => {
        this.loading3 = false
      })
    },
    handleEventEdit (target, action, configs) {
      if (action === 'add') {
        const tab = {
          relatedEvents: '',
          eventWords: [],
          paramsConfig: [
            {
              type: '',
              field: '',
              valueList: [],
              params: '',
              isComponentWord: false
            }
          ]
        }
        configs.push(tab);
        this.activeTab = (configs.length - 1) + '';
      } else if (action === 'remove') {
        if (configs.length === 1) {
          this.$message.warn('请至少保留1项')
          return
        }
        const index = +target;
        configs.splice(index, 1);
        if (index > (configs.length - 1)) {
          this.activeTab = (configs.length - 1) + '';
        } else {
          this.activeTab = index + '';
        }
        this.$message.success('已删除')
      }
      this.activeTab2 = '0';
    },
    handleParamsEdit (target, action, configs) {
      if (action === 'add') {
        const tab = {
          type: '',
          field: '',
          valueList: [],
          params: '',
          isComponentWord: false
        }
        configs.push(tab);
        this.activeTab2 = (configs.length - 1) + '';
      } else if (action === 'remove') {
        if (configs.length === 1) {
          this.$message.warn('请至少保留1项')
          return
        }
        const index = +target;
        configs.splice(index, 1);
        if (index > (configs.length - 1)) {
          this.activeTab2 = (configs.length - 1) + '';
        } else {
          this.activeTab2 = index + '';
        }
        this.$message.success('已删除')
      }
    },
    tabClick () {
      this.activeTab2 = '0';
    },
    closePanel () {
      this.$store.commit('editor/initVoiceConfigShow', { show: false })
    },
    currComChange (id) {
      if (!id) return;
      const com = this.comList.find(item => item.id === id);
      this.loading2 = true;
      const params = {
        screenId: this.screenInfo.id,
        comId: id
      }
      findVoiceConfig(params).then(res => {
        if (res && res.success) {
          this.comConfig = res.data;
        }
      }).finally(() => {
        this.loading2 = false;
      })
      // 组件是否在当前大屏
      const isCurrScreen = this.screenInfo.id === com.screenId;
      if (!isCurrScreen) { // 非当前大屏组件，需要获取数据和过滤器
        this.getCompdataById(id);
        this.getFilterByScreenId(com.screenId);
      }
    },
    getVocieOptions () { // 获取事件词配置
      getVoiceconfig().then(res => {
        if (res && res.success) {
          this.eventWordMap = res.data;
        }
      })
    },
    relatedEventChange (event) {
      event.eventWords = [];
    },
    getCompdataById (id) {
      if (!this.screenComData[id]) {
        postData({}, [{ componentId: id }]).then(res => {
          const data = res[0];
          if (data.success) {
            this.$set(this.screenComData, id, data.data);
          }
        })
      }
    },
    getFilterByScreenId (id) {
      if (!this.screenFilterMap[id]) {
        getFilters({ screenId: id }).then(res => {
          if (res.success) {
            this.$set(this.screenFilterMap, id, res.data);
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.instruct-config {
  display: flex;
  flex-direction: column;
  position: absolute;
  right: 0;
  top: 0;
  width: 470px;
  height: 100%;
  background: #1c1f25;
  z-index: 2000;
  .page-title {
    position: relative;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
    .close-panel {
      position: absolute;
      top: 12px;
      left: 10px;
      font-size: 16px;
      cursor: pointer;
    }
  }
  .page-content {
    flex: 1;
    padding: 20px 16px;
    overflow: auto;
    .comp-wrapper {
      ::v-deep {
        .el-collapse-item__header {
          height: 48px;
          line-height: 48px;
          color: #fff;
          background-color: unset;
          border-bottom: 1px solid rgba(204, 219, 255, 0.16);
        }
        .el-collapse-item__wrap {
          background-color: unset;
        }
        .el-collapse-item__content {
          padding: 10px 10px 25px;
        }
      }
      .com-instruct {
        .com-tab ~.com-tab {
          margin-top: 24px;
        }
        ::v-deep {
          .el-form {
            .el-form-item__label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
            }
          }
          .el-tabs {
            .el-tabs__header {
              margin: 0;
              .el-tabs__nav-wrap::after {
                display: none;
              }
              .el-tabs__active-bar {
                display: none;
              }
              .el-tabs__new-tab {
                line-height: 16px;
                margin: 5px 0 0 0;
              }
              .el-tabs__nav-next, .el-tabs__nav-prev {
                line-height: 28px;
              }
              .el-tabs__item {
                height: 28px;
                line-height: 28px;
                font-size: 12px;
                padding: 0 12px;
                border: 1px solid #3A3F4A;
                border-radius: 4px 4px 0px 0px;
                margin-right: 5px;
                &.is-active {
                  border-color: #3D85FF;
                }
              }
            }
            .el-tabs__content {
              border: 1px solid rgba(204, 219, 255, 0.16);
              min-height: 50px;
              padding: 15px;
            }
          }
        }
        .tag-wrapper {
          padding: 10px 0;
          .tag-item {
            position: relative;
            display: inline-block;
            min-height: 28px;
            line-height: 20px;
            padding: 4px 26px 4px 10px;
            background: rgba(204, 219, 255, 0.1);
            border-radius: 16px;
            margin: 0 5px 5px 0;
            .tag-text {
              color: #fff;
              font-size: 12px;
            }
            .tag-close {
              position: absolute;
              top: 7px;
              right: 10px;
              cursor: pointer;
            }
          }
        }
        .btn-wrapper {
          text-align: center;
          padding-top: 15px;
          // background: #2d2f38;
        }
      }
    }
  }
  .w100 {
    width: 100%;
  }
  .switch-tip {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  }
}
</style>
