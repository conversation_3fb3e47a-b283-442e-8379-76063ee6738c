<template>
  <div class="filter-item-wrapper">
    <FilterItem
      :key="source.id"
      :item="source"
      v-on="$listeners"
      @delItem="delItem" />
  </div>
</template>

<script>
import FilterItem from './FilterItem';
export default {
  props: {
    index: Number,
    source: {
      type: Object,
      default () {
        return []
      }
    }
  },
  components: {
    FilterItem
  },
  methods: {
    delItem () {
      this.$parent.$parent.$emit('delItem', this.source, this.index);
    }
  }
}
</script>
