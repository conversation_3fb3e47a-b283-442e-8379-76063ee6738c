<template>
  <el-dialog :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'" :visible.sync="show" title="预览数据" append-to-body width="700px" :close-on-click-modal="false" @close="closeDialog" top="0">
    <el-table :data="tableData" :height="400" size="mini">
      <el-table-column :label="val" :prop="val" v-for="(key, val, idx) in keyObj" :key="idx" min-width="150" align="center"></el-table-column>
    </el-table>
    <div slot="footer">
      <el-button type="plain" size="mini" @click="closeDialog">关闭</el-button>
    </div>
    <seatom-loading v-if="loading"></seatom-loading>
  </el-dialog>
</template>

<script>
import { previewData } from '@/api/datastorage';
export default {
  name: 'PreviewData',
  props: {},
  data () {
    return {
      show: false,
      cmOptions: {
        readOnly: true
      },
      tableData: [],
      loading: false
    }
  },
  computed: {
    keyObj () {
      if (this.tableData.length) {
        const obj = this.tableData[0];
        return obj
      }
      return {}
    }
  },
  methods: {
    previewData (item) {
      this.show = true;
      this.getData({ storageId: item.id, type: item.type });
    },
    closeDialog () {
      this.show = false;
      this.tableData = [];
    },
    async getData ({ storageId, type }) {
      this.loading = true;
      const res = await previewData({ storageId, type });
      if (res && res.success) {
        this.tableData = res.data;
      }
      this.loading = false;
    }
  }
}
</script>
