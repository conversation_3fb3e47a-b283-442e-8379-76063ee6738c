<template>
  <div class="collapse-panel-wp filter-item template-item">
    <div class="panel-header">
      <div class="panel-title">
        <div class="filter-title">
          <div class="filter-name">
            <el-input v-if="item.editable" v-model="item.name" v-select class="datav-input filter-name-input" size="mini" @blur="inputBlur(item)" />
            <div v-else class="filter-name-text" :title="item.name">{{ item.name }}</div>
          </div>
          <div class="action-wp --edit">
            <!-- <i class="datav-icon datav-font icon-edit edit-btn" @click="setEdit(item)"></i> -->
          </div>
          <div class="filter-count">
            <div v-if="!item.id">
              <i class="dot"></i>
              <span>未保存</span>
            </div>
          </div>
          <div class="action-wp">
            <i class="datav-icon datav-font icon-delete del-btn" @click="delItem(item)"></i>
          </div>
          <div class="filter-dot --none"></div>
        </div>
      </div>
      <div class="toggle-btn" @click="toggle(item)">
        <i class="datav-icon datav-font toggle-icon" :class="[item.show ? 'icon-bottom' : 'icon-right']"></i>
      </div>
    </div>
    <div class="panel-content-wp" :class="{'panel-content-exit-done': !item.show}">
      <div class="cp-wrap" title="">
        <div class="filter-editor">
          <div class="func-tit">
            <span>function filter (<span>data</span>,<span> callbackArgs</span>,<span> fieldSetting</span>) {</span>
          </div>
          <CodeEditor v-model="content" :options="cmOptions" />
          <div class="func-tit">
            <span>}</span>
          </div>
        </div>
        <!-- <div class="filter-actions">
          <el-button type="default" size="mini" class="plain-btn" @click="toggle(item)">取消</el-button>
          <el-button type="primary" size="mini" @click="saveFilter(item)" :loading="loading">完成</el-button>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import CodeEditor from '@/components/editor/data-source/CodeEditor';
import { createScreenFilter, updateFilter } from '@/api/filter';
import { Encrypt, handleDecrypt } from '@/utils/base';
import { mapState } from 'vuex';
export default {
  name: 'FilterItem',
  props: ['item'],
  components: {
    CodeEditor
  },
  data () {
    return {
      loading: false,
      content: '',
      cmOptions: {
        readOnly: true
      }
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  directives: {
    select: {
      inserted: function (el) {
        el.querySelector('input').select()
      }
    }
  },
  created () {
    if (this.item) {
      this.content = handleDecrypt(this.item.content) || this.item.content
    }
  },
  methods: {
    setEdit (item) {
      this.$set(item, 'editable', !item.editable);
    },
    toggle (item) {
      this.$set(item, 'show', !item.show);
    },
    inputBlur (item) { // 输入框失去焦点
      if (!item.id) {
        if (item.name !== '新建过滤器') {
          this.saveFilter(item);
        }
      } else {
        this.saveFilter(item);
      }
      this.setEdit(item);
    },
    async saveFilter (item) { // 保存
      item.content = this.content
      const filter = {
        callbackKeys: item.callbackKeys,
        enable: item.enable,
        id: item.id,
        name: item.name,
        screenId: this.screenInfo.id,
        show: item.show,
        systemParams: item.systemParams,
        type: item.type,
        content: Encrypt(item.content)
      }
      this.loading = true;
      if (filter.id) {
        const res = await updateFilter(filter, {});
        if (res && res.success) {
          this.$store.commit('editor/updateScreenFilter', filter);
          this.$message.success('保存成功');
          this.$emit('refresh');
        } else {
          this.$message.error(res.message);
        }
      } else {
        const res = await createScreenFilter({ screenId: this.screenInfo.id, ...filter }, {});
        if (res && res.success) {
          this.$store.commit('editor/updateScreenFilter', res.data);
          this.$message.success('保存成功');
          this.$emit('refresh');
        } else {
          this.$message.error(res.message);
        }
      }
      this.loading = false;
    },
    delItem (item) {
      this.$emit('delItem', item);
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-item {
  color: #aaa;
  margin: 10px 0;
  box-shadow: 0 0 10px -3px #000;
  transition: opacity .2s;
  .panel-header {
    padding-left: 6px;
    display: flex;
    height: 30px;
    line-height: 30px;
    align-items: center;
    position: relative;
    background: var(--datav-panel-color);
    border: 1px solid #566273;
    color: #bcc9d4;
    background: #262c33;
    transition: border-color .2s;
    .panel-title {
      display: flex;
      flex: 1;
      .filter-title {
        flex: 1;
        display: flex;
        align-items: center;
        position: relative;
        .filter-name {
          position: relative;
          width: 150px;
          .filter-name-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .action-wp.--edit {
          width: 40px;
          text-align: left;
        }
        .filter-count {
          flex: 1;
          .dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: red;
            margin-right: 5px;
          }
        }
        .action-wp {
          width: 30px;
          text-align: center;
          padding: 0 5px;
        }
        .filter-dot {
          position: absolute;
          left: -24px;
          top: 5px;
          width: 6px;
          height: 6px;
          padding: 8px;
          border-radius: 20px;
          background: var(--datav-main-color);
          background-clip: content-box;
          box-sizing: content-box;
          transition: .2s;
          &.--none {
            display: none;
          }
        }
      }
    }
    .toggle-btn {
      width: 30px;
      height: 100%;
      cursor: pointer;
      text-align: center;
      line-height: 30px;
      border-left: 1px solid #566273;
      transition: border-color .2s;
      font-size: 0;
    }
  }
  .panel-content-wp {
    height: 205px;
    overflow: hidden;
    transition: border-color .2s;
    border-left: 1px solid #566273;
    border-right: 1px solid #566273;
    border-bottom: 1px solid #566273;
    transition: height .25s linear;
    &.panel-content-exit-done {
      height: 0;
      border-bottom: none;
    }
    .cp-wrap {
      height: 100%;
    }
    .filter-editor {
      height: 200px;
      width: 100%;
      .func-tit {
        line-height: 20px;
        padding-left: 5px;
      }
    }
    .filter-actions {
      position: relative;
      height: 37px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      background: #262c33;
      border-top: 1px solid #566273;
      padding: 0 5px;
    }
  }
  .plain-btn {
    background: transparent;
    color: #409eff;
    border-color: #409eff;
  }
  ::v-deep {
    .CodeMirror {
      height: 160px;
    }
  }
}
</style>
