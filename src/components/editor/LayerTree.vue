<template>
  <div class="layer-tree">
    <div class="layer-tree-list" @dragover="handleDragOver" @drop="handleDrop">
      <li
        v-for="(node, index) in loadedNodes"
        :key="node.data.id"
        :ref="`ref_node_${node.data.id}`"
        :class="[
          'layer-node',
          node.data.type === 'group' ? 'layer-node-group' : 'layer-node-com',
          {
            collapse: node.collapse,
            selected: node.selected || selectGroup(node),
            'layer-node-thumbnail': !isThumbnail,
          },
        ]"
        :style="{
          paddingLeft: `${8 + 10 * (node.depth - 1)}px`,
        }"
        :data-id="node.data.id"
        :data-index="index"
        draggable="true"
        @dragstart="handleDragStart($event, node)"
        @dragend="handleDragEnd"
        @click.exact="handleClick(node)"
        @click.meta.exact="handleClickCtrl(node)"
        @click.shift.exact="handleClickShift(node)"
        v-contextmenu:contextmenu
      >
        <i
          :class="[
            node.collapse ? 'icon el-icon-arrow-right' : 'icon el-icon-arrow-right active',
          ]"
          v-if="node.data.type === 'group'"
          @click.stop="handleCollapseClick(node)"
        >
        </i>
        <div class="folder" v-if="!!node.data.groupName">
          <i
            :class="[
              node.collapse ? 'el-icon-folder' : 'el-icon-folder-opened',
            ]"
            style="margin: 0 4px"
          ></i>
          {{ node.data.groupName }}
        </div>
        <div
          v-show="isThumbnail && node.data.type !== 'group'"
          class="components-item-img"
          :style="{
            backgroundImage: getComDataById(node.data.id)
              ? `url(${replaceUrl(getComDataById(node.data.id).icon)})`
              : null,
          }"
        ></div>
        <hz-icon
          class="mr-10"
          v-show="!isThumbnail"
          :name="handleIsThumbnailIcon(node.data.id)"
        ></hz-icon>
        <div class="layer-right-content">
          <span class="layer-name">{{ getComDataById(node.data.id) && getComDataById(node.data.id).alias }}</span>
          <div class="layer-thumbail-item">
            <i v-show="
                (node.data.type === 'group')
                  ? compLock(node)
                  : getComDataById(node.data.id) &&
                    getComDataById(node.data.id).attr.lock"
              class="el-icon-lock mr-6"
              @click="handleCtxClick('unLock')"
            ></i>
            <i v-show="(node.data.type === 'group')
                ? !compShow(node)
                : getComDataById(node.data.id) && !getComDataById(node.data.id).show" class="el-icon-view mr-6" @click="handleCtxClick('show')"></i>
          </div>
        </div>
      </li>
      <div
        v-if="layerMoveLineShow"
        class="layer-move-to-line"
        :style="{
          transform: `translate(0, ${layerMoveLineY}px)`,
        }"
      ></div>
      <div ref="dragingWrap" class="draging-wrap"></div>
    </div>
    <!-- 右键菜单 -->
    <div style="display: none">
      <v-contextmenu ref="contextmenu" @contextmenu="handleCtxMenu">
        <v-contextmenu-item
          v-for="menu in ctxMenuList"
          v-show="menu.show"
          :key="menu.key"
          @click="handleCtxClick(menu.key)"
          :class="[{'contextmenu-disable': (!cancelGroupShow && menu.key === 'cancelGroup') ||
          (!groupShow && menu.key === 'group') ||
          (renameShow && menu.key === 'rename')}]"
        >
          {{ menu.label }}
        </v-contextmenu-item>
      </v-contextmenu>
    </div>
    <SeatomLoading v-if="layerLoading" />
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { compPanelList } from '@/common/constants';
import canvasBus from '@/utils/canvasBus';
import { replaceUrl } from '@/utils/base';

export default {
  name: 'LayerTree',

  inject: ['getLayerTree'],

  props: {
    isThumbnail: {
      type: Boolean,
      default: true
    }
  },

  data () {
    this._insertArr = []
    return {
      shiftNode: null,
      layerMoveLineShow: false,
      layerMoveLineY: 0,
      replaceUrl: replaceUrl,
      contextmenuNode: '', // 右键组件名称
      originTree: [],
      canUpdateScreenLayers: false
    };
  },

  directives: {
    focus: {
      inserted: function (el) {
        el.querySelector('input').focus();
      }
    }
  },
  computed: {
    ...mapState({
      screenInfo: (state) => state.editor.screenInfo,
      sceneId: (state) => state.editor.sceneId,
      pageId: (state) => state.editor.pageId,
      currentSelectId: (state) => state.editor.currentSelectId,
      layerLoading: state => state.editor.layerLoading
    }),
    ...mapGetters('editor', ['getComDataById', 'ctxMenuList']),
    layerTree () {
      return this.getLayerTree();
    },
    loadedNodes () {
      let loadedNodes = this.layerTree.loadedNodes;
      if (this.screenInfo.type === 'mobile') { // 移动端不显示模块组件
        loadedNodes = loadedNodes.filter(item => {
          return !item.data.id.startsWith('interaction-container-modulepanel');
        })
      }
      if (this.screenInfo.screenType === 'scene') {
        if (this.pageId) {
          const nodes = loadedNodes.filter((node) => {
            return (
              node.data.pageId === this.pageId ||
              (node.data.sceneId === this.sceneId && !node.data.pageId)
            );
          });
          return nodes
        } else {
          return loadedNodes.filter((node) => {
            return node.data.sceneId === this.sceneId && !node.data.pageId;
          });
        }
      } else {
        return loadedNodes;
      }
    },
    cancelGroupShow () {
      const selectedNode = []; let show = false;
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNode.push(item.data.id);
      })
      if (selectedNode.length) {
        let sum = 0;
        selectedNode.forEach(item => {
          if (item.includes('groups_')) sum++;
        })
        if (sum > 0) {
          show = true
        }
      }
      return show;
    },
    groupShow () {
      const selectedNodes = []; let show = false;
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNodes.push(item);
      })
      if (selectedNodes.length > 1) {
        for (let i = 1; i < selectedNodes.length; i++) {
          // 判断所选组件是否是同一层级
          if ((selectedNodes[0]?.parent?.data?.id === selectedNodes[i]?.parent?.data?.id)) {
            show = true; break;
          }
          // 判断在场景大屏中是否是同一场景或页面
          if ((selectedNodes[0]?.data?.pageId === selectedNodes[i]?.data?.pageId &&
          selectedNodes[0]?.data?.sceneId === selectedNodes[i]?.data?.sceneId)) {
            show = true; break;
          }
        }
      }
      return show;
    },
    renameShow () {
      const selectedNodes = [];
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNodes.push(item);
      })
      return selectedNodes.length !== 1;
    },
    selectGroup () { // 移动端选中模块，图层中选中分组
      const vm = this;
      return function (node) {
        if (node.data.type === 'group' && this.screenInfo.type === 'mobile') {
          if (_.isArray(vm.currentSelectId)) {
            return vm.currentSelectId.includes(node.data.comId);
          }
          return vm.currentSelectId === node.data.comId
        }
        return false
      }
    }
  },

  methods: {
    handleCtxMenu (vnode) {
      const nodeId = vnode.elm.dataset.id;
      let node;
      if (nodeId && (node = this.layerTree.getNodeById(nodeId))) {
        if (!node.selected) {
          this.layerTree.select(node.data.id);
          canvasBus.emit('select_node', nodeId);
          if (node.data.type === 'group' && this.screenInfo.type === 'mobile') {
            canvasBus.emit('select_node', node.data.comId);
          }
        }
      }
    },
    handleCtxClick (key) {
      canvasBus.emit('ctx_click', key);
      if (key !== 'rename' && key !== 'delete') canvasBus.emit('select_node', null);
      if (key === 'copy') this.layerTree.clearSelect();
    },
    handleDragStart (e, node) {
      this._dragSelectDatas = null;
      this._startDragAnchorNode = null;
      this._dragAnchorDirection = -1; // 0 代表前，1 代表后
      this._dragAnchorNode = null;

      this._startDragAnchorNode = node;

      let selectedDatas = [];
      if (!node.selected) {
        selectedDatas = [node.data];
      } else {
        selectedDatas = this.layerTree.getSelectedNodes().map((n) => n.data);
      }
      this._dragSelectDatas = selectedDatas;

      const selectEles = this._dragSelectDatas.map((d) =>
        this.$refs[`ref_node_${d.id}`][0].cloneNode(true)
      );
      const dragImageEle = this.$refs.dragingWrap;
      dragImageEle.innerHTML = '';
      dragImageEle.append(...selectEles);
      e.dataTransfer.setDragImage(dragImageEle, -10, -10);

      this.layerTree.clearSelect();
    },
    async handleDragEnd (e) {
      if (this._dragSelectDatas) {
        this._dragSelectDatas
          .map((d) => this.layerTree.getNodeById(d.id))
          .forEach((n) => {
            n.selected = true;
          });
      }
      this.layerMoveLineShow = false;
      this.$refs.dragingWrap.innerHTML = '';
      this.saveTree();

      if (!this.canUpdateScreenLayers) {
        return
      }

      if (!!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene') {
        // 过滤节点与锚点节点相同的数据
        this._insertArr = this._insertArr.filter((insertItem) => {
          return insertItem.nodeData.id !== insertItem.anchorNodeData.id
        })

        if (!this._insertArr.length) {
          return
        }

        this.$store
          .dispatch('editor/dragInsertScreenLayer', {
            screenId: this.screenInfo.id,
            insertArr: this._insertArr
          })
          .then(() => {
            this.recoverTree();
          })
      } else {
        this.$store
          .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
          .then(() => {
            this.recoverTree();
          })
      }
    },
    formatCopyrTree (nodes) {
      nodes.forEach(node => {
        delete node.tree;
        delete node.parent;
        if (node.children) this.formatCopyrTree(node.children);
      })
    },
    /* 存储图层右键操作前的树结构 */
    saveTree () {
      const copyTree = _.cloneDeep((this.layerTree.loadedNodes || []));
      copyTree.forEach(node => {
        delete node.tree;
        delete node.parent;
        if (node.children) this.formatCopyrTree(node.children);
      })
      localStorage.setItem('originTree', JSON.stringify(copyTree));
    },
    /* 对图层右键操作后不改变原有树文件夹的展开收起情况 */
    recoverTree () {
      let originTree = localStorage.getItem('originTree');
      originTree = JSON.parse(originTree);
      let i = 0; let j = 0;
      while (i < originTree.length) { // !(j === this.layerTree.loadedNodes.length && i === this.originTree.length)) { // j > i) {
        const tree = this.layerTree.loadedNodes;
        if (tree[j].data.id === originTree[i].data.id && tree[j].data.type === 'group' && originTree[i].collapse === false) {
          tree[j].collapse = originTree[i].collapse;
          this.layerTree.collapseChildNodes(tree[j].data, tree[j].collapse);
          this.recursionChangeCollapse(tree[j].children, originTree[i].children);
          i++;
          j++;
        } else if (tree[j].data.id !== originTree[i].data.id) {
          j++;
        } else if (tree[j].data.id === originTree[i].data.id) {
          i++;
          j++;
        }
      }
    },
    recursionChangeCollapse (tree, originTree) {
      tree.forEach((node, index) => {
        node.collapse = originTree[index].collapse;
        this.layerTree.collapseChildNodes(node.data, node.collapse);
        if (node.children) this.recursionChangeCollapse(node.children, originTree[index].children);
      })
    },
    handleDragOver (e) {
      e.preventDefault();
      const isNode = e.target && e.target.classList.contains('layer-node');
      if (!isNode) return false;
      const id = e.target.dataset.id;
      let index = e.target.dataset.index;
      const rect = e.target.getBoundingClientRect();
      const top = e.clientY - rect.top;
      const next = top > 24;
      const node = this.layerTree.getNodeById(id);

      if (next) index++;
      this._dragAnchorDirection = +next;
      this._dragAnchorNode = node;

      this.layerMoveLineShow = true;
      this.layerMoveLineY = this.isThumbnail ? 48 * index : 32 * index;
    },
    handleDrop (e) {
      e.preventDefault();
      const list = [...this._dragSelectDatas]

      let anchorNode = this._dragAnchorNode
      if (anchorNode.data.type === 'group') {
        // 如果锚点是展开的组，且插入到它后面。则应该把插入到它的子节点中
        if (!anchorNode.collapse && this._dragAnchorDirection === 1) {
          anchorNode = anchorNode.children[0];
        }

        if (anchorNode.parent.data.id !== 'root') {
          list.push(anchorNode.data)
        }
      } else {
        if (anchorNode.parent.data.id !== 'root') {
          list.push(anchorNode.data)
        }
      }
      const hasScene = list.some((item) => {
        return !item.pageId
      })
      const hasScenePage = list.some((item) => {
        return !!item.pageId
      })
      if (hasScene && hasScenePage) {
        this.$message.warn('不同页面组件不支持分组')
        this.canUpdateScreenLayers = false
        return
      }

      this.canUpdateScreenLayers = true

      const insertArr = this.layerTree.dragInsert(
        this._dragAnchorNode,
        this._dragAnchorDirection,
        this._dragSelectDatas
      );

      this._insertArr = insertArr || []
    },
    handleClick (node) {
      this.$refs.contextmenu.hide();
      this.layerTree.clearSelect();
      this.layerTree.select(node.data.id);
      this.shiftNode = node;
      if (node.data.type === 'group' && this.screenInfo.type === 'mobile') {
        canvasBus.emit('select_node', node.data.comId);
        return
      }
      canvasBus.emit('select_node', node.data.id);
    },
    handleCollapseClick (node) {
      node.collapse = !node.collapse;
      this.layerTree.collapseChildNodes(node.data, node.collapse);
    },
    handleClickCtrl (node) {
      this.layerTree.select(node.data.id, 1);
      this.shiftNode = node;
      canvasBus.emit('select_node', this.getSelectedIds());
    },
    handleClickShift (node) {
      this.layerTree.select(
        node.data.id,
        2,
        this.shiftNode && this.shiftNode.data
      );
      this.shiftNode = node;
      canvasBus.emit('select_node', this.getSelectedIds());
    },
    handleIsThumbnailIcon (id) {
      if (!this.getComDataById(id)) return;
      const type = this.getComDataById(id).comType;
      if (!id.includes('groups_')) {
        const comList = compPanelList[type.split('-')[0]];
        if (comList) {
          if (comList.length > 1) {
            const iconObj = comList.filter((item) => {
              return item.type === type.split('-').slice(0, 2).join('-');
            });
            return iconObj[0].icon;
          } else {
            const iconObj = comList.filter((item) => {
              return item.type.split('-')[0] === type.split('-')[0];
            });
            return iconObj[0].thumIcon;
          }
        }
      }
    },
    getSelectedIds () {
      return this.layerTree.getSelectedNodes().map((n) => n.data.id);
    },
    findGroupLock (node) {
      if (!node.children) return;
      node.children.forEach((item) => {
        if (item.children) {
          this.findGroupLock(item);
        } else {
          if (!this.groupLock) this.groupLock = []
          this.groupLock.push(item);
        }
      });
    },
    compLock (node) {
      this.findGroupLock(node)
      const lock = this.groupLock.map((item) => {
        return !!(this.getComDataById(item.data.id)?.attr?.lock);
      });
      this.groupLock = [];
      return !lock.includes(false);
    },
    findGroupShow (node) {
      if (!node.children) return;
      node.children.forEach((item) => {
        if (item.children) {
          this.findGroupShow(item);
        } else {
          if (!this.groupShowList) this.groupShowList = []
          this.groupShowList.push(item);
        }
      });
    },
    compShow (node) {
      this.findGroupShow(node)
      const show = this.groupShowList.map((item) => {
        return !!(this.getComDataById(item.data.id)?.show);
      });
      this.groupShowList = [];
      return !!show.includes(true);
    }
  }
};
</script>

<style scoped lang="scss">
.layer-tree {
  .layer-tree-list {
    background: #1d2127;
    user-select: none;
    position: relative;
    cursor: pointer;
    .layer-node {
      width: 100%;
      height: 48px;
      background: transparent; //#1b1f25;
      color: white;
      padding-right: 6px;
      display: flex;
      align-items: center;
      position: relative;
      .layer-right-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow: hidden;
        .layer-name {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .layer-thumbail-item {
          font-size: 14px;
        }
      }
      &:hover {
        background: #8fe1ff1a;
        color: #fff;
        .layer-thumbail-item i {
          color: #fff;
        }
      }
      &.selected {
        background: #2681ff;
        .layer-thumbail-item i {
          color: #fff;
        }
      }
    }
    .layer-node-thumbnail {
      height: 32px;
    }
    .layer-move-to-line {
      height: 2px;
      width: 100%;
      background: #00c1de;
      position: absolute;
      left: 0;
      top: 0;
    }
    .draging-wrap {
      width: 200px;
      position: fixed;
      right: 150%;
    }
  }
  .components-item-img {
    width: 53px;
    height: 34px;
    flex: none;
    display: block;
    border: 1px solid #3a4659;
    background: #282a30;
    background-clip: content-box;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 10px;
    margin-left: 6px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mr-6 {
    margin-right: 6px;
  }

}
.icon {
  transition: transform 0.25s linear;
}
.active {
  transform: rotate(90deg);
}
.contextmenu-disable {
    pointer-events: none;
    opacity: 0.5;
  }
</style>
