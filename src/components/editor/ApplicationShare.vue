<template>
  <div class="app-share-wrapper element-ui-override">
    <el-dialog
      title="应用共享设置"
      :visible="true"
      width="842px"
      top="0"
      :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
      @close="() => { $emit('cancelShare'); }">
      <div class="app-share-target">
        <div class="list-wrapper">
          <share-list mode="share" @change="onChange" :selectedList="defaultSelectedList" ref="shareList"></share-list>
        </div>
        <div class="selected-list">
          <div class="header">
            <span class="main">已选择<div class="tag">{{ filterUserList.length }}</div></span>
            <el-button size="small" type="text" @click="clear">清空</el-button>
          </div>
          <ul class="data-container" v-if="filterUserList.length">
            <li class="selected-item" v-for="item in filterUserList" :key="item.id">
              <span class="item-name">
                <CircleAvatar
                  v-if="item.type === 'user'"
                  :avatarStyle="handleAvatarStyle(item)"
                  :avatarText="handleAvatarText(item.name)" />
                  <div class="avatar" v-else-if="item.type === 'role'" :class="item.role">
                    <hz-icon name="user" class="icon"></hz-icon>
                  </div>
                  <div class="avatar" :class="item.role" v-else>
                    <hz-icon name="organ" class="icon"></hz-icon>
                  </div>
                {{ item.name }}
              </span>
              <el-dropdown
                @visible-change="handleChange($event, item.role)"
                @command="handleCommand($event, item)"
                trigger="click"
              >
                <span class="el-dropdown-link">
                  {{ item.role | filter_formatRole
                  }}<i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <template #dropdown>
                  <el-dropdown-menu class="coedit-dropdown">
                    <el-dropdown-item
                      v-for="(item, index) in dropList"
                      :key="index"
                      :command="item.value"
                      :divided="item.divided"
                      :class="{ selected: selectStatus == item.value }"
                      >{{ item.label }}</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </li>
          </ul>
          <div class="empty" v-else>
            <div class="no-data"></div>
            <p class="info">暂无选择</p>
          </div>
        </div>
        <!-- <div class="user-list-container list-container">
          <MutiSelect
            :userList="userList"
            @selectdUserChange="selectdUserChange"
            :selectdUserList="selectdUserList"
          >
          </MutiSelect>

          <div class="app-creater-wrapper">
            <div>应用创建者</div>
            <div class="app-creater-box">
              <CircleAvatar
                :avatarText="handleAvatarText(createUsername)"
              ></CircleAvatar>
              <span>{{ createUsername || "-" }}</span>
            </div>
          </div>

          <span class="coedit-user">共享者</span>
          <el-scrollbar class="scroll-content" style="flex: 1" v-if="filterUserList.length">
            <li
              class="list-item common"
              v-for="(user, index) in filterUserList"
              :key="index"
            >
              <div class="avatar-name-container">
                <CircleAvatar
                  :avatarStyle="handleAvatarStyle(user)"
                  :avatarText="handleAvatarText(user.userName)"
                ></CircleAvatar>
                <span class="list-content">{{ user.userName }}</span>
              </div>
              <span class="list-content">
                <el-dropdown
                  @visible-change="handleChange($event, user.role)"
                  @command="handleCommand($event, user, index)"
                  trigger="click"
                >
                  <span class="el-dropdown-link">
                    {{ user.role | filter_formatRole
                    }}<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu class="coedit-dropdown">
                      <el-dropdown-item
                        v-for="(item, index) in dropList"
                        :key="index"
                        :command="item.value"
                        :divided="item.divided"
                        :class="{ selected: selectStatus == item.value }"
                        >{{ item.label }}</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </span>
            </li>

            <div class="empty-info" v-if="!filterUserList.length">
              <span class="empty-text">暂无数据</span>
            </div>
          </el-scrollbar>
        </div> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="poper-cancel" size="mini" @click="$emit('cancelShare')">取消</el-button>
        <el-button type="primary" size="mini" @click="saveCoeditScreen" :loading="saveLoading">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import { getShareUser } from '@/api/user';
import { saveCoeditScreen } from '@/api/screencoedit';
import CircleAvatar from '@/components/editor/CircleAvatar';
import ShareList from './ShareList';
export default {
  name: 'ApplicationShare',
  components: {
    CircleAvatar,
    ShareList
  },
  props: {
    currScreenInfo: {
      type: Object,
      default: () => {},
      require: true
    }
  },
  data () {
    return {
      selectStatus: '', // 下拉菜单当前选中项
      dropList: [
        // 下拉菜单
        {
          label: '协作者',
          value: 'collaborators',
          divided: false
        },
        {
          label: '查看者',
          value: 'viewers',
          divided: false
        },
        {
          label: '删除',
          value: 'delete',
          divided: true
        }
      ],
      dialogVisible: false,
      activeTab: 'user',
      userQuery: '',
      createUsername: '', // 当前创建协作大屏的用户名
      createUserId: '', // 当前创建协作大屏的用户id
      selectdUserList: [],
      defaultSelectedList: [], // 默认选中用户列表
      appType: '', // 应用状态，新建 create，更新 update
      saveLoading: false // loading
    };
  },

  filters: {
    filter_formatRole (role) {
      const roleMap = {
        viewers: '查看者',
        collaborators: '协作者'
      };
      return roleMap[role];
    }
  },
  created () {
    const coeditInfo = this.currScreenInfo?.coeditInfo ?? null;
    const shareCollection = this.currScreenInfo?.shareCollection ?? null;
    this.createUsername = localStorage.getItem('name') ?? '-';
    this.appType = coeditInfo ? 'update' : 'new';
    // this.getShareUser(this.currScreenInfo);
    // 获取协同相关数据
    this.handleCoeditRelated(shareCollection);
  },
  computed: {
    userId () {
      return this.$store.state.user.id;
    },
    // 包含已存库的用户和待添加的用户列表
    filterUserList: {
      get: function () {
        // 已被选中的用户列表
        // const userNameList = this.getUserNameById();
        return this.selectdUserList;
      },
      // setter
      set: function ({ user, command }) {
        // 设置项在已入库列表
        this.selectdUserList.forEach((item, index) => {
          if (item.id === user.id) {
            if (command === 'delete') {
              this.$refs.shareList.unselect(item)
            } else {
              this.$set(this.selectdUserList, index, {
                ...item,
                role: command
              });
            }
          }
        });
      }
    }
  },
  methods: {
    handleAvatarText (userName) {
      return userName.slice(0, 1).toUpperCase();
    },
    // 设置下拉菜单默认显示项
    handleChange (flag, role) {
      if (flag) {
        if (role !== 'delete') {
          this.selectStatus = role;
        }
      }
    },
    handleCommand (command, user) {
      this.filterUserList = {
        command,
        user
      };
    },
    // 处理头像样式
    handleAvatarStyle (user) {
      if (!user) return {};
      const { role } = user;
      return {
        fontSize: 12,
        color: '#fff',
        width: 20,
        height: 20,
        borderRadius: 10,
        bgc: role === 'viewers' ? '#1F71FF' : '#FF9431'
      };
    },
    // 处理协同数据
    handleCoeditRelated (shareCollection = {}) {
      if (!shareCollection || Object.keys(shareCollection).length === 0) return;
      const coeditUsers = shareCollection?.userList ?? [];
      const coeditRoles = shareCollection?.roleList ?? [];
      const coeditGroups = shareCollection?.groupList ?? [];
      this.defaultSelectedList = coeditUsers.map((item) => {
        return {
          ...item,
          id: item.userId,
          name: item.userName,
          type: 'user'
        };
      }).concat(coeditRoles.map((item) => {
        return {
          ...item,
          id: item.role_id,
          name: item.role_name,
          type: 'role'
        };
      })).concat(coeditGroups.map((item) => {
        return {
          ...item,
          id: item.group_id,
          name: item.group_name,
          type: 'group'
        };
      }));
      this.selectdUserList = this.defaultSelectedList
    },
    // 根据userid去全量用户列表反查username
    // getUserNameById () {
    //   const userMap = {};
    //   this.selectdUserList.map((item) => {
    //     if (!userMap[item.id]) {
    //       userMap[item.id] = item;
    //     }
    //   });
    //   const userNameList = this.selectdUserList.map((item) => {
    //     if (userMap[item.userId]) {
    //       return {
    //         ...item,
    //         userName: userMap[item.userId].userName
    //       };
    //     }
    //   });
    //   return userNameList;
    // },
    // 新建/更新共享大屏
    async saveCoeditScreen () {
      if (this.saveLoading) return;
      const { id = null } = this.currScreenInfo;
      if (!id) {
        throw new Error('大屏id为空');
      }
      const coeditInfoParams = {
        coeditScreenId: id, // '分享的大屏id'
        userList: this.filterUserList.filter(item => item.type === 'user').map(item => {
          return {
            userId: item.id,
            userName: item.name,
            role: item.role
          }
        }), // 协同列表
        roleList: this.filterUserList.filter(item => item.type === 'role').map(item => {
          return {
            role_id: item.id,
            role_name: item.name,
            role: item.role
          }
        }),
        groupList: this.filterUserList.filter(item => item.type === 'group').map(item => {
          return {
            group_id: item.id,
            group_name: item.name,
            role: item.role
          }
        })
      };
      if (this.appType === 'update') {
        const createUserId = this.currScreenInfo?.coeditInfo?.createUserId;
        const coeditId = this.currScreenInfo?.coeditId;
        coeditInfoParams.createUserId = createUserId;
        coeditInfoParams.coeditId = coeditId;
      } else {
        if (this.filterUserList.length === 0) {
          return this.$message.warn('请添加共享者');
        }
      }
      this.saveLoading = true
      try {
        const res = await saveCoeditScreen(coeditInfoParams);
        if (res.success) {
          this.$message.success('操作成功');
          this.$emit('refreshData');
        } else {
          this.$message.warn(res.message);
        }
        this.saveLoading = false
      } catch (error) {
        this.saveLoading = false
        throw new Error(error);
      }
    },
    // 获取共享用户列表
    // async getShareUser () {
    //   const res = await getShareUser();
    //   if (res.success) {
    //     const userId = localStorage.getItem('userId'); // 当前登录用户id
    //     // 共享列表不包含当前登录用户
    //     this.userList = res.data.filter((item) => item.userId !== userId);
    //   }
    // },
    // 获取待添加的共享用户列表
    onChange (list = [], type) {
      let userList = this.selectdUserList.filter(user => user.type === 'user')
      let roleList = this.selectdUserList.filter(user => user.type === 'role')
      let groupList = this.selectdUserList.filter(user => user.type === 'group')
      if (type === 'user') {
        userList = list.map(item => {
          const selectedItem = this.selectdUserList.find(user => user.id && user.id === item.userId)
          let oItem = item
          if (selectedItem) {
            oItem = {
              ...item,
              ...selectedItem
            }
          } else {
            oItem.id = oItem.userId
            oItem.name = oItem.userName
            oItem.role = 'viewers'
            oItem.type = 'user'
          }
          return oItem
        })
      } else if (type === 'role') {
        roleList = list.map(item => {
          const selectedItem = this.selectdUserList.find(user => user.id && user.id === item.role_id)
          let oItem = item
          if (selectedItem) {
            oItem = {
              ...item,
              ...selectedItem
            }
          } else {
            oItem.id = oItem.role_id
            oItem.name = oItem.role_name
            oItem.role = 'viewers'
            oItem.type = 'role'
          }
          return oItem
        })
      } else if (type === 'group') {
        groupList = list.map(item => {
          const selectedItem = this.selectdUserList.find(user => user.id && user.id === item.group_id)
          let oItem = item
          if (selectedItem) {
            oItem = {
              ...item,
              ...selectedItem
            }
          } else {
            oItem.id = oItem.group_id
            oItem.name = oItem.group_name
            oItem.role = 'viewers'
            oItem.type = 'group'
          }
          return oItem
        })
      } else {
        this.selectdUserList = []
        return
      }
      this.selectdUserList = userList.concat(roleList).concat(groupList)
    },
    clear () {
      this.$refs.shareList.unselect(null, true)
    }
  }
};
</script>

<style lang="scss" scoped>
.tag {
  display: inline-block;
  vertical-align: top;
  background-color: var(--seatom-mono-a200);
  color: var(--seatom-type-800);
  line-height: 14px;
  font-size: 10px;
  border-radius: 4px;
  font-weight: 400;
  width: 20px;
  text-align: center;
  margin-left: 5px;
  padding: 2px 0;
  margin-top: 1px;
}
.app-share-wrapper {
  .app-share-target {
    display: flex;
    .list-wrapper {
      width: 364px;
      border-right: 1px solid var(--seatom-mono-a300);
    }
    .empty {
      width: 192px;
      margin: 0 auto;
      margin-top: 50px;
      .no-data {
        height: 192px;
        background: var(--seatom-noSelect-bg) no-repeat top / cover;;
      }
      .info {
        text-align: center;
        color: var(--seatom-type-700);
      }
    }
    .selected-list {
      flex: 1;
      height: auto;
      max-height: 500px;
      overflow: auto;
      background-color: var(--seatom-mono-200);
      padding: 16px 24px;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .main {
          display: inline-block;
          font-weight: 600;
        }
      }
      .data-container {
        margin-top: 16px;
        .selected-item {
          height: 40px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .item-name {
            display: flex;
            gap: 5px;
            align-items: center;
            .avatar {
              display: flex;
              width: 20px;
              height: 20px;
              padding: 4px;
              justify-content: center;
              align-items: center;
              flex-shrink: 0;
              border-radius: 1000px;
              background: var(--seatom-orange-a100);
              .icon {
                color: var(--seatom-orange-900);
              }
              &.viewers {
                background: var(--seatom-primary-a100);
                .icon {
                  color: var(--seatom-primary-900);
                }
              }
            }
          }
        }
      }
    }
    .list-item {
      color: #fff;
      list-style: none;
      padding: 10px 8px;
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      &:hover {
        background-color: rgba(61, 133, 255, 0.1);
        .delete-icon {
          opacity: 1;
        }
      }
    }
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .app-creater-wrapper {
    font-size: 14px;
    color: var(--seatom-type-800);
    font-weight: 600;
    line-height: 26px;
    padding-top: 8px;
    .app-creater-box {
      display: flex;
      align-items: center;
      padding: 10px 8px;
      & > span {
        margin-left: 4px;
      }
    }
  }
  .coedit-user {
    font-size: 14px;
    color: var(--seatom-type-800);
    font-weight: 600;
  }
  .delete-icon {
    opacity: 0;
  }
  .dmc-user-list {
    height: 450px;
  }
  .el-dropdown {
    color: var(--seatom-type-800);
  }
}
::v-deep .el-input-group__append {
  width: 100px;
  border: 0;
}
.selected {
  background-color: var(--seatom-mono-a100);
}
.avatar-name-container {
  display: flex;
  & > span {
    margin-left: 4px;
  }
}
</style>
<style lang="scss">
.coedit-dropdown {
  padding: 8px;
  color: var(--seatom-type-800);
  background-color: var(--seatom-background-300);
  border: none;
  box-shadow: var(--seatom-container-c300);
  .el-dropdown-menu__item {
    width: auto;
    padding: 5px 16px;
    border-radius: 4px;
    color: var(--seatom-type-800);
    line-height: 22px;
  }
  &.el-popper[x-placement^="bottom"] .popper__arrow {
    border: none;
    display: none;
  }
  &.el-popper[x-placement^="bottom"] .popper__arrow::after {
    border: none;
  }
  .el-dropdown-menu__item:focus,
  .el-dropdown-menu__item:not(.is-disabled):hover {
    color: var(--seatom-type-800);
    background-color: var(--seatom-mono-a100);
  }
  .el-dropdown-menu__item--divided {
    border-color: var(--seatom-mono-a300);
    border-radius: 0;
  }
  .el-dropdown-menu__item--divided:before {
    background: transparent;
    height: 0;
  }
  // .el-dropdown-menu__item--divided {
  //   border-top: 1px solid var(--seatom-mono-a100);
  // }
}
</style>
