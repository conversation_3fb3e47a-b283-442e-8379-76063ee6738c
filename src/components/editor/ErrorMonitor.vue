<template>
  <div class="error-monitor">
    <div class="error-monitor-header">
      <span>异常监控</span>
      <i class="icon el-icon-close" @click="closeDataErrorMonitor()"></i>
    </div>
    <p class="help-info">
      <span v-if="dataMappingErrorComps.length"
        >以下组件存在异常，请及时处理！</span
      >
      <span v-else>暂无组件异常</span>
    </p>
    <ul class="error-monitor-container">
      <el-scrollbar style="height: 100%">
        <li
          v-for="comp in dataMappingErrorComps"
          :key="'mapping_' + comp.id"
          class="error-monitor-list"
          :class="{ selected: currentSelectId === comp.id }"
          @click="locateComp(comp.id)"
        >
          <div class="error-item-main">
            <div
              class="components-item-img"
              :style="{
                backgroundImage: getComDataById(comp.id)
                  ? `url(${replaceUrl(getComDataById(comp.id).icon)})`
                  : null,
              }"
            ></div>
            <span>{{
              getComDataById(comp.id) && getComDataById(comp.id).alias
            }}</span>
          </div>
          <span class="error-type">
            <hz-icon
              class="mr-10 info-icon"
              :name="getErrorTypeIcon().name"
            ></hz-icon>
            <span class="error-text">{{ getErrorTypeIcon().text }}</span>
          </span>
        </li>
        <li
          v-for="comp in errorComps"
          :key="'error_' + comp.id"
          class="error-monitor-list"
          :class="{ selected: currentSelectId === comp.id }"
          @click="showError(comp.id)"
        >
          <div class="error-item-main">
            <div
              class="components-item-img"
              :style="{
                backgroundImage: getComDataById(comp.id)
                  ? `url(${replaceUrl(getComDataById(comp.id).icon)})`
                  : null,
              }"
            ></div>
            <span>{{
              getComDataById(comp.id) && getComDataById(comp.id).alias
            }}</span>
          </div>
          <span class="error-type">
            <hz-icon
              class="mr-10 info-icon"
              :name="getErrorTypeIcon('error').name"
            ></hz-icon>
            <span class="error-text">{{
              getErrorTypeIcon("error").text
            }}</span>
          </span>
        </li>
      </el-scrollbar>
    </ul>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { replaceUrl } from '@/utils/base';
import canvasBus from '@/utils/canvasBus';
import emitter from '@/utils/bus';
export default {
  name: 'ErrorMonitor',
  inject: ['getLayerTree'],
  data () {
    return {
      replaceUrl
    }
  },
  computed: {
    ...mapState({
      currentSelectId: (state) => state.editor.currentSelectId
    }),
    ...mapGetters('editor', [
      'getComDataById',
      'dataMappingErrorComps',
      'errorComps'
    ]),
    layerTree () {
      return this.getLayerTree();
    }
  },
  methods: {
    closeDataErrorMonitor () {
      this.$store.commit('editor/updateDrawerSelect', {
        type: 'abnormal',
        value: false
      });
    },
    locateComp (id) {
      // 定位组件
      this.layerTree.select(id);
      canvasBus.emit('select_node', id);
      this.$nextTick(() => {
        emitter.emit('jumpToData');
      });
    },
    getErrorTypeIcon (type = 'mapping') {
      // 获取组件的错误类型，预留方法，后续扩展使用，目前只有数据源错误
      if (type === 'mapping') {
        return {
          name: 'abnormal-info',
          text: '数据源配置错误'
        };
      } else {
        return {
          name: 'puzzle',
          text: '组件升级错误'
        };
      }
    },
    showError (id) {
      // 定位组件并弹出错误
      const { errorInfo } =
        this.errorCompIds.find((item) => item.componentId === id) || {};
      const h = this.$createElement;
      const vm = this;
      this.layerTree.select(id);
      canvasBus.emit('select_node', id);
      this.$nextTick(() => {
        this.$msgbox({
          title: '错误信息',
          confirmButtonText: '确定',
          customClass: 'error-info-container',
          message: h(
            'div',
            {
              class: 'error-info-box'
            },
            [
              h(
                'p',
                {
                  class: 'error-info-header'
                },
                [
                  errorInfo.message,
                  h('i', {
                    class: 'el-icon-arrow-up icon',
                    on: {
                      click: function (e) {
                        vm.showDetailError = !vm.showDetailError;
                        const icon = e.target;
                        const stack =
                          document.getElementsByClassName(
                            'error-info-stack'
                          )[0];
                        if (vm.showDetailError) {
                          icon.className += ' rotate';
                          stack && (stack.className += ' show');
                        } else {
                          icon.className = icon.className.replace(/rotate/, '');
                          stack &&
                            (stack.className = stack.className.replace(
                              /show/,
                              ''
                            ));
                        }
                      }
                    }
                  })
                ]
              ),
              h(
                'div',
                {
                  class: 'error-info-stack'
                },
                errorInfo.stack
              )
            ]
          )
        }).catch((e) => {});
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.error-monitor {
  .error-monitor-header {
    box-sizing: border-box;
    padding: 0 10px;
    line-height: 42px;
    font-size: 16px;
    font-weight: 600;
    background: rgba(46, 52, 60, 0.5);
    color: rgba(255, 255, 255, 0.5);
    display: flex;
    justify-content: space-between;
    align-items: center;
    .icon {
      font-size: 14px;
      cursor: pointer;
    }
  }
  .help-info {
    text-align: center;
    font-size: 12px;
    background: rgba(46, 52, 60, 0.5);
    color: #7f8183;
    line-height: 42px;
    margin-top: 1px;
  }
  .error-monitor-container {
    flex: 1;
    .error-monitor-list {
      width: 100%;
      height: 60px;
      background: #13161a;
      box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25);
      color: rgba(255, 255, 255, 0.5);
      padding-right: 6px;
      padding-left: 8px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      .error-item-main {
        display: flex;
        align-items: center;
        .components-item-img {
          width: 60px;
          height: 40px;
          flex: none;
          display: block;
          border: 1px solid #5292ff;
          background: #1f2125;
          background-clip: content-box;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          margin-right: 10px;
        }
      }
      .error-type {
        color: #ff475d;
        font-size: 12px;
        .error-text {
          display: none;
          margin-left: 5px;
        }
      }
      .info-icon {
        font-size: 14px;
      }
      &:hover {
        background: #1f2430;
        color: #fff;
        .layer-thumbail-item i {
          color: #fff;
        }
        .error-text {
          display: inline;
        }
      }
      &.selected {
        background: #2681ff;
        color: #fff;
        .layer-thumbail-item i {
          color: #fff;
        }
        .error-text {
          display: inline;
        }
      }
    }
  }
}
</style>
