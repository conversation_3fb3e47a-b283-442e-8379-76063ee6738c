<template>
  <div class="filter-btn" v-clickoutside="close">
    <div class="--label" :class="{active: show}" @click="toggle">滤镜配置</div>
    <el-switch v-model="enable" size="mini" @change="(val) => valueChange(val, 'enable')"></el-switch>
    <div class="drop-pop" v-show="show">
      <div class="content">
        <el-form label-width="60px" label-position="left" size="mini">
          <el-form-item label="色相">
            <div class="number-range">
              <el-slider
                v-model="hue"
                :min="0"
                :max="360"
                :step="1"
                @change="(val) => valueChange(val, 'hue')">
              </el-slider>
              <el-input-number
                v-model="hue"
                controls-position="right"
                :min="0"
                :max="360"
                size="mini"
                :step="1"
                id="hue"
                @change="(val) => valueChange(val, 'hue')">
              </el-input-number>
            </div>
          </el-form-item>
          <el-form-item label="饱和度">
            <div class="number-range">
              <el-slider
                v-model="saturate"
                :min="0"
                :max="200"
                :step="1"
                @change="(val) => valueChange(val, 'saturate')">
              </el-slider>
              <el-input-number
                v-model="saturate"
                controls-position="right"
                :min="0"
                :max="200"
                size="mini"
                :step="1"
                id="saturate"
                @change="(val) => valueChange(val, 'saturate')">
              </el-input-number>
            </div>
          </el-form-item>
          <el-form-item label="亮度">
            <div class="number-range">
              <el-slider
                v-model="brightness"
                :min="0"
                :max="200"
                :step="1"
                @change="(val) => valueChange(val, 'brightness')">
              </el-slider>
              <el-input-number
                v-model="brightness"
                controls-position="right"
                :min="0"
                :max="200"
                size="mini"
                :step="1"
                id="brightness"
                @change="(val) => valueChange(val, 'brightness')">
              </el-input-number>
            </div>
          </el-form-item>
          <el-form-item label="对比度">
            <div class="number-range">
              <el-slider
                v-model="contrast"
                :min="0"
                :max="200"
                :step="1"
                @change="(val) => valueChange(val, 'contrast')">
              </el-slider>
              <el-input-number
                v-model="contrast"
                controls-position="right"
                :min="0"
                :max="200"
                size="mini"
                :step="1"
                id="contrast"
                @change="(val) => valueChange(val, 'contrast')">
              </el-input-number>
            </div>
          </el-form-item>
          <el-form-item label="透明度">
            <div class="number-range">
              <el-slider
                v-model="opacity"
                :min="0"
                :max="100"
                :step="1"
                @change="(val) => valueChange(val, 'opacity')">
              </el-slider>
              <el-input-number
                v-model="opacity"
                controls-position="right"
                :min="0"
                :max="100"
                size="mini"
                :step="1"
                id="opacity"
                @change="(val) => valueChange(val, 'opacity')">
              </el-input-number>
            </div>
          </el-form-item>
          <el-form-item label="灰度" style="margin-bottom:0;">
            <div class="number-range">
              <el-slider
                v-model="grayscale"
                :min="0"
                :max="100"
                :step="1"
                @change="(val) => valueChange(val, 'grayscale')">
              </el-slider>
              <el-input-number
                v-model="grayscale"
                controls-position="right"
                :min="0"
                :max="100"
                size="mini"
                :step="1"
                id="grayscale"
                @change="(val) => valueChange(val, 'grayscale')">
              </el-input-number>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'ColorSetting', // 滤镜配置
  data () {
    return {
      show: false,
      value: 0,
      enable: false,
      hue: 0,
      saturate: 100,
      brightness: 100,
      contrast: 100,
      opacity: 100,
      grayscale: 0
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      loaded: state => state.editor.loaded
    })
  },
  watch: {
    loaded: {
      handler: function (val) {
        if (val) {
          const { globalFilterParams } = this.screenInfo.config;
          this.enable = globalFilterParams.enable;
          this.hue = globalFilterParams.hue;
          this.saturate = globalFilterParams.saturate;
          this.brightness = globalFilterParams.brightness;
          this.contrast = globalFilterParams.contrast;
          this.opacity = globalFilterParams.opacity;
          this.grayscale = globalFilterParams.grayscale;
        }
      },
      immediate: true
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.addSuffix('hue', '度');
      this.addSuffix('saturate', '%');
      this.addSuffix('brightness', '%');
      this.addSuffix('contrast', '%');
      this.addSuffix('opacity', '%');
      this.addSuffix('grayscale', '%');
    })
  },
  methods: {
    toggle () {
      this.show = !this.show;
    },
    close () {
      if (this.show) {
        this.show = false;
      }
    },
    valueChange (val, key) {
      if (key === 'enable') {
        if (val) {
          this.show = true;
        } else {
          this.show = false;
        }
      }
      this.$store.dispatch('editor/updateScreenInfo', [{ key: `config.globalFilterParams.${key}`, value: val }]);
    },
    addSuffix (eleId, str) {
      const span = document.createElement('span');
      const innerspan = document.createElement('span');
      const textspan = document.createElement('span');
      // 添加elementUI 内置 class
      span.setAttribute('class', 'el-input__suffix');
      innerspan.setAttribute('class', 'el-input__suffix-inner');

      span.append(innerspan);
      innerspan.append(textspan);
      textspan.append(str);

      this.$nextTick(() => {
        if (document.getElementById(eleId)) {
          document.getElementById(eleId).lastElementChild.prepend(span);
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
  .filter-btn {
    position: relative;
    display: flex;
    align-items: center;
    color: #ddd;
  }
  .--label {
    cursor: pointer;
    margin-right: 5px;
    &:hover, &.active {
      color: #2681ff;
      border-bottom: 1px solid #2681ff;
    }
  }
  .drop-pop {
    position: absolute;
    top: 25px;
    left: 0;
    width: 320px;
    background: #1d2125;
    .content {
      padding: 12px;
    }
  }
  .number-range {
    display: flex;
    align-items: center;
    height: 24px;
    ::v-deep .el-slider {
      flex: 1;
      margin-right: 15px;
      .el-slider__runway {
        margin: 4px 0;
      }
      .show-range-div {
        width: 100%;
        height: 12px;
        line-height: 12px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        color: #b2b2b2;
      }
      .show-range-div-current {
        width: 100%;
        height: 12px;
        line-height: 12px;
        display: flex;
        margin-bottom: 4px;
        color: #b2b2b2;
        justify-content: space-around;
      }
    }
    ::v-deep .el-input-number--mini {
      width: 80px;
      .el-input__inner {
        padding-left: 2px;
        padding-right: 40px;
      }
      .el-input__suffix {
        right: 32px;
      }
    }
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
      color: #fff;
    }
  }
</style>
