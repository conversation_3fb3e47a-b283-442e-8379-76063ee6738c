<template>
  <el-dialog
    class="save-as-theme"
    :visible.sync="show"
    width="500px"
    title="保存主题"
    :close-on-click-modal="false"
    top="0"
    append-to-body
    :before-close="closeDialog">
    <div class="form-content">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="主题名称" prop="name">
          <el-input v-model="form.name" size="mini"></el-input>
        </el-form-item>
        <el-form-item label="主题类型" prop="type">
          <el-select v-model="form.type" clearable size="mini" style="width: 100%">
            <el-option label="样式主题" value="style"></el-option>
            <el-option label="功能主题" value="function"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="text" @click="submit">确定</el-button>
      <el-button type="text" @click="closeDialog">取消</el-button>
    </div>
    <seatom-loading v-if="loading"></seatom-loading>
  </el-dialog>
</template>

<script>
import { saveComtheme } from '@/api/theme';
import { mapGetters } from 'vuex'
import emitter from '@/utils/bus';
export default {
  name: 'SaveAsTheme', // 保存为主题
  data () {
    return {
      show: false,
      form: {
        name: '',
        type: ''
      },
      rules: {
        name: [
          { required: true, message: '主题名称必填', trigger: 'change' }
        ],
        type: [
          { required: true, message: '主题类型必选', trigger: 'change' }
        ]
      },
      loading: false
    }
  },
  computed: {
    ...mapGetters('editor', ['currentCom'])
  },
  methods: {
    showDialog () {
      this.show = true;
    },
    closeDialog () {
      this.resetForm();
      this.show = false;
    },
    resetForm () {
      this.$refs.form.resetFields();
    },
    submit () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const data = {
            comId: this.currentCom.id,
            name: this.form.name,
            themeType: this.form.type
          }
          this.loading = true;
          const res = await saveComtheme(data);
          if (res && res.success) {
            this.$store.dispatch('editor/updateScreenCom', {
              id: this.currentCom.id,
              keyValPairs: [
                {
                  key: 'other.themeUrl',
                  value: res.data.icon
                },
                {
                  key: 'other.comThemeId',
                  value: res.data.id
                }
              ]
            }).then(() => {
              this.$message.success('保存成功');
              this.closeDialog();
              emitter.emit('refreshTheme');
            })
          } else if (res.code === 400) {
            this.$message.warn(res.message)
          }
          this.loading = false;
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.save-as-theme {}
</style>
