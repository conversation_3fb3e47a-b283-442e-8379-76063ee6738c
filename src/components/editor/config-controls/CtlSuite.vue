<template>
<div class="config-control">
  <i class="config-icon"
    :class="[isShow ? 'icon-visible' : 'icon-hide']"
    v-if="node.data.enableHide"
    @click="isShow = !isShow;"></i>
  <!-- <i class="config-icon config-placeholder" v-if="!node.data.enableHide && node.depth <= 1"></i> -->
  <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
  <div class="width-div">
    <ConfigNode
      v-for="child in node.children"
      :key="child.id"
      :node="child"
      :configObj="configObj"
    >
    </ConfigNode>
  </div>
</div>
</template>

<script>
export default {
  name: 'CtlSuite',
  data () {
    return {
      isShow: true
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }

  ::v-deep .width-div {
    .config-icon {
      display: none;
    }
    .config-placeholder {
      display: none;
    }
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 16px 0;
    }
  }
}
</style>
