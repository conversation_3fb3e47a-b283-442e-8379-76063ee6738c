<template>
  <div class="config-control-box">
    <div class="config-control-item" v-if="itemShow('url')">
      <div class="item-label">内容</div>
      <div class="image-item">
        <div class="image-operation">
          <span class="texts" @click="delIcon">删除</span>
        </div>
        <div class="image-upload">
          <div class="avatar-uploader">
            <el-button
              class="upload-texts"
              type="primary"
              size="mini"
              @click="openDialog()"
              >替换</el-button
            >
          </div>
        </div>
        <svg
          class="hz-icon"
          aria-hidden="true"
          :key="time"
          :class="{'svg-reverse': reverse}"
        >
          <use :xlink:href="iconConfig.url"></use>
        </svg>
      </div>
    </div>
    <div class="config-control-item" v-if="itemShow('size')">
      <div class="item-label">大小</div>
      <div class="item-select">
        <el-input-number
          size="mini"
          controls-position="right"
          v-model="iconConfig.size"
          @change="value=>handleChange({path: 'size',value})"
        >
        </el-input-number>
      </div>
    </div>
    <div class="config-control-item" v-if="itemShow('rotate')">
      <div class="item-label">旋转</div>
      <div class="item-select">
        <el-input-number
          size="mini"
          controls-position="right"
          v-model="iconConfig.rotate"
          :step="1"
          :min="-360"
          :max="360"
          @change="value=>handleChange({path: 'rotate',value})"
        >
        </el-input-number>
      </div>
    </div>
    <div class="config-control-item" v-if="itemShow('color')">
      <div class="item-label">颜色</div>
      <div class="item-select">
        <el-color-picker
          v-model="iconConfig.color"
          show-alpha
          size="mini"
          @change="value=>handleChange({path: 'color',value})"
        >
        </el-color-picker>
      </div>
    </div>

    <select-resources-dialog
      v-if="isResourcesDialog"
      :isOpenDialog="isResourcesDialog"
      :resourceType="resourceType"
      :openFolderName="locateFolderName"
      :selectImgUrl="value.url"
      @selectedFile="selectedFile"
      @closeRescourcesDialog="closeDialog"
      />
  </div>
</template>

<script>
import SelectResourcesDialog from '@/components/resources-control/SelectResourcesDialog';
import { uploadScreenIcon } from '@/api/common';
import { replaceUrl } from '@/utils/base'
import FONT_ICON_OPTIONS from '@/common/fontIconOptions'
import { mapState } from 'vuex';
export default {
  name: 'CtlIcon',
  components: {
    SelectResourcesDialog
  },
  data () {
    return {
      // 打开上传弹窗
      isResourcesDialog: false,
      // 资源类型
      resourceType: 'icon',
      // 定位到某个文件夹
      locateFolderName: '',
      iconConfig: {
        size: '',
        color: '',
        url: '',
        rotate: 0
      },
      components: ['size', 'color', 'url', 'rotate'],
      time: '',
      baseOptions: FONT_ICON_OPTIONS,
      customOptions: [],
      reverse: false,
      initUrl: ''
    };
  },
  mounted () {
    this.getDefaultConfig()
  },
  created () {
    // 定位文件夹
    this.locateFolderName = this.node.data.locateFolderName || '默认文件夹';
    if (this.node.data.components) {
      this.components = this.node.data.components;
    }
  },
  updated () {
  },
  computed: {
    ...mapState({
      screenInfo: (state) => state.editor.screenInfo
    }),
    itemShow () {
      return (name) => {
        return this.components.includes(name)
      }
    }
  },
  methods: {
    delIcon () {
      this.iconConfig.url = '';
      this.handleChange({
        path: 'url',
        value: ''
      })
    },
    openDialog () {
      this.isResourcesDialog = true;
    },
    closeDialog () {
      this.isResourcesDialog = false;
    },
    // 选择图片后执行
    async selectedFile ({ resourceUrl, description = 'common' }, changeColor = false) {
      if (description === 'merge') {
        this.reverse = true
      }
      // const param = {
      //   screenId: this.screenInfo.id,
      //   resourceUrl: data.resourceUrl
      // }
      this.handleChange({ path: 'url', value: resourceUrl })
      this.initUrl = resourceUrl
      const param = {
        resourceUrlList: [resourceUrl],
        changeColor,
        screenId: this.screenInfo.id
      }
      try {
        const res = await uploadScreenIcon(param)
        if (res && res.success) {
          this.iconConfig.url = replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.mergeSvgPath + `?_t=${Date.now()}` + '#' + res.data.svgId[0])
          this.handleChange({ path: 'url', value: this.iconConfig.url })
          this.time = Date.now();
        }
      } catch (error) {
      }
    },
    getDefaultConfig () {
      setTimeout(() => {
        const { color, size, url, rotate, defaultImage } = this.value || {}
        this.iconConfig.color = color
        this.iconConfig.size = size
        this.iconConfig.url = url
        this.iconConfig.rotate = rotate
        this.initUrl = defaultImage
        this.selectedFile({ resourceUrl: this.initUrl }, true)
      }, 900);
    },
    handleChange ({ path, value }) {
      if (path === 'color') {
        this.selectedFile({ resourceUrl: this.initUrl }, true)
      }
      const pathArr = path.split('.');
      const tree = this.node && this.node.tree;
      if (tree) {
        const upperPaths = tree.valuePathMap[this.node.id];
        this.dispatch('ConfigTree', 'change', {
          path: upperPaths.concat(pathArr).join('.'),
          value
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.config-control-box {
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
  .config-control-item {
    display: flex;
    width: 100%;
    position: relative;
    padding: 4px 8px;
    align-items: center;
    color: #fafafa;
    .item-label {
      width: 70px;
      height: 24px;
      line-height: 24px;
      padding-right: 5px;
      color: #bfbfbf;
      font-size: 12px;
    }
    .item-select {
      flex: 1;
    }

    .item-select-content{
      position: relative;
      .select-icon{
        position: absolute;
        top: 10px;
        left: 12px;
      }
    }

    .upload-box{
      display: inline-block;
      margin-right: 10px;
    }
  }

  ::v-deep .el-input-number--mini {
    width: 100%;
    line-height: 24px;
    .el-input__inner {
      height: 24px;
      line-height: 24px;
    }
    .el-input-number__decrease {
      line-height: 12px;
    }
    .el-input-number__increase {
      height: 12px;
      line-height: 12px;
    }
    .el-input__suffix {
      right: 32px;
    }
  }

  ::v-deep .item-content-select .el-input__inner{
    height: 32px;
    padding-left: 32px;
  }

  ::v-deep .el-color-picker {
    margin-left: 4px;
    height: 24px;
    .el-color-picker__trigger {
      height: 24px;
      width: 24px;
    }
  }

  ::v-deep .el-link{
    font-size: 12px;
    display: inline-block;
  }
  .image-operation {
    text-align: left;
    margin-top: 8px;
    cursor: pointer;
    display: none;
    position: absolute;
    width: 230px;
    z-index: 1;
    .texts {
      color: #a6a6a6;
      &:hover {
        color: #409eff;
      }
    }
  }
  .image-upload {
    margin-top: 33px;
    cursor: pointer;
    position: absolute;
    width: 230px;
    z-index: 1;
    display: flex;
    justify-content: center;
    .upload-texts {
      opacity: 0.7;
    }
    .image-color-change {
      opacity: 0.7;
      margin-left: 10px;
    }
  }
  .image-item {
    &:hover {
      .image-operation {
        display: inline-block;
      }
    }
  }
  .hz-icon {
    width: 210px;
    height: 100px;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
}
.svg-reverse {
  transform: scale(-1, -1);
}

::v-deep .option-div {
  // display: flex;
  // flex-flow: row wrap;
  // justify-content: space-between;
  // width: 230px;
  .image-select-item {
    // width: 30%;
    // height: 58px;
    // margin: 2px;
    // background: #1F2125;
    // padding: 0;
    .option-icon-card {
        // text-align: center;
      // .option-div-name {
      //   line-height: 20px;
      //   margin-left: 5px;
      // }
      .option-item-icon{
        margin-right: 10px;
      }
    }
  }
}
</style>
