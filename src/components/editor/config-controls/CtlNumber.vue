<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
        <!-- 是否显示滑动条 -->
        <div v-if="node.data.range" class="number-range">
          <!-- 带输入框的滑动条样式存在大问题 所以使用以下方式-->
          <el-slider
            v-model="singleValue"
            :min="node.data.range[0]"
            :max="node.data.range[1]"
            @change="changeSlider()"
            >
            </el-slider>
          <el-input-number
            v-model="singleValue"
            controls-position="right"
            :min="node.data.range[0]"
            :max="node.data.range[1]"
            size="mini"
            @change="changeSlider()"
            >
          </el-input-number>
        </div>
        <div v-if="!node.data.range" class="number-no-range">
        <el-input-number
          v-model="singleValue"
          controls-position="right"
          :min="node.data.min || 0"
          :max="node.data.max || 99999999"
          size="mini"
          @change="changeSlider()"
          >
        </el-input-number>
        </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlNumber',
  data () {
    return {
      singleValue: this.value
    }
  },
  created () {
    this.singleValue = this.value;
  },
  methods: {
    changeSlider () {
      this.value = this.singleValue;
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .width-div {
    .number-range {
      display: flex;
      align-items: center;
      height: 24px;
      ::v-deep .el-slider {
        flex: 1;
      }
      ::v-deep .el-input-number--mini {
        width: 65px;
        margin-left: 4px;
        .el-input-number__decrease {
          top: 12px;
          height: 11px;
        }
        .el-input__inner {
          padding-left: 2px;
          padding-right: 30px;
        }
      }
    }
    .number-no-range {
      ::v-deep .el-input-number--mini {
        width: 100%;
      }
    }
    ::v-deep .el-input-number--mini {
      line-height: 24px;
      .el-input__inner {
        height: 24px;
        line-height: 24px;
      }
      .el-input-number__decrease {
        line-height: 11px;
      }
      .el-input-number__increase {
        height: 12px;
        line-height: 12px;
      }
    }
  }
}
</style>
