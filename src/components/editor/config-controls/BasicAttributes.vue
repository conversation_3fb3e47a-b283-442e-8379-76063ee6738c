<template>
  <div class="config-control basic-attributes">
    <div class="title">
      <span class="name-max-width" :title="currentCom.alias">{{ currentCom.alias }}</span
      ><span
        @click="updateComp"
        v-if="compUpdate !== this.currentCom.version"
        class="update"
        >更新版本</span
      >
    </div>
    <div class="desc">
      <span class="name-max-width" :title="currentCom.orginName">{{ currentCom.orginName }}</span>
      <span v-html="nbsp"></span> | <span v-html="nbsp"></span>
      <span>v{{ currentCom.version }}</span>
      <span v-html="nbsp"></span> | <span v-html="nbsp"></span>
      <span>{{ (currentCom.packageCreateDate || currentCom.createdAt) | formatDate }}</span>
    </div>
    <div class="theme-wrap">
      <div class="theme-img">
        <img v-if="currentCom.other && currentCom.other.themeUrl" :src="replaceUrl(currentCom.other.themeUrl)">
      </div>
      <div class="theme-btn" v-if="!noThemeComs.includes(currentCom.comType)">
        <el-button type="primary" size="mini" @click="toggle">更换主题风格</el-button>
        <el-button type="text" size="mini" @click.stop="saveAsTheme">保存为主题</el-button>
      </div>
    </div>
    <div class="attributes">
      <div class="title basic-info">基本属性</div>
      <div class="info">
        <div class="info-name">位置尺寸</div>
        <div class="info-content">
          <div class="content-position-xy">
            <el-input-number
              :value="left"
              @change="left = $event"
              controls-position="right"
              :min="0"
              :step="1"
              size="mini"
              id="id-input-position-X"
              @focus="focusInputXY"
            >
            </el-input-number>
            <i class="icon-placeholder"></i>
            <el-input-number
              :value="top"
              @change="top = $event"
              controls-position="right"
              :min="0"
              size="mini"
              id="id-input-position-Y"
              @focus="focusInputXY"
            >
            </el-input-number>
          </div>
          <div class="content-position-xy">
            <el-input-number
              v-model="width"
              controls-position="right"
              :min="0"
              size="mini"
              id="id-input-position-W"
              @focus="focusInputWH"
            >
            </el-input-number>
            <i class="icon-placeholder"></i>
            <!-- 此处有循环赋值问题 <i class="icon-placeholder pointer" :class="[isRelation ? 'el-icon-lock' : 'el-icon-unlock']" @click="isRelation = !isRelation"></i> -->
            <el-input-number
              v-model="height"
              controls-position="right"
              :min="0"
              size="mini"
              id="id-input-position-H"
              @focus="focusInputWH"
            >
            </el-input-number>
          </div>
        </div>
      </div>
      <div class="info">
        <div class="info-name" style="margin-top: 0;">光标</div>
        <div class="info-content contant-opacity">
          <el-radio-group
          v-model="cursorVal"
          @change="changeCursor()"
          size="mini">
            <el-radio-button
              v-for="(itemt, indext) in cursorTypeList"
              :label="itemt.value"
              :key="itemt.label + indext"
              >{{itemt.label}}</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="info">
        <div class="info-name">透明度</div>
        <div class="info-content contant-opacity">
          <el-slider
            v-model="opacityVal"
            :min="0"
            :max="1"
            :step="0.05"
            @change="changeOpacity()"
          >
          </el-slider>
          <el-input-number
            v-model="opacityVal"
            controls-position="right"
            :min="0"
            :max="1"
            :step="0.05"
            size="mini"
            @change="changeOpacity()"
          >
          </el-input-number>
        </div>
      </div>
      <div class="info">
        <div class="info-name" style="margin-top: 0;">默认隐藏</div>
        <div class="info-content contant-opacity">
          <el-checkbox v-model="isHideDefault" style="margin-left: 4px;">
          </el-checkbox>
        </div>
      </div>
      <div class="info">
        <div class="info-name" style="margin-top: 0;">配置选项</div>
        <div class="info-content contant-opacity">
          <el-radio-group
          v-model="configType"
          @change="changeConfigType()"
          size="mini">
            <el-radio-button
              v-for="(itemt, indext) in configTypeList"
              :label="itemt.value"
              :key="itemt.label + indext"
              >{{itemt.label}}</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { upgradeComponent } from '@/api/component'
import { replaceUrl } from '@/utils/base';
import { updateScreen } from '@/api/screen';
import { noThemeComs } from '@/common/constants'
export default {
  name: 'BasicAttributes',
  data () {
    return {
      replaceUrl: replaceUrl,
      noThemeComs,
      nbsp: '&nbsp',
      isRelation: false,
      opacityVal: 0,
      cursorVal: 'default',
      oldLeft: 0,
      oldTop: 0,
      oldWidth: 0,
      oldHeight: 0,
      // 配置类型
      configType: 'advanced',
      configTypeList: [
        { value: 'simple', label: '精简' },
        { value: 'advanced', label: '高级' }],
      cursorTypeList: [
        { value: 'default', label: '箭头' },
        { value: 'pointer', label: '手势' },
        { value: 'crosshair', label: '十字线' },
        { value: 'move', label: '拖拽' }
      ]
    };
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      currentSelectId: state => state.editor.currentSelectId,
      compPackges: state => state.editor.compPackges,
      compPackgesMap: state => state.editor.compPackgesMap
    }),
    ...mapGetters('editor', ['currentCom']),
    compUpdate () {
      return this.compPackgesMap[this.currentCom.comName]?.version;
    },
    left: {
      get: function () {
        return this.currentCom.attr.x;
      },
      set: function (value) {
        this.changeValue('left', value);
      }
    },
    top: {
      get: function () {
        return this.currentCom.attr.y;
      },
      set: function (value) {
        this.changeValue('top', value);
      }
    },
    width: {
      get: function () {
        return this.currentCom.attr.w;
      },
      set: function (value) {
        this.changeValue('width', value);
      }
    },
    height: {
      get: function () {
        return this.currentCom.attr.h;
      },
      set: function (value) {
        this.changeValue('height', value);
      }
    },
    isHideDefault: {
      get: function () {
        return !this.currentCom.show;
      },
      set: function (value) {
        this.isHide(value);
      }
    }
  },
  watch: {
    currentSelectId: {
      handler: function (val) {
        this.initVal();
      },
      immediate: true
    }
  },
  created () {
    this.addInputSuffix();
    this.initVal();
  },
  methods: {
    // 初始化设置
    initVal () {
      this.opacityVal = this.currentCom.attr.opacity;
      this.cursorVal = this.currentCom.attr.cursor;
      this.configType = this.currentCom.other.configType;
      // 回车键触发时使用
      this.oldLeft = this.left;
      this.oldTop = this.top;
      this.oldWidth = this.width;
      this.oldHeight = this.height;
    },

    // 获取input焦点时赋值
    focusInputXY () {
      // 回车键触发时使用
      this.oldLeft = this.left;
      this.oldTop = this.top;
    },

    updateChartXY (left, top) {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'attr.x', value: left },
          { key: 'attr.y', value: top }
        ]
      });
    },

    // 获取input焦点时赋值
    focusInputWH () {
      // 回车键触发时使用
      this.oldWidth = this.width;
      this.oldHeight = this.height;
    },

    updateChartWH (width, height) {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'attr.w', value: width },
          { key: 'attr.h', value: height }
        ]
      });
      // 更新动态面板下大屏的宽高
      const panels = [
        'interaction-container-dynamicpanel',
        'interaction-container-newdynamicpanel',
        'interaction-container-carousepanel',
        'interaction-container-popoverpanel',
        'interaction-container-mapShadowPanel',
        'interaction-container-fold-panel',
        'interaction-container-affixPanel',
        'interaction-container-popup',
        'interaction-container-statusdialogpanel'
      ]
      if (panels.includes(this.currentCom.comName)) {
        const screens = this.currentCom.config.screens || [];
        let _width, _height;
        if (this.screenInfo.type === 'pc') {
          _width = width;
          _height = height;
        } else {
          _width = this.screenInfo.config.width * (width / 24);
          // _height = height * 10
          _height = height * 10 + ((height - 1) * 5)
        }
        if (this.currentCom.comName === 'interaction-container-fold-panel') { // 折叠面板高度取config.panel.panelHeight
          _height = this.currentCom.config.panel.panelHeight;
        }
        if (screens.length) {
          screens.forEach(item => {
            updateScreen({ 'config.width': _width, 'config.height': _height }, { id: item.id })
          })
        }
      }
    },

    // 图表不透明度设置
    changeOpacity () {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [{ key: 'attr.opacity', value: this.opacityVal }]
      });
    },

    // 组件光标设置
    changeCursor () {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [{ key: 'attr.cursor', value: this.cursorVal }]
      });
    },

    // 图表配置选项
    changeConfigType () {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [{ key: 'other.configType', value: this.configType }]
      });
    },

    changeValue (type, val) {
      switch (type) {
        case 'left': {
          if (this.oldLeft === val) {
            return;
          }
          this.oldLeft = val;
          this.updateChartXY(val, this.top);
          break;
        }
        case 'top': {
          if (this.oldTop === val) {
            return;
          }
          this.oldTop = val;
          this.updateChartXY(this.left, val);
          break;
        }
        case 'width': {
          if (this.oldWidth === val) {
            return;
          }
          let tempheight = this.height;
          if (this.isRelation) {
            const proportion = val / this.oldWidth;
            tempheight = Math.round(this.height * proportion);
          }
          this.oldWidth = val;
          this.oldHeight = tempheight;
          this.updateChartWH(val, tempheight);
          break;
        }
        case 'height': {
          if (this.oldHeight === val) {
            return;
          }
          let tmepwidth = this.width;
          if (this.isRelation) {
            const proportion = val / this.oldHeight;
            tmepwidth = Math.round(this.width * proportion);
          }
          this.oldHeight = val;
          this.oldWidth = tmepwidth;
          this.updateChartWH(tmepwidth, val);
          break;
        }
        default:
          break;
      }
    },

    addInputSuffix () {
      const suffixList = ['X', 'Y', 'W', 'H'];
      suffixList.forEach(item => {
        const span = document.createElement('span');
        const innerspan = document.createElement('span');
        const textspan = document.createElement('span');
        // 添加elementUI 内置 class
        span.setAttribute('class', 'el-input__suffix');
        innerspan.setAttribute('class', 'el-input__suffix-inner');

        span.append(innerspan);
        innerspan.append(textspan);
        textspan.append(item);
        this.$nextTick(() => {
          if (document.getElementById('id-input-position-' + item)) {
            document.getElementById('id-input-position-' + item).lastElementChild.prepend(span);
          }
        });
      });
    },

    // 当前组件是否默认隐藏
    isHide (val) {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [{ key: 'show', value: !val }]
      });
    },

    toggle () {
      this.$emit('toggle');
    },

    saveAsTheme () {
      this.$emit('saveAsTheme');
    },

    updateComp () {
      const text =
        '更新组件有可能导致组件不可用，如果不确定请咨询客服，是否确定更新？';
      this.$confirm(text, '', {
        confirmButtonText: '确定(Enter)',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async () => {
          // 点击确认的逻辑
          const res = await upgradeComponent([
            { version: this.compUpdate, id: this.currentCom.id }
          ]);
          if (res && res.success && res.data) {
            this.$store.commit('editor/updateScreenComs', res.data);
          }
        })
        .catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
.basic-attributes {
  display: inline-block;
  padding-top: 0;
  .title {
    line-height: 30px;
    display: flex;
    justify-items: center;
    font-size: 14px;
    justify-content: space-between;
    .name-max-width {
      max-width: 300px;
    }
    .update {
      font-size: 12px;
      cursor: pointer;
    }
  }
  .basic-info {
    font-size: 13px;
    border-top: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    border-color: var(--control-border-color);
  }
  .desc {
    display: flex;
    padding-bottom: 6px;
    color: #999;
    .name-max-width {
      max-width: 128px;
    }
  }
  .name-max-width {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .theme-wrap {
    .theme-img {
      width: 100%;
      height: 180px;
      padding: 10px 20px;
      background-color: #0e0e0e;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .theme-btn {
      text-align: center;
      padding: 10px 0;
    }
  }
  .info {
    display: flex;
    padding: 10px 0;
    .info-name {
      flex: none;
      width: 80px;
      line-height: 24px;
      margin-top: 8px;
    }
    .info-content {
      flex: 1;
      .icon-placeholder {
        display: block;
        width: 16px;
        margin-left: 4px;
        line-height: 16px;
        text-align: center;
      }
      ::v-deep .el-slider {
        flex: 1;
      }
      ::v-deep .el-input-number--mini {
        width: 100%;
        margin-left: 4px;
        line-height: 24px;
        .el-input-number__decrease {
          top: 12px;
          height: 11px;
          line-height: 11px;
        }
        .el-input__inner {
          padding-left: 2px;
          padding-right: 18px;
          height: 24px;
          line-height: 24px;
        }
        .el-input-number__increase {
          height: 12px;
          line-height: 12px;
        }
        .el-input__suffix {
          right: 6px;
        }
      }
      .content-position-xy {
        display: flex;
        align-items: center;
        height: 24px;
        margin-top: 8px;
        ::v-deep .el-input-number__decrease {
          display: none;
        }
        ::v-deep .el-input-number__increase {
          display: none;
        }
      }
    }
    .contant-opacity {
      display: flex;
      line-height: 24px;
      align-items: center;
      ::v-deep .el-input-number--mini {
        width: 70px;
        margin-left: 12px;
        .el-input__inner {
          padding-right: 30px;
        }
      }
    }
  }
}
</style>
