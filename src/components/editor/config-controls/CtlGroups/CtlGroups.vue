<template>
  <div class="config-control">
    <el-collapse accordion>
      <el-collapse-item>
        <template slot="title">
          {{ node.data.name }}
          <i
            class="config-icon"
            :class="[isShow ? 'icon-visible' : 'icon-hide']"
            v-if="node.data.enableHide"
            @click="isShow = !isShow"
          ></i>
          <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
            <div
              slot="content"
              :style="{ 'max-width': '15rem' }"
              v-html="node.data.desc"
            ></div>
            <span class="icon icon2 el-icon-question"></span>
          </el-tooltip>
        </template>
        <ConfigTree
          :treeData="config"
          :configObj="this.value"
          @change="handleChange"
        >
        </ConfigTree>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: 'CtlGroups',
  data () {
    return {
      isShow: true
    };
  },
  methods: {
    handleChange ({ path, value }) {
      const pathArr = path.split('.');
      const tree = this.node && this.node.tree;
      if (tree) {
        const upperPaths = tree.valuePathMap[this.node.id];
        this.dispatch('ConfigTree', 'change', {
          path: upperPaths.concat(pathArr).join('.'),
          value
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 16px 0;
    }
  }
}
</style>
