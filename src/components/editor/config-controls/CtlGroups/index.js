import CtlGroup from './CtlGroups'
import CtlTabsGroup from './CtlTabsGroups'
import background from './background.json'
import border from './border.json'
import animation from './animation.json'
import legend from './legend.json'
import tooltip from './tooltip.json'
import xaxis from './xaxis.json'
import yaxis from './yaxis.json'
import advancedYaxis from './advancedYaxis.json'
import guide from './guide.json'
import warn from './warn.json'
import label from './label.json'
import tooltipNew from './tooltipNew.json'
import animationNew from './animationNew.json'
import legendNew from './legendNew.json'
import legendV2 from './legendV2.json'
import legendV3 from './legendV3.json'
import dataZoom from './dataZoom.json'

const configList = {
  CtlBackgroundGroup: background,
  CtlBorderGroup: border,
  CtlAnimationGroup: animation,
  CtlAnimationGroupNew: animationNew,
  CtlLegendGroup: legend,
  CtlLegendGroupNew: legendNew,
  CtlLegendGroupV2: legendV2,
  CtlLegendGroupV3: legendV3,
  CtlToolTipGroup: tooltip,
  CtlToolTipGroupNew: tooltipNew,
  CtlXAxisGroup: xaxis,
  CtlYAxisGroup: yaxis,
  CtlAdvYAxisGroup: advancedYaxis,
  CtlWarnGroup: warn,
  CtlLabelGroup: label,
  CtlDataZoomGroup: dataZoom
}
const configTabsList = {
  CtlGuideGroup: guide
}
const CtlList = {}
Object.keys(configList).forEach(key => {
  const tempCtl = _.cloneDeep(CtlGroup)
  tempCtl.name = key
  const mixin = {
    data: function () {
      return {
        config: configList[key]
      }
    }
  };
  (tempCtl.mixins || (tempCtl.mixins = [])).push(mixin)
  CtlList[key] = tempCtl
})

Object.keys(configTabsList).forEach(key => {
  const tempCtl = _.cloneDeep(CtlTabsGroup)
  tempCtl.name = key
  const mixin = {
    data: function () {
      return {
        config: configTabsList[key]
      }
    }
  };
  (tempCtl.mixins || (tempCtl.mixins = [])).push(mixin)
  CtlList[key] = tempCtl
})
export default CtlList
