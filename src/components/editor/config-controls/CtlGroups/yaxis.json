{"show": {"name": "y轴可见", "type": "switch", "isNecessary": true, "default": true}, "showDataType": {"type": "boolean", "isNecessary": true, "name": "显示数据类型", "default": true, "showInPanel": {"conditions": [[".dataType", "$eq", " "]]}}, "showMin": {"type": "boolean", "isNecessary": true, "name": "显示最小值", "default": true, "showInPanel": {"conditions": [[".dataType", "$eq", " "], [".show", "$eq", true]]}}, "dataType": {"name": "数据类型", "type": "buttonRadio", "isNecessary": true, "default": "category", "options": [{"label": "数值型", "value": "value"}, {"label": "类目型", "value": "category"}, {"label": "时间型", "value": "time"}], "showInPanel": {"conditions": [[".showDataType", "$eq", true], [".show", "$eq", true]]}}, "yAxisLabel": {"name": "轴标签", "type": "group", "isNecessary": true, "children": {"show": {"name": "显示/隐藏", "type": "boolean", "isNecessary": true, "default": true}, "rotate": {"name": "旋转角度", "type": "number", "isNecessary": true, "default": 0, "range": [-90, 90]}, "min": {"name": "最小值", "type": "number", "isNecessary": true, "default": 0, "showInPanel": {"conditions": [["..showMin", "$eq", true]]}}, "text": {"name": "文本样式", "type": "font", "isNecessary": true, "default": {"fontFamily": "思源黑体Medium", "fontWeight": "normal", "fontSize": 12, "color": "#fff"}}, "unitContent": {"name": "单位内容", "type": "text", "default": ""}, "overflow": {"name": "文本适配", "type": "select", "isNecessary": true, "default": "none", "options": [{"value": "none", "label": "自适应"}, {"value": "truncate", "label": "溢出省略号"}, {"value": "breakAll", "label": "溢出换行"}]}, "width": {"name": "文本宽度", "type": "number", "isNecessary": true, "default": 100, "showInPanel": {"conditions": [[".overflow", "$ne", "none"]]}}}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "yAxisname": {"name": "轴标题", "type": "group", "isNecessary": true, "children": {"content": {"name": "标题内容", "type": "text", "isNecessary": true, "default": "数量"}, "location": {"name": "显示位置", "type": "select", "isNecessary": true, "default": "end", "options": [{"name": "前", "value": "start"}, {"name": "中", "value": "middle"}, {"name": "后", "value": "end"}]}, "offset": {"name": "偏移", "type": "stepper", "isNecessary": true, "default": 15, "step": 0.5}, "text": {"name": "文本样式", "type": "font", "isNecessary": true, "default": {"color": "#fff", "fontFamily": "思源黑体Medium", "fontWeight": "normal", "fontSize": 12}}}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "yAxisLine": {"name": "轴线", "type": "group", "isNecessary": true, "children": {"show": {"name": "显示/隐藏", "type": "boolean", "isNecessary": true, "default": false}, "color": {"name": "颜色", "type": "color", "isNecessary": true, "default": "rgba(255,255,255, .1)"}, "weight": {"name": "粗细", "type": "number", "isNecessary": true, "default": 1}}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "ySplitLine": {"name": "网格线", "type": "group", "isNecessary": true, "children": {"show": {"name": "显示/隐藏", "type": "boolean", "isNecessary": true, "default": true}, "color": {"name": "颜色", "type": "color", "isNecessary": true, "default": "rgba(255, 255, 255, .1)"}, "type": {"name": "样式", "type": "select", "isNecessary": true, "default": "solid", "options": [{"value": "solid", "label": "直线"}, {"value": "dashed", "label": "虚线"}, {"value": "dotted", "label": "点线"}]}, "width": {"name": "粗细", "type": "number", "isNecessary": true, "default": "1"}}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}}