{"show": {"name": "显示/隐藏", "type": "boolean", "isNecessary": true, "default": true}, "confine": {"name": "限制在图表内", "type": "boolean", "isNecessary": true, "default": false, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "showUnit": {"name": "显示单位", "type": "boolean", "isNecessary": true, "default": false, "showInPanel": {"conditions": [[".show", "$eq", true]]}, "desc": "图表值标签有配置单位时才生效"}, "showType": {"type": "boolean", "isNecessary": true, "name": "显示触发方式", "default": true, "showInPanel": {"conditions": [[".type", "$eq", true]]}}, "type": {"name": "触发方式", "type": "select", "isNecessary": true, "options": [{"value": "item", "label": "数据项"}, {"value": "axis", "label": "坐标轴"}], "default": "item", "showInPanel": {"conditions": [[".showType", "$eq", true], [".show", "$eq", true]]}}, "textStyle": {"name": "文本样式", "type": "font", "isNecessary": true, "default": {"fontFamily": "思源黑体Medium", "fontWeight": "normal", "fontSize": 14, "color": "#fff"}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "crossStyle": {"name": "指示器", "type": "suite", "isNecessary": true, "children": {"width": {"name": "宽度", "type": "stepper", "isNecessary": true, "default": 1, "step": 1, "suffix": "px"}, "lineType": {"name": "类型", "type": "select", "isNecessary": true, "default": "solid", "options": [{"value": "solid", "label": "实线"}, {"value": "dashed", "label": "虚线"}, {"value": "dotted", "label": "点线"}]}, "color": {"name": "颜色", "type": "suite", "isNecessary": true, "children": {"start": {"name": "前/后", "type": "color", "isNecessary": true, "default": "rgba(0, 255, 233,0)"}, "center": {"name": "中", "type": "color", "isNecessary": true, "default": "rgba(255, 255, 255,1)"}}}}, "showInPanel": {"conditions": [[".type", "$eq", "axis"], [".show", "$eq", true]]}}, "bgBox": {"name": "背景框样式", "type": "group", "isNecessary": true, "children": {"bgColor": {"name": "背景色", "type": "color", "isNecessary": true, "default": "rgba(0,0,0, 0.65)"}, "padding": {"name": "内边距", "type": "number", "isNecessary": true, "default": 10}, "border": {"name": "边框", "type": "suite", "isNecessary": true, "children": {"borderWidth": {"name": "边框粗细", "type": "number", "isNecessary": true, "default": 0}, "borderColor": {"name": "边框颜色", "type": "color", "isNecessary": true, "default": "#333"}}}}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}}