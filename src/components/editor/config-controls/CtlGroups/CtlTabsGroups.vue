<template>
<div>
  <ConfigTree
    :treeData="config"
    :configObj="{ guide: this.value }"
    @change="handleChange"
  >
  </ConfigTree>
</div>
</template>

<script>
export default {
  name: 'CtlTabsGroups',
  data () {
    return {
      isShow: true
    };
  },
  methods: {
    handleChange ({ path, value }) {
      const pathArr = path.split('.');
      pathArr.shift()
      const tree = this.node && this.node.tree;
      if (tree) {
        const upperPaths = tree.valuePathMap[this.node.id];
        this.dispatch('ConfigTree', 'change', {
          path: upperPaths.concat(pathArr).join('.'),
          value
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 16px 0;
    }
  }
}
</style>
