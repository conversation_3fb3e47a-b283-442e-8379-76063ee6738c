{"guide": {"name": "辅助线", "type": "tabs", "isNecessary": true, "addable": true, "maxTabs": 10, "default": [], "templatesName": "辅助线", "templates": {"type": {"name": "类型", "type": "buttonRadio", "isNecessary": true, "default": "compute", "options": [{"value": "compute", "label": "计算值"}, {"value": "fixed", "label": "固定值"}, {"value": "dataSource", "label": "字段值"}]}, "compute": {"name": "计算值", "type": "select", "isNecessary": true, "default": "max", "options": [{"value": "max", "label": "最大值"}, {"value": "min", "label": "最小值"}, {"value": "average", "label": "平均值"}], "showInPanel": {"conditions": [[".type", "$eq", "compute"]]}}, "fixed": {"name": "固定值", "type": "text", "isNecessary": true, "default": "", "showInPanel": {"conditions": [[".type", "$eq", "fixed"]]}}, "dataSource": {"name": "字段值", "type": "dataSource", "isNecessary": true, "default": "", "showInPanel": {"conditions": [[".type", "$eq", "dataSource"]]}}, "lineStyle": {"name": "线样式", "type": "group", "isNecessary": true, "children": {"type": {"name": "类型", "type": "select", "isNecessary": true, "default": "dashed", "options": [{"value": "solid", "label": "直线"}, {"value": "dashed", "label": "虚线"}, {"value": "dotted", "label": "点线"}]}, "width": {"name": "宽度", "type": "slider", "isNecessary": true, "default": 15, "step": 1, "min": 0, "max": 20}, "color": {"name": "颜色", "type": "color", "isNecessary": true, "default": "rgba(31, 188, 255, 1)"}}}, "label": {"name": "值标签", "type": "group", "isNecessary": true, "children": {"position": {"name": "位置", "type": "select", "isNecessary": true, "default": "insideEndTop", "options": [{"value": "start", "label": "起始点"}, {"value": "middle", "label": "中点"}, {"value": "end", "label": "结束点"}, {"value": "insideStartTop", "label": "内部起始点上方"}, {"value": "insideStartBottom", "label": "内部起始点下方"}, {"value": "insideEndTop", "label": "内部结束点上方"}, {"value": "insideEndBottom", "label": "内部结束点下方"}]}, "text": {"name": "文本样式", "type": "font", "isNecessary": true, "default": {"fontFamily": "思源黑体Medium", "fontWeight": "normal", "fontSize": 14, "color": "rgba(31, 188, 255, 1)"}}}}}}}