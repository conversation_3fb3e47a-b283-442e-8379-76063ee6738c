{"show": {"name": "开启/关闭", "type": "boolean", "isNecessary": true, "default": true}, "animationEasing": {"name": "动画类型", "type": "select", "isNecessary": true, "default": "linear", "options": [{"value": "linear", "label": "线性"}, {"value": "quadraticIn", "label": "平方进入"}, {"value": "quadraticOut", "label": "平方退出"}, {"value": "quadraticInOut", "label": "平方进出"}, {"value": "cubicIn", "label": "立方进入"}, {"value": "cubicOut", "label": "立方退出"}, {"value": "cubicInOut", "label": "立方进出"}, {"value": "quarticIn", "label": "四次进入"}, {"value": "quarticOut", "label": "四次退出"}, {"value": "quarticInOut", "label": "四次进出"}, {"value": "quinticIn", "label": "五次进入"}, {"value": "quinticOut", "label": "五次退出"}, {"value": "quinticInOut", "label": "五次进出"}, {"value": "sinusoidalIn", "label": "正弦进入"}, {"value": "sinusoidalOut", "label": "正弦退出"}, {"value": "sinusoidalInOut", "label": "正弦进出"}, {"value": "exponentialIn", "label": "指数进入"}, {"value": "exponentialOut", "label": "指数退出"}, {"value": "exponentialInOut", "label": "指数进出"}, {"value": "circularIn", "label": "圆弧进入"}, {"value": "circularOut", "label": "圆弧退出"}, {"value": "circularInOut", "label": "圆弧进出"}, {"value": "elasticIn", "label": "弹性进入"}, {"value": "elasticOut", "label": "弹性退出"}, {"value": "elasticInOut", "label": "弹性进出"}, {"value": "backIn", "label": "背部进入"}, {"value": "backOut", "label": "背部退出"}, {"value": "backInOut", "label": "背部进出"}, {"value": "bounceIn", "label": "反弹进入"}, {"value": "bounceOut", "label": "反弹退出"}, {"value": "bounceInOut", "label": "反弹进出"}], "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "animationDuration": {"name": "首次时间", "description": "首次加载时的动画时长", "type": "number", "isNecessary": true, "default": 1000, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "animationDurationUpdate": {"name": "更新时间", "description": "数据更新时的动画时长", "type": "number", "isNecessary": true, "default": 1000, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "showTooltip": {"name": "自动显示提示框", "type": "boolean", "isNecessary": true, "default": true, "showInPanel": {"conditions": [[".show", "$eq", true]]}}}