{"show": {"name": "x轴可见", "type": "switch", "isNecessary": true, "default": true}, "showDataType": {"type": "boolean", "isNecessary": true, "name": "显示数据类型", "default": true, "showInPanel": {"conditions": [[".dataType", "$eq", " "]]}}, "dataType": {"name": "数据类型", "type": "buttonRadio", "isNecessary": true, "default": "category", "options": [{"label": "数值型", "value": "value"}, {"label": "类目型", "value": "category"}, {"label": "时间型", "value": "time"}], "showInPanel": {"conditions": [[".showDataType", "$eq", true], [".show", "$eq", true]]}}, "xAxisLabel": {"name": "轴标签", "type": "group", "isNecessary": true, "children": {"show": {"name": "显示/隐藏", "type": "boolean", "isNecessary": true, "default": true}, "rotate": {"name": "旋转角度", "type": "number", "isNecessary": true, "default": 0, "range": [-90, 90]}, "interval": {"name": "标签隔行显示", "type": "number", "isNecessary": true, "default": 0, "range": [0, 10]}, "width": {"name": "文本显示宽度", "type": "number", "isNecessary": true, "default": 70, "range": [0, 200]}, "overflow": {"name": "超出宽度后的处理方式", "type": "select", "isNecessary": true, "default": "truncate", "options": [{"value": "truncate", "label": "截断"}, {"value": "break", "label": "换行"}]}, "text": {"name": "文本样式", "type": "font", "isNecessary": true, "default": {"fontFamily": "思源黑体Medium", "fontWeight": "normal", "fontSize": 12, "color": "#fff"}}}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "xAxisname": {"name": "轴标题", "type": "group", "isNecessary": true, "children": {"content": {"name": "标题内容", "type": "text", "isNecessary": true, "default": ""}, "location": {"name": "显示位置", "type": "select", "isNecessary": true, "default": "end", "options": [{"name": "前", "value": "start"}, {"name": "中", "value": "middle"}, {"name": "后", "value": "end"}]}, "offset": {"name": "偏移", "type": "stepper", "isNecessary": true, "default": 0, "step": 0.5}, "text": {"name": "文本样式", "type": "font", "isNecessary": true, "default": {"fontFamily": "思源黑体Medium", "fontWeight": "normal", "fontSize": 12, "color": "#fff"}}}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "xAxisLine": {"name": "轴线", "type": "group", "isNecessary": true, "children": {"show": {"name": "显示/隐藏", "type": "boolean", "isNecessary": true, "default": false}, "color": {"name": "颜色", "type": "color", "isNecessary": true, "default": "rgba(255,255,255, .1)"}, "weight": {"name": "粗细", "type": "number", "isNecessary": true, "default": 1}}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}, "xSplitLine": {"name": "网格线", "type": "group", "isNecessary": true, "children": {"show": {"name": "显示/隐藏", "type": "boolean", "isNecessary": true, "default": false}, "color": {"name": "颜色", "type": "color", "isNecessary": true, "default": "rgba(255, 255, 255, .1)"}, "type": {"name": "样式", "type": "select", "isNecessary": true, "default": "solid", "options": [{"value": "solid", "label": "直线"}, {"value": "dashed", "label": "虚线"}, {"value": "dotted", "label": "点线"}]}, "width": {"name": "粗细", "type": "number", "isNecessary": true, "default": "1"}}, "showInPanel": {"conditions": [[".show", "$eq", true]]}}}