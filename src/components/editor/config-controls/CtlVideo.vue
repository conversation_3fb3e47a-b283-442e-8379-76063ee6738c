<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <el-input
        v-model="tempValue"
        @change="changeValue()"
        size="mini"
        prefix-icon="el-icon-link"
        >
      </el-input>
      <div class="image-item">
        <div class="image-operation">
          <span class="texts" @click="delImage()">删除</span>
          <!-- <span v-html="nbsp"></span> | <span v-html="nbsp"></span> -->
        </div>
        <div class="image-upload">
          <div class="avatar-uploader">
            <el-button class="upload-texts" type="primary" size="mini" @click="openDialog()">替换</el-button>
          </div>
        </div>
        <video
          style="width: 100%; height: 100px"
          :src="value"
          >
        </video>
      </div>
    </div>
    <select-resources-dialog
    v-if="isResourcesDialog"
    :isOpenDialog="isResourcesDialog"
    :resourceType="resourceType"
    :openFolderName="locateFolderName"
    @selectedFile="selectedFile"
    @closeRescourcesDialog="closeDialog"></select-resources-dialog>
  </div>
</template>

<script>
import SelectResourcesDialog from '@/components/resources-control/SelectResourcesDialog.vue'
export default {
  name: 'CtlVideo',
  components: {
    SelectResourcesDialog
  },
  data () {
    return {
      nbsp: '&nbsp',
      // 打开上传弹窗
      isResourcesDialog: false,
      tempValue: this.value,
      // 资源类型
      resourceType: '',
      // 定位到某个文件夹
      locateFolderName: ''
    }
  },
  created () {
    this.tempValue = this.value;
    // 判断是否为 主题库与模板库弹窗
    this.resourceType = this.node.data.imagePath || 'video';
    // 定位文件夹
    this.locateFolderName = this.node.data.locateFolderName || '默认文件夹';
  },
  methods: {
    openDialog () {
      this.isResourcesDialog = true;
    },
    closeDialog () {
      this.isResourcesDialog = false;
    },
    // 选择图片后执行
    selectedFile (data) {
      // console.log('选择图片后', data, this.node.data);
      // if (this.node.data.isBase64) {
      //   const image = new Image();
      //   image.crossOrigin = '';
      //   image.src = data.url;
      //   const self = this;
      //   image.onload = () => {
      //     const tempBase64 = self.getBase64Image(image);
      //     self.value = tempBase64;
      //     self.tempValue = tempBase64;
      //   };
      // } else {
      this.value = data.url;
      this.tempValue = data.url;
      // }
    },
    delImage () {
      this.value = '';
    },
    changeValue () {
      this.value = this.tempValue;
    },

    // width、height调用时传入具体像素值，控制大小 ,不传则默认图像大小
    getBase64Image (img, width, height) {
      var canvas = document.createElement('canvas');
      // canvas.width = width ? width : img.width;
      // canvas.height = height ? height : img.height;
      canvas.width = img.width;
      canvas.height = img.height;
      var ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      const ext = img.src.substring(img.src.lastIndexOf('.') + 1).toLowerCase();
      const dataURL = canvas.toDataURL('image/' + ext);
      return dataURL;
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
  .image-operation {
    text-align: left;
    margin-top: 8px;
    cursor: pointer;
    display: none;
    position: absolute;
    width: 230px;
    z-index: 1;
    .texts {
      color: #a6a6a6;
      &:hover {
        color: #409eff;
      }
    }
  }
  .image-upload {
    margin-top: 33px;
    cursor: pointer;
    position: absolute;
    width: 230px;
    z-index: 1;
    .upload-texts {
      margin-left: 88px;
      opacity: 0.7;
    }
  }
  .image-item {
    &:hover {
      .image-operation {
        display: inline-block;
      }
    }
  }
}
</style>
