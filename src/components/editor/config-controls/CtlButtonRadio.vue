<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div" :class="{'evenlySplit' : !!node.data.evenlySplit}">
      <el-radio-group v-model="value" size="mini">
        <el-radio-button
          v-for="(item, index) in node.data.options"
          :label="item.value"
          :key="item.label + index"
          >{{item.label}}</el-radio-button>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlButtonRadio'
}
</script>

<style lang="scss" scoped>
.config-control {
  .evenlySplit {
    ::v-deep .el-radio-group {
      display: flex;
      flex-direction: row;
      .el-radio-button {
        flex: 1;
      }
    }
    ::v-deep .el-radio-button--mini .el-radio-button__inner {
      width: 100%;
    }
  }
  ::v-deep .el-radio-button--mini .el-radio-button__inner {
    padding: 5px 9px;
  }
}
</style>
