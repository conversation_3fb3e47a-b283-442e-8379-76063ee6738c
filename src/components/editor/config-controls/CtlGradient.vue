<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <div class="div-content" :style="isSimple ? {height: '56px'} : ''">
        <el-radio-group v-if="!isSimple" v-model="gradientValue.type" size="mini" style="margin-bottom: 4px;" @change="changeGradientValue()">
          <el-radio-button label="linear-gradient">线性渐变</el-radio-button>
          <el-radio-button label="radial-gradient">径向渐变</el-radio-button>
        </el-radio-group>
        <div class="conent-color-block">
          <div class="color-block"
          :style="colorBlockStyle"></div>
          <div>
            <div class="display-flex">
              <el-color-picker
                v-model="gradientValue.start"
                show-alpha
                size='mini'
                style="margin-right: 4px;"
                @change="changeGradientValue()"
                >
              </el-color-picker>
              <el-input v-model="gradientValue.start" size="mini" style="flex: 1;"
              @change="changeGradientValue()">
              </el-input>
              <el-input-number
                v-model="gradientValue.startScale"
                controls-position="right"
                :min="0"
                :max="100"
                size="mini"
                :id="idStartScale"
                @change="changeGradientValue()"
                >
              </el-input-number>
            </div>
            <div class="display-flex" style="margin: 4px 0;">
              <el-color-picker
                v-model="gradientValue.end"
                show-alpha
                size='mini'
                style="margin-right: 4px;"
                @change="changeGradientValue()"
                >
              </el-color-picker>
              <el-input v-model="gradientValue.end" size="mini" style="flex: 1;"
              @change="changeGradientValue()">
              </el-input>
              <el-input-number
                v-model="gradientValue.endScale"
                controls-position="right"
                :min="0"
                :max="100"
                size="mini"
                :id="idEndScale"
                @change="changeGradientValue()"
                >
              </el-input-number>
            </div>
          </div>
        </div>
        <div class="number-range" v-show="!isSimple && gradientValue.type === 'linear-gradient'">
          <span style="margin-right: 4px;">角度</span>
          <el-slider
            v-model="gradientValue.deg"
            :min="0"
            :max="359"
            @change="changeGradientValue()"
            >
            </el-slider>
          <el-input-number
            v-model="gradientValue.deg"
            controls-position="right"
            :min="0"
            :max="359"
            size="mini"
            :id="idInputDeg"
            @change="changeGradientValue()"
            >
          </el-input-number>
        </div>
        <div class="number-range" v-if="!isSimple && gradientValue.type === 'radial-gradient'">
          <span style="margin-right: 4px;">形状</span>
          <el-radio-group v-model="gradientValue.shape" size="mini" @change="changeGradientValue()">
            <el-radio-button label="ellipse">椭圆</el-radio-button>
            <el-radio-button label="circle">圆形</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlGradient',
  data () {
    return {
      colorBlockStyle: { background: '' },
      gradientValue: {
        type: 'linear-gradient',
        deg: 0,
        start: '#0000ff',
        startScale: 0,
        end: '#ffc0cb',
        endScale: 100,
        shape: 'ellipse'
      },
      isSimple: false,
      idStartScale: 'id-input-start-' + new Date().getTime(),
      idEndScale: 'id-input-end-' + new Date().getTime(),
      idInputDeg: 'id-input-deg-' + new Date().getTime()
    }
  },
  created () {
    this.isSimple = this.node.data.isSimple;
    this.gradientValue = _.cloneDeep(this.value);
    this.setGradientColor();
    this.setSuffix(this.idStartScale, '%');
    this.setSuffix(this.idEndScale, '%');
    this.setSuffix(this.idInputDeg, '°');
  },
  methods: {
    setGradientColor () {
      if (this.gradientValue.type === 'radial-gradient') {
        this.colorBlockStyle.background = this.gradientValue.type + '(' + this.gradientValue.shape + ', ' + this.gradientValue.start + ' ' + this.gradientValue.startScale + '%, ' + this.gradientValue.end + ' ' + this.gradientValue.endScale + '%)';
      } else {
        this.colorBlockStyle.background = this.gradientValue.type + '(' + this.gradientValue.deg + 'deg, ' + this.gradientValue.start + ' ' + this.gradientValue.startScale + '%, ' + this.gradientValue.end + ' ' + this.gradientValue.endScale + '%)';
      }
    },
    changeGradientValue () {
      this.setGradientColor();
      this.value = _.cloneDeep(this.gradientValue);
    },
    setSuffix (idsuffix, suffix) {
      const span = document.createElement('span');
      const innerspan = document.createElement('span');
      const textspan = document.createElement('span');
      span.setAttribute('class', 'el-input__suffix');
      innerspan.setAttribute('class', 'el-input__suffix-inner');

      span.append(innerspan);
      innerspan.append(textspan);
      textspan.append(suffix);

      this.$nextTick(() => {
        if (document.getElementById(idsuffix)) {
          document.getElementById(idsuffix).lastElementChild.prepend(span);
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.width-div {
  .display-flex {
    display: flex;
  }
  .number-range {
    display: flex;
    align-items: center;
    height: 24px;
    ::v-deep .el-slider {
      flex: 1;
    }
  }
  .div-content {
    height: 112px;
    .conent-color-block {
      display: flex;
      .color-block {
        display: block;
        height: 52px;
        width: 30px;
        border: 1px solid #393b4a;
      }
    }
    ::v-deep .el-color-picker {
      margin-left: 4px;
      height: 24px;
      .el-color-picker__trigger {
        height: 24px;
        width: 24px;
      }
    }
    ::v-deep .el-input__inner {
      height: 24px;
      line-height: 24px;
    }
    ::v-deep .el-radio-group {
      display: flex;
      flex-direction: row;
      .el-radio-button {
        flex: 1;
      }
    }
    ::v-deep .el-radio-button--mini .el-radio-button__inner {
      width: 100%;
    }
    ::v-deep .el-radio-button--mini .el-radio-button__inner {
      padding: 5px 9px;
    }
  }
  ::v-deep .el-input-number--mini {
    line-height: 24px;
    width: 65px;
    margin-left: 4px;
    .el-input__inner {
      height: 24px;
      line-height: 24px;
      padding-left: 2px;
      padding-right: 40px;
    }
    .el-input-number__decrease {
      top: 12px;
      height: 11px;
      line-height: 11px;
    }
    .el-input-number__increase {
      height: 12px;
      line-height: 12px;
    }
    .el-input__suffix {
      right: 30px;
    }
  }
}
</style>
