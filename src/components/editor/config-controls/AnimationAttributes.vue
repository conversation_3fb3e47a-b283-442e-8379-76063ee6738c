<template>
  <div class="config-control basic-attributes pt-4">
    <el-collapse accordion>
      <el-collapse-item>
        <template slot="title">
          转场动画
        </template>
        <el-collapse v-model="active" accordion>
          <el-collapse-item title="入场动画" name="1">
            <template slot="title">
              <div class="c-header">
                <span class="name">入场动画</span>
                <div class="btn-box" v-if="active === '1'">
                  <span class="btn" @click.stop></span>
                  <span class="btn" @click.stop></span>
                  <span class="btn el-icon-circle-plus" @click.stop="addEntAnimation('ent')"></span>
                  <span class="btn el-icon-delete" @click.stop="delEntAnimation"></span>
                </div>
              </div>
            </template>
            <el-tabs class="tab-c" type="card" v-model="activeEntAnimation" size="mini">
              <el-tab-pane
                :label="'动画' + (index + 1)"
                :name="index + ''"
                v-for="(item, index) in entAnimation"
                :key="item.value">
                <el-form
                  class="form"
                  label-width="100px"
                  label-position="left"
                  size="mini">
                  <el-form-item label="动画类型">
                    <el-select v-model="item.value" class="w100" @change="saveAnimation">
                      <el-option v-for="opt in entAnimationOpt" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="动画时长">
                    <el-input-number
                      v-model="item.duration"
                      controls-position="right"
                      :step="500"
                      :min="1000"
                      class="w100"
                      @change="saveAnimation"
                    ></el-input-number>
                  </el-form-item>
                  <el-form-item label="动画延时">
                    <el-input-number
                      v-model="item.delay"
                      controls-position="right"
                      :step="500"
                      :min="0"
                      class="w100"
                      @change="saveAnimation"
                    ></el-input-number>
                  </el-form-item>
                  </el-form>
                </el-tab-pane>
            </el-tabs>
            <div class="no-data" v-if="!entAnimation.length">暂无数据</div>
          </el-collapse-item>
          <el-collapse-item title="驻场动画" name="2">
            <template slot="title">
              <div class="c-header">
                <span class="name">驻场动画</span>
                <div class="btn-box" v-if="active === '2'">
                  <span class="btn" @click.stop></span>
                  <span class="btn" @click.stop></span>
                  <span class="btn el-icon-circle-plus" @click.stop="addEntAnimation('resident')"></span>
                  <span class="btn el-icon-delete" @click.stop="delResidentAnimation"></span>
                </div>
              </div>
            </template>
            <el-tabs class="tab-c" type="card" v-model="activeResidentAnimation" size="mini">
              <el-tab-pane
                :label="'动画' + (index + 1)"
                :name="index + ''"
                v-for="(item, index) in residentAnimation"
                :key="item.value">
                <el-form
                  class="form"
                  label-width="100px"
                  label-position="left"
                  size="mini">
                  <el-form-item label="动画类型">
                    <el-select v-model="item.value" class="w100" @change="saveAnimation">
                      <el-option v-for="opt in residentAnimationOpt" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="动画时长">
                    <el-input-number
                      v-model="item.duration"
                      controls-position="right"
                      :step="500"
                      :min="1000"
                      class="w100"
                      @change="saveAnimation"
                    ></el-input-number>
                  </el-form-item>
                  <el-form-item label="动画延时">
                    <el-input-number
                      v-model="item.delay"
                      controls-position="right"
                      :step="500"
                      :min="0"
                      class="w100"
                      @change="saveAnimation"
                    ></el-input-number>
                  </el-form-item>
                  </el-form>
                </el-tab-pane>
            </el-tabs>
            <div class="no-data" v-if="!residentAnimation.length">暂无数据</div>
          </el-collapse-item>
          <el-collapse-item title="离场动画" name="3">
            <template slot="title">
              <div class="c-header">
                <span class="name">离场动画</span>
                <div class="btn-box" v-if="active === '3'">
                  <span class="btn" @click.stop></span>
                  <span class="btn" @click.stop></span>
                  <span class="btn el-icon-circle-plus" @click.stop="addEntAnimation('exit')"></span>
                  <span class="btn el-icon-delete" @click.stop="delExitAnimation"></span>
                </div>
              </div>
            </template>
            <el-tabs class="tab-c" type="card" v-model="activeExitAnimation" size="mini">
              <el-tab-pane
                :label="'动画' + (index + 1)"
                :name="index + ''"
                v-for="(item, index) in exitAnimation"
                :key="item.value">
                <el-form
                  class="form"
                  label-width="100px"
                  label-position="left"
                  size="mini">
                  <el-form-item label="动画类型">
                    <el-select v-model="item.value" class="w100" @change="saveAnimation">
                      <el-option v-for="opt in exitAnimationOpt" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="动画时长">
                    <el-input-number
                      v-model="item.duration"
                      controls-position="right"
                      :step="500"
                      :min="1000"
                      class="w100"
                      @change="saveAnimation"
                    ></el-input-number>
                  </el-form-item>
                  <el-form-item label="动画延时">
                    <el-input-number
                      v-model="item.delay"
                      controls-position="right"
                      :step="500"
                      :min="0"
                      class="w100"
                      @change="saveAnimation"
                    ></el-input-number>
                  </el-form-item>
                  </el-form>
                </el-tab-pane>
            </el-tabs>
            <div class="no-data" v-if="!exitAnimation.length">暂无数据</div>
          </el-collapse-item>
        </el-collapse>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import animationOpt from '@/common/animationClassData'
export default {
  name: 'AnimationAttributes',
  data () {
    return {
      active: '1',
      activeEntAnimation: '0',
      activeExitAnimation: '0',
      activeResidentAnimation: '0',
      entAnimation: [], // 入场动画
      exitAnimation: [], // 离场动画
      residentAnimation: [], // 驻场动画
      entAnimationOpt: animationOpt.entAnimation,
      exitAnimationOpt: animationOpt.exitAnimation,
      residentAnimationOpt: animationOpt.attention
    }
  },
  computed: {
    ...mapState({
      currentSelectId: state => state.editor.currentSelectId
    }),
    ...mapGetters('editor', ['currentCom'])
  },
  watch: {
    currentSelectId () {
      this.initVal();
    }
  },
  created () {
    this.initVal();
  },
  methods: {
    // 初始化设置
    initVal () {
      this.entAnimation = _.cloneDeep(this.currentCom.animation.animationIn);
      this.exitAnimation = _.cloneDeep(this.currentCom.animation.animationOut);
      this.residentAnimation = _.cloneDeep(this.currentCom.animation.animationKeep);
    },
    addEntAnimation (type) { // 新增入场动画
      const callback = {
        value: null,
        duration: 1000,
        delay: null
      }
      if (type === 'ent') {
        if (this.entAnimation.some(item => !item.value)) {
          this.$message.error('动画类型不能为空');
          return
        }
        this.entAnimation.push(callback);
        this.activeEntAnimation = this.entAnimation.length - 1 + '';
      } else if (type === 'exit') {
        if (this.exitAnimation.some(item => !item.value)) {
          this.$message.error('动画类型不能为空');
          return
        }
        this.exitAnimation.push(callback);
        this.activeExitAnimation = this.exitAnimation.length - 1 + '';
      } else if (type === 'resident') {
        if (this.residentAnimation.length >= 1) {
          this.$message.error('驻场动画只能添加一个');
          return
        }
        this.residentAnimation.push(callback);
      }
      this.saveAnimation();
    },
    delEntAnimation () { // 删除入场动画
      if (this.entAnimation.length) {
        this.entAnimation.splice(this.activeEntAnimation, 1);
        this.saveAnimation();
        this.activeEntAnimation = '0';
      }
    },
    delExitAnimation () {
      if (this.exitAnimation.length) {
        this.exitAnimation.splice(this.activeExitAnimation, 1);
        this.saveAnimation();
        this.activeExitAnimation = '0';
      }
    },
    delResidentAnimation () {
      if (this.residentAnimation.length) {
        this.residentAnimation.splice(this.activeResidentAnimation, 1);
        this.saveAnimation();
        this.activeResidentAnimation = '0';
      }
    },
    saveAnimation () {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'animation.animationIn', value: _.cloneDeep(this.entAnimation.filter(item => !!item.value)) },
          { key: 'animation.animationOut', value: _.cloneDeep(this.exitAnimation.filter(item => !!item.value)) },
          { key: 'animation.animationKeep', value: _.cloneDeep(this.residentAnimation.filter(item => !!item.value)) }
        ]
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-attributes {
  display: inline-block;
  padding-top: 0;
  .title {
    line-height: 30px;
    display: flex;
    justify-items: center;
    font-size: 14px;
    justify-content: space-between;
    .update {
      font-size: 12px;
      cursor: pointer;
    }
  }
  .basic-info {
    font-size: 13px;
    border-top: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    border-color: var(--control-border-color);
  }
  .desc {
    display: flex;
    padding-bottom: 6px;
    color: #999;
  }
  .theme {
    padding-bottom: 6px;
    .t-name {
      font-size: 12px;
      color: #ddd;
      margin-right: 3px;
      &:hover {
        color: #2681ff;
        .icon {
          color: #2681ff;
        }
      }
    }
  }
  .info {
    display: flex;
    padding: 10px 0;
    .info-name {
      width: 80px;
      line-height: 24px;
      margin-top: 8px;
    }
    .info-content {
      flex: 1;
      .icon-placeholder {
        display: block;
        width: 16px;
        margin-left: 4px;
        line-height: 16px;
        text-align: center;
      }
      ::v-deep .el-slider {
        flex: 1;
      }
      ::v-deep .el-input-number--mini {
        width: 100%;
        margin-left: 4px;
        line-height: 24px;
        .el-input-number__decrease {
          top: 12px;
          height: 11px;
          line-height: 11px;
        }
        .el-input__inner {
          padding-left: 2px;
          padding-right: 18px;
          height: 24px;
          line-height: 24px;
        }
        .el-input-number__increase {
          height: 12px;
          line-height: 12px;
        }
        .el-input__suffix {
          right: 6px;
        }
      }
      .content-position-xy {
        display: flex;
        align-items: center;
        height: 24px;
        margin-top: 8px;
        ::v-deep .el-input-number__decrease {
          display: none;
        }
        ::v-deep .el-input-number__increase {
          display: none;
        }
      }
    }
    .contant-opacity {
      display: flex;
      line-height: 24px;
      align-items: center;
      ::v-deep .el-input-number--mini {
        width: 70px;
        margin-left: 12px;
        .el-input__inner {
          padding-right: 30px;
        }
      }
    }
  }
}
.c-header {
  width: 250px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .btn-box {
    span {
      font-size: 14px;
      margin-left: 10px;
    }
  }
}
::v-deep .el-collapse {
  width: 100%;
  .el-collapse-item__content {
    padding: 16px 8px;
  }
}
::v-deep .el-collapse-item__content {
  padding: 8px 0;
}
::v-deep .el-tabs--border-card{
  background: unset;
  border: unset;
  box-shadow: unset;
  width: 100%;
}
::v-deep .el-tabs__item {
  font-size: 12px;
}
::v-deep .el-tabs--card>.el-tabs__header {
  border-bottom: unset;
  .el-tabs__item {
    height: 28px;
    line-height: 28px;
    border-radius: 4px 4px 0 0;
  }
  .el-tabs__nav-next, .el-tabs__nav-prev {
    line-height: 28px;
  }
  .el-tabs__nav-wrap.is-scrollable {
      padding: 0 20px;
      box-sizing: border-box;
  }
}
.form {
  ::v-deep .el-form-item__label {
    font-size: 12px;
    color: var(--control-text-color);
  }
}
.w100 {
  width: 100%;
}
.mt10 {
  margin-top: 10px;
}
.pt-4 {
  padding-top: 4px;
}
.no-data {
  text-align: center;
  color: #fafafa;
}
</style>
