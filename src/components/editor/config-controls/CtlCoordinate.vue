<template>
  <div class="config-control">
    <i class="config-icon"
      :class="[defaultValue.show ? 'icon-visible' : 'icon-hide']"
      v-if="node.data.enableHide"
      @click="defaultValue.show = !defaultValue.show;"></i>
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>
      <div class="title-name">{{node.data.name}}</div>
      <div class="config-title-less">{{defaultValue.coordinateSystem === 'normal' ? '通用坐标' : '自定义'}}
        <el-tooltip
          effect="dark"
          placement="top"
          v-if="defaultValue.coordinateSystem === 'normal'">
          <div slot="content" :style="{'max-width': '15rem'}">
            默认：EPSG3857；切换后，底图层对应的瓦片url需要重新设置
          </div>
          <span class="icon icon2 el-icon-question"></span>
        </el-tooltip>
      </div></div>
    <div class="width-div" :class="{'disabled-container': !defaultValue.show}">
      <div class="div-content">
        <el-radio-group v-model="defaultValue.coordinateSystem" size="mini" style="margin-bottom: 4px;" @change="changeCoordinate()">
          <el-radio-button label="normal">通用坐标</el-radio-button>
          <el-radio-button label="custom">自定义</el-radio-button>
        </el-radio-group>
      </div>
      <div class="content-value"
        v-if="defaultValue.coordinateSystem === 'normal'">
        <el-radio-group v-model="defaultValue.normal" @change="changeCoordinate()">
          <el-radio
            v-for="(item, index) in normalOptions"
            :label="item.value"
            :key="item.name + index"
            >{{item.name}}</el-radio>
        </el-radio-group>
      </div>
      <div class="content-value"
        v-if="defaultValue.coordinateSystem !== 'normal'">
        <div class="value-div">
          <el-input
            v-model="defaultValue.custom.epsg"
            size="mini"
            @change="changeCoordinate()"
            >
          </el-input>
          <div class="value-name">EPSG</div>
        </div>
        <div class="value-div">
          <el-input
            v-model="defaultValue.custom.proj"
            size="mini"
            @change="changeCoordinate()"
            >
          </el-input>
          <div class="value-name">proj参数</div>
        </div>
        <div class="value-div">
          <el-input
            v-model="defaultValue.custom.originX"
            size="mini"
            @change="changeCoordinate()"
            >
          </el-input>
          <div class="value-name">原点X(经度)</div>
        </div>
        <div class="value-div">
          <el-input
            v-model="defaultValue.custom.originY"
            size="mini"
            @change="changeCoordinate()"
            >
          </el-input>
          <div class="value-name">原点Y(维度)</div>
        </div>
        <div class="value-div">
          <el-input
            v-model="defaultValue.custom.resolutions"
            size="mini"
            @change="changeCoordinate()"
            >
          </el-input>
          <div class="value-name">分辨率
          <el-tooltip
            effect="dark"
            placement="top"
            >
            <div slot="content" :style="{'max-width': '11rem'}">
              从大到小，逗号分隔，如 0.7039144156840451, 0.35195720784202256
            </div>
            <span class="icon icon2 el-icon-question"></span>
          </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlCoordinate',
  components: {
  },
  data () {
    return {
      isShow: true,
      defaultValue: {
        coordinateSystem: 'normal',
        normal: 'EPSG3857',
        custom: {
          epsg: 'test',
          originX: 0,
          originY: 0,
          proj: '',
          resolutions: ''
        },
        show: false
      },
      normalOptions: [{
        name: 'EPSG3857',
        value: 'EPSG3857'
      },
      {
        name: 'EPSG4326',
        value: 'EPSG4326'
      }]
    }
  },
  // computed: {

  // },
  created () {
    this.defaultValue = _.cloneDeep(this.value);
    this.normalOptions = _.cloneDeep(this.node.data.normalOptions);
  },
  methods: {
    changeCoordinate () {
      this.value = _.cloneDeep(this.defaultValue);
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  align-items: start;
  .config-icon {
    cursor: pointer;
  }
  .config-title {
    line-height: unset;
    height: unset;
    .title-name {
      line-height: 28px;
      height: 28px;
    }
    .config-title-less {
      line-height: 28px;
      height: 28px;
    }
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 8px 0;
    }
  }
  .width-div {
    .div-content {
      ::v-deep .el-radio-group {
        display: flex;
        flex-direction: row;
        .el-radio-button {
          flex: 1;
        }
      }
      ::v-deep .el-radio-button--mini .el-radio-button__inner {
        width: 100%;
      }
      ::v-deep .el-radio-button--mini .el-radio-button__inner {
        padding: 5px 9px;
      }
    }
    .content-value {
      margin-top: 4px;
      ::v-deep .el-radio {
        width: 230px;
        margin-right: 0;
        line-height: 28px;
      }
      .value-div {
        margin-bottom: 4px;
        .value-name {
          color: #606266;
        }
      }
    }
    ::v-deep .el-input__inner {
      height: 24px;
      line-height: 24px;
      padding: 0 4px;
    }
    ::v-deep .el-input__icon {
      line-height: 24px;
    }
  }

}
</style>
