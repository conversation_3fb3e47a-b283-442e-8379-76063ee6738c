import CtlText from './CtlText';
import CtlCheckbox from './CtlCheckbox';
import CtlNumber from './CtlNumber';
import CtlSelect from './CtlSelect';
import CtlColor from './CtlColor';
import CtlBoolean from './CtlBoolean';
import CtlRadio from './CtlRadio';
import CtlButtonRadio from './CtlButtonRadio';
import CtlIconRadio from './CtlIconRadio';
import CtlPercent from './CtlPercent';
import CtlSlider from './CtlSlider';
import CtlSwitch from './CtlSwitch';
import CtlImage from './CtlImage';
import CtlImageSelect from './CtlImageSelect';
import CtlStepper from './CtlStepper';
import CtlMargin from './CtlMargin';
import CtlHidden from './CtlHidden';
import CtlLine from './CtlLine';
import CtlFont from './CtlFont';
import CtlGroup from './CtlGroup';
import CtlTabs from './CtlTabs';
import CtlSuite from './CtlSuite';
import CtlMenu from './CtlMenu';
import CtlPadding from './CtlPadding';
import CtlUpload from './CtlUpload';
import CtlGradient from './CtlGradient';
import CtlCoordinate from './CtlCoordinate';
import CtlTabstest from './CtlTabstest';
import CtlRoll from './CtlRoll';
import CtlVideo from './CtlVideo';
import CtlIconSelect from './CtlIconSelect';
import CtlRegion from './CtlRegion';
import CtlObjArr from './CtlObjArr';
import CtlCondition from './CtlCondition';
import CtlTemplate from './CtlTemplate';
import CtlHtml from './CtlHtml';
import CtlDataSource from './CtlDataSource.vue'
import CtlIconFont from './CtlIconFont'
import CtlIcon from './CtlIcon';
import CtlViewEditor from './CtlViewEditor';
import CtlTableHeader from './CtlTableHeader';
import CtlBorderRadius from './CtlBorderRadius';
import CtlGroupList from './CtlGroups';

export default {
  CtlText,
  CtlCheckbox,
  CtlNumber,
  CtlSelect,
  CtlColor,
  CtlBoolean,
  CtlRadio,
  CtlButtonRadio,
  CtlIconRadio,
  CtlPercent,
  CtlSlider,
  CtlSwitch,
  CtlImage,
  CtlImageSelect,
  CtlStepper,
  CtlMargin,
  CtlHidden,
  CtlLine,
  CtlFont,
  CtlGroup,
  CtlTabs,
  CtlSuite,
  CtlMenu,
  CtlPadding,
  CtlUpload,
  CtlGradient,
  CtlCoordinate,
  CtlRoll,
  CtlVideo,
  CtlObjArr,
  CtlCondition,
  CtlRegion,
  CtlIconSelect,
  ...CtlGroupList,
  CtlTabstest,
  CtlTemplate,
  CtlHtml,
  CtlDataSource,
  CtlIconFont,
  CtlIcon,
  CtlViewEditor,
  CtlTableHeader,
  CtlBorderRadius
};
