<template>
  <div class="config-control" :class="{'config-control-data-stly': (isTabsDataStly == '2')}">
  <el-collapse accordion ref="collapsePropagation" value="itemOpen">
    <el-collapse-item name="itemOpen">
      <template slot="title">
        <div class="child-control-title">
          <div class="tabs2-name">子组件管理</div>
          <div>
            <el-dropdown
            style="width: 30px;"
            trigger="click"
            @click.native.stop="stopSomething">
              <span class="el-dropdown-link">
                <i class="el-icon-circle-plus-outline el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                v-for="(item, idx) in chidCompListMenu"
                :key="idx"
                @click.native="addChildComp(item)">
                  {{item.alias}}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </template>
      <ul class="child-control-ul">
        <li class="child-control-li"
          v-for="(item, index) in existChidCompList"
          :key="index"
          >
          <i class="config-icon"
          :class="[item.show ? 'icon-visible' : 'icon-hide']"
          :title="item.show ? '隐藏' : '显示'"
          @click="setChildcompHide(item)"></i>
          <div class="li-name">
            <div class="name-text"
            :style="item.id !== nameEditId ? displayBlockStyle : displayNoneStyle"
            @click="currentComp(item)"
            :class="{'hide-comp': !item.show}"
            :title="item.alias">
              {{item.alias}}
            </div>
            <div class="name-text" :style="item.id === nameEditId ? displayBlockStyle : displayNoneStyle">
              <el-input
                v-model="nameEditNewName"
                size="mini"
                @blur="focusInput(item)"
                v-focusname
                @keyup.enter.native="nameEditId = null;"
                >
              </el-input>
            </div>
            <div class="icon-show-edit">
              <i class="config-icon el-icon-edit" title="编辑" @click="editName(item)"></i>
              <i class="config-icon el-icon-copy-document" title="复制" @click="copyComp(item)"></i>
              <i class="config-icon el-icon-delete" title="删除" @click="delComp(item)"></i>
            </div>
          </div>
          <div></div>
        </li>
      </ul>
    </el-collapse-item>
  </el-collapse>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
export default {
  name: 'ChidCompNode',
  props: {
    chidCompListMenu: {
      type: Array,
      default: function () {
        return [];
      }
    },
    isTabsDataStly: {
      type: String,
      default: '1'
    }
  },
  computed: {
    ...mapState({
      screenComs: state => state.editor.screenComs
    }),
    ...mapGetters('editor', ['currentCom']),
    existChidCompList: function () {
      const tempChidCompList = [];
      this.currentCom.children.forEach(item => {
        const itemInfo = this.screenComs[item];
        const tempChildCompList = {
          alias: itemInfo.alias,
          orginName: itemInfo.orginName,
          comName: itemInfo.comName,
          id: itemInfo.id,
          show: itemInfo.show,
          version: itemInfo.version,
          packageCreateDate: itemInfo.packageCreateDate,
          updatedAt: itemInfo.updatedAt.slice(0, 10)
        }
        tempChidCompList.push(tempChildCompList);
      });
      return tempChidCompList;
    }
  },
  data () {
    return {
      displayBlockStyle: {
        display: 'block'
      },
      displayNoneStyle: {
        display: 'none'
      },
      currentChidComp: {},
      nameEditId: '',
      nameEditNewName: ''
    }
  },
  // 自动聚焦
  directives: {
    focusname: {
      update: function (el) {
        el.querySelector('input').focus();
      }
    }
  },
  created () {
  },
  methods: {
    // 设置子组件显示隐藏
    setChildcompHide (item) {
      this.childCompUpdate(item.id, 'show', !item.show);
    },
    async addChildComp (item) {
      await this.$store.dispatch('editor/createChildComp', {
        name: item.comName,
        version: item.version,
        pid: this.currentCom.id
      });
    },
    // 当前选中的组件
    currentComp (item) {
      this.currentChidComp = item;
      this.$emit('chidCompClick', item);
    },
    editName (item) {
      this.nameEditId = item.id;
      this.nameEditNewName = item.alias;
    },
    focusInput (item) {
      this.nameEditId = null;
      if (item.alias === this.nameEditNewName) {
        return;
      }
      this.childCompUpdate(item.id, 'alias', this.nameEditNewName);
    },
    async copyComp (item) {
      await this.$store.dispatch('editor/copyChildComp', {
        id: item.id,
        pid: this.currentCom.id
      });
    },
    delComp (item) {
      this.$confirm(`确认删除${item.alias}？`, { type: 'warning', title: '提示' }).then(async () => {
        await this.$store.dispatch('editor/deleteChildComp', {
          id: item.id,
          pid: this.currentCom.id
        });
      }).catch(() => {})
    },
    // 子组件编辑
    childCompUpdate (id, keys, value) {
      this.$store.dispatch('editor/updateScreenCom', {
        id: id,
        keyValPairs: [{ key: keys, value: value }]
      });
    },
    stopSomething () {
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  cursor: pointer;
  .child-control-title {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 16px 0;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
    padding: 0;
  }
  ::v-deep .el-input__icon {
    line-height: 24px;
  }
  .child-control-ul {
    .child-control-li {
      list-style: none;
      display: flex;
      align-items: center;
      padding: 0 12px;
      color: #bfbfbf;
      height: 30px;
      line-height: 30px;
      border-bottom: 1px solid #393b4a;
      .config-icon {
        opacity: 0.8;
        &:hover {
          opacity: 1;
        }
      }
      .li-name {
        flex: 1;
        display: flex;
        align-items: center;
        .name-text {
          flex: 1;
          max-width: 200px;
          overflow: hidden; //超出隐藏
          text-overflow: ellipsis; //文字显示省略号
          white-space: nowrap; //文字单行 不换行
        }
        .icon-show-edit {
          display: none;
          margin-left: 4px;
          width: 60px;
        }
        .hide-comp {
          color: rgb(83, 82, 82);
          &:hover {
            color: #bfbfbf;
          }
        }
        &:hover {
          .icon-show-edit {
            display: block;
          }
        }
      }
      &:hover {
        color: #fff;
      }
    }
  }
  ::v-deep .el-collapse .el-collapse-item__content {
    padding-top: 0;
  }
}
::v-deep .el-dropdown-menu__item {
  color: rgba(255, 255, 255, 0.7);
}
.config-control-data-stly {
  padding: 4px 0;
  .tabs2-name {
    margin-left: 8px;
  }
}
</style>
