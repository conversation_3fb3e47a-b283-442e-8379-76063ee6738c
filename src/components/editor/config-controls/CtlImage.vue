<template>
  <div class="config-control">
    <div
      class="config-title nowrap"
      :class="{ 'config-title65': node.depth > 1 }"
      :title="node.data.name"
    >
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div
          slot="content"
          :style="{ 'max-width': '15rem' }"
          v-html="node.data.desc"
        ></div>
        <span class="icon icon2 el-icon-question"></span> </el-tooltip
      >{{ node.data.name }}
    </div>
    <div class="width-div">
      <el-input
        v-model="tempValue"
        @change="changeValue()"
        size="mini"
        prefix-icon="el-icon-link"
      >
      </el-input>
      <div class="width-div" style="margin: 12px 0;">
        <span class="config-title">开启压缩</span>
        <el-switch
          v-model="isZip">
        </el-switch>
      </div>
      <div class="image-item">
        <div class="image-operation">
          <span class="texts" @click="delImage()">删除</span>
        </div>
        <div class="image-upload">
          <div class="avatar-uploader">
            <el-button
              class="upload-texts"
              type="primary"
              size="mini"
              @click="openDialog()"
              >替换</el-button
            >
          </div>
          <div class="image-color">
            <el-button
              class="image-color-change"
              type="primary"
              size="mini"
              @click="openImageColorChange()"
              >编辑</el-button
            >
          </div>
        </div>
        <el-image style="width: 100%; height: 100px" :src="value" fit="contain">
        </el-image>
      </div>
      <!-- 暂时禁用前端缩略图功能 -->
      <!-- <div class="thumbnail-set-div">
        <div class="thumbnail-title">
          <el-tooltip effect="dark" placement="top">
            <div
              slot="content"
              :style="{ 'max-width': '15rem' }"
            >压缩图片会丢失图片透明度</div>
            <span class="icon icon2 el-icon-question"></span>
          </el-tooltip>
          <span class="ml-8 mr-8">压缩图片</span>
          <el-switch
          v-model="isThumbnail"
          @change="changeThumbnail()"
          active-color="#409EFF"
          inactive-color="#5F5F5F"
          >
          </el-switch>
        </div>
        <div class="thumbnail-wh" v-if="isThumbnail">
          <span class="mr-8">宽</span>
          <el-input
          v-model="thumbnailWidth"
          @change="changeImageSize()"
          size="mini"></el-input>
          <span class="ml-8 mr-8">高</span>
          <el-input
          v-model="thumbnailHeight"
          @change="changeImageSize()"
          size="mini"></el-input>
        </div>
      </div> -->
    </div>

    <select-resources-dialog
      v-if="isResourcesDialog"
      :isOpenDialog="isResourcesDialog"
      :resourceType="resourceType"
      :openFolderName="locateFolderName"
      :selectImgUrl="value"
      @selectedFile="selectedFile"
      @closeRescourcesDialog="closeDialog"
    ></select-resources-dialog>
    <el-dialog
      :visible.sync="showImageColor"
      :before-close="close"
      title="图片编辑"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
      top="0"
    >
      <div class="element-ui-override image-edit">
        <el-tabs v-model="imageHandleTab">
          <el-tab-pane name="changeColor">
            <span slot="label"><hz-icon name="color-board" class="tab-icon"></hz-icon> 改色</span>
            <div class="image-color-wrapper">
              <div class="color-picker">
                <span class="label">目标色</span>
                <el-color-picker
                  v-model="previewColor"
                  size="mini"
                  show-alpha
                  :predefine="historyColor"
                  @change="changeImageColor"
                ></el-color-picker>
              </div>
              <div class="image-preview" style="margin-top: 15px;">
                <span class="label">预览</span>
                <div
                  class="image-container preview"
                  v-loading="loading"
                  element-loading-text="颜色转换中"
                  element-loading-spinner="el-icon-loading"
                  element-loading-background="rgba(0, 0, 0, 0.8)"
                >
                  <el-image :src="previewData" class="image" fit="contain"></el-image>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="裁剪" name="crop">
            <span slot="label"><i class="el-icon-crop tab-icon"></i> 裁剪</span>
            <div class="image-color-wrapper">
              <div class="image-preview">
                <span class="label">预览</span>
                <div class="image-container preview"
                  v-loading="cropLoading"
                  element-loading-text="图片加载中"
                  element-loading-spinner="el-icon-loading"
                  element-loading-background="rgba(0, 0, 0, 0.8)">
                  <el-image :src="previewData" class="image" fit="contain" @load="loadImage"></el-image>
                </div>
              </div>
              <div class="bottom-toolbar">
                <span class="size-info">当前尺寸：{{currentSize}}</span>
                <div class="btns">
                  <span @click="scale(-1)"><i class="el-icon-remove-outline tab-icon"></i></span>
                  <span @click="scale(1)"><i class="el-icon-circle-plus-outline tab-icon"></i></span>
                  <span @click="rotate(-1)"><i class="el-icon-refresh-left tab-icon"></i></span>
                  <span @click="rotate(1)"><i class="el-icon-refresh-right tab-icon"></i></span>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary" @click="confirm()" :loading="uploadLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SelectResourcesDialog from '@/components/resources-control/SelectResourcesDialog.vue';
import {
  getMainColorByHSL,
  changeColor,
  createCanvasFromPxList
} from '@/utils/imageMagic';
import { getFormData, replaceUrl } from '@/utils/base';
import { commonUpload } from '@/api/common';
import Cropper from 'cropperjs';
import 'cropperjs/dist/cropper.css';
import { mapState } from 'vuex';
export default {
  name: 'CtlImage',
  components: {
    SelectResourcesDialog
  },
  computed: {
    historyColor () {
      return this.$store.state.editor.historyColor.background;
    },
    currentSize () {
      return this.imageWidth + 'x' + this.imageHeight;
    },
    ...mapState({
      screenInfo: (state) => state.editor.screenInfo
    })
  },
  data () {
    return {
      nbsp: '&nbsp',
      // 打开上传弹窗
      isResourcesDialog: false,
      tempValue: '',
      // 资源类型
      resourceType: '',
      // 定位到某个文件夹
      locateFolderName: '',
      // 是否为缩略图
      isThumbnail: false,
      // 设置压缩图片宽高
      thumbnailWidth: 100,
      thumbnailHeight: 80,
      // 存储当前选择图片对象
      selectData: null,
      // 显示图片改色窗口
      showImageColor: false,
      targetColor: null,
      previewColor: null,
      previewData: null,
      loading: false,
      imageHandleTab: 'changeColor',
      imageWidth: 0,
      imageHeight: 0,
      cropper: null,
      scaleNumber: 1,
      uploadLoading: false,
      cropLoading: false,
      isZip: true
    };
  },
  created () {
    this.tempValue = this.value;
    const isZip = this.getQueryVariable('isZip');
    if (isZip === 'false' || isZip === false) {
      this.isZip = false;
    } else {
      this.isZip = true;
    }
    // 判断是否为 主题库与模板库弹窗 由于组合控件的 config 配置信息是写在编辑器中，后端无法修改 imagePath，
    // 此处在 模板和主题 页面 无法正确获取传参
    // this.resourceType = this.node.data.imagePath || 'picture';
    this.resourceType = 'picture';
    // other 数据不准确，通过模板创建大屏时返回数据异常
    if (this.screenInfo && this.screenInfo.isScreentpl) {
      this.resourceType = 'tplPicture';
    } else if (this.$route.query.themeId) {
      this.resourceType = 'themePicture';
    }
    // 定位文件夹
    this.locateFolderName = this.node.data.locateFolderName || '页面背景';
    // 初始化是否使用缩略图
    // this.getThumbnailInfo();
  },
  watch: {
    isZip: {
      handler (val) {
        if (val) {
          this.value = this.value.split('?')[0] + '?isZip=true';
          return;
        }
        this.value = this.value.split('?')[0] + '?isZip=false';
      }
    }
  },
  methods: {
    // 获取是否需要缩略图
    getThumbnailInfo () {
      // 如果链接上有 宽高值 则默认为打开缩略图
      if (this.getQueryVariable('width') && this.getQueryVariable('height')) {
        this.thumbnailWidth = this.getQueryVariable('width');
        this.thumbnailHeight = this.getQueryVariable('height');
        this.isThumbnail = true;
      } else {
        if (this.node.data.isThumbnail) {
          this.thumbnailWidth = this.node.data.thumbnailWidth || 100;
          this.thumbnailHeight = this.node.data.thumbnailHeight || 80;
          this.isThumbnail = true;
        }
      }
    },

    scale (flag) {
      const step = 0.1 * flag;
      this.cropper && this.cropper.zoom(step);
      this.updateSize();
    },
    rotate (flag) {
      const step = 15 * flag;
      this.cropper && this.cropper.rotate(step);
      this.updateSize();
    },
    updateSize () {
      const data = this.cropper.getData();
      if (!data) return false;
      this.imageWidth = parseInt(data.width);
      this.imageHeight = parseInt(data.height);
    },
    loadImage (e) {
      const image = e.target;
      const vm = this;
      if (image) {
        this.cropper && this.cropper.destroy();
        this.imageWidth = image.naturalWidth;
        this.imageHeight = image.naturalHeight;
        this.cropper = new Cropper(image, {
          viewMode: 1,
          dragMode: 'move',
          background: true,
          zoomOnWheel: false,
          autoCropArea: 1,
          initialAspectRatio: 1,
          aspectRatio: image.naturalWidth / image.naturalHeight,
          autoCrop: false,
          minContainerWidth: 395,
          minContainerHeight: 395,
          ready: () => {
            this.cropper.crop();
            this.cropLoading = false;
          },
          crop: (e) => {
            vm.imageWidth = parseInt(e.detail.width);
            vm.imageHeight = parseInt(e.detail.height);
          }
        })
      }
    },
    openDialog () {
      this.isResourcesDialog = true;
    },
    closeDialog () {
      this.isResourcesDialog = false;
    },
    openImageColorChange () {
      this.cropLoading = true;
      this.cropper && this.cropper.destroy();
      this.previewData = this.value.split('?')[0];
      this.previewColor = this.targetColor;
      this.showImageColor = true;
    },
    close () {
      this.showImageColor = false;
      this.previewColor = null;
      this.previewData = null;
    },
    confirm () {
      this.uploadLoading = true;
      const vm = this;
      if (this.cropper) {
        const data = this.cropper.getCroppedCanvas({
          imageSmoothingQuality: 'high'
        }).toDataURL('image/png');
        const formData = getFormData(data);
        commonUpload(formData).then(res => {
          if (res.success && res.data) {
            const previewData = replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.url);
            vm.value = previewData + (vm.isZip ? '?isZip=true' : '?isZip=false');
            vm.tempValue = previewData;
            vm.showImageColor = false;
            vm.targetColor = this.previewColor;
            vm.uploadLoading = false;
          }
          vm.loading = false;
        }).catch(err => {
          console.error(err);
          vm.targetColor = this.previewColor;
          vm.uploadLoading = false;
          vm.showImageColor = false;
          vm.loading = false;
        });
      } else {
        this.value = this.previewData + (this.isZip ? '?isZip=true' : '?isZip=false');
        this.tempValue = this.previewData;
      }
    },
    changeImageColor (val) {
      const vm = this;
      if (!val) {
        vm.previewData = vm.value;
        return;
      }
      this.loading = true;
      this.$store.commit('editor/setHistoryColor', {
        // 收集选择过的颜色
        type: 'background',
        data: val
      });
      setTimeout(() => {
        getMainColorByHSL(vm.value).then((hsl) => {
          changeColor(vm.value, hsl, val, (colorChangedPxList, image) => {
            const data = createCanvasFromPxList(colorChangedPxList, image);
            const formData = getFormData(data);
            commonUpload(formData).then(res => {
              if (res.success && res.data) {
                vm.previewData = replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.url);
              }
              vm.loading = false;
            }).catch(err => {
              console.error(err);
              vm.loading = false;
            });
          });
        });
      }, 0);
    },
    // 选择图片后执行
    selectedFile (data) {
      // console.log('选择图片后', data, this.node.data);
      if (this.node.data.isBase64) {
        const image = new Image();
        image.crossOrigin = '';
        image.src = data.url;
        const self = this;
        image.onload = () => {
          const tempBase64 = self.getBase64Image(image);
          self.value = tempBase64;
          self.tempValue = tempBase64;
        };
      } else {
        this.selectData = data;
        // 是否返回缩略图
        // if (this.isThumbnail) {
        //   let reUrl = '';
        //   if (data.ecryptUrl) {
        //     const tempurl = data.ecryptUrl.split('?')[0];
        //     reUrl = tempurl || data.url;
        //     reUrl += `?width=${this.thumbnailWidth}&height=${this.thumbnailHeight}`;
        //     this.value = reUrl;
        //     this.tempValue = reUrl;
        //   } else {
        //     this.value = data.url;
        //     this.tempValue = data.url;
        //   }
        // } else {
        this.value = data.url + (this.isZip ? '?isZip=true' : '?isZip=false');
        this.tempValue = data.url;
        // }
      }
    },
    delImage () {
      this.value = '';
      this.tempValue = '';
    },
    changeValue () {
      this.value = this.tempValue + (this.isZip ? '?isZip=true' : '?isZip=false');
    },
    beforeAvatarUpload (file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 100;
      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG / PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 100MB!');
      }
      return isJPG && isLt2M;
    },
    // getBase64 (file) {
    //   return new Promise(function (resolve, reject) {
    //     const reader = new FileReader();
    //     let imgResult = '';
    //     reader.readAsDataURL(file);
    //     reader.onload = function () {
    //       imgResult = reader.result;
    //     };
    //     reader.onerror = function (error) {
    //       reject(error);
    //     };
    //     reader.onloadend = function () {
    //       resolve(imgResult);
    //     };
    //   });
    // },
    // width、height调用时传入具体像素值，控制大小 ,不传则默认图像大小
    getBase64Image (img, width, height) {
      var canvas = document.createElement('canvas');
      // canvas.width = width ? width : img.width;
      // canvas.height = height ? height : img.height;
      canvas.width = img.width;
      canvas.height = img.height;
      var ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      const ext = img.src.substring(img.src.lastIndexOf('.') + 1).toLowerCase();
      const dataURL = canvas.toDataURL('image/' + ext);
      return dataURL;
    },
    // 打开关闭压缩
    changeThumbnail () {
      if (this.isThumbnail) {
        // 已经使用了压缩图片
        if (this.getQueryVariable('width') && this.getQueryVariable('height')) {
          // 重置宽高
          this.thumbnailWidth = this.getQueryVariable('width');
          this.thumbnailHeight = this.getQueryVariable('height');
        } else {
          // 最近一次选择图片的对象
          if (this.selectData) {
            // 兼容历史版本是否有缩略图服务
            if (this.selectData.ecryptUrl) {
              let reUrl = '';
              const tempurl = this.selectData.ecryptUrl.split('?')[0];
              reUrl = tempurl || this.selectData.url;
              reUrl += `?width=${this.thumbnailWidth}&height=${this.thumbnailHeight}`;
              this.value = reUrl;
              this.tempValue = reUrl;
            } else {
              this.value = this.selectData.url;
              this.tempValue = this.selectData.url;
            }
          } else {
            // 无需修改值
            console.error('温馨提示:当前url为非压缩图片，请确认图片服务已，并重新选择图片~');
          }
        }
      } else {
        if (this.selectData) {
          this.value = this.selectData.url;
          this.tempValue = this.selectData.url;
        } else {
          // 使用原始图片
          const tempquery = this.tempValue.split('?');
          this.value = tempquery[0];
          this.tempValue = tempquery[0];
        }
      }
    },
    // 设置压缩图片大小
    changeImageSize () {
      // 当前是否为压缩图片url
      if (this.getQueryVariable('width') && this.getQueryVariable('height')) {
        let reUrl = '';
        const tempurl = this.tempValue.split('?')[0];
        reUrl = tempurl || this.selectData.url;
        reUrl += `?width=${this.thumbnailWidth}&height=${this.thumbnailHeight}`;
        this.value = reUrl;
        this.tempValue = reUrl;
      } else {
        console.error('温馨提示:当前url为非压缩图片，请重新选择图片~');
      }
    },
    getQueryVariable (variable) {
      const tempquery = (this.tempValue || '').split('?');
      if (tempquery.length <= 1) {
        return undefined;
      }
      const query = tempquery[1];
      const vars = query.split('&');
      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=');
        if (pair[0] === variable) {
          return pair[1];
        }
      }
      return undefined;
    }
  }
};
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
  .image-operation {
    text-align: left;
    margin-top: 8px;
    cursor: pointer;
    display: none;
    position: absolute;
    width: 230px;
    z-index: 1;
    .texts {
      color: #a6a6a6;
      &:hover {
        color: #409eff;
      }
    }
  }
  .image-upload {
    margin-top: 33px;
    cursor: pointer;
    position: absolute;
    width: 230px;
    z-index: 1;
    display: flex;
    justify-content: center;
    .upload-texts {
      opacity: 0.7;
    }
    .image-color-change {
      opacity: 0.7;
      margin-left: 10px;
    }
  }
  .image-item {
    &:hover {
      .image-operation {
        display: inline-block;
      }
    }
  }
  .thumbnail-set-div {
    .icon2 {
      cursor: pointer;
    }
    .thumbnail-title {
      line-height: 32px;
    }
    .thumbnail-wh {
      display: flex;
      line-height: 32px;
    }
    .ml-8 {
      margin-left: 8px;
    }
    .mr-8 {
      margin-right: 8px;
    }
  }
}
.image-color-wrapper {
  .color-picker {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .image-preview {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .preview {
      flex: 1;
      font-size: 0;
      ::v-deep .el-icon-loading {
        font-size: 14px;
      }
      .image {
        width: 100%;
        ::v-deep img {
          max-height: 400px;
          max-width: 395px;
        }
      }
    }
  }
  .label {
    margin-right: 10px;
    width: 45px;
    color: rgba(255, 255, 255, 1);
  }
  .bottom-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    color: rgba(255, 255, 255, 1);
    .size-info {
      line-height: 32px;
    }
    .btns {
      font-size: 18px;
      span {
        display: inline-block;
        margin: 0 5px;
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
      }
    }
  }
}
.dialog-footer {
  ::v-deep .el-button {
    padding: 4px 12px;
  }
}
</style>
<style lang="scss">
  .image-edit {
    .tab-icon {
      color: inherit;
      font-size: 16px;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: rgba(204, 219, 255, 0.16);
    }
    .el-tabs__active-bar {
      height: 1px;
    }
  }
  .hidden {
    display: none;
  }
</style>
