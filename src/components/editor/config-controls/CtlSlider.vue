<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
        <div class="number-range" :class="{'number-range-prefix': !!node.data.prefix}">
          <!-- 带输入框的滑动条样式存在大问题 所以使用以下方式-->
          <el-slider
            v-model="singleValue"
            :min="node.data.min || 0"
            :max="node.data.max || 100"
            :step="node.data.step || 1"
            :range="node.data.mode === 'double'"
            @change="changeSlider()"
            :id="idSlider"
            >
            </el-slider>
          <!-- 滑块模式为单滑块 -->
          <el-input-number
            v-if="!valIsArray"
            v-model="singleValue"
            controls-position="right"
            :min="node.data.min || 0"
            :max="node.data.max || 100"
            size="mini"
            :id="idInputNumber"
            :step="node.data.step || 1"
            :precision="node.data.precision"
            @change="changeSlider()"
            >
          </el-input-number>
          <!-- 滑块模式为双滑块 -->
          <el-input-number
            v-if="valIsArray"
            v-model="inputMOdel"
            controls-position="right"
            :min="node.data.min || 0"
            :max="node.data.max || 100"
            size="mini"
            :id="idInputNumber"
            :step="node.data.step || 1"
            :precision="node.data.precision"
            @change="changeInput(inputMOdel)"
            >
          </el-input-number>
        </div>
    </div>
    <div class="show-range-div" :id="idRangeDiv" v-if="this.node.data.showRange">
      <span>{{this.node.data.min}}</span>
      <span>{{this.node.data.max}}</span>
    </div>
    <div class="show-range-div-current" :id="idRangeDiv" v-if="valIsArray && this.node.data.showCurrentRange && !this.node.data.showRange">
      <span>{{value[0]}}</span>
      <span>{{value[1]}}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlSlider',
  data () {
    return {
      // input 输入框添加id
      idInputNumber: 'id-input-slider-' + new Date().getTime(),
      // 滑动条添加id
      idSlider: 'id-slider-' + new Date().getTime(),
      idRangeDiv: 'id-range-div-' + new Date().getTime(),
      inputMOdel: this.value,
      singleValue: this.value
    }
  },
  computed: {
    valIsArray: function () {
      return Object.prototype.toString.call(this.value) === '[object Array]';
    }
  },
  created () {
    // 双滑块时使用
    if (this.valIsArray) {
      this.inputMOdel = this.value[0];
      this.singleValue = this.value;
    } else {
      this.singleValue = this.value;
    }
    // 前缀
    if (this.node.data.prefix) {
      this.addPrefix(this.node.data.prefix);
    }
    // 后缀
    if (this.node.data.suffix) {
      this.addSuffix(this.node.data.suffix);
    }
    // 是否显示 范围值
    if (this.node.data.showRange || this.node.data.showCurrentRange) {
      this.addShowLable();
    }
  },
  methods: {
    addShowLable () {
      this.$nextTick(() => {
        const div0 = document.getElementById(this.idRangeDiv);
        if (document.getElementById(this.idSlider)) {
          document.getElementById(this.idSlider).prepend(div0);
        }
      });
    },
    addPrefix (str) {
      const span = document.createElement('span');
      const innerspan = document.createElement('span');
      const textspan = document.createElement('span');
      // 添加elementUI 内置 class
      span.setAttribute('class', 'el-input__prefix');
      innerspan.setAttribute('class', 'el-input__prefix-inner');

      span.append(innerspan);
      innerspan.append(textspan);
      textspan.append(str);

      this.$nextTick(() => {
        if (document.getElementById(this.idInputNumber)) {
          document.getElementById(this.idInputNumber).lastElementChild.prepend(span);
        }
      });
    },
    addSuffix (str) {
      const span = document.createElement('span');
      const innerspan = document.createElement('span');
      const textspan = document.createElement('span');
      // 添加elementUI 内置 class
      span.setAttribute('class', 'el-input__suffix');
      innerspan.setAttribute('class', 'el-input__suffix-inner');

      span.append(innerspan);
      innerspan.append(textspan);
      textspan.append(str);

      this.$nextTick(() => {
        if (document.getElementById(this.idInputNumber)) {
          document.getElementById(this.idInputNumber).lastElementChild.prepend(span);
        }
      });
    },
    // 双滑块时 赋值
    changeSlider () {
      if (!this.valIsArray) {
        this.value = this.singleValue;
        return;
      }
      this.inputMOdel = this.singleValue[0];
      this.value = this.singleValue;
    },
    // 双滑块时 赋值
    changeInput (currentValue) {
      this.value = [currentValue, this.value[1]];
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .width-div {
    .number-range {
      display: flex;
      align-items: center;
      height: 24px;
      ::v-deep .el-slider {
        flex: 1;
        .el-slider__runway {
          margin: 4px 0;
        }
        .show-range-div {
          width: 100%;
          height: 12px;
          line-height: 12px;
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          color: #b2b2b2;
        }
        .show-range-div-current {
          width: 100%;
          height: 12px;
          line-height: 12px;
          display: flex;
          margin-bottom: 4px;
          color: #b2b2b2;
          justify-content: space-around;
        }
      }
      ::v-deep .el-input-number--mini {
        width: 65px;
        margin-left: 4px;
        .el-input-number__decrease {
          top: 12px;
          height: 11px;
        }
        .el-input__inner {
          padding-left: 2px;
          padding-right: 40px;
        }
      }
    }
    .number-range-prefix {
      ::v-deep .el-input-number--mini {
        width: 85px;
        .el-input__inner {
          padding-left: 16px;
          padding-right: 40px;
        }
      }
    }
    ::v-deep .el-input-number--mini {
      line-height: 24px;
      .el-input__inner {
        height: 24px;
        line-height: 24px;
      }
      .el-input-number__decrease {
        line-height: 11px;
      }
      .el-input-number__increase {
        height: 12px;
        line-height: 12px;
      }
      .el-input__suffix {
        right: 32px;
      }
    }
  }
}
</style>
