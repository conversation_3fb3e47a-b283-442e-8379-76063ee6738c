<template>
  <div class="config-control">
    <div>{{ node.data.name }}</div>
    <button @click="handleAdd">新增</button>
    <ul>
      <li v-for="(tabObj, index) in tabsData" :key="tabObj._id">
        <button @click="handleDelete(index)">删除</button>
        <ConfigTree
          :treeData="node.data.templates"
          :configObj="tabObj"
          @change="handleChange($event, index, tabObj)">
        </ConfigTree>
      </li>
    </ul>
  </div>
</template>

<script>
import { parseConfig } from '@/lib/ConfigTree';
import { uuid } from '@/utils/base';
export default {
  name: 'CtlTabstest',
  data () {
    return {
      tabsData: []
    };
  },
  created () {
    if (this.value && this.value.length) {
      this.tabsData = [..._.cloneDeep(this.value)].map(d => ({ ...d, _id: uuid() }));
    }
  },
  methods: {
    updateValue () {
      this.value = _.cloneDeep(this.tabsData);
    },
    handleAdd () {
      const newConfig = parseConfig(this.node.data.templates);
      this.tabsData.push({ ...newConfig, _id: uuid() });
      this.updateValue();
    },
    handleDelete (index) {
      this.tabsData.splice(index, 1);
      this.updateValue();
    },
    handleChange (params, index, tabObj) {
      _.set(tabObj, params.path, params.value);
      this.tabsData.splice(index, 1, tabObj);
      this.updateValue();
    }
  }
}
</script>
