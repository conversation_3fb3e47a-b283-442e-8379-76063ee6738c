<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <el-select
        v-model="value"
        filterable
        placeholder="请选择"
        size="mini"
        :id="idIconSelectStr"
        >
        <div class="option-div">
          <el-option
            v-for="(item, index) in node.data.options"
            :key="item.label + index"
            :label="item.label"
            :value="item.value"
            class="image-select-item"
            >
              <div class="option-icon-card">
                <el-image
                  style="width: 18px; height: 18px"
                  :src="item.value"
                  fit='contain'
                  ></el-image>
                <div class="option-div-name">{{item.label}}</div>
              </div>
          </el-option>
        </div>
      </el-select>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlIconSelect',
  data () {
    return {
      // input 输入框添加id
      idIconSelectStr: 'ref-icon-select-' + new Date().getTime(),
      idIconSelect: ''
    }
  },
  watch: {
    value (newVal, oldVal) {
      this.changeSelection(newVal);
    }
  },
  mounted () {
    this.idIconSelect = document.getElementById(this.idIconSelectStr);
    this.changeSelection(this.value);
  },
  methods: {
    changeSelection (value) {
      if (this.idIconSelect) {
        this.idIconSelect.setAttribute('style', 'background:url(' + value + ') no-repeat;background-position: 16px center;height: 62px;padding-left: 48px;');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
}
::v-deep .option-div {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  width: 230px;
  .image-select-item {
    width: 30%;
    height: 58px;
    margin: 2px;
    background: #1F2125;
    padding: 0;
    .option-icon-card {
        text-align: center;
      .option-div-name {
        line-height: 20px;
      }
    }
  }
}
</style>
