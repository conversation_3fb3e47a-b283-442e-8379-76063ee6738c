<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <el-checkbox-group v-model="value">
        <el-checkbox
          v-for="(item, index) in node.data.options"
          :label="item.value"
          :key="item.label + index"
          :style="{width: node.data.optionCol + 'px'}"
          >
          {{item.label}}
          </el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlCheckbox'
}
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep .el-checkbox {
    margin-right: 20px;
    .el-checkbox__label {
      font-size: 12px;
    }
  }
}
</style>
