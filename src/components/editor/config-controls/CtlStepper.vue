<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <el-input-number
        v-model="tempValue"
        @change="changeValue()"
        controls-position="right"
        :min="node.data.min || 0"
        :max="node.data.max || 100"
        size="mini"
        :id="idInputNumber"
        :step="node.data.step || 1"
        :precision="node.data.precision"
        >
      </el-input-number>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlStepper',
  data () {
    return {
      // input 输入框添加id
      idInputNumber: 'id-input-slider-' + new Date().getTime(),
      tempValue: this.value
    }
  },
  created () {
    this.tempValue = this.value;
    // 前缀
    if (this.node.data.prefix) {
      this.addPrefix(this.node.data.prefix);
    }
    // 后缀
    if (this.node.data.suffix) {
      this.addSuffix(this.node.data.suffix);
    }
  },
  methods: {
    changeValue () {
      this.value = this.tempValue;
    },
    addPrefix (str) {
      const span = document.createElement('span');
      const innerspan = document.createElement('span');
      const textspan = document.createElement('span');
      // 添加elementUI 内置 class
      span.setAttribute('class', 'el-input__prefix');
      innerspan.setAttribute('class', 'el-input__prefix-inner');

      span.append(innerspan);
      innerspan.append(textspan);
      textspan.append(str);

      this.$nextTick(() => {
        if (document.getElementById(this.idInputNumber)) {
          document.getElementById(this.idInputNumber).lastElementChild.prepend(span);
        }
      });
    },
    addSuffix (str) {
      const span = document.createElement('span');
      const innerspan = document.createElement('span');
      const textspan = document.createElement('span');
      // 添加elementUI 内置 class
      span.setAttribute('class', 'el-input__suffix');
      innerspan.setAttribute('class', 'el-input__suffix-inner');

      span.append(innerspan);
      innerspan.append(textspan);
      textspan.append(str);

      this.$nextTick(() => {
        if (document.getElementById(this.idInputNumber)) {
          document.getElementById(this.idInputNumber).lastElementChild.prepend(span);
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .width-div {
    ::v-deep .el-input-number--mini {
      width: 100%;
      line-height: 24px;
      .el-input__inner {
        height: 24px;
        line-height: 24px;
        padding-left: 20px;
        padding-right: 40px;
      }
      .el-input-number__decrease {
        line-height: 11px;
        top: 12px;
        height: 11px;
      }
      .el-input-number__increase {
        height: 12px;
        line-height: 12px;
      }
      .el-input__suffix {
        right: 32px;
      }
    }
  }
}
</style>
