<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div" :class="{'evenlySplit' : !!node.data.evenlySplit}">
      <el-radio-group v-model="value" size="mini">
        <el-radio-button
          v-for="(item, index) in node.data.options"
          :label="item.value"
          :key="item.label + index"
          :title="item.label"
          >
          <i :class="item.src"></i>
          </el-radio-button>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlIconRadio'
}
</script>

<style lang="scss" scoped>
.config-control {
  .evenlySplit {
    ::v-deep .el-radio-group {
      display: flex;
      flex-direction: row;
      .el-radio-button {
        flex: 1;
        margin: 0 4px;
      }
    }
    ::v-deep .el-radio-button--mini .el-radio-button__inner {
      width: 100%;
    }
  }
  ::v-deep .el-radio-button {
    margin-right: 4px;
  }
  ::v-deep .el-radio-button--mini .el-radio-button__inner {
    padding: 5px 9px;
    border: 1px solid #393b4a;
  }
  ::v-deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0;
  }
  ::v-deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 0;
  }
  ::v-deep .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    box-shadow: unset;
  }
}
</style>
