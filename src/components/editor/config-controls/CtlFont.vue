<template>
  <div class="config-control">
    <i class="config-icon"
      :class="[defaultValue.show ? 'icon-visible' : 'icon-hide']"
      v-if="node.data.enableHide"
      @click="defaultValue.show = !defaultValue.show;"></i>
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div" :class="{'disabled-container': !defaultValue.show}">
      <div class="component-div" v-for="it in components" :key="it">
        <el-input-number
          v-model="defaultValue[it]"
          controls-position="right"
          size="mini"
          :id="it + idInputNumber"
          :step="1"
          :min="0"
          @change="change()"
          v-if="it === 'fontSize'"
          >
        </el-input-number>
        <el-select
          v-model="defaultValue[it]"
          size="mini"
          v-if="it === 'fontFamily'"
          @change="change()"
          filterable
          >
          <el-option
            v-for="(item, index) in fontFamily.options"
            :key="item.label + index"
            :label="item.label"
            :value="item.value"
            >
          </el-option>
        </el-select>
        <el-select
          v-model="defaultValue[it]"
          size="mini"
          v-if="it === 'fontWeight'"
          @change="change()"
          >
          <el-option
            v-for="(item, index) in fontWeight.options"
            :key="item.label + index"
            :label="item.label"
            :value="item.value"
            >
          </el-option>
        </el-select>
        <div class="component-color">
          <el-input v-model="defaultValue[it]" size="mini" v-if="it === 'color'" @change="change()">
          </el-input>
          <el-color-picker
            v-model="defaultValue[it]"
            show-alpha
            size='mini'
            :predefine="historyColor"
            v-if="it === 'color'"
            @change="change()"
            >
          </el-color-picker>
        </div>
        <span class="text-name">{{textNanme[it]}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getFontList } from '@/api/workspace';
export default {
  name: 'CtlFont',
  data () {
    return {
      userId: 'd0438698030d3e206cf55f8e1005e996',
      // input 输入框添加id
      idInputNumber: '-id-input-line-' + new Date().getTime(),
      // 默认配置模块
      components: ['fontFamily', 'fontWeight', 'fontSize', 'color'],
      textNanme: {
        fontFamily: '字体',
        fontWeight: '字体粗细',
        fontSize: '字号',
        color: '颜色'
      },
      defaultValue: {
        fontFamily: 'simSun',
        fontWeight: 400,
        fontSize: 12,
        color: '#f40',
        show: true
      },
      fontFamily: {
        options: [{ value: '思源黑体Medium', label: '思源黑体Medium' },
          { value: 'simSun', label: '宋体' },
          { value: 'simHei', label: '黑体' }]
      },
      fontWeight: {
        options: [{ value: 'normal', label: 'normal' },
          { value: 'bold', label: 'bold' },
          // { value: 'bolder', label: 'bolder' },
          { value: 'lighter', label: 'lighter' }
          // { value: '100', label: '100' },
          // { value: '200', label: '200' },
          // { value: '300', label: '300' },
          // { value: '400', label: '400' },
          // { value: '500', label: '500' },
          // { value: '600', label: '600' },
          // { value: '700', label: '700' },
          // { value: '800', label: '800' },
          // { value: '900', label: '900' }
        ]
      }
    }
  },
  created () {
    this.userId = localStorage.getItem('userId');
    if (this.node.data.components) {
      this.components = this.node.data.components;
    }
    if (this.value) {
      for (const key of Object.keys(this.value)) {
        this.defaultValue[key] = this.value[key];
      }
      if (this.defaultValue.fontFamily) {
        if (this.defaultValue.fontFamily.includes('systemFont')) {
          this.defaultValue.fontFamily = '系统自带字体';
        }
        // console.log('默认字体--修改后', this.defaultValue.fontFamily);
      }
      this.defaultValue.show = true;
    }
    this.components.forEach(item => {
      if (item === 'fontSize') {
        // 后缀
        this.addSuffix('fontSize', 'px');
      }
    });
    if (window.cangjie && window.cangjie.fonts.length > 0) {
      this.setFontListFun(window.cangjie.fonts);
    } else {
      this.getFontListFun();
    }
  },
  computed: {
    historyColor () {
      return this.$store.state.editor.historyColor.font;
    }
  },
  methods: {
    async getFontListFun () {
      const res = await getFontList({ userId: this.userId });
      if (res.success) {
        this.fontFamily.options = [];
        res.data.forEach(item => {
          if (item.enable) {
            this.fontFamily.options.push({
              value: item.fontName,
              label: item.fontName
            });
          }
        });
      }
    },
    setFontListFun (list) {
      this.fontFamily.options = [];
      list.forEach(item => {
        if (item.enable) {
          this.fontFamily.options.push({
            value: item.fontName,
            label: item.fontName
          });
        }
      });
      this.fontFamily.options.unshift({
        value: '系统自带字体',
        label: '系统自带字体'
      });
    },
    addSuffix (divid, str) {
      const span = document.createElement('span');
      const innerspan = document.createElement('span');
      const textspan = document.createElement('span');
      // 添加elementUI 内置 class
      span.setAttribute('class', 'el-input__suffix');
      innerspan.setAttribute('class', 'el-input__suffix-inner');

      span.append(innerspan);
      innerspan.append(textspan);
      textspan.append(str);

      this.$nextTick(() => {
        if (document.getElementById(divid + this.idInputNumber)) { // 非空判断
          document.getElementById(divid + this.idInputNumber).lastElementChild.prepend(span);
        }
      });
    },
    change () {
      const tempVal = {};
      this.components.forEach(item => {
        tempVal[item] = this.defaultValue[item];
        if (item === 'color') {
          this.$store.commit('editor/setHistoryColor', { // 收集选择过的颜色
            type: 'font',
            data: this.defaultValue[item]
          });
        }
      });
      if (this.node.data.enableHide) {
        tempVal.show = this.defaultValue.show;
      } else {
        delete tempVal.show;
      }
      if (tempVal.fontFamily === '系统自带字体') {
        tempVal.fontFamily = 'PingFang SC, Helvetica Neue, Helvetica, STHeitiSC-Light, WOL_SB, Segoe UI Semibold, Segoe UI, Tahoma, Helvetica, Microsoft Yahei, systemFont, sans-serif';
      }
      this.value = tempVal;
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  .width-div {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  .component-div {
    position: relative;
    // width: calc(100% - 4px);
    margin: 0 2px;
    // margin-bottom: 4px;
    .text-name {
      height: 24px;
      line-height: 24px;
      text-align: center;
      color: #606266;
    }
    ::v-deep .el-input-number--mini {
      width: 100%;
      line-height: 24px;
      .el-input__inner {
        height: 24px;
        line-height: 24px;
      }
      .el-input-number__decrease {
        line-height: 12px;
      }
      .el-input-number__increase {
        height: 12px;
        line-height: 12px;
      }
      .el-input__suffix {
        right: 32px;
      }
    }
    ::v-deep .el-select--mini {
      width: 100%;
      .el-input__icon {
        line-height: 24px;
      }
    }
    .component-color {
      display: flex;
      width: 100%;
      ::v-deep .el-input__inner {
        padding: 0 5px;
      }
    }
    ::v-deep .el-input__inner {
      height: 24px;
      line-height: 24px;
    }
    ::v-deep .el-color-picker {
      margin-left: 4px;
      height: 24px;
      .el-color-picker__trigger {
        height: 24px;
        width: 24px;
      }
    }
  }
  .disabled-container {
    opacity: 0.6; // 透明度
    pointer-events: none; // 屏蔽鼠标事件
    cursor: not-allowed; // 鼠标样式
  }
}
</style>
