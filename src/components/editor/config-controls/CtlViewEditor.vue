<template>
  <div class="config-control">
    <div
      class="config-title nowrap"
      :class="{ 'config-title65': node.depth > 1 }"
      :title="node.data.name"
    >
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div
          slot="content"
          :style="{ 'max-width': '15rem' }"
          v-html="node.data.desc"
        ></div>
        <span class="icon icon2 el-icon-question"></span> </el-tooltip
      >{{ node.data.name }}
    </div>
    <div class="width-div">
      <el-button
        class="upload-texts"
        type="primary"
        size="mini"
        @click="openEditor()"
        >{{btnName}}</el-button>
    </div>
    <dialog-component
      :workspaceId="screenInfo.workspaceId"
      :componentId="currentCom.id"
      :title="dialogTitle"
      :chartAttr="currentCom.attr"
      @confirm="confirm"
      ref="refDialogComponent">
    </dialog-component>
  </div>
</template>

<script>
import DialogComponent from './DialogComponent.vue'
import { mapState, mapGetters } from 'vuex';
export default {
  name: 'CtlViewEditor',
  components: {
    DialogComponent
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      screenFilters: state => state.editor.screenFilters,
      screenComs: state => state.editor.screenComs
    }),
    ...mapGetters('editor', [
      'currentCom'
    ])
  },
  data () {
    return {
      btnName: this.node.data.btnName || '打开编辑器',
      dialogTitle: this.node.data.dialogTitle
    }
  },
  // mounted () {
  //   this.initEditor();
  // },
  methods: {
    // 提交 返回数据
    confirm (data) {
      // console.log('确定返回数据', data);
      this.value = data;
    },
    // 打开编辑页
    openEditor () {
      // console.log('视角信息 - currentCom.attr', this.currentCom.attr)
      this.$refs.refDialogComponent.openDialog();
    },
    initEditor () {
      // console.log('初始化', this.value);
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
