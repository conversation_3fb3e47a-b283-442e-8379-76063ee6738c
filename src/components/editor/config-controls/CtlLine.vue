<template>
  <div class="config-control">
    <i class="config-icon"
      :class="[defaultValue.show ? 'icon-visible' : 'icon-hide']"
      v-if="node.data.enableHide"
      @click="defaultValue.show = !defaultValue.show;"></i>
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div" :class="{'disabled-container': !defaultValue.show}">
      <div class="component-div" v-for="it in components" :key="it">
        <el-input-number
          v-model="defaultValue[it]"
          controls-position="right"
          size="mini"
          :id="it + idInputNumber"
          :step="1"
          @change="change()"
          v-if="it === 'width'"
          >
        </el-input-number>
        <el-radio-group v-model="defaultValue[it]" size="mini" v-if="it === 'curve'" @change="change()">
          <el-radio-button
            v-for="(item, index) in curve.options"
            :label="item.value"
            :key="item.label + index"
            :title="item.label"
            >
            <i :class="item.src"></i>
            </el-radio-button>
        </el-radio-group>
        <el-select
          v-model="defaultValue[it]"
          size="mini"
          v-if="it === 'style'"
          @change="change()"
          >
          <el-option
            v-for="(item, index) in style.options"
            :key="item.label + index"
            :label="item.label"
            :value="item.value"
            >
          </el-option>
        </el-select>
        <div class="component-color">
          <el-input v-model="defaultValue[it]" size="mini" v-if="it === 'color'" @change="change()">
          </el-input>
          <el-color-picker
            v-model="defaultValue[it]"
            show-alpha
            size='mini'
            v-if="it === 'color'"
            @change="change()"
            >
          </el-color-picker>
        </div>
        <span class="text-name">{{textNanme[it]}}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlLine',
  data () {
    return {
      // input 输入框添加id
      idInputNumber: '-id-input-line-' + new Date().getTime(),
      // 默认配置模块
      components: ['width', 'style', 'curve', 'color'],
      textNanme: {
        width: '粗细',
        style: '类型',
        curve: '曲线类型',
        color: '颜色'
      },
      defaultValue: {
        width: 1,
        style: 'solid',
        curve: 'smooth',
        color: '#fff',
        show: true
      },
      curve: {
        options: [{ value: 'smooth', label: '曲线', src: 'icon-curve' },
          { value: 'polyline', label: '折线', src: 'icon-line' }]
      },
      style: {
        options: [{ value: 'solid', label: '实线' },
          { value: 'dotted', label: '虚线' }]
      }
    }
  },
  created () {
    if (this.node.data.components) {
      this.components = this.node.data.components;
    }
    if (this.value) {
      for (const key of Object.keys(this.value)) {
        this.defaultValue[key] = this.value[key];
      }
      this.defaultValue.show = true;
    }
    this.components.forEach(item => {
      if (item === 'width') {
        // 后缀
        this.addSuffix('width', 'px');
      }
    });
  },
  methods: {
    addSuffix (divid, str) {
      const span = document.createElement('span');
      const innerspan = document.createElement('span');
      const textspan = document.createElement('span');
      // 添加elementUI 内置 class
      span.setAttribute('class', 'el-input__suffix');
      innerspan.setAttribute('class', 'el-input__suffix-inner');

      span.append(innerspan);
      innerspan.append(textspan);
      textspan.append(str);

      this.$nextTick(() => {
        if (document.getElementById(divid + this.idInputNumber)) {
          document.getElementById(divid + this.idInputNumber).lastElementChild.prepend(span);
        }
      });
    },
    change () {
      const tempVal = {};
      this.components.forEach(item => {
        tempVal[item] = this.defaultValue[item];
      });
      if (this.node.data.enableHide) {
        tempVal.show = this.defaultValue.show;
      } else {
        delete tempVal.show;
      }
      this.value = tempVal;
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  .width-div {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  .component-div {
    position: relative;
    // width: calc(100% - 4px);
    margin: 0 2px;
    // margin-bottom: 4px;
    .text-name {
      height: 24px;
      line-height: 24px;
      text-align: center;
      color: #606266;
    }
    ::v-deep .el-input-number--mini {
      width: 100%;
      line-height: 24px;
      .el-input__inner {
        height: 24px;
        line-height: 24px;
      }
      .el-input-number__decrease {
        line-height: 12px;
      }
      .el-input-number__increase {
        height: 12px;
        line-height: 12px;
      }
      .el-input__suffix {
        right: 32px;
      }
    }
    ::v-deep .el-select--mini {
      width: 100%;
      .el-input__icon {
        line-height: 24px;
      }
    }
    .component-color {
      display: flex;
      width: 100%;
      ::v-deep .el-input__inner {
        padding: 0 5px;
      }
    }
    ::v-deep .el-input__inner {
      height: 24px;
      line-height: 24px;
    }
    ::v-deep .el-color-picker {
      margin-left: 4px;
      height: 24px;
      .el-color-picker__trigger {
        height: 24px;
        width: 24px;
      }
    }
  }
  .disabled-container {
    opacity: 0.6; // 透明度
    pointer-events: none; // 屏蔽鼠标事件
    cursor: not-allowed; // 鼠标样式
  }
  .icon-curve {
    display: block;
    width: 16px;
    height: 16px;
    background: url('../../../assets/img/svg/curve.svg') bottom center no-repeat;
  }
  .icon-line {
    display: block;
    width: 16px;
    height: 16px;
    background: url('../../../assets/img/svg/line.svg') bottom center no-repeat;
  }
  ::v-deep .el-radio-button {
    margin-right: 4px;
  }
  ::v-deep .el-radio-button--mini .el-radio-button__inner {
    padding: 3px 8px;
    border: 1px solid #393b4a;
  }
  ::v-deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0;
  }
  ::v-deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 0;
  }
  ::v-deep .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    box-shadow: unset;
  }
}
</style>
