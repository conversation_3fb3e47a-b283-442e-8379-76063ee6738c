<template>
  <div class="config-control">
    <i class="config-icon"
      :class="[showSet ? 'icon-visible' : 'icon-hide']"
      v-if="node.data.enableHide"
      @click="showSet = !showSet;"></i>
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div" :class="{'disabled-container': !showSet}">
      <div class="padding-border-outside">
        <div class="padding-top">
          <el-input-number
            v-model="padding.top"
            :min="node.data.min || 0"
            :max="node.data.max || 99999999"
            size="mini"
            @focus="focusInputPadding"
            @blur="keyupSubmitPadding('T')"
            @keyup.enter.native="keyupSubmitPadding('T')"
            >
          </el-input-number>
        </div>
        <div class="padding-middle">
          <div>
            <el-input-number
              v-model="padding.left"
              :min="node.data.min || 0"
              :max="node.data.max || 99999999"
              size="mini"
              @focus="focusInputPadding"
              @blur="keyupSubmitPadding('L')"
              @keyup.enter.native="keyupSubmitPadding('L')"
              >
            </el-input-number>
          </div>
          <div class="border-within"></div>
          <div>
            <el-input-number
              v-model="padding.right"
              :min="node.data.min || 0"
              :max="node.data.max || 99999999"
              size="mini"
              @focus="focusInputPadding"
              @blur="keyupSubmitPadding('R')"
              @keyup.enter.native="keyupSubmitPadding('R')"
              >
            </el-input-number>
          </div>
        </div>
        <div class="padding-top">
          <el-input-number
            v-model="padding.bottom"
            :min="node.data.min || 0"
            :max="node.data.max || 99999999"
            size="mini"
            @focus="focusInputPadding"
            @blur="keyupSubmitPadding('B')"
            @keyup.enter.native="keyupSubmitPadding('B')"
            >
          </el-input-number>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlPadding',
  data () {
    return {
      showSet: true,
      padding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
      },
      oldPadding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
      }
    }
  },
  created () {
    this.initInputPading();
  },
  methods: {
    initInputPading () {
      this.padding.top = this.value.top;
      this.padding.right = this.value.right;
      this.padding.bottom = this.value.bottom;
      this.padding.left = this.value.left;
    },
    focusInputPadding () {
      this.oldPadding.top = this.padding.top;
      this.oldPadding.right = this.padding.right;
      this.oldPadding.bottom = this.padding.bottom;
      this.oldPadding.left = this.padding.left;
    },
    keyupSubmitPadding (type) {
      switch (type) {
        case 'T': {
          if (this.oldPadding.top === this.padding.top) {
            return;
          }
          break;
        }
        case 'R': {
          if (this.oldPadding.right === this.padding.right) {
            return;
          }
          break;
        }
        case 'B': {
          if (this.oldPadding.bottom === this.padding.bottom) {
            return;
          }
          break;
        }
        case 'L': {
          if (this.oldPadding.left === this.padding.left) {
            return;
          }
          break;
        }
        default:
          break;
      }
      // if (!this.node.data.enableHide) {
      //   delete this.showSet;
      // }
      this.value = _.cloneDeep(this.padding);
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  .width-div {
    flex: 1;
    .padding-border-outside {
      border: 1px solid #393b4a;
      width: 100%;
      padding: 4px;
      ::v-deep .el-input-number--mini {
        margin: 0 !important;
        width: 60px;
        .el-input__inner {
          padding: 0 !important;
        }
      }
      ::v-deep .el-input-number__decrease {
        display: none;
      }
      ::v-deep .el-input-number__increase {
        display: none;
      }
      .padding-top {
        text-align: center;
      }
      .padding-middle {
        display: flex;
        line-height: 40px;
        margin: 4px 0;
      }
      .border-within {
        display: block;
        height: 40px;
        border: 1px dashed #393b4a;
        flex: 1;
        margin: 0 4px;
      }
    }
  }

  .disabled-container {
    opacity: 0.6; // 透明度
    pointer-events: none; // 屏蔽鼠标事件
    cursor: not-allowed; // 鼠标样式
  }
}
</style>
