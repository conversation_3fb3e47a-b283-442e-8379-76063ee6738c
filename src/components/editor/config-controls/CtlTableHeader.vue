<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}
    </div>
    <div class="width-div">
      <el-button class="upload-texts" type="primary" size="mini" @click="openDialog()">
        点击设置
      </el-button>
    </div>
    <el-dialog
      :visible.sync="showDialog"
      :before-close="close"
      title="表头设置"
      width="60%"
      append-to-body
      :close-on-click-modal="false"
      top="0"
    >
      <div class="content">
        <div class="content-btn" style="margin-bottom: 8px;">
          <el-button size="mini" type="primary" @click="addfield()">新 增</el-button>
          <el-button size="mini" @click="refresh()">刷 新</el-button>
        </div>
        <dragTreeTable
          ref="table"
          :data="treeData"
          @drag="onTreeDataChange"
          resize
          fixed
          :isdraggable="true">
          <template #selection="{row}">
            <el-input
              v-if="row.id === clickTitleId"
              style="flex: 1;"
              ref="editTitleInput"
              v-model="row.title"
              size="mini"
              placeholder="请输入表头名称"
              @blur="inputTitleBlur"/>
            <span
              v-else
              @dblclick="cellTitleDbClick(row)"
              style="height:100%;width:100%;display:inline-block;">
              {{ row.title }}
            </span>
          </template>
          <template #edit="{row}">
            <el-input
              v-if="row.id === clickFieldId"
              style="flex: 1;"
              ref="editFieldInput"
              v-model="row.field"
              size="mini"
              placeholder="请输入表头字段"
              @blur="inputFieldBlur"/>
            <span
              v-else
              @dblclick="cellFieldDbClick(row)"
              style="height:100%;width:100%;display:inline-block;">
              {{ row.field }}
            </span>
          </template>
          <template #switch="{row}">
            <el-switch v-model="row.show"></el-switch>
          </template>
          <template #action="{row}">
            <el-button size="mini" type="text" :disabled="delDisable(row)" @click="deleteRow(row)">删除</el-button>
          </template>
        </dragTreeTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="close()">取 消</el-button>
        <el-button size="mini" type="primary" @click="confirm()" :loading="loading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import dataUtil from '@/utils/data'
import { randomStr } from '@/utils/base'
import dragTreeTable from '@/lib/DragTreeTable/dragTreeTable.vue'

export default {
  name: 'CtlTableHeader',
  components: { dragTreeTable },
  data () {
    return {
      showDialog: false,
      loading: false,
      rowKey: 'id',
      treeProps: { children: 'children', hasChildren: 'hasChildren' },
      treeData: {
        columns: [],
        lists: []
      },
      flattArray: [],
      clickTitleId: null, // 当前点击数据id
      clickFieldId: null
    }
  },
  created () {},
  mounted () {},
  computed: {
    ...mapState({
      screenComs: state => state.editor.screenComs,
      comsData: state => state.editor.comsData,
      screenFilters: state => state.editor.screenFilters
    }),
    ...mapGetters('editor', ['currentCom']),
    // 获取过滤器以后数据唯一字段
    uniKeys () {
      const data = this.getFilterData()
      const uniKeys = data.reduce((pre, cur) => {
        const keys = pre.concat(Object.keys(cur))
        return [...new Set(keys)]
      }, [])
      return uniKeys
    },
    delDisable () {
      return function (row) {
        return this.uniKeys.includes(row.field)
      }
    }
  },
  methods: {
    openDialog () {
      this.initSetting()
      this.showDialog = true
    },
    confirm () {
      this.value = this.treeData.lists
      this.showDialog = false
    },
    close () {
      this.$confirm('确认关闭？', '', {
        type: 'warning',
        closeOnClickModal: false,
        iconClass: 'message-warning'
      }).then(() => {
        this.showDialog = false
      })
    },
    // 控制input显示 row 当前行 column 当前列
    cellTitleDbClick (row) {
      this.clickTitleId = row.id
      this.$nextTick(() => {
        this.$refs.editTitleInput.focus()
      })
    },
    cellFieldDbClick (row) {
      if (this.delDisable(row)) return
      this.clickFieldId = row.id
      this.$nextTick(() => {
        this.$refs.editFieldInput.focus()
      })
    },
    // 失去焦点初始化
    inputTitleBlur () {
      this.clickTitleId = null
    },
    inputFieldBlur () {
      this.clickFieldId = null
    },
    onTreeDataChange (list) {
      this.treeData.lists = list
    },
    // 初始化设置
    initSetting () {
      let lists = []
      if (this.value && Array.isArray(this.value) && this.value.length) {
        lists = _.cloneDeep(this.value)
      } else {
        lists = this.uniKeys.map((item, index) => {
          return {
            field: item,
            title: item,
            show: true,
            id: `${randomStr(10)}_${index}`,
            pid: 0,
            lists: []
          }
        })
      }
      const columns = [
        {
          type: 'selection',
          title: '表头名称',
          field: 'title',
          flex: 1,
          align: 'left',
          index: 0
        },
        {
          type: 'edit',
          title: '表头字段',
          field: 'field',
          flex: 1,
          align: 'left',
          index: 1
        },
        {
          title: '是否显示',
          field: 'show',
          align: 'center',
          width: 90,
          index: 2,
          type: 'switch'
        },
        {
          title: '操作',
          type: 'action',
          width: 90,
          align: 'center',
          index: 3
        }
      ]
      this.treeData = { columns, lists }
    },
    // 删除
    deleteRow (row) {
      this.$confirm('确认删除？', '', {
        type: 'warning',
        closeOnClickModal: false,
        iconClass: 'message-warning'
      }).then(() => {
        const data = _.cloneDeep(this.treeData.lists)
        const item = this.getTreeDataItem(data, row.id)
        this.treeData.lists = this.getFilterTreeDate(data, row.id)
        item.lists && item.lists.length && this.refresh()
      })
    },
    // 新增
    addfield: _.throttle(function () {
      const obj = {
        field: `${randomStr(5)}_c`,
        title: '',
        show: true,
        id: `${randomStr(10)}_c`,
        pid: 0,
        lists: []
      }
      this.treeData.lists.unshift(obj)
      this.cellTitleDbClick(obj)
    }, 1000),
    // 刷新
    refresh () {
      const fields = this.getTableFields(this.treeData.lists)
      this.uniKeys.length && this.uniKeys.forEach((item, index) => {
        if (!fields.includes(item)) {
          const obj = {
            field: item,
            title: item,
            show: true,
            id: `${randomStr(10)}_${index}`,
            pid: 0
          }
          this.treeData.lists.unshift(obj)
        }
      })
    },
    // 获取过滤器以后的数据
    getFilterData () {
      const comCfg = this.screenComs[this.currentCom.id]
      let filter = []
      if (!_.isEmpty(comCfg)) {
        const filters = comCfg.dataConfig.dataResponse.filters
        if (!filters.enable) {
          filter = []
        } else {
          filter = _.filter(filters.list, { enable: true }).map(({ id }) => this.screenFilters[id])
        }
      }
      const sourceData = this.comsData[this.currentCom.id]
      const filterData = dataUtil.filterData(sourceData || [], filter)
      return filterData
    },
    // 获取当前表格字段
    getTableFields (data) {
      return data.reduce((acc, curr) => {
        acc.push(curr.field)
        if (curr.lists && curr.lists.length) {
          acc = acc.concat(this.getTableFields(curr.lists))
        }
        return acc
      }, [])
    },
    // 根据id过滤树结构
    getFilterTreeDate (data, id) {
      const newTree = data.filter(x => x.id !== id)
      newTree.forEach(x => x.lists && x.lists.length > 0 && (x.lists = this.getFilterTreeDate(x.lists, id)))
      return newTree
    },
    getTreeDataItem (data, id) {
      for (let i = 0; i < data.length; i++) {
        const x = data[i]
        if (x.id === id) {
          return x
        } else {
          const res = x.lists && x.lists.length > 0 && this.getTreeDataItem(x.lists, id)
          if (res) return res
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .dialog-footer {
    ::v-deep .el-button {
      padding: 4px 12px;
    }
  }
}
</style>
