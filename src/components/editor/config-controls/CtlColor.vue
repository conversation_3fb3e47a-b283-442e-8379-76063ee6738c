<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <el-input v-model="tempValue" @change="changeValue()" size="mini">
      </el-input>
      <el-color-picker
        v-model="tempValue"
        @change="changeValue()"
        :predefine="historyColor"
        show-alpha
        size='mini'
        >
      </el-color-picker>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlColor',
  data () {
    return {
      tempValue: this.value
    }
  },
  computed: {
    historyColor () {
      return this.$store.state.editor.historyColor.background;
    }
  },
  created () {
    this.tempValue = this.value;
  },
  methods: {
    changeValue () {
      this.value = this.tempValue;
      this.$store.commit('editor/setHistoryColor', { // 收集选择过的颜色
        type: 'background',
        data: this.tempValue
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.width-div {
  display: flex;
  align-items: center;
  ::v-deep .el-color-picker {
    margin-left: 4px;
    height: 24px;
    .el-color-picker__trigger {
      height: 24px;
      width: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
}
</style>
