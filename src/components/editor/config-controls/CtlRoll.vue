<template>
<div class="config-control">
  <el-collapse :value="activeName">
    <el-collapse-item :disabled="node.data.enableHide ? !defaultValue.show : false" name="simple">
      <template slot="title">
      {{node.data.name}}
      <i class="config-icon"
        :class="[defaultValue.show ? 'icon-visible' : 'icon-hide']"
        v-if="node.data.enableHide"
        @click="activeItem()"></i>
        <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
          <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
          </div>
          <span class="icon icon2 el-icon-question"></span>
        </el-tooltip>
      </template>
      <div class="control-flex" v-if="!node.data.enableHide">
        <div class="config-title nowrap config-title65">开启滚动</div>
        <div class="width-div">
          <el-switch
            v-model="defaultValue.show"
            active-color="#409EFF"
            inactive-color="#5F5F5F"
            @change="changeConfig()"
            >
          </el-switch>
        </div>
      </div>
      <div class="control-flex" v-if="defaultValue.show">
        <div class="config-title nowrap config-title65">滚动方式</div>
        <div class="width-div">
          <el-select
            v-model="defaultValue.rollType"
            filterable
            placeholder="请选择"
            size="mini"
            @change="changeConfig()"
            >
            <el-option
              v-for="(item, index) in rollOptions"
              :key="item.value + index"
              :label="item.label"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="control-flex" v-if="defaultValue.show && defaultValue.rollType !== 'smooth'">
        <div class="config-title nowrap config-title65">滚动间隔</div>
        <div class="width-div">
          <el-input-number
            v-model="defaultValue.rollTimer"
            controls-position="right"
            :min="500"
            size="mini"
            @change="changeConfig()"
            >
          </el-input-number>
        </div>
      </div>
      <div class="control-flex" v-if="defaultValue.show && defaultValue.rollType === 'smooth' && currentCom.comName === 'data-set-listSwiper'">
        <div class="config-title nowrap config-title65">滚动速率</div>
        <div class="width-div">
          <el-input-number
            v-model="defaultValue.rollSpeed"
            controls-position="right"
            :min="1"
            :max="200"
            size="mini"
            @change="changeConfig()"
            >
          </el-input-number>
        </div>
      </div>
    </el-collapse-item>
  </el-collapse>
</div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'CtlRoll',
  computed: {
    ...mapGetters('editor', ['currentCom']),
    activeName: {
      get: function () {
        return this.currentCom ? this.currentCom.other.configType : 'other';
      },
      set: function (val) {
      }
    }
  },
  data () {
    return {
      // activeName: '',
      defaultValue: {
        show: true,
        rollType: 'smooth',
        rollTimer: 1500,
        rollSpeed: 5
      },
      rollOptions: [
        {
          value: 'smooth',
          label: '平滑滚动'
        },
        {
          value: 'oneWay',
          label: '单行滚动'
        },
        {
          value: 'page',
          label: '整页滚动'
        }
      ]
    }
  },
  created () {
    this.defaultValue = _.cloneDeep(this.value);
    // if (!this.node.data.enableHide) {
    //   this.activeName = 'default';
    // }
  },
  methods: {
    activeItem () {
      this.defaultValue.show = !this.defaultValue.show;
      if (this.node.data.enableHide) {
        if (!this.defaultValue.show) {
          this.activeName = '';
        }
        this.changeConfig();
      }
    },
    changeConfig () {
      this.value = _.cloneDeep(this.defaultValue);
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 16px 0;
    }
  }
  ::v-deep .el-input-number--mini {
    line-height: 24px;
    width: 100%;
    .el-input__inner {
      height: 24px;
      line-height: 24px;
      padding-left: 2px;
      padding-right: 30px;
    }
    .el-input-number__decrease {
      line-height: 11px;
      top: 12px;
      height: 11px;
    }
    .el-input-number__increase {
      height: 12px;
      line-height: 12px;
    }
  }
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
    .el-input__inner {
      height: 24px;
      line-height: 24px;
    }
  }
}
</style>
