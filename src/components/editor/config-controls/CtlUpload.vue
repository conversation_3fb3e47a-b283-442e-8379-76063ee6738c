<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <div class="image-item">
        <div class="image-operation" v-if="value !== null">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :data="isModelFile ? {isModelFile: true}: null"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload">
            <span class="texts">替换</span>
          </el-upload>
          <span v-html="nbsp"></span> | <span v-html="nbsp"></span>
          <span class="texts" @click="delImage()">删除</span>
        </div>
        <div style="width: 100%; height: 80px;background: rgba(204, 219, 255, 0.06);border: 1px solid #393b4a;">
          <el-upload
            class="text-center"
            v-if="value === null"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload">
            <span class="upload-text">请点击上传</span>
          </el-upload>
          <div class="text-center-name" v-if="value !== null">
            <!-- <div class="icon-box"></div> -->
            <div class="name-style" :title="fileName" @click="downloadField($event, fileName)">{{fileName}}</div>
          </div>
        </div>
      </div>
      <div class="file-type">
        <span class="config-title" >文件类型</span>
         <el-radio-group v-model="isModelFile">
            <el-radio :label="false">普通文件</el-radio>
            <el-radio :label="true">tgz项目包</el-radio>
          </el-radio-group>
      </div>
    </div>
  </div>
</template>

<script>
import { replaceUrl, getHttpHeaders } from '@/utils/base';
export default {
  name: 'CtlUpload',
  data () {
    return {
      nbsp: '&nbsp',
      fileType: 'JSON',
      isModelFile: false,
      fileName: '', // 修改需要显示文件名称
      uploadUrl: replaceUrl(process.env.VUE_APP_SERVER_URL + '/api/common/upload'),
      uploadHeaders: getHttpHeaders()
      // filefolder: { filefolder: 'otherFiles' } //  此参数在请求体里，需要后端配合改  :data="filefolder"
    }
  },
  created () {
    if (this.node.data.getData) {
      this.uploadUrl = replaceUrl(process.env.VUE_APP_SERVER_URL + '/api/mapdata/upload');
      // this.filefolder = { filefolder: 'mapFiles' };
    } else {
      this.uploadUrl = replaceUrl(process.env.VUE_APP_SERVER_URL + '/api/common/upload');
      // this.filefolder = { filefolder: 'otherFiles' };
    }
    if (this.value) {
      const tempArr = this.value.split('/');
      if (tempArr.length > 1) {
        this.fileName = tempArr[tempArr.length - 1];
      } else {
        this.fileName = 'jsonData';
      }
    }
  },
  methods: {
    delImage () {
      this.value = null;
      this.fileName = '';
    },
    downloadField (e, name) {
      if (this.node.data.getData) {
        // console.log('当前文件对象数据:', this.value)
        this.saveJson(this.value, 'jsonData');
      } else {
        // window.open(this.value);
        const el = document.createElement('a');
        el.style.display = 'none';
        el.setAttribute('target', '_blank');
        el.setAttribute('download', name);
        el.href = this.value;
        document.body.appendChild(el);
        el.click();
        document.body.removeChild(el);
      }
    },
    beforeAvatarUpload (file) {
      // eslint-disable-next-line
      // const isLt30M = file.size / 1024 / 1024 < 100;
      // if (!isLt30M) {
      //   this.$message.error('上传文件大小不能超过 100MB!');
      // }
      // return isLt30M;
      return true;
    },
    handleAvatarSuccess (res, file) {
      if (this.node.data.getData) {
        this.value = res.data.mapData;
        this.fileName = 'jsonData';
      } else {
        this.value = replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.url);
        const tempArr = res.data.url.split('/');
        if (tempArr.length > 1) {
          this.fileName = tempArr[tempArr.length - 1];
        }
      }
    },
    saveJson (jsonData, name) {
      const strData = typeof jsonData !== 'object' ? jsonData : JSON.stringify(jsonData);
      const uri = 'data:application/json;charset=utf-8,' + encodeURIComponent(strData);
      const link = document.createElement('a');
      link.href = uri;

      link.style = 'visibility:hidden';
      link.download = name + '.json';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
  .image-operation {
    text-align: center;
    // margin-top: 8px;
    // margin-left: 8px;
    margin-left: 88px;
    line-height: 24px;
    margin-top: 24px;
    cursor: pointer;
    display: flex;
    position: absolute;
    z-index: 1;
    .texts {
      color: #a6a6a6;
      &:hover {
        color: #409eff;
      }
    }
  }
  .image-item {
    .text-center-name {
      line-height: 24px;
      margin-top: 56px;
      color: #606266;
      text-align: center;
      .name-style {
        max-width: 230px;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 4px;
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
      }
    }
    .text-center {
      line-height: 80px;
      text-align: center;
      height: 80px;
      .icon-box {
        width: 20px;
        height: 25px;
        background: linear-gradient(to bottom, #999999a8 0%, #999999a8 0%, transparent 20%, transparent 40%, #999999a8 40%, #999999a8 40%, transparent 60%, transparent 80%, #999999a8 100%);
        outline: 1px solid #393b4a;
        outline-offset: 6px;
        margin: auto;
        top: 30px;
        position: relative;
      }
    }
    .text-icon {
      font-size: 54px;
      font-family: monospace;
      font-weight: 600;
    }
  }
  .file-type{
    .el-radio{
      margin-right: 5px;
    }
    ::v-deep .el-radio__label {
      font-size: 12px;
      padding-left: 2px;
    }
  }
}
</style>
