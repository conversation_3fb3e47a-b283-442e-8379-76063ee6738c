<template>
<div class="dialog-component-root">
  <el-dialog
    class="view-editor-dialog"
    :visible.sync="showComponentVisible"
    :before-close="close"
    :title="title"
    :width="`${chartAttr.w}px`"
    append-to-body
    :close-on-click-modal="false"
    top="0"
    >
    <!-- 放缩 -->
    <!-- <div class="component-content" :style="{height: `${chartAttr.h}px`,'max-height': 'calc(100vh - 100px)'}"> -->
    <div class="component-content" :style="{height: `${chartAttr.h}px`}">
      <ViewCompNode
        :key="componentId"
        :workspaceId="workspaceId"
        :id="componentId"
        ref="refViewComNode"
        class="dialog-view-comp-node"
        type="com"/>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close()">取 消</el-button>
      <el-button type="primary" @click="confirm()" :loading="uploadLoading">确 定</el-button>
    </span>
  </el-dialog>
</div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'DialogComponent',
  props: {
    title: {
      type: String,
      default: '编辑器'
    },
    workspaceId: {
      type: Number,
      default: 0
    },
    componentId: {
      type: String,
      default: 'xxx'
    },
    platform: {
      type: String,
      default: 'pc'
    },
    // 属性
    chartAttr: {
      type: Object,
      require: true,
      default: () => {
        return {
          h: 600,
          w: 1000,
          lock: false
        }
      }
    }
  },
  provide: function () {
    return {
      filtersMap: () => this.screenFilters,
      screenComs: () => this.screenComs,
      permissionMap: () => null
    }
  },
  computed: {
    ...mapState({
      screenFilters: state => state.editor.screenFilters,
      screenComs: state => state.editor.screenComs
    })
  },
  data () {
    return {
      showComponentVisible: false,
      uploadLoading: false
    }
  },
  methods: {
    openDialog () {
      this.showComponentVisible = true;
      const scaleObj = this.transformScale();
      this.$nextTick(() => {
        if (this.$refs.refViewComNode) {
          if (scaleObj.isScale) {
            this.$refs.refViewComNode.$el.style.transform = `scale(${scaleObj.scale})`;
            this.$refs.refViewComNode.$el.style.left = `${scaleObj.left}px`;
            this.$refs.refViewComNode.$el.style.top = `${scaleObj.top}px`;
          } else {
            this.$refs.refViewComNode.$el.style.left = '0px';
            this.$refs.refViewComNode.$el.style.top = '54px';
          }
          // console.log('openDialog->', scaleObj)
        }
      });
    },
    transformScale () {
      const screenW = window.innerWidth;
      const screenH = window.innerHeight;
      const sw = Math.floor((screenW / this.chartAttr.w) * 100) / 100;
      const sh = Math.floor(((screenH - 100) / this.chartAttr.h) * 100) / 100;
      let templeft = 0;
      let temptop = 54;
      if (sw <= sh) {
        temptop = ((screenH - 100) - this.chartAttr.h * sw) / 2 + 54;
      } else {
        templeft = (screenW - this.chartAttr.w * sh) / 2;
      }
      return {
        isScale: (sw < 1 || sh < 1),
        scale: Math.min(sw, sh),
        top: temptop,
        left: templeft
      }
    },
    confirm () {
      this.$emit('confirm', this.$refs.refViewComNode.$children[0].saveConfig())
      this.close()
    },
    close () {
      this.showComponentVisible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .component-content {
  width: 100%;
  max-height: calc(100vh - 100px);
  .view-comp-node {
    // width: 100% !important;
    // transform: scale(0.78);
    transform-origin: 0 0;
    // left: 0 !important;
    // top: 54px;
    // left: unset !important;
    // top: unset !important;
    .render-box {
      width: 100% !important;
      // height: 100% !important;
      // max-height: calc(100vh - 100px);
    }
  }
}
.dialog-footer {
  ::v-deep .el-button {
    padding: 4px 12px;
  }
}
.view-editor-dialog {
  ::v-deep .el-dialog {
    max-width: 100vw;
  }
  ::v-deep .el-dialog__body {
    padding: 0;
    overflow: auto;
  }
}
</style>
