<template>
  <div class="config-control-box">
    <div class="config-control-item">
      <div class="item-label">图标类型</div>
      <div class="item-select">
        <el-select
          size="mini"
          v-model="iconConfig.iconType"
          @change="iconTypeChange"
        >
          <el-option label="基础图标" :value="1" />
          <el-option label="自定义图标" :value="2" />
        </el-select>
      </div>
    </div>

    <div class="config-control-item" v-if="iconConfig.iconType === 2">
      <div class="item-label">图标来源</div>
      <div class="item-select">
        <el-select
          size="mini"
          v-model="iconConfig.sourceType"
          @change="sourceTypeChange"
        >
          <el-option label="自定义" :value="1" />
          <el-option label="iconfont" :value="2" />
        </el-select>
      </div>
    </div>

    <div class="config-control-item" v-if="iconConfig.iconType === 2">
      <div class="item-label">上传</div>
      <div class="item-select">
        <el-upload
          v-model="fileList"
          class="upload-box"
          action="/api/common/upload"
          method="post"
          multiple
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-change="uploadChange"
          :auto-upload="true"
          size="mini"
          :show-file-list="false"
          accept="image/svg+xml"
          :limit="0"
        >
          <el-link type="primary" :underline="false" size=mini>
            <span v-if="fileCount !== fileReadyCount">
              <i class="el-icon-loading"></i>
              图片整理中...
            </span>
            <span v-else>选择文件</span>
          </el-link>
        </el-upload>

        <el-link
         :type="fileWaitting ? 'info':'primary'"
         size=mini
         :disabled="fileWaitting"
         :underline="false"
         @click="upload"
        >上传</el-link>
      </div>
    </div>

    <div class="config-control-item">
      <div class="item-label">图标内容</div>
      <div class="item-select item-select-content">
        <SvgIcon :name="iconConfig.iconName" class="select-icon"/>
        <el-select
          v-model="iconConfig.iconName"
          filterable
          placeholder="请选择"
          size="mini"
          class="item-content-select"
          @change="value=>handleChange({path: 'iconName',value})"
        >
          <div class="option-div">
            <!-- v-for="(item, index) in node.data.options" -->
            <el-option
              v-for="(item, index) in iconOptions"
              :key="item.label + index"
              :label="item.label"
              :value="item.value"
              class="image-select-item"
            >
              <div class="option-icon-card">
                <span class="option-item-icon">
                  <SvgIcon :name="item.value"/>
                </span>
                <span class="option-div-name">{{ item.label }}</span>
              </div>
            </el-option>
          </div>
        </el-select>
      </div>
    </div>

    <div class="config-control-item">
      <div class="item-label">图标大小</div>
      <div class="item-select">
        <el-input-number
          size="mini"
          controls-position="right"
          v-model="iconConfig.size"
          @change="value=>handleChange({path: 'size',value})"
        >
        </el-input-number>
      </div>
    </div>

    <div class="config-control-item">
      <div class="item-label">图标颜色</div>
      <div class="item-select">
        <el-color-picker
          v-model="iconConfig.color"
          show-alpha
          size="mini"
          @change="value=>handleChange({path: 'color',value})"
        >
        </el-color-picker>
      </div>
    </div>
  </div>
</template>

<script>
import SvgIcon from '@/lib/icon/index';
import { commonUpload } from '@/api/common';
import http from '@/utils/http';
import { replaceUrl, getHttpHeaders } from '@/utils/base'
import FONT_ICON_OPTIONS from '@/common/fontIconOptions'
import { getSvgOptionsByContent, formatSvg, insertSvgToHtml, formatIconFontSvg, getSvgOptionsByIconFontContent } from '@/utils/iconFont'
export default {
  name: 'CtlIconFont',
  components: {
    SvgIcon
  },
  data () {
    return {
      iconConfig: {
        iconType: 1,
        sourceType: 1,
        iconName: '',
        size: '',
        color: '',
        url: ''
      },
      fileList: [],
      fileContentList: [],
      fileCount: 0,
      fileReadyCount: 0,

      baseOptions: FONT_ICON_OPTIONS,
      customOptions: [],
      uploadHeaders: getHttpHeaders()
    };
  },
  mounted () {
    this.getDefaultConfig()
    this.getIconOptions()
  },
  created () {
  },
  updated () {
  },
  computed: {
    fileWaitting () {
      return this.fileCount === this.fileReadyCount && this.fileReadyCount === 0
    },
    iconOptions () {
      if (this.iconConfig.iconType === 1) {
        return this.baseOptions
      }

      return this.customOptions
    }
  },
  watch: {

  },
  methods: {
    iconTypeChange (value) {
      this.handleChange({
        path: 'iconType',
        value
      })

      this.clearData()
    },
    sourceTypeChange (value) {
      this.handleChange({
        path: 'sourceType',
        value
      })

      this.clearData()
    },
    clearData () {
      this.iconConfig.url = ''
      this.iconConfig.iconName = ''

      this.handleChange({
        path: 'url',
        value: ''
      })

      this.handleChange({
        path: 'iconName',
        value: ''
      })

      this.customOptions.length = 0

      this.fileCount = 0
      this.fileReadyCount = 0
      this.fileContentList.length = 0
    },
    beforeUpload (rawFile) {
      const name = rawFile.name
      const fileName = name.split('.')[0]
      rawFile.text().then(fileString => {
        let fileContent = fileString
        if (this.iconConfig.sourceType === 1) {
          fileContent += `[[${fileName}]]`
        }
        this.fileContentList.push(fileContent)
        this.fileReadyCount++
      })
      return false
    },
    getDefaultConfig () {
      const { color, iconName, iconType, size, sourceType, url } = this.value
      this.iconConfig.color = color
      this.iconConfig.iconName = iconName
      this.iconConfig.iconType = iconType
      this.iconConfig.size = size
      this.iconConfig.sourceType = sourceType
      this.iconConfig.url = url
    },
    uploadChange (file) {
      if (file.status === 'ready') {
        this.fileCount++
      }
    },
    upload () {
      const fileName = `icon-${Date.now()}.svg`
      let fileContent = this.fileContentList[0]

      // 上传分为：多个svg合并上传   fonticon图标方式上传单个js文件
      if (this.iconConfig.sourceType === 1) {
        fileContent = this.fileContentList.join('')
      }
      const file = new File([fileContent], fileName, {
        type: 'text/plain'
      })

      const formData = new FormData();
      formData.append('file', file);
      formData.append('filefolder', 'svg-icon');

      commonUpload(formData).then(res => {
        const { code, data } = res
        if (code === 200) {
          const { url } = data
          this.iconConfig.url = url

          this.handleChange({ path: 'url', value: url })

          this.fileCount = 0
          this.fileReadyCount = 0
          this.fileContentList.length = 0

          this.getIconOptions()
        }
      })
    },

    getIconOptions () {
      const componentId = this.$store.state.editor.currentSelectId
      const { iconType, sourceType, url } = this.iconConfig
      // 基础图标
      if (iconType === 1 || !url) {
        return
      }

      // 自定义图标
      const absoluteUrl = replaceUrl(url)
      // iconfont 方式图标
      if (sourceType === 2) {
        // const scriptDom = document.createElement('script')
        // document.body.appendChild(scriptDom)

        // http.get(absoluteUrl).then(res => {
        //   scriptDom.innerText = res
        //   const options = this.getFontIconOptionsByContent(res)
        //   const len = this.customOptions.length
        //   this.customOptions.splice(0, len, ...options)
        // })
        http.get(absoluteUrl).then(res => {
          const content = formatIconFontSvg(res, componentId)

          insertSvgToHtml(content, componentId)

          const options = getSvgOptionsByIconFontContent(res)
          const len = this.customOptions.length
          this.customOptions.splice(0, len, ...options)
        })
      } else {
        http.get(absoluteUrl).then(res => {
          const content = formatSvg(res, componentId)
          insertSvgToHtml(content, componentId)

          const options = getSvgOptionsByContent(res)
          const len = this.customOptions.length
          this.customOptions.splice(0, len, ...options)
        })
      }
    },

    handleChange ({ path, value }) {
      const pathArr = path.split('.');
      const tree = this.node && this.node.tree;
      if (tree) {
        const upperPaths = tree.valuePathMap[this.node.id];
        this.dispatch('ConfigTree', 'change', {
          path: upperPaths.concat(pathArr).join('.'),
          value
        });
      }
    },

    getFontIconOptionsByContent (string = '') {
      const idRxp = /id=.*?\s/g
      const arr = string.match(idRxp)
      const result = []

      if (!arr) {
        return result
      }

      let itemId
      arr.forEach(item => {
        itemId = item.split('=')[1].replace(/\s/g, '').replace(/"/g, '').replace(/icon-/g, '')
        result.push({
          value: itemId,
          label: itemId
        })
      })

      return result
    }
  }
};
</script>

<style lang="scss" scoped>
.config-control-box {
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
  .config-control-item {
    display: flex;
    width: 100%;
    position: relative;
    align-items: center;
    color: #fafafa;
    margin-bottom: 10px;
    padding: 4px 8px 4px 8px;
    .item-label {
      width: 70px;
      height: 24px;
      line-height: 24px;
      padding-right: 5px;
      color: #bfbfbf;
      font-size: 12px;
    }
    .item-select {
      flex: 1;
      padding-left: 8px;
    }

    .item-select-content{
      position: relative;
      .select-icon{
        position: absolute;
        top: 10px;
        left: 12px;
      }
    }

    .upload-box{
      display: inline-block;
      margin-right: 10px;
    }
  }

  ::v-deep .el-input-number--mini {
    width: 100%;
    line-height: 24px;
    .el-input__inner {
      height: 24px;
      line-height: 24px;
    }
    .el-input-number__decrease {
      line-height: 12px;
    }
    .el-input-number__increase {
      height: 12px;
      line-height: 12px;
    }
    .el-input__suffix {
      right: 32px;
    }
  }

  ::v-deep .item-content-select .el-input__inner{
    height: 32px;
    padding-left: 32px;
  }

  ::v-deep .el-color-picker {
    margin-left: 4px;
    height: 24px;
    .el-color-picker__trigger {
      height: 24px;
      width: 24px;
    }
  }

  ::v-deep .el-link{
    font-size: 12px;
    display: inline-block;
  }
}

::v-deep .option-div {
  // display: flex;
  // flex-flow: row wrap;
  // justify-content: space-between;
  // width: 230px;
  .image-select-item {
    // width: 30%;
    // height: 58px;
    // margin: 2px;
    // background: #1F2125;
    // padding: 0;
    .option-icon-card {
        // text-align: center;
      // .option-div-name {
      //   line-height: 20px;
      //   margin-left: 5px;
      // }
      .option-item-icon{
        margin-right: 10px;
      }
    }
  }
}
</style>
