<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <v-region v-model="value"></v-region>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import vRegion from 'v-region';
Vue.use(vRegion)
export default {
  name: 'CtlRegion'
}
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep {
    .rg-select {
      width: 62px;
      .rg-select__el {
        color: #a7a7a8;
        background: #25272e;
        border: 1px solid #393b4a;
        .rg-select__content {
          width: 65px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 12px;
          padding: 0px 30px 0px 6px;
          vertical-align: middle;
        }
      }
    }
  }
}
::v-deep {
  .rg-select__list {
    background: rgb(32 37 48);
  }
}
</style>
