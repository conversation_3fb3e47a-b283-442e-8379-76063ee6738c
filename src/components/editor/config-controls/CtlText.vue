<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <el-input
        v-model="tempValue"
        @blur="focusInput()"
        @keyup.enter.native="focusInput()"
        size="mini"
        :prefix-icon="node.data.prefixIcon"
        :suffix-icon="node.data.suffixIcon"
        >
        <label
          slot="prefix"
          v-if="!!node.data.prefix"
          class="el-input__icon"
          >{{node.data.prefix}}</label>
        <label
          slot="suffix"
          v-if="!!node.data.suffix"
          class="el-input__icon"
          >{{node.data.suffix}}</label>
      </el-input>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlText',
  data () {
    return {
      tempValue: ''
    }
  },
  created () {
    this.tempValue = this.value;
  },
  methods: {
    focusInput () {
      this.value = this.tempValue;
    }
  }
}
</script>

<style lang="scss" scoped>
.config-icon {
  display: inline-block;
  vertical-align: middle;
}
.config-control {
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
  ::v-deep .el-input__icon {
    line-height: 24px;
  }
}
</style>
