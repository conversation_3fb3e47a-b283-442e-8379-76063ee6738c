<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
     <el-switch
      v-model="value"
      active-color="#409EFF"
      inactive-color="#5F5F5F"
      >
    </el-switch>
    <span v-if="node.data.statusText" class="status-text">{{value ? 'open':'close'}}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlSwitch'
}
</script>

<style lang="scss" scoped>
.config-control {
  .width-div {
    display: flex;
    .status-text {
      margin: 0 4px;
      color: #fff;
      line-height: 20px;
    }
  }
}
</style>
