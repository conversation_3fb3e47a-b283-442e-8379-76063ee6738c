<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <el-select
        v-model="value"
        filterable
        placeholder="请选择"
        size="mini"
        >
        <el-option
          v-for="(item, index) in node.data.options"
          :key="item.label + index"
          :label="item.label"
          :value="item.value"
          class="image-select-item"
          >
          <div class="option-div">
            <el-image
              style="width: 122px; height: 60px"
              :src="item.src"
              fit='contain'
              ></el-image>
            <span class="option-div-name">{{item.label}}</span>
          </div>
        </el-option>
      </el-select>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlImageSelect'
}
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
}
::v-deep .image-select-item {
  height: unset;
  line-height: unset;
  margin: 4px 0;
  .option-div {
    display: flex;
    .option-div-name {
      flex: 1;
      line-height: 60px;
      text-align: center;
    }
  }
}
</style>
