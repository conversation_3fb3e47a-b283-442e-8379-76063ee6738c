<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <el-radio-group v-model="value">
        <el-radio
          v-for="(item, index) in node.data.options"
          :label="item.value"
          :key="item.label + index"
          :style="{width: node.data.optionCol + 'px'}"
          >{{item.label}}</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlRadio'
}
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep .el-radio {
    margin-right: 20px;
    margin-bottom: 2px;
    margin-top: 2px;
    .el-radio__label {
      font-size: 12px;
    }
  }
}
</style>
