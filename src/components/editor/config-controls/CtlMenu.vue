<template>
<div class="config-control">
  <el-tabs
  v-model="editableTabsValue"
  type="border-card"
  >
    <el-tab-pane
      v-for="(item, index) in editableTabs"
      :key="item.name + index"
      :label="item.name"
      :name="item.name"
    >
    <div v-if="item.mode === 'single'">
      <ConfigNode
        v-for="child in item.children"
        :key="child.id"
        :node="child"
        :configObj="configObj"
        >
      </ConfigNode>
    </div>
    <div v-if="item.mode !== 'single'" class="multiple-tabs" style="padding: 0 8px;">
        <el-tabs
          v-model="item.activetab"
          type="border-card"
          >
            <el-tab-pane
              v-for="(it, index) in item.children"
              :key="it.name + index"
              :label="it.name"
              :name="it.name"
            >
            <ConfigNode
              v-for="child in it.children"
              :key="child.id"
              :node="child"
              :configObj="configObj"
              >
            </ConfigNode>
            </el-tab-pane>
        </el-tabs>
    </div>
    </el-tab-pane>
  </el-tabs>
</div>
</template>

<script>
export default {
  name: 'CtlMenu',
  components: {
  },
  data () {
    return {
      isShow: true,
      tabsDepth: 1,
      editableTabsValue: '',
      editableTabs: []
    }
  },
  created () {
    this.editableTabs = [];
    for (let key = 0; key < this.node.children.length; key++) {
      let tempTab = {}
      if (this.node.children[key].data.mode === 'single') {
        tempTab = {
          title: this.node.children[key].data.name,
          name: this.node.children[key].data.name,
          mode: this.node.children[key].data.mode,
          children: this.node.children[key].children
        }
      } else {
        tempTab = {
          title: this.node.children[key].data.name,
          name: this.node.children[key].data.name,
          mode: this.node.children[key].data.mode,
          children: []
        }
        for (let ck = 0; ck < this.node.children[key].children.length; ck++) {
          const ctemptab = {
            title: this.node.children[key].children[ck].data.name,
            name: this.node.children[key].children[ck].data.name,
            children: this.node.children[key].children[ck].children
          }
          tempTab.children.push(ctemptab);
        }
      }
      if (tempTab.children.length > 0) {
        tempTab.activetab = tempTab.children[0].name;
      }
      this.editableTabs.push(tempTab);
    }
    if (this.editableTabs.length > 0) {
      this.editableTabsValue = this.editableTabs[0].name;
    }
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 16px 0;
    }
  }
  ::v-deep .el-tabs--border-card{
    background: unset;
    border: unset;
    box-shadow: unset;
    width: 100%;
    .el-tabs__content {
      padding: 8px 0;
      background-color: #20232a;
    }
  }
  ::v-deep .el-tabs__item {
    font-size: 12px;
    height: 32px;
    line-height: 32px;
    border: unset;
  }
  ::v-deep .el-tabs--border-card>.el-tabs__header {
    border-bottom: unset;
    background-color: unset;
    .el-tabs__item.is-active {
      border-right-color: unset;
      border-left-color: unset;
      background-color: #20232a;
    }
    .el-tabs__nav {
      display: flex;
      float: unset;
      .el-tabs__item {
        flex: 1;
        text-align: center;
      }
    }
  }
  .multiple-tabs {
    padding: 0 8px;
    ::v-deep .el-tabs__item {
      border: 1px solid #393b4a;
      margin: 1px 0;
      text-align: center;
      height: 28px;
      line-height: 28px;
    }
  }
}
</style>
