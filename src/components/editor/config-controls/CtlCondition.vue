<template>
  <div class="config-control">
    <el-collapse accordion>
      <el-collapse-item>
        <template slot="title">
          {{ node.data.name }}
          <i
            class="config-icon"
            :class="[isShow ? 'icon-visible' : 'icon-hide']"
            v-if="node.data.enableHide"
            @click="isShow = !isShow"
          ></i>
          <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
            <div
              slot="content"
              :style="{ 'max-width': '15rem' }"
              v-html="node.data.desc"
            ></div>
            <span class="icon icon2 el-icon-question"></span>
          </el-tooltip>
        </template>
        <el-tabs
          v-model="editableTabsValue"
          type="card"
          editable
          @edit="handleTabsEdit"
        >
          <el-tab-pane
            v-for="(tabObj, index) in tabsData"
            :key="seriesName + index"
            :label="seriesName + index"
            :name="seriesName + index"
          >
          <div class="item-switch">
            <div class="config-title title-enable">启用</div>
            <el-switch
              v-model="tabObj.enable"
              active-color="#409EFF"
              inactive-color="#5F5F5F"
              @change="updateValue()"
              >
            </el-switch>
            <div class="config-title title-logical">逻辑</div>
            <el-radio-group
            v-model="tabObj.logicalType"
            @change="updateValue()"
            size="mini">
              <el-radio-button
                v-for="(itemt, indext) in logicalTypeList"
                :label="itemt.value"
                :key="itemt.label + indext"
                >{{itemt.label}}</el-radio-button>
            </el-radio-group>
          </div>
          <div class="list-condition">
            <div class="condition-item-list" v-for="(conditionItem, itemidx) in tabObj.conditionArray" :key="itemidx">
              <div class="condition-item">
                <div class="condition-children">
                  <div class="condition-children-radio">
                    <el-radio-group
                    v-model="conditionItem.logicalType"
                    @change="updateValue()"
                    size="mini">
                      <el-radio-button
                        v-for="(itemt, indext) in logicalTypeList"
                        :label="itemt.value"
                        :key="itemt.label + indext"
                        >{{itemt.label}}</el-radio-button>
                    </el-radio-group>
                  </div>
                  <i class="el-icon-delete del-condition" @click="minusCondition(tabObj.conditionArray, itemidx)"></i>
                </div>
              </div>
              <div class="condition-item" v-for="(ite, idx) in conditionItem.condition" :key="idx">
                <div class="item-iput">
                  <el-select
                      v-model="ite.parm"
                      filterable
                      allow-create
                      placeholder="请选择"
                      size="mini"
                      @change="updateValueParm(ite)"
                      >
                      <el-option
                        v-for="(item) in parmList"
                        :key="item.label"
                        :label="item.label"
                        :value="item.value"
                        >
                      </el-option>
                  </el-select>
                </div>
                <div class="item-select">
                  <el-select
                      v-model="ite.operator"
                      filterable
                      placeholder="请选择"
                      size="mini"
                      @change="updateValue()"
                      @focus="operatorListFocus(ite)"
                      >
                      <el-option
                        v-for="(item) in operatorList"
                        :key="item.label"
                        :label="item.label"
                        :value="item.value"
                        >
                      </el-option>
                  </el-select>
                </div>
                <div class="item-iput-value">
                  <el-input-number
                    v-if="ite.parm == 'index(序号)'"
                    v-model="ite.value"
                    controls-position="right"
                    :min="0"
                    size="mini"
                    @change="updateValue()"
                    >
                  </el-input-number>
                  <el-input
                  v-if="ite.parm !== 'index(序号)'"
                  v-model="ite.value"
                  @change="updateValue()"></el-input>
                </div>
                <div class="item-add-remove">
                  <i v-if="idx !== conditionItem.condition.length - 1" style="display: block;width: 14px;height: 14px;"></i>
                  <i class="el-icon-minus" v-if="conditionItem.condition.length !== 1" @click="minusConditionSubitem(conditionItem.condition, idx)"></i>
                  <i class="el-icon-plus" v-if="idx === conditionItem.condition.length - 1" @click="addConditionSubitem(conditionItem.condition, idx)"></i>
                </div>
              </div>
            </div>
            <div>
              <el-button class="item-add-btn" size="mini" type="primary" @click="addCondition(index)">新增条件</el-button>
            </div>
          </div>
          <ConfigTree
            :treeData="node.data.templates"
            :configObj="tabObj.style"
            @change="handleChange($event, index, tabObj)">
          </ConfigTree>
          </el-tab-pane>
        </el-tabs>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { parseConfig } from '@/lib/ConfigTree';
import { uuid } from '@/utils/base';
import dataUtil from '@/utils/data'

export default {
  name: 'CtlCondition',
  components: {},
  computed: {
    ...mapGetters('editor', ['currentCom', 'currentConfigId']),
    ...mapState({
      currentSelectId: state => state.editor.currentSelectId,
      comsData: state => state.editor.comsData,
      screenFilters: state => state.editor.screenFilters
    }),
    validFilters () {
      if (_.isEmpty(this.currentCom)) return []
      const filters = this.currentCom.dataConfig.dataResponse.filters
      if (!filters.enable) return []
      return _.filter(filters.list, { enable: true }).map(({ id }) => this.screenFilters[id])
    }
  },
  data () {
    return {
      isShow: true,
      editableTabsValue: '',
      editableTabs: [],
      // 默认动态添加tabs 项
      maxTabs: 50,
      tabsData: [],
      seriesName: '条件',
      // 条件模板
      conditionTemplate: {
        logicalType: '$or',
        condition: [{
          parm: '',
          operator: '$eq',
          value: ''
        }]
      },
      operatorList: [
        { value: '$eq', label: '等于' },
        { value: '$ne', label: '不等于' },
        { value: '$gt', label: '大于' },
        { value: '$lt', label: '小于' },
        { value: '$gte', label: '大于等于' },
        { value: '$lte', label: '小于等于' },
        { value: '$in', label: '包含' },
        { value: '$nin', label: '不包含' }],
      logicalTypeList: [
        { value: '$and', label: '并且' },
        { value: '$or', label: '或者' }],
      parmList: []
    };
  },
  watch: {
    'currentCom.dataConfig.dataResponse': {
      handler: function (val) {
        // console.log('当前数据源发生变化', val);
        this.getParmList();
      },
      deep: true
    }
  },
  created () {
    this.editableTabs = [];
    this.seriesName = this.node.data.templatesName ? this.node.data.templatesName : '条件';
    this.getParmList();
    if (this.value && this.value.length) {
      // this.tabsData = [..._.cloneDeep(this.value)].map(d => ({ ...d, _id: uuid() }));
      this.tabsData = _.cloneDeep(this.value);
      this.tabsData.forEach((item) => {
        item.style._id = uuid();
      });
      // console.log('初始化数据', this.tabsData);
      this.editableTabsValue = this.seriesName + 0;
    }
    if (this.node.data.maxTabs) {
      this.maxTabs = (this.tabsData.length > this.node.data.maxTabs) ? this.tabsData.length : this.node.data.maxTabs;
    } else {
      this.maxTabs = (this.tabsData.length > 10) ? this.tabsData.length : 10;
    }
  },
  methods: {
    // 获取字段列表
    getParmList () {
      this.parmList = [];
      const comData = this.comsData[this.currentConfigId];
      if (!_.isEmpty(comData)) {
        const filterData = dataUtil.filterData(comData, this.validFilters);
        if (filterData.length > 0) {
          for (const key in filterData[0]) {
            this.parmList.push({
              value: key,
              label: key,
              // type: typeof Number(filterData[0][key]) === 'number' && !isNaN(Number(filterData[0][key])) ? 'number' : 'string'
              // 内置 conditionalFilter 条件方法 是直接比对的，没有转换，所以此处不能转换类型
              type: typeof filterData[0][key] === 'number' && !isNaN(filterData[0][key]) ? 'number' : 'string'
            });
          }
        }
      }
      // 静态数据 或者其它数据源时，使用 组件给出字段
      // if (this.currentCom && this.currentCom.dataConfig && this.currentCom.dataConfig.fields) {
      //   console.log('当前选中组件 currentCom -', this.currentCom);
      //   // this.parmList = _.cloneDeep()
      //   this.currentCom.dataConfig.fields.forEach(item => {
      //     this.parmList.push({
      //       value: item.name,
      //       label: item.name,
      //       type: item.type
      //     });
      //   });
      // }
      if (this.node.data.showIndex) {
        this.parmList.push({
          value: 'index(序号)',
          label: 'index(序号)',
          type: 'number'
        });
      }
    },
    updateValue () {
      this.value = _.cloneDeep(this.tabsData);
    },
    handleChange (params, index, tabObj) {
      _.set(tabObj.style, params.path, params.value);
      this.tabsData.splice(index, 1, tabObj);
      this.updateValue();
    },
    handleTabsEdit (targetName, action) {
      if (action === 'add') {
        if (this.tabsData.length >= this.maxTabs) {
          console.error('注意: 默认最大系列数为 ' + this.maxTabs);
          return;
        }
        const newConfig = parseConfig(this.node.data.templates);
        this.tabsData.push({
          enable: true,
          logicalType: '$or',
          conditionArray: [_.cloneDeep(this.conditionTemplate)],
          style: { ...newConfig, _id: uuid() }
        });
        this.updateValue();
        this.editableTabsValue = this.seriesName + (this.tabsData.length - 1);
      } else if (action === 'remove') {
        let deletIndex = 0;
        let activeName = this.editableTabsValue;
        this.tabsData.forEach((tab, index) => {
          if ((this.seriesName + index) === targetName) {
            deletIndex = index;
          }
        });
        if (this.editableTabsValue === targetName) {
          if (deletIndex === 0) {
            activeName = this.seriesName + (deletIndex + 1);
          } else {
            activeName = this.seriesName + (deletIndex - 1);
          }
          this.editableTabsValue = activeName;
        }
        this.tabsData = this.tabsData.filter((it, index) => {
          return index !== deletIndex;
        });
        this.updateValue();
      }
    },

    // 当前选中值
    updateValueParm (item) {
      if (item.parm === 'index(序号)') {
        item.value = 0;
      }
      this.operatorListFocus(item);
      const isIn = this.operatorList.some(it => {
        return it.value === item.operator;
      });
      if (!isIn) {
        item.operator = '';
      }
      this.updateValue();
    },
    // 选择前 重置 选择列表
    operatorListFocus (item) {
      const selectParm = this.parmList.filter(it => {
        return it.value === item.parm;
      });
      if (selectParm[0].type === 'number') {
        this.operatorList = [
          { value: '$eq', label: '等于' },
          { value: '$ne', label: '不等于' },
          { value: '$gt', label: '大于' },
          { value: '$lt', label: '小于' },
          { value: '$gte', label: '大于等于' },
          { value: '$lte', label: '小于等于' }];
      } else {
        this.operatorList = [
          { value: '$eq', label: '等于' },
          { value: '$ne', label: '不等于' },
          { value: '$in', label: '包含' },
          { value: '$nin', label: '不包含' }];
      }
    },
    // 新增条件
    addCondition (index) {
      const tempItem = {
        logicalType: '$or',
        condition: [
          {
            parm: '',
            operator: '$eq',
            value: ''
          }
        ]
      }
      if (this.tabsData[index] && this.tabsData[index].conditionArray) {
        this.tabsData[index].conditionArray.push(tempItem);
        this.updateValue();
      }
    },

    // 删除条件
    minusCondition (item, index) {
      if (item.length === 0) {
        return;
      }
      item.splice(index, 1);
      this.updateValue();
    },

    // 新增条件分项
    addConditionSubitem (item, index) {
      // let templateItem = item[index];
      item.push(_.cloneDeep(item[index]));
      this.updateValue();
    },

    // 删除分项
    minusConditionSubitem (item, idx) {
      if (item.length === 1) {
        return;
      }
      item.splice(idx, 1);
      this.updateValue();
    }
  }
};
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 8px 0;
    }
    .el-tabs__new-tab {
      margin: 5px 0;
    }
  }
  ::v-deep .el-tabs--border-card {
    background: unset;
    border: unset;
    box-shadow: unset;
    width: 100%;
  }
  ::v-deep .el-tabs__item {
    font-size: 12px;
  }
  ::v-deep .el-tabs--card > .el-tabs__header {
    border-bottom: unset;
    .el-tabs__item {
      height: 28px;
      line-height: 28px;
      border-radius: 4px 4px 0 0;
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 28px;
    }
    .el-tabs__nav-wrap.is-scrollable {
      padding: 0 20px;
      box-sizing: border-box;
    }
  }
  ::v-deep .list-condition {
      padding: 2px;
      background-color: #fbfbfb10;
      border: 1px solid #185aabb3;
      // color: #f40;
      .condition-item-list {
        padding: 0 2px;
        background-color: #00000088;
        border: 1px solid #185aabb3;
        border-radius: 6px;
        margin: 8px 0;
      }
    .condition-item {
      display: flex;
      margin: 4px 4px 4px 0;
      align-items: center;
      .condition-children {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
        border-bottom: 1px solid #185aabb3;
        .condition-children-radio {
          line-height: 24px;
          height: 24px;
        }
        .del-condition {
          cursor: pointer;
        }
      }
      .el-input__inner {
        height: 24px;
        line-height: 24px;
        padding: 0 4px;
      }
      .item-add-remove {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
      .el-input__suffix {
        display: none;
      }
      .item-select {
        margin: 0 4px;
        .el-input__inner {
          width: 58px;
        }
      }
      .item-select-logical {
        margin: 0 4px;
        .el-input__inner {
          width: 40px;
        }
      }
      .item-iput-value {
        .el-input-number {
          width: 110px;
          line-height: 24px;
        }
        .el-input {
          width: 110px;
        }
      }
      .el-radio-button--mini .el-radio-button__inner {
        padding: 5px 9px;
      }
    }
    .item-add-btn {
      width: calc(100% - 2px);
      margin: 0 1px;
      // background: #fff0;
      // border: 1px solid #185aabb3;
    }
  }
  ::v-deep .config-tree-root {
    .config-control {
      padding: 4px 0;
      .config-title {
        width: 65px;
      }
    }
  }
  .item-switch {
    line-height: 24px;
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    .config-title {
      width: 65px;
    }
    .title-logical {
      width: 32px;
      margin-left: 80px;
    }
    .title-enable {
      width: 32px;
    }
  }
}
</style>
