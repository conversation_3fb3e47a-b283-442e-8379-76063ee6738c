<template>
<div class="config-control">
  <el-collapse accordion :value="openName">
    <el-collapse-item name="simple">
      <template slot="title">
        {{node.data.name}}
      <i class="config-icon"
        :class="[isShow ? 'icon-visible' : 'icon-hide']"
        v-if="node.data.enableHide"
        @click="isShow = !isShow;"></i>
        <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
          <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
          </div>
          <span class="icon icon2 el-icon-question"></span>
        </el-tooltip>
      </template>
      <ConfigNode
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :configObj="configObj"
      >
      </ConfigNode>
    </el-collapse-item>
  </el-collapse>
</div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'CtlGroup',
  computed: {
    ...mapGetters('editor', ['currentCom']),
    openName () {
      return this.currentCom ? this.currentCom.other.configType : 'other';
    }
  },
  data () {
    return {
      isShow: true
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 16px 0;
    }
  }
}
</style>
