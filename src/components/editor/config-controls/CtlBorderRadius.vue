<template>
  <div class="config-control">
    <i class="config-icon"
      :class="[defaultValue.show ? 'icon-visible' : 'icon-hide']"
      v-if="node.data.enableHide"
      @click="defaultValue.show = !defaultValue.show;"></i>
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div" :class="{'disabled-container': !defaultValue.show}">
      <div class="component-div" v-for="it in components" :key="it">
        <el-input-number
          v-model="defaultValue[it]"
          controls-position="right"
          size="mini"
          :id="it + idInputNumber"
          :step="1"
          @change="changeInput(defaultValue)"
          >
        </el-input-number>
        <span class="text-name">{{textNanme[it]}}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlBorderRadius',
  data () {
    return {
      // input 输入框添加id
      idInputNumber: '-id-input-radius-' + new Date().getTime(),
      // 默认配置模块
      components: ['tl', 'tr', 'bl', 'br'],
      textNanme: {
        tl: '左上',
        tr: '右上',
        bl: '左下',
        br: '右下'
      },
      defaultValue: {
        tl: 0,
        tr: 0,
        bl: 0,
        br: 0,
        show: true
      }
    }
  },
  created () {
    if (this.node.data.components) {
      this.components = this.node.data.components;
    }
    if (this.value) {
      for (const key of Object.keys(this.value)) {
        this.defaultValue[key] = this.value[key];
      }
      this.defaultValue.show = true;
    }
    this.components.forEach(item => {
      // 后缀
      this.addSuffix(item, 'px');
    });
  },
  methods: {
    addSuffix (divid, str) {
      const span = document.createElement('span');
      const innerspan = document.createElement('span');
      const textspan = document.createElement('span');
      // 添加elementUI 内置 class
      span.setAttribute('class', 'el-input__suffix');
      innerspan.setAttribute('class', 'el-input__suffix-inner');

      span.append(innerspan);
      innerspan.append(textspan);
      textspan.append(str);

      this.$nextTick(() => {
        if (document.getElementById(divid + this.idInputNumber)) {
          document.getElementById(divid + this.idInputNumber).lastElementChild.prepend(span);
        }
      });
    },
    changeInput () {
      const tempVal = {};
      this.components.forEach(item => {
        tempVal[item] = this.defaultValue[item];
      });
      if (this.node.data.enableHide) {
        tempVal.show = this.defaultValue.show;
      } else {
        delete tempVal.show;
      }
      this.value = tempVal;
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  .width-div {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  .component-div {
    position: relative;
    // width: calc(100% - 4px);
    margin: 0 2px;
    // margin-bottom: 4px;
    .text-name {
      height: 24px;
      line-height: 24px;
      text-align: center;
      color: #606266;
    }
    ::v-deep .el-input-number--mini {
      width: 100%;
      line-height: 24px;
      .el-input__inner {
        height: 24px;
        line-height: 24px;
      }
      .el-input-number__decrease {
        line-height: 12px;
      }
      .el-input-number__increase {
        height: 12px;
        line-height: 12px;
      }
      .el-input__suffix {
        right: 32px;
      }
    }
  }
  .disabled-container {
    opacity: 0.6; // 透明度
    pointer-events: none; // 屏蔽鼠标事件
    cursor: not-allowed; // 鼠标样式
  }
}
</style>
