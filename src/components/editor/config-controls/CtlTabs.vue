<template>
  <div class="config-control">
    <el-collapse accordion :value="openName">
      <el-collapse-item name="simple">
        <template slot="title">
          {{ node.data.name }}
          <i
            class="config-icon"
            :class="[isShow ? 'icon-visible' : 'icon-hide']"
            v-if="node.data.enableHide"
            @click="isShow = !isShow"
          ></i>
          <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
            <div
              slot="content"
              :style="{ 'max-width': '15rem' }"
              v-html="node.data.desc"
            ></div>
            <span class="icon icon2 el-icon-question"></span> </el-tooltip
          >
        </template>
        <el-tabs
          v-model="editableTabsValue"
          type="card"
          editable
          @edit="handleTabsEdit"
          v-if="this.node.data.addable"
        >
          <el-tab-pane
            v-for="(tabObj, index) in tabsData"
            :key="tabObj._id"
            :label="seriesName + index"
            :name="tabObj._id"
          >
          <ConfigTree
            :treeData="node.data.templates"
            :configObj="tabObj"
            @change="handleChange($event, index, tabObj)">
          </ConfigTree>
          </el-tab-pane>
        </el-tabs>
        <el-tabs
          v-model="editableTabsValue"
          type="card"
          v-if="!this.node.data.addable"
        >
          <el-tab-pane
            v-for="item in editableTabs"
            :key="item._id"
            :label="item.name"
            :name="item._id"
          >
            <ConfigNode
              v-for="child in item.children"
              :key="child.id"
              :node="child"
              :configObj="configObj"
            >
            </ConfigNode>
          </el-tab-pane>
        </el-tabs>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { parseConfig } from '@/lib/ConfigTree';
import { uuid } from '@/utils/base';
import { mapGetters } from 'vuex';
export default {
  name: 'CtlTabs',
  components: {},
  computed: {
    ...mapGetters('editor', ['currentCom']),
    openName () {
      return this.currentCom ? this.currentCom.other.configType : 'other';
    }
  },
  data () {
    return {
      isShow: true,
      editableTabsValue: '',
      editableTabs: [],
      // 默认动态添加tabs 项
      maxTabs: 50,
      tabsData: [],
      seriesName: '系列'
    };
  },
  created () {
    this.editableTabs = [];
    this.seriesName = this.node.data.templatesName ? this.node.data.templatesName : '系列';
    // 内容固定（不配置template）
    if (!this.node.data.addable) {
      for (let i = 0; i < this.node.children.length; i++) {
        const tempTab = {
          title: this.node.data.children[i].name,
          name: this.node.data.children[i].name,
          children: this.node.children[i].children,
          _id: uuid()
        };
        this.editableTabs.push(tempTab);
      }
      if (this.editableTabs.length > 0) {
        this.editableTabsValue = this.editableTabs[0]._id;
      }
    } else {
      if (this.value && this.value.length) {
        this.tabsData = [..._.cloneDeep(this.value)].map(d => ({ ...d, _id: uuid() }));
        this.editableTabsValue = this.tabsData[0]._id;
      }
      if (this.node.data.maxTabs) {
        this.maxTabs = (this.tabsData.length > this.node.data.maxTabs) ? this.tabsData.length : this.node.data.maxTabs;
      } else {
        this.maxTabs = (this.tabsData.length > 10) ? this.tabsData.length : 10;
      }
    }
  },
  methods: {
    updateValue () {
      this.value = _.cloneDeep(this.tabsData);
    },
    handleChange (params, index, tabObj) {
      _.set(tabObj, params.path, params.value);
      this.tabsData.splice(index, 1, tabObj);
      this.updateValue();
    },
    handleTabsEdit (targetName, action) {
      if (action === 'add') {
        if (this.tabsData.length >= this.maxTabs) {
          console.error('注意: 默认最大系列数为 ' + this.maxTabs);
          return;
        }
        const newConfig = _.cloneDeep(parseConfig(this.node.data.templates));
        const tab = { ...newConfig, _id: uuid() }
        this.tabsData.push(tab);
        this.editableTabsValue = tab._id;
        this.updateValue();
      } else if (action === 'remove') {
        const index = this.tabsData.findIndex(item => item._id === targetName);
        if (index > -1) {
          this.tabsData.splice(index, 1);
          if (this.tabsData.length) {
            if (index === 0) {
              this.editableTabsValue = this.tabsData[0]._id;
            } else {
              this.editableTabsValue = this.tabsData[index - 1]._id;
            }
          } else {
            this.editableTabsValue = '';
          }
        }
        this.updateValue();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.config-control {
  .config-icon {
    cursor: pointer;
  }
  ::v-deep .el-collapse {
    width: 100%;
    .el-collapse-item__content {
      padding: 8px 0;
    }
    .el-tabs__new-tab {
      margin: 5px 0;
    }
  }
  ::v-deep .el-tabs--border-card {
    background: unset;
    border: unset;
    box-shadow: unset;
    width: 100%;
  }
  ::v-deep .el-tabs__item {
    font-size: 12px;
  }
  ::v-deep .el-tabs--card > .el-tabs__header {
    border-bottom: unset;
    .el-tabs__item {
      height: 28px;
      line-height: 28px;
      border-radius: 4px 4px 0 0;
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 28px;
    }
    .el-tabs__nav-wrap.is-scrollable {
      padding: 0 20px;
      box-sizing: border-box;
    }
  }
}
</style>
