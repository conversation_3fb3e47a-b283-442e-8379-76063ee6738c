<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-08-09 15:17:47
 * @LastEditors: WangNing
 * @LastEditTime: 2022-08-10 15:11:03
 * @FilePath: /seatom/src/components/editor/config-controls/CtlDataSource.vue
 * @Description: 获取数据源字段下拉框
-->
<template>
  <div
    class="config-control"
  >
    <div
      class="config-title nowrap"
      :class="{'config-title65': node.depth > 1}"
      :title="node.data.name"
    >
      <el-tooltip
        effect="dark"
        placement="top"
        v-if="node.data.desc"
      >
        <div
          slot="content"
          :style="{'max-width': '15rem'}"
          v-html="node.data.desc"
        >
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}
    </div>
    <div class="width-div">
      <el-select
        v-model="value"
        size="mini"
      >
        <el-option
          v-for="(item, index) in keyOptions"
          :key="item.label + index"
          :label="item.label"
          :value="item.value"
          class="image-select-item"
        >
          <div class="option-div-name">{{item.label}}</div>
        </el-option>
      </el-select>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import dataUtil from '@/utils/data'
export default {
  name: 'CtlDataSource',
  data () {
    return {
      keyOptions: [] // 映射字段集合
    }
  },
  computed: {
    ...mapGetters('editor', ['currentCom', 'currentConfigId']),
    ...mapState({
      currentSelectId: state => state.editor.currentSelectId,
      comsData: state => state.editor.comsData
    })
  },
  created () {
    this.getParmList();
  },
  watch: {
    'currentCom.dataConfig': {
      handler: function (val) {
        this.getParmList();
        this.resetValue()
      },
      deep: true
    }
  },
  methods: {
    // 修改完数据源字段名称后重置选中值
    resetValue () {
      this.value = ''
    },
    // 获取字段列表
    getParmList () {
      const comData = this.comsData[this.currentConfigId];
      this.keyOptions = []
      if (!_.isEmpty(comData)) {
        const filterData = dataUtil.filterData(comData, this.validFilters);
        if (filterData.length > 0) {
          for (const key in filterData[0]) {
            this.keyOptions.push({
              value: key,
              label: key
            });
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
}
</style>
