<template>
  <div class="config-control">
    <div class="config-title nowrap" :class="{'config-title65': node.depth > 1}" :title="node.data.name">
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div slot="content" :style="{'max-width': '15rem'}" v-html="node.data.desc">
        </div>
        <span class="icon icon2 el-icon-question"></span>
      </el-tooltip>{{node.data.name}}</div>
    <div class="width-div">
      <el-select
        v-model="value"
        filterable
        placeholder="请选择"
        size="mini"
        v-if="!node.data.valueObj"
        >
        <el-option
          v-for="(item, index) in node.data.options"
          :key="item.value + index"
          :label="item.label"
          :value="item.value"
          >
        </el-option>
      </el-select>
      <el-select
        v-model="value"
        filterable
        placeholder="请选择"
        size="mini"
        v-if="node.data.valueObj"
        value-key="label"
        >
        <el-option
          v-for="(item) in node.data.options"
          :key="item.label"
          :label="item.label"
          :value="item"
          >
        </el-option>
      </el-select>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CtlSelect'
}
</script>

<style lang="scss" scoped>
.config-control {
  ::v-deep .el-select--mini {
    width: 100%;
    .el-input__icon {
      line-height: 24px;
    }
  }
  ::v-deep .el-input__inner {
    height: 24px;
    line-height: 24px;
  }
}
</style>
