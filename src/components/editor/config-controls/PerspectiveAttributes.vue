<template>
  <div class="config-control basic-attributes pt-4">
    <el-collapse accordion>
      <el-collapse-item>
        <template slot="title">
          3D转换
        </template>
        <div>
          <div class="config-control">
            <div class="config-title65 config-title">开启</div>
            <div>
              <el-switch
                v-model="isOpen3D"
                active-color="#409EFF"
                inactive-color="#5F5F5F"
              >
              </el-switch>
            </div>
          </div>
          <div class="config-control" v-show="isOpen3D">
            <div class="config-title65 config-title">X轴旋转</div>
            <el-input-number
              :value="flipH"
              controls-position="right"
              size="mini"
              :step="1"
              :min="-360"
              :max="360"
              class="w100"
              @change="flipH = $event"
            ></el-input-number>
          </div>
          <div class="config-control" v-show="isOpen3D">
            <div class="config-title65 config-title">Y轴旋转</div>
            <el-input-number
              :value="flipV"
              controls-position="right"
              size="mini"
              :step="1"
              :min="-360"
              :max="360"
              class="w100"
              @change="flipV = $event"
            ></el-input-number>
          </div>
          <div class="config-control" v-show="isOpen3D">
            <div class="config-title65 config-title">Z轴旋转</div>
            <el-input-number
              :value="flipZ"
              controls-position="right"
              size="mini"
              :step="1"
              :min="-360"
              :max="360"
              class="w100"
              @change="flipZ = $event"
            ></el-input-number>
          </div>
          <div class="config-control" v-show="isOpen3D">
            <div class="config-title65 config-title">透视距离</div>
            <el-input-number
              :value="perspective"
              controls-position="right"
              size="mini"
              :step="1"
              :min="0"
              class="w100"
              @change="perspective = $event"
            ></el-input-number>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
export default {
  name: 'PerspectiveAttributes',
  data () {
    return {
    }
  },
  computed: {
    ...mapState({
      currentSelectId: state => state.editor.currentSelectId
    }),
    ...mapGetters('editor', ['currentCom']),
    isOpen3D: {
      get () {
        return this.currentCom.attr.enable3D
      },
      set (val) {
        this.enable3D(val);
      }
    },
    flipH: {
      get () {
        return this.currentCom.attr.flipH
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'attr.flipH', value: val }
          ]
        });
      }
    },
    flipV: {
      get () {
        return this.currentCom.attr.flipV
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'attr.flipV', value: val }
          ]
        });
      }
    },
    flipZ: {
      get () {
        return Math.round(this.currentCom.attr.deg)
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'attr.deg', value: val }
          ]
        });
      }
    },
    perspective: {
      get () {
        return this.currentCom.attr.perspective
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'attr.perspective', value: val }
          ]
        });
      }
    }
  },
  methods: {
    enable3D (val) {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: 'attr.enable3D', value: val }
        ]
      });
    }
  }

}
</script>

<style lang="scss" scoped>
.basic-attributes {
  display: inline-block;
  padding-top: 0;
  .title {
    line-height: 30px;
    display: flex;
    justify-items: center;
    font-size: 14px;
    justify-content: space-between;
    .update {
      font-size: 12px;
      cursor: pointer;
    }
  }
  ::v-deep .el-collapse-item__content {
    padding: 16px 0;
  }
  .config-control {
    display: flex;
    width: 100%;
    position: relative;
    padding: 4px 8px;
    align-items: center;
    color: #fafafa;

    .config-title {
      width: 80px;
      height: 24px;
      line-height: 24px;
      padding-right: 5px;
      color: #bfbfbf;
      font-size: 12px;
      }
    .config-title65 {
      width: 65px;
    }

    .el-input-number {
      flex: 1;
    }
  }

}

</style>
