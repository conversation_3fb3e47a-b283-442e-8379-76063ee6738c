<template>
  <div class="AVYju" v-if="editPanelState.config.select && show" @mouseleave="close">
    <div class="v6BHz">
      <div class="_2_A_B" v-for="(item, index) in list" :key="index" @click="selectTheme(item)">
        <div class="_2czmi" :class="{ active: themeId == item.id }">
          <div style="width: 100%; height: 100%">
            <img :src="replaceUrl(item.icon)" draggable="false" style="width: 100%; height: 100%; object-fit: contain;"/>
          </div>
        </div>
        <span>{{ item.name }}</span>
      </div>
    </div>
    <div class="no-data" v-if="list.length === 0">{{ loading ? '加载中...' : '暂无数据' }}</div>
    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { themeList } from '@/api/theme';
import { flatten } from '@/lib/Flat';
import { replaceUrl, isPanel } from '@/utils/base';
import emitter from '@/utils/bus';
export default {
  name: 'ThemeStyle',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      list: [],
      replaceUrl: replaceUrl,
      loading: false
    }
  },
  watch: {
    show: {
      handler: function (val) {
        if (val) {
          document.body.append(this.$el);
        } else {
          this.$el.parentElement && this.$el.parentElement.removeChild(this.$el);
        }
      }
    },
    'currentCom.id': {
      handler: function () {
        this.getThemeList();
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      editPanelState: state => state.editor.editPanelState
    }),
    ...mapGetters('editor', ['currentCom']),
    themeId () {
      if (this.currentCom && this.currentCom.other) {
        return this.currentCom.other.comThemeId
      }
      return ''
    }
  },
  created () {
    emitter.on('refreshTheme', () => {
      this.getThemeList();
    })
  },
  methods: {
    close () {
      this.$emit('update:show', false);
    },
    async getThemeList () {
      if (!this.currentCom) {
        return
      }
      const params = {
        comType: this.currentCom.comType,
        themeType: 'style'
      }
      this.loading = true;
      const res = await themeList(params);
      if (res && res.success) {
        this.list = res.data;
      }
      this.loading = false;
    },
    selectTheme (item) {
      const config = _.cloneDeep(item.config);
      if (isPanel(item.comType)) { // 动态面板 去掉screens选项
        delete config.screens
      }
      const setting = {
        config: config
      };
      const keyObj = flatten(setting);
      const result = _.map(keyObj, (val, key) => {
        return { key: key, value: val }
      })
      const cData = [
        {
          key: 'other.themeUrl',
          value: item.icon
        },
        {
          key: 'other.comThemeId',
          value: item.id
        }
      ]
      result.push(...cData);
      this.loading = true;
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: result
      }).then(() => {
        emitter.emit('updateConfigKey');
      }).finally(() => {
        this.loading = false;
      })
    }
  },
  beforeDestroy () {
    emitter.off('refreshTheme');
    this.$el.parentElement && this.$el.parentElement.removeChild(this.$el);
  }
}
</script>

<style lang="scss" scoped>
.AVYju {
  position: fixed;
  z-index: 12;
  top: 150px;
  right: 332px;
  bottom: 50px;
  width: 300px;
  padding: 20px 12px;
  background: #22242b;
  color: #e8e8e8;
  box-shadow: 0 6px 6px 0 rgb(0 0 0 / 40%);
  overflow-y: auto;
  overflow-x: hidden;
  .v6BHz {
    display: grid;
    grid-template-columns: repeat(2, 132px);
    grid-template-rows: min-content;
    grid-gap: 12px;
    ._2_A_B {
      cursor: pointer;
      ._2czmi {
        position: relative;
        width: 132px;
        height: 100px;
        padding: 15px 10px;
        border: 1px solid #393b4a;
        background-color: var(--background-1);
        margin-bottom: 4px;
        &.active {
          border-color: #2681ff;
        }
      }
    }
  }
  .no-data {
    text-align: center;
  }
}
</style>
