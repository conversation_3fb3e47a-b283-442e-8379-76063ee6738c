<template>
  <div class="config-control">
    <div
      class="config-title nowrap"
      :class="{ 'config-title65': node.depth > 1 }"
      :title="node.data.name"
    >
      <el-tooltip effect="dark" placement="top" v-if="node.data.desc">
        <div
          slot="content"
          :style="{ 'max-width': '15rem' }"
          v-html="node.data.desc"
        ></div>
        <span class="icon icon2 el-icon-question"></span> </el-tooltip
      >{{ node.data.name }}
    </div>
    <div class="width-div">
      <el-button
        class="upload-texts"
        type="primary"
        size="mini"
        @click="openDialog()"
        >编辑HTML</el-button>
    </div>
    <el-dialog
      :visible.sync="showEditHtml"
      :before-close="close"
      title="编辑HTML"
      width="60%"
      append-to-body
      :close-on-click-modal="false"
      top="0"
    >
      <div class="html-content">
        <codemirror
          class="code"
          :style="fontStyle"
          :options="codeOptions"
          ref="codeArea"
          v-model="tempValue"
          :value="tempValue"></codemirror>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary" @click="confirm()" :loading="uploadLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { codemirror } from 'vue-codemirror';
import 'codemirror/lib/codemirror.css';
import 'codemirror/mode/htmlmixed/htmlmixed';
export default {
  name: 'CtlHtml',
  components: {
    codemirror
  },
  data () {
    return {
      showEditHtml: false,
      tempValue: this.value,
      uploadLoading: false,
      fontStyle: {
        fontSize: '12px'
      },
      codeOptions: {},
      modeStyleList: {
        HTML: 'text/html',
        CSS: 'css'
      },
      lintList: ['HTML', 'CSS'] // Current languages which has lint function
    }
  },
  created () {
    // this.tempValue = this.value;
  },
  mounted () {
    this.initEditor();
  },
  methods: {
    openDialog () {
      this.showEditHtml = true;
    },
    confirm () {
      this.value = this.clearDomScriptTag(this.tempValue);
      // console.log('过滤后 字符模板', this.value);
      this.showEditHtml = false;
      this.tempValue = this.value;
    },
    close () {
      this.showEditHtml = false;
      // this.tempValue = this.value;
    },
    clearDomScriptTag (str) {
      const reg = /<script[^>]*>([\S\s]*?)<\/script>/gim;
      // 清除标签内 相关 xss 安全代码
      const reg1 = /javascript:/gim;
      const reg2 = / *.js/gim;
      if (reg.test(str)) {
        str = str.replace(reg, '');
        this.$message.error('非法标签 <script> 已被替换为空');
      }
      if (reg1.test(str)) {
        str = str.replace(reg1, '');
        this.$message({
          text: '非法代码javascript:已被替换为空',
          type: 'error',
          offset: 60
        });
      }
      if (reg2.test(str)) {
        str = str.replace(reg2, '');
        this.$message({
          text: '非法*.js已被替换为空',
          type: 'error',
          offset: 100
        });
      }
      return str;
    },
    initEditor () {
      this.tempValue = this.value;
    }
  }
}
</script>

<style lang="scss" scoped>
// .config-control {
// }
.html-content {
  min-height: 300px;
  max-height: 600px;
  overflow-y: auto;
  ::v-deep .code {
    height: 100%;
    overflow: hidden;

    .CodeMirror {
      height: 100% !important;
      resize: none;
      outline: none;
      border: none;
      font-family: inherit;
      font-size: inherit;
      background-color: #191F28;
    }

    .CodeMirror-gutter {
      background: #29313A;
    }
    .CodeMirror-linenumber {
      color: none;
    }
  }
}
.dialog-footer {
  ::v-deep .el-button {
    padding: 4px 12px;
  }
}
</style>
