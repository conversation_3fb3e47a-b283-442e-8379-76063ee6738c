<template>
  <div class="skeleton-layout-panel"
    :class="`panel-${selectLayoutFolder.resourceFolder}`"
    v-clickoutside="close"
    >
    <div class="skeleton-classify panel-border">
      <div>
        {{selectLayoutFolder.resourceFolder}}
      </div>
    </div>
    <div class="skeleton-list">
      <div class="item-card"
      :class="{'select-card': '' === selectCardId}"
      @click="selectSkeleton()">
        <div class="img-size">
          <div class="file-name file-none">空</div>
        </div>
        <div class="file-name">空</div>
      </div>
      <div class="item-card"
        :class="{'select-card': imgfile.resourceId === selectCardId}"
        v-for="(imgfile, index) in selectLayoutFolder.list"
        :key="'skeleton' + index"
        :title="imgfile.name"
        @click="selectSkeleton(imgfile, selectLayoutFolder.id)">
        <div class="img-size">
          <el-image
            class="img-style"
            style="width: 100%; height: 100%"
            :src="imgfile.url"
            :lazy="true"
            >
            <div slot="placeholder" class="image-slot" style="line-height: 30px;text-align: center;color: #797979;">
              加载中<span class="dot">...</span>
            </div>
          </el-image>
        </div>
        <div class="file-name">{{imgfile.name}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getResourceList } from '@/api/workspace';
import { replaceUrl } from '@/utils/base';
export default {
  name: 'SkeletonLayoutPanel',
  data () {
    return {
      // 当前选择的 skeleton 文件夹
      selectLayoutFolder: {
        resourceFolder: '',
        id: -1,
        list: []
      },
      selectCardId: ''
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      skeletonList: state => state.editor.skeletonList
    })
  },
  created () {
    if (this.skeletonList && this.skeletonList.length > 0) {
      this.layoutFolder(this.skeletonList);
    } else {
      this.getResourceListFun();
    }
    this.selectCardId = this.screenInfo.config.skeleton && this.screenInfo.config.skeleton.fileId;
  },
  methods: {
    layoutFolder (list) {
      const width = this.screenInfo.config.width;
      const layoutMap = {}
      list.forEach(item => {
        const key = item.resourceFolder;
        layoutMap[key] = item;
      });
      switch (true) {
        case width <= 2200: {
          layoutMap['1920x1080'] && (this.selectLayoutFolder = layoutMap['1920x1080']);
          break;
        }
        case width > 2200 && width <= 3100: {
          layoutMap['2560x1080'] && (this.selectLayoutFolder = layoutMap['2560x1080']);
          break;
        }
        case width > 3100 && width <= 5700: {
          layoutMap['3840x1080'] && (this.selectLayoutFolder = layoutMap['3840x1080']);
          break;
        }
        case width > 5700 && width <= 8600: {
          layoutMap['7680x1890'] && (this.selectLayoutFolder = layoutMap['7680x1890']);
          break;
        }
        case width > 8600: {
          layoutMap['9600x3240'] && (this.selectLayoutFolder = layoutMap['9600x3240']);
          break;
        }
        default:
          break;
      }
    },
    async getResourceListFun () {
      this.loading = true;
      const res = await getResourceList({ resourceType: 'skeletonPicture' });
      this.loading = false;
      if (res.success) {
        if (res.data.length === 0) {
          this.$message.error('获取资源文件为空，请联系管理员');
          return;
        }
        const fileListImg = this.getFileList(res.data)[0].folderList;
        this.layoutFolder(fileListImg);
        this.$store.commit('editor/updateSkeletonList', fileListImg);
      }
    },
    // 数据映射
    getFileList (list) {
      list.forEach(item => {
        item.folderList.forEach(folder => {
          folder.name = folder.resourceFolder;
          folder.resourceList.forEach(img => {
            img.name = img.resourceName.substring(0, img.resourceName.lastIndexOf('.'));
            img.isSelect = false;
            img.isShow = true;
            img.url = replaceUrl(process.env.VUE_APP_SERVER_URL + img.resourceUrl);
          });
          folder.list = folder.resourceList;
        });
      });
      return list;
    },
    selectSkeleton (item = { resourceId: '', url: '' }, folderId = -1) {
      const tempItem = {
        folderId: folderId,
        fileId: item.resourceId,
        url: item.url
      };
      this.selectCardId = item.resourceId;
      // dispatch
      this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.skeleton', value: tempItem }]);
    },
    close () {
      this.$store.commit('editor/updateDrawerSelect', { type: 'layout', value: false })
    }
  }

}
</script>

<style lang="scss" scoped>
.skeleton-layout-panel {
  display: flex;
  flex-direction: column;
  width: 272px;
  min-height: 262px;
  max-height: 262px;
  overflow: hidden;
  background: #1F2430;
  box-shadow: 0px 48px 128px -16px rgba(4, 8, 16, 0.64), 0px 16px 64px -16px rgba(4, 8, 16, 0.72), 0px 0px 1px rgba(4, 8, 16, 0.32);
  border-radius: 8px;
  user-select: none;
  .skeleton-classify {
    color: #bcc9d4;
    display: flex;
    align-items: center;
    height: 28px;
    line-height: 28px;
    padding: 0 16px;
  }
  .panel-border {
    justify-content: space-between;
    border-bottom: 1px solid #3a4659;
  }
  .skeleton-list {
    overflow: auto;
    padding: 8px 4px;
    display: flex;
    flex-flow: row wrap; // flex 布局下超出自动换行
  }
  .item-card {
    margin:0 4px 2px 4px;
    cursor: pointer;
    width: 120px;
    .img-size {
      height: 65px;
    }
    .file-none {
      background: #303643;
      height: 65px;
      line-height: 65px;
    }
    &.select-card {
      .img-style {
        background: #3a7ada;
        // background-blend-mode: 'screen, overlay'; // 混合模式
      }
      .file-name {
        color: #3a7ada;
      }
      .file-none {
        background: #3a7ada7d;
      }
    }
  }
  .img-style {
    background: #526D95;
  }
  .file-name {
    height: 20px;
    line-height: 20px;
    text-align: center;
    font-family: PingFang SC;
    color: rgba(255, 255, 255, 0.7);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  ::v-deep .el-image__error {
    background: #303643;
    font-size: 10px;
    color: #C0C4CC;
  }
}

.panel-1920x1080 {
  width: 396px;
  .item-card {
    width: 120px;
  }
}
.panel-2560x1080 {
  width: 362px;
  .item-card {
    width: 167px;
  }
}
.panel-3840x1080 {
  width: 428px;
  .item-card {
    width: 200px;
  }
}
.panel-7680x1890 {
  width: 300px;
  .item-card {
    width: 283px;
    .img-size {
      height: 80px;
    }
    .file-none {
      height: 80px;
      line-height: 80px;
    }
  }
}
.panel-9600x3240 {
  width: 203px;
  .item-card {
    width: 183px;
    .img-size {
      height: 70px;
    }
    .file-none {
      height: 70px;
      line-height: 70px;
    }
  }
}
</style>
