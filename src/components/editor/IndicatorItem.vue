<template>
  <div class="item-container" draggable @dragstart="handleDragStart" v-if="info.isShow">
    <div class="indicator-list">
      <div class="indicator-list-item">
        <el-image
          v-if="info.ecryptUrl"
          style="width:128px;height:76px"
          :src="info.ecryptUrl"
        >
        </el-image>
        <div class="indicator-item-img" v-else :style="{backgroundColor:'#17191c'}"></div>
        <!-- <div class="indicator-item-title">{{ info.resourceName }}</div> -->
      </div>
      <div class="components-item-text">{{ info.name }}</div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    info: Object
  },
  methods: {
    handleDragStart (e) {
      const data = {
        ...this.info,
        type: 'indicator'
      }
      e.dataTransfer.setData('application/json', JSON.stringify(data));
    }
  }
};
</script>
<style lang="scss" scoped>
.item-container {
  .indicator-list {
    background: #0a0b0d;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 134px;
    height: 114px;
    .indicator-list-item {
      width: 100%;
      cursor: pointer;
      // display: inline-block;
      color: #bcc9d4;
      user-select: none;
      flex: none;
      display: flex;
      height: 92px;
      padding: 8px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 5.156px;
      align-self: stretch;
      background-color: #1F2125;
      overflow: hidden;
      .indicator-item-img {
        width: 128px;
        height: 76px;
        background: url('../../assets/img/defaultIndicator.png') no-repeat center/contain;
      }
    }
    .indicator-item-title {
      font-size: 12px;
      padding: 0 5px;
      text-align: left;
      overflow: hidden;
      background: #212326;
      white-space: nowrap;
      text-overflow: ellipsis;
      line-height: 22px;
      width: 100%;
    }
    .components-item-text {
      width: 100%;
      font-size: 12px;
      padding: 0 5px;
      color:var(--seatom-type-800);
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      line-height: 22px;
    }
  }
}
</style>
