<template>
  <div class="page-setting">
    <template v-if="!particlesConfigVisible">
      <div class="page-title">页面设置</div>
      <div class="page-content">
        <el-form label-width="90px" label-position="left" size="small">
          <el-form-item label="屏幕大小" v-if="!isDynamicScreen" size="mini">
            <span slot="label" v-if="screenInfo.type === 'mobile'">
              屏幕大小 <el-tooltip content="设计稿的设备尺寸，如iPhone8则宽高设置为375*667" placement="top"><i class="el-icon-info"></i></el-tooltip>
            </span>
            <el-select v-model="screenType" class="full-w bm20" v-if="screenInfo.type == 'pc'">
              <el-option v-for="opt in screenList" :key="opt.id" :value="opt.value" :label="opt.label"></el-option>
            </el-select>
            <div class="content-wrap">
              <div class="input-row">
                <el-input v-model.number.trim="width" @blur="val => changeSize('width', val)" class="item">
                  <template slot="suffix">W</template>
                </el-input>
                <el-input v-model.number.trim="height" @blur="val => changeSize('height', val)" class="item">
                  <template slot="suffix">H</template>
                </el-input>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="背景色" size="mini">
            <div class="input-row">
              <el-color-picker v-model="backgroundColor" show-alpha></el-color-picker>
              <el-input v-model="backgroundColor" class="item2"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="背景图" size="mini">
            <span class="backThumbnailStyle">开启压缩</span><el-switch v-model="backThumbnail"></el-switch>
            <UploadImage v-model="backgroundImage"/>
          </el-form-item>
          <el-form-item label="平铺方式" size="mini">
            <el-select v-model="backgroundRepeat" class="full-w">
              <el-option v-for="opt in repeatOpt" :key="opt.id" :value="opt.value" :label="opt.label"></el-option>
            </el-select>
          </el-form-item>
          <template v-if="screenInfo.type == 'pc'">
            <el-form-item label="粒子效果" v-if="!isDynamicScreen" size="mini">
              <el-select v-model="backgroundParticlesType" class="full-w">
                <el-option
                  v-for="opt in particlesTypeOpt"
                  :key="opt.id"
                  :value="opt.value"
                  :label="opt.label"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="backgroundParticlesType !== 'STYLE_NONE' && backgroundParticlesType" size="mini">
              <el-button type="primary" size="mini" @click="particlesConfigVisible = true">
                编辑粒子效果
              </el-button>
            </el-form-item>
          </template>
          <template v-if="!isDynamicScreen">
            <template v-if="screenInfo.type === 'pc'">
              <el-form-item label="栅格间距" size="mini">
                <el-input-number v-model="gridSpace" controls-position="right" class="full-w" :min="1"></el-input-number>
              </el-form-item>
              <el-form-item label="缩放设置">
                <el-radio-group v-model="scaleType" class="scale-radio">
                  <el-radio-button v-for="opt in scaleOpt" :key="opt.value" :label="opt.value" class="tempButton">
                    <el-tooltip placement="bottom" :content="opt.label">
                      <hz-icon :name="opt.icon"></hz-icon>
                    </el-tooltip>
                  </el-radio-button>
                </el-radio-group>
              </el-form-item>
            </template>
            <el-form-item label="封面" size="mini">
              <UploadImage v-model="thumbnail"/>
            </el-form-item>
            <el-form-item size="mini">
              <el-button type="primary" size="mini" @click="record" :loading="loading">
                {{ loading ? '截屏中' : '截屏' }}
              </el-button>
            </el-form-item>
          </template>
        </el-form>
      </div>
    </template>
    <template v-else>
      <ParticlesConfig
        @closeParticlesConfig="particlesConfigVisible = false"
        :custom-style.sync="backgroundParticlesCustom"
        :particles-type="backgroundParticlesType"
      />
    </template>
  </div>
</template>

<script>
import ParticlesConfig from '@/components/editor/ParticlesConfig'
import UploadImage from './data-source/UploadImage'
import { replaceUrl, screenshot } from '@/utils/base';
import { mapState } from 'vuex'
import { screenList, repeatOpt, scaleOpt, particlesTypeOpt } from '@/common/constants'
import * as PARTICLES_STYLE from '@/common/particlesTheme'

export default {
  name: 'PageSetting', // 页面设置
  props: {},
  components: {
    UploadImage,
    ParticlesConfig
  },
  data () {
    return {
      screenList: screenList,
      repeatOpt: repeatOpt,
      scaleOpt: scaleOpt,
      particlesTypeOpt: particlesTypeOpt,
      width: 1920,
      height: 1080,
      loading: false,
      particlesConfigVisible: false
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    isDynamicScreen () { // 是否为动态面板创建的大屏
      return this.screenInfo.isDynamicScreen
    },
    screenType: {
      get: function () {
        const keyMap = ['1920*1080', '1366*768', '1024*768']
        if (keyMap.includes(`${this.width}*${this.height}`)) {
          return `${this.width}*${this.height}`
        } else {
          return 'diy'
        }
      },
      set: function (val) {
        if (val !== 'diy') {
          const [width, height] = val.split('*')
          this.width = +width
          this.height = +height
        } else {
          this.width = 1440
          this.height = 768
        }
        this.$store.dispatch('editor/updateScreenInfo', [
          { key: 'config.width', value: this.width },
          { key: 'config.height', value: this.height }
        ])
      }
    },
    backgroundColor: {
      get: function () {
        return this.screenInfo.config.backgroundColor
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundColor', value: val }])
      }
    },
    backThumbnail: {
      get: function () {
        return this.screenInfo.config.backThumbnail
      },
      set: function (val) {
        // let backImage = this.backgroundImage
        // const serch = backImage.includes('?isZip=false')
        // if (val && !serch && backImage) {
        //   backImage = backImage + '?isZip=true'
        // } else {
        //   backImage = backImage.replace('true')
        // }
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backThumbnail', value: val }])
        // this.$store.dispatch('deitor/updateScreeInfo', [{ key: 'config.backThumbnail', value: val }])
      }
    },
    backgroundImage: {
      get: function () {
        return replaceUrl(this.screenInfo.config.backgroundImage)
      },
      set: function (val) {
        // if (this.backThumbnail && val !== '') {
        //   val = val + '?isZip=true'
        // }
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundImage', value: val }])
      }
    },
    backgroundRepeat: {
      get: function () {
        return this.screenInfo.config.backgroundRepeat
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundRepeat', value: val }])
      }
    },
    backgroundParticlesType: {
      set (val) {
        this.backgroundParticlesCustom = this.getParticlesConfig(val)
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundParticlesType', value: val }])
      },
      get () {
        return this.screenInfo.config.backgroundParticlesType
      }
    },
    backgroundParticlesCustom: {
      get () {
        if (this.screenInfo.config.backgroundParticlesCustom) {
          return this.screenInfo.config.backgroundParticlesCustom
        } else {
          if (this.backgroundParticlesType === 'STYLE_NONE') {
            return {}
          } else {
            return this.getParticlesConfig(this.screenInfo.config.backgroundParticlesType)
          }
        }
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundParticlesCustom', value: val }])
      }
    },
    gridSpace: {
      get: function (val) {
        return this.screenInfo.config.gridSpace
      },
      set: function (val) {
        if (val !== this.gridSpace) {
          this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.gridSpace', value: val }])
        }
      }
    },
    scaleType: {
      get: function (val) {
        return this.screenInfo.config.scaleType
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.scaleType', value: val }])
      }
    },
    thumbnail: {
      get: function (val) {
        return replaceUrl(this.screenInfo.config.thumbnail)
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.thumbnail', value: val }])
      }
    }
  },
  watch: {
    screenInfo: {
      handler: function (info) {
        this.width = info.config.width
        this.height = info.config.height
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async record () { // 改为前端截屏
      this.loading = true
      const dom = document.getElementById('screenshoter')
      try {
        screenshot(dom, url => {
          this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.thumbnail', value: url }]).finally(() => {
            this.loading = false
          })
        })
      } catch (error) {
        this.loading = false
      }
    },
    changeSize (type) {
      if (this[type] === '') {
        this[type] = this.screenInfo.config[type]
        return
      }
      this.$store.dispatch('editor/updateScreenInfo', [{ key: `config.${type}`, value: this[type] }])
    },
    getParticlesConfig (styleType) {
      const config = PARTICLES_STYLE[styleType]
      const temp = {}
      if (styleType === 'STYLE_NONE') return temp
      temp.color = []
      temp.opacity = [config.particles.opacity.value.min, config.particles.opacity.value.max]
      temp.size = [config.particles.size.value.min, config.particles.size.value.max]
      temp.speed = [config.particles.move.speed.min, config.particles.move.speed.max]
      temp.number = config.particles.number.value
      styleType === 'STYLE_METEOR'
        ? config.particles.stroke.color.value.forEach(item => temp.color.push({ value: item }))
        : config.particles.color.value.forEach(item => temp.color.push({ value: item }))
      return temp
    }
  }
}
</script>

<style lang="scss" scoped>
.page-setting {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .page-title {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
  }
  .page-content {
    flex: 1;
    padding: 20px 16px;
    overflow: auto;
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
      color: var(--control-text-color);
    }
    .el-radio__label {
      font-size: 12px;
      color: #bfbfbf;
    }
    .el-upload-dragger {
      width: 200px;
      height: 90px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
      }
      .uploaded-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        object-fit: contain;
      }
    }
  }
  .single-line ::v-deep .el-radio {
    display: block;
    line-height: 26px;
  }
  .full-w {
    width: 100%;
  }
  .bm20 {
    margin-bottom: 18px;
  }
  .input-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item {
      width: 100px;
    }
    .item1 {
      width: 100px;
      margin-left: 10px
    }
    .item2 {
      flex: 1;
      margin-left: 10px;
    }
  }
  .radio-group {
    .el-radio {
      margin-right: 20px;
    }
  }
  .scale-radio ::v-deep {
    .tempButton {
      width: 20%;
    }
    .el-radio-button--mini .el-radio-button__inner {
      padding: 7px 12px;
    }
    .el-radio-button:first-child .el-radio-button__inner,
    .el-radio-button:last-child .el-radio-button__inner {
      border-radius: 0;
    }
  }
  .backThumbnailStyle {
    font-size: 12px;
    color: #bfbfbf;
    margin-right: 6px;
  }
}
</style>
