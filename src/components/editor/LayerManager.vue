<template>
  <div class="layer-manager">
    <div class="layer-manager-top">
      <div class="layer-num">图层</div>
      <div class="layer-manager-layout-selector">
        <i
          class="seatom-icon datav-font icon-logo"
          :class="{ selected: thumbnailSelect }"
          title="缩略图版"
          @click.stop="thumbnailSelect = true"
        >
        </i>
        <i
          class="seatom-icon datav-font icon-list"
          :class="{ selected: !thumbnailSelect }"
          title="文字版"
          @click.stop="thumbnailSelect = false"
        >
        </i>
        <i
          class="seatom-icon datav-font icon-back"
          @click.stop="handleBack"
        ></i>
      </div>
    </div>
    <div class="layer-toolbar layer-toolbar-top">
      <div v-if="screenInfo.type === 'pc'" style="display: flex">
      <template v-for="item in toolbarTop">
      <div :title="(item.icon === 'layer-hideOthers' && closeSoloShow) ? '取消'+item.title : item.title"
      :key="item.icon" class="toolbar-item"
      :class="[{'toolbar-item-disable':(!cancelGroupShow && item.title === '取消分组' ||
      groupShow && item.title === '分组' || soloShow && item.title === '单独显示当前图层')}]"
      @click="toolbarClick(item.title)">
        <hz-icon :name="item.icon" style="width:16px;height:16px;"></hz-icon>
      </div>
      </template>
      </div>
    </div>
    <div class="layer-manager-wrap">
      <LayerTree :isThumbnail="thumbnailSelect" />
    </div>
    <!-- 搜索结果页面 -->
    <div class="layer-panel" :class="{ '--hide': searchState }">
      <div class="search-result">
        <div class="search-result-title">
          <span
            >{{ '"' + searchWord + '"' }}搜索结果{{
              "(" + layerList + ")"
            }}</span
          >
          <div
            class="close-btn"
            @click="closeSearchPage()"
          >
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="search-result-body" style="color:#fff">
          <!-- 用LayerTree组件展示，仅支持单击及右键（功能用在原图层上）功能 -->
          <LayerSearchTree v-if="searchWord.length" :searchWord="searchWord"
          :isThumbnail="thumbnailSelect"></LayerSearchTree>
          <div class="empty-data" v-if="!layerList">未找到图层</div>
        </div>
      </div>
    </div>

    <div class="layer-toolbar layer-toolbar-bottom">
      <el-input
        v-if="screenInfo.type === 'pc'"
        v-model.trim="searchWord"
        placeholder="搜索图层"
        size="mini"
        class="my-project project-input"
        clearable
      />
    </div>
  </div>
</template>

<script>
import LayerTree from '@/components/editor/LayerTree';
import { mapState, mapGetters } from 'vuex';
import LayerSearchTree from '@/components/editor/LayerSearchTree';
import canvasBus from '@/utils/canvasBus';

export default {
  name: 'LayerManager',
  inject: ['getLayerTree'],
  components: {
    LayerTree,
    LayerSearchTree
  },

  data () {
    return {
      thumbnailSelect: true,
      searchWord: '',
      searchState: true,
      // searchCount: [],
      searchLayerCount: 0,
      originTree: [],
      folderShowOrHide: true
    }
  },
  // created () {
  //   canvasBus.on('saveOriginTree', this.saveOriginTreeFun);
  // },
  computed: {
    ...mapState({
      screenLayers: state => state.editor.screenLayers,
      screenComs: state => state.editor.screenComs,
      screenInfo: (state) => state.editor.screenInfo,
      sceneId: (state) => state.editor.sceneId,
      pageId: (state) => state.editor.pageId
      // showGroup: (state) => state.editor.showGroup
    }),
    ...mapGetters('editor', ['getComDataById']),
    layerTree () {
      return this.getLayerTree();
    },
    layerList () {
      // if (!this.searchWord.length) return 0;
      const layerList = _.cloneDeep(this.loadedNodes); // this.loadedNodes;// this.layerTree.loadedNodes;
      this.searchLayerList(layerList);
      const searchLayer = this.searchCount;
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.searchCount = 0;
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      if (this.searchWord.length) this.searchState = false;
      return searchLayer;
    },
    loadedNodes () {
      const loadedNodes = this.layerTree.loadedNodes;
      if (this.screenInfo.screenType === 'scene') {
        if (this.pageId) {
          return loadedNodes.filter((node) => {
            return (
              node.data.pageId === this.pageId ||
              (node.data.sceneId === this.sceneId && !node.data.pageId)
            );
          });
        } else {
          return loadedNodes.filter((node) => {
            return node.data.sceneId === this.sceneId && !node.data.pageId;
          });
        }
      } else {
        return loadedNodes;
      }
    },
    toolbarTop () {
      const toolbarTop = [
        {
          icon: 'layer-creatGroup',
          title: '分组'
        },
        {
          icon: 'layer-cancelGroup',
          title: '取消分组'
        },
        {
          icon: 'layer-hideOthers',
          title: '单独显示当前图层'
        }
      ]
      const folder = this.folderShowOrHide ? {
        icon: 'layer-folder',
        title: '展开'
      } : {
        icon: 'layer-folder-open',
        title: '收起'
      }
      toolbarTop.push(folder);
      return toolbarTop;
    },
    // searchCount () {
    //   const tree = this.loadedNodes;
    //   if (this.searchWord !== '') {
    //     tree.forEach(node => {
    //       if (node.searchNode && node.searchNode === true) delete node.searchNode;
    //       // if (node.data.groupName && node.collapse === true) {
    //       //   node.collapse = false;
    //       //   this.layerTree.collapseChildNodes(node.data, false);
    //       // }
    //       if ((node.data.groupName && node.data.groupName.includes(this.searchWord)) ||
    //       (node.data.type !== 'group' && this.getComDataById(node.data.id).alias.includes(this.searchWord)) ||
    //       (node.parent.data.groupName && node.parent.data.groupName.includes(this.searchWord))) {
    //         node.searchNode = true;
    //       }
    //     })
    //   } else {
    //     tree.forEach(node => {
    //       if (node.searchNode && node.searchNode === true) delete node.searchNode;
    //     })
    //   }
    //   let searchCount = 0;
    //   if (this.searchWord !== '') {
    //     tree.forEach(node => {
    //       if ((node.data.groupName && node.data.groupName.includes(this.searchWord)) ||
    //       (node.data.type !== 'group' && this.getComDataById(node.data.id).alias.includes(this.searchWord))) {
    //         searchCount++;
    //       }
    //     })
    //   }
    //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    //   if (searchCount) this.searchState = false;
    //   return searchCount;
    // },
    groupShow () {
      let show = false;
      const selectedNodes = [];
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNodes.push(item);
      })
      if (selectedNodes.length > 1) {
        for (let i = 1; i < selectedNodes.length; i++) {
          // 判断所选组件是否是同一层级
          if (!(selectedNodes[0]?.parent?.data?.id === selectedNodes[i]?.parent?.data?.id)) {
            show = true; break;
          }
          // 判断在场景大屏中是否是同一场景或页面
          if (!(selectedNodes[0]?.data?.pageId === selectedNodes[i]?.data?.pageId &&
          selectedNodes[0]?.data?.sceneId === selectedNodes[i]?.data?.sceneId)) {
            show = true; break;
          }
        }
      }
      if (selectedNodes.length < 1) return true;
      // if (this.showGroup === false) return true;
      return show;
    },
    cancelGroupShow () {
      const selectedNode = []; let show = false;
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNode.push(item.data.id);
      })
      if (selectedNode.length) {
        let sum = 0;
        selectedNode.forEach(item => {
          if (item.includes('groups_')) sum++;
        })
        if (sum > 0) {
          show = true
        }
      }
      return show;
    },
    soloShow () {
      const selectedNodes = []; let show = true;
      this.layerTree.loadedNodes.forEach(item => {
        if (item.selected) selectedNodes.push(item);
      })
      if (selectedNodes.length) show = false;
      return show;
    },
    closeSoloShow () {
      const selectedNodes = this.layerTree.getSelectedNodes();
      let selectedNodeId = selectedNodes.map(n => {
        if (n.data.type !== 'group') return n.data.id;
        else {
          this.nodeOfGroup = [];
          this.findNodeOfGroup(n);
          return this.nodeOfGroup;
        }
      });
      /* 用于查找选中图层id */
      selectedNodeId = selectedNodeId.flat();
      const soloShowCompList = Object.values(this.screenComs);
      let show = true;
      for (let i = 0; i < soloShowCompList.length; i++) {
        if (!((selectedNodeId.includes(soloShowCompList[i].id) && soloShowCompList[i].show) ||
        (!selectedNodeId.includes(soloShowCompList[i].id) && !soloShowCompList[i].show))) {
          show = false; break;
        }
      }
      return show;
    }
  },
  methods: {
    handleBack () {
      this.$store.commit('editor/updateEditPanelSelect', {
        type: 'layer',
        value: false
      });
      this.$store.commit('indicator/updateEditSelect', {
        type: 'layer',
        value: false
      })
    },
    toolbarClick (judge) {
      switch (judge) {
        case '分组': canvasBus.emit('ctx_click', 'group'); break;
        case '取消分组': canvasBus.emit('ctx_click', 'cancelGroup'); break;
        case '单独显示当前图层': !this.closeSoloShow ? canvasBus.emit('ctx_click', 'soloShow')
          : canvasBus.emit('ctx_click', 'closeSoloShow'); break;
        case '展开': this.openTree(); break;
        case '收起': this.closeTree(); break;
      }
      if (judge === '分组' || judge === '取消分组') canvasBus.emit('select_node', null);
    },
    findNodeOfGroup (node) {
      node.children.forEach(item => {
        if (item.children) {
          this.findNodeOfGroup(item);
        } else {
          if (!this.nodeOfGroup) this.nodeOfGroup = [];
          this.nodeOfGroup.push(item.data.id);
        }
      });
    },
    searchLayerList (layerList) {
      layerList.forEach(item => {
        if (this.searchWord.length !== 0 && ((item.data.groupName && item.data.groupName.includes(this.searchWord)) ||
        (item.data.type !== 'group' && this.getComDataById(item.data.id).alias.includes(this.searchWord)) ||
        (item.data.type !== 'group' && item.data.id.includes(this.searchWord))
        )) {
          // if (item.searchNode) {
          if (!this.searchCount) this.searchCount = 0;
          this.searchCount++;
          // }
        }
        // if (item.children && item.children.length) {
        //   this.searchLayerList(item.children);
        // }
      })
    },
    // saveOriginTreeFun (data) {
    //   this.originTree = data;
    // },
    closeSearchPage () {
      this.searchState = true;
      this.searchWord = '';
      this.closeTree();
      canvasBus.emit('select_node', null);
      // let i = 0; let j = 0;
      // while (i < this.originTree.length) { // !(j === this.layerTree.loadedNodes.length && i === this.originTree.length)) { // j > i) {
      //   const tree = this.layerTree.loadedNodes;
      //   if (tree[j].data.id === this.originTree[i].data.id && tree[j].data.type === 'group') {
      //     tree[j].collapse = this.originTree[i].collapse;
      //     this.layerTree.collapseChildNodes(tree[j].data, tree[j].collapse);
      //     this.recursionChangeCollapse(tree[j].children, this.originTree[i].children);
      //     i++;
      //     j++;
      //   } else if (tree[j].data.id !== this.originTree[i].data.id) { // && tree[j].data.type === 'group') {
      //     tree[j].collapse = true;
      //     this.layerTree.collapseChildNodes(tree[j].data, tree[j].collapse);
      //     j++;
      //   } else if (tree[j].data.id === this.originTree[i].data.id) {
      //     i++;
      //     j++;
      //   }
      // }
      // while (this.layerTree.loadedNodes.length > this.originTree.length) {
      //   this.layerTree.loadedNodes.pop();
      // }
    },
    // recursionChangeCollapse (tree, originTree) {
    //   tree.forEach((node, index) => {
    //     node.collapse = originTree[index].collapse;
    //     this.layerTree.collapseChildNodes(node.data, node.collapse);
    //     if (node.children) this.recursionChangeCollapse(node.children, originTree[index].children);
    //   })
    // },
    openTree () {
      // 循环将带有分组的tree树全部展开
      for (let i = 0; i < this.layerTree.loadedNodes.length; i++) {
        const tree = this.layerTree.loadedNodes;
        if (tree[i].data.groupName && tree[i].collapse === true) {
          tree[i].collapse = false;
          this.layerTree.collapseChildNodes(tree[i].data, false);
        }
      }
      this.folderShowOrHide = false;
    },
    closeTree () {
      // 循环将带有分组的tree树全部收起
      for (let i = this.layerTree.loadedNodes.length - 1; i >= 0; i--) {
        const tree = this.layerTree.loadedNodes;
        if (tree[i].data.groupName && tree[i].collapse === false) {
          tree[i].collapse = true;
          this.layerTree.collapseChildNodes(tree[i].data, true);
        }
      }
      this.folderShowOrHide = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.layer-manager {
  display: flex;
  flex-direction: column;
  width: 200px;
  height: 100%;
  overflow: hidden;
  flex: auto;
  transition: 0.3s ease;
  .layer-manager-top {
    background: var(--seatom-panel-title-bg);
    text-indent: 10px;
    height: 30px;
    color: #bcc9d4;
    line-height: 30px;
    position: relative;
    user-select: none;
    .layer-num {
      line-height: 30px;
      vertical-align: middle;
    }
    .layer-manager-layout-selector {
      position: absolute;
      top: 0;
      right: 1px;
      font-size: 0;
      letter-spacing: 4px;
      .datav-font {
        text-indent: 0;
        cursor: pointer;
        transition: color 0.2s;
        &:hover {
          color: #fff;
        }
        &.selected {
          color: var(--seatom-main-color);
        }
      }
    }
  }
  .layer-toolbar {
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    // padding: 0 18px;
    margin-right: 1px;
    background: #20242a;
    flex: none;
    &.layer-toolbar-top {
      border-bottom: 1px solid var(--seatom-panel-border-color);
      .toolbar-item {
        margin: 0 5px;
        height: 16px;
        cursor: pointer;
      }
    }
    &.layer-toolbar-bottom {
      border-top: 1px solid var(--seatom-panel-border-color);
    }
    ::v-deep .el-input__inner {
      background-color: rgba(29, 38, 46, 0.54);
      color: #fff;
      padding: 0 6px;
      border: 1px solid #2681ff;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .layer-manager-wrap {
    flex: auto;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    color: #bcc9d4;
    font-size: 12px;
    list-style: none;
    line-height: 2;
    user-select: none;
    position: relative;
    background: var(--seatom-panel-color);
  }
  .layer-panel {
    flex: none;
    position: absolute;
    top: 30px;
    left: 0;
    width: 199px;
    height: calc(100% - 60px);
    z-index: 4;
    background: var(--seatom-panel-color);
    transition: width 0.25s ease-in-out;
    overflow: hidden;
    box-shadow: 1px 0 #000;
    &.--hide {
      width: 0;
      // height: 0;
    }
    .search-result {
      padding: 10px;
      height: 100%;
      &-title {
        line-height: 16px;
        color: #2681ff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        padding: 5px;
        display: flex;
        justify-content: space-between;
        .close-btn {
          cursor: pointer;
          width: 16px;
          height: 16px;
          text-align: center;
          position: relative;
          transition: 0.3s ease;
          i {
            color: #2681ff;
          }
          &:hover {
            transform: rotateZ(90deg);
          }
        }
      }
      &-body {
        overflow: auto;
        height: calc(100% - 27px);
      }
    }
    .empty-data {
      height: 220px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #a1aeb3;
    }
  }
}
.toolbar-item-disable {
  pointer-events: none;
  opacity: 0.5;
}
</style>
