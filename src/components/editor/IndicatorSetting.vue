<template>
  <div class="page-setting">
    <template>
      <div class="page-title">指标配置</div>
      <div class="page-content">
        <el-form label-width="90px" label-position="left" size="small">
          <el-form-item label="尺寸" size="mini">
            <!-- <span slot="label" v-if="screenInfo.type === 'mobile'">
              尺寸 <el-tooltip content="设计稿的设备尺寸，如iPhone8则宽高设置为375*667" placement="top"><i class="el-icon-info"></i></el-tooltip>
            </span> -->
            <!-- <el-select v-model="screenType" class="full-w bm20" v-if="screenInfo.type == 'pc'">
              <el-option v-for="opt in screenList" :key="opt.id" :value="opt.value" :label="opt.label"></el-option>
            </el-select> -->
            <div class="content-wrap">
              <div class="input-row">
                <el-input v-model.number.trim="width" @blur="val => changeSize('width', val)" class="item">
                  <template slot="suffix">W</template>
                </el-input>
                <el-input v-model.number.trim="height" @blur="val => changeSize('height', val)" class="item">
                  <template slot="suffix">H</template>
                </el-input>
              </div>
            </div>
          </el-form-item>
         <el-form-item label="背景色" size="mini">
            <div class="input-row">
              <el-color-picker v-model="backgroundColor" show-alpha></el-color-picker>
              <el-input v-model="backgroundColor" class="item2"></el-input>
            </div>
          </el-form-item>
          <template v-if="screenInfo.parentId === '1'">
            <el-form-item label="封面">
              <UploadImage v-model="thumbnail" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="mini" @click="record" :loading="recordLoading">{{ recordLoading ? '截屏中' : '截屏' }}</el-button>
            </el-form-item>
          </template>
           <!-- <el-form-item label="背景图" size="mini">
            <span class="backThumbnailStyle">开启压缩</span><el-switch v-model="backThumbnail"></el-switch>
            <UploadImage v-model="backgroundImage"/>
          </el-form-item>
          <el-form-item label="平铺方式" size="mini">
            <el-select v-model="backgroundRepeat" class="full-w">
              <el-option v-for="opt in repeatOpt" :key="opt.id" :value="opt.value" :label="opt.label"></el-option>
            </el-select>
          </el-form-item> -->
          <!-- <template v-if="screenInfo.type == 'pc'">
            <el-form-item label="粒子效果" v-if="!isDynamicScreen" size="mini">
              <el-select v-model="backgroundParticlesType" class="full-w">
                <el-option
                  v-for="opt in particlesTypeOpt"
                  :key="opt.id"
                  :value="opt.value"
                  :label="opt.label"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="backgroundParticlesType !== 'STYLE_NONE' && backgroundParticlesType" size="mini">
              <el-button type="primary" size="mini" @click="particlesConfigVisible = true">
                编辑粒子效果
              </el-button>
            </el-form-item>
          </template> -->
          <!-- <template v-if="!isDynamicScreen">
            <template v-if="screenInfo.type === 'pc'">
              <el-form-item label="栅格间距" size="mini">
                <el-input-number v-model="gridSpace" controls-position="right" class="full-w" :min="1"></el-input-number>
              </el-form-item>
              <el-form-item label="缩放设置">
                <el-radio-group v-model="scaleType" class="scale-radio">
                  <el-radio-button v-for="opt in scaleOpt" :key="opt.value" :label="opt.value" class="tempButton">
                    <el-tooltip placement="bottom" :content="opt.label">
                      <hz-icon :name="opt.icon"></hz-icon>
                    </el-tooltip>
                  </el-radio-button>
                </el-radio-group>
              </el-form-item>
            </template>
            <el-form-item label="封面" size="mini">
              <UploadImage v-model="thumbnail"/>
            </el-form-item>
            <el-form-item size="mini">
              <el-button type="primary" size="mini" @click="record" :loading="loading">
                {{ loading ? '截屏中' : '截屏' }}
              </el-button>
            </el-form-item>
          </template> -->
        </el-form>
        <ConfigTree
          :key="configKey"
          :treeData="configTreeData"
          :configObj="configObj"
          @change="handleConfigTreeChange" />
        <el-form label-width="90px" label-position="top" size="small">
          <el-form-item label="字段配置" size="mini">
            <el-table class="mapping-t" empty-text="请新增字段" :data="fieldMappingCp.length ? fieldMappingCp : []" size="mini">
                <el-table-column label="字段" width="70" align="center">
                  <template slot-scope="scope">
                    <el-input size="mini" v-model="scope.row.source" ref="fieldInput" @change="saveFieldMapping"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="说明" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-input size="mini" v-model="scope.row.description" @change="saveFieldMapping"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="" width="40" align="center">
                  <template slot-scope="scope">
                    <el-button @click="handleDelete(scope)" type="text" size="mini">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </template>
                </el-table-column>
            </el-table>
            <div class="text-btn" @click="addMappingField">
              <i class="el-icon-plus"></i>
              <span class="btn-name">新增</span>
            </div>
          </el-form-item>
        </el-form>
        <template>
          <div class="data-source">
            <el-steps direction="vertical" :key="sourceType || 'stepkey'">
              <el-step title="配置数据">
                <div class="content-block" slot="description">
                  <div class="input-row row">
                    <div class="item nowrap">数据源类型</div>
                    <el-select v-model="sourceType" size="mini" class="full-w">
                      <el-option v-for="item in sourceTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </div>
                  <source-detail :type="sourceType" :jsonData="jsonData" :source="source" @sourceChange="sourceChange" @staticDataChange="val => staticDataChange(val, currentCom.id)"></source-detail>
                </div>
              </el-step>
              <el-step title="配置请求参数" v-if="sourceType === 'api'">
                <div class="content-block" slot="description">
                  <div class="config-control">
                    <div class="config-title nowrap">Base URL</div>
                    <div class="width-div">
                      <el-input v-model="source.api.data.baseUrl" readonly size="mini" class="w100"></el-input>
                    </div>
                  </div>
                  <div class="config-control">
                    <div class="config-title nowrap">请求方式</div>
                    <div class="width-div">
                      <el-select v-model="source.api.data.method" @change="commitUpdate('api')" size="mini" class="w100">
                        <el-option v-for="opt in methodOpt" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div class="config-control">
                    <div class="config-title nowrap">
                      动态参数
                      <el-tooltip placement="top-start" content="如需在header或body中使用动态参数，可在此处设置参数并引用">
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </div>
                    <div class="width-div">
                      <el-button type="plain" size="mini" @click="editDynamicParams()">编辑</el-button>
                    </div>
                  </div>
                  <div class="editor-wrap">
                    <div class="editor-title">请求头(JSON格式)</div>
                    <CodeEditor v-model="source.api.data.headers" @blur="commitUpdate('api')" :options="editorOpt" key="api-code"/>
                  </div>
                  <div class="config-control">
                    <div class="config-title nowrap">路径</div>
                    <div class="width-div">
                      <el-input v-model="source.api.data.path" @blur="commitUpdate('api')" size="mini"></el-input>
                    </div>
                  </div>
                  <div class="config-control">
                    <div class="config-title nowrap">参数</div>
                    <div class="width-div">
                      <el-input v-model="source.api.data.params" @blur="commitUpdate('api')" size="mini"></el-input>
                    </div>
                  </div>
                  <div class="editor-wrap"
                      v-if="source.api.data.method === 'POST' || source.api.data.method === 'PUT' || source.api.data.method === 'PATCH'">
                    <div class="editor-title">Body(JSON格式)</div>
                    <CodeEditor v-model="source.api.data.body" @blur="commitUpdate('api')" :options="editorOpt" key="api-code2"/>
                  </div>
                  <!-- <div class="config-control">
                    <div class="config-title width-auto nowrap">
                      <el-checkbox v-model="source.api.data.reqFromBack" @change="commitUpdate('api')" size="mini"
                                  class="mr5"></el-checkbox>
                      后端发起请求
                    </div>
                  </div> -->
                  <div class="config-control">
                    <div class="config-title width-auto nowrap">
                      <el-checkbox v-model="source.api.data.needCookie" @change="commitUpdate('api')" size="mini"
                                  class="mr5"></el-checkbox>
                      需要cookie
                    </div>
                  </div>
                </div>
              </el-step>
              <el-step title="配置查询语句" v-else-if="sourceType === 'mongodb'">
                <div class="slot-title" slot="title">
                  <span class="c-title">配置字段</span>
                </div>
                <div class="content-block db-block" slot="description">
                  <div class="config-control">
                    <div class="config-title nowrap">工作表名称</div>
                    <div class="width-div flex-row">
                      <div class="table-name nowrap" :title="source.mongodb.data.tbName">{{ source.mongodb.data.tbName }}</div>
                      <el-button @click="selectTable('mongodb')" class="ml10" type="plain" size="mini">选择</el-button>
                    </div>
                  </div>
                  <div class="editor-wrap">
                    <CodeEditor v-model="source.mongodb.data.sql" @blur="commitUpdate('mongodb')" key="mongodb-code"/>
                  </div>
                  <div class="row-btn pl0">
                    <el-button type="primary" size="mini" @click="getData" :disabled="!needUpdate">确认配置</el-button>
                  </div>
                </div>
              </el-step>
              <el-step title="配置字段" v-if="workTableDb.includes(sourceType)">
                <div class="slot-title" slot="title">
                  <span class="c-title">配置字段</span>
                </div>
                <div class="content-block db-block" slot="description">
                  <template v-if="sourceType === 'dmc'">
                    <div class="config-control">
                      <div class="config-title nowrap">工作表名称</div>
                      <div class="width-div flex-row">
                        <div class="table-name nowrap" :title="source.dmc.data.tbName">{{ source.dmc.data.tbName }}</div>
                        <el-button @click="selectTable('dmc')" class="ml10" type="plain" size="mini">选择</el-button>
                      </div>
                    </div>
                    <div class="config-control" v-if="source.dmc.data.tbName">
                      <div class="config-title nowrap">添加字段</div>
                      <div class="width-div flex-row">
                        <el-button @click="addDmcField('calculationField')" type="plain" size="mini">计算字段</el-button>
                        <el-button @click="addDmcField('groupField')" type="plain" size="mini">分组字段</el-button>
                      </div>
                    </div>
                    <div class="config-control" v-if="source.dmc.data.tbName">
                      <div class="width-div flex-row">
                        <div class="field-box" style="width:100%">
                          <span class="field-div" size="mini" :key="item.name" v-for="(item, index) in calfieldList">
                            {{ item.name }}
                            <i
                              @click="editCalfield(item)"
                              class="el-icon-edit el-input__icon"
                              slot="suffix">
                            </i>
                            <i
                              @click="deleteCalfield(item, index)"
                              class="el-icon-close el-input__icon"
                              slot="suffix">
                            </i>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="config-control">
                      <div class="config-title nowrap w45_px">维度</div>
                      <div class="width-div flex-row">
                        <div class="field-box">
                          <span class="field-div" size="mini" :key="item.fid"
                                v-for="(item, index) in source.dmc.data.fields.dimension">
                            {{ item.name }}
                            <el-select v-model="item.calculation" size="mini" v-show="item.data_type === 'date' && item.field_type === 0"
                                      @change="commitUpdate('dmc')">
                              <el-option v-for="opt in dateCalculation" :key="opt.value" :value="opt.value"
                                        :label="opt.label"></el-option>
                            </el-select>
                            <i
                              class="el-icon-close el-input__icon"
                              @click="deleteField('dmc', 'dimension', index)"
                              slot="suffix">
                            </i>
                          </span>
                          <span class="field-div add-field" v-if="source.dmc.data.tbName" @click="addDmcField('dimension')" size="mini">
                            <i class="el-icon-plus el-input__icon">
                            </i>
                            字段
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="config-control">
                      <div class="config-title nowrap w45_px">数值</div>
                      <div class="width-div flex-row">
                        <div class="field-box">
                          <div class="field-div" :key="index" v-for="(item, index) in source.dmc.data.fields.numericalValue">
                            <span class="nowrap">{{ item.name }}</span>
                            <el-select v-model="item.calculation" size="mini" v-show="item.data_type === 'number'"
                                      @change="commitUpdate('dmc')">
                              <el-option v-for="opt in numCalculation" :key="opt.value" :value="opt.value"
                                        :label="opt.label" ></el-option>
                            </el-select>
                            <el-select v-model="item.calculation" size="mini" v-show="item.data_type !== 'number' && item.data_type !== 'date'"
                                      @change="commitUpdate('dmc')">
                              <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                                        :label="opt.label" ></el-option>
                            </el-select>
                            <el-select v-model="item.calculation" size="mini" v-show="item.data_type === 'date' && item.field_type === 0"
                                      @change="commitUpdate('dmc')">
                                <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                                          :label="opt.label"></el-option>
                            </el-select>
                            <i
                              class="el-icon-close el-input__icon"
                              @click="deleteField('dmc' ,'numericalValue', index)"
                              slot="suffix">
                            </i>
                          </div>
                          <span class="field-div add-field" v-if="source.dmc.data.tbName" @click="addDmcField('numericalValue')"
                                size="mini">
                              <i class="el-icon-plus el-input__icon">
                              </i>
                              字段
                            </span>
                        </div>
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <div class="config-control">
                      <div class="config-title nowrap">工作表名称</div>
                      <div class="width-div flex-row">
                        <div class="table-name nowrap" :title="source[sourceType].data.tbName">{{ source[sourceType].data.tbName }}</div>
                        <el-button @click="selectTable(sourceType)" class="ml10" type="plain" size="mini">选择</el-button>
                      </div>
                    </div>
                    <div class="config-control">
                      <div class="config-title nowrap w45_px">维度</div>
                      <div class="width-div flex-row">
                        <div class="field-box">
                          <span class="field-div" size="mini" :key="item.fid"
                                v-for="(item, index) in source[sourceType].data.fields.dimension">
                            {{ item.name }}
                            <i
                              class="el-icon-close el-input__icon"
                              @click="deleteField(sourceType, 'dimension', index)"
                              slot="suffix">
                            </i>
                          </span>
                          <span class="field-div add-field" v-if="source[sourceType].data.tbName" @click="addField(sourceType, 'dimension')" size="mini">
                            <i class="el-icon-plus el-input__icon">
                            </i>
                            字段
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="config-control">
                      <div class="config-title nowrap w45_px">数值</div>
                      <div class="width-div flex-row">
                        <div class="field-box">
                          <div class="field-div" :key="index" v-for="(item, index) in source[sourceType].data.fields.numericalValue">
                            <span class="nowrap">{{ item.name }}</span>
                            <el-select v-model="item.calculation" size="mini" v-show="item.type === 'number'"
                                      @change="commitUpdate(sourceType)">
                              <el-option v-for="opt in numCalculation" :key="opt.value" :value="opt.value"
                                        :label="opt.label"></el-option>
                            </el-select>
                            <el-select v-model="item.calculation" size="mini" v-show="item.type !== 'number'"
                                      @change="commitUpdate(sourceType)">
                              <el-option v-for="opt in stringCalculation" :key="opt.value" :value="opt.value"
                                        :label="opt.label"></el-option>
                            </el-select>
                            <i
                              class="el-icon-close el-input__icon"
                              @click="deleteField(sourceType, 'numericalValue', index)"
                              slot="suffix">
                            </i>
                          </div>
                          <span class="field-div add-field" v-if="source[sourceType].data.tbName" @click="addField(sourceType, 'numericalValue')"
                                size="mini">
                              <i class="el-icon-plus el-input__icon">
                              </i>
                              字段
                            </span>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="row-btn">
                    <el-button type="primary" size="mini" @click="getData" :disabled="!needUpdate">确认配置</el-button>
                  </div>
                </div>
              </el-step>
              <el-step title="后端数据筛选"
                v-if="workTableDb.includes(sourceType)"
                >
                <div class="content-block" slot="description">
                  <div class="config-control">
                    <div class="config-title nowrap">
                      数据筛选：
                    </div>
                    <div class="width-div">
                      <el-button @click="openDataSelect" type="plain" size="mini" plain>
                        <span v-if="selectList">已添加{{ selectList }}个筛选条件</span>
                        <span v-else>添加筛选条件</span>
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-step>
              <el-step title="前端数据处理">
                <div class="content-block" slot="description">
                  <div class="config-control">
                    <div class="config-title nowrap width-auto">
                      <el-checkbox v-if="!filtersEnable" v-model="filtersEnable" class="mr5"></el-checkbox>
                      数据过滤器：
                    </div>
                    <div class="width-div">
                      <el-button @click="filterConfig" type="plain" size="mini" plain>
                        <span v-if="!!filterNum">已启用{{ filterNum }}个过滤器</span>
                        <span v-else>添加过滤器</span>
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-step>
              <el-step title="显示条目数"
                v-if="['csv_file', 'api', 'mysql', 'dmdb','dmc', 'postgresql', 'oracle', 'dashboard', 'excel'].includes(sourceType)">
                <div class="content-block" slot="description">
                  <div class="config-control">
                    <div class="config-title nowrap">
                      <el-checkbox v-model="source[sourceType].data.isLimit" @change="commitUpdate(sourceType)"
                                    class="mr5"></el-checkbox>
                      开启：
                    </div>
                    <div class="width-div">
                      <el-input-number v-model="source[sourceType].data.limitNum" :min="0" controls-position="right"
                                        @change="commitUpdate(sourceType)" size="mini" class="w100">
                      </el-input-number>
                    </div>
                  </div>
                </div>
              </el-step>
              <el-step title="映射字段">
                <div class="content-block pb10" slot="description">
                  <el-table class="mapping-t" empty-text="请新增字段" :data="fieldMappingCp.length ? fieldMappingCp : []" size="mini">
                    <el-table-column label="字段" width="70" align="center">
                      <template slot-scope="scope">
                        <span style="margin-left: 5px">{{ scope.row.source }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="映射" prop="target" align="center" class="map" width="80">
                      <template slot-scope="scope">
                        <el-select
                          v-if="fieldMappingCp.length"
                          v-model="scope.row.target"
                          size="mini"
                          clearable
                          :title="scope.row.target"
                          @change="saveFieldMapping">
                          <el-option v-for="opt in fieldOptions" :key="opt.id" :value="opt.name" :label="opt.name"></el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="说明" prop="description" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column label="状态" align="center" width="60">
                      <template slot-scope="scope">
                        <span v-if="isMatch(scope.row) === 0">未匹配</span>
                        <span v-if="isMatch(scope.row) === 1" class="el-icon-check t-icon green"></span>
                        <span v-if="isMatch(scope.row) === 2" class="el-icon-close t-icon red"></span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-step>
            </el-steps>
          </div>
        </template>
        <!-- 查看响应结果 -->
        <div class="result-btn">
          <el-button type="primary" size="mini" @click="getResult">查看数据响应结果</el-button>
        </div>
      </div>
    </template>
    <!-- 指标数据源设置 -->
    <!-- 创建数据源 -->
    <template v-if="['csv_file', 'excel', 'json', 'api', 'mysql', 'dmdb', 'postgresql', 'oracle', 'mongodb', 'websocket'].includes(sourceType)">
      <CreateDataSource ref="create" @update="updateDatastorage" :type="sourceType"/>
    </template>
    <template v-if="sourceType === 'dmc'">
      <!-- 选择dmc工作表 -->
      <CreateDmcTable ref="createDmcTable" @update="getDmcTable"/>
      <!-- 新增dmc计算字段 -->
      <CreateCalculationField ref="createCalculationField" @update="getCaField"/>
      <!-- 新增dmc分组字段 -->
      <CreateGroupField ref="createGroupField" @update="getCaField"/>
      <!-- 选择dmc维度、数值字段 -->
      <SelectAllField ref="selectAllField" @update="getSelectAllField"/>
    </template>
    <!-- mysql -->
    <template v-if="sourceType === 'mysql'">
      <CreateMySqlTable ref="createMySqlTable" @update="getMysqlTable" />
      <SelectMysqlFields ref="selectMysqlFields" @update="getSelectMysqlFields"/>
    </template>
    <!-- 达梦数据库 -->
    <template v-if="sourceType === 'dmdb'">
      <CreateDMdbTable ref="createDMdbTable" @update="getDMdbTable" />
      <SelectDMdbFields ref="selectDMdbFields" @update="getSelectDMdbFields"/>
    </template>
    <!-- postgresql -->
    <template v-if="sourceType === 'postgresql'">
      <CreatePostgresqlTable ref="createPostgresqlTable" @update="getPostgresqlTable"/>
      <SelectPostgresqlFields ref="selectPostgresqlFields" @update="getSelectPostgresqlFields"/>
    </template>
    <!-- oracle -->
    <template v-if="sourceType === 'oracle'">
      <CreateOracleTable ref="createOracleTable" @update="getOracleTable"/>
      <SelectOracleFields ref="selectOracleFields" @update="getSelectOracleFields"/>
    </template>
    <!-- 数据筛选 -->
    <template v-if="['dmc', 'mysql', 'postgresql','dmdb', 'oracle', 'excel'].includes(sourceType)">
      <DataSelect ref="dataSelect" @update="updateDataSelect" :type="sourceType" />
    </template>
    <!-- excel -->
    <template v-if="sourceType === 'excel'">
      <CreateExcelTable ref="createExcelTable" @update="getExcelTable" />
      <SelectExcelFields ref="selectExcelFields" @update="getSelectExcelFields"/>
    </template>
    <!-- 从仪表盘导入 -->
    <CreateDashBoard v-if="sourceType === 'dashboard'" ref="createDash"  @update="getDashList" />
    <!-- mongodb -->
    <CreateMongoDBTable v-if="sourceType === 'mongodb'" ref="createMongoDBTable" @update="getMongoDBTable" />
    <!-- 动态参数 -->
    <DynamicParams v-if="sourceType === 'api'" ref="dynamicParams" @updateApiFunc="updateApiFunc" />
    <!-- 过滤器配置 -->
    <FilterConfig ref="filter" :sourceData="sourceData" />
    <!-- 数据量过大提醒 -->
    <LimitDataTip ref="limit" @update="updateLimitData" />
    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import dataUtil from '@/utils/data'
import { postData } from '@/api/datastorage'
import { replaceUrl, screenshot } from '@/utils/base';
import { mapGetters, mapState } from 'vuex'
import { calculatefieldList, deleteCalfield } from '@/api/calculatefield'
import { dateCalculation, numCalculation, stringCalculation, screenList, repeatOpt, sourceTypeOpt, methodOpt } from '@/common/constants'
import { updateIndicator } from '@/api/workspace';

export default {
  name: 'PageSetting', // 页面设置
  props: {
    backGroundImg: {
      type: String,
      default: ''
    }
  },
  inject: ['callbackManager'],
  components: {
    SourceDetail: () => import('@/components/editor/data-source/SourceDetail'),
    CodeEditor: () => import('@/components/editor/data-source/CodeEditor'),
    CreateDataSource: () => import('@/components/editor/data-source/CreateDataSource'),
    CreateDmcTable: () => import('@/components/editor/data-source/CreateDmcTable'),
    CreateCalculationField: () => import('@/components/editor/data-source/CreateCalculationField'),
    CreateGroupField: () => import('@/components/editor/data-source/CreateGroupField'),
    SelectAllField: () => import('@/components/editor/data-source/SelectAllField'),
    DataSelect: () => import('@/components/editor/data-source/DataSelect'),
    CreateMySqlTable: () => import('@/components/editor/data-source/CreateMySqlTable'),
    CreateDMdbTable: () => import('@/components/editor/data-source/CreateDMdbTable'),
    SelectDMdbFields: () => import('@/components/editor/data-source/SelectDMdbFields'),
    SelectMysqlFields: () => import('@/components/editor/data-source/SelectMysqlFields'),
    CreateMongoDBTable: () => import('@/components/editor/data-source/CreateMongoDBTable'),
    CreatePostgresqlTable: () => import('@/components/editor/data-source/CreatePostgresqlTable'),
    SelectPostgresqlFields: () => import('@/components/editor/data-source/SelectPostgresqlFields'),
    CreateOracleTable: () => import('@/components/editor/data-source/CreateOracleTable'),
    SelectOracleFields: () => import('@/components/editor/data-source/SelectOracleFields'),
    CreateDashBoard: () => import('@/components/editor/data-source/CreateDashBoard'),
    CreateExcelTable: () => import('@/components/editor/data-source/CreateExcelTable'),
    SelectExcelFields: () => import('@/components/editor/data-source/SelectExcelFields'),
    FilterConfig: () => import('@/components/editor/data-source/FilterConfig'),
    LimitDataTip: () => import('@/components/editor/data-source/LimitDataTip'),
    DynamicParams: () => import('@/components/editor/data-source/DynamicParams'),
    UploadImage: () => import('@/components/editor/data-source/UploadImage.vue'),
    ConfigTree: () => import('@/components/editor/ConfigTree')
  },
  data () {
    return {
      jsonData: [],
      screenList: screenList,
      repeatOpt: repeatOpt,
      dateCalculation: Object.freeze(dateCalculation),
      numCalculation: Object.freeze(numCalculation),
      stringCalculation: Object.freeze(stringCalculation),
      methodOpt: Object.freeze(methodOpt),
      editorOpt: Object.freeze({
        mode: 'application/json',
        lint: true
      }),
      width: 1920,
      height: 1080,
      loading: false,
      recordLoading: false,
      sourceTypeList: sourceTypeOpt.filter(opt => opt.value !== 'datacontainer'),
      workTableDb: ['mysql', 'dmdb', 'oracle', 'excel', 'dmc', 'postgresql'],
      noList: [{ source: '任意' }],
      fieldType: '',
      source: {
        api: {
          data: {
            sourceId: '',
            baseUrl: '',
            method: '',
            headers: '{}',
            path: '',
            params: '',
            body: '{}',
            reqFromBack: false,
            needCookie: false,
            isLimit: false,
            limitNum: 20
          }
        },
        csv_file: {
          data: {
            sourceId: '',
            isLimit: false,
            limitNum: 20
          }
        },
        dashboard: {
          data: {
            name: '',
            sourceId: '',
            isLimit: false,
            limitNum: 20
          }
        },
        json: {
          data: {
            sourceId: ''
          }
        },
        mysql: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        dmdb: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        mongodb: {
          data: {
            sourceId: '',
            tbName: '',
            sql: '',
            isLimit: false,
            limitNum: 20
          }
        },
        postgresql: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        oracle: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        websocket: {
          data: {
            duration: 10,
            durationType: 'minute',
            sourceId: ''
          }
        },
        dmc: {
          data: {
            tbName: '',
            tbId: '',
            sourceId: '',
            folderId: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        excel: {
          data: {
            tbName: '',
            sourceId: '',
            fields: {
              numericalValue: [],
              dimension: []
            },
            where: {
              enable: false,
              whereCondition: [],
              orderCondition: []
            },
            isLimit: false,
            limitNum: 20
          }
        },
        datacontainer: {
          data: {
            dataContainerComId: ''
          }
        }
      },
      fieldMappingCp: [],
      sourceData: '[]',
      calfieldList: [],
      currentCompId: '',
      needUpdate: true,
      configKey: Math.random()
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      comsData: state => state.editor.comsData,
      screenFilters: state => state.editor.screenFilters
    }),
    ...mapGetters('editor', ['currentConfigId', 'currentCom']),
    isDynamicScreen () { // 是否为动态面板创建的大屏
      return this.screenInfo.isDynamicScreen
    },
    backgroundColor: {
      get: function () {
        return this.screenInfo.config.backgroundColor
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundColor', value: val }])
      }
    },
    backThumbnail: {
      get: function () {
        return this.screenInfo.config.backThumbnail
      },
      set: function (val) {
        // let backImage = this.backgroundImage
        // const serch = backImage.includes('?isZip=false')
        // if (val && !serch && backImage) {
        //   backImage = backImage + '?isZip=true'
        // } else {
        //   backImage = backImage.replace('true')
        // }
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backThumbnail', value: val }])
      }
    },
    backgroundImage: {
      get: function () {
        return replaceUrl(this.screenInfo.config.backgroundImage)
      },
      set: function (val) {
        // if (this.backThumbnail && val !== '') {
        //   val = val + '?isZip=true'
        // }
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundImage', value: val }])
      }
    },
    backgroundRepeat: {
      get: function () {
        return this.screenInfo.config.backgroundRepeat
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.backgroundRepeat', value: val }])
      }
    },
    scaleType: {
      get: function (val) {
        return this.screenInfo.config.scaleType
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.scaleType', value: val }])
      }
    },
    thumbnail: {
      get: function (val) {
        // this.screenInfo.config.thumbnail = backGroundImg
        return replaceUrl(this.currentCom?.icon)
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'icon', value: val }
          ]
        })
        updateIndicator({ 'config.backgroundImage': val }, { id: this.$route.query.indicatorId })
      }
    },
    sourceType: { // 数据源类
      get: function () {
        return this.currentCom?.dataConfig.dataResponse.sourceType
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'dataConfig.dataResponse.sourceType', value: val }
          ]
        }).then(() => {
          this.getData()
        })
      }
    },
    filtersEnable: { // 是否启用过滤器
      get: function () {
        return this.currentCom.dataConfig.dataResponse.filters.enable
      },
      set: function (val) {
        this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'dataConfig.dataResponse.filters.enable', value: val }
          ]
        })
      }
    },
    fieldMapping () {
      return this.currentCom.dataConfig.fieldMapping
    },
    filterNum () { // 启用的过滤器数量
      const filters = this.currentCom.dataConfig.dataResponse.filters.list;
      const enableFilters = filters.filter(item => item.enable);
      return enableFilters.length
    },
    selectList () {
      const where = this.currentCom.dataConfig.dataResponse.source[this.sourceType] ? this.currentCom.dataConfig.dataResponse.source[this.sourceType].data.where : null
      if (where) {
        return where.whereCondition.length + where.orderCondition.length
      } else {
        return 0
      }
    },
    fieldOptions () { // 字段映射集合
      let result = []
      try {
        const keys = []
        const sourceData = JSON.parse(this.sourceData)
        const filterData = dataUtil.filterData(sourceData || [], this.validFilters)
        filterData.forEach(item => {
          const key = Object.keys(item)
          keys.push(...key)
        })
        const uniKeys = Array.from(new Set(keys))
        result = uniKeys.map((key, idx) => {
          return {
            id: idx,
            name: key
          }
        })
      } catch (e) {}
      return result
    },
    validFilters () {
      if (_.isEmpty(this.currentCom)) return []
      const filters = this.currentCom.dataConfig.dataResponse.filters
      if (!filters.enable) return []
      return _.filter(filters.list, { enable: true }).map(({ id }) => this.screenFilters[id])
    },
    configTreeData () {
      const obj = {}
      obj.border = JSON.parse(this.currentCom.controlConfig || '{}')?.border
      return obj
    },
    configObj () {
      const obj = {}
      obj.border = this.currentCom.config.border
      return obj
    },
    isMatch () { // 匹配状态 0：未匹配 1：匹配成功 2：匹配失败
      return function (item) {
        if (item.target === '' || item.target === undefined) {
          return 0
        }
        return item.target &&
        this.fieldOptions.findIndex(opt => opt.name === item.target) > -1 ? 1 : 2
      }
    }
  },
  created () {
    this.$nextTick(() => {
      this.fieldMappingCp = _.cloneDeep(this.currentCom.dataConfig.fieldMapping)
      this.source = _.cloneDeep(this.currentCom.dataConfig.dataResponse.source)
      this.sourceData = JSON.stringify(this.comsData[this.currentConfigId] || [])
      this.jsonData = JSON.parse(this.sourceData)
      const tips = _.cloneDeep(this.currentCom.dataConfig.dataResponse.tips || {});
      this.tips = Object.assign({}, tips);
      this.getData()
    })
  },
  mounted () {
    this.$nextTick(() => {
      this.currentCompId = this.currentCom.id;
      this.calculatefieldList()
    })
  },
  watch: {
    screenInfo: {
      handler: function (info) {
        this.width = info.config.width
        this.height = info.config.height
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleConfigTreeChange ({ path, value }) {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [{ key: `config.${path}`, value: value }]
      });
    },
    editDynamicParams () { // 编辑动态参数
      this.$refs.dynamicParams.showDialog(this.source.api.data.func);
    },
    staticDataChange (val, compId) {
      try {
        this.$store.dispatch('editor/updateScreenCom', {
          id: compId,
          keyValPairs: [
            { key: 'staticData', value: val }
          ]
        }).then(() => {
          this.getData(true)
        })
      } catch (e) {
        this.$message.warn('内容错误')
      }
    },
    async getData (updateStatic = true) { // 获取static/csv/api/mysql后台数据
      if (this.sourceType === 'websocket') return true;
      const componentId = this.currentConfigId
      const _var = await dataUtil.getApiParams(this.currentCom.dataConfig.dataResponse, this.callbackManager());
      try {
        this.loading = true
        const result = await postData({}, [{
          componentId,
          workspaceId: this.screenInfo.workspaceId,
          params: { _var },
          sourceType: this.sourceType
        }])
        this.loading = false
        const res = result[0]
        if (res && res.success) {
          const data = res.data || []
          // 数据量过大提醒弹窗
          if (['csv_file', 'api', 'mysql', 'dmc', 'dmdb', 'postgresql', 'oracle', 'excel'].includes(this.sourceType)) {
            const isLimit = this.source[this.sourceType].data.isLimit
            if (!isLimit && data.length > 100) {
              this.$refs.limit.showDialog()
              return
            }
          }
          const filterData = dataUtil.filterData(res.data, this.validFilters, this.callbackManager())
          const resultData = []
          filterData.forEach(item => {
            const newItem = _.cloneDeep(item)
            const obj = {}
            this.fieldMappingCp.forEach(map => {
              if (Object.prototype.hasOwnProperty.call(newItem, map.target)) {
                obj[map.source] = newItem[map.target]
              }
            })
            resultData.push(obj)
          })
          this.$store.commit('indicator/setIndicatorData', resultData)
          this.$store.commit('editor/updateComData', { componentId, data: res.data })
          this.sourceData = JSON.stringify(this.comsData[this.currentConfigId] || [])
          this.jsonData = JSON.parse(this.sourceData)
          this.autoMapping()
          if (this.sourceType === 'static') {
            if (!updateStatic) return
            this.$nextTick(() => {
              this.jsonData = JSON.parse(this.sourceData)
            })
          }
        } else {
          this.sourceData = JSON.stringify([]);
          this.$store.commit('editor/updateComData', { componentId, data: [] })
        }
      } catch (e) {}
    },
    autoMapping () { // 自动映射
      let comData = [];
      comData = this.comsData[this.currentConfigId];
      if (!_.isEmpty(comData) && this.fieldMapping.length) {
        // 过滤器过滤后的数据再去自动映射
        const filterData = dataUtil.filterData(comData, this.validFilters);
        // if (this.drillDown.length) {
        //   this.drillDown.forEach(e => {
        //     if (e.linkType === 'links') {
        //       filterData = transDrillData(filterData, e)
        //       filterData = filterData.map(item => _.omit(item, ['_id', '_parentId'])); // 去掉_id, _parentId
        //     }
        //   })
        // }
        const origin = _.cloneDeep(this.fieldMapping);
        const fieldMapping = dataUtil.fieldAutoMapping(_.cloneDeep(this.fieldMapping), filterData);
        if (!_.isEqual(origin, fieldMapping)) {
          this.saveFieldMapping()
        }
      }
    },
    commitUpdate: _.debounce(function (type) { // 提交更新
      return this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCompId,
        keyValPairs: [
          { key: `dataConfig.dataResponse.source.${type}`, value: _.cloneDeep(this.source[type]) }
        ]
      }).then(() => {
        if (!['dmc', 'mysql', 'postgresql', 'dmdb', 'oracle'].includes(type)) {
          this.getData()
        }
      }).catch(() => {
        // 不加catch会导致请求失败时控制台报错
      })
    }, 300),
    selectTable (type) {
      const vm = this
      this.fieldType = type
      switch (type) {
        case 'mongodb':
          vm.$refs.createMongoDBTable.showDialog()
          break
        case 'mysql':
          vm.$refs.createMySqlTable.showDialog()
          break
        case 'dmdb':
          vm.$refs.createDMdbTable.showDialog()
          break
        case 'excel':
          vm.$refs.createExcelTable.showDialog('excel')
          break
        case 'postgresql':
          vm.$refs.createPostgresqlTable.showDialog()
          break
        case 'oracle':
          vm.$refs.createOracleTable.showDialog()
          break
        case 'dmc':
          this.$refs.createDmcTable.showDialog()
          break
      }
    },
    addField (sourceType, fieldType) {
      const vm = this
      this.fieldType = fieldType
      switch (sourceType) {
        case 'mysql':
          vm.$refs.selectMysqlFields.showDialog(vm.source.mysql.data)
          break
        case 'dmdb':
          vm.$refs.selectDMdbFields.showDialog(vm.source.dmdb.data)
          break
        case 'excel':
          vm.$refs.selectExcelFields.showDialog(vm.source.excel.data, 'excel')
          break
        case 'postgresql':
          vm.$refs.selectPostgresqlFields.showDialog(vm.source.postgresql.data)
          break
        case 'oracle':
          vm.$refs.selectOracleFields.showDialog(vm.source.oracle.data)
          break
      }
    },
    addDmcField (type) {
      this.fieldType = type
      switch (type) {
        case 'calculationField' :
          this.$refs.createCalculationField.showDialog(this.source.dmc.data)
          break
        case 'groupField' :
          this.$refs.createGroupField.showDialog(this.source.dmc.data)
          break
        case 'dimension' :
          this.$refs.selectAllField.selectDimension(this.source.dmc.data)
          break
        case 'numericalValue' :
          this.$refs.selectAllField.showDialog(this.source.dmc.data)
          break
      }
    },
    editCalfield (item) {
      if (item.field_type === 1) {
        this.$refs.createCalculationField.showDialog(this.source.dmc.data, item)
      } else {
        this.$refs.createGroupField.showDialog(this.source.dmc.data, item)
      }
    },
    deleteField (sourceType, fieldType, index) {
      this.source[sourceType].data.fields[fieldType].splice(index, 1)
      this.commitUpdate(sourceType)
    },
    async calculatefieldList () {
      if (!this.source.dmc.data.tbId) {
        this.calfieldList = []
        return
      }
      const data = {
        workspaceId: this.screenInfo.workspaceId,
        tbId: this.source.dmc.data.tbId
      }
      const res = await calculatefieldList(data)
      if (res && res.success) {
        this.calfieldList = res.data
        // this.comUpdate()
      }
    },
    async deleteCalfield (item) {
      const data = {
        workspaceId: this.screenInfo.workspaceId,
        tbId: this.source.dmc.data.tbId,
        name: item.name,
        fid: item.fid
      }
      const res = await deleteCalfield(data)
      if (res && res.success) {
        this.calculatefieldList()
      } else {
        this.$message.error(res.message)
      }
    },
    getCaField () { // 获取计算字段
      this.calculatefieldList()
    },
    openDataSelect () {
      this.$refs.dataSelect.open(this.source[this.sourceType].data)
    },
    filterConfig () { // 打开过滤器面板
      this.$refs.filter.showFilter()
    },
    getResult () { // 查看响应结果
      this.filterConfig()
    },
    updateLimitData ({ isLimit, limitNum }) { // 更新限制组件数据量
      const source = _.cloneDeep(this.source[this.sourceType])
      source.data.isLimit = isLimit
      source.data.limitNum = limitNum
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [
          { key: `dataConfig.dataResponse.source.${this.sourceType}`, value: source }
        ]
      }).then(() => {
        this.source = _.cloneDeep(this.currentCom.dataConfig.dataResponse.source)
        this.getData()
      })
    },
    updateDataSelect ({ path, value }) {
      if (!path) return false
      _.set(this.source[this.sourceType].data, path, value)
      this.getData()
    },
    async updateDatastorage (data) { // 数据源创建成功 刷新数据源列表
      const sourceType = data.type
      const sourceId = data.id + ''
      if (sourceType === this.sourceType) {
        await this.getDatasourceList(this.sourceType)
        this.source[sourceType].data.sourceId = sourceId
        this.sourceChange(sourceType, sourceId)
      }
    },
    sourceChange (type, sourceId) {
      if (type === 'api') {
        const source = this.sourceList.find(item => item.id === sourceId)
        if (source && source.config) {
          this.source.api.data.baseUrl = source.config.baseUrl
        } else {
          this.source.api.data.baseUrl = ''
        }
      }
      this.commitUpdate(type)
    },
    getDashList (data) { // 获取dashboard数据
      this.source.dashboard.data.sourceId = data.id
      this.source.dashboard.data.name = data.config.name
      this.commitUpdate('dashboard')
      // this.calculatefieldList()
    },
    getDmcTable (data) { // 获取dmc表数据
      this.source.dmc.data.tbId = data.tb_id
      this.source.dmc.data.tbName = data.name
      this.source.dmc.data.fields = {
        numericalValue: [],
        dimension: []
      }
      this.calculatefieldList()
      this.commitUpdate('dmc')
    },
    getMysqlTable (data) { // 获取mysql表数据
      this.source.mysql.data.tbName = data
      this.source.mysql.data.fields = {
        numericalValue: [],
        dimension: []
      }
      // this.calculatefieldList()
      this.commitUpdate('mysql')
    },
    getDMdbTable (data) { // 获取达梦数据库表数据
      this.source.dmdb.data.tbName = data
      this.source.dmdb.data.fields = {
        numericalValue: [],
        dimension: []
      }
      // this.calculatefieldList()
      this.commitUpdate('dmdb')
    },
    getExcelTable (data) { // 获取excel表数据
      this.source.excel.data.tbName = data
      this.source.excel.data.fields = {
        numericalValue: [],
        dimension: []
      }
      // this.calculatefieldList()
      this.commitUpdate('excel')
    },
    getMongoDBTable (data) { // 获取mongodb表数据
      this.source.mongodb.data.tbName = data
      this.source.mongodb.data.fields = {
        numericalValue: [],
        dimension: []
      }
      this.commitUpdate('mongodb')
    },
    getPostgresqlTable (data) { // 获取postgresql表数据
      this.source.postgresql.data.tbName = data
      this.source.postgresql.data.fields = {
        numericalValue: [],
        dimension: []
      }
      this.commitUpdate('postgresql')
    },
    getOracleTable (data) { // 获取oracle表数据
      this.source.oracle.data.tbName = data
      this.source.oracle.data.fields = {
        numericalValue: [],
        dimension: []
      }
      this.commitUpdate('oracle')
    },
    getSelectAllField (data) { // 获取dmc表字段
      if (this.fieldType === 'numericalValue') {
        const numericalValue = data.map(item => {
          if (item.data_type === 'date') {
            return {
              calculation: '',
              ...item
            }
          } else {
            return {
              calculation: 'count',
              ...item
            }
          }
        })
        this.source.dmc.data.fields.numericalValue = [...this.source.dmc.data.fields.numericalValue, ...numericalValue]
      } else {
        this.source.dmc.data.fields.dimension = [...data]
      }
      this.commitUpdate('dmc')
    },
    getSelectMysqlFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.mysql.data.fields.numericalValue = [...this.source.mysql.data.fields.numericalValue, ...data]
      } else {
        this.source.mysql.data.fields.dimension = [...this.source.mysql.data.fields.dimension, ...data]
      }
      this.commitUpdate('mysql')
    },
    getSelectDMdbFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.dmdb.data.fields.numericalValue = [...this.source.dmdb.data.fields.numericalValue, ...data]
      } else {
        this.source.dmdb.data.fields.dimension = [...this.source.dmdb.data.fields.dimension, ...data]
      }
      this.commitUpdate('dmdb')
    },
    getSelectExcelFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.excel.data.fields.numericalValue = [...this.source.excel.data.fields.numericalValue, ...data]
      } else {
        this.source.excel.data.fields.dimension = [...this.source.excel.data.fields.dimension, ...data]
      }
      this.commitUpdate('excel')
    },
    getSelectPostgresqlFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.postgresql.data.fields.numericalValue = [...this.source.postgresql.data.fields.numericalValue, ...data]
      } else {
        this.source.postgresql.data.fields.dimension = [...this.source.postgresql.data.fields.dimension, ...data]
      }
      this.commitUpdate('postgresql')
    },
    getSelectOracleFields (data) {
      if (this.fieldType === 'numericalValue') {
        this.source.oracle.data.fields.numericalValue = [...this.source.oracle.data.fields.numericalValue, ...data]
      } else {
        this.source.oracle.data.fields.dimension = [...this.source.oracle.data.fields.dimension, ...data]
      }
      this.commitUpdate('oracle')
    },
    updateApiFunc (func) {
      this.source.api.data.func = func;
      this.commitUpdate('api');
    },
    async saveFieldMapping () { // 保存映射字段
      let fieldMapping = _.cloneDeep(this.fieldMappingCp)
      // const fieldMapping = this.currentCom.dataConfig.fieldMapping
      if (!_.isEqual(fieldMapping, this.fieldMapping)) {
        const fields = []
        fieldMapping = fieldMapping.map(item => {
          item.type = 'string'
          fields.push({
            description: item.description,
            name: item.source,
            optional: false,
            type: 'string'
          })
          return item
        })
        const filterData = dataUtil.filterData(this.jsonData, this.validFilters, this.callbackManager())
        const resultData = []
        filterData.forEach(item => {
          const newItem = _.cloneDeep(item)
          const obj = {}
          this.fieldMappingCp.forEach(map => {
            if (Object.prototype.hasOwnProperty.call(newItem, map.target)) {
              obj[map.source] = newItem[map.target]
            }
          })
          resultData.push(obj)
        })
        this.$store.commit('indicator/setIndicatorData', resultData)
        await this.$store.dispatch('editor/updateScreenCom', {
          id: this.currentCom.id,
          keyValPairs: [
            { key: 'dataConfig.fieldMapping', value: fieldMapping },
            { key: 'dataConfig.fields', value: fields }
          ]
        })
      }
    },
    addMappingField () { // 添加映射字段
      this.fieldMappingCp.push({
        source: '',
        target: '',
        description: ''
      })
      this.$nextTick(() => {
        const inputEl = this.$refs.fieldInput
        if (inputEl && inputEl.length) {
          inputEl[inputEl.length - 1].focus()
        } else if (inputEl) {
          inputEl.focus()
        }
      })
    },
    handleDelete (data) {
      const index = data.$index
      this.fieldMappingCp.splice(index, 1)
      this.saveFieldMapping()
    },
    record () {
      this.recordLoading = true
      const dom = document.getElementById('screenshoter')
      try {
        screenshot(dom, url => {
          this.$store.dispatch('editor/updateScreenCom', {
            id: this.currentCom.id,
            keyValPairs: [
              { key: 'icon', value: url }
            ]
          })
          updateIndicator({ 'config.backgroundImage': url }, { id: this.$route.query.indicatorId })
          this.recordLoading = false
        }, 'screenshotBackImage')
      } catch (error) {
        this.recordLoading = false
      }
    },
    changeSize (type) {
      if (this[type] === '') {
        this[type] = this.screenInfo.config[type]
        return
      }
      this.$store.dispatch('editor/updateScreenInfo', [{ key: `config.${type}`, value: this[type] }])
    }
  }
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100%;
}
.page-setting {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .page-title {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(36, 38, 46, 0.4);
    background: #2d2f38;
    font-size: 14px;
    text-align: center;
    font-weight: normal;
    color: #d8d8d8;
  }
  .page-content {
    padding: 20px 16px 0;
    overflow: auto;
    flex: 1;
  }
  ::v-deep {
    .editor-wrap {
      margin-bottom: 10px;
    }
    .config-control {
      padding: 4px 0;
      margin: 0 0 10px 0;
      .config-title {
        flex-shrink: 0;
        &.width-auto {
          width: auto;
        }
      }
      .el-collapse-item__header {
        font-size: 14px;
      }
    }
    .el-form-item__label {
      font-size: 14px;
      color: var(--control-text-color);
    }
    .el-radio__label {
      font-size: 12px;
      color: #bfbfbf;
    }
    .el-upload-dragger {
      width: 200px;
      height: 90px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
      }
      .uploaded-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        object-fit: contain;
      }
    }
    .mapping-t {
      line-height: 1;
      .t-icon {
        font-size: 16px;
        &.green {
          color: #00a755;
        }
        &.red {
          color: #ef5350;
        }
      }
    }
    .el-input-number--mini {
      width: 90px;
    }
    .CodeMirror {
      height: 238px;
    }
    .el-step {
      .el-step__description {
        padding-right: 0;
        margin-top: 0;
        overflow: hidden;
      }
      &.is-vertical .el-step__main {
        padding-left: 8px;
      }
      .el-step__icon {
        width: 18px;
        height: 18px;
        font-size: 12px;
        background-color: #2681ff;
      }
      .el-step__icon.is-text {
        border: none;
      }
      .el-step__icon-inner {
        font-weight: 400;
      }
      &.is-vertical .el-step__line {
        width: 2px;
        top: 0;
        bottom: 0;
        left: 8px;
        background-color: #2e343c;
      }
      &.is-vertical .el-step__title {
        font-size: 14px;
        line-height: 18px;
      }
      &.is-vertical .el-step__head {
        width: 18px;
      }
      &.error .el-step__icon {
        background: #F56C6C;
      }
    }
  }
  .single-line ::v-deep .el-radio {
    display: block;
    line-height: 26px;
  }
  .full-w {
    width: 100%;
  }
  .bm20 {
    margin-bottom: 18px;
  }
  .row {
    margin-bottom: 10px;
    padding: 4px 0;
  }
  .input-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item {
      width: 80px;
      flex-shrink: 0;
    }
    .item1 {
      width: 80px;
      margin-left: 10px
    }
    .item2 {
      flex: 1;
      margin-left: 10px;
    }
  }
  .content-block {
    padding: 6px 0;
    overflow: hidden;
    &.db-block {
      border: 1px solid rgba(204, 219, 255, 0.16);
      border-radius: 8px;
      padding: 8px;
      margin: 8px 0 16px;
    }
  }
  .field-box {
    width: 100%;
    min-height: 85px;
    background: #0c0b0b;
    padding-bottom: 8px;
    border-radius: 8px;
    .field-div {
      &.add-field {
        & i {
          color:#2681ff;
        }
        color:#2681ff;
        border-color:#2681ff;
      }
      display: inline-block;
      cursor: pointer;
      margin-left: 8px;
      margin-top: 8px;
      padding: 0px 8px;
      background: #191D25;
      line-height: 22px;
      border-radius: 16px;
      border: 1px solid rgba(76, 82, 95, 0.32);
      .el-input__icon {
        width: 13px;
        height: 22px;
        line-height: 22px;
        cursor: pointer;
      }
      ::v-deep {
        .el-input__inner {
          height: 22px;
          border: none;
          padding-right: 20px;
          background: none;
        }
        .el-select {
          max-width: 105px;
        }
      }
    }
  }
  .row-btn {
    padding-left: 45px;
    &.pl0 {
      padding-left: 0;
    }
  }
  .flex-row {
    display: flex;
    justify-content: space-between;
    .ml10 {
      margin-left: 10px;
    }
    .table-name {
      flex: none;
      width: 110px;
      line-height: 28px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .result-btn {
    text-align: center;
    margin-top: 20px;
    padding-bottom: 30px;
  }
  .text-btn {
    display: inline-block;
    user-select: none;
    cursor: pointer;
    font-size: 14px;
    color: rgba(61, 133, 255, 1);
    margin-top: 10px;
    padding: 4px 8px;
    border-radius: 4px;
    i {
      color: rgba(61, 133, 255, 1);
    }
    .btn-name {
      margin-left: 5px;
    }
    &:hover {
      // background-color: rgba(61, 133, 255, 0.4);
    }
  }
}
</style>
