<template>
  <div class="toolbox-panel">
    <!-- 滤镜配置 -->
    <ColorSetting />
    <!-- 全局主题 -->
    <GolbalTheme />
    <div class="undo-redo" v-if="platform == 'pc'">
      <el-tooltip content="撤销" placement="bottom">
        <hz-icon @click="undo" name="undo" title="撤销"></hz-icon>
      </el-tooltip>
      <el-tooltip content="重做" placement="bottom">
        <hz-icon @click="redo" name="redo" title="重做"></hz-icon>
      </el-tooltip>
      <el-tooltip content="清空组件" placement="bottom">
        <hz-icon :class="{'cursor-not-allowed': isDelAll}" style="opacity: 0.7" @click="clearComs" name="clear-all" title="清空全部"></hz-icon>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import ColorSetting from './ColorSetting';
import GolbalTheme from './GlobalTheme';
import { clearComs } from '@/api/component'
export default {
  name: 'ToolboxPanel',
  components: {
    ColorSetting,
    GolbalTheme
  },
  computed: {
    screenInfo () {
      return this.$store.state.editor.screenInfo;
    },
    screenLayers () {
      return this.$store.state.editor.screenLayers;
    },
    platform () {
      return this.screenInfo.type;
    },
    isDelAll () {
      return !this.screenLayers.length
    }
  },
  data () {
    return {}
  },
  methods: {
    undo () {
      this.$store.commit('editor/undo');
    },
    redo () {
      this.$store.commit('editor/redo');
    },
    clearComs () {
      if (this.isDelAll) return
      const params = {
        screenId: this.screenInfo.id
      };
      const text = '清空后无法恢复，是否要清空组件？';
      this.$confirm(text, '', {
        confirmButtonText: '确定(Enter)',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(async () => {
        const res = await clearComs(params);
        if (res && res.success && res.data) {
          this.$store.commit('editor/clearScreenCom');
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.toolbox-panel {
  position: relative;
  transition: .3s ease;
  width: 100%;
  background: #191c21;
  padding: 8px 30px;
  display: flex;
  align-items: center;
  height: 100%;
  .style-filter-title {
    display: flex;
    align-items: center;
  }
  .undo-redo {
    position: absolute;
    right: 30px;
    .hz-icon {
      // background: #DADADA;
      color: #DADADA;
      font-size: 20px;
      margin-left: 16px;
      cursor: pointer;
    }
  }
}
.cursor-not-allowed {
  cursor: not-allowed !important;
}
.ml-16 {
  margin-left: 16px;
}
</style>
