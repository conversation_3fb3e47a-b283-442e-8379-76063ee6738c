<template>
  <div class="components-panel">
    <div class="panel-title">
      <span class="panel-text">
        组件列表{{'('+filterCompPackges.length+')'}}
        <!-- <i class="seatom-icon datav-font icon-refresh refresh-btn" title="刷新"></i> -->
      </span>
      <i class="seatom-icon datav-font icon-back close-btn" @click.stop="$store.commit('editor/updateEditPanelSelect',{type:'component',value: false});$store.commit('indicator/updateEditSelect',{type:'component',value: false})"></i>
    </div>
    <div class="components-panel-wrapper">
      <div class="wrapper-left-nav">
        <el-menu
          :collapse="true"
          @select="SelectMenu"
          :default-active="activeIndex">
            <el-menu-item
              v-for="menuItem in menuList"
              :key="menuItem.type"
              :index="menuItem.type"
              >
              <hz-icon :name="menuItem.icon"></hz-icon>
              <div class="menu-name">{{menuItem.name}}</div>
              <span slot="title">{{menuItem.title}}</span>
            </el-menu-item>
        </el-menu>
      </div>
      <div class="wrapper-right-tree">
        <div v-for="item in list[activeIndex]" :key="item.title">
          <div>
            <div @click="nodeClick(item)" class="list-title">
              <i class="icon el-icon-arrow-right" :class="{ active: item.open }"></i>
              <span class=""><hz-icon :name="item.icon"></hz-icon>{{item.title+'('+item.child.length+')'}}</span></div>
              <CompList v-show="item.open">
                <CompListItem
                  v-for="comp in item.child"
                  :key="comp.id"
                  :title="comp.alias"
                  :icon="comp.icon"
                  :data="comp"
                  :platforms="comp.platform"
                  draggable
                  @click="handleItemClick($event)">
                </CompListItem>
              </CompList>
          </div>
        </div>
      </div>
    </div>
    <div class="component-panel-wp" :class="{'--hide': searchState}">
      <div class="search-result">
        <div class="search-result-title">
          <span>{{labelShow? '' : '"searchCom"'}}搜索结果{{labelShow? tagsSerchResult.length :searchList.length}}</span>
          <div class="close-btn" @click="searchState = true;searchCom = ''">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="search-result-body">
          <CompListSearItem
            v-for="comp in tagsSerch? tagsSerchResult: searchList"
            :key="comp.id"
            :title="comp.alias"
            :icon="comp.icon"
            :data="comp"
            :version="comp.version"
            :type="comp.type"
            :platforms="comp.platform"
            draggable
            @click="handleItemClick($event)"
          />
          <div class="empty-data" v-if="!searchList.length">
            未找到组件
          </div>
        </div>
      </div>
    </div>
    <div class="components-panel-search">
      <el-input
        v-model.trim="searchCom"
        placeholder="搜索组件"
        size="mini"
        class="my-project project-input"
        clearable>
        <el-button class="search-button" style="height: 28px; line-height: 5px;padding-right:10px; " slot="suffix" type="text" @click="openLabel()">筛选 > </el-button>
      </el-input>
    </div>
    <div v-if="labelShow" class="labelPage">
    <div class="clearSeries">
        <span class="clearButton" @click="clearLable()">清空</span>
        <img class="deletButton" @click="labelShow = false" src="../../assets/img/delete-field.png" alt="">
    </div>
    <div  v-for="(item, i) in labelList" :key="item.categoryId">
            <div class="labelValueTitle">{{ item.categoryName + ":"}}</div>
            <div style="display: flex;">
              <!-- <el-radio  v-for="it in item.tags" :key="it.id" v-model="radio1" label="1" border>{{it.name}}</el-radio> -->
              <div @click="labelSerch(it, index, i)" :class=" it.checked ? 'highLight':'' " class="labelValue" v-for="(it, index) in item.tags" :key="it.id"><span style="margin:10px;">{{it.name}}</span></div>
            </div>
    </div>
    </div>
  </div>
</template>

<script>
import CompList from '@/components/editor/CompList';
import CompListItem from '@/components/editor/CompListItem';
import CompListSearItem from '@/components/editor/CompListSearItem'
import { mapState } from 'vuex';
import { getlabelList, serchList } from '@/api/theme';
import { uuid, isPanel } from '@/utils/base';
import { compPanelList, indicatorCompList, compPanelMenu, indicatorCompPanelMenu, alawysHideComs, mobileHideComs } from '@/common/constants';
import canvasBus from '@/utils/canvasBus';
import emitter from '@/utils/bus';
import PinyinMatch from 'pinyin-match'
// import { log } from '@chenfengyuan/vue-qrcode';
// import { get } from 'fingerprintjs2';

export default {
  name: 'CompPanel',
  props: {
    type: {
      type: String,
      default: 'screen'
    }
  },
  components: {
    CompList,
    CompListItem,
    CompListSearItem
  },

  data () {
    return {
      list: [],
      activeIndex: 'regular',
      searchCom: '',
      searchState: true,
      labelShow: false,
      labelList: [],
      clicking: true,
      selarr: [1],
      tagsList: [],
      tagsSerchList: [],
      tagsSerchResult: [],
      tagsSerch: false
    };
  },

  created () {
    this.handleCompPackges();
    emitter.on('drop_image', this.createImageComp); // 拖拽图片进来出发组件创建
  },

  beforeDestroy () {
    emitter.off('drop_image', this.createImageComp);
  },

  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      compPackges: state => state.editor.compPackges
    }),
    isIndicator () { // 是否是指标面板
      return this.screenInfo.relationCompId?.startsWith('interaction-container-')
    },
    menuList () {
      return this.isIndicator ? indicatorCompPanelMenu : compPanelMenu
    },
    filterCompPackges () {
      let packages = _.cloneDeep(this.compPackges);
      // 按平台类型显示的组件
      packages = packages.filter(item => {
        const platform = item.platform || 'common';
        return platform === 'common' || platform === this.platform
      })
      // 总是隐藏的组件
      packages = packages.filter(item => {
        return !item.hidden && !alawysHideComs.includes(item.type)
      })

      // 移动端不显示的组件
      if (this.screenInfo.type === 'mobile') {
        packages = packages.filter(item => {
          return !mobileHideComs.includes(item.type)
        })
      }

      // 如果是动态面板或类似组件 则面板类组件不可用
      if (this.screenInfo.isDynamicScreen) {
        packages = packages.filter(item => {
          return !isPanel(item.type)
        })
      }
      // 如果是子屏 则引用面板组件不可用
      if (this.screenInfo.screenType === 'child') {
        packages = packages.filter(item => {
          return item.type !== 'interaction-container-referpanel'
        })
      }
      return packages
    },
    searchList () {
      if (!this.searchCom) return [];
      return this.filterCompPackges.filter(item => {
        this.tagsSerch = false;
        this.searchState = false;
        return PinyinMatch.match(item.alias, this.searchCom)
      })
    },
    platform () {
      return this.screenInfo.type
    }
  },
  methods: {
    async labelSerch (item, index, i) {
      // debugger
      if (!item.checked) {
        item.checked = true
        this.selarr.push(index);
        this.tagsList.push(item.id);
      } else {
        item.checked = false
        // this.selarr.splice(this.selarr.indexOf(index + 1), 1);
        // this.tagsList.splice(this.selarr.indexOf(index), 1);
        this.tagsList = this.tagsList.filter(function (it) { return it !== item.id });
      }
      const params = {
        tags: this.tagsList
      }
      const res = await serchList(params)
      if (res.code === 200 && res.success) {
        this.tagsSerchList = res.data
      }
      this.tagsSerchResult = this.filterCompPackges.filter(item => {
        this.searchState = false;
        this.tagsSerch = true;
        for (let i = 0; i < this.tagsSerchList.length; i++) {
          if (item.name === this.tagsSerchList[i].compName) {
            return item
          }
        }
      })
      // 单选按钮方法（先留着万一有用呢）
      // this.labelList[i].tags[index].checked = true
      // this.labelList.forEach(item => {
      //   item.tags.forEach(val => {
      //     if ( item === this.labelList[i] && val !== this.labelList[i].tags[index] ) {
      //       val.checked = false
      //     }
      //   })
      // })
    },
    async clearLable () {
      this.labelList.forEach(item => {
        item.tags.forEach(it => {
          it.checked = false
        })
      })
      this.tagsList = []
      const params = {
        tags: this.tagsList
      }
      const res = await serchList(params)
      if (res.code === 200 && res.success) {
        this.tagsSerchList = res.data
      }
      this.tagsSerchResult = this.filterCompPackges.filter(item => {
        this.searchState = false;
        this.tagsSerch = true;
        for (let i = 0; i < this.tagsSerchList.length; i++) {
          if (item.name === this.tagsSerchList[i].compName) {
            return item
          }
        }
      })
    },
    async openLabel () {
      this.labelShow = true
      const res = await getlabelList()
      if (res.code === 200) {
        const resData = res.data
        resData.forEach(item => {
          item.tags.forEach(val => {
            val.checked = false
          })
        })
        this.labelList = resData
        // this.labelList = res.data;
      }
    },
    createImageComp (pos) {
      const comp = this.findComByName('media-image-normal');
      if (!comp) return false;
      this.handleItemClick(comp, pos);
    },
    handleItemClick (comp, pos) {
      const w = comp.width;
      const h = comp.height;
      const { width, height } = this.screenInfo.config;
      const x = Math.max((width - w) / 2, 0);
      const y = Math.max((height - h) / 2, 0);
      const data = {
        id: uuid(comp.name),
        name: comp.name,
        version: comp.version,
        attr: {
          w,
          h,
          x,
          y
        }
      };
      if (pos) { // 有确定位置，目前从粘贴剪切板内容的方法处调用
        data.attr.x = pos.x;
        data.attr.y = pos.y;
      }
      if (this.platform === 'mobile') { // 移动端删除attr 创建时重新赋值
        delete data.attr;
      }
      canvasBus.emit('create_com', data);
    },
    findComByName (name) {
      let allComps = [];
      Object.values(this.list).forEach(arr => {
        arr.forEach(item => {
          item && item.child && (allComps = allComps.concat(item.child));
        })
      })
      return allComps.find(com => com.name === name) || null;
    },
    handleCompPackges () {
      this.list = this.isIndicator ? indicatorCompList : compPanelList;
      Object.values(this.list).forEach(listItem => {
        listItem.forEach(item => {
          item.child = []
        })
      })
      this.filterCompPackges.forEach(com => {
        if (!this.list[com.type.split('-')[0]]) {
          // console.log(com.type + '组件书写错误');
          this.list.other[0].child.push(com);
        } else {
          this.list[com.type.split('-')[0]][0].child.push(com);
        }
      })
      Object.values(this.list).forEach(listItem => {
        const allChild = listItem[0].child;
        listItem.slice(1).length && listItem.slice(1).forEach(item => {
          allChild.forEach(childItem => {
            if (childItem.type.split('-').slice(0, 2).join('-') === item.type) {
              item.child.push(childItem);
            }
          })
        })
      })
    },
    SelectMenu (key, keyPath) {
      this.activeIndex = key;
    },
    nodeClick (item) {
      item.open = !item.open;
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/style/mixins';
.components-panel {
  position: relative;
  width: 233px;
  transition: .3s ease;
  height: 100%;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  user-select: none;
  .panel-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    background: var(--seatom-panel-title-bg);
    color: #bcc9d4;
    padding: 10px;
    user-select: none;
    .panel-text {
      .refresh-btn {
        margin-left: 5px;
        cursor: pointer;
        transition: .2s;
      }
    }
    .close-btn {
      cursor: pointer;
      transition: color .2s;
    }
  }
  .components-panel-wrapper {
    display: flex;
    height: calc(100% - 68px);
    position: absolute;
    margin-top: 40px;
    width: 100%;
    .wrapper-left-nav {
      overflow: auto;
      width: 56px;
      .el-menu {
        border-right: none;
        background-color: transparent;
      }
      .el-menu-item:focus, .el-menu-item:hover {
        outline: 0;
        background-color: transparent;
      }
      .el-menu-item.is-active {
        color: #2681FF;
        background-color: #14161a;
        &::after {
          content: '';
          display: block;
          width: 4px;
          height: 60px;
          background: radial-gradient(3478.12% 66.25% at 50% 50%, #3D85FF 0%, rgba(61, 133, 255, 0) 100%);
          position: absolute;
          left: 0;
        }
      }
      ::v-deep .el-menu--collapse {
        width: 48px;
      }
      ::v-deep .el-menu-item {
        width: 56px;
        height: 60px;
      }
      ::v-deep .el-tooltip {
        display: flex !important;
        justify-content: center;
        align-items: center;
        padding: 0 !important;
        top: -6px !important;
      }
    }
    .wrapper-left-nav::-webkit-scrollbar {
      display: block;
      width: 4px;
    }

    .wrapper-left-nav::-webkit-scrollbar-thumb{
      background: #434b55;
      border: 1px solid #434b55;
    }
  }
  .wrapper-right-tree {

    width: calc(100% - 48px);
    background-color: #13161a;
    overflow: auto;

    .list-title {
      height: 40px;
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 14px;
      padding-left: 5px;
      padding-right: 16px;
      color: #bcc9d4;
      box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.25);
      > .icon {
        transition: transform .25s linear;
        &.active {
          transform: rotate(90deg);
        }
      }
      .hz-icon {
        font-size: 20px;
        margin: 0 6px;
      }
    }
  }
  .wrapper-right-tree::-webkit-scrollbar {
    display: block;
    width: 4px;
  }

  .wrapper-right-tree::-webkit-scrollbar-thumb{
    background: #434b55;
    border: 1px solid #434b55;
  }

  ::v-deep .el-input__inner {
    background-color: rgba(29,38,46,.54);
    color: #fff;
    padding: 0 6px;
    border: 1px solid #2681ff;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}
.component-panel-wp {
  flex: none;
  position: relative;
  width: 233px;
  height: calc(100% - 58px);
  z-index: 4;
  background: var(--seatom-panel-color);
  transition: width .25s ease-in-out;
  overflow: hidden;
  box-shadow: 1px 0 #000;
  &.--hide {
    width: 0;
  }
  .search-result {
    padding: 10px;
    height: 100%;
    &-title {
      line-height: 16px;
      color: #2681FF;
      border-bottom: 1px solid rgba(255,255,255,0.15);
      padding: 5px;
      display: flex;
      justify-content: space-between;
      .close-btn {
        cursor: pointer;
        width: 16px;
        height: 16px;
        position: relative;
        transition: .3s ease;
        i {
          color: #2681FF;
        }
        &:hover {
          transform: rotateZ(90deg)
        }
      }
    }
    &-body {
      overflow: auto;
      height: calc(100% - 27px);
    }
  }
}
.components-panel-search {
  display: flex;
  position: absolute;
  bottom: 0;
  width: 100%;
}

.empty-data {
  height: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #a1aeb3;
}
.hz-icon {
  color: white;
  font-size: 18px;
}
::v-deep .is-active .hz-icon {
  color: #2681FF;
}
.menu-name {
  line-height: 20px;
  position: absolute;
  bottom: 0px;
  font-size: 12px;
  color: #FFF;
}
::v-deep .is-active .menu-name {
  color: #2681FF;
}
.labelValueTitle {
    margin: 6px 10px 3px 13px;
    // width: 42px;
    height: 22px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(255, 255, 255, 0.5);
  }
  .labelValue {
    // width: 32px;
    margin: 10px;
    height: 20px;
    background: rgba(204, 219, 255, 0.1);
    border-radius: 4px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #FFFFFF;
    cursor: pointer;
  }
  .labelPage {
    position: fixed;
    left: 235px;
    bottom: 0px;
    // height: 334px;
    background: #202737;
    border-radius: 8px;
    box-shadow: 0px 8px 24px -6px rgba(4, 8, 16, 0.56), 0px 1px 3px rgba(4, 8, 16, 0.3), 0px 0px 1px rgba(4, 8, 16, 0.32);
  }
  .highLight {
    background: #3D85FF;
  }
  .clearSeries {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    .clearButton {
      cursor: pointer;
      display: inherit;
      margin-right: 30px;
      margin-top: 10px;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 600;
      font-size: 12px;
      // line-height: 12px;
      color: rgba(255,255,255,0.7);
    }
    .deletButton {
      cursor: pointer;
      margin-right: 10px;
      margin-top: 10px;
    }
  }

</style>
