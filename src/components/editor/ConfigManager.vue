<template>
  <div class="config-manager">
    <el-tabs class="config-tab" :class="{ 'indicator-edit': isIndicatorEdit }" v-model="activeTab" :key="currentConfigId">
      <BasicAttributes
        v-if="activeTab === '1' && !isShowChidCompConfig"
        @toggle="togleShow"
        @saveAsTheme="saveAsTheme"
      />
      <ThemeStyle
        v-if="activeTab === '1' && !isShowChidCompConfig && showStyle"
        :show.sync="showStyle"
      />
      <SaveAsTheme
        v-if="activeTab === '1' && !isShowChidCompConfig"
        ref="theme"
      />
      <div
        class="child-plane-style"
        :class="{ '--hide': !(!isShowChidComp && isShowChidCompConfig) }"
      >
        <div class="config-control chid-comp-attributes">
          <div class="title">
            <span class="name-max-width" :title="chidCompInfo.alias">
              <i class="el-icon-arrow-left" @click="backChidCompClick()" />
              {{ chidCompInfo.alias }}
            </span>
            <span
              @click="updateComp"
              v-if="compUpdate !== currentCom.version"
              class="update"
              >更新版本</span
            >
          </div>
          <div class="desc">
            <span class="name-max-width" :title="chidCompInfo.orginName">{{
              chidCompInfo.orginName
            }}</span>
            <span v-html="nbsp"></span> |
            <span v-html="nbsp"></span>
            <span>v{{ chidCompInfo.version }}</span>
            <span v-html="nbsp"></span> |
            <span v-html="nbsp"></span>
            <span>{{
              chidCompInfo.packageCreateDate ||
              chidCompInfo.updatedAt | formatDate
            }}</span>
          </div>
        </div>
      </div>
      <ChidCompNode
        v-if="isShowChidComp && !isShowChidCompConfig"
        @chidCompClick="funChidCompClick"
        :chidCompListMenu="chidCompListMenu"
        :isTabsDataStly="activeTab"
      />
      <el-tab-pane label="样式" name="1">
        <PerspectiveAttributes
          v-if="show3DTransform && !isShowChidCompConfig"
        />
        <ConfigTree
          :key="configKey"
          :treeData="comCtlConfigTreeData"
          :configObj="comCtlConfigObj"
          @change="handleConfigTreeChange"
        >
        </ConfigTree>
        <Animation-attributes
          v-if="!isShowChidCompConfig"
        ></Animation-attributes>
        <ReferScreenlist
          v-if="
            !isShowChidCompConfig &&
            currentCom.comName === 'interaction-container-referpanel'
          "
        />
        <PitchConfig
          @editIndicator="$emit('editIndicator', $event)"
          v-if="
            !isShowChidCompConfig &&
            (currentCom.comName === 'interaction-container-loop-pitch' ||
              currentCom.comName === 'interaction-container-roll-pitch' ||
              currentCom.comName === 'interaction-container-list-pitch' ||
              currentCom.comName === 'interaction-container-carousepanel' ||
              currentCom.comName === 'interaction-container-popoverpanel' ||
              currentCom.comName === 'interaction-container-dynamicpanel' ||
              currentCom.comName === 'interaction-container-newdynamicpanel' ||
              currentCom.comName === 'interaction-container-mapShadowPanel' ||
              currentCom.comName === 'interaction-container-fold-panel' ||
              currentCom.comName === 'interaction-container-affixPanel' ||
              currentCom.comName === 'interaction-container-popup' ||
              currentCom.comName === 'interaction-form-group' ||
              currentCom.comName === 'interaction-container-flowlayoutpanel' ||
              currentCom.comName === 'interaction-container-statusdialogpanel') ||
              currentCom.comName === 'interaction-container-indicator'
          "
        />
      </el-tab-pane>
      <el-tab-pane label="数据" name="2" lazy>
        <CompData />
      </el-tab-pane>
      <el-tab-pane label="交互" name="3" lazy v-if="!isIndicatorEdit">
        <CompInteraction />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import BasicAttributes from '@/components/editor/config-controls/BasicAttributes';
import ChidCompNode from '@/components/editor/config-controls/ChildCompNode';
import ThemeStyle from '@/components/editor/config-controls/ThemeStyle';
import SaveAsTheme from '@/components/editor/SaveAsTheme.vue';
import CompData from './data-source/CompData';
import CompInteraction from './interaction/CompInteraction';
import { mapState, mapGetters } from 'vuex';
import AnimationAttributes from '@/components/editor/config-controls/AnimationAttributes';
import PerspectiveAttributes from '@/components/editor/config-controls/PerspectiveAttributes';
import ReferScreenlist from './ReferScreenlist';
import PitchConfig from '@/components/editor/PitchConfig';
import emitter from '@/utils/bus';
import { upgradeComponent } from '@/api/component';

export default {
  name: 'ConfigManager',

  components: {
    BasicAttributes,
    ThemeStyle,
    SaveAsTheme,
    CompData,
    CompInteraction,
    ChidCompNode,
    AnimationAttributes,
    PerspectiveAttributes,
    ReferScreenlist,
    PitchConfig
  },

  data () {
    return {
      nbsp: '&nbsp',
      activeTab: '1',
      showStyle: false,
      chidCompInfo: {},
      configKey: 0,
      isIndicatorEdit: false
    };
  },
  created () {
    emitter.on('jumpToEvent', () => {
      this.$store.commit('editor/updateEditPanelSelect', {
        type: 'config',
        value: true
      });
      this.activeTab = '3';
    });
    emitter.on('updateConfigKey', () => {
      this.configKey++;
    });
    emitter.on('jumpToData', () => {
      // this.$store.commit('editor/updateEditPanelSelect', { type: 'config', value: true });
      this.activeTab = '2';
    });

    if (this.$route.name === 'screen/indicator/edit') { // 指标编辑页去掉交互面板
      this.isIndicatorEdit = true;
    }
  },
  computed: {
    ...mapState({
      jumpToEvent: (state) => state.editor.jumpToEvent,
      currentSelectId: (state) => state.editor.currentSelectId,
      compPackgesMap: (state) => state.editor.compPackgesMap,
      currentChildId: (state) => state.editor.currentChildId
    }),
    ...mapGetters('editor', [
      'comCtlConfigTreeData',
      'comCtlConfigObj',
      'getComDataById',
      'currentCom',
      'currentConfigId'
    ]),
    isShowChidComp () {
      // 是否显示子组件组
      return this.chidCompListMenu.length > 0;
    },
    chidCompListMenu () {
      // 获取所有可添加的子组件列表
      let list = [];
      const allList = this.compPackgesMap[this.currentCom.comName].children;
      if (allList && allList.length > 0) {
        allList.forEach((item) => {
          const tempChildCompList = {
            alias: item.alias,
            comName: item.name,
            id: item.id,
            show: item.show,
            version: item.version,
            updatedAt: item.updatedAt.slice(0, 10)
          };
          list.push(tempChildCompList);
        });
      } else {
        list = [];
      }
      return list;
    },
    isShowChidCompConfig () {
      // 是否显示子组件配置
      return !!this.currentChildId;
    },
    show3DTransform () {
      const blackList = [
        'interaction-container-carousepanel',
        'interaction-container-popoverpanel',
        'interaction-container-mapShadowPanel',
        'interaction-container-statusdialogpanel',
        'interaction-form-group'
      ];
      return !blackList.includes(this.currentCom.comName);
    },
    compUpdate () {
      return this.compPackgesMap[this.currentCom.comName]?.version;
    }
  },
  watch: {
    currentSelectId: {
      handler: function (val) {
        this.$nextTick(() => {
          this.activeTab = '1';
        });
      },
      immediate: true
    }
  },
  methods: {
    handleConfigTreeChange ({ path, value }) {
      this.$store.dispatch('editor/updateScreenCom', {
        id: this.currentCom.id,
        keyValPairs: [{ key: `config.${path}`, value: value }]
      });
    },
    togleShow () {
      this.showStyle = !this.showStyle;
    },
    saveAsTheme () {
      // 保存为主题
      this.$refs.theme.showDialog();
    },
    // 显示子组件配置
    funChidCompClick (data) {
      this.chidCompInfo = data;
      this.$store.commit('editor/updateCurrentChildId', this.chidCompInfo.id);
    },
    // 返回父组件配置
    backChidCompClick () {
      this.$store.commit('editor/updateCurrentChildId', null);
    },

    updateComp () {
      const text =
        '更新组件有可能导致组件不可用，如果不确定请咨询客服，是否确定更新？';
      this.$confirm(text, '', {
        confirmButtonText: '确定(Enter)',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async () => {
          // 点击确认的逻辑
          const res = await upgradeComponent([
            { version: this.compUpdate, id: this.currentCom.id }
          ]);
          if (res && res.success && res.data) {
            this.chidCompInfo = res.data[0];
            // console.log('res.data', res.data);
            this.$store.commit('editor/updateScreenComs', res.data);
          }
        })
        .catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
.config-manager {
  width: 100%;
  height: 100%;
  // background: var(--seatom-gui-bg);
  background-color: #13161A;
  transition: 0.25s ease-in-out;
  user-select: none;
  .config-tab {
    &.indicator-edit {
      ::v-deep > .el-tabs__header
      .el-tabs__item {
        width: 50% !important;
      }
    }
    ::v-deep {
      > .el-tabs__header {
        margin: 0 0 2px;
        .el-tabs__nav {
          width: 100%;
        }
        .el-tabs__active-bar {
          bottom: unset;
          // background-color: var(--seatom-sub-main-color);
          height: 1px;
          background-color: #1F71FF;
        }
        .el-tabs__item {
          width: 33.33%;
          height: 33px;
          line-height: 33px;
          text-align: center;
          // color: #bfbfbf;
          color: #ffffff;
          font-weight: 600;
          font-size: 12px;
          padding: 0;
          background: rgba(255, 255, 255, 0.05);
          &.is-active {
            color: #1F71FF;
            // color: var(--seatom-sub-main-color);
            background-color: #191C21;
          }
        }
        .el-tabs__nav-wrap::after {
          display: none;
        }
      }
      > .el-tabs__content {
        height: calc(100vh - 105px);
        overflow-x: hidden;
        overflow-y: auto;
      }
    }
  }
  ::v-deep .child-plane-style {
    position: relative;
    display: block;
    left: 0;
    transition: all 0.25s ease-in-out;
    &.--hide {
      display: none;
      left: 332px;
    }
    .chid-comp-attributes {
      display: inline-block;
      padding-top: 0;
      .name-max-width {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .title {
        line-height: 30px;
        display: flex;
        justify-items: center;
        font-size: 14px;
        justify-content: space-between;
        i {
          margin-right: 4px;
          cursor: pointer;
        }
        .name-max-width {
          max-width: 256px;
        }
        .update {
          font-size: 12px;
          cursor: pointer;
        }
      }
      .desc {
        display: flex;
        padding-bottom: 6px;
        color: #999;
        .name-max-width {
          max-width: 128px;
        }
      }
    }
  }
}
</style>
