<template>
  <el-dialog class="publish-screen" :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'" :visible.sync="show" title="发布" width="435px" top="0" :close-on-click-modal="false" :before-close="close">
    <div class="pub-content" v-loading="loading2" v-if="!showProgress" element-loading-background="#303640" element-loading-text="加载中">
      <div class="render-type flexible-item" v-if="renderType !== 'none' && isPublic">
        <!-- <div class="label">渲染方式</div>
        <el-radio-group v-model="renderType" size="mini" @input="renderTypeChange">
          <el-radio label="static">前端</el-radio>
          <el-radio label="server">服务端
            <el-tooltip class="item" content="服务端渲染首屏加载更快" placement="bottom-end">
              <i class="el-icon-question pointer"></i>
            </el-tooltip>
          </el-radio>
        </el-radio-group> -->
      </div>
      <div class="pub-status">
        <template v-if="isPublic === false">
          <i class="dot"></i>
          <span>未发布</span>
        </template>
        <template v-else>
          <i class="dot active"></i>
          <span>已发布</span>
          <el-switch v-model="active"></el-switch>
          <button class="el-button el-button--primary el-button--mini fr" @click="updatePublish">发布更新</button>
        </template>
      </div>
      <div class="pub-btn" v-if="isPublic === false">
        <el-button class="btn" type="primary" size="medium" @click="publish" :loading="loading">发布大屏</el-button>
        <div class="pub-tip">发布后获得大屏链接</div>
      </div>
      <div class="link" v-else>
        <el-form size="mini" label-width="70px" label-position="left">
          <el-form-item label="使用方式">
            <el-radio-group v-model="activeTab" size="mini" @input="activeTabChange">
              <el-radio label="1">使用链接</el-radio>
              <el-radio label="2">嵌入第三方
                <!-- <el-tooltip class="item" content="服务端渲染首屏加载更快" placement="bottom-end">
                  <i class="el-icon-question pointer"></i>
                </el-tooltip> -->
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-show="activeTab == '1'">
            <template v-if="!childScreens.length">
              <el-form-item label="" label-width="170px">
                <div slot="label"><div class="span-label">后端渲染链接</div><div class="span-icon">推荐</div></div>
              </el-form-item>
              <el-form-item label="" label-width="0px">
                <el-input
                  v-model="afterRender"
                  placeholder="大屏链接地址"
                  size="mini"
                  class="pointer input-theme"
                  readonly
                  v-clipboard:copy="afterRender"
                  v-clipboard:success="onCopy">
                  <i slot="suffix" class="el-input__icon el-icon-document-copy"></i>
                </el-input>
              </el-form-item>
              <el-form-item label="前端渲染链接" label-width="100px">
              </el-form-item>
              <el-form-item label="" label-width="0px">
                <el-input
                  v-model="frontRender"
                  placeholder="大屏链接地址"
                  size="mini"
                  class="pointer input-theme"
                  readonly
                  v-clipboard:copy="frontRender"
                  v-clipboard:success="onCopy">
                  <i slot="suffix" class="el-input__icon el-icon-document-copy"></i>
                </el-input>
              </el-form-item>
            </template>
            <el-tabs type="card" v-model="addressName" class="config-tab" v-else>
              <el-tab-pane label="父屏" name="父屏">
                <el-form-item label="" label-width="170px">
                  <div slot="label"><div class="span-label">后端渲染链接</div><div class="span-icon">推荐</div></div>
                </el-form-item>
                <el-form-item label="" label-width="0px">
                  <el-input
                    v-model="afterRender"
                    placeholder="大屏链接地址"
                    size="mini"
                    class="pointer input-theme"
                    readonly
                    v-clipboard:copy="afterRender"
                    v-clipboard:success="onCopy">
                    <i slot="suffix" class="el-input__icon el-icon-document-copy"></i>
                  </el-input>
                </el-form-item>
                <el-form-item label="前端渲染链接" label-width="100px">
                </el-form-item>
                <el-form-item label="" label-width="0px">
                  <el-input
                    v-model="frontRender"
                    placeholder="大屏链接地址"
                    size="mini"
                    class="pointer input-theme"
                    readonly
                    v-clipboard:copy="frontRender"
                    v-clipboard:success="onCopy">
                    <i slot="suffix" class="el-input__icon el-icon-document-copy"></i>
                  </el-input>
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane :label="panel.name" :name="panel.name" v-for="panel in addressList" :key="panel.name">
                <el-form-item label="" label-width="170px">
                  <div slot="label"><div class="span-label">后端渲染链接</div><div class="span-icon">推荐</div></div>
                </el-form-item>
                <el-form-item label="" label-width="0px">
                  <el-input
                    v-model="panel.afterRender"
                    placeholder="大屏链接地址"
                    size="mini"
                    class="pointer input-theme"
                    readonly
                    v-clipboard:copy="panel.afterRender"
                    v-clipboard:success="onCopy">
                    <i slot="suffix" class="el-input__icon el-icon-document-copy"></i>
                  </el-input>
                </el-form-item>
                <el-form-item label="前端渲染链接" label-width="100px">
                </el-form-item>
                <el-form-item label="" label-width="0px">
                  <el-input
                    v-model="panel.frontRender"
                    placeholder="大屏链接地址"
                    size="mini"
                    class="pointer input-theme"
                    readonly
                    v-clipboard:copy="panel.frontRender"
                    v-clipboard:success="onCopy">
                    <i slot="suffix" class="el-input__icon el-icon-document-copy"></i>
                  </el-input>
                </el-form-item>
              </el-tab-pane>
            </el-tabs>
            <el-form-item label="预览动画" class="flexible-item">
              <el-switch v-model="needLoading" @change="loadingChange"></el-switch>
            </el-form-item>
            <el-form-item label-width="100px" label="开启数据缓存" class="flexible-item">
              <el-switch v-model="enableCache" @change="cacheChange"></el-switch>
            </el-form-item>
            <el-form-item label="开启第三方用户体系接入" class="no-width flexible-item">
              <el-switch v-model="needAuth" @change="needAuthChange"></el-switch>
            </el-form-item>
            <el-form-item label="开启密码" class="flexible-item">
              <el-switch v-model="needPassword" @change="switchChange"></el-switch>
            </el-form-item>
            <el-form-item label="密码" v-if="needPassword">
              <el-input class="input-theme" v-model="sharePassword" @blur="passwordChange" placeholder="设置访问密码"></el-input>
              <!-- <el-input class="input-theme" v-else readonly placeholder="获取访问密码"></el-input> -->
            </el-form-item>
          </div>
          <div v-show="activeTab == '2'">
            <div class="tips">后端渲染代码：</div>
            <el-input
              class="code-input input-theme"
              type="textarea"
              :rows="6"
              readonly
              v-model="aftercodeVal">
            </el-input>
            <div class="tips text-right">方便快捷，灵活嵌入，支持自由扩展</div>
            <div class="copy-btn">
              <el-button type="primary" size="mini" v-clipboard:copy="aftercodeVal"
              v-clipboard:success="onCopy">复制</el-button>
            </div>
            <div class="tips">前端渲染代码：</div>
            <el-input
              class="code-input input-theme"
              type="textarea"
              :rows="6"
              readonly
              v-model="codeVal">
            </el-input>
            <div class="tips text-right">方便快捷，灵活嵌入，支持自由扩展</div>
            <div class="copy-btn">
              <el-button type="primary" size="mini" v-clipboard:copy="codeVal"
              v-clipboard:success="onCopy">复制</el-button>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <el-progress :percentage="percentage" v-else></el-progress>
  </el-dialog>
</template>

<script>
import { getScreenShare, updateScreenShare, updateCache } from '@/api/screen';
import { mapState } from 'vuex';
export default {
  name: 'PublishScreen',
  components: {},
  props: [],
  data () {
    return {
      show: false,
      showProgress: false,
      screenId: '',
      isPublic: false,
      needPassword: false,
      needAuth: false,
      needLoading: true,
      enableCache: false,
      sharePassword: '',
      loading: false,
      loading2: false,
      shareUrl: '',
      activeTab: '1',
      percentage: 0,
      renderType: 'static',
      frontRender: '',
      afterRender: '',
      previewPath: process.env.BASE_URL + 'preview/index.html?_t=' + Date.now() + Math.random().toFixed(3) * 1000 + '#',
      addressName: '父屏'
    }
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo,
      childScreens: state => state.editor.childScreens
    }),
    addressList () {
      const children = _.cloneDeep(this.childScreens)
      return children.map(item => {
        item.shareUrl = ''
        item.frontRender = ''
        item.afterRender = ''
        return item
      })
    },
    active: {
      get: function () {
        return this.isPublic
      },
      set: function (val) {
        this.$confirm('下一次再打开分享时，分享链接会发生变化。', {
          title: '提示',
          type: 'warning',
          cancelButtonClass: 'poper-cancel',
          iconClass: 'message-warning',
          customClass: 'poper-theme'
        }).then(async () => {
          this.updateShareInfo({ isPublic: false })
          this.isPublic = false;
        })
      }
    },
    codeVal () {
      return `<iframe frameborder="0" width="${this.screenInfo.config.width}" height="${this.screenInfo.config.height}" src="${this.frontRender}"></iframe>`
    },
    aftercodeVal () {
      return `<iframe frameborder="0" width="${this.screenInfo.config.width}" height="${this.screenInfo.config.height}" src="${this.afterRender}"></iframe>`
    }
  },
  watch: {
    renderType (val) {
      if (val === 'server') {
        this.previewPath = '/ssr'
      } else {
        this.previewPath = process.env.BASE_URL + 'preview/index.html?_t=' + Date.now() + Math.random().toFixed(3) * 1000 + '#'
      }
    }
  },
  methods: {
    showPublish (screenId) {
      this.screenId = screenId || this.$route.params.screenId;
      this.show = true;
      this.getShareInfo();
      if (this.childScreens.length) {
        this.childScreens.forEach(screen => {
          this.getShareInfo(screen.id)
        })
      }
    },
    updatePublish () {
      this.showProgress = true;
      const timer = setInterval(() => {
        if (this.percentage < 99) {
          this.percentage += 1;
        } else {
          clearInterval(timer);
        }
      }, 100);
      const pList = []
      pList.push(updateCache({ screenId: this.screenId }))
      if (this.childScreens.length) {
        this.childScreens.forEach(item => {
          pList.push(updateCache({ screenId: item.id }))
        })
      }
      return Promise.all(pList).then(res => {
        const success = res.every(item => item.success && item.code === 200)
        if (success) {
          this.percentage = 100;
          clearInterval(timer);
          this.$message.success('发布成功！');
          this.showProgress = false;
          this.percentage = 0;
          this.timeout && clearTimeout(this.timeout);
          this.handlePublishSuccess()
        } else {
          this.handlePublishError(timer);
        }
      }).catch(err => {
        console.error(err);
        this.handlePublishError(timer);
      });
    },
    handlePublishError (timer) {
      this.$message.error('发布失败！');
      this.showProgress = false;
      this.percentage = 0;
      // this.ghostIframe && document.body.removeChild(this.ghostIframe);
      // this.ghostIframe = null;
      timer && clearInterval(timer);
    },
    handlePublishSuccess () {
      this.$emit('success', {
        isPublic: this.isPublic,
        shareUrl: this.shareUrl
      })
    },
    // handleCompLoaded () {
    //   window.removeEventListener('message', this.handleCompLoaded);
    //   this.percentage = 100;
    //   this.timeout && clearTimeout(this.timeout);
    //   const timer = setTimeout(() => {
    //     this.showProgress = false;
    //     this.percentage = 0;
    //     // this.ghostIframe && document.body.removeChild(this.ghostIframe); //成功之后不再删除iframe，防止请求被取消
    //     this.ghostIframe = null;
    //     clearTimeout(timer);
    //     this.$message.success('发布成功！');
    //   }, 1000);
    // },
    close () {
      this.show = false;
      this.$nextTick(() => {
        this.isPublic = false;
        this.showProgress = false;
        this.percentage = 0;
        this.renderType = 'none';
        // this.ghostIframe && document.body.removeChild(this.ghostIframe);
        // this.ghostIframe = null;
      })
      this.$emit('close', { screenId: this.screenId, isPublic: this.isPublic });
    },
    async getShareInfo (screenId) {
      const data = {
        screenId: screenId || this.screenId
      }
      try {
        this.loading2 = true;
        const res = await getScreenShare(data);

        if (res && res.success) {
          const result = res.data;
          const userId = localStorage.getItem('userId');
          if (result) {
            if (screenId) { // 子屏
              const screen = this.addressList.find(addr => addr.id === screenId)
              if (screen) {
                screen.shareUrl = `${window.location.origin}${this.previewPath}${result.shareUrl}?loading=false&userId=${userId}`;
                screen.afterRender = `${window.location.origin}${'/ssr'}${result.shareUrl}?loading=false&userId=${userId}`;
                screen.frontRender = `${window.location.origin}${process.env.BASE_URL + 'preview/index.html?_t=' + Date.now() + Math.random().toFixed(3) * 1000 + '#'}${result.shareUrl}?loading=false&userId=${userId}`;
              }
            } else {
              this.renderType = result.renderType ? result.renderType : 'none';
              this.isPublic = result.isPublic;
              this.needLoading = result.needLoading;
              this.needAuth = result.needAuth;
              this.enableCache = result.enableCache;
              this.previewPath = result.renderType === 'server' ? '/ssr' : process.env.BASE_URL + 'preview/index.html?_t=' + Date.now() + Math.random().toFixed(3) * 1000 + '#';
              this.shareUrl = `${window.location.origin}${this.previewPath}${result.shareUrl}?loading=${this.needLoading}&userId=${userId}`;
              this.afterRender = `${window.location.origin}${'/ssr'}${result.shareUrl}?loading=${this.needLoading}&userId=${userId}`;
              this.frontRender = `${window.location.origin}${process.env.BASE_URL + 'preview/index.html?_t=' + Date.now() + Math.random().toFixed(3) * 1000 + '#'}${result.shareUrl}?loading=${this.needLoading}&userId=${userId}`;
              this.needPassword = result.needPassword;
              this.sharePassword = result.sharePassword;
            }
          }
        }
        this.loading2 = false;
      } catch (e) {
        this.loading2 = false;
      }
    },
    async updateShareInfo (info = {}, isChild = false) {
      const data = {
        screenId: this.screenId,
        isPublic: this.isPublic,
        needPassword: this.needPassword,
        needLoading: this.needLoading,
        ...info
      }
      if (this.password) {
        data.password = this.password;
      }
      try {
        this.loading = true;
        const res = await updateScreenShare(data);
        if (res && res.success) {
          const result = res.data;
          if (result) {
            const userId = localStorage.getItem('userId');
            if (isChild) {
              const screen = this.addressList.find(addr => addr.id === data.screenId)
              if (screen) {
                screen.shareUrl = `${window.location.origin}${this.previewPath}${result.shareUrl}?loading=false&userId=${userId}`;
                screen.afterRender = `${window.location.origin}${'/ssr'}${result.shareUrl}?loading=false&userId=${userId}`;
                screen.frontRender = `${window.location.origin}${process.env.BASE_URL + 'preview/index.html?_t=' + Date.now() + Math.random().toFixed(3) * 1000 + '#'}${result.shareUrl}?loading=false&userId=${userId}`;
              }
            } else {
              this.renderType = result.renderType ? result.renderType : 'none';
              this.sharePassword = result.sharePassword;
              this.$nextTick(() => {
                this.shareUrl = `${window.location.origin}${this.previewPath}${result.shareUrl}?loading=${this.needLoading}&userId=${userId}`;
                this.afterRender = `${window.location.origin}${'/ssr'}${result.shareUrl}?loading=${this.needLoading}&userId=${userId}`;
                this.frontRender = `${window.location.origin}${process.env.BASE_URL + 'preview/index.html?_t=' + Date.now() + Math.random().toFixed(3) * 1000 + '#'}${result.shareUrl}?loading=${this.needLoading}&userId=${userId}`;
                if (!data.isPublic) {
                  this.handlePublishSuccess()
                }
              })
            }
          }
        }
        this.loading = false;
      } catch (e) {
        this.loading = false;
      }
    },
    async publish () {
      await this.updateShareInfo({ isPublic: true });
      if (this.childScreens.length) {
        for await (const screen of this.childScreens) {
          await this.updateShareInfo({ isPublic: true, needPassword: false, needLoading: false, screenId: screen.id }, true);
        }
      }
      this.updatePublish();
      this.isPublic = true;
    },
    activeTabChange (val) {
      this.activeTab = val
    },
    switchChange (val) {
      this.updateShareInfo({ needPassword: val });
    },
    passwordChange () {
      this.updateShareInfo({ sharePassword: this.sharePassword });
    },
    loadingChange (val) {
      this.updateShareInfo({ needLoading: val });
    },
    needAuthChange (val) {
      this.updateShareInfo({ needAuth: val });
    },
    cacheChange (val) {
      this.updateShareInfo({ enableCache: val });
    },
    onCopy () {
      this.$message.destroyAll();
      this.$message.success('已复制到粘贴板');
    }
  }
}
</script>

<style lang="scss" scoped>
.span-label{
  display: inline-block;
  height: 20px;
  line-height: 20px;
  margin-right: 2px;
}
.span-icon{
  display: inline-block;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  width: 34px;
  padding-left: 7px;
  color: #ffffff;
  background-image:  url('../../assets/img/tuijian.png');
  background-size:100% 100%;
  background-repeat: no-repeat;
}
.publish-screen {
  .pub-content {
    .render-type {
      color: var(--seatom-type-700);
      font-weight: 600;
      padding: 8px 20px;
      line-height: 30px;
    }
    .pub-status {
      padding: 8px 20px;
      line-height: 30px;
      .dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #737c80;
        margin-right: 8px;
        &.active {
          background: #2681ff;
        }
      }
      span {
        margin-right: 8px;
      }
    }
    .pub-btn {
      padding: 10px 20px 20px;
      .btn {
        width: 100%;
      }
      .pub-tip {
        font-size: 14px;
        text-align: center;
        margin-top: 10px;
      }
    }
    .link {
      padding: 10px 20px 20px;
      .repub {
        margin-top: 15px;
        text-align: center;
      }
    }
  }
  .config-tab ::v-deep {
    > .el-tabs__header {
      margin: 0;
      border: none;
      .el-tabs__nav {
        border: none;
        background: none;
      }
      .el-tabs__active-bar {
        bottom: unset;
        // background-color: var(--seatom-sub-main-color);
        height: 1px;
        background-color: #1F71FF;
      }
      .el-tabs__item {
        height: 28px;
        line-height: 16px;
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 600;
        font-size: 12px;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid rgba(204, 219, 255, 0.32);
        padding: 6px 12px;
        background: #1f2430;
        &.is-active {
          color: #ffffff;
          background-color: #3D85FF;
          border-color: #3D85FF;
        }
        &:not(:last-child) {
          margin-right: 3px;
        }
      }
      .el-tabs__nav-wrap::after {
        display: none;
      }
    }
    > .el-tabs__content {
      padding: 16px 16px 0;
      margin-bottom: 18px;
      box-shadow: 0 0 0 0.3333px rgba(204, 219, 255, 0.32) inset;
    }
  }
  .flexible-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  ::v-deep {
    .el-input__inner, .el-input__suffix {
      cursor: pointer;
    }

    .code-input {
      .el-textarea__inner {
        border-radius: 0;
        background-color: #000;
      }
    }

    .el-form-item::after, .el-form-item::before {
      display: none;
    }
  }
  .tab-b ::v-deep {
    > .el-tabs__header {
      .el-tabs__nav {
        width: 100%;
      }
      .el-tabs__active-bar {
        display: none;
      }
      .el-tabs__item {
        width: 50%;
        text-align: center;
        color: #bfbfbf;
        padding: 0;
        background: #2d2f38;
        &.is-active {
          color: var(--seatom-sub-main-color);
          border: 1px solid var(--seatom-sub-main-color);
        }
      }
      .el-tabs__nav-wrap::after {
        display: none;
      }
    }
  }
  .tips {
    font-size: 12px;
    color: #999;
    padding: 5px 0;
  }
  .text-right {
    text-align: right;
  }
  .copy-btn {
    text-align: center;
    padding-top: 10px;
    button {
      width: 100px;
    }
  }
  .no-width {
    ::v-deep {
      label {
        width: unset !important;
      }
    }
  }
}
</style>
