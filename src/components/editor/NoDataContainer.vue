<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-11-17 17:51:39
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-22 18:26:33
 * @FilePath: /seatom/src/components/editor/NoDataContainer.vue
 * @Description: 没有内容占位
-->
<template>
  <div class="no-data-container">
    <div class="no-data-image"></div>
  </div>
</template>

<script>
export default {
  name: 'CircleAvatar',
  mounted () {

  },
  data () {
    return {

    }
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.no-data-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .no-data-image {
        width: 776px;
        height: 480px;
        background: var(--seatom-noData-bg) no-repeat top / cover;
    }
}

</style>
