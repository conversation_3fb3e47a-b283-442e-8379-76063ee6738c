<template>
  <div class="public-data element-ui-override">
    <el-tabs
      v-model="activeTab"
      :class="{'tab-theme': !$route.name.includes('edit')}">
      <el-tab-pane label="自动更新" name="autoUpdate" lazy>
        <AutoUpdate :allComs="allComs" ref="autoUpdate"/>
      </el-tab-pane>
      <el-tab-pane label="无数据提示" name="noDataPrompt" lazy>
        <NoDataPrompt :allComs="allComs" ref="noDataPrompt"/>
      </el-tab-pane>
      <el-tab-pane label="数据容器" name="dataContainer" lazy>
        <ContainerManage ref="container" />
      </el-tab-pane>
      <el-tab-pane label="全局变量" name="globalVariables" lazy>
        <GlobalVariables />
      </el-tab-pane>
    </el-tabs>
    <div class="auto-update-all-button" v-if="['autoUpdate', 'noDataPrompt'].includes(activeTab)" @click="batchSetting">批量编辑</div>
    <div class="auto-update-all-button" v-if="activeTab === 'dataContainer'" @click="addContainer">新增容器</div>
  </div>
</template>

<script>
import AutoUpdate from './public-manage/AutoUpdate'
import NoDataPrompt from './public-manage/NoDataPrompt'
import ContainerManage from './public-manage/datacontainer/ContainerManage'
import GlobalVariables from './public-manage/global-variable'
import { isPanel } from '@/utils/base'
import { mapState } from 'vuex'
export default {
  name: 'PublicDataManage',
  components: {
    AutoUpdate,
    NoDataPrompt,
    ContainerManage,
    GlobalVariables
  },
  data () {
    return {
      activeTab: 'autoUpdate'
    }
  },
  computed: {
    ...mapState({
      screenComs: state => state.editor.screenComs,
      childScreenComs: state => state.editor.childScreenComs
    }),
    allComs () {
      const panelComs = (Object.values(this.screenComs) || []).filter(item => {
        return isPanel(item.comType) && item.comType !== 'interaction-container-referpanel'
      }).reduce((prev, cur) => {
        const screens = (cur.config.screens || []).reduce((coms, screen) => {
          const curComs = Object.values(this.childScreenComs[screen.id]) || []
          coms.push(...curComs)
          return coms
        }, []).map(com => {
          return {
            pAlias: cur.alias,
            ...com
          }
        })
        prev.push(...screens)
        return prev
      }, [])
      return [...panelComs, ...Object.values(this.screenComs)]
    }
  },
  methods: {
    batchSetting () {
      switch (this.activeTab) {
        case 'autoUpdate':
          this.$refs.autoUpdate.showBatchUpdate();
          break;
        case 'noDataPrompt':
          this.$refs.noDataPrompt.showBatchNoData();
          break;
        default:
          break;
      }
    },
    addContainer () {
      this.$refs.container.addContainer();
    }
  }
}
</script>

<style lang="scss" scoped>
.public-data {
  max-height: 100%;
  // overflow-y: auto;
  padding: 0 24px;
  padding-top: 24px;
  height: 100%;
  position: relative;

  ::v-deep {
    .el-tabs {
      height: calc(100% - 24px);
    }
    .el-tabs__nav-wrap::after {
      background-color: #3c4150;
    }
    .el-tabs__nav-wrap {
      display: flex;
      // justify-content: center;
    }
    .el-tabs__content {
      position: unset;
      height: calc(100% - 40px);
    }
    .el-tab-pane {
      height: 100%;
    }
  }
  .auto-update-all-button {
    width: 80px;
    height: 28px;
    background: #3D85FF;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-weight: 600;
    font-size: 14px;
    position: absolute;
    right: 24px;
    top: 24px;
    cursor: pointer;
  }
}
</style>
