<template>
  <div class="shortcut-tips">
    <div class="shortcut-tips-title">
      <span>快捷键</span>
    </div>
    <div class="shortcut-tips-content">
      <ul>
        <li v-for="item in shortcut" :key="item.name">
          <span>{{ item.name }}</span>
          <span class="key">
            <span class="key-code">{{ item.leftKey }}</span>
            <span v-if="item.rightKey" class="ml-5 mr-5">+</span>
            <span class="key-code" v-if="item.rightKey">{{
              item.rightKey
            }}</span>
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      show: false
    };
  },
  computed: {
    platform () {
      return this.$store.state.editor.screenInfo?.type;
    },
    shortcut () {
      if (this.platform === 'pc') {
        return [
          {
            name: '拷贝',
            leftKey: '⌘ / ctrl',
            rightKey: 'C'
          },
          {
            name: '粘贴',
            leftKey: '⌘ / ctrl',
            rightKey: 'V'
          },
          {
            name: '撤销',
            leftKey: '⌘ / ctrl',
            rightKey: 'Z'
          },
          {
            name: '重做',
            leftKey: '⌘ / ctrl',
            rightKey: 'Y'
          },
          {
            name: '多选',
            leftKey: 'shift',
            rightKey: '单击'
          },
          {
            name: '删除',
            leftKey: 'delete / backspace',
            rightKey: ''
          },
          {
            name: '组件上移1个单位',
            leftKey: '↑',
            rightKey: ''
          },
          {
            name: '组件下移1个单位',
            leftKey: '↓',
            rightKey: ''
          },
          {
            name: '组件左移1个单位',
            leftKey: '←',
            rightKey: ''
          },
          {
            name: '组件右移1个单位',
            leftKey: '↑',
            rightKey: ''
          },
          {
            name: '组件上移10个单位',
            leftKey: '⌘ / ctrl',
            rightKey: '↑'
          },
          {
            name: '组件下移10个单位',
            leftKey: '⌘ / ctrl',
            rightKey: '↓'
          },
          {
            name: '组件左移10个单位',
            leftKey: '⌘ / ctrl',
            rightKey: '←'
          },
          {
            name: '组件右移10个单位',
            leftKey: '⌘ / ctrl',
            rightKey: '→'
          }
        ];
      }
      return [
        {
          name: '拷贝',
          leftKey: '⌘ / ctrl',
          rightKey: 'C'
        },
        {
          name: '粘贴',
          leftKey: '⌘ / ctrl',
          rightKey: 'V'
        },
        {
          name: '删除',
          leftKey: 'delete / backspace',
          rightKey: ''
        }
      ];
    }
  }
};
</script>

<style  lang="scss" scoped>
.shortcut-tips {
  position: absolute;
  bottom: 30px;
  width: 295px;
  left: 0px;
  background-color: #1c1f25;
  box-shadow: 1px 1px 8px -2px #000;
  color: #bcc9d4;
  z-index: 99;
  &-title {
    display: flex;
    justify-content: space-between;
    height: 44px;
    padding: 0 16px;
    font-size: 14px;
    border-bottom: 1px solid #22242b;
    align-items: center;
  }
  &-content {
    max-height: calc(100vh - 144px);
    overflow: auto;
    ul {
      li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;
        height: 36px;
        border-bottom: 1px solid #22242b;

        .key {
          display: flex;
          align-items: center;
        }

        .key-code {
          display: flex;
          justify-content: center;
          align-items: center;
          min-width: 24px;
          height: 24px;
          padding: 0 5px;
          border: 1px solid #393b4a;
          border-radius: 2px;
          box-shadow: 0 1px 3px 0 rgb(44 46 64 / 82%);
        }
      }
    }
  }
  .mr-5 {
    margin-right: 5px;
  }
  .ml-5 {
    margin-left: 5px;
  }
}
</style>
