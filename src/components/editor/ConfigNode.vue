<template>
  <component
    v-if="componentName && showInPanel"
    v-show="showCtl"
    :is="componentName"
    :node="node"
    :configObj="configObj"
  >
  </component>
</template>

<script>
import configControls from './config-controls';
import configMixin from '@/mixins/config';
import { mapGetters, mapState } from 'vuex';
import { uploadScreenIcon } from '@/api/common';
import { replaceUrl } from '@/utils/base'
Object.values(configControls).forEach(ctl => {
  (ctl.mixins || (ctl.mixins = [])).push(configMixin);
});

export default {
  name: 'ConfigNode',

  // TODO: 高阶组件 http://hcysun.me/2018/01/05/%E6%8E%A2%E7%B4%A2Vue%E9%AB%98%E9%98%B6%E7%BB%84%E4%BB%B6/
  mixins: [configMixin],

  components: {
    ...configControls
  },

  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    ...mapGetters('editor', ['currentCom']),
    componentName () {
      const type = this.node && this.node.data.type;
      if (type && typeof type === 'string') {
        return `Ctl${type[0].toUpperCase()}${type.slice(1)}`;
      }
      return '';
    },
    showCtl () {
      // platform: pc: 电脑端 mobile: 移动端 (设置了则仅在对应平台可用)
      // isNecessary：是否精简模式
      // configType：simple：精简 advanced：高级
      return (!this.node.data.platform || this.node.data.platform === this.screenInfo.type) &&
        (
          this.node.data.isNecessary ||
          !this.currentCom ||
          this.currentCom.other.configType === 'advanced'
        )
    }
  },
  created () {
    this.handleIcon();
  },
  methods: {
    async handleIcon () {
      if (this.componentName === 'CtlIcon') {
        if (this.value.url) return
        let resUrl = this.value.defaultImage;
        if (this.value.defaultImage.indexOf('./public') > -1) {
          resUrl = '/public' + this.value.defaultImage.split('./public')[1]
        }
        await this.uploadScreenIcons(resUrl)
      }
    },
    handleChange ({ path, value }) {
      const pathArr = path.split('.');
      const tree = this.node && this.node.tree;
      if (tree) {
        const upperPaths = tree.valuePathMap[this.node.id];
        this.dispatch('ConfigTree', 'change', {
          path: upperPaths.concat(pathArr).join('.'),
          value
        });
      }
    },
    uploadScreenIcons: (function () {
      const resourceUrlList = [];
      const instance = [];
      const time = 30;
      let timeout = null
      return async function (resourceUrl) {
        return new Promise((resolve, reject) => {
          clearTimeout(timeout)
          resourceUrlList.push(resourceUrl)
          instance.push(this);
          timeout = setTimeout(async () => {
            const part = resourceUrlList.splice(0);
            const partInstance = instance.splice(0);
            const params = {
              resourceUrlList: part,
              changeColor: false,
              screenId: this.screenInfo.id
            }
            try {
              const res = await uploadScreenIcon(params)
              if (res && res.success) {
                const { data: { svgId, mergeSvgPath } } = res
                svgId.forEach((item, index) => {
                  const url = replaceUrl(process.env.VUE_APP_SERVER_URL + mergeSvgPath + '#' + item)
                  partInstance[index].node.data.default.url = url
                  partInstance[index].handleChange({ path: 'url', value: url });
                })
                resolve(partInstance)
              }
            } catch (e) {}
          }, time)
        })
      }
    }())
  }
}
</script>

<style>
@import './../../style/configControl.scss';
</style>
