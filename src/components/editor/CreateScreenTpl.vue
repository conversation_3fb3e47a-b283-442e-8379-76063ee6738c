<template>
    <el-dialog
      title="创建大屏模版"
      :visible.sync="dialogVisible"
      width="450px"
      top="0"
      :before-close="closeCreate"
      :close-on-click-modal="false">
      <el-form :model="selectParams" :rules="rules" size="medium" label-width="100px" ref="form">
        <el-form-item label="模版名称：" prop="name" status-icon>
          <el-input v-model="selectParams.name" placeholder="请输入大屏模版名称"></el-input>
        </el-form-item>
        <el-form-item label="标签：">
          <div class="tag-wrap">
            <div class="tag-item" v-for="(item, idx) in selectParams.tag" :key="idx">
              <span class="tag">{{ item }}</span>
              <span class="el-icon-close" @click="delTag(idx)"></span>
            </div>
            <el-input v-if="showInput" v-model="tagVal" v-select class="tag-input" size="mini" @blur="inputBlur()"></el-input>
            <el-button type="primary" size="mini" icon="el-icon-plus" @click="addTag()">添加</el-button>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button class="preview-btn" @click="closeCreate" size="mini">取 消</el-button>
        <el-button class="create-btn" type="primary" @click="createScreen" size="mini" :loading="createScreenLoading">确 定</el-button>
      </span>
    </el-dialog>
</template>
<script>
import { screenCopy } from '@/api/screen';
export default {
  name: 'CreateScreenTpl',
  data () {
    return {
      dialogVisible: false,
      selectProject: [],
      ScreenTpl: [],
      selectParams: {
        id: '',
        projectId: 0,
        workspaceId: 0,
        level: 1,
        name: '',
        tag: []
      },
      rules: {
        name: [
          { required: true, message: '模板名称必填', trigger: 'change' }
        ]
      },
      showInput: false,
      tagVal: '',
      createScreenLoading: false
    }
  },
  directives: {
    select: {
      inserted: function (el) {
        el.querySelector('input').select()
      }
    }
  },
  methods: {
    showTpl (screenInfo) {
      this.dialogVisible = true;
      this.selectParams.type = screenInfo.type
      this.selectParams.templateId = screenInfo.templateId
      this.selectParams.workspaceId = screenInfo.workspaceId
      this.selectParams.id = screenInfo.id
      this.selectParams.isScreentpl = true
    },
    closeCreate () {
      this.$refs.form.resetFields();
      this.selectParams.tag = [];
      this.dialogVisible = false;
    },
    createScreen () {
      this.$refs.form.validate(async valid => {
        if (valid && !this.createScreenLoading) {
          this.createScreenLoading = true;
          const res = await screenCopy(this.selectParams);
          if (res.success) {
            this.dialogVisible = false;
            this.createScreenLoading = false;
            this.$message.success('生成模版成功');
            // this.$router.push({ path: `/screen/edit/${res.data.screenId}` });
          } else {
            this.$message.error(res.message);
          }
        }
      })
    },
    addTag () {
      this.showInput = true;
    },
    delTag (index) {
      this.selectParams.tag.splice(index, 1);
    },
    inputBlur () {
      if (this.tagVal) {
        this.selectParams.tag.push(this.tagVal);
        this.tagVal = '';
      }
      this.showInput = false;
    }
  }

}
</script>
