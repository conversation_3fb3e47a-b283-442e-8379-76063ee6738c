<template>
  <div class="tinymce-editor">
    <Editor
      :id="tinymceId"
      :init="init"
      :disabled="disabled"
      v-model="selfValue"
    />
  </div>
</template>

<script>
import tinymce from 'tinymce/tinymce' // tinymce默认hidden，不引入不显示
import Editor from '@tinymce/tinymce-vue' // 编辑器引入
import 'tinymce/themes/silver/theme' // 编辑器主题
import 'tinymce/icons/default' // 引入编辑器图标icon，不引入则不显示对应图标
// 引入编辑器插件（基本免费插件都在这儿了）
import 'tinymce/plugins/advlist' // 高级列表
import 'tinymce/plugins/autolink' // 自动链接
import 'tinymce/plugins/link' // 超链接
import 'tinymce/plugins/image' // 插入编辑图片
import 'tinymce/plugins/lists' // 列表插件
import 'tinymce/plugins/charmap' // 特殊字符
import 'tinymce/plugins/paste' // 粘贴功能
import 'tinymce/plugins/fullscreen' // 全屏插件
import 'tinymce/plugins/wordcount' // 字数统计
import { tutorialUpload } from '@/api/tutorial'
import { replaceUrl } from '@/utils/base'

const fonts = [
  '宋体=Simsun',
  '思源黑体=思源黑体Medium',
  '新宋体=NSimSun',
  '黑体=SimHei',
  '楷体=KaiTi',
  '隶书=LiSu',
  'Courier New=courier new,courier',
  'AkrutiKndPadmini=Akpdmi-n',
  'Andale Mono=andale mono,times',
  'Arial=arial,helvetica,sans-serif',
  'Arial Black=arial black,avant garde',
  'Book Antiqua=book antiqua,palatino',
  'Comic Sans MS=comic sans ms,sans-serif',
  'Courier New=courier new,courier',
  'Georgia=georgia,palatino',
  'Helvetica=helvetica',
  'Impact=impact,chicago',
  'Symbol=symbol',
  'Tahoma=tahoma,arial,helvetica,sans-serif',
  'Terminal=terminal,monaco',
  'Times New Roman=times new roman,times',
  'Trebuchet MS=trebuchet ms,geneva',
  'Verdana=verdana,geneva',
  'Webdings=webdings',
  'Wingdings=wingdings,zapf dingbats'
]
export default {
  name: 'TEditor',
  components: {
    Editor
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    plugins: {
      type: [String, Array],
      default: 'advlist autolink link image lists charmap  wordcount paste fullscreen'
    },
    toolbar: {
      type: [String, Array],
      default: 'undo redo |  formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists image table paste fullscreen'
    },
    tutorialId: {
      type: String,
      default: 'NEW_TUTORIAL_DRAFT'
    }
  },
  data () {
    return {
      tinymceId: 'tinymce',
      selfValue: this.value,
      init: {
        selector: '#tinymce',
        language_url: '/tinymce/langs/zh_CN.js',
        language: 'zh_CN',
        skin_url: '/tinymce/skins/ui/oxide-dark',
        plugins: this.plugins,
        toolbar: this.toolbar,
        toolbar_location: '/',
        fontsize_formats: '12px 14px 16px 18px 20px 22px 24px 28px 32px 36px 48px 56px 72px',
        font_formats: fonts.join(';'),
        height: 600,
        min_height: 600,
        width: '100%',
        max_width: 1080,
        resize: true,
        placeholder: '在这里输入文字',
        branding: false,
        paste_data_images: true,
        contextmenu: false,
        images_upload_handler: async (blobInfo, success, failure) => {
          const formData = new FormData()
          formData.append('img', blobInfo.blob())
          const res = await tutorialUpload(formData)
          res && res.success ? success(`${replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.url)}`) : failure('上传失败，稍后重试！')
        },
        content_style: 'img{ max-width:864px; display:block;height:auto; }',
        setup: (editor) => {
          // this指向 编辑器用function，vue用箭头函数
          editor.on('init', function () {
            this.getBody().style.fontSize = '14px'
            this.getBody().style.color = '#000000'
            this.getBody().style.fontFamily = '思源黑体Medium'
            this.getBody().style.backgroundColor = '#ffffff'
            this.getBody().style.lineHeight = 1.5
          })
          editor.on('blur', () => {
            const key = this.tutorialId || 'NEW_TUTORIAL_DRAFT'
            localStorage.setItem(key, this.selfValue)
          })
        }
      }
    }
  },
  watch: {
    value (newValue) {
      this.selfValue = (newValue == null ? '' : newValue)
    },
    selfValue (newValue) {
      this.$emit('input', newValue)
    }
  },
  mounted () {
    tinymce.init({})
  }
}
</script>
<style>
.tinymce-editor {
  width: 100%;
}
.tox.tox-tinymce-aux {
  z-index: 9999 !important;
}
</style>
