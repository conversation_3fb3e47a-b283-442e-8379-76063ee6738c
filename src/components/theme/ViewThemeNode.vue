<template>
  <div
    class="view-comp-node animate__animated"
    :class="isShow ? 'animate__fadeIn' : 'animate__fadeOut'"
    :style="nodeStyle"
    v-show="isShow"
   >
    <template v-if="comCfg.comName === 'interaction-container-referpanel'">
      <ReferPanel
        ref="compRef"
        :style="comStyle"
        :id="id"
        :data-id="id"
        :config="comCfg.config"
        :data="comData"
        @mounted="handleCompMounted"
        isView />
    </template>
    <template v-else-if="comCfg.comName === 'interaction-container-dynamicpanel'">
      <DynamicPanel
        ref="compRef"
        :style="comStyle"
        :id="id"
        :data-id="id"
        :config="comCfg.config"
        :data="comData"
        @mounted="handleCompMounted"
        isView />
    </template>
    <template v-else>
      <component
        ref="compRef"
        :style="comStyle"
        :id="id"
        :data-id="id"
        :config="comCfg.config"
        :data="compData"
        v-if="isCreateComp"
        @mounted="handleCompMounted"
        :is="compDefObj">
      </component>
    </template>
  </div>
</template>

<script>
import ReferPanel from '@/components/refer-panel/ReferPanel'
import DynamicPanel from '@/components/refer-panel/DynamicPanel'
import { mapState } from 'vuex';
import compMixins from '@/mixins/comp';

export default {
  name: 'ViewCompNode',

  components: {
    ReferPanel,
    DynamicPanel
  },

  data () {
    return {
      compDefObj: null,
      isShow: true
    };
  },

  props: {
    id: {
      type: [String, Number],
      required: true
    },
    type: {
      type: String,
      required: true,
      default: 'com',
      validator: v => ['com', 'subCom', 'group'].indexOf(v) > -1
    },
    zIndex: {
      type: Number,
      required: true,
      default: 0
    }
  },

  computed: {
    ...mapState({
      screenLayers: state => state.comtheme.screenLayers
    }),
    comCfg () {
      return this.screenLayers[0];
    },
    compData () {
      return this.comCfg.staticData;
    },
    isCreateComp () {
      return this.compDefObj && this.isShow;
    },
    nodeStyle () {
      const { comCfg } = this;
      if (_.isEmpty(comCfg)) return;
      const style = {
        width: comCfg.attr.w + 'px',
        height: comCfg.attr.h + 'px',
        // left: comCfg.attr.x + 'px',
        // top: comCfg.attr.y + 'px',
        left: 0,
        right: 0,
        zIndex: this.zIndex,
        transition: 'transform 600ms ease 0s'
      };
      return style;
    },
    comStyle () {
      return {
        position: 'absolute',
        left: 0,
        top: 0,
        width: this.comCfg.attr.w + 'px',
        height: this.comCfg.attr.h + 'px'
      };
    }
  },

  created () {
    // 加载组件
    const { comName, version, show } = this.comCfg;
    this.isShow = show;
    const moduleName = `${comName}@${version}`;
    // eslint-disable-next-line
    System.import(moduleName).then(res => {
      const compDef = res.default;
      (compDef.mixins || (compDef.mixins = [])).push(compMixins);
      this.compDefObj = compDef;
    });
  },

  beforeDestroy () {
    const compRef = this.$refs.compRef;
    if (compRef) {
      compRef.off();
    }
  },

  methods: {
    handleCompMounted () {
      this.$emit('compLoaded');
    }
  }
}
</script>

<style lang="scss" scoped>
.view-comp-node {
  position: absolute;
  left: 0;
  top: 0;
  // overflow: hidden;
}
</style>
