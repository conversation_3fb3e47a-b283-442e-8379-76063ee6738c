<template>
  <div class="theme-item">
    <div class="theme-wraper">
      <div class="theme-img" :style="{'background-image': `${url}`}">
        <div class="pop-cont">
          <div class="btn">
            <i class="datav-font icon-edit" @click.stop="editItem()"></i>
            <i class="datav-font icon-delete" @click.stop="delItem()"></i>
          </div>
          <div class="is-def" v-if="!data.isDefault" @click="setDefalut">设为默认</div>
        </div>
      </div>
      <div class="theme-name">
        <div class="name">{{ data.name }}</div>
        <div class="edit" @click="editName">
          <i class="el-icon-edit"></i>
        </div>
      </div>
      <div class="def-tip" v-if="data.isDefault">
        <span class="def">默认主题</span>
        <span class="ndef" @click="setDefalut">取消默认</span>
      </div>
    </div>
  </div>
</template>

<script>
import { replaceUrl } from '@/utils/base'
export default {
  name: 'ThemeListItem',
  props: {
    data: {
      type: Object,
      default () {
        return []
      }
    }
  },
  data () {
    return {
      url: `url(${replaceUrl(this.data.icon)})`
    }
  },
  methods: {
    editItem () { // 编辑
      this.$emit('editItem', this.data);
    },
    delItem () { // 删除
      this.$emit('delItem', this.data);
    },
    setDefalut () {
      this.$emit('setDefault', this.data);
    },
    editName () {
      this.$emit('editName', this.data);
    }
  }
}
</script>

<style lang="scss" scoped>
.theme-item {
  position: relative;
  display: flex;
  height: 194px;
  width: 266px;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  .theme-wraper {
    display: flex;
    position: relative;
    flex-direction: column;
    color: #bcc9d4;
    border: 1px solid #3a4659;
    width: 238px;
    height: 152px;
    transition: .2s;
    overflow: hidden;
    .theme-img {
      position: relative;
      flex: 1;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      &:hover {
        .pop-cont {
          display: block;
        }
      }
    }
    .pop-cont {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, .7);
      .btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        i {
          font-size: 22px;
          cursor: pointer;
          &:nth-of-type(1) {
            margin-right: 10px;
          }
        }
      }
      .is-def {
        position: absolute;
        left: 50%;
        bottom: 20px;
        transform: translateX(-50%);
        color: rgb(0, 119, 255);
        font-size: 12px;
        cursor: pointer;
      }
    }
    .theme-name {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 30px;
      line-height: 30px;
      padding: 0 15px;
      border-top: 1px solid #3a4659;
      .name {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .edit {
        cursor: pointer;
      }
    }
    .def-tip {
      position: absolute;
      top: 0;
      left: 0;
      font-size: 12px;
      padding: 5px 10px;
      background: rgba(0,0,0,.5);
      cursor: pointer;
      &:hover {
        .def {
          display: none;
        }
        .ndef {
          display: inline-block;
        }
      }
      .ndef {
        display: none;
      }
    }
  }
}
</style>
