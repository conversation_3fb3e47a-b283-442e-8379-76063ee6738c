<template>
  <el-dialog :visible.sync="show" :title="title" width="450px" append-to-body top="0" class="theme-type">
    <el-form :model="form" :rules="rules" ref="form" label-suffix="：" label-width="100px" size="mini">
      <el-form-item label="方案名称" prop="name">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="色块" prop="colorList">
        <div class="color-list">
          <div class="color-item" v-for="(item, index) in form.colorList" :key="index">
            <el-color-picker v-model="form.colorList[index]"></el-color-picker>
            <span class="close el-icon-circle-close" @click.stop="deleteColor(index)"></span>
          </div>
          <div class="add-btn" @click="addColor">
            <span class="el-icon-plus"></span>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button size="mini" @click="closeDialog">取消</el-button>
      <el-button size="mini" type="primary" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createThemeScheme, updateThemeScheme } from '@/api/theme';
export default {
  name: 'ThemeScheme', // 主题方案
  props: {},
  data () {
    return {
      show: false,
      title: '新增主题方案',
      form: {
        id: '',
        name: '',
        colorList: ['#F8F407', '#F89407', '#57F807', '#0794F8']
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'change' }
        ],
        colorList: [
          { required: true, message: '请增加主题颜色', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    showDialog () {
      this.form.id = '';
      this.show = true;
      this.title = '新增主题方案';
    },
    showEdit (data) {
      this.show = true;
      this.$nextTick(() => {
        this.form.id = data.id;
        this.form.name = data.name;
        this.form.colorList = data.colorList;
        this.title = '修改主题方案';
      })
    },
    closeDialog () {
      this.show = false;
      this.resetForm();
    },
    deleteColor (index) {
      this.form.colorList.splice(index, 1);
    },
    addColor () {
      this.form.colorList.push('#F8F407')
    },
    resetForm () {
      this.$refs.form.resetFields();
    },
    submit () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          if (!this.form.id) {
            const payload = {
              name: this.form.name,
              colorList: this.form.colorList
            }
            const res = await createThemeScheme(payload);
            if (res && res.success) {
              const data = res.data;
              this.$emit('refresh', data.id);
              this.closeDialog();
            }
          } else {
            const payload = {
              name: this.form.name,
              colorList: this.form.colorList
            }
            const res = await updateThemeScheme(payload, { id: this.form.id });
            if (res && res.success) {
              const data = res.data;
              this.$emit('refresh', data.id);
              this.closeDialog();
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.theme-type {
  .color-list {
    display: flex;
    flex-wrap: wrap;
    .color-item {
      position: relative;
      margin-right: 10px;
      margin-bottom: 10px;
      &:hover {
        .close {
          display: block;
        }
      }
      .close {
        display: none;
        position: absolute;
        top: -5px;
        right: -5px;
        cursor: pointer;
      }
    }
    .add-btn {
      width: 28px;
      height: 28px;
      line-height: 26px;
      text-align: center;
      border: 1px solid #409EFF;
      border-radius: 4px;
      cursor: pointer;
      span {
        color: #409EFF;
      }
    }
  }
}
</style>
