<template>
  <div class="canvas-main">
    <div ref="canvasWrap" id="canvas-wp" class="canvas-panel-wrap">
      <div
        class="screen-shot"
        ref="canvasScreenShot"
        :style="{
          width: shotWidth + 'px',
          height: shotHeight + 'px'
        }"
      >
        <SeatomRuler :scale="scale" :translate="rulerTranslate" :size="rulerSize"/>
        <ThemeCanvasPanel ref="canvasPanel" @ctxClick="$emit('ctxClick', $event)"/>
      </div>
    </div>
    <div class="edit-slider">
      <SeatomSlider v-model="scale"/>
    </div>
  </div>
</template>

<script>
import ThemeCanvasPanel from './ThemeCanvasPanel';
import { mapState } from 'vuex';

export default {
  name: 'CanvasMain',

  components: {
    ThemeCanvasPanel
  },

  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    scale: {
      get () {
        return this.screenInfo.config.scale;
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.scale', value: val }]);
      }
    },
    width: {
      get () {
        return this.screenInfo.config.width;
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.width', value: val }]);
      }
    },
    height: {
      get () {
        return this.screenInfo.config.height;
      },
      set (val) {
        this.$store.dispatch('editor/updateScreenInfo', [{ key: 'config.height', value: val }]);
      }
    },
    rulerSize () {
      return {
        width: Math.max(this.shotWidth - 20, 0),
        height: Math.max(this.shotHeight - 20, 0)
      };
    }
  },

  data () {
    return {
      shotWidth: 0,
      shotHeight: 0,
      rulerTranslate: {
        x: 0,
        y: 0
      }
    };
  },

  watch: {
    'scale' () {
      this.updateShotSize();
    }
  },

  mounted () {
    this.updateShotSize();
    this.$refs.canvasWrap.addEventListener('scroll', this.handleScroll);
  },

  methods: {
    updateShotSize () {
      const canvasPanel = this.$refs.canvasWrap;
      const scaleWidth = this.width * this.scale;
      const scaleHeight = this.height * this.scale;
      const clientWidth = canvasPanel.clientWidth;
      const clientHeight = canvasPanel.clientHeight;
      const left = 80;
      const top = 80;
      const margin = 300;
      if (clientWidth < scaleWidth + left) {
        this.shotWidth = scaleWidth + left + margin;
      } else {
        this.shotWidth = clientWidth;
      }
      if (clientHeight < scaleHeight + top) {
        this.shotHeight = scaleHeight + top + margin;
      } else {
        this.shotHeight = clientHeight;
      }
    },
    handleScroll () {
      const canvasPanel = this.$refs.canvasWrap;
      this.rulerTranslate.x = canvasPanel.scrollLeft;
      this.rulerTranslate.y = canvasPanel.scrollTop;
    }
  }
}
</script>

<style lang="scss" scoped>
.canvas-main {
  flex: 1;
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  user-select: none;
  overflow: hidden;
  .canvas-panel-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
  }
  .edit-slider {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background: #222528;
    box-shadow: 0 -1px #000;
    user-select: none;
    z-index: 1;
  }
}
</style>
