<template>
  <component
    v-if="componentName && showInPanel"
    :is="componentName"
    :node="node"
  >
  </component>
</template>

<script>
import configControls from '@/components/editor/config-controls';
import ConfigNode from '@/components/editor/ConfigNode';
import configMixin from '@/mixins/theme';

Object.values(configControls).forEach(ctl => {
  (ctl.mixins || (ctl.mixins = [])).push(...[configMixin, { components: { ConfigNode } }]);
});

export default {
  name: 'ThemeConfigNode',

  // TODO: 高阶组件 http://hcysun.me/2018/01/05/%E6%8E%A2%E7%B4%A2Vue%E9%AB%98%E9%98%B6%E7%BB%84%E4%BB%B6/
  mixins: [{
    props: configMixin.props,
    computed: { showInPanel: configMixin.computed.showInPanel }
  }],

  components: {
    ...configControls
  },

  computed: {
    componentName () {
      const type = this.node && this.node.data.type;
      if (type && typeof type === 'string') {
        return `Ctl${type[0].toUpperCase()}${type.slice(1)}`;
      }
      return '';
    }
  }
}
</script>

<style>
@import './../../style/configControl.scss';
</style>
