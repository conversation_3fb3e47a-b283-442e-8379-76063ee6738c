<template>
  <VueDraggableResizable
    className="seatom-draggable-resize"
    classNameDraggable="my-draggable-class"
    classNameActive="my-active-class"
    :disableUserSelect="true"
    :active="active"
    :w="comCfg.attr.w"
    :h="comCfg.attr.h"
    :maxWidth="screenWidth"
    :maxHeight="screenHeight"
    :x="comCfg.attr.x"
    :y="comCfg.attr.y"
    :z="active ? 999 : zIndex"
    :scale="1"
    :grid="[10,10]"
    :parent="true"
    :lock="lock"
    :onDragStart="handleDragStart"
    v-show="comCfg.show"
    @activated="onActivated"
    @deactivated="onDeactivated"
    @resizestop="onResizstop"
    @dragging="onDraging"
    @dragstop="onDragstop"
  >
    <div class="comp-overlay"></div>
    <div class="comp-wrapper" id="screenshoter">
      <template v-if="comCfg.comName === 'interaction-container-referpanel'">
        <ReferPanel
          ref="compRef"
          :style="comStyle"
          :id="id"
          :data-id="id"
          :config="comCfg.config"
          :data="comData"
          @mounted="handleCompMounted"
          isView />
      </template>
      <template v-else-if="comCfg.comName === 'interaction-container-dynamicpanel'">
        <DynamicPanel
          ref="compRef"
          :style="comStyle"
          :id="id"
          :data-id="id"
          :config="comCfg.config"
          :data="comData"
          @mounted="handleCompMounted"
          isView />
      </template>
      <template v-else>
        <component
          ref="compRef"
          :style="comStyle"
          :id="id"
          :data-id="id"
          :config="comCfg.config"
          :data="comData"
          v-if="!!compDefObj && comCfg.show"
          @mounted="handleCompMounted"
          :is="compDefObj">
        </component>
      </template>
    </div>
  </VueDraggableResizable>
</template>

<script>
import VueDraggableResizable from '@/lib/vue-draggable-resizable/src/index';
import ReferPanel from '@/components/refer-panel/ReferPanel'
import DynamicPanel from '@/components/refer-panel/DynamicPanel'
import { mapState } from 'vuex';
import compMixins from '@/mixins/comp';

export default {
  name: 'ThemeNode',
  inject: ['getLayerTree'],
  components: {
    VueDraggableResizable,
    ReferPanel,
    DynamicPanel
  },

  data () {
    return {
      compDefObj: null
    };
  },

  props: ['id', 'type', 'zIndex'],

  computed: {
    ...mapState({
      screenComs: state => state.comtheme.screenComs,
      screenInfo: state => state.editor.screenInfo,
      screenFilters: state => state.editor.screenFilters
    }),
    layerTree () {
      return this.getLayerTree();
    },
    comData () {
      return this.comCfg.staticData;
    },
    scale () {
      return this.screenInfo.config.scale;
    },
    screenWidth () {
      return this.screenInfo.config.width;
    },
    screenHeight () {
      return this.screenInfo.config.height;
    },
    active () {
      return true;
    },
    comCfg () {
      return this.screenComs[this.id];
    },
    comStyle () {
      return {
        width: this.comCfg.attr.w + 'px',
        height: this.comCfg.attr.h + 'px'
      };
    },
    comSize () {
      return {
        x: this.comCfg.attr.x,
        y: this.comCfg.attr.y,
        w: this.comCfg.attr.w,
        h: this.comCfg.attr.h
      };
    },
    lock () {
      return this.comCfg.attr.lock;
    },
    moduleName () {
      if (!this.comCfg) return '';
      const { comName, version } = this.comCfg;
      return `${comName}@${version}`;
    }
  },

  watch: {
    'comSize' (newAttr) {
      const comp = this.$refs.compRef;
      if (comp) {
        comp.resize({ width: newAttr.w, height: newAttr.h });
      }
    },
    'moduleName' (newVal) {
      if (newVal) {
        this.loadCompDef();
      }
    }
  },

  created () {
    this.loadCompDef();
  },

  methods: {
    handleCompMounted () {},
    async loadCompDef () {
      if (!this.moduleName) return;
      // eslint-disable-next-line
      const res = await System.import(this.moduleName);
      const compDef = res.default;
      (compDef.mixins || (compDef.mixins = [])).push(compMixins);
      this.compDefObj = compDef;
    },
    onActivated () {
      // this.$parent.$refs.contextmenu.hide();
      this.$store.commit('editor/updateCurrentSelectId', this.id);
    },
    onDeactivated () {
      // 当前结点取消激活时触发
    },
    onResizstop (left, top, width, height) {
      this.$store.dispatch('comtheme/updateScreenComTheme', {
        id: this.id,
        keyValPairs: [
          { key: 'attr.w', value: width },
          { key: 'attr.h', value: height }
        ]
      });
    },
    handleDragStart () {
      // this.$parent.$refs.contextmenu.hide();
    },
    onDraging (left, top) {
      //
    },
    onDragstop (left, top) {
      // this.$store.dispatch('editor/updateScreenCom', {
      //   id: this.id,
      //   keyValPairs: [
      //     { key: 'attr.x', value: left },
      //     { key: 'attr.y', value: top }
      //   ]
      // });
    }
  }
}
</script>

<style lang="scss" scoped>
.canvas-panel {
  position: absolute;
  top: 80px;
  left: 80px;
  transform-origin: 0 0;
  transition: 0.2s all ease-in-out;
  // background-color: rgb(32, 32, 32);
  // background-size: cover, contain;
  // background-position: center, right bottom;
  // background-repeat: no-repeat, no-repeat;
  box-shadow: rgba(0, 0, 0, 0.5) 0 0 30px 0;
  // background: linear-gradient(-90deg, rgba(0, 0, 0, 1) 1px, transparent 1px) 0% 0% / 10px 10px, linear-gradient(rgba(0, 0, 0, 1) 1px, transparent 1px) 0% 0% / 10px 10px;
}
.seatom-draggable-resize {
  background: transparent;
  position: absolute;
  &.my-draggable-class.my-active-class {
    cursor: move;
  }
  .navigator-line {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    box-sizing: content-box;
    .navigator-line-left {
      position: absolute;
      border-top: 1px dashed #2483ff;
      height: 0;
      top: 0;
      transform: translateX(-100%);
    }
    .navigator-line-top {
      position: absolute;
      border-left: 1px dashed #2483ff;
      width: 0;
      left: 0;
      transform: translateY(-100%);
    }
    .navigator-line-account {
      position: absolute;
      transform: translate(-100%,-100%);
      color: #2483ff;
      // text-shadow: 1px 1px 1px #222;
      white-space: nowrap;
    }
  }
  .comp-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
  }
}
</style>
