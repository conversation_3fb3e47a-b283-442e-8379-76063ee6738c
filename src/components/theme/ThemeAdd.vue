<template>
  <div class="theme-add">
    <el-dialog :visible.sync="show" :title="title" width="500px" top="0vh" :close-on-click-modal="false">
      <el-form :model="form" :rules="rules" label-width="100px" ref="form">
        <el-form-item label="主题名称：" prop="name">
          <el-input v-model="form.name" placeholder="请输入主题名称" size="mini"></el-input>
        </el-form-item>
        <el-form-item label="主题类型：" prop="type">
          <div class="content-row">
            <el-select class="select" v-model="form.type" clearable placeholder="请选择主题类型" size="mini">
              <el-option label="样式主题" value="style"></el-option>
              <el-option label="功能主题" value="function"></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="主题方案：" prop="themeSchemeId">
          <div class="content-row">
            <el-select class="select" v-model="form.themeSchemeId" clearable placeholder="请选择主题方案" size="mini">
              <el-option :value="item.id" :label="item.name" v-for="item in themeScheme" :key="item.id">
                <div class="theme-item-1">
                  <span>{{ item.name }}</span>
                  <div class="color-list">
                    <div class="color-item" :style="{'background-color': color}" v-for="(color, index) in item.colorList" :key="index"></div>
                  </div>
                </div>
              </el-option>
            </el-select>
            <el-dropdown class="add-btn" size="mini" split-button type="primary" @click="add" @command="schemeManage">
              新增
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="scheme">管理方案</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button size="mini" @click="close">取消</el-button>
        <el-button type="primary" size="mini" @click="submit">确定</el-button>
      </div>
    </el-dialog>
    <ThemeScheme ref="theme" @refresh="refreshScheme" />
  </div>
</template>

<script>
import { createTheme, updateTheme, themeScheme } from '@/api/theme';
import ThemeScheme from './ThemeScheme';
export default {
  name: 'ThemeAdd', // 新增主题
  props: {
    packageId: [String, Number]
  },
  components: {
    ThemeScheme
  },
  data () {
    return {
      show: false,
      title: '新增主题',
      form: {
        id: '',
        name: '',
        type: '',
        themeSchemeId: ''
      },
      rules: {
        name: [
          { required: true, message: '主题名称必填', trigger: 'change' }
        ],
        type: [
          { required: true, message: '主题类型必选', trigger: 'change' }
        ]
      },
      themeScheme: []
    }
  },
  methods: {
    showTheme () {
      this.show = true;
      this.form.id = '';
      this.title = '新增主题';
      this.getThemeScheme();
    },
    showEdit (item) {
      this.show = true;
      this.$nextTick(() => {
        this.form.id = item.id;
        this.form.name = item.name;
        this.form.themeSchemeId = item.themeSchemeId;
        this.form.type = item.themeType;
        this.title = '修改主题';
        this.getThemeScheme();
      })
    },
    close () {
      this.show = false;
      this.$refs.form.resetFields();
    },
    add () {
      this.$refs.theme.showDialog();
    },
    schemeManage (command) {
      if (command === 'scheme') {
        this.$router.push({
          path: '/setting/theme-scheme'
        })
      }
    },
    async getThemeScheme () { // 获取主题方案
      const res = await themeScheme();
      if (res && res.success) {
        this.themeScheme = res.data || [];
      }
    },
    refreshScheme (id) {
      this.getThemeScheme();
      if (id) {
        this.form.themeSchemeId = id;
      }
    },
    submit () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const data = {
            packageId: this.packageId,
            name: this.form.name,
            themeSchemeId: this.form.themeSchemeId,
            themeType: this.form.type
          }
          if (!this.form.id) {
            const res = await createTheme(data, {});
            if (res && res.success) {
              this.$emit('refresh');
              this.close();
              this.$router.push({
                path: '/theme-edit',
                query: {
                  themeId: res.data.id,
                  comType: res.data.comType,
                  packageId: this.packageId
                }
              })
            }
          } else {
            const data = {
              name: this.form.name,
              themeSchemeId: this.form.themeSchemeId,
              themeType: this.form.type
            }
            this.loading = true;
            const res = await updateTheme(data, { id: this.form.id });
            if (res && res.success) {
              this.$emit('refresh');
              this.close();
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.theme-add {
  .content-row {
    display: flex;
    align-items: center;
    .select {
      flex: 1;
    }
    .add-btn {
      margin-left: 15px;
    }
  }
}
::v-deep .theme-item-1 {
  display: flex;
  align-items: center;
  cursor: pointer;
  span {
    display: inline-block;
    width: 60px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .color-list {
    display: flex;
    margin-left: 10px;
    .color-item {
      height: 14px;
      width: 30px;
    }
  }
}
</style>
