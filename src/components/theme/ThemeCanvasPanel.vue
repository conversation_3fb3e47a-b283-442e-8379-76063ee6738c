<template>
  <div
    class="canvas-panel"
    :style="panelStyle"
    ref="canvasPanel"
    @click.stop
    @dragover.prevent="handleDragOver"
    @drop.prevent="handleDrop">
    <ThemeNode
      v-for="(item, index) in comLayerArr"
      :key="item.id"
      :id="item.id"
      :type="item.type"
      :zIndex="comLayerArr.length - index"
    >
    </ThemeNode>
  </div>
</template>

<script>
import ThemeNode from './ThemeNode';
import { mapState, mapGetters } from 'vuex';
import { uuid } from '@/utils/base';
import { getOffsetX, getOffsetY } from '@/utils/dom';

export default {
  name: 'CanvasPanel',

  inject: ['getLayerTree'],

  components: {
    ThemeNode
  },

  data () {
    return {
      isClickBlank: false
    };
  },

  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    }),
    ...mapGetters('editor', ['getComDataById']),
    panelStyle () {
      const { width, height, scale, backgroundColor, backgroundImage, globalFilterParams } = this.screenInfo.config;
      const style = {
        width: width + 'px',
        height: height + 'px',
        transform: `scale(${scale})`,
        backgroundColor
      };
      if (backgroundImage) {
        style.backgroundImage = `url(${backgroundImage})`;
      }
      if (globalFilterParams.enable) {
        style.filter = `hue-rotate(${globalFilterParams.hue}deg)
          saturate(${globalFilterParams.saturate}%)
          brightness(${globalFilterParams.brightness}%)
          contrast(${globalFilterParams.contrast}%)
          opacity(${globalFilterParams.opacity}%)
          grayscale(${globalFilterParams.grayscale}%)`
      }
      return style;
    },
    layerTree () {
      return this.getLayerTree();
    },
    comLayerArr () {
      const nodes = this.layerTree?.data?.children || [];
      if (nodes && nodes.length) {
        return nodes;
      }
      return [];
    }
  },

  methods: {
    handleDragOver (e) { /* 勿删，否则无法触发拖拽结束事件！！！！ */ },
    handleDrop (e) {
      let dragData = e.dataTransfer.getData('application/json');
      dragData = JSON.parse(dragData);
      if (dragData && dragData.type === 'com') {
        const compData = dragData.data;
        // 创建组件
        const w = compData.width;
        const h = compData.height;
        const ox = getOffsetX(e);
        const oy = getOffsetY(e);
        const x = Math.max(ox, 0);
        const y = Math.max(oy, 0);
        const data = {
          id: uuid(compData.name),
          name: compData.name,
          version: compData.version,
          attr: {
            w,
            h,
            x,
            y
          }
        };
        this.$store.dispatch('editor/createScreenComp', data).then(() => {
          const tree = this.layerTree;
          const newData = this.getComDataById(data.id);
          if (newData) {
            const layer = {
              id: newData.id,
              type: newData.type
            }
            tree.addChild(layer);

            if (!!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene') {
              this.$store.dispatch('editor/insertScreenLayers', {
                screenId: this.screenInfo.id,
                layers: [layer]
              })
            } else {
              this.$store.dispatch('editor/updateScreenLayers', tree.data.children)
            }

            this.$store.commit('editor/updateCurrentSelectId', newData.id);
          }
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.canvas-panel {
  position: absolute;
  top: 80px;
  left: 80px;
  transform-origin: 0 0;
  transition: 0.2s all ease-in-out;
  // background-color: rgb(32, 32, 32);
  // background-size: cover, contain;
  // background-position: center, right bottom;
  // background-repeat: no-repeat, no-repeat;
  box-shadow: rgba(0, 0, 0, 0.5) 0 0 30px 0;
  // background: linear-gradient(-90deg, rgba(0, 0, 0, 1) 1px, transparent 1px) 0% 0% / 10px 10px, linear-gradient(rgba(0, 0, 0, 1) 1px, transparent 1px) 0% 0% / 10px 10px;
}
</style>
