import Vue from 'vue'
import ref from 'vue-ref'

Vue.use(ref, { name: 'ant-ref' })

Vue.directive('focus', {
  inserted: function (el, binding) {
    el.querySelector('input').focus()
  }
})

Vue.directive('clickoutside', {
  bind (el, props) {
    el.clickCatcher = function (e) {
      if (props.modifiers.hidden) { // hidden modifier is required when target component is hidden using v-if or v-show
        props.modifiers.hidden = false
        return
      }
      if (!el.contains(e.target) && typeof props.value === 'function') {
        props.value(props.arg)
      }
    }
    window.addEventListener('click', el.clickCatcher)
  },
  unbind: el => window.removeEventListener('click', el.clickCatcher)
})

Vue.directive('drag', {
  inserted: function (el, binding) {
    // 拖拽时的手势
    el.style.cursor = binding.value && binding.value.cursor ? binding.value.cursor : 'default'
    // 如果拖动元素非元素本身（el），传入id
    var moveEl = binding.value && binding.value.moveElId ? document.getElementById(binding.value.moveElId) : el
    // 为拖动元素添加绝对定位
    moveEl.style.position = 'absolute'
    // 如果容器为设置position属性，默认为 position = 'relative'
    if (getComputedStyle(moveEl.parentNode, null).position === 'static') {
      moveEl.parentNode.style.position = 'relative'
    }
    var mouseDownFn = function (e) {
      // .shaow---------- 复制节点，并且插入容器中原来位置
      if (binding.modifiers.shaow) {
        var newNode = moveEl.cloneNode(true)
        moveEl.style.opacity = '0.5'
        moveEl.parentNode.appendChild(newNode)
      }
      // ----------
      var disX, disY
      if (!binding.modifiers.dragY) disX = e.clientX - moveEl.offsetLeft
      if (!binding.modifiers.dragX) disY = e.clientY - moveEl.offsetTop
      var mouseMoveFn = function (e) {
        e.preventDefault()
        var left = e.clientX - disX
        var top = e.clientY - disY
        // 可以拖出去的元素的剩余宽度
        // dragOutX
        var limitWidth = binding.value && binding.value.dragOutX ? moveEl.offsetWidth - binding.value.dragOutX : 0
        // dragOutY
        var limitHeigthTop, limitHeigth
        if (binding.value && binding.value.dragOutY) {
          limitHeigth = moveEl.offsetHeight - binding.value.dragOutY
          // 防止可拖拽区域被拖出容器区域
          // 拖拽元素在顶部
          limitHeigthTop = el.offsetHeight - binding.value.dragOutY
        } else {
          limitHeigth = 0
          limitHeigthTop = 0
        }
        if (left < 0 - limitWidth) {
          left = 0 - limitWidth
        } else if (left > moveEl.parentNode.clientWidth - moveEl.offsetWidth + limitWidth) {
          left = moveEl.parentNode.clientWidth - moveEl.offsetWidth + limitWidth
        }
        if (top < 0 - limitHeigthTop) {
          top = 0 - limitHeigthTop
        } else if (top > moveEl.parentNode.clientHeight - moveEl.offsetHeight + limitHeigth) {
          top = moveEl.parentNode.clientHeight - moveEl.offsetHeight + limitHeigth
        }
        moveEl.style.left = left + 'px'
        moveEl.style.top = top + 'px'
        // 拖拽事件
        if (binding.value && binding.value.ondrag) {
          if (typeof binding.value.ondrag !== 'function') {
            throw (new Error('ondrag: should be a function'))
          }
          binding.value.ondrag(e, { left: left, top: top })
        }
      }
      // mousemove
      document.addEventListener('mousemove', mouseMoveFn)
      var mouseUpFn = function () {
        // 移除临时shaow节点
        if (newNode) {
          moveEl.style.opacity = '1'
          newNode.parentNode.removeChild(newNode)
        }
        document.removeEventListener('mousemove', mouseMoveFn)
        document.removeEventListener('mouseup', mouseUpFn)
      }
      // mouseup
      document.addEventListener('mouseup', mouseUpFn)
    }
    // mousedown
    el.addEventListener('mousedown', mouseDownFn)
  }
})

Vue.directive('ant-input', {
  created (el, binding) {
    if (!binding.modifiers || !binding.modifiers.lazy) {
      addEventListener(el, 'compositionstart', onCompositionStart)
      addEventListener(el, 'compositionend', onCompositionEnd)
      // Safari < 10.2 & UIWebView doesn't fire compositionend when
      // switching focus before confirming composition choice
      // this also fixes the issue where some browsers e.g. iOS Chrome
      // fires "change" instead of "input" on autocomplete.
      addEventListener(el, 'change', onCompositionEnd)
    }
  }
})

Vue.directive('web-title', {
  inserted (el, binding) {
    const { value } = binding
    if (el.dataset.title) {
      document.title = el.dataset.title
    } else if (value && value.title) {
      document.title = value.title
    }
  },
  update (el, binding, vNode, oldVNode) {
    const { value } = binding
    if (el.dataset.title) {
      document.title = el.dataset.title
    } else if (value && value.title) {
      document.title = value.title
    }
  }
})

function onCompositionStart (e) {
  e.target.composing = true
}

function onCompositionEnd (e) {
  // prevent triggering an input event for no reason
  if (!e.target.composing) return
  e.target.composing = false
  trigger(e.target, 'input')
}

function trigger (el, type) {
  const e = document.createEvent('HTMLEvents')
  e.initEvent(type, true, true)
  el.dispatchEvent(e)
}

export function addEventListener (el, event, handler, options) {
  el.addEventListener(event, handler, options)
}
