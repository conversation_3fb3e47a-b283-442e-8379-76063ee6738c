<template>
  <div class="seatom-slider">
    <el-select v-model="selectValue" placeholder="请选择" size="mini">
      <el-option
        v-for="item in scaleSelect"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </el-option>
    </el-select>
    <i class="seatom-icon datav-font icon-zoom-out slider-icon zoom-out" @click.stop="handleZoomOut"></i>
    <input
      type="range"
      min="0.1"
      max="1.9"
      :value="value"
      step="0.1"
      @change="handleChange"
      class="input-range"
    >
    <i class="seatom-icon datav-font icon-zoom-in slider-icon zoom-in" @click.stop="handleZoomIn"></i>
    <i class="seatom-icon slider-icon datav-font icon-viewport" @click.stop="handleThumbnail"></i>
  </div>
</template>

<script>
import canvasBus from '@/utils/canvasBus';
export default {
  name: 'SeatomSlider',

  model: {
    prop: 'value',
    event: 'change'
  },

  data () {
    return {
      scaleSelect: [{
        label: '50%',
        value: 0.5
      }, {
        label: '100%',
        value: 1
      }, {
        label: '150%',
        value: 1.5
      }, {
        label: '190%',
        value: 1.9
      }]
      // selectValue: 1
    }
  },

  props: ['value'],

  created () {
    // console.log(this.value);
  },

  computed: {
    selectValue: {
      get () {
        return Math.floor(this.value.toPrecision(2) * 100) + '%';
      },
      set (val) {
        this.$emit('change', val);
      }
    }
  },

  methods: {
    handleChange (e) {
      this.$emit('change', Number(e.target.value));
    },
    handleZoomOut () {
      const val = Math.max(0.1, this.value - 0.1);
      this.$emit('change', val);
    },
    handleZoomIn () {
      const val = Math.min(1.9, this.value + 0.1);
      this.$emit('change', val);
    },
    handleThumbnail () {
      canvasBus.emit('thumbnail-hide')
    }
  }
}
</script>

<style lang="scss" scoped>
.seatom-slider {
  position: relative;
  right: 4px;
  padding: 4px 0;
  display: flex;
  align-items: center;
  cursor: pointer;
  .icon-disable {
    color: #bcc9d4;
  }
  .slider-icon {
    color: #bcc9d4;
    font-size: 15px;
    margin: 0 8px;
  }
  .zoom-out {
    cursor: zoom-out;
  }
  .zoom-in {
    cursor: zoom-in;
  }
  .input-range {
    cursor: pointer;
    height: 2px;
    box-sizing: border-box;
    outline: 0;
    margin: 0;
    display: inline-block;
  }
  .el-select {
    width: 60px;
    ::v-deep .el-input__inner {
      padding-left: 8px;
      padding-right: 0;
    }
    ::v-deep .el-input__suffix {
      width: 20px;
    }
  }
}
</style>
