import Vue from 'vue';

// 字符串超出 count 个字符，显示省略号过滤器
Vue.filter('textOverflowEllipsis', function (value, count = 10) {
  if (!value) return ''
  value = value.toString();
  if (value.length > count) {
    value = value.slice(0, count) + '...';
  }
  return value;
});

Vue.filter('formatDate', (value) => {
  const date = new Date(value);
  const y = date.getFullYear();
  let MM = date.getMonth() + 1;
  MM = MM < 10 ? ('0' + MM) : MM;
  let d = date.getDate();
  d = d < 10 ? ('0' + d) : d;
  let h = date.getHours();
  h = h < 10 ? ('0' + h) : h;
  let m = date.getMinutes();
  m = m < 10 ? ('0' + m) : m;
  let s = date.getSeconds();
  s = s < 10 ? ('0' + s) : s;
  return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
})

Vue.filter('formatField', (value) => {
  switch (value) {
    case 'date' : return '日期'
    case 'string' : return '文本'
    case 'number' : return '数值'
    default : return value
  }
})

// 根据文件列表获取文件总数
Vue.filter('fileListTotal', (value) => {
  return value.filter(it => it.isShow).length;
})
