import Vue from 'vue'
import keymaster from 'keymaster'

const bindKeyHandler = ({ func, params = '' }) => {
  return () => {
    func(params)
    return false
  }
}

keymaster.filter = (e) => {
  const tagName = (e.target || e.srcElement).tagName;
  return !(tagName === 'INPUT' || tagName === 'SELECT' || tagName === 'TEXTAREA' || tagName === 'HZ-AI-DIALOG')
}

export const shortcut = {
  bind: (seed, func) => keymaster(seed, bindKeyHandler(func)),
  ...keymaster
}

Vue.prototype.$shortcut = shortcut
