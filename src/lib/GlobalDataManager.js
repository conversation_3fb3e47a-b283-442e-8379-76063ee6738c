export default class GlobalDataManager {
  constructor (variables, { state, route, loginUser }) {
    this.state = {};
    this.variableKeys = [];
    this.initGlobalData(variables, { state, route, loginUser });
  }

  initGlobalData (variables, { state, route, loginUser }) {
    variables.forEach(item => {
      this.variableKeys.push(item.name);
      Object.defineProperty(this.state, item.name, {
        get: function () {
          // eslint-disable-next-line
          const func = new Function('callbackArgs', 'route', 'loginUser', item.content);
          return func(state, route, loginUser)
        },
        set: function () {
          console.error(`ERROR: 自定义变量[globalData.${item.name}]不允许赋值操作！！`)
        }
      })
    })
  }

  getGlobalData () {
    return _.pick(this.state, this.variableKeys)
  }
}
