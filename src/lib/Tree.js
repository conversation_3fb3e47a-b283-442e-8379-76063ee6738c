/*eslint-disable*/
/*
支持功能：
1、置顶、置底、上移、下移
2、成组、取消成组
3、复制、删除
*/

import EventEmiter from '@/utils/event';

// ideas from [d3-hierarchy](https://github.com/d3/d3-hierarchy)
function hierarchy (data, children, tree) {
  children = children || 'children';
  const root = new Node(data, tree);

  let node; const nodes = [root];

  while (node = nodes.pop()) {
    let childs, child, n;
    if ((childs = node.data[children]) && (n = (childs = Array.from(childs)).length)) { // 如果有孩子节点
      node.children = childs;
      for (let i = n - 1; i >= 0; --i) { // 遍历孩子节点
        nodes.push(child = childs[i] = new Node(childs[i], tree));
        child.parent = node;
        child.depth = node.depth + 1;
      }
    }
  }

  return root;
}
class Tree extends EventEmiter {
  constructor(data, options) {
    super();
    this.options = Object.assign({
      children: 'children'
    }, options);
    this.loadedNodes = [];
    this.nodesMap = {};
    this.data = data;

    this.root = hierarchy(data, this.options.children, this);

    if (this.root.children) {
      this.root.children.forEach(child => {
        child.setLoad();
      });
    }

    this.render();
  }
  each (callback, that) {
    return this.root.each(callback, that);
  }
  eachBefore (callback, that) {
    return this.root.eachBefore(callback, that);
  }
  eachAfter (callback, that) {
    return this.root.eachAfter(callback, that);
  }
  find (callback, that) {
    return this.root.find(callback, that);
  }
  findAll (callback, that) {
    return this.root.findAll(callback, that);
  }
  registerNode (node) {
    this.nodesMap[node.data.id] = node;
    return this;
  }
  getNodeById (id) {
    return this.nodesMap[id];
  }
  render () {
    this.loadedNodes = [];
    this.eachBefore(n => {
      if (!n.isRoot() && n.loaded) {
        this.loadedNodes.push(n);
      }
    });

    this.data = this.toJson();
    this.emit('render');
    return this;
  }
  toJson () {
    return this.root.toJson();
  }
  addChild (data) {
    if (!data) return this;
    if (!Array.isArray(data)) {
      data = [data];
    }
    const nodes = data.map(d => hierarchy(d, this.options.children, this));
    if (nodes.length) {
      nodes.forEach(n => {
        this.root.addChildAtIndex(n, 0);
        n.setLoad();
      });
      this.render();
    }
    return this;
  }
  bringToTop (id) {
    if (!Array.isArray(id)) {
      id = [id];
    }
    const nodes = id.map(d => this.getNodeById(d)).filter(n => n instanceof Node);
    for (let i = nodes.length - 1; i >= 0; --i) {
      nodes[i].setIndex(0);
    }
    this.render();
    return this;
  }
  bringToBottom (id) {
    if (!Array.isArray(id)) {
      id = [id];
    }
    const nodes = id.map(d => this.getNodeById(d)).filter(n => n instanceof Node);
    for (let i = 0, n = nodes.length; i < n; ++i) {
      nodes[i].setIndex(nodes[i].parent.children.length - 1);
    }
    this.render();
    return this;
  }
  moveUp (id) {
    if (!Array.isArray(id)) {
      id = [id];
    }
    const nodes = id.map(d => this.getNodeById(d)).filter(n => n instanceof Node);
    for (let i = 0, n = nodes.length; i < n; ++i) {
      const node = nodes[i];
      const preNode = node.preSibling();
      if (preNode) {
        node.switch(preNode);
      } else {
        break;
      }
    }
    this.render();
    return this;
  }
  moveDown (id) {
    if (!Array.isArray(id)) {
      id = [id];
    }
    const nodes = id.map(d => this.getNodeById(d)).filter(n => n instanceof Node);
    for (let i = nodes.length - 1; i >= 0; --i) {
      const node = nodes[i];
      const nextNode = node.nextSibling();
      if (nextNode) {
        node.switch(nextNode);
      } else {
        break;
      }
    }
    this.render();
    return this;
  }
  delete (id) {
    if (!Array.isArray(id)) {
      id = [id];
    }
    const nodes = id.map(d => this.getNodeById(d)).filter(n => n instanceof Node);
    nodes.forEach(node => {
      node.drop();
      delete this.nodesMap[node.data.id];
    });
    this.removeEmptyGroup();
    this.render();
    return this;
  }
  // 移除空组
  removeEmptyGroup () {
    const emptyNodes = this.findAll(node => {
      return !node.isRoot() && node.data.type === 'group' && !node.hasChildren();
    });
    emptyNodes.forEach(node => {
      node.drop();
    });
    return this;
  }
  /**
   * 成组
   * 限制条件
   * 1、层数限制，如果组里嵌套了组，则不能成组
   * 2、如果组里面的组展开了，成组后再次打开保持打开状态
   */
  createGroup (groups, options = {}) {
    const nodes = groups.map(data => this.getNodeById(data.id));
    // if (nodes.length > 1) {
      let timeStamp = new Date();
      let info = { id: `groups_${+timeStamp}`, type: 'group', groupName: '分组', ...options };
      nodes.forEach(item => {
        // if(item.data && item.data.groupName) {
        //   item.data.parentId = `group_${+timeStamp}`
        // }
        if(item.data?.sceneId?.length) {
          info.sceneId = item.data.sceneId;
          info.pageId = item.data.pageId;
        }
      })
      const parent = nodes[0].parent;

      const groupNode = hierarchy(info, this.options.children, this);
      parent.addChildAtIndex(groupNode, nodes[0].getIndex());

      nodes.forEach(n => {
        n.drop();
        groupNode.addChild(n);
      });

      groupNode.setLoad();
      groupNode.collapse = true;
      groupNode.each(n => {
        if (n !== groupNode) {
          n.setLoad(false);
        }
      });

      this.render();
    // }
    return groupNode;
  }
  cancelGroup (data) {
    const node = this.getNodeById(data.id);
    if (!node || node.data.type !== 'group') return this;
    let index = node.getIndex();
    const parent = node.parent;
    if (node.children) {
      node.children.forEach(child => {
        parent.addChildAtIndex(child, index++);
        child.setLoad();
      });
      node.drop();
      this.render();
    }
    return this;
  }
  collapseChildNodes (data, collapse = true) {
    const node = this.getNodeById(data.id);
    const selectedNodes = this.getSelectedNodes();
    if (node && node.hasChildren()) {
      node.children.forEach(child => {
        child.setLoad(!collapse);
        const isSelected = selectedNodes.includes(child);
        child.selected = isSelected;
        if (!child.collapse) {
          // child.children && child.children.forEach(n => {
          //   n.setLoad(!collapse);
          //   child.selected = false;
          // });
          /* 递归修复超过3层带有子分组的分组展开时bug */
          this.collapseChildNodes(child.data, collapse)
        }
      });
      this.render();
    }
    return this;
  }
  getSelectedNodes () {
    // return this.loadedNodes.filter(n => n.selected);
    let selectedList = [];
    this.loadedNodes.forEach(n => {
      let idList = selectedList.map(item => item.data.id);
      if (!idList.includes(n.data.id) && n.selected) selectedList.push(n);
      if (n.children) selectedList = this.recursionGetSelectedNodes(n.children, selectedList);
    })
    return selectedList;
  }
  recursionGetSelectedNodes(nodes, selectedList) {
    nodes.forEach(n => {
      let idList = selectedList.map(item => item.data.id);
      if (!idList.includes(n.data.id) && n.selected) selectedList.push(n);
      if (n.children) this.recursionGetSelectedNodes(n.children, selectedList);
    })
    return selectedList;
  }
  clearSelect (silent = true) {
    const selectedNodes = this.getSelectedNodes();
    selectedNodes.forEach(n => {
      n.selected = false;
    });
    if (!silent) {
      this.render();
    }
    return this;
  }
  select (id, muti = 0, preData) {
    if (id == void 0) return this;
    let node;
    // const node = this.loadedNodes.find(n => {
    //   return n.data.id === id;
    // });
    // if (!node) return this;

    const selectedNodes = this.loadedNodes.filter(n => n.selected);

    if (muti === 0) { // 选中一个或多个，其他清空
      selectedNodes.forEach(n => {
        n.selected = false;
      });
      if (!Array.isArray(id)) {
        id = [id];
      }
      id.forEach(d => {
        node = this.nodesMap[d];
        if (node) {
          node.selected = true;
        }
      });
    } else if (muti === 1) { // toggle 取反选中
      node = this.nodesMap[id];
      if (!node) return this;
      selectedNodes.forEach(n => {
        if (n.depth !== node.depth) {
          n.selected = false;
        }
      });
      node.selected = !node.selected;
    } else if (muti === 2) { // shift键连续选中
      node = this.nodesMap[id];
      if (!node) return this;
      const preNode = this.loadedNodes.find(n => {
        return n.data.id === preData?.id;
      });
      if (!preNode) return this;
      selectedNodes.forEach(n => {
        if (n !== preNode) n.selected = false;
      });
      node.selected = true;
      if (node.depth !== preNode.depth) {
        preNode.selected = false;
      }
      node.betweenNodes(preNode).forEach(n => {
        n.selected = true;
      });
    }
  }
  dragInsert (anchorNode, anchorDirection, selectDatas) {
    if (!anchorNode || anchorDirection < 0 || !selectDatas || !selectDatas.length) return this;

    const anchorParent = anchorNode.parent;
    let isInsertBefore = anchorDirection === 0;
    const selectNodes = selectDatas.map(d => this.getNodeById(d.id));

    const hasAnchorParent = selectNodes.some(n => n === anchorParent);
    if (hasAnchorParent) return this; // 如果选中锚节点的父节点，则无法移动

    if (anchorNode.data.type === 'group' && !anchorNode.collapse && !isInsertBefore) {
      // 如果锚点是展开的组，且插入到它后面。则应该把插入到它的子节点中
      anchorNode = anchorNode.children[0];
      isInsertBefore = true;
    }

    const insertArr = []

    let node;
    if (!isInsertBefore) selectNodes.reverse();
    while (node = selectNodes.pop()) {
      node[isInsertBefore ? 'insertBefore' : 'insertAfter'](anchorNode);

      insertArr.push({
        nodeData: node.data,
        anchorNodeData: anchorNode.data,
        action: isInsertBefore ? 'insertBefore' : 'insertAfter'
      })

      anchorNode = node;
    }

    this.removeEmptyGroup();
    this.render();
    return insertArr;
  }
}

function Node (data, tree) {
  this.data = data || {};
  this.depth = 0;
  this.parent = null;
  // 自定义的一些属性
  this.loaded = false;
  this.selected = false;
  this.collapse = true; // 默认收起
  this.tree = tree;
  // 可选属性 this.children

  this.tree.registerNode(this);
}

// 最小公共祖先
function leastCommonAncestor (a, b) {
  if (a === b) return a;
  const aNodes = a.ancestors();
  const bNodes = b.ancestors();
  let c = null;
  a = aNodes.pop();
  b = bNodes.pop();
  while (a === b) {
    c = a;
    a = aNodes.pop();
    b = bNodes.pop();
  }
  return c;
}

function insertChild (self, child, insertIndex) {
  if (!(child instanceof Node)) {
    throw new Error('Child must be of type Node.');
  }
  if (typeof insertIndex === 'undefined' || !self.hasChildren()) {
    (self.children || (self.children = [])).push(child);
  } else {
    if (insertIndex < 0 || insertIndex > self.children.length) {
      throw new Error('Invalid index.');
    }
    self.children.splice(insertIndex, 0, child);
  }

  child.parent = self;
  child.depth = self.depth + 1;
  child.eachBefore(function (n) {
    if (n !== child) {
      n.depth = n.parent.depth + 1;
    }
  });

  return self;
}

Node.prototype = {
  constructor: Node,
  isRoot () {
    return this.parent == null && this.depth === 0;
  },
  hasChildren () {
    return this.children && this.children.length;
  },
  addChild (child) {
    return insertChild(this, child);
  },
  addChildAtIndex (child, index) {
    return insertChild(this, child, index);
  },
  insertBefore (node) {
    if (!(node instanceof Node)) return this;
    if (node.isRoot() || this.isRoot()) return this;
    if (node === this) return this;
    if (node.parent === this) return this;
    const parent = node.parent;
    const selfNode = this.drop();
    const index = node.getIndex();
    parent.addChildAtIndex(selfNode, Math.max(0, index));
    return this;
  },
  insertAfter (node) {
    if (!(node instanceof Node)) return this;
    if (node.isRoot() || this.isRoot()) return this;
    if (node === this) return this;
    if (node.parent === this) return this;
    const parent = node.parent;
    const selfNode = this.drop();
    let index = node.getIndex();
    parent.addChildAtIndex(selfNode, ++index);
    return this;
  },
  setIndex (index) {
    if (this.isRoot()) return this;
    if (index < 0 || index >= this.parent.children.length) {
      throw new Error('Invalid index.');
    }
    const oldIndex = this.parent.children.indexOf(this);
    this.parent.children.splice(index, 0, this.parent.children.splice(oldIndex, 1)[0]);
    return this;
  },
  getIndex () {
    if (this.isRoot()) return 0;
    return this.parent.children.indexOf(this);
  },
  drop () {
    if (!this.isRoot()) {
      if (!this.parent) return;
      const index = this.parent.children.indexOf(this);
      this.parent.children.splice(index, 1);
      this.parent = null;
    }
    return this;
  },
  path (end) {
    let start = this;
    const ancestors = leastCommonAncestor(start, end);
    const nodes = [start];
    while (start !== ancestors) {
      start = start.parent;
      nodes.push(start);
    }
    const k = nodes.length;
    while (end !== ancestors) {
      nodes.splice(k, 0, end);
      end = end.parent;
    }
    return nodes;
  },
  ancestors () {
    let node = this;
    const nodes = [node];
    while (node = node.parent) {
      nodes.push(node);
    }
    return nodes;
  },
  descendants () {
    return Array.from(this);
  },
  leaves () {
    const leaves = [];
    this.eachBefore(function (node) {
      if (!node.children || !node.children.length) {
        leaves.push(node);
      }
    });
    return leaves;
  },
  preSibling () {
    if (!this.parent) return;
    const index = this.getIndex();
    if (index <= 0) return;
    return this.parent.children[index - 1];
  },
  nextSibling () {
    if (!this.parent) return;
    const index = this.getIndex();
    if (index >= this.parent.children.length - 1) return;
    return this.parent.children[index + 1];
  },
  // 获取夹在两个节点中间的结点，不包括结点本身
  betweenNodes (node) {
    if (!(node instanceof Node) ||
      this.isRoot() ||
      node.isRoot() ||
      node.depth !== this.depth ||
      node === this) {
      return [];
    }
    const index1 = node.getIndex();
    const index2 = this.getIndex();
    const betweenNodes = this.parent.children.slice(Math.min(index1, index2), Math.max(index1, index2));
    betweenNodes.shift(); // 去除结点本身
    return betweenNodes;
  },
  // 同级结点交换位置
  switch (node) {
    if (!node || node.depth !== this.depth) return this;
    const index1 = node.getIndex();
    const index2 = this.getIndex();
    // let temp = this.parent.children[index1];
    // this.parent.children[index1] = this.parent.children[index2];
    // this.parent.children[index2] = temp;
    // 改成响应式交换
    const childrens = this.parent.children;
    childrens[index1] = childrens.splice(index2, 1, childrens[index1])[0];
    return this;
  },
  each (callback, that) {
    let index = -1;
    for (const node of this) {
      callback.call(that, node, ++index, this);
    }
    return this;
  },
  eachBefore (callback, that) {
    let node = this;
    const nodes = [node];
    let children;
    let index = -1;
    while (node = nodes.pop()) {
      callback.call(that, node, ++index, this);
      if (children = node.children) {
        for (let i = children.length - 1; i >= 0; --i) {
          nodes.push(children[i]);
        }
      }
    }
    return this;
  },
  eachAfter (callback, that) {
    let node = this;
    const nodes = [node];
    const next = [];
    let children;
    let index = -1;
    while (node = nodes.pop()) {
      next.push(node); // 存成：根->右->左
      if (children = node.children) {
        for (let i = 0, n = children.length; i < n; ++i) {
          nodes.push(children[i]);
        }
      }
    }

    while (node = next.pop()) { // 遍历：左 -> 右 -> 根
      callback.call(that, node, ++index, this);
    }

    return this;
  },
  find (callback, that) {
    let index = -1;
    for (const node of this) {
      if (callback.call(that, node, ++index, this)) {
        return node;
      }
    }
  },
  findAll (callback, that) {
    const result = [];
    let index = -1;
    for (const node of this) {
      if (callback.call(that, node, ++index, this)) {
        result.push(node);
      }
    }
    return result;
  },
  copy () {
    hierarchy(this, null, this.tree).eachBefore(n => {
      n.data = JSON.parse(JSON.stringify(n.data.data));
    });
  },
  toJson () {
    const childKey = this.tree.options.children;

    let node = this;
    const nodes = [node];
    const root = {};
    const outputs = [root];

    while (node = nodes.pop()) {
      const output = outputs.pop();
      const data = JSON.parse(JSON.stringify(node.data));
      delete data[childKey];
      Object.assign(output, data);

      let children, n;
      if ((children = node.children) && (n = children.length)) {
        output[childKey] = new Array(n);
        for (let i = n - 1; i >= 0; --i) {
          const child = {};
          output[childKey][i] = child; // 逆序推入，顺序pop
          outputs.push(child);
          nodes.push(children[i]);
        }
      }
    }

    return root;

    // 方法二：使用递归
    // const output = {};
    // (function traverse(node, output) {
    //   if (!node) return;
    //   const data = JSON.parse(JSON.stringify(node.data));
    //   delete data[childKey];
    //   Object.assign(output, data);
    //   let n;
    //   if (node.children && ( n = node.children.length )) {
    //     output[childKey] = [];
    //     for (let i = 0; i < n; ++i) {
    //       output[childKey].push({});
    //       traverse(node.children[i], output[childKey][i]);
    //     }
    //   }
    // })(this, output);
    // return output;
  },
  setLoad (loaded = true) {
    if (loaded) {
      this.loaded = true;
    } else {
      this.each(n => {
        n.loaded = false;
      });
    }
  },
  [Symbol.iterator]: function * () {
    let node = this;
    let current;
    let next = [node];
    let children;
    do {
      current = next.reverse(), next = [];
      while (node = current.pop()) {
        yield node;
        if (children = node.children) {
          for (let i = 0, n = children.length; i < n; ++i) {
            next.push(children[i]);
          }
        }
      }
    } while (next.length);
  }
}

export default Tree;
export { Tree, Node };