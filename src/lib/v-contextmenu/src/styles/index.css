@font-face {
  font-family: "v-contextmenu-iconfont";
  src: url("./fonts/iconfont.eot");
  src: url("./fonts/iconfont.eot#iefix") format("embedded-opentype"), url("./fonts/iconfont.woff") format("woff"), url("./fonts/iconfont.ttf") format("truetype"), url("./fonts/iconfont.svg") format("svg");
}
.v-contextmenu-iconfont {
  font-family: "v-contextmenu-iconfont" !important;
  font-size: inherit;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.v-contextmenu {
  position: absolute;
  padding: 5px 0;
  margin: 0;
  background-color: #1f2430;
  list-style: none;
  font-size: 12px;
  white-space: nowrap;
  cursor: pointer;
  z-index: 2800;
  -webkit-tap-highlight-color: transparent;
}
.v-contextmenu .v-contextmenu-item {
  padding: 6px 19px;
  line-height: 1;
  color: #fff;
}
.v-contextmenu .v-contextmenu-item.v-contextmenu-item--hover {
  color: #fff;
}
.v-contextmenu .v-contextmenu-item.v-contextmenu-item--disabled {
  color: #ccc;
  cursor: not-allowed;
}
.v-contextmenu .v-contextmenu-divider {
  height: 0;
  margin: 5px 0;
  border-bottom: 1px solid #e8e8e8;
}
.v-contextmenu .v-contextmenu-group__menus {
  padding: 0 5px;
  margin: 0;
  list-style: none;
}
.v-contextmenu .v-contextmenu-group__menus .v-contextmenu-item {
  display: inline-block;
  padding: 5px 9px;
}
.v-contextmenu .v-contextmenu-submenu {
  position: relative;
}
.v-contextmenu .v-contextmenu-submenu > .v-contextmenu {
  position: absolute;
}
.v-contextmenu .v-contextmenu-submenu > .v-contextmenu.left {
  left: 0;
  transform: translateX(-100%);
}
.v-contextmenu .v-contextmenu-submenu > .v-contextmenu.right {
  right: 0;
  transform: translateX(100%);
}
.v-contextmenu .v-contextmenu-submenu > .v-contextmenu.top {
  top: -6px;
}
.v-contextmenu .v-contextmenu-submenu > .v-contextmenu.bottom {
  bottom: -6px;
}
.v-contextmenu .v-contextmenu-submenu .v-contextmenu-submenu__title {
  margin-right: 10px;
}
.v-contextmenu .v-contextmenu-submenu .v-contextmenu-submenu__icon {
  position: absolute;
  right: 5px;
}
.v-contextmenu .v-contextmenu-submenu .v-contextmenu-submenu__icon::before {
  content: "\e622";
}
.v-contextmenu--default .v-contextmenu-item--hover {
  background-color: #46a0fc;
}
.v-contextmenu--bright .v-contextmenu-item--hover {
  background-color: #ef5350;
}
.v-contextmenu--dark .v-contextmenu-item--hover {
  background-color: #2d3035;
}
