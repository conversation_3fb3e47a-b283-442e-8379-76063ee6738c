.v-contextmenu
  position: absolute
  padding: padding-vertical 0
  margin: 0
  background-color: rgb(31 36 48)
  list-style: none
  font-size: 12px
  white-space: nowrap
  cursor: pointer
  z-index: 2800
  -webkit-tap-highlight-color: transparent

  .v-contextmenu-item
    padding: 6px 19px
    line-height: 1
    color: #fff

    &.v-contextmenu-item--hover
      color: #fff

    &.v-contextmenu-item--disabled
      color: #ccc
      cursor: not-allowed

  .v-contextmenu-divider
    height: 0
    margin: padding-vertical 0
    border-bottom: 1px solid border-color

  .v-contextmenu-group__menus
    padding: 0 5px
    margin: 0
    list-style: none

    .v-contextmenu-item
      display: inline-block
      padding: padding-vertical (contextmenu-item-padding-horizonal - 5)

  .v-contextmenu-submenu
    position: relative

    & > .v-contextmenu
      position: absolute

      &.left
        left: 0
        transform: translateX(-100%)

      &.right
        right: 0
        transform: translateX(100%)

      &.top
        top: -(padding-vertical) - 1

      &.bottom
        bottom: -(padding-vertical) - 1

    .v-contextmenu-submenu__title
      margin-right: padding-vertical * 2

    .v-contextmenu-submenu__icon
      position: absolute
      right: padding-vertical

      &::before
        content: "\e622"

.v-contextmenu--default
  .v-contextmenu-item--hover
    background-color: active-color

.v-contextmenu--bright
  .v-contextmenu-item--hover
    background-color: active-color-bright

.v-contextmenu--dark
  .v-contextmenu-item--hover
    background-color: active-color-dark
