/**
 * 回调管理器
 * [easyv 回调组件开发](https://dtstack.yuque.com/books/share/e4165ff9-8406-48a7-8036-f417d74aa091/biqsl3)
 * [回调的使用](https://www.yuque.com/zhouyanjiuyi/wdh5pn/eksx4b)
 */
export default class CallbackManager {
  constructor (state = {}) {
    this.state = { ...state };
    this._subs = {};
  }

  pick (keys = []) {
    return _.pick(this.state, keys);
  }

  addCallback<PERSON>ey (key, value) {
    const { state } = this;
    if (_.has(state, key)) {
      console.warn(`该回调参数[${key}]已经添加！`);
      return;
    }
    _.set(state, key, value);
  }

  removeCallbackKey (key, compId) {
    delete this.state[key];
    delete this._subs[key];
  }

  subscribeCallbackKey (compId, keys, callback = function () {}) {
    if (!compId || !keys || !keys.length) return;
    keys.forEach(key => {
      (this._subs[key] || (this._subs[key] = [])).push({
        id: compId,
        cb: callback
      });
    });
  }

  // 取消订阅回调参数
  unsubscribeCallbackKey (compId, keys) {
    if (!compId || !keys || !keys.length) return;
    keys.forEach(key => {
      this._subs[key] && this._subs[key].forEach((cbObj, idx) => {
        if (cbObj.id === compId) {
          this._subs[key].splice(idx, 1);
        }
      });
    });
  }

  updateCallbackValue (params = {}) {
    const { state } = this;
    const updateKeys = _.intersection(_.keys(state), _.keys(params));
    const callbackObjs = updateKeys.reduce((res, key) => {
      this._subs[key] && res.push(...this._subs[key]);
      return res;
    }, []);
    const updateData = _.pick(params, updateKeys);
    _.assign(this.state, updateData);
    const called = {};
    callbackObjs.forEach((cbObj, idx) => {
      if (!called[cbObj.id]) {
        cbObj.cb.call(null, updateData);
        // called[cbObj.id] = true;
      }
    });
  }
}
