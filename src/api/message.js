import http from '@/utils/http';

export function getMessage (params) {
  return http.post('/api/message', params);
}

export function updateMessage (data, params) {
  return http.post('/api/message/update', data, params);
}

export function deleteMessage (params) {
  return http.post('/api/message/delete', params);
}

// 协同消息确定
export function readMsg (params) {
  return http.post('/api/message/readMsg', params);
}
