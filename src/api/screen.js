import http from '@/utils/http';

// export function clearCache (params) {
//   return http.get('/api/preview/clearCache', params);
// }

export function updateCache (params) {
  return http.get('/api/preview/updateCache', params);
}

export function checkToken (params) {
  return http.get('/api/user/checkToken', params)
}

// 滚动分页获取分组大屏
export function paging (params) {
  return http.post('/api/screen/list', params)
}

export function getScreen (data) {
  // 是否是服务端渲染
  const isSsrRender = process.env.IS_SSR_RENDER === 'true'
  if (isSsrRender || (window.router && window.router.currentRoute.name === 'screen/share')) {
    return http.get('/api/preview/screen', data);
  }
  return http.get('/api/screen', data);
}

export function getScreens (data) {
  return http.get('/api/screen/findChild', data);
}

export function getScreenInfo (data) {
  return http.get('/api/screen/info', data);
}

export function getScreenComps (data, params) {
  return http.post('/api/screen/coms', data, params);
}

export function getPanelComs (data, params) {
  // 是否是服务端渲染
  const isSsrRender = process.env.IS_SSR_RENDER === 'true'
  if (isSsrRender || (window.router && window.router.currentRoute.name === 'screen/share')) {
    return http.post('/api/preview/getScreensComs', data, params)
  }
  return http.post('/api/screen/getScreensComs', data, params)
}

export function getScreenLayers (data) {
  return http.get('/api/screenlayer/layers', data);
}

export function createScreen (params) {
  // return http.post('/api/screen/create', data);
  if (!Array.isArray(params.screens)) {
    params.screens = [params.screens]
  }
  return http.post('/api/screen/create', params);
}

export function aiCreateScreen (params) {
  return http.post('/api/screenai/create', params);
}

export function deleteScreen (params) {
  // return http.post('/api/screen/delete', null, params);
  if (!Array.isArray(params.screens)) {
    params.screens = [params.screens || params.id]
    delete params.id
  }
  return http.post('/api/screen/delete', params);
}
// export function batchExport (params) {
//   return http.post('/api/screen/batchExport', params)
// }
export function updateScreen (data, params) {
  return http.post('/api/screen/update', data, params);
}

// 通过layerIds删除场景大屏页面LayerTree
export function deleteScreenLayerByIds (data, params) {
  return http.post('/api/screenlayer/deleteLayerByIds', data, params);
}

// 通过layerIds移动大屏页面LayerTree
export function moveScreenLayerByIds (data, params) {
  return http.post('/api/screenlayer/moveLayerByIds', data, params);
}

// 拖动插入大屏页面LayerTree
export function dragInsertScreenLayer (data, params) {
  return http.post('/api/screenlayer/dragInsertLayer', data, params);
}

// 插入大屏页面LayerTree
export function insertScreenLayers (data, params) {
  return http.post('/api/screenlayer/insertLayers', data, params);
}

// 更新大屏选中的LayerTree
export function updateScreenSelectedLayers (data, params) {
  return http.post('/api/screenlayer/updateSelectedLayers', data, params);
}

// 创建图层分组
export function createLayerGroups (data, params) {
  return http.post('/api/screenlayer/createLayerGroups', data, params);
}

// 取消图层分组
export function cancelLayerGroups (data, params) {
  return http.post('/api/screenlayer/cancelLayerGroups', data, params);
}

// 协同大屏移动
export function coeditScreenMove (data, params) {
  return http.post('/api/screencoedit/move', data, params);
}

export function getScreenShare (params) {
  return http.get('/api/screen/share', params);
}
// 获取仪表盘列表数据
export function getImportTree (params) {
  return http.post('/api/datadashboard/getProjectTree', null, params);
}
// 点击仪表盘调
export function getImportDashboard (data, params) {
  return http.post('/api/datadashboard/getDashboardInfo', data, params);
}
// 点击确定，返回组件的类型和数据
export function getImportChartData (data, params) {
  return http.post('/api/datadashboard/getChartData', data, params);
}
// 点击确定，返回组件的类型和数据
export function getCheckChartData (data, params) {
  return http.post('/api/datadashboard/checkChartData', data, params);
}

export function updateScreenShare (data) {
  return http.post('/api/screen/share/update', data);
}

export function screenShareVerify (data) {
  return http.post('/api/screen/share/verify', data);
}

export function recordScreen (data, params) {
  return http.post('/api/screenshot/create', data, params);
}

export function importScreen (data, params, fn) {
  return http.post('/api/screen/import', data, params, fn);
}

export function screenCopy (data) {
  return http.post('/api/screen/copy', data);
}

export function addScene (data, params) {
  return http.post('/api/screen/addScene', data, params);
}

export function addPage (data, params) {
  return http.post('/api/screen/addPage', data, params);
}

export function deleteScene (data, params) {
  return http.post('/api/screen/deleteScene', data, params);
}

export function deletePage (data, params) {
  return http.post('/api/screen/deletePage', data, params);
}

export function updateName (data, params) {
  return http.post('/api/screen/updateName', data, params);
}

export function screensocket (params) {
  return http.get('/api/screensocket', params);
}

export function updateScreensocket (data, params) {
  return http.post('/api/screensocket/update', data, params);
}

export function screensocketVerify (data, params) {
  return http.post('/api/screensocket/verify', data, params);
}

export function screenShare (data, params) {
  return http.post('/api/usershare/create', data, params);
}

export function moveShareScreen (data, params) {
  return http.post('/api/screen/move', data, params);
}

export function updateShareScreen (data, params) {
  return http.post('/api/usershare/update', data, params);
}

export function orderPage (data, params) {
  return http.post('/api/screen/orderPage', data, params);
}

export function orderScene (data, params) {
  return http.post('/api/screen/orderScene', data, params);
}
export function importTpl (data, params) {
  return http.post('/apTpl', data, params);
}

export function voicecontrol (data, params) {
  return http.post('/api/voicecontrol', data, params)
}

export function createInitControl (params) {
  return http.get('/api/voicecontrol/createInitControl', params)
}

export function voiceConfig (params) {
  return http.get('/api/voicecontrol/comList', params)
}

export function findVoiceConfig (params) {
  return http.get('/api/voicecontrol/find', params)
}

export function updateVoiceConfig (data, params) {
  return http.post('/api/voicecontrol/update', data, params)
}

export function getVoiceconfig (data) {
  return http.get('/api/voicecontrol/voiceconfig', data)
}

// 更新面板parentId，不用检查该用户是否是大屏所有者
export function updateScreenParentId (data, params) {
  return http.post('/api/screen/updateParentId', data, params);
}

export function getVariables (params) {
  return http.get('/api/screen/variableList', params);
}

export function delVariables (params) {
  return http.get('/api/screen/deletevariableList', params);
}

export function addVariable (data, params) {
  return http.post('/api/screen/addvariableList', data, params);
}

export function updateVariable (data, params) {
  return http.post('/api/screen/updatevariableList', data, params);
}

export function getscreenlinkAge (params) {
  return http.get('/api/screen/getscreenlinkAge', params);
}
