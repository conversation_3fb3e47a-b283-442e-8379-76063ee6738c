import http from '@/utils/http';

export function createCustom (data) {
  return http.post('/api/customcomponent/create', data);
}

export function infoCustom (params) {
  return http.get('/api/customcomponent/info', params);
}

export function updateCustom (data, params) {
  return http.post('/api/customcomponent/update', data, params);
}

export function publishCustom (data) {
  return http.post('/api/customcomponent/publish', data);
}

export function createCustomProject (data) {
  return http.post('/api/customcomproject/create', data);
}

export function getCustomProject () {
  return http.get('/api/customcomproject');
}

export function updateCustomProject (data) {
  return http.post('/api/customcomproject/update', data)
}

export function getCustom () {
  return http.get('/api/customcomponent');
}

// export function infoCustomProject () {
//   return http.get('/api/customcomproject/info');
// }

export function deleteCustom (data) {
  return http.post('/api/customcomponent/delete', data)
}

export function deleteCustomProject (data) {
  return http.post('/api/customcomproject/delete', data)
}

export function createCustomScreenshot (data) {
  return http.post('/api/customcomponent/createComScreenshot', data)
}
