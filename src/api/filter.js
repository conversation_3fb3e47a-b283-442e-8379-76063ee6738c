import http from '@/utils/http';

export function getFilters (params) {
  return http.get('/api/filter', params);
}

export function createFilter (data, params) {
  return http.post('/api/filter/create', data, params);
}

export function updateFilter (data, params) {
  return http.post('/api/filter/update', data, params);
}

export function getFilterInfo (params) {
  return http.get('/api/filter/info', params);
}

export function deleteFilter (data, params) {
  return http.post('/api/filter/delete', data, params);
}

export function orderFilter (data, params) {
  return http.post('/api/filter/order', data, params);
}

export function createScreenFilter (data, params) {
  return http.post('/api/filter/createFilter', data, params);
}

export function deleteScreenFilter (data, params) {
  return http.post('/api/filter/deleteFilter', data, params);
}

export function systemfilter (params) {
  return http.get('/api/systemfilter', params);
}

export function createSystemfilter (data, params) {
  return http.post('/api/systemfilter/create', data, params);
}

export function deleteSystemfilter (data, params) {
  return http.post('/api/systemfilter/delete', data, params);
}

export function updateSystemfilter (data, params) {
  return http.post('/api/systemfilter/update', data, params);
}
