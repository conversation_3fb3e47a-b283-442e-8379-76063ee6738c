import http from '@/utils/http';
import { tableToList } from '@/utils/base'

export function datastorageList (params) {
  return http.get('/api/datastorage/list', params);
}

export function createDatastorage (data, params) {
  return http.post('/api/datastorage/create', data, params);
}

export function updateDatastorage (data, params) {
  return http.post('/api/datastorage/update', data, params);
}

export function getDbList (params) {
  return http.get('/api/datastorage/getDbList', params);
}

export function deleteStorage (data, params) {
  return http.post('/api/datastorage/delete', data, params);
}

export function getData (params) {
  return http.get('/api/datastorage/getData', params).then((res = {}) => {
    // 将表格数据转json
    res.data = tableToList(res.data)
    return res
  })
}

export function postData (params, data) {
  return http.post('/api/datastorage/getData', data, params).then((res) => {
    if (Array.isArray(res)) {
      res.forEach((item, index) => {
        // 将表格数据转json
        res[index].data = tableToList(res[index].data)
      })
    } else {
      // 将表格数据转json
      res.data = tableToList(res.data)
    }
    return res
  })
}

export function getTree (data, params) {
  return http.post('/api/datastorage/getTree', data, params);
}

export function getTbList (data, params) {
  return http.post('/api/datastorage/getTbList', data, params);
}

export function getFieldList (data, params) {
  return http.post('/api/datastorage/getFieldList', data, params);
}

export function searchTb (data, params) {
  return http.post('/api/datastorage/searchTb', data, params);
}

export function getFunctionList (params) {
  return http.get('/api/datastorage/getFunctionList', params);
}

export function previewData (params) {
  return http.get('/api/datastorage/preview', params);
}

export function getProjectTree (data, params) {
  return http.post('/api/datastorage/getProjectTree', data, params);
}
