/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-11-14 11:22:06
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-11-15 20:01:34
 * @FilePath: /seatom/src/api/screencoedit.js
 * @Description: 大屏协同编辑相关接口
 */
import http from '@/utils/http';

// 新建共享
export function saveCoeditScreen (data, params) {
  return http.post('/api/screencoedit/save', data, params);
}

// 共享给我的
export function sharedToMe (params) {
  return http.get('/api/screencoedit/sharedToMe', params);
}
