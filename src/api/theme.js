import http from '@/utils/http';

export function createTheme (data, params) {
  return http.post('/api/comtheme/create', data, params);
}

export function themeList (params) {
  return http.get('/api/comtheme', params);
}

export function getlabelList (params) {
  return http.get('/api/component/tags', params);
}

export function createLabel (params) {
  return http.post('/api/component/tags/create', params);
}

export function deleteLabelContent (params) {
  return http.post('/api/component/tags/delete', {}, params)
}

export function update (params) {
  return http.post('/api/component/tags/update', params)
}

export function serchList (params) {
  return http.post('/api/component/tags/list', params)
}

export function themeInfo (data, params) {
  return http.post('/api/comtheme/info', data, params);
}

export function updateTheme (data, params) {
  return http.post('/api/comtheme/update', data, params);
}

export function deleteTheme (data, params) {
  return http.post('/api/comtheme/delete', data, params);
}

export function saveTheme (data, params) {
  return http.post('/api/comtheme/save', data, params);
}

export function createThemeScheme (data, params) {
  return http.post('/api/themescheme/create', data, params);
}

export function updateThemeScheme (data, params) {
  return http.post('/api/themescheme/update', data, params);
}

export function deleteThemeScheme (data, params) {
  return http.post('/api/themescheme/delete', data, params);
}

export function themeScheme (params) {
  return http.get('/api/themescheme', params);
}

export function getComtheme (data) {
  return http.post('/api/themescheme/getComtheme', data);
}

export function saveComtheme (data) {
  return http.post('/api/comtheme/saveComtheme', data);
}
