import http from '@/utils/http';

export function commonUpload (data, params) {
  return http.post('/api/common/upload', data, params);
}
export function packcom (data, params) {
  return http.post('/api/packcom/pack', data, params);
}
export function packlist (data, params) {
  return http.post('/api/packcom/index', data, params);
}
export function sendCustomReq (data, params) {
  return http.post('/api/common/customApiRequest', data, params);
}
export function authPermission (data, params) {
  return http.post('/api/common/dataPermission', data, params)
}
export function uploadScreenIcon (data, params) {
  return http.post('/api/resource/uploadScreenIcon', data, params)
}
