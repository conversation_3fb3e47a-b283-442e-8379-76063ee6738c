/**
 * <AUTHOR>
 * @description 教程相关API接口
 */

import http from '@/utils/http'

/**
 * @description        上传资源接口(富文本编辑器图片、视频等)
 * @param param
 * @return {Promise | Promise<unknown>}
 */
export function tutorialUpload (param) {
  return http.post('/api/guide/upload', param)
}

/**
 * @description         删除资源接口
 * @param param.name          type:string 必填
 * @return {Promise | Promise<unknown>}
 */
export function tutorialDeleteFile (param) {
  return http.post('/api/guide/deleteFile', param)
}

/**
 * @description         获取封面数据
 * @return {Promise | Promise<unknown>}
 */
export function tutorialGetCover () {
  return http.get('/api/guide/getFrontCover')
}

/**
 * @description         获取文件夹列表
 * @param param.folderId      type:number 必填
 * @return {Promise | Promise<unknown>}
 */
export function tutorialGetFolderList (param = { folderId: 1 }) {
  return http.post('/api/guide/', param)
}

/**
 * @description        创建文件夹
 * @param param.name         type:string 必填
 * @param param.parentID     type:number 必填
 * @return {Promise | Promise<unknown>}
 */
export function tutorialCreatFolder (param) {
  return http.post('/api/guide/createContent', param)
}

/**
 * @description        删除文件夹
 * @param param.id           type:number 必填
 * @return {Promise | Promise<unknown>}
 */
export function tutorialDeleteFolder (param) {
  return http.post('/api/guide/deleteContent', param)
}

/**
 * @description                 重命名文件夹
 * @param param.id              type:number 必填
 * @param param.folderName      type:string 必填
 * @return {Promise | Promise<unknown>}
 */
export function tutorialRenameFolder (param) {
  return http.post('/api/guide/rename', param)
}

/**
 * @description                   获取标签列表
 * @param param.id                type:number 必填 文件夹id，不传表示根目录
 * @return {Promise | Promise<unknown>}
 */
export function tutorialGetMarks (param) {
  return http.post('/api/guide/getMarks', param)
}

/**
 * @description 创建文档
 * @param param.title             文档标题
 * @param param.videoPath         视频地址
 * @param param.coverStyle        封面样式标识         type:number
 * @param param.mark              标签               type:string[]
 * @param param.currentFolderID   文件夹id           type:number
 * @param param.content           文档内容
 * @return {Promise | Promise<unknown>}
 */
export function tutorialGenDoc (param) {
  return http.post('/api/guide/createDoc', param)
}

/**
 * @description 获取文档列表
 * @param param.folderId type:number  文件夹id 根目录为数字1
 * @param param.title
 * @param param.marks
 * @return {Promise | Promise<unknown>}
 */
export function tutorialGetDocList (param) {
  return http.post('/api/guide/getDocList', param)
}

/**
 * @description 更新文章数据
 * @param
 * @param param.title             文档标题
 * @param param.videoPath         视频地址
 * @param param.coverStyle        封面样式标识         type:number
 * @param param.mark              标签               type:string[]
 * @param param.currentFolderID   文件夹id           type:number
 * @param param.content           文档内容
 * @return {Promise | Promise<unknown>}
 */
export function tutorialUpdateDoc (param) {
  return http.post('/api/guide/updateDoc', param)
}

/**
 * @description 删除文章
 * @param param.ids  type:string[] 必填 文章id数组
 * @return {Promise | Promise<unknown>}
 */
export function tutorialDeleteDoc (param) {
  return http.post('/api/guide/deleteDoc', param)
}

/**
 * @description 通过id获取文章详情
 * @param.id  type:string[] 必填 文章id数组
 * @return {Promise | Promise<unknown>}
 */
export function tutorialGetDoc (param) {
  return http.get('/api/guide/getDocById', param)
}

/**
 * @description 通过id获取文章详情
 * @param.id  type:string[] 必填 文章id数组
 * @return {Promise | Promise<unknown>}
 */
export function tutorialSortFolder (param) {
  return http.post('/api/guide/changeContent', param)
}

/**
 * @description 通过id获取文章详情
 * @param.id  type:string[] 必填 文章id数组
 * @return {Promise | Promise<unknown>}
 */
export function tutorialSaveDraft (param) {
  return http.post('/api/guide/saveDraft', param)
}

/**
 * @description 通过id获取文章详情
 * @param.id  type:string[] 必填 文章id数组
 * @return {Promise | Promise<unknown>}
 */
export function tutorialGetDraft (param) {
  return http.post('/api/guide/getDraft', param)
}
