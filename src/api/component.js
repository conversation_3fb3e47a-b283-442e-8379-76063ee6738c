import http from '@/utils/http'

export function createComponent (data, params) {
  return http.post('/api/component/create', data, params)
}

export function createChildComponent (data, params) {
  return http.post('/api/component/createChild', data, params)
}

export function updateComponent (data, params) {
  return http.post('/api/component/update', data, params)
}

export function deleteComponent (data, params) {
  return http.post('/api/component/delete', data, params)
}

export function deleteChildComponent (data, params) {
  return http.post('/api/component/deleteChild', data, params)
}

export function copyComponent (data, params) {
  return http.post('/api/component/copy', data, params)
}

export function copyChildComponent (data, params) {
  return http.post('/api/component/copyChild', data, params)
}

export function upgradeComponent (data, params) {
  return http.post('/api/component/upgrade', data, params)
}

export function getComponentInfo (data, params) {
  return http.post('/api/component/cominfo', data, params)
}

export function clearComs (params) {
  return http.get('/api/screen/clearConfig', params);
}

export function getCompCfg (params) {
  return http.get('/api/indicatorcard/com', params);
}
