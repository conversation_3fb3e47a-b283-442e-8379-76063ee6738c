/*
 * @Author: your name
 * @Date: 2022-01-09 15:36:04
 * @LastEditTime: 2023-01-16 11:01:38
 * @LastEditors: chenxingyu
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /seatom/src/api/workspace.js
 */
import http from '@/utils/http';

export function getProject (param) {
  return http.get('/api/project', param);
}

export function createProject (data) {
  return http.post('/api/project/create', data);
}

export function updateProject (data) {
  return http.post('/api/project/update', data);
}

export function delProject (data) {
  return http.post('/api/project/delete', data);
}

export function getScreenTpl (data) {
  return http.get('/api/screentpl', data);
}

// 获取字体列表
export function getFontList (param) {
  return http.get('/api/font', param);
}

// 修改字体信息
export function updateFont (params, data) {
  return http.post('/api/font/update', params, data);
}

// 删除字体
export function deleteFont (param) {
  return http.post('/api/font/delete', param);
}
// 获取工作空间
export function getWorkspace (param) {
  return http.get('/api/workspace', param);
}
// 获取资源列表 资源类型 enum: ['video', 'audio', 'picture', 'json'],
export function getResourceList (param) {
  return http.get('/api/resource', param);
}
// 上传资源文件
export function uploadResource (param) {
  return http.post('/api/resource/upload', param);
}
// 截图上传
export function screenShot (param) {
  return http.post('/api/common/uploadfileList', param)
}

// 上传资源时新建文件夹
export function ResourceCreateFolder (param) {
  return http.post('/api/resource/createfolder', param);
}

// 删除资源文件
export function delResourceFile (param) {
  return http.post('/api/resource/delete', param);
}
// 移动文件
export function moveResourcdFile (param) {
  return http.post('/api/resource/move', param);
}

// 删除资源文件夹
export function delResourceFolder (param) {
  return http.post('/api/resource/deletefolder', param);
}

// 修改资源文件名称 图片与视频
export function editResourceFileName (param, data) {
  return http.post('/api/resource/updateFileName', param, data);
}

// 获取同步大屏
export function getShareScreen (param) {
  return http.post('/api/usershare', param);
}

// 获取同步大屏列表
export function getUsershareList (param) {
  return http.get('/api/usershare/list', param);
}

// 通过ShareIds获取同步大屏列表
export function getUsershareListByShareIds (param) {
  return http.post('/api/usershare/getListByShareIds', param);
}

// 修改资源文件夹名称 图片与视频
export function editResourceFolderName (param, data) {
  return http.post('/api/resource/updatefolder', param, data);
}

// 获取嵌入产品链接列表
export function getProductList (param) {
  return http.get('/api/user/getProductList', param);
}

// 上传授权文件
export function uploadLicence (param, data, fn) {
  return http.post('/api/common/uploadLicence', param, data, fn);
}

// 获取授权码
export function getLicenseCode (param) {
  return http.get('/api/common/getLicenceCode', param);
}

// 创建指标库
export function createIndicator (param, data) {
  return http.post('/api/indicatorcard/create', param, data)
}

// 获取指标库列表
export function getIndicators (param) {
  return http.get('/api/indicatorcard/index', param)
}

// 删除指标
export function deleteIndicator (param) {
  return http.post('/api/indicatorcard/delete', param)
}

// 更新指标库属性
export function updateIndicator (param, data) {
  return http.post('/api/indicatorcard/update', param, data)
}

// 获取dmc用户信息
export function getDmcUserInfo (param) {
  return http.get('/api/user/getDmcUserInfo', param)
}

// 跳转用户中心
export function jumpUcenter (param) {
  return http.get('/api/user/genRedirecUrl', param)
}
