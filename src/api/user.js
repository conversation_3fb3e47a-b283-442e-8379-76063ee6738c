import http from '@/utils/http'

export function localLogin (data) {
  return http.post('/api/user/localLogin', data)
}

export function dmcLogin (data) {
  return http.post('/api/user/dmcLogin', data)
}

export function getShareUser () {
  return http.get('/api/usershare/userlist')
}

export function getUserId (data) {
  return http.post('/api/user/getUserId', data)
}

export function getAuthorization () {
  return http.get('/api/user/getAuthorizationDays')
}

export function dmcUcenter () {
  return http.get('/api/user/dmcUcenter')
}

export function screenConfig () {
  return http.get('/api/user/screenConfig')
}

export function syncUser () {
  return http.get('/api/user/synchronizeDmcUsers')
}

export function getRoleList () {
  return http.get('/api/usershare/rolelist')
}

export function getGroupList () {
  return http.get('/api/usershare/grouplist')
}

export function getGroupTree (params) {
  return http.get('/api/usershare/getDmcGroupSublistList', params)
}

export function getUserbehaviorLog (data) {
  return http.post('/api/userbehavior/log', data)
}

export function getUserGroupCategory () {
  return http.get('/api/user/group/category')
}

export function getUserList (params) {
  return http.get('/api/group/user/list', params)
}

export function getRoleType (params) {
  return http.get('/api/user/role/list', params)
}
