:root {
  --control-background-color: #181b24;
  --control-border-color: #393b4a;
  --control-text-color: #bfbfbf;
  --control-text-primary-color: #a1aeb3;
  --control-checked-background-color: #2483ff;
}

/* 普通状态样式覆写 start */
.el-input__inner,
.el-textarea__inner,
.el-input-number__decrease,
.el-input-number__increase,
.el-radio-button__inner,
.el-color-picker__trigger,
.el-radio__inner,
.el-tabs__item {
  background-color: rgba(204, 219, 255, 0.06);
  border-color: var(--control-border-color);
}

.el-checkbox__inner {
  background-color: transparent;
  border-color: var(--seatom-type-700);
}

.config-control .el-collapse .el-collapse-item__header,
.el-collapse-item__wrap,
.el-tabs--card>.el-tabs__header .el-tabs__item,
.el-tabs--card>.el-tabs__header .el-tabs__nav {
  background-color: #191c21;
  border-color: var(--control-border-color);
}

.el-input__inner,
.el-textarea__inner,
.config-control .config-title,
[class*=" el-icon-"],
[class^="el-icon-"],
.el-radio-button__inner,
.config-control .el-collapse .el-collapse-item__header,
.el-tabs__item {
  color: var(--control-text-color);
}

.el-collapse-item__wrap {
  border: none;
}

.el-slider__runway {
  background-color: var(--control-border-color);
}

.el-input-number.is-controls-right .el-input-number__decrease,
.el-input-number.is-controls-right .el-input-number__increase,
.el-radio-button:first-child .el-radio-button__inner,
.el-collapse {
  border-color: var(--control-border-color);
}

.el-input__inner {
  border-radius: 2px;
}

.el-input.is-disabled .el-input__inner {
  color: #a9adb6 !important;
}

.el-collapse-item__header {
  height: 30px;
  line-height: 30px;
  flex-direction: row-reverse;
  -webkit-flex-direction: row-reverse;
  justify-content: flex-end;
}

.el-collapse-item__header .el-collapse-item__arrow {
  margin: 0 8px 0 0;
}

.el-input-group__append {
  background-color: rgba(204, 219, 255, 0.06);
  border-color: var(--control-border-color);
}

/* 普通状态样式覆写 end */

/* 选中、active样式覆写 start */
.el-slider__button {
  width: 12px;
  height: 12px;
  border-color: var(--control-active-background-color);
}

.el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  border-bottom-color: var(--control-active-background-color);
}

.el-radio-button__orig-radio:checked+.el-radio-button__inner,
.el-switch.is-checked .el-switch__core,
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner,
.el-radio__input.is-checked .el-radio__inner {
  background-color: var(--control-checked-background-color);
  border-color: var(--control-checked-background-color);
}

.el-tabs__item.is-active {
  color: var(--control-checked-background-color);
  box-shadow: none !important;
}

/* 选中、active样式覆写 end */

/* 弹窗样式覆写 start */
.el-dialog {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: #1f2430;
  box-shadow: 0px 48px 128px -16px rgba(4, 8, 16, 0.64),
    0px 16px 64px -16px rgba(4, 8, 16, 0.72), 0px 0px 1px rgba(4, 8, 16, 0.32);
  border-radius: 8px;
}

.el-dialog .el-dialog__title {
  color: #fff;
  font-size: 16px;
}

.el-dialog .el-dialog__headerbtn .el-dialog__close {
  font-size: 18px;
  color: #fafafa;
  transition: transform 0.3s linear;
}

.el-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  transform: rotate(180deg);
}

.el-dialog .el-dialog__body {
  padding: 15px 20px 10px 20px;
}

.el-dialog .el-dialog__footer {
  padding: 10px 20px 12px;
}

/* 弹窗样式覆写 end */

/* message-box 样式覆盖 start */
.el-message-box {
  background-color: #303640;
  border: none;
  position: relative;
}

.message-warning {
  height: 88px;
  width: 88px;
  background: url('../assets/img/warning-icon.png') no-repeat 50% 50% / cover;
}

.el-message-box__header .el-message-box__title {
  color: #fff;
}

.el-message-box__container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.el-message-box__container .el-message-box__status {
  position: static;
  font-size: 46px !important;
  color: red;
  transform: none;
}

.el-message-box__container .el-message-box__message {
  color: #fff;
  text-align: center;
  padding: 20px;
}

/* message-box 样式覆盖 end */

/* message提示框样式覆盖 start */
.el-message {
  min-width: 320px;
  min-height: 42px;
  line-height: 40px;
  border: 1px solid var(--seatom-main-color);
  border-radius: 0;
  background: #1B3964;
}

.el-message .el-message__icon {
  font-size: 14px;
}

.el-message .el-message__content {
  font-size: 12px;
  color: #fff;
}

/* message提示框样式覆盖 end */

/* 按钮样式覆盖 start */
.el-button--light-blue {
  color: #3d85ff;
  background: rgba(61, 133, 255, 0.15);
  border-radius: 8px;
  border: none;
}

.el-button--plain {
  color: #3d85ff;
  background: unset;
  border-radius: 0;
  border-color: #3d85ff;
}

.el-button--light-blue:hover,
.el-button--plain:hover {
  border-color: unset;
  background-color: rgba(61, 133, 255, 0.4);
}

.el-button--plain i {
  color: #3d85ff;
}

/* 按钮样式覆盖 end */

/* select下拉弹层覆盖 start */
.el-select-dropdown {
  border: 1px solid #3a4659;
  background-color: #1f2430;
}

.el-popper[x-placement^="bottom"] .popper__arrow {
  border-bottom-color: #3a4659;
}

.el-popper[x-placement^="bottom"] .popper__arrow::after {
  border-bottom-color: #202530;
}

.el-popper[x-placement^=top] .popper__arrow {
  border-top-color: #3a4659;
}

.el-popper[x-placement^=top] .popper__arrow::after {
  border-top-color: #202530;
}

.el-select-dropdown__item {
  color: rgba(255, 255, 255, 0.7);
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: rgba(61, 133, 255, 0.15);
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  background-color: rgba(61, 133, 255, 0.15);
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
  background-color: rgba(61, 133, 255, 0.35);
}

.el-select .el-tag.el-tag--info {
  height: 21px;
  color: #409eff;
  padding-right: 10px;
  background-color: rgba(244, 244, 245, 0);
  border-color: #409eff;
}

.el-select .el-tag.el-tag--info .el-tag__close {
  color: #ffffff;
}

.el-select .el-tag__close.el-icon-close {
  background-color: #419eff;
}

.el-input.is-disabled .el-input__inner {
  background-color: #65666791;
  border-color: #575757;
  color: #6969699e;
  cursor: not-allowed;
}

.el-select-dropdown__item.is-disabled {
  color: #606266;
}

.el-select-dropdown__item.is-disabled:hover {
  background-color: #fff0;
}

/* select下拉弹层覆盖 end */

/* cascader 级联选择器下拉弹层覆盖 start */
.el-cascader-node {
  color: #bfbfbf;
}

.el-cascader__dropdown {
  border: 1px solid #3a4659;
  background-color: #1f2430;
}

.el-cascader-menu {
  border-right: 1px solid #3a4659;
}

.el-cascader-menu__wrap {
  height: 350px;
  width: 204px;
}

.el-cascader__dropdown__item {
  color: rgba(255, 255, 255, 0.7);
}

.el-cascader-node:not(.is-disabled):focus,
.el-cascader-node:not(.is-disabled):hover {
  background-color: rgba(61, 133, 255, 0.15);
}

.el-cascader__dropdown.is-multiple .el-cascader__dropdown__item.selected {
  background-color: rgba(61, 133, 255, 0.15);
}

.el-cascader__dropdown.is-multiple .el-cascader__dropdown__item.selected.hover {
  background-color: rgba(61, 133, 255, 0.35);
}

.el-cascader-node.is-disabled {
  color: #606266;
}

/* cascader 级联选择器下拉弹层覆盖 end */

/* 内置过滤器下拉面板样式重置 */
.filter-cascader .el-cascader-menu:nth-of-type(1) {
  .el-cascader-menu__list .el-cascader-node:last-child {
    margin-top: 10px;
    border-top: 1px solid #3a4659;
  }
}

.filter-cascader .el-cascader-menu:nth-of-type(1) .el-cascader-menu__wrap {
  width: 100%;
}

.filter-cascader .el-cascader-menu:nth-of-type(2) .el-cascader-menu__wrap {
  width: 240px;
}

.filter-cascader .el-cascader-menu:nth-of-type(2) .el-cascader-menu__wrap .el-cascader-node {
  padding: 0 !important;
}

.filter-cascader .el-cascader-menu:nth-of-type(2) .el-cascader-node .filter-txt {
  display: inline-block;
  width: 185px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  padding-right: 5px;
}

/* .el-tree覆盖 start */
.el-tree,
.filter-tree {
  background-color: #1f2430;
  max-height: 400px;
  overflow-y: auto;
}

.el-tree-node__label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 1px;
}

.el-tree-node__content {
  height: 36px;
}

.el-tree .el-tree-node__content:hover {
  background: rgba(61, 133, 255, 0.1);
}

.el-tree-node:focus>.el-tree-node__content {
  background: rgba(61, 133, 255, 0.1);
}

.el-tree-node__expand-icon,
.el-icon-caret-right {
  display: none;
}

/* .el-tree下拉弹层覆盖 end */

/* 下拉菜单覆盖 start */
.el-dropdown-menu {
  max-height: 80%;
  border: 1px solid #3a4659;
  background-color: #1f2430;
  overflow: auto;
}

.el-dropdown-menu::-webkit-scrollbar {
  width: 2px;
}

.el-dropdown-menu__item {
  color: #fff;
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: rgba(61, 133, 255, 0.15);
}

/* 下拉菜单覆盖 end */

/* 提示覆盖 start */
.el-tooltip__popper .field-content {
  width: 200px;
  line-height: 18px;
}

.el-tooltip__popper .field-content .title {
  font-weight: bold;
  color: #2483ff;
}

.atooltip {
  background: #FFFFFF;
  font-size: 12px !important;
  padding: 4px !important;
}

.icon-tooltip {
  background-color: var(--seatom-background-500) !important;
  color: var(--seatom-type-900) !important;
}

/* 提示覆盖 end */


.el-table::before {
  display: none;
}

.el-table thead {
  color: #fafafa;
}

.el-table th.el-table__cell {
  background-color: #2e343c !important;
}

.el-table th,
.el-table tr,
.el-table,
.el-table__expanded-cell {
  background-color: #22242b !important;
}

.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  color: #bfbfbf;
  border-bottom: 1px solid var(--control-border-color);
}

.el-table--enable-row-hover .el-table__body tr:hover>td {
  background-color: #40434c !important;
}

.el-cascader-panel .el-radio {
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 10px;
  right: 10px;
}

.el-cascader-panel .el-radio__input {
  visibility: hidden;
}

.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}


/* poper 主题切换 start */
.poper-theme {
  background: var(--seatom-background-300);
  box-shadow: var(--seatom-container-c300);
  border: none;
}

/* poper 列表内样式 */
.poper-theme .list-item {
  color: var(--seatom-type-800) !important;
}

.poper-theme .el-select-dropdown__item {
  color: var(--seatom-type-900);
}

.poper-theme .el-select-dropdown__item.selected {
  color: var(--seatom-primary-900);
}

.poper-theme .el-cascader-node {
  color: var(--seatom-type-900);
}

.poper-theme .el-cascader-node.is-active {
  color: var(--seatom-primary-900);
}

.poper-theme .el-cascader-menu {
  border-right: solid 1px #E4E7ED;
}

.poper-theme .form-theme {
  color: var(--seatom-type-700) !important;
  font-weight: 600;
  font-size: 14px !important;
}

.poper-theme .popper__arrow {
  display: none;
}

.poper-theme .el-message-box__title,
.poper-theme .el-dialog__title {
  font-weight: 600;
  font-size: 16px;
  color: var(--seatom-type-900) !important;
}

.poper-theme .el-dialog__close {
  color: var(--seatom-type-700) !important;
}

.poper-theme .el-button--primary {
  color: #3d85ff;
  background: rgba(61, 133, 255, 0.15);
  border-radius: 8px;
  border: none;
  font-size: 14px;
}

.poper-theme .poper-cancel {
  color: #409EFF;
  background: 0 0;
  padding-left: 0;
  padding-right: 0;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
  border: none;
}

.poper-theme .poper-cancel:hover {
  color: #66b1ff;
  border-color: transparent;
  background-color: transparent;
}

.poper-theme .el-button--primary:hover {
  border-color: unset;
  background-color: rgba(61, 133, 255, 0.4);
}

.poper-theme .el-form .el-form-item__label {
  color: var(--seatom-type-700) !important;
  font-weight: 600;
  font-size: 14px !important;
}

.poper-theme .el-message-box__message,
.poper-theme .el-dialog__body {
  color: var(--seatom-type-900);
}

/* select下拉弹层覆盖 主题 end */

/* input 主题切换 start */
.input-theme {
  background: var(--seatom-mono-a100);
  border-radius: 4px 4px 0px 0px;
  color: #fff;
  border: none;
  border-bottom: 1px solid var(--seatom-mono-a300);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  transition: .2s;
  margin-right: 16px;
}

.input-theme .el-input__inner {
  border: none;
  color: var(--seatom-type-800);
}

.input-theme .el-textarea__inner {
  background: var(--seatom-mono-a100) !important;
  border: none;
  color: var(--seatom-type-800);
}

/* input 主题切换 end */

/* tab 主题切换 start */
.tab-theme .el-tabs__item {
  color: var(--seatom-type-800) !important;
  background: none !important;
  font-weight: 600;
  font-size: 16px;
}

.tab-theme .el-tabs__nav-wrap::after {
  display: block !important;
  height: 1px;
  background-color: #e5e7ea;
}

.tab-theme .el-tabs__active-bar {
  display: block !important;
  background: #1F71FF;
  height: 3px;
  border-radius: 3px 3px 0px 0px;
}

.tab-theme .el-tabs__item.is-active {
  border: none !important;
  color: var(--seatom-primary-900) !important;
}

/* tab 主题切换 end */

/* upload 主题切换 start */
.upload-theme .el-upload-dragger {
  background: var(--seatom-mono-a100) !important;
  border: none !important;
  border-radius: 8px !important;
}

/* upload 主题切换 end */

/* popover start */
.el-popover {
  min-width: 80px;
}

.more-action-popover {
  background-color: var(--seatom-background-500);
  color: var(--seatom-type-900);
  border: none;
  padding: 8px;
  border-radius: 8px;
}

/* popover end */

/* Switch start */
.el-switch {
  width: 30px;
  height: 12px;
}

.el-switch__core {
  width: 30px;
  height: 12px;
  background-color: rgba(204, 219, 255, 0.32);
  ;
  border: unset;
}

.el-switch.is-checked .el-switch__core {
  background-color: rgba(61, 133, 255, 0.4);
}

.el-switch__core:after {
  height: 16px;
  width: 16px;
  top: -2px;
  background: rgba(230, 235, 255, 0.9);
}

.el-switch.is-checked .el-switch__core::after {
  margin-left: -16px;
  background: #5C98FF;
}

/* Switch end */

.el-picker-panel {
  background: #1F2430;
  border-radius: 8px;
  color: #FFF;
  border-color: transparent;
  box-shadow: 0px 0px 1px 0px rgba(4, 8, 16, 0.32), 0px 3px 8px -2px rgba(4, 8, 16, 0.48), 0px 16px 32px -6px rgba(4, 8, 16, 0.64);
}
.el-date-range-picker__content.is-left {
  border-right: 1px solid transparent;
}
.el-date-table th {
  border-bottom: 1px solid rgba(204, 219, 255, 0.10);
}
.el-date-table .in-range div {
  background-color: rgba(61, 133, 255, 0.3) !important;
}
.el-date-table td span:hover {
  color: #3D85FF;
  border: 1px solid #3D85FF;
}