.config-control {
  display: flex;
  width: 100%;
  position: relative;
  padding: 4px 6px;
  align-items: center;
  color: #fafafa;
}
.config-control .config-icon {
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  margin-right: 4px;
}
.config-control .config-placeholder {
  display: block;
  width: 16px;
  margin-right: 4px;
}
.config-control .config-title {
  width: 80px;
  height: 24px;
  line-height: 24px;
  padding-right: 5px;
  color: #bfbfbf;
  font-size: 12px;
}
.config-control .config-title65 {
  width: 78px;
}
.config-control .width-div {
  flex: 1;
}
.config-control .pointer {
  cursor: pointer;
}
.config-control .icon-hide {
  display: block;
  width: 16px;
  height: 16px;
  background: url("../assets/img/svg/hide.svg") bottom center no-repeat;
}

.config-control .icon-visible {
  display: block;
  width: 16px;
  height: 16px;
  background: url("../assets/img/svg/visible.svg") bottom center no-repeat;
}

.config-control .control-flex {
  display: flex;
  margin-bottom: 4px;
}

.config-control .el-collapse .el-tabs__new-tab{
  margin-left: 8px !important;
}