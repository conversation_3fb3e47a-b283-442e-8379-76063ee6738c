* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
}

html,
body {
  overflow: hidden;
  height: 100%;
  width: 100%;
  font-family: "思源黑体Medium", "PingFang SC", "Helvetica Neue", Arial, sans-serif !important;
  font-size: 12px;
  color: #2c3e50;
  padding: 0 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.seatom-icon {
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
}

.nowrap {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.mt-16 {
  margin-top: 16px;
}

::-webkit-scrollbar {
  display: block;
  width: 4px !important;
  height: 4px !important;
  /*background: var(--seatom-mono-200) !important;*/
  background: rgba(30, 30, 30, .1) !important;
}

::-webkit-scrollbar-thumb {
  /* 全局修改颜色时 在编辑页面 会不适配
  // background-color: var(--seatom-mono-a500) !important;
  border: unset !important;*/
  background-color: #434b55 !important;
  border: 1px solid #434b55 !important;
}

.fr {
  float: right;
}

.mr-6 {
  margin-right: 6px;
}

.el-button.common-cancel {
  border-radius: 4px;
  border-color: rgba(204, 219, 255, 0.32);
  padding: 8px 16px;
  color: #fff;

  &:hover {
    background-color: transparent;
  }
}

.seatom-dialog.el-dialog {
  background-color: #1F2430;
  border-radius: 8px;
  box-shadow: 0px 0px 1px 0px rgba(4, 8, 16, 0.32), 0px 16px 64px -16px rgba(4, 8, 16, 0.72), 0px 48px 128px -16px rgba(4, 8, 16, 0.64);

  .el-textarea__inner {
    padding: 4px 8px;
    border: none;
    border-bottom: 1px solid rgba(204, 219, 255, 0.16);
  }

  textarea::placeholder {
    color: rgba(255, 255, 255, 0.32) !important;
  }
}