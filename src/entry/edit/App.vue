<template>
  <div id="app">
    <router-view></router-view>
    <BrowserVersion/>
  </div>
</template>

<script>
import BrowserVersion from '@/components/dialog/BrowserVersion'
import { getAuthorization } from '@/api/user'

export default {
  name: 'App',
  components: {
    BrowserVersion
  },
  created () {
    this.init()
  },
  methods: {
    async init () {
      const res = await getAuthorization()
      window.localStorage.setItem('authorizationInfo', JSON.stringify(res.data || {}))
      if (res.data.authorizationDays <= 0) this.$store.commit('user/setOverdueVisible', true)
      this.$store.commit('user/setAuthorizationInfo', res.data)
    }
  }
}
</script>

<style>
@import '../../style/common.scss';
@import '../../style/coverElement.scss';
@import '../../assets/font/datav-font.css';
#app {
  height: 100%;
}
html, body {
  min-width: 1024px;
}
</style>
