import 'babel-polyfill';
import Vue from 'vue'
import store from '../../store'
import router from '../../router/edit'
import '../../components/global/index'
import toggleTheme from '../../utils/theme'
import ElementUI from 'element-ui'
import Message from 'hz-message'
import 'element-ui/lib/theme-chalk/index.css'
import App from './App.vue'
import VueClipboard from 'vue-clipboard2'
import '@/assets/img/index'
import 'animate.css'
import ViewScreen from '@/components/view/ViewScreen'
import ConfigTree from '@/components/editor/ConfigTree'
import ViewCompNode from '@/components/view/ViewCompNode'
import ViewGroupComp from '@/components/view/ViewGroupComp'
import PitchViewScreen from '@/components/view/PitchViewScreen'
import FormViewScreen from '@/components/view/FormViewScreen'
import Particles from 'particles.vue'
import '@/assets/css/custom.css'
import VueLazyload from 'vue-lazyload'

Vue.use(VueLazyload)
Vue.config.productionTip = false
Vue.use(ElementUI)
Vue.use(VueClipboard)
Vue.use(Particles)
Vue.component('ViewScreen', ViewScreen)
Vue.component('ConfigTree', ConfigTree)
Vue.component('ViewCompNode', ViewCompNode)
Vue.component('ViewGroupComp', ViewGroupComp)
Vue.component('PitchViewScreen', PitchViewScreen)
Vue.component('FormViewScreen', FormViewScreen)

Vue.prototype.$message = Message;

toggleTheme(store.state.theme)

new Vue({
  store,
  router,
  render: h => h(App)
}).$mount('#app')
