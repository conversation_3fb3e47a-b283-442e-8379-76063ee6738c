/*
 * @Description: 入口
 * @Date: 2022-09-28 18:44:35
 * @Author: chen<PERSON><PERSON>
 * @LastEditors: chenxingyu
 */
import 'babel-polyfill';
import Vue from 'vue'
import store from '../../store'
import router from '../../router/preview/index'
import '../../components/global/preview' // 预览页不引入全局组件
import 'element-ui/lib/theme-chalk/index.css'
import App from './App.vue'
import '@/assets/img/index'
import 'animate.css'
import Particles from 'particles.vue'
import ViewScreen from '@/components/view/ViewScreen'
import ViewCompNode from '@/components/view/ViewCompNode'
import ViewGroupComp from '@/components/view/ViewGroupComp'
import SeatomLoading from '@/components/global/components/SeatomLoading'
import PitchViewScreen from '@/components/view/PitchViewScreen'
import FormViewScreen from '@/components/view/FormViewScreen'
import { Dialog, Progress, Button, Select, Option, Popover, Tooltip, Collapse, CollapseItem, MessageBox, Table, TableColumn, Input, Form, FormItem } from 'element-ui'

import FastClick from 'fastclick'
FastClick.attach(document.body);

Vue.use(Particles)

Vue.config.productionTip = false

Vue.component('ViewScreen', ViewScreen)
Vue.component('ViewCompNode', ViewCompNode)
Vue.component('ViewGroupComp', ViewGroupComp)
Vue.component('SeatomLoading', SeatomLoading)
Vue.component('PitchViewScreen', PitchViewScreen)
Vue.component('FormViewScreen', FormViewScreen)

const Coms = [Dialog, Progress, Button, Select, Option, Popover, Tooltip, Collapse, CollapseItem, Table, TableColumn, Input, Form, FormItem];
Coms.forEach(com => {
  Vue.component(com.name, com)
})

Vue.prototype.$alert = MessageBox.alert;

new Vue({
  store,
  router,
  render: h => h(App)
}).$mount('#prev')
