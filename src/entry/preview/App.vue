<template>
  <div id="prev">
    <router-view></router-view>
  </div>
</template>

<script>

export default {
  name: 'App',
  mounted () {
    const svg = document.body.querySelector('#__SVG_SPRITE_NODE__');
    if (svg) {
      svg.parentElement.removeChild(svg);
    }
  },
  methods: {}
}
</script>

<style>
@import '../../style/common.scss';
@import '../../style/coverElement.scss';
#prev {
  height: 100%;
}
</style>
