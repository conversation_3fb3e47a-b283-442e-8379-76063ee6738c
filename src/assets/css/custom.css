.element-ui-override .el-tabs__item {
  text-align: center;
  min-width: 80px;
  background-color: transparent;
  padding-left: 0;
}

.element-ui-override .el-scrollbar__wrap {
  overflow-x: hidden;
}

.cm-s-default.CodeMirror {
  background: #1e1e1e;
  color: #eadff7;
}

.cm-s-default div.CodeMirror-selected {
  background: #35424f;
}

.cm-s-default .CodeMirror-line::selection,
.cm-s-default .CodeMirror-line>span::selection,
.cm-s-default .CodeMirror-line>span>span::selection {
  background: #718bb9;
}

.cm-s-default .CodeMirror-line::-moz-selection,
.cm-s-default .CodeMirror-line>span::-moz-selection,
.cm-s-default .CodeMirror-line>span>span::-moz-selection {
  background: #718bb9;
}

.cm-s-default .CodeMirror-gutters {
  background: #1e1e1e;
  border-right: 0px;
}

.cm-s-default .CodeMirror-guttermarker {
  color: white;
}

.cm-s-default .CodeMirror-guttermarker-subtle {
  color: #d0d0d0;
}

.cm-s-default .CodeMirror-linenumber {
  color: #999999;
  /* font-size: 14px !important; */
}

.cm-s-default .CodeMirror-cursor {
  border-left: 1px solid #f8f8f0;
}

.cm-s-default span.cm-comment {
  color: #75715e;
}

.cm-s-default span.cm-atom {
  color: #ae81ff;
}

.cm-s-default span.cm-number {
  color: #ae81ff;
}

.cm-s-default span.cm-comment.cm-attribute {
  color: #97b757;
}

.cm-s-default span.cm-comment.cm-def {
  color: #bc9262;
}

.cm-s-default span.cm-comment.cm-tag {
  color: #e06c75;
}

.cm-s-default span.cm-comment.cm-type {
  color: #5998a6;
}

.cm-s-default span.cm-property,
.cm-s-default span.cm-attribute {
  color: #dcdcaa;
}

.cm-s-default .cm-qualifier {
  color: #f1f18f;
}

.cm-s-default span.cm-keyword {
  color: #c678dd;
}

.cm-s-default span.cm-builtin {
  color: #66d9ef;
}

.cm-s-default span.cm-string {
  color: #98c379;
}

.cm-s-default span.cm-variable {
  color: #3bc9b0;
}

.cm-s-default span.cm-variable-2 {
  color: #9bdeff;
}

.cm-s-default span.cm-variable-3,
.cm-s-default span.cm-type {
  color: #66d9ef;
}

.cm-s-default span.cm-def {
  color: #98c379;
  font-style: italic;
}

.cm-s-default span.cm-bracket {
  color: #577480 !important;
}

.cm-s-default span.cm-tag {
  color: #e06c75;
}

.cm-s-default span.cm-header {
  color: #ae81ff;
}

.cm-s-default span.cm-link {
  color: #ae81ff;
}

.cm-s-default span.cm-error {
  text-decoration: underline;
  text-decoration-color: #f92672;
  text-decoration-skip-ink: auto;
  color: #f92672;
}

.cm-s-default .CodeMirror-activeline-background {
  background-color: transparent;
  box-sizing: border-box;
  opacity: 0.3;
}

.cm-s-default .CodeMirror-activeline-background::before,
.cm-s-default .CodeMirror-activeline-background::after {
  content: " ";
  height: 2px;
  width: 100%;
  background-color: #4e4e4e;
  position: absolute;
}

.cm-s-default .CodeMirror-activeline-background::after {
  bottom: 0;
}

.cm-s-default .CodeMirror-matchingbracket {
  /* border: .5px solid #777777; */
  border-radius: 3px;
  box-sizing: border-box;
  color: rgb(183, 68, 250) !important;
  font-weight: bolder !important;
}

.cm-matchhighlight {
  background: #35424f;
}

.cm-none {
  color: #6a6a6a;
}

.cm-operator {
  color: #FFFFFF;
}

.cm-trailingspace {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAACCAYAAAB/qH1jAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3QUXCToH00Y1UgAAACFJREFUCNdjPMDBUc/AwNDAAAFMTAwMDA0OP34wQgX/AQBYgwYEx4f9lQAAAABJRU5ErkJggg==);
  background-position: bottom left;
  background-repeat: repeat-x;
}

.cm-tab {
  /* background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAMCAYAAAAkuj5RAAAAAXNSR0IArs4c6QAAAGFJREFUSMft1LsRQFAQheHPowAKoACx3IgEKtaEHujDjORSgWTH/ZOdnZOcM/sgk/kFFWY0qV8foQwS4MKBCS3qR6ixBJvElOobYAtivseIE120FaowJPN75GMu8j/LfMwNjh4HUpwg4LUAAAAASUVORK5CYII=);
  background-position: right;
  background-repeat: no-repeat; */
  position: relative;
}

.CodeMirror-selection-highlight-scrollbar {
  background-color: #777777;
  width: 4px !important;
  right: 4px !important;
}

.CodeMirror-search-match {
  background: gold;
  width: 4px !important;
  right: 0 !important;
  box-sizing: border-box;
  opacity: .5;
}


/* The lint marker gutter */
.CodeMirror-lint-markers {
  width: 16px;
}

.CodeMirror-lint-tooltip {
  background-color: #262626;
  border: 1px solid #333333;
  border-radius: 2px;
  color: #FFFFFF;
  font-family: monospace;
  font-size: 10pt;
  overflow: hidden;
  padding: 5px 10px;
  position: fixed;
  white-space: pre-wrap;
  z-index: 100;
  max-width: 600px;
  opacity: 0;
  transition: opacity .4s;
  -moz-transition: opacity .4s;
  -webkit-transition: opacity .4s;
  -o-transition: opacity .4s;
  -ms-transition: opacity .4s;
}

.CodeMirror-lint-mark-error,
.CodeMirror-lint-mark-warning {
  background-position: left bottom;
  background-repeat: repeat-x;
}

.CodeMirror-lint-mark-error {
  background-image:
    url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAADCAYAAAC09K7GAAAAAXNSR0IArs4c6QAAAAZiS0dEAP8A/wD/oL2nkwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9sJDw4cOCW1/KIAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAAHElEQVQI12NggIL/DAz/GdA5/xkY/qPKMDAwAADLZwf5rvm+LQAAAABJRU5ErkJggg==");
}

.CodeMirror-lint-mark-warning {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAADCAYAAAC09K7GAAAAAXNSR0IArs4c6QAAAAZiS0dEAP8A/wD/oL2nkwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9sJFhQXEbhTg7YAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAAMklEQVQI12NkgIIvJ3QXMjAwdDN+OaEbysDA4MPAwNDNwMCwiOHLCd1zX07o6kBVGQEAKBANtobskNMAAAAASUVORK5CYII=");
}

.CodeMirror-lint-marker-error,
.CodeMirror-lint-marker-warning {
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  display: inline-block;
  height: 16px;
  width: 16px;
  vertical-align: middle;
  position: relative;
}

.CodeMirror-lint-message-error,
.CodeMirror-lint-message-warning {
  padding-left: 20px;
  background-position: center left;
  background-repeat: no-repeat;
}

.CodeMirror-lint-marker-error,
.CodeMirror-lint-message-error {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAHlBMVEW7AAC7AACxAAC7AAC7AAAAAAC4AAC5AAD///+7AAAUdclpAAAABnRSTlMXnORSiwCK0ZKSAAAATUlEQVR42mWPOQ7AQAgDuQLx/z8csYRmPRIFIwRGnosRrpamvkKi0FTIiMASR3hhKW+hAN6/tIWhu9PDWiTGNEkTtIOucA5Oyr9ckPgAWm0GPBog6v4AAAAASUVORK5CYII=");
}

.CodeMirror-lint-marker-warning,
.CodeMirror-lint-message-warning {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAANlBMVEX/uwDvrwD/uwD/uwD/uwD/uwD/uwD/uwD/uwD6twD/uwAAAADurwD2tQD7uAD+ugAAAAD/uwDhmeTRAAAADHRSTlMJ8mN1EYcbmiixgACm7WbuAAAAVklEQVR42n3PUQqAIBBFUU1LLc3u/jdbOJoW1P08DA9Gba8+YWJ6gNJoNYIBzAA2chBth5kLmG9YUoG0NHAUwFXwO9LuBQL1giCQb8gC9Oro2vp5rncCIY8L8uEx5ZkAAAAASUVORK5CYII=");
}

.CodeMirror-lint-marker-multiple {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAHCAMAAADzjKfhAAAACVBMVEUAAAAAAAC/v7914kyHAAAAAXRSTlMAQObYZgAAACNJREFUeNo1ioEJAAAIwmz/H90iFFSGJgFMe3gaLZ0od+9/AQZ0ADosbYraAAAAAElFTkSuQmCC");
  background-repeat: no-repeat;
  background-position: right bottom;
  width: 100%;
  height: 100%;
}

.cm-mustache {
  color: #abb2bf !important;
}