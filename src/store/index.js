import Vue from 'vue'
import Vuex from 'vuex'
import root from './root'
import user from './modules/user'
import editor from './modules/editor'
import indicator from './modules/indicator'
import view from './modules/view'
import comtheme from './modules/comtheme'
import customcomp from './modules/customcomp'
import tutorial from './modules/tutorial'
import event from './modules/event'
import datacontainer from './modules/datacontainer'

Vue.use(Vuex)

const debug = process.env.NODE_ENV !== 'production'

export default new Vuex.Store({
  ...root,
  modules: {
    user,
    editor,
    indicator,
    view,
    comtheme,
    customcomp,
    tutorial,
    event,
    datacontainer
  },
  strict: debug
})
