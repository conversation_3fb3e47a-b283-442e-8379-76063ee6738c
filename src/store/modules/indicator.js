import Message from 'hz-message';
import route from '@/router/edit/index';
import { getCompPkgList } from '@/api/package';
import { updateComponent } from '@/api/component';

const state = {
  // 所有组件包
  compPackges: [],
  // 组件包 map 结构(父子组件打平)，key 为 name,便于快速获取包信息
  compPackgesMap: {},
  editPanelState: {
    layer: {
      type: 'layer',
      name: '图层',
      icon: 'layer',
      select: false
    },
    component: {
      type: 'component',
      name: '组件列表',
      icon: 'comlist',
      select: true
    },
    config: {
      type: 'config',
      name: '右侧面板',
      icon: 'rightpanel',
      select: true
    }
  },
  drawerBtnState: {
    update: {
      type: 'update',
      name: '组件升级',
      icon: 'update',
      select: false
    }
  },
  currentCom: null,
  indicatorData: null
}

const getters = {

}

const mutations = {
  initCompPackages (state, data) {
    state.compPackges = data;
    for (const pkg of state.compPackges) {
      state.compPackgesMap[pkg.name] = pkg;
      if (pkg.children) {
        for (const child of pkg.children) {
          state.compPackgesMap[child.name] = child;
        }
      }
    }
  },
  updateDrawerSelect (state, data) {
    const { type, value } = data
    state.drawerBtnState[type].select = value
  },
  updateEditSelect (state, data) {
    const { type, value } = data
    state.editPanelState[type].select = value
  },
  updateCurrentCom (state, data) {
    state.currentCom = data
  },
  updateScreenCom (state, params) {
    // 更新组件信息，支持单个和多个更新传参
    if (_.isNil(params)) return;
    if (!_.isArray(params)) {
      params = [params];
    }
    params.forEach(({ keyValPairs }) => {
      const comData = state.currentCom;
      if (comData && keyValPairs && keyValPairs.length) {
        keyValPairs.forEach((pair) => {
          _.set(comData, pair.key, pair.value);
        });
      }
    });
  },
  setIndicatorData (state, data) {
    state.indicatorData = _.cloneDeep(data)
  }
}

const actions = {
  async initScreenAction ({ state, dispatch }, { screenId }) {
    const res = await dispatch('editor/getScreenInfo', { id: screenId }, { root: true });
    if (res.code === 402) {
      Message.error('指标不存在！');
      route.push('/');
      return;
    }
    await dispatch('editor/getScreenComps', { indicator: state.currentCom }, { root: true });
    await dispatch('editor/getPanelScreenInfo', {}, { root: true });
    await dispatch('editor/getScreenLayers', {}, { root: true });
    await dispatch('getCompPackages');
    await dispatch('editor/getScreenFilters', { screenId: 1 }, { root: true });
    await dispatch('editor/getSystemFilters', {}, { root: true });
    // await dispatch('getScreenGroupData');
  },
  async getCompPackages ({ commit }) {
    const res = await getCompPkgList();
    if (res && res.success) {
      commit('editor/initCompPackages', res.data, { root: true });
    }
  },
  async updateCurrentCom ({ commit }, params) {
    // 更新组件信息，支持单个和多个
    if (_.isNil(params)) return false;
    if (!_.isArray(params)) {
      params = [params];
    }
    const newParams = params.map(({ id, keyValPairs }) => {
      const data = _.reduce(
        keyValPairs,
        (obj, pair) => {
          obj[pair.key] = pair.value;
          return obj;
        },
        {}
      );
      return {
        id,
        data
      };
    });
    const res = await updateComponent(newParams, { id: 0 });
    if (res && res.success) {
      await commit('updateScreenCom', params);
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
