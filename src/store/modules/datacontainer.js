// 数据容器-公共数据源
import Vue from 'vue'
const state = () => ({
  // 数据容器数据集合
  containerData: {}
})

// actions
const actions = {
  // 初始化数据容器data
  initContainerData ({ state, commit }, ids) {
    commit('initContanerData', ids)
  },
  // 设置数据容器data
  setContanerData ({ state, commit }, { id, data }) {
    commit('setContanerData', { id, data })
  }
}

// mutations
const mutations = {
  initContanerData (state, ids) {
    ids.forEach(id => {
      Vue.set(state.containerData, id, { loaded: false, data: [] })
    })
  },
  setContanerData (state, { id, data }) {
    Vue.set(state.containerData, id, { loaded: true, data })
  }
}

const getters = {
  getContainerDataById: state => (id) => {
    if (_.has(state.containerData, id)) {
      return state.containerData[id]
    } else {
      console.warn(`获取不到 数据容器id 为 ${id} 的数据！`);
      return { loaded: true, data: [] }
    }
  }
}

export default {
  namespaced: true,
  state,
  actions,
  mutations,
  getters
}
