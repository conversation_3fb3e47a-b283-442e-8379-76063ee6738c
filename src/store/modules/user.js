// state
const state = () => ({
  name: window.localStorage.getItem('name') || '',
  id: window.localStorage.getItem('userId') || '',
  authorizationInfo: JSON.parse(window.localStorage.getItem('authorizationInfo') || '{}'),
  overdueVisible: false,
  roleType: 3 // 角色类型：1:超管，2:应用编辑者，3:应用使用者
})

// getters
const getters = {
  userId: state => state.id
}

// actions
const actions = {}

// mutations
const mutations = {
  setName (state, name) {
    state.name = name
  },
  setUserId (state, id) {
    state.id = id
  },
  setRoleType (state, type) {
    state.roleType = type
  },
  setAuthorizationInfo (state, info) {
    state.authorizationInfo = _.cloneDeep(info)
    if (info.authorizationDays <= 0) state.overdueVisible = true
  },
  setOverdueVisible (state, bool) {
    state.overdueVisible = bool
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
