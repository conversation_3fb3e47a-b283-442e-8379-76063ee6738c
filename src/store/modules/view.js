import { getScreen } from '@/api/screen'
import { getFilters } from '@/api/filter'

// state
const state = () => ({
  screenData: {},
  filtersMap: {},
  linkComps: { // 联动组件集合
    uid: '',
    comps: []
  },
  screenLoaded: false, // 大屏是否加载完成
  showLoading: false, // 是否显示加载动画
  immediateLink: {}
})

// getters
const getters = {
  screenComs: state => {
    const { screenData } = state
    return screenData ? screenData.components : {}
  },
  screenConfig: state => {
    const { screenData } = state
    return screenData ? screenData.config : {}
  },
  screenLayers: state => {
    const { screenData } = state
    return screenData ? screenData.layers : []
  }
}

// actions
const actions = {
  async initScreenData ({ commit, dispatch }, { screenId }) {
    await dispatch('getFilters', { screenId })
    await dispatch('getScreenData', { screenId })
  },
  async getScreenData ({ commit }, { screenId }) {
    const res = await getScreen({ id: screenId })
    if (res && res.success) {
      commit('initScreenData', res.data)
    }
  },
  async getFilters ({ commit }, { screenId }) {
    const res = await getFilters({ screenId })
    if (res && res.success) {
      commit('initFiltersMap', res.data)
    }
  }
}

// mutations
const mutations = {
  initScreenData (state, data) {
    state.screenData = _.cloneDeep(data)
  },
  initFiltersMap (state, filters = []) {
    state.filtersMap = _.keyBy(filters, 'id')
  },
  updateLinkComps (state, { uid, comps }) {
    if (state.linkComps.uid === uid) {
      comps.forEach(c => {
        if (!state.linkComps.comps.includes(c)) {
          state.linkComps.comps.push(c)
        }
      })
    } else {
      state.linkComps.uid = uid;
      state.linkComps.comps = comps;
    }
  },
  clearLinkComps (state) {
    state.linkComps.uid = '';
    state.linkComps.comps = [];
  },
  changeLoading (state, loading) {
    state.showLoading = !!loading
  },
  updateImmediateLink (state, immediateLink) {
    state.immediateLink = immediateLink
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
