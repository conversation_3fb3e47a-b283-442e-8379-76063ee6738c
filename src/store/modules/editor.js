import _ from 'lodash';
import {
  getScreen,
  getScreenInfo,
  getScreenComps,
  getPanelComs,
  getScreenLayers,
  getScreens,
  updateScreen,
  addScene,
  addPage,
  deleteScene,
  deletePage,
  updateName,
  moveShareScreen,
  updateShareScreen,
  deleteScreenLayerByIds,
  moveScreenLayerByIds,
  dragInsertScreenLayer,
  insertScreenLayers,
  updateScreenSelectedLayers,
  createLayerGroups,
  cancelLayerGroups,
  paging
} from '@/api/screen';
import { postData } from '@/api/datastorage';
import { getCompPkgList } from '@/api/package';
import {
  createComponent,
  createChildComponent,
  updateComponent,
  deleteComponent,
  deleteChildComponent,
  copyComponent,
  copyChildComponent,
  getComponentInfo
} from '@/api/component';
import { getFilters, systemfilter, deleteScreenFilter } from '@/api/filter';
import {
  getMessage,
  updateMessage,
  deleteMessage,
  readMsg
} from '@/api/message';
import { themeScheme } from '@/api/theme';
import { getUsershareList } from '@/api/workspace';
import { chargeData, isPanel, findImageUrl, replaceUrl } from '@/utils/base';
import { REQUEST_TIME_RANGE, REQUEST_MERGE_NUM } from '@/common/constants';
import snapshot from './snapshot';
import Vue from 'vue';
import route from '@/router/edit/index';
import dataUtil from '@/utils/data';
import Message from 'hz-message';

// state
const state = () => ({
  // 编辑器快照
  ...snapshot.state,
  // 面板大屏数据对象
  panelScreensObj: {},
  // 大屏分组数据
  screenGroupData: [],
  // 子屏列表
  childScreens: [],
  // 父屏信息
  parentScreen: [],
  // 子屏组件信息
  childScreensComps: [],
  // 父屏组件信息
  parentScreenComps: [],
  // 大屏信息
  screenInfo: {
    id: null,
    name: '',
    type: 'pc',
    templateId: null,
    workspaceId: null,
    projectId: null,
    themeSchemeId: null,
    parentId: null,
    relationCompId: null, // 弹窗面板关联组件id
    config: {
      width: 1920,
      height: 1080,
      bgType: 'built_in', // 背景图类型 ['built_in', 'custom']
      backgroundColor: 'rgba(0,0,0,0)',
      backgroundImage: '',
      backThumbnail: false,
      backgroundParticlesType: 'STYLE_NONE', // 粒子效果
      backgroundParticlesCustom: {}, // 自定义粒子属性
      scale: 1,
      scaleType: 'full_height_scroll', // ['full_screen', 'full_width', 'full_height', 'full_height_scroll', 'no_scale']
      gridSpace: 1,
      thumbnail: '', // 封面
      ruler_h_lines: [], // x轴标尺参照线
      ruler_v_lines: [], // y轴标尺参照线
      globalFilterParams: {
        enable: false,
        hue: 0,
        saturate: 100,
        brightness: 100,
        contrast: 100,
        opacity: 100,
        grayscale: 0
      },
      // 骨骼信息
      skeleton: {
        folderId: -1,
        fileId: '',
        url: ''
      }
    },
    screenType: 'common',
    sceneConfig: [],
    permissionDataConfig: {
      // 权限数据源
      dataResponse: {
        // 用户权限api数据源设置
        sourceType: '',
        source: {
          api: {
            data: {
              sourceId: '',
              baseUrl: '',
              method: '',
              headers: '{}',
              path: '',
              params: '',
              body: '{}',
              reqFromBack: false,
              needCookie: false
            }
          }
        }
      },
      mappingRelation: {
        // 用户权限映射字段
        id: '',
        label: '',
        permission: ''
      },
      userPermissionList: [], // 用户权限列表
      tips: {
        background: {
          image: {
            repeat: 'no-repeat',
            positionY: 'center',
            positionX: 'center',
            size: 'contain',
            url: ''
          },
          gradient: {
            deg: 90,
            end: 'rgba(31, 113, 255, 0.1)',
            start: 'rgba(31, 113, 255, 0.6)'
          },
          pure: 'rgba(32, 188, 255, 0.15)',
          type: 'pure',
          show: true
        },
        font: {
          fontSize: 16,
          color: '#999',
          fontFamily: 'Microsoft YaHei',
          fontWeight: '700'
        },
        info: '暂无数据',
        type: 'hide' // hide--隐藏 tips--提示 disable--禁用
      }
    }
  },
  // 大屏组件信息
  screenComs: {},
  // 父面板大屏信息
  parentScreenInfo: {},
  // 父面板组件信息
  parentScreenComs: {},
  // 子面板组件信息
  childScreenComs: {},
  // 父子面板layerMap
  layerMap: {},
  // 组件数据
  comsData: {},
  // 大屏图层树信息
  screenLayers: [],
  layerLoading: false,
  // 大屏过滤器
  screenFilters: {},
  // 内置过滤器
  systemFilters: {},
  // 所有组件包
  compPackges: [],
  // 组件包 map 结构(父子组件打平)，key 为 name,便于快速获取包信息
  compPackgesMap: {},
  // 大屏加载状态
  loaded: false,
  editLoading: false,
  // 当前选中组件id(如果没有选中，则为 null；如果只有一个选中，为 id string；如果多个选中，为 id Array)
  currentSelectId: null,
  // 当前选中子组件id
  currentChildId: null,
  // 图层面板数据
  editPanelState: {
    layer: {
      type: 'layer',
      name: '图层',
      icon: 'layer',
      select: false
    },
    component: {
      type: 'component',
      name: '组件列表',
      icon: 'comlist',
      select: true
    },
    config: {
      type: 'config',
      name: '右侧面板',
      icon: 'rightpanel',
      select: true
    },
    tool: {
      type: 'tool',
      name: '指标库',
      icon: 'source',
      select: false
    }
  },
  drawerBtnState: {
    filter: {
      // 暂时隐藏
      type: 'filter',
      name: '数据过滤器',
      icon: 'filter',
      select: false
    },
    update: {
      type: 'update',
      name: '组件升级',
      icon: 'update',
      select: false
    },
    layout: {
      type: 'layout',
      name: '布局',
      icon: 'layout',
      select: false
    },
    // childScreens: {
    //   type: 'childScreens',
    //   name: '子屏',
    //   icon: 'child-screens',
    //   select: false
    // },
    abnormal: {
      type: 'abnormal',
      name: '异常',
      icon: 'abnormal',
      select: false
    },
    // abnormal: {
    //   type: 'abnormal',
    //   name: '异常',
    //   icon: 'abnormal',
    //   select: false
    // },
    permission: {
      type: 'permission',
      name: '权限设置',
      icon: 'authority',
      select: false
    },
    publicDataManage: {
      type: 'publicDataManage',
      name: '公共数据',
      icon: 'other-management',
      select: false
    }
  },
  // 复制的组件 id
  copySelectId: null,
  // 右键点击菜单的位置
  menuPosition: {
    x: null,
    y: null
  },
  // 主题方案
  themeScheme: [],
  // 场景id
  sceneId: null,
  // 组件归属的页面id
  pageId: null,
  // 通知消息列表
  noticeList: [],
  // 缓存 skeleton 骨骼列表
  skeletonList: [],
  // 数据源映射出错的组件列表
  dataMappingErrorCompIds: [],
  // 报错的组件列表
  errorCompIds: [],
  // 分组列表
  manage: [],
  // 选中分组列表下的大屏
  selectedManageScreen: null,
  // 组件样式中的历史颜色记录
  historyColor: {
    font: [],
    background: []
  },
  // 父组件数据
  inheritData: [],
  // 判断是否显示分组按钮
  // showGroup: true
  // 当前大屏占用情况
  coeditData: {},
  // 语音指令配置
  showVoiceConfig: false,
  // socket链接状态
  socketStatus: '',

  // 服务端渲染用到的数据
  ssrRender: {
    showCompIds: [], // ssr预渲染组件id
    childScreensInfo: {} // ssr预渲染的子大屏信息
  },
  indicatorId: ''
});

// getters
const getters = {
  getInheritData: (state) => state.inheritData,
  ...snapshot.getters,
  getComDataById: (state) => (id) => {
    if (_.has(state.screenComs, id)) {
      return state.screenComs[id];
    } else if (id && id.includes('groups_')) {
      return true;
    } else {
      console.warn(`获取不到 id 为 ${id} 的组件数据！`);
    }
  },
  // 当前选中的配置项类型, 枚举：screen-大屏；com-组件；group-图层组(多个组件)。
  currentConfigType: (state) => {
    const { currentSelectId } = state;
    if (!currentSelectId) {
      return 'screen';
    } else if (Array.isArray(currentSelectId)) {
      return 'group';
    } else {
      const comConfig = state.screenComs[state.currentSelectId];
      return comConfig ? 'com' : 'screen';
    }
  },
  currentCom: (state, getters) => {
    const currentId = getters.currentConfigId;
    return state.screenComs[currentId];
  },
  // 当前组件右侧配置项关联的 id
  currentConfigId: (state) => {
    const { currentChildId, currentSelectId } = state;
    return (
      currentChildId ||
      (Array.isArray(currentSelectId) ? null : (currentSelectId || (state.indicatorId ? state.indicatorId : '')))
    );
  },
  comCtlConfigTreeData: (state, getters) => {
    const { screenComs } = state;
    const currentId = getters.currentConfigId;
    if (currentId) {
      try {
        const treeData = JSON.parse(screenComs[currentId].controlConfig);
        return treeData;
      } catch (err) {
        console.error('配置解析出错！', err);
      }
    }
  },
  comCtlConfigObj: (state, getters) => {
    const currentId = getters.currentConfigId;
    if (currentId) {
      return state.screenComs[currentId].config;
    }
  },
  drawerMask: (state) => {
    return Object.values(state.drawerBtnState).some((item) => item.select);
  },
  ctxMenuList: (state) => {
    let comData = null;
    const { currentSelectId, screenInfo } = state;
    if (_.isString(currentSelectId)) {
      comData = state.screenComs[currentSelectId];
    }
    if (_.isArray(currentSelectId)) {
    }
    const isMuti = _.isArray(currentSelectId) && currentSelectId.length > 1;

    const selected = _.isArray(currentSelectId)
      ? currentSelectId
      : [currentSelectId];
    const hasGroup = selected.some((item) => {
      // 存在分组
      return (
        item &&
        (item.startsWith('groups_') ||
          item.startsWith('interaction-container-modulepanel'))
      );
    });

    const hasMobileGroup = screenInfo.type === 'mobile' && hasGroup;

    return [
      {
        key: 'top',
        label: '置顶',
        icon: '',
        show: !!comData || isMuti
      },
      {
        key: 'bottom',
        label: '置底',
        icon: '',
        show: !!comData || isMuti
      },
      {
        key: 'moveUp',
        label: '上移',
        icon: '',
        show: !!comData || isMuti
      },
      {
        key: 'moveDown',
        label: '下移',
        icon: '',
        show: !!comData || isMuti
      },
      {
        key: 'show',
        label: '显示',
        icon: '',
        show: (comData && !comData.show) || isMuti
      },
      {
        key: 'hide',
        label: '隐藏',
        icon: '',
        show: ((comData && comData.show) || isMuti) && !hasMobileGroup
      },
      {
        key: 'lock',
        label: '锁定',
        icon: '',
        show: ((comData && !comData.attr.lock) || isMuti) && !hasMobileGroup
      },
      {
        key: 'unLock',
        label: '解锁',
        icon: '',
        show: (comData && comData.attr.lock) || isMuti
      },
      {
        key: 'group',
        label: '分组',
        icon: '',
        show: isMuti
      },
      {
        key: 'cancelGroup',
        label: '取消分组',
        icon: '',
        show: true
      },
      {
        key: 'rename',
        label: '重命名',
        icon: '',
        show: true
      },
      {
        key: 'copy',
        label: '复制',
        icon: '',
        show: (!!comData || isMuti)
      },
      {
        key: 'paste',
        label: '粘贴',
        icon: '',
        show: true
      },
      {
        key: 'delete',
        label: '删除',
        icon: '',
        show:
          (screenInfo.type === 'mobile' && !hasGroup) ||
          (screenInfo.type === 'pc' && (!!comData || isMuti))
      }
    ];
  },
  dataMappingErrorComps: (state) => {
    return state.dataMappingErrorCompIds
      .map((item) => state.screenComs[item])
      .filter((item) => item !== undefined);
  },
  errorComps: (state) => {
    return state.errorCompIds
      .map((item) => state.screenComs[item.componentId])
      .filter((item) => item !== undefined);
  },
  // socket是否连接成功
  isSocketConnect: (state) => {
    return state.socketStatus === 'connect';
  }
};

// actions
const actions = {
  // 获取消息列表
  async getMessage ({ state, commit }, params) {
    const res = await getMessage(params);
    if (res.success) {
      // 获取同步列表
      const usershareListRes = (await getUsershareList()) || {};

      const data = res.data.map((item) => {
        // 判断消息是否为同步大屏类型
        if (item.type === 'usershare') {
          const usershareList = usershareListRes.success
            ? usershareListRes.data
            : [];

          const current = usershareList.find((i) => {
            return i.id === item.content.usershare.usershareId;
          });

          if (current) {
            item.text = `${current.userName}给你同步了一个可视化项目`;
            item.isAccept = current.isAccept;
          }
        } else if (item.type === 'screenCoedit') {
          const roleMap = { collaborators: '协作者', viewers: '查看者' };
          // 判断消息是否为协同大屏类型
          const screenCoedit = item?.content?.screenCoedit ?? {};
          const actionMap = { add: '添加', update: '修改', remove: '移除' };
          const action = actionMap[screenCoedit.action] ?? '';
          const role = roleMap[screenCoedit.coeditRole] ?? '';

          if (action === 'remove') {
            item.text = `${screenCoedit.createUserName}将你从可视化项目 ${screenCoedit.coeditScreenName}中移除`;
          } else {
            item.text = `${screenCoedit.createUserName}将你${action}为可视化项目 ${screenCoedit.coeditScreenName} 的${role}`;
          }
        } else {
          item.text = item.content.common.commonContent;
        }
        return item;
      });
      commit('updateNoticeList', data);
    }
  },
  // 删除消息
  async deleteMessage ({ state, commit }, params) {
    const res = await deleteMessage(params);
    if (res.success) {
      commit('deleteNotice', params);
    }
  },
  // 更新消息状态
  async updateMessage ({ state, commit }, { data, params }) {
    // 同步消息
    const { type = 'usershare' } = data;
    if (type === 'usershare') {
      delete data.type;
      const res = await updateMessage(data, params);
      if (res.success) {
        commit('updateNotice', params);
      }
    } else if (type === 'screenCoedit') {
      // 协同消息
      const res = await readMsg(params);
      if (res.success) {
        commit('updateNotice', params);
      }
    }
  },
  // 接受共享的大屏
  async moveShareScreen ({ state, commit }, params) {
    return moveShareScreen(params);
  },
  // 更新共享大屏的接受状态
  async updateShareScreen ({ state, commit }, { data, params }) {
    await updateShareScreen(data, params);
  },
  // 获取大屏分组数据
  async getScreenGroupData ({ state, commit }, params) {
    // 如果是面板，则不请求
    if (state.screenInfo.isDynamicScreen) {
      return;
    }
    const res = await paging({ workspaceId: state.screenInfo.workspaceId });
    if (res && res.success) {
      commit('initScreenGroupData', res.data);
    }
  },
  // 获取子屏数据
  async getChildScreens ({ state, commit, dispatch }, params) {
    // 如果是面板，则不请求
    if (state.screenInfo.isDynamicScreen || state.screenInfo.screenType === 'child') {
      return;
    }
    const res = await getScreens({ screenType: 'child', parentId: state.screenInfo.id });
    if (res && res.success) {
      commit('initChildScreensData', res.data);
      dispatch('getChildScreensComps', res.data); // 获取子屏的组件信息
    }
  },
  async getParentScreen ({ state, commit, dispatch }, params) {
    if (state.screenInfo.screenType === 'child' && state.screenInfo.parentId) {
      const res = await getScreens({ id: state.screenInfo.parentId });
      if (res && res.success) {
        commit('initParentScreenData', res.data);
        dispatch('getParentScreenComps', res.data); // 获取父屏的组件信息
      }
    }
  },
  async getChildScreensComps ({ state, commit }, params) { // 获取子屏的组件信息
    if (state.childScreens.length) {
      const childComs = {}
      let panel = []
      for (let i = 0; i < state.childScreens.length; i++) {
        const item = state.childScreens[i]
        item.comList.forEach(com => {
          childComs[com.id] = com
          if (isPanel(com.comType)) {
            panel = panel.concat(com.config.screens)
          }
        })
        if (panel.length) {
          const res = await getPanelComs({ screenIds: panel.map(p => p.id) })
          if (res && res.success) {
            res.data.forEach(p => {
              p.comList.forEach(com => {
                childComs[com.id] = com
              })
              state.layerMap[p.id] = p.layerTree
            })
          }
        }
      }
      commit('initChildScreensComps', childComs)
    }
  },
  async getParentScreenComps ({ state, commit }, params) { // 获取父屏的组件信息
    const coms = {}
    let panel = []
    if (!params.length) return false;
    params[0].comList.forEach(com => {
      coms[com.id] = com
      if (isPanel(com.comType)) {
        panel = panel.concat(com.config.screens)
      }
    })
    if (panel.length) {
      const res = await getPanelComs({ screenIds: panel.map(p => p.id) })
      if (res && res.success) {
        res.data.forEach(p => {
          p.comList.forEach(com => {
            coms[com.id] = com
          })
          state.layerMap[p.id] = p.layerTree
        })
      }
    }
    commit('initParentScreenComps', coms)
  },
  // 获取预览、发布页大屏数据
  async getPreviewScreen ({ state, commit }, { screenId, shareToken }) {
    if (state.panelScreensObj[screenId]) {
      return Promise.resolve(state.panelScreensObj[screenId]);
    }

    const params = {};
    if (shareToken) {
      params.shareToken = shareToken;
    } else {
      params.id = screenId;
    }

    const res = await getScreen(params)
    if (res.code !== 200) {
      return Promise.reject(res);
    }

    const screenData = res.data || {};
    if (screenData.panelScreens && screenData.panelScreens.length) {
      commit('mergePanelScreensObj', screenData.panelScreens);
      delete screenData.panelScreens;
    }

    return screenData;
  },
  async initScreenAction ({ dispatch, commit }, { screenId }) {
    const res = await dispatch('getScreenInfo', { id: screenId });
    if (res.code === 402) {
      Message.error('大屏不存在！');
      route.push('/');
      return;
    }
    await dispatch('getScreenComps');
    await dispatch('getPanelScreenInfo');
    await dispatch('getScreenLayers');
    await dispatch('getCompPackages');
    await dispatch('getScreenFilters', { screenId });
    await dispatch('getSystemFilters');
    await dispatch('getScreenGroupData');
    await dispatch('getChildScreens');
    await dispatch('getParentScreen');
  },
  async getScreenInfo ({ state, dispatch, commit }, params) {
    const res = await getScreenInfo(params);
    if (res.code === 200) {
      commit('initScreenInfo', res.data);
    }
    return res;
  },
  // 获取面板类组件的组件信息
  async getPanelScreenInfo ({ state, dispatch, commit }, params) {
    const _route = route.app.$route;
    const screenId = _route.query.screenId;
    if (state.screenInfo.isDynamicScreen && !!screenId) {
      await dispatch('getParentScreenInfo', screenId);
    }

    const { screenComs, parentScreenComs } = state;
    const panels = Object.values({ ...screenComs, ...parentScreenComs }).filter(
      (com) => {
        return isPanel(com.comType);
      }
    );
    if (panels.length) {
      const screenArr = [];
      panels.forEach((pa) => {
        const screens = pa.config.screens.filter((item) => item.id);
        screenArr.push(...screens);
      });
      if (screenArr.length) {
        dispatch('getChildScreenComps', screenArr);
      }
    }
  },
  async getSceneConfig ({ commit }, params) {
    const data = (await getScreenInfo(params)).data;
    commit('initSceneConfig', data);
  },
  async updateScreenInfo ({ state, commit }, keyValPairs = []) {
    const newInfo = keyValPairs.reduce((obj, pair) => {
      obj[pair.key] = pair.value;
      return obj;
    }, {});
    const res = await updateScreen(newInfo, { id: state.screenInfo.id });
    if (res && res.success) {
      commit('updateScreenInfo', keyValPairs);
    }
  },
  async createScreenComp ({ state, commit, dispatch }, data) {
    const { isActive, result, id } = data;
    delete data.isActive;
    delete data.result;
    const param = { screenId: state.screenInfo.id };
    const res = await createComponent(data, param);
    if (isActive) {
      const sid = state.screenInfo.id;
      const params =
        typeof result === 'object'
          ? [{ data: result, id }]
          : [{ data: { staticData: [{ url: result }] }, id }];
      const resData = await updateComponent(params, { id: sid });
      if (resData && resData.success) {
        await commit('updateScreenCom', data);
      }
    }
    if (res && res.success && res.data) {
      res.data.forEach((com) => {
        commit('createScreenCom', com);
      });
    }
    return res;
  },
  async createChildComp ({ state, commit }, { name, version, pid }) {
    const param = { screenId: state.screenInfo.id };
    const res = await createChildComponent({ name, version, pid }, param);
    if (res && res.success && res.data) {
      const pdata = state.screenComs[pid];
      commit('createScreenCom', res.data);
      commit('updateScreenCom', {
        id: pid,
        keyValPairs: [
          {
            key: 'children',
            value: pdata.children ? [...pdata.children, res.data.id] : []
          }
        ]
      });
    }
  },
  async copyScreenComp ({ state, commit }, data) {
    const param = { screenId: state.screenInfo.id };
    if (_.isNil(data)) return false;
    if (!_.isArray(data)) {
      data = [data];
    }
    const res = await copyComponent(data, param);
    if (res && res.success && res.data) {
      res.data.forEach((com) => {
        commit('createScreenCom', com);
      });
      return true;
    }
    return false;
  },
  async copyChildComp ({ state, commit }, { id, pid }) {
    const res = await copyChildComponent({ id, pid });
    if (res && res.success && res.data) {
      const pdata = state.screenComs[pid];
      commit('createScreenCom', res.data);
      commit('updateScreenCom', {
        id: pid,
        keyValPairs: [
          {
            key: 'children',
            value: pdata.children ? [...pdata.children, res.data.id] : []
          }
        ]
      });
    }
  },
  async updateScreenCom ({ state, commit, getters }, params) {
    // 更新组件信息，支持单个和多个
    if (_.isNil(params)) return false;
    if (!_.isArray(params)) {
      params = [params];
    }
    const newParams = params.map(({ id, keyValPairs }) => {
      const data = _.reduce(
        keyValPairs,
        (obj, pair) => {
          obj[pair.key] = pair.value;
          return obj;
        },
        {}
      );
      return {
        id,
        data
      };
    });
    let sid = params[0]?.sid == null ? state.screenInfo.id : params[0]?.sid;
    if (getters.currentCom?.indicatorContainer) {
      sid = 1
    }
    let sceneId = '';
    let pageId = '';
    if (state.screenInfo.screenType === 'scene') {
      sceneId = state.sceneId;
      pageId = state.pageId;
    }
    const res = await updateComponent(newParams, { id: sid, sceneId, pageId });
    if (res && res.success) {
      // 当前屏为子屏，通知父屏更新子屏数据，否则通知子屏更新数据
      Vue.prototype.$socket && Vue.prototype.$socket.io.emit('linkage', {
        room: state.screenInfo.parentId || state.screenInfo.id,
        msg: {
          type: 'update',
          currId: state.screenInfo.id
        }
      })
      await commit('updateScreenCom', params);
    }
  },
  async deleteComponent ({ state, commit }, id) {
    if (_.isNil(id)) return;
    if (!_.isArray(id)) {
      id = [id];
    }
    const deleteIds = [];
    id.forEach((d) => {
      const comCfg = state.screenComs[d];
      if (comCfg) {
        deleteIds.push(d);
        if (comCfg.children) {
          // 删除父组件时也要删除子组件
          deleteIds.push(...comCfg.children);
        }
      }
    });
    if (!deleteIds.length) return;
    const sid = state.screenInfo.id;

    let sceneId = '';
    let pageId = '';
    if (state.screenInfo.screenType === 'scene') {
      sceneId = state.sceneId;
      pageId = state.pageId;
    }

    const res = await deleteComponent(
      { deleteIds },
      { id: sid, sceneId, pageId }
    );
    if (res && res.success) {
      // 考虑父子组件删除多个 id
      deleteIds.forEach((deId) => {
        commit('updateDataMappingErrorCompIds', {
          componentId: deId,
          type: 'remove'
        });
        commit('deleteScreenCom', deId);
        commit('deleteComData', deId);
      });
    }
  },
  async deleteChildComp ({ state, commit }, { id, pid }) {
    const res = await deleteChildComponent({ id, pid });
    if (res && res.success) {
      const pdata = state.screenComs[pid];
      commit('deleteScreenCom', id);
      commit('deleteComData', id);
      commit('updateScreenCom', {
        id: pid,
        keyValPairs: [
          {
            key: 'children',
            value: _.pull(_.cloneDeep(pdata.children), id)
          }
        ]
      });
    }
  },
  getCompData: (function () {
    // 改造原来的组件数据获取方法，用来实现请求合并；
    const time = REQUEST_TIME_RANGE;
    const maxNum = REQUEST_MERGE_NUM; // 每个请求里最多包含的组件
    const params = [];
    let timeout = null;
    return async function (
      { state, commit, dispatch },
      { componentId, type, workspaceId, callbackManager }
    ) {
      const comConfig = state.screenComs[componentId];
      let _var = {};
      const { sourceType } = comConfig.dataConfig.dataResponse;
      if (sourceType === 'websocket') return;
      if (sourceType === 'static') {
        return Promise.resolve(comConfig.staticData).then(item => handle({ data: item, success: true }, { componentId }))
      }
      _var = await dataUtil.getApiParams(
        comConfig.dataConfig.dataResponse,
        callbackManager()
      );
      clearTimeout(timeout);
      params.push({ componentId, type, workspaceId, sourceType, params: { _var } });
      if (params.length === maxNum) {
        const part = params.splice(0);
        return postData({}, part)
          .then((res) => {
            res.forEach((item, index) => handle(item, part[index]));
            return res;
          })
          .catch((err) => {
            console.error(err);
            part.forEach((item) => handle(null, item));
          });
      } else {
        timeout = setTimeout(() => {
          if (params.length) {
            const part = params.splice(0);
            return postData({}, part)
              .then((res) => {
                res.forEach((item, index) => handle(item, part[index]));
                return res;
              })
              .catch((err) => {
                console.error(err);
                part.forEach((item) => handle(null, item));
              });
          }
        }, time);
      }

      function handle (res, { componentId }) {
        try {
          if (res && res.success) {
            dispatch('updateDataMappingErrorCompIds', {
              componentId,
              data: res.data
            });
            commit('updateComData', { componentId, data: res.data });
            return res;
          } else {
            commit('updateComData', { componentId, data: [] });
          }
        } catch (e) {
          commit('updateComData', { componentId, data: [] });
        }
      }
    };
  })(),
  async getScreenComps ({ state, commit }, data) {
    const res = await getScreenComps(null, { screenId: state.screenInfo.id });
    if (res && res.success) {
      if (data && data.indicator) {
        const { indicator } = data
        res.data[indicator.id] = indicator
        commit('updateIndicatorId', indicator.id)
      } else if (state.indicatorId) {
        res.data[state.indicatorId] = state.screenComs[state.indicatorId]
      }
      commit('initScreenComs', res.data);
    }
  },
  async getComponentInfo ({ state, commit, getters }, cid) {
    const res = await getComponentInfo({ cid }, { id: getters.currentCom.screenId || state.screenInfo.id });
    if (res && res.success) {
      commit('initComponentInfo', res.data);
    }
  },
  async getParentScreenInfo ({ commit }, screenId) {
    // 获取父面板的信息和组件
    const res = await getPanelComs({ screenIds: [screenId] });
    if (res && res.success) {
      commit('initParentScreenComs', res.data);
    }
  },
  async getChildScreenComps ({ commit }, screenArr) {
    // 获取子面板的组件
    const screenIds = screenArr.map((s) => s.id);

    const chunks = _.chunk(screenIds, 10);
    const request = chunks.map((chunk) => {
      return getPanelComs({ screenIds: chunk });
    });
    const resList = await Promise.all(request);
    let comArr = [];
    resList.forEach((res) => {
      if (res && res.success) {
        const data = res.data || [];
        comArr = comArr.concat(data);
      }
    });
    commit('initChildScreenComs', comArr);
  },
  async getScreenLayers ({ state, commit }) {
    const res = await getScreenLayers({ screenId: state.screenInfo.id });
    if (res && res.success) {
      commit('initScreenLayers', res.data);
    }
  },

  // 更新图层成功回调
  async updateScreenLayersSuccCallback ({ state, commit, dispatch }) {
    await dispatch('getScreenComps');
    await dispatch('getScreenLayers');
    commit('updateEditPanelSelect', { type: 'config', value: true });
    commit('updateLayerLoading', false);
  },

  async updateScreenLayers ({ state, commit, dispatch }, data = []) {
    commit('updateLayerLoading', true);
    const res = await updateScreen(
      { layerTree: data },
      { id: state.screenInfo.id }
    );
    if (res && res.success) {
      await dispatch('updateScreenLayersSuccCallback');
      return true;
    }
  },

  /**
   * 通过layerIds删除场景大屏页面LayerTree
   * @param {*} param0
   * @param {*} layerIds
   * @returns
   */
  async deleteScreenLayerByIds ({ state, commit, dispatch }, { layerIds = [] }) {
    commit('updateLayerLoading', true);
    const res = await deleteScreenLayerByIds({
      screenId: state.screenInfo.id,
      layerIds: layerIds
    });
    if (res && res.success) {
      await dispatch('updateScreenLayersSuccCallback');
      return true;
    }
  },

  /**
   * 通过layerIds移动大屏页面LayerTree
   * @param {*} param0
   * @param {*} layerIds
   * @returns
   */
  async moveScreenLayerByIds (
    { state, commit, dispatch },
    { layerIds = [], action = 'moveUp' }
  ) {
    commit('updateLayerLoading', true);
    const res = await moveScreenLayerByIds({
      screenId: state.screenInfo.id,
      layerIds: layerIds,
      action
    });
    if (res && res.success) {
      await dispatch('updateScreenLayersSuccCallback');
      return true;
    }
  },

  /**
   * 处理拖动插入大屏LayerTree
   * @param {*} payload
   * @returns
   */
  async dragInsertScreenLayer ({ state, commit, dispatch }, payload) {
    commit('updateLayerLoading', true);
    const res = await dragInsertScreenLayer(payload);
    if (res && res.success) {
      await dispatch('updateScreenLayersSuccCallback');
      return true;
    }
  },

  /**
   * 插入大屏LayerTree
   * @param {*} payload
   * @returns
   */
  async insertScreenLayers ({ state, commit, dispatch }, payload) {
    commit('updateLayerLoading', true);
    const res = await insertScreenLayers(payload);
    if (res && res.success) {
      await dispatch('updateScreenLayersSuccCallback');
      return true;
    }
  },

  /**
   * 更新大屏选中的LayerTree
   * @param {*} payload
   * @returns
   */
  async updateScreenSelectedLayers ({ state, commit, dispatch }, payload) {
    commit('updateLayerLoading', true);
    const res = await updateScreenSelectedLayers(payload);
    if (res && res.success) {
      await dispatch('updateScreenLayersSuccCallback');
      return true;
    }
  },

  /**
   * 创建图层分组
   * @param {*} payload
   * @returns
   */
  async createLayerGroups ({ state, commit, dispatch }, payload) {
    commit('updateLayerLoading', true);
    const res = await createLayerGroups(payload);
    if (res && res.success) {
      await dispatch('updateScreenLayersSuccCallback');
      return true;
    }
  },

  /**
   * 取消图层分组
   * @param {*} payload
   * @returns
   */
  async cancelLayerGroups ({ state, commit, dispatch }, payload) {
    commit('updateLayerLoading', true);
    const res = await cancelLayerGroups(payload);
    if (res && res.success) {
      await dispatch('updateScreenLayersSuccCallback');
      return true;
    }
  },

  async getScreenFilters ({ state, commit }, { screenId }) {
    if (!screenId) {
      screenId = state.screenInfo.id;
    }
    const res = await getFilters({ screenId });
    if (res && res.success) {
      commit('initScreenFilters', res.data);
    }
  },
  async getSystemFilters ({ commit }) {
    const res = await systemfilter();
    if (res && res.success) {
      commit('initSystemFilters', res.data);
    }
  },
  async deleteScreenFilter ({ state, commit, dispatch }, id) {
    const res = await deleteScreenFilter(
      {},
      { id: id, screenId: state.screenInfo.id }
    );
    if (res && res.success) {
      commit('deleteScreenFilter', id);
      dispatch('getScreenComps');
    }
  },
  async getCompPackages ({ commit }, type = 'screen') {
    const res = await getCompPkgList();
    if (res && res.success) {
      commit('initCompPackages', res.data);
    }
  },
  async getThemeScheme ({ commit }) {
    const res = await themeScheme();
    if (res && res.success) {
      commit('initThemeScheme', res.data);
    }
  },
  async addScreenScene ({ state, commit }, data) {
    const res = await addScene(data, { id: state.screenInfo.id });
    if (res && res.success) {
      commit('initScreenScene', res.data);
    }
  },
  async addScenePage ({ state, commit }, data) {
    const res = await addPage(data, { id: state.screenInfo.id });
    if (res && res.success) {
      commit('initScenePage', res.data);
    }
  },
  async deleteScene ({ state, commit, dispatch }, data) {
    const res = await deleteScene(data, { id: state.screenInfo.id });
    if (res && res.success) {
      await dispatch('getScreenLayers');
      commit('initDeleteScene', data.sceneId);
    }
  },
  async deletePage ({ state, commit, dispatch }, data) {
    const res = await deletePage(data, { id: state.screenInfo.id });
    if (res && res.success) {
      await dispatch('getScreenLayers');
      commit('initDeletePage', data.pageId);
    }
  },
  async updateName ({ state, commit }, data) {
    const res = await updateName(data, { id: state.screenInfo.id });
    if (res && res.success) {
      const data = (await getScreenInfo({ id: state.screenInfo.id })).data;
      commit('initUpdateName', data);
    }
  },
  updateDataMappingErrorCompIds ({ state, commit }, { componentId, data }) {
    const contrastVal = chargeData(
      state.screenComs[componentId],
      data,
      state.screenFilters
    );
    if (!contrastVal) {
      // 组件数据源映射出错
      commit('updateDataMappingErrorCompIds', { componentId, type: 'add' });
    } else {
      commit('updateDataMappingErrorCompIds', { componentId, type: 'remove' });
    }
  },
  async updateIndicatorInfo ({ commit, state }, params) {
    updateComponent(params, { id: state.screenInfo.id }).then(res => {
    })
  }
};

// mutations
const mutations = {
  updateIndicatorInfo (state, params) {
    state.screenInfo.config.thumbnail = params[0].data.icon
  },
  setState (state, object) {
    for (const key in object) {
      state[key] = object[key];
    }
  },
  // 合并面板大屏数据对象
  mergePanelScreensObj (state, panelScreens = []) {
    for (let index = 0; index < panelScreens.length; index++) {
      const panelScreen = panelScreens[index];
      state.panelScreensObj[panelScreen.id] = panelScreen;
    }
  },
  updateCoeditData (state, data) {
    state.coeditData = data;
  },
  setInheritData (state, value) {
    state.inheritData = _.cloneDeep(value);
  },
  ...snapshot.mutations,
  initScreenGroupData (state, data) {
    state.screenGroupData = data;
  },
  initChildScreensData (state, data) {
    state.childScreens = data;
  },
  initParentScreenData (state, data) {
    state.parentScreen = data;
  },
  initScreenInfo (state, value) {
    state.screenInfo = value;
    if (state.screenInfo.screenType === 'child' || state.screenInfo.isDynamicScreen) {
      state.drawerBtnState = {
        filter: {
          type: 'filter',
          name: '数据过滤器',
          icon: 'filter',
          select: false
        },
        update: {
          type: 'update',
          name: '组件升级',
          icon: 'update',
          select: false
        },
        layout: {
          type: 'layout',
          name: '布局',
          icon: 'layout',
          select: false
        },
        permission: {
          type: 'permission',
          name: '权限设置',
          icon: 'authority',
          select: false
        },
        publicDataManage: {
          type: 'publicDataManage',
          name: '公共数据',
          icon: 'other-management',
          select: false
        }
      }
    } else {
      state.drawerBtnState = {
        filter: {
          type: 'filter',
          name: '数据过滤器',
          icon: 'filter',
          select: false
        },
        update: {
          type: 'update',
          name: '组件升级',
          icon: 'update',
          select: false
        },
        layout: {
          type: 'layout',
          name: '布局',
          icon: 'layout',
          select: false
        },
        childScreens: {
          type: 'childScreens',
          name: '子屏',
          icon: 'child-screens',
          select: false
        },
        permission: {
          type: 'permission',
          name: '权限设置',
          icon: 'authority',
          select: false
        },
        publicDataManage: {
          type: 'publicDataManage',
          name: '公共数据',
          icon: 'other-management',
          select: false
        }
      }
    }
    if (state.screenInfo.screenType === 'scene') {
      const { sceneConfig, coeditId } = state.screenInfo;
      if (sceneConfig && !coeditId) {
        // 非协同场景大屏默认取值,协同场景大屏需先判断所有页面占用情况
        if (sceneConfig.length) {
          const _route = route.app.$route;
          const sceneId = _route.query.sceneId;
          if (sceneId) {
            const scene = sceneConfig.find((s) => s.sceneId === sceneId);
            if (scene) {
              state.sceneId = scene.sceneId;
              state.pageId = scene.pageList[0]?.pageId;
              return;
            }
          }
          const scene = sceneConfig[0];
          state.sceneId = scene.sceneId;
          state.pageId = scene.pageList[0]?.pageId;
        }
      }
    } else {
      state.sceneId = null;
      state.pageId = null;
    }
    const historyColor = JSON.parse(
      window.localStorage.getItem('historyColor') || '{}'
    )[value.id];
    state.historyColor = historyColor || state.historyColor;
  },
  initSceneConfig (state, value) {
    state.screenInfo = value;
  },
  updateScreenInfo (state, keyValPairs = []) {
    keyValPairs.forEach((pair) => {
      _.set(state.screenInfo, pair.key, pair.value);
    });
  },
  initScreenComs (state, data) {
    Object.values(data).forEach((item) => {
      findImageUrl(JSON.parse(item.controlConfig), replaceUrl);
      findImageUrl(item.config, replaceUrl);
    });
    state.screenComs = data;
  },
  initComponentInfo (state, data) {
    state.screenComs[data.id] = data;
  },
  initParentScreenInfo (state, data) {
    state.parentScreenInfo = data;
  },
  initParentScreenComs (state, data) {
    state.parentScreenInfo = data[0];
    const comList = data[0].comList;
    comList.forEach((com) => {
      state.parentScreenComs[com.id] = com;
    });
    state.layerMap[data[0].id] = data[0].layerTree;
  },
  initChildScreenComs (state, data) {
    const screenMap = {};
    data.forEach((item) => {
      screenMap[item.id] = _.keyBy(item.comList, 'id');
      state.layerMap[item.id] = item.layerTree;
    });
    state.childScreenComs = screenMap;
  },
  createScreenCom (state, data) {
    Vue.set(state.screenComs, data.id, data);
  },
  updateScreenComs (state, datas = []) {
    datas.forEach((d) => {
      if (state.screenComs[d.id]) {
        state.screenComs[d.id] = d;
      } else {
        Vue.set(state.screenComs, d.id, d);
      }
    });
  },
  updateScreenCom (state, params) {
    // 更新组件信息，支持单个和多个更新传参
    if (_.isNil(params)) return;
    if (!_.isArray(params)) {
      params = [params];
    }
    params.forEach(({ id, keyValPairs }) => {
      const comData = state.screenComs[id];
      if (comData && keyValPairs && keyValPairs.length) {
        keyValPairs.forEach((pair) => {
          _.set(comData, pair.key, pair.value);
        });
      }
    });
  },
  deleteScreenCom (state, id) {
    Vue.delete(state.screenComs, id);
  },
  clearScreenCom (state) {
    state.screenComs = {};
    state.comsData = {};
    state.screenLayers = [];
  },
  updateComData (state, { componentId, data }) {
    if (!_.has(state.comsData, componentId)) {
      Vue.set(state.comsData, componentId, []); // 此处刻意为之，做个浅监听
    }
    state.comsData[componentId] = data;
  },
  deleteComData (state, componentId) {
    Vue.delete(state.comsData, componentId);
  },
  initScreenLayers (state, data) {
    // 修复 普通大屏复制分组或组件到场景大屏，分组下的组件sceneId/pageId缺失问题 (脏数据兼容)
    function setSceneInfo (data) {
      data.forEach((item) => {
        if (item.type === 'group' && item.children && item.children.length) {
          const { sceneId, pageId } = item;
          item.children.forEach((child) => {
            !child.sceneId && (child.sceneId = sceneId);
            !child.pageId && (child.pageId = pageId);
            if (
              child.type === 'group' &&
              child.children &&
              child.children.length
            ) {
              setSceneInfo(child.children);
            }
          });
        }
      });
    }
    if (state.screenInfo.screenType === 'scene') {
      setSceneInfo(data);
    }
    state.screenLayers = data;
  },
  updateLayerLoading (state, flag) {
    state.layerLoading = flag;
  },
  initScreenFilters (state, data = []) {
    state.screenFilters = _.keyBy(data, 'id');
  },
  initSystemFilters (state, data = []) {
    state.systemFilters = _.keyBy(data, 'id');
  },
  updateScreenFilter (state, filter) {
    if (!filter) return;
    Vue.set(state.screenFilters, filter.id, filter);
  },
  deleteScreenFilter (state, filterId) {
    Vue.delete(state.screenFilters, filterId);
  },
  initCompPackages (state, data) {
    state.compPackges = data;
    for (const pkg of state.compPackges) {
      state.compPackgesMap[pkg.name] = pkg;
      if (pkg.children) {
        for (const child of pkg.children) {
          state.compPackgesMap[child.name] = child;
        }
      }
    }
  },
  updateLoadState (state, value) {
    state.loaded = !!value;
  },
  updateEditLoadingState (state, value) {
    state.editLoading = !!value;
  },
  updateCurrentSelectId (state, id) {
    state.currentSelectId = id;
    state.currentChildId = null;
  },
  updateCurrentChildId (state, id) {
    state.currentChildId = id;
  },
  updateEditPanelSelect (state, obj) {
    state.editPanelState[obj.type].select = !!obj.value;
  },
  updateDrawerSelect (state, obj) {
    state.drawerBtnState[obj.type].select = !!obj.value;
  },
  updateDrawerSelectAllFalse (state) {
    Object.values(state.drawerBtnState).forEach((item) => {
      item.select = false;
    });
  },
  setMenuPosition (state, { top, left }) {
    state.menuPosition.x = left;
    state.menuPosition.y = top;
  },
  initThemeScheme (state, payload) {
    state.themeScheme = payload;
  },
  updateSceneId (state, sceneId) {
    state.sceneId = sceneId;
  },
  updatePageId (state, pageId) {
    state.pageId = pageId;
  },
  initScreenScene (state, value) {
    state.screenInfo = value;
    const { sceneConfig } = state.screenInfo;
    const lastScene = sceneConfig[sceneConfig.length - 1];
    state.sceneId = lastScene.sceneId;
    state.pageId = lastScene.pageList[0].pageId;
  },
  initScenePage (state, value) {
    state.screenInfo = value;
    const { sceneConfig } = state.screenInfo;
    const sceneObj = sceneConfig.find((scene) => {
      return (
        scene.pageList.findIndex((page) => page.pageId === state.pageId) > -1
      );
    });
    if (sceneObj) {
      state.pageId = sceneObj.pageList[sceneObj.pageList.length - 1].pageId;
    }
  },
  initDeleteScene (state, sceneId) {
    const { sceneConfig } = state.screenInfo;
    const index = sceneConfig.findIndex((item) => item.sceneId === sceneId);
    const isCurrScene =
      sceneConfig[index].pageList.findIndex(
        (page) => page.pageId === state.pageId
      ) > -1;
    if (index > -1) {
      sceneConfig.splice(index, 1);
      if (isCurrScene) {
        state.pageId = sceneConfig[0].pageList[0].pageId;
      }
    }
  },
  initDeletePage (state, pageId) {
    const { sceneConfig } = state.screenInfo;
    const scene = sceneConfig.find((scene) => {
      return scene.pageList.findIndex((page) => page.pageId === pageId) > -1;
    });
    if (scene) {
      const index = scene.pageList.findIndex((page) => page.pageId === pageId);
      if (index > -1) {
        scene.pageList.splice(index, 1);
        if (state.pageId === pageId) {
          state.pageId = scene.pageList[0].pageId;
        }
      }
    }
  },
  initUpdateName (state, data) {
    state.screenInfo = data;
  },
  // 骨骼列表
  updateSkeletonList (state, arr) {
    state.skeletonList = arr;
  },
  updateNoticeList (state, notice) {
    if (Array.isArray(notice)) {
      notice = notice.filter((item) => {
        const index = state.noticeList.findIndex(
          (record) => item.id === record.id
        );
        return index < 0;
      });
      state.noticeList = state.noticeList.concat(notice);
    } else {
      state.noticeList.push(notice);
    }
  },
  updateNotice (state, notice) {
    const item = state.noticeList.find((item) => item.id === notice.id);
    item && (item.isRead = true);
  },
  deleteNotice (state, notice) {
    const index = state.noticeList.findIndex((item) => item.id === notice.id);
    if (index > -1) {
      state.noticeList.splice(index, 1);
    }
  },
  // 数据源映射错误组件列表
  updateDataMappingErrorCompIds (state, { componentId, type }) {
    const index = state.dataMappingErrorCompIds.indexOf(componentId);
    if (type === 'add') {
      if (index === -1) state.dataMappingErrorCompIds.push(componentId);
    } else if (type === 'remove') {
      if (index > -1) state.dataMappingErrorCompIds.splice(index, 1);
    }
  },
  // 更新分组
  updateManage (state, data) {
    state.manage = data;
  },
  // 设置选中分组列表下的大屏
  setSelectedManageScreen (state, selectedManageScreen = null) {
    state.selectedManageScreen = selectedManageScreen;
  },
  // 更新选中分组列表下的大屏属性
  updateSelectedManageScreenProps (state, props = {}) {
    if (!state.selectedManageScreen) {
      return;
    }
    Object.assign(state.selectedManageScreen, props);
  },
  // 报错的组件列表
  updateErrorCompIds (state, { componentId, errorInfo }) {
    const index = state.errorCompIds.findIndex(
      (item) => item.componentId === componentId
    );
    if (index === -1) {
      state.errorCompIds.push({
        componentId,
        errorInfo
      });
    }
  },
  // 清空异常监控的信息
  clearErrorInfo (state) {
    state.dataMappingErrorCompIds = [];
    state.errorCompIds = [];
  },
  // 同步历史颜色缓存
  setHistoryColor (state, { type = 'background', data }) {
    const screenId = state.screenInfo.id;
    const index = (state.historyColor[type] || []).indexOf(data);
    if (index > -1) {
      state.historyColor[type].splice(index, 1); // 选择缓存过的颜色，则将此颜色的位置更改到首位
    }
    if (state.historyColor[type].length > 20) {
      // 历史颜色缓存最多20个，超过则替换原有颜色
      state.historyColor[type].pop(); // 根据LRU算法删除最后一个颜色，也是最长时间未使用的颜色
    }
    state.historyColor[type].unshift(data); // 每次选择的颜色都放在首位，方便下次选择
    const obj = {
      [screenId]: state.historyColor
    };
    window.localStorage.setItem('historyColor', JSON.stringify(obj));
  },
  initVoiceConfigShow (state, { show }) {
    state.showVoiceConfig = show
  },
  // 设置ssr预渲染组件id
  setSsrShowCompIds (state, data) {
    state.ssrRender.showCompIds = data;
  },
  // 删除ssr预渲染组件id
  removeSsrShowCompId (state, id) {
    if (!id) return false;
    const index = state.ssrRender.showCompIds.indexOf(id);
    if (index > -1) {
      state.ssrRender.showCompIds.splice(index, 1);
    }
  },
  // 设置ssr预渲染的子大屏信息
  setSsrChildScreensInfo (state, data) {
    state.ssrRender.childScreensInfo = data;
  },
  updateIndicatorId (state, id) {
    state.indicatorId = id
  },
  // 设置子屏组件信息
  initChildScreensComps (state, data) {
    state.childScreensComps = data
  },
  // 设置父屏组件信息
  initParentScreenComps (state, data) {
    state.parentScreenComps = data
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
