import { themeInfo, updateTheme } from '@/api/theme';
import Vue from 'vue'
// state
const state = () => ({
  screenLayers: [],
  screenComs: {},
  scrollTop: 0
});

// getters
const getters = {
  comCtlConfigTree: state => {
    if (state.screenLayers.length) {
      const treeData = JSON.parse(state.screenLayers[0].controlConfig);
      return treeData
    }
  },
  comCtlConfigObj: state => {
    if (state.screenLayers.length) {
      return state.screenLayers[0].config;
    }
  }
};

// actions
const actions = {
  async initThemeInfo ({ commit, dispatch }, { packageId, id }) {
    const res = await themeInfo({ packageId, id });
    if (res && res.success) {
      commit('initThemeInfo', res.data);
    }
  },
  async updateScreenComTheme ({ state, commit }, params) {
    const newInfo = params.keyValPairs.reduce((obj, pair) => {
      obj[pair.key] = pair.value;
      return obj;
    }, {});
    const res = await updateTheme(newInfo, { id: state.screenLayers[0].id });
    if (res && res.success) {
      commit('updateScreenComTheme', params);
    }
  }
};

// mutations
const mutations = {
  changeScrollTop (state, data) {
    state.scrollTop = data
  },
  initThemeInfo (state, data) {
    state.screenLayers = [data];
    Vue.set(state.screenComs, data.id, data);
  },
  updateScreenComTheme (state, data) {
    const comData = state.screenLayers[0];
    if (comData && data.keyValPairs) {
      data.keyValPairs.forEach(pair => {
        _.set(comData, pair.key, pair.value);
      });
      Vue.set(state.screenComs, comData.id, comData)
    }
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
