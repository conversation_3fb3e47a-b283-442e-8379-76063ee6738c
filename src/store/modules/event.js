// state
import Vue from 'vue'
const state = () => ({
  fnQueue: {}
})

// actions
const actions = {
  // 设置函数
  pushFnQueue ({ state, commit }, queueList) {
    commit('pushFnQueue', queueList)
  },

  // 执行函数队列
  runFnQueue ({ state, commit }, comId) {
    const fns = state.fnQueue[comId]
    commit('clearFnQueue', comId)
    return fns
  }
}

// mutations
const mutations = {
  pushFnQueue (state, queueList) {
    queueList.forEach(item => {
      if (!state.fnQueue[item.comId]) {
        Vue.set(state.fnQueue, item.comId, []);
      }

      Vue.set(state.fnQueue, item.comId, [...state.fnQueue[item.comId], item]);
    })
  },
  clearFnQueue (state, comId) {
    Vue.set(state.fnQueue, comId, []);
  }
}

export default {
  namespaced: true,
  state,
  actions,
  mutations
}
