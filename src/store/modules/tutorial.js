import {
  tutorialCreatFolder,
  tutorialDeleteFolder,
  tutorialRenameFolder,
  tutorialGetFolderList,
  tutorialGetCover,
  tutorialGetMarks,
  tutorialGetDocList,
  tutorialGenDoc,
  tutorialUpdateDoc,
  tutorialDeleteDoc,
  tutorialGetDoc,
  tutorialSortFolder,
  tutorialSaveDraft,
  tutorialGetDraft
} from '@/api/tutorial'
import { replaceUrl } from '@/utils/base'
import Message from 'hz-message'

const state = () => ({
  folderId: 1,
  marks: '',
  title: '',
  markList: [],
  tutorialList: [],
  folderList: [],
  coverList: [],
  createFolderVisible: false,
  tutorialInfo: {}
})

const getters = {
  folderId: state => state.folderId,
  marks: state => state.marks,
  title: state => state.title,
  folderList: state => state.folderList,
  coverList: state => state.coverList,
  markList: state => state.markList,
  tutorialList: state => state.tutorialList,
  createFolderVisible: state => state.createFolderVisible,
  tutorialInfo: state => state.tutorialInfo
}

const actions = {
  async getFolderList ({ commit }) {
    commit('setFolderId', 1)
    const res = await tutorialGetFolderList({ folderId: 1 })
    if (res && res.success) {
      commit('setFolderList', res.data)
    }
  },
  async sortFolderList ({ dispatch }, { folderId, parentID, treeList }) {
    const res = await tutorialSortFolder({ folderId, parentID, treeList })
    if (res && res.success) {
      dispatch('getFolderList')
    }
  },
  async createFolder ({ dispatch, commit }, { parentID, name }) {
    const res = await tutorialCreatFolder({ parentID, name })
    if (res && res.success) {
      Message.success(`成功创建 ${name} 目录!`)
      dispatch('getFolderList')
    }
  },
  async deleteFolder ({ dispatch, commit }, { id, name }) {
    const res = await tutorialDeleteFolder({ id })
    if (res && res.success) {
      Message.success(`成功删除 ${name} 目录!`)
      dispatch('getFolderList')
    }
  },
  async renameFolder ({ dispatch, commit }, { id, folderName }) {
    const res = await tutorialRenameFolder({ id, folderName, name })
    if (res && res.success) {
      Message.success(`目录成功重命名为 ${folderName}!`)
      dispatch('getFolderList')
    }
  },
  async getCoverList ({ commit }) {
    const res = await tutorialGetCover()
    if (res && res.success) {
      commit('setCoverList', res.data)
    }
  },
  async getMarkList ({ commit, state }) {
    const res = await tutorialGetMarks({ id: state.folderId })
    if (res && res.success) {
      commit('setMarkList', res.data)
    }
  },
  async getTutorialList ({ commit, state }) {
    const data = { folderId: state.folderId, title: state.title, marks: state.marks }
    const res = await tutorialGetDocList(data)
    if (res && res.success) {
      commit('setTutorialList', res.data)
    }
  },
  async createTutorial ({ dispatch, state }, param) {
    const res = await tutorialGenDoc(param)
    if (res && res.success) {
      Message.success('成功创建文档!')
      dispatch('getTutorialList', { folderId: state.folderId, title: state.title, marks: state.marks })
    }
  },
  async updateTutorial ({ dispatch, state }, param) {
    const res = await tutorialUpdateDoc(param)
    if (res && res.success) {
      Message.success('成功修改文档!')
      dispatch('getTutorialList', { folderId: state.folderId, title: state.title, marks: state.marks })
    }
  },
  async deleteTutorial ({ dispatch, state }, { ids }) {
    const res = await tutorialDeleteDoc({ ids })
    if (res && res.success) {
      Message.success(`删除成功，共删除 ${ids.length} 篇文档!`)
      dispatch('getTutorialList', { folderId: state.folderId, title: state.title, marks: state.marks })
    }
  },
  async getTutorialById ({ commit, state }, { id }) {
    const res = await tutorialGetDoc({ id })
    if (res && res.success) {
      commit('setTutorialInfo', res.data)
    }
  },
  async saveTutorialDraft ({ commit, state }, { userId, docId, content }) {
    const res = await tutorialSaveDraft({ userId, docId, content })
    if (res && res.success) {
      commit('setTutorialDraft', res.data)
    }
  },
  async getTutorialDraft ({ commit, state }, { userId, docId }) {
    const res = await tutorialGetDraft({ userId, docId })
    if (res && res.success) {
      commit('setTutorialDraft', res.data)
      return res.data
    }
    return ''
  }
}

const mutations = {
  setFolderId (state, folderId) {
    state.folderId = folderId
  },
  setTitle (state, title) {
    state.title = title
  },
  setMarks (state, marks) {
    state.marks = marks
  },
  setFolderList (state, folderList) {
    state.folderList = folderList
  },
  setCreateFolderVisible (state, boolean) {
    state.createFolderVisible = boolean
  },
  setCoverList (state, coverList) {
    state.coverList = coverList.reduce((acc, cur) => {
      acc.push(replaceUrl(process.env.VUE_APP_SERVER_URL + cur.url))
      return acc
    }, [])
  },
  setMarkList (state, markList) {
    const result = markList
    result.unshift('全部')
    state.markList = result
  },
  setTutorialList (state, tutorialList) {
    state.tutorialList = tutorialList
  },
  setTutorialInfo (state, tutorialInfo) {
    state.tutorialInfo = tutorialInfo
  },
  setTutorialDraft (state, tutorialDraft) {
    state.draft = tutorialDraft
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
