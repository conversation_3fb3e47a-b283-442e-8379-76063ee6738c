import store from '../index'

export default {
  state: {
    snapshotData: [], // 编辑器快照数据
    snapshotIndex: -1, // 快照索引
    undoLimit: 20 // 撤销步数限制
  },

  getters: {
    cursnapshotData: state => {
      return state.snapshotData[state.snapshotIndex]
    }
  },

  mutations: {
    // 添加新的快照
    recordSnapshot (state, params) {
      if (params.type === 'delete') {
        state.snapshotData[++state.snapshotIndex] = _.cloneDeep(params);
      } else {
        const cloneParams = _.cloneDeep(params);
        const cursnapshotData = state.snapshotData[state.snapshotData.length - 1];
        // 如果当前没变化 则不添加快照
        if (JSON.stringify(cursnapshotData) === JSON.stringify(cloneParams)) return
        state.snapshotData[++state.snapshotIndex] = cloneParams;
      }
      if (state.snapshotData.length >= state.undoLimit) {
        state.snapshotData = state.snapshotData.slice(1)
        state.snapshotIndex--;
      }
      if (state.snapshotData.length - 1 > state.snapshotIndex) {
        // 在 undo 过程中，当前步骤大于总步骤时，添加新快照时要把它后面的快照清理掉
        state.snapshotData = state.snapshotData.slice(0, state.snapshotIndex + 1)
      }
      // console.log(state.snapshotIndex, state.snapshotData);
    },

    undo (state) { // 撤销
      if (state.snapshotIndex < 0) return
      const cursnapshotData = state.snapshotData[state.snapshotIndex];
      if (cursnapshotData && cursnapshotData.type === 'delete') {
        state.snapshotIndex--;
        const start = +new Date()
        store.commit('editor/updateEditLoadingState', true)
        store.dispatch('editor/updateScreenLayers', cursnapshotData.originLayers)
        store.dispatch('editor/updateScreenCom', cursnapshotData.data).then(() => {
          const end = +new Date()
          if (end - start < 1000) {
            const timer = setTimeout(() => {
              store.commit('editor/updateEditLoadingState', false)
              clearTimeout(timer)
            }, 1000 - (end - start))
          } else {
            store.commit('editor/updateEditLoadingState', false)
          }
        })
        return
      }
      if (state.snapshotIndex >= 1) {
        if (cursnapshotData.length !== state.snapshotData[state.snapshotIndex - 1].length || _.difference(_.map(cursnapshotData, 'id'), _.map(state.snapshotData[state.snapshotIndex - 1], 'id')).length) {
          state.snapshotIndex = state.snapshotIndex - 2;
        } else {
          state.snapshotIndex--;
        }
        store.dispatch('editor/updateScreenCom', _.cloneDeep(state.snapshotData[state.snapshotIndex]))
      }
      // console.log(state.snapshotData, state.snapshotIndex)
    },

    redo (state) { // 重做
      let cursnapshotData = state.snapshotData[state.snapshotIndex];
      if (state.snapshotData[state.snapshotIndex + 1] && state.snapshotData[state.snapshotIndex + 1].type === 'delete') {
        state.snapshotIndex++;
        cursnapshotData = state.snapshotData[state.snapshotIndex];
        const start = +new Date()
        store.commit('editor/updateEditLoadingState', true)
        store.dispatch('editor/updateScreenLayers', cursnapshotData.layers)
        const data = _.cloneDeep(cursnapshotData.data)
        data.forEach(item => {
          item.keyValPairs[0].isDelete = true
        })
        store.dispatch('editor/updateScreenCom', data).then(() => {
          const end = +new Date()
          if (end - start < 1000) {
            const timer = setTimeout(() => {
              store.commit('editor/updateEditLoadingState', false)
              clearTimeout(timer)
            }, 1000 - (end - start))
          } else {
            store.commit('editor/updateEditLoadingState', false)
          }
        })
        return
      }
      if (state.snapshotIndex < state.snapshotData.length - 1) {
        if (cursnapshotData.length !== state.snapshotData[state.snapshotIndex + 1].length || _.difference(_.map(cursnapshotData, 'id'), _.map(state.snapshotData[state.snapshotIndex + 1], 'id')).length) {
          state.snapshotIndex = state.snapshotIndex + 2;
        } else {
          state.snapshotIndex++;
        }
        store.dispatch('editor/updateScreenCom', _.cloneDeep(state.snapshotData[state.snapshotIndex]))
      }
      // console.log(state.snapshotData, state.snapshotIndex)
    }
  }
}
