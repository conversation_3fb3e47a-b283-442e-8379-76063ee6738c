import { customTemp } from '@/utils/customTemp'
import {
  createCustom,
  updateCustom
} from '@/api/custom';
import { uuid } from '@/utils/base';

// state
const state = () => ({
  echartsId: uuid('echarts'),
  custom_id: null,
  compConfig: {
    name: '测试自定义',
    type: 'custom-all-ceshi',
    width: 400,
    height: 200,
    version: ''
  },
  editorWidth: 0, // 编辑器宽度
  iframeWidth: 0, // 预览区域宽度
  preprocessor: ['HTML', 'CSS', 'JavaScript'], // 预处理器类型
  currentTab: 'HTML',
  currentTemp: '',
  instanceCode: {
    HTML: {
      language: 'html',
      content: '',
      resources: []
    },
    CSS: {
      language: 'css',
      content: '',
      resources: []
    },
    JavaScript: {
      language: 'javascript',
      content: '',
      resources: [],
      resizeContent: ''
    }
  },
  dataCode: [], // 数据
  configCode: {}, // 配置项
  callbacks: [], // 回调
  instanceSetting: {
    delayTime: 500,
    autoExecute: true,
    autoComplete: true,
    tabIndent: true,
    lint: true,
    lineWrap: true,
    indentSpaces: 2,
    fontFamily: 'Consolas',
    fontSize: 14,
    headTags: ''
  }
});

// mutations
const mutations = {
  infoData: (state, data) => {
    state.custom_id = data.id
    state.compConfig.name = data.compConfig.chartConfig.cn_name
    state.compConfig.type = data.compConfig.chartConfig.type
    state.compConfig.version = data.compConfig.version
    state.instanceCode = _.cloneDeep(data.instanceCode)
    if (data.currentTemp === 'ECharts') {
      state.instanceCode.HTML.content = ''
    }
    state.dataCode = data.compConfig.chartConfig.data.source || []
    state.configCode = data.compConfig.chartConfig.config || {}
    state.callbacks = data.compConfig.chartConfig.callbacks || []
    state.currentTemp = data.currentTemp || 'dom'
    state.compConfig.width = data.compConfig.chartConfig.width - 0 || 400;
    state.compConfig.height = data.compConfig.chartConfig.height - 0 || 200;
  },
  handleEditorW: (state, width) => {
    state.editorWidth = width
  },
  handleIframeW: (state, width) => {
    state.iframeWidth = width
  },
  handleCurrentTab: (state, tab) => {
    state.currentTab = tab
  },
  handleInstanceCode: (state, codeContent) => {
    state.instanceCode[codeContent.codeMode].content = codeContent.code
  },
  setInstanceCode: (state, data) => {
    state.instanceCode = data
    if (state.currentTemp === 'ECharts') {
      state.instanceCode.JavaScript.resizeContent = `this.myChart.resize({
        width: width,
        height: height,
      })`
    } else {
      state.instanceCode.JavaScript.resizeContent = ''
    }
  },
  setCodeResource: (state, { type, resources }) => {
    state.instanceCode[type].resources = resources
  },
  handleCurrentTemp: (state, temp) => {
    state.currentTemp = temp
  },
  handleDataCode: (state, codeContent) => {
    state.dataCode = codeContent
  },
  handleconfigCode: (state, codeContent) => {
    state.configCode = codeContent
  },
  handlecallbackCode: (state, codeContent) => {
    state.callbacks = codeContent
  },
  initCustomId: (state, id) => {
    state.custom_id = id;
  },
  setCompWH: (state, { width, height }) => {
    state.compConfig.width = width - 0;
    state.compConfig.height = height - 0;
  }
};

const actions = {
  async createCustom ({ state, commit }) {
    const cmConfig = customTemp(state)
    const res = await createCustom(cmConfig)
    if (res && res.success && res.data) {
      commit('initCustomId', res.data.id);
    }
  },

  async updateCustom ({ state }, customId) {
    const cmConfig = customTemp(state)
    await updateCustom(cmConfig, { id: customId })
  }

}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
