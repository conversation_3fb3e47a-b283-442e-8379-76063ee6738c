// 根级 store

// state
const state = {
  theme: window.localStorage.getItem('theme') || 'dark',
  browserVisible: false
};

// getters
const getters = {

};

// actions
const actions = {};

// mutations
const mutations = {
  changeTheme (state, theme) {
    state.theme = theme;
  },
  changeBrowserVisible (state, data) {
    state.browserVisible = data;
  }
};

export default {
  state,
  getters,
  actions,
  mutations
}
