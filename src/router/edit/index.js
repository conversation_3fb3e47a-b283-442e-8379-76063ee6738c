import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'
import { getRoleType } from '@/api/user'

// 解决重复路由报错问题
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push (location) {
  return originalPush.call(this, location).catch((err) => err);
};

const Index = () => import('../../views/Index')
const Login = () => import('../../views/Login')
const LoginLocal = () => import('../../views/LoginLocal')
const Register = () => import('../../views/Register')
const ResetPassword = () => import('../../views/ResetPassword')
const WorkSpaceIndex = () => import('../../views/workspace/Index')
const Screen = () => import('../../views/workspace/Screen')
const Data = () => import('../../views/workspace/Data')
const Component = () => import('../../views/workspace/Component')
const SettingDoc = () => import('../../views/setting/SettingDoc')
const ScreenIndex = () => import('../../views/screen/Index')
const Create = () => import('../../views/screen/Create')
const Edit = () => import('../../views/screen/Edit')
const IndicatorLibraryEdit = () => import('../../views/screen/IndicatorLibraryEdit')
const Preview = () => import('../../views/screen/Preview')
const MobilePreview = () => import('../../views/screen/MobilePreview')
const Control = () => import('../../views/screen/Control')
const Resources = () => import('../../views/workspace/Resources')
const Theme = () => import('../../views/theme/Theme')
const ThemeEdit = () => import('../../views/theme/ThemeEdit')
const MediaLabel = () => import('../../views/setting/MediaLabel')
const ThemePreview = () => import('../../views/theme/ThemePreview')
const Setting = () => import('../../views/setting/Index');
const SystemFilter = () => import('../../views/setting/SystemFilter')
const ImportTpl = () => import('../../views/setting/ImportTpl')
const packCom = () => import('../../views/setting/packCom')
const publishCom = () => import('../../views/setting/publishCom')
const ThemeSchemeManage = () => import('../../views/setting/ThemeSchemeManage')
const CustomComp = () => import('../../views/customcomp/CustomEdit')
const Skeleton = () => import('../../views/setting/Skeleton')
const SettingTools = () => import('../../views/setting/SettingTools')
const Tutorial = () => import('../../views/workspace/Tutorial')
const TutorialPreview = () => import('../../views/tutorial/TutorialPreview')
const NewTutorial = () => import('../../views/workspace/NewTutorial')
const CustomView = () => import('../../views/customcomp/CustomScreen')

// const ScreentplIndex = () => import('../views/screentpl/Index');
const ScreentplCreate = () => import('../../views/screentpl/Create')
const ScreentplPreview = () => import('../../views/screentpl/Preview')
const NotFound = () => import('../../views/NotFound')

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/index'
  },
  {
    path: '/index',
    name: 'index',
    component: Index
  },
  {
    path: '/login',
    name: 'login',
    component: Login
  },
  {
    path: '/loginlocal',
    name: 'LoginLocal',
    component: LoginLocal
  },
  {
    path: '/register',
    name: 'register',
    component: Register
  },
  {
    path: '/reset-password',
    name: 'resetPassword',
    component: ResetPassword
  },
  {
    path: '/theme',
    name: 'theme',
    component: Theme
  },
  {
    path: '/setting/media-label',
    name: 'MediaLabel',
    component: MediaLabel
  },
  {
    path: '/theme-edit',
    name: 'themeEdit',
    component: ThemeEdit
  },
  {
    path: '/Theme-preview',
    name: 'themePreview',
    component: ThemePreview
  },
  {
    path: '/custom-preview',
    name: 'customPreview',
    component: CustomView
  },
  {
    path: '/setting',
    name: 'Setting',
    component: Setting
  },
  {
    path: '/setting/filter',
    name: 'SystemFilter',
    component: SystemFilter
  },
  {
    path: '/setting/doc',
    name: 'SettingDoc',
    component: SettingDoc
  },
  {
    path: '/screen/preview/:screenId',
    name: 'screen/preview',
    component: Preview
  },
  {
    path: '/tutorial',
    name: 'Tutorial',
    component: NewTutorial
  },
  {
    path: '/tutorial/preview',
    name: 'TutorialPreview',
    component: TutorialPreview
  },
  {
    path: '/component/customcomp/:customId',
    name: 'CustomComp',
    component: CustomComp
  },
  {
    path: '/setting/theme-scheme',
    name: 'ThemeSchemeManage',
    component: ThemeSchemeManage
  },
  {
    path: '/setting/skeleton',
    name: 'Skeleton',
    component: Skeleton
  },
  {
    path: '/setting/tools',
    name: 'Tools',
    component: SettingTools
  },
  {
    path: '/setting/importTpl',
    name: 'importtpl',
    component: ImportTpl
  },
  {
    path: '/setting/packCom',
    name: 'packCom',
    component: packCom
  },
  {
    path: '/setting/publishCom',
    name: 'publishCom',
    component: publishCom
  },
  {
    path: '/screentpl',
    name: 'screentpl',
    component: ScreentplCreate
  },
  {
    path: '/screentpl/preview',
    name: 'screentplPreview',
    component: ScreentplPreview
  },
  {
    path: '/workspace/:workspaceId',
    component: WorkSpaceIndex,
    children: [
      {
        path: '',
        name: 'workspace/index',
        component: Screen,
        meta: { routeName: '/' }
      },
      {
        path: 'screen',
        name: 'workspace/screen',
        component: Screen,
        meta: { routeName: '/screen' }
      },
      {
        path: 'data',
        name: 'workspace/data',
        component: Data,
        meta: { routeName: '/data' }
      },
      {
        path: 'resources',
        name: 'workspace/resources',
        component: Resources,
        meta: { routeName: '/resources' }
      },
      {
        path: 'component',
        name: 'workspace/component',
        component: Component,
        meta: { routeName: '/component' }
      },
      {
        path: 'doc',
        name: 'workspace/doc',
        component: Tutorial,
        meta: { routeName: '/doc' }
      }
    ]
  },
  {
    path: '/screen',
    name: 'screen',
    component: ScreenIndex,
    children: [
      {
        path: 'create',
        name: 'screen/create',
        component: Create
      },
      {
        path: 'edit/:screenId',
        name: 'screen/edit',
        component: Edit
      },
      {
        path: 'indicator/edit/:id',
        name: 'screen/indicator/edit',
        component: IndicatorLibraryEdit
      },
      {
        path: 'mobile-preview/:screenId',
        name: 'screen/mobilepreview',
        component: MobilePreview
      },
      {
        path: 'share/:shareToken',
        name: 'screen/share',
        meta: { isShare: true },
        component: Preview
      },
      {
        path: 'control/:screenId',
        name: 'screen/control',
        component: Control
      }
    ]
  },
  {
    path: '*',
    name: 'notfound',
    component: NotFound
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

router.beforeEach(async (to, from, next) => {
  try {
    const userAgent = window.navigator.userAgent
    const browserVisible = window.sessionStorage.getItem('browser_visible')
    const localLogin = JSON.parse(window.localStorage.getItem('localLogin') || 'false')
    if (/[Cc]hrome\/\d+/.test(userAgent) && browserVisible !== '1') {
      const tempArray = /([Cc]hrome)\/(\d+)/.exec(userAgent)
      if (tempArray[2] - 0 < 80) {
      // console.log('浏览器版本过低');
        store.commit('changeBrowserVisible', true)
      }
    }
    if (to.name.startsWith('workspace/') || to.name === 'Setting') {
      if (localLogin) {
        store.commit('user/setRoleType', 2)
      } else {
        const res = await getRoleType()
        if (res.success && res.data) {
          const roleType = res.data.length ? res.data[0].role_type : 3
          store.commit('user/setRoleType', roleType)
          if (to.name === 'Setting' && roleType !== 1) { // 非超级管理员，不允许进入管理界面
            next('/noaccess')
          }
        }
      }
    }
    if (from.path !== '/' && to.path !== '/noaccess' && to.path !== '/notfound' && from.path !== '/noaccess' && from.path !== '/notfound') {
      window.localStorage.setItem('former_path', from.path)
      window.localStorage.setItem('toer_path', to.path)
    } else if (from.path === '/') { // 手动修改url导致页面刷新时
      window.localStorage.setItem('former_path', window.localStorage.toer_path)
    }
    next()
  } catch (error) {
    if (to.name === 'Setting') { // 非超级管理员，不允许进入管理界面
      next('/noaccess')
    } else next()
  }
})

export default router
