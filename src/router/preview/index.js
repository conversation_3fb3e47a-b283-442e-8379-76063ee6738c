import VueRouter from 'vue-router'
import Vue from 'vue'

const Preview = () => import('../../views/screen/Preview')
Vue.use(VueRouter);
const routes = [{
  path: '/:screenId',
  name: 'screen/preview',
  component: Preview
}, {
  path: '/screen/share/:shareToken',
  name: 'screen/share',
  meta: { isShare: true },
  component: Preview
}]

const router = new VueRouter({
  mode: 'hash',
  // base: '/preview',
  routes
})
window.router = router;
export default router;
