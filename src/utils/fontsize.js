import each from 'lodash/each'
import extend from 'lodash/extend'

function getSizeType (width, height) {
  const minInterval = Math.floor(Math.min(width, height) / 3)
  let type = ''
  if (Math.abs(width - height) < minInterval) {
    type = 'square'
  } else {
    if (width > 4.5 * height) {
      type = 'longHorRectangle'
    } else if (width <= 4.5 * height && width > 2 * height) {
      type = 'middleHorRectangle'
    } else if (width <= 2 * height && width > height) {
      type = 'horRectangle'
    } else if (height > 3 * width) {
      type = 'longVerRectangle'
    } else if (height < 3 * width && width < height) {
      type = 'verRectangle'
    } else {
      type = 'other'
    }
  }
  return type
}

let fontSizeHelper = null

function getFontSizeHelper (customStyle = {}) {
  // if(!fontSizeHelper){
  fontSizeHelper = document.createElement('div')
  let cssText = 'position: absolute;top: -99999px;font-size: 12px;box-sizing: border-box;line-height:1;padding: 0;border: none;margin: 0;'

  each(customStyle, (value, key) => {
    cssText += key + ': ' + value + ';'
  })

  fontSizeHelper.style.cssText = cssText

  document.body.appendChild(fontSizeHelper)
  // }
  return fontSizeHelper
}

let fontSizeVerHelper = null

function getVerFontSizeHelper () {
  if (!fontSizeVerHelper) {
    fontSizeVerHelper = document.createElement('div')
    fontSizeVerHelper.style.cssText = 'position: absolute;top: -99999px;font-size: 12px;box-sizing: border-box;line-height:1;writing-mode: vertical-lr;'
    document.body.appendChild(fontSizeVerHelper)
  }
  return fontSizeVerHelper
}

function getFullHeightFitSize (text, width, height) {
  const helper = getFontSizeHelper()
  helper.textContent = text
  let fontSize = 12
  helper.style.fontSize = fontSize + 'px'
  while (helper.offsetWidth < width && helper.offsetHeight < height) {
    if (fontSize >= 100) {
      break
    }
    helper.style.fontSize = ++fontSize + 'px'
  }

  return {
    fontSize: fontSize,
    width: helper.offsetWidth,
    height: helper.offsetHeight
  }
}

function getFitSize (text, width, height, fontHeight = 1, rowNumber = 1, customStyle = {}) {
  const lineHeight = height / rowNumber
  let fontSize = lineHeight * fontHeight

  const helper = getFontSizeHelper(extend({
    'line-height': lineHeight + 'px',
    width: width + 'px'
  }, customStyle))
  helper.textContent = text || 's' // 适配没有内容的节点
  const borderSize = (customStyle.border || '').match(/(\d+)px/)
  const limitHeight = height + (borderSize && borderSize.length ? parseFloat(borderSize[1]) * 2 : 0)

  helper.style.fontSize = fontSize + 'px'
  while (helper.offsetHeight > limitHeight) {
    if (fontSize < 12) {
      break
    }
    helper.style.fontSize = --fontSize + 'px'
  }
  /**
   * 计算文字宽度
   */
  const helper2 = getFontSizeHelper(extend({
    'line-height': lineHeight + 'px'
  }, customStyle))
  helper2.textContent = text || 's' // 适配没有内容的节点

  helper2.style.fontSize = fontSize + 'px'
  while (helper2.offsetHeight > limitHeight) {
    if (fontSize < 12) {
      break
    }
    helper2.style.fontSize = --fontSize + 'px'
  }

  const sizeObj = {
    fontSize: fontSize,
    lineHeight: lineHeight,
    width: helper.offsetWidth,
    height: helper.offsetHeight,
    textWidth: helper2.offsetWidth
  }

  helper.parentNode.removeChild(helper)
  helper2.parentNode.removeChild(helper2)

  return sizeObj
}

function getVerFitSize (text, width, height) {
  const helper = getVerFontSizeHelper()
  helper.textContent = text
  let fontSize = 12
  helper.style.fontSize = fontSize + 'px'
  while (helper.offsetWidth < width && helper.offsetHeight < height) {
    if (fontSize >= 100) {
      break
    }
    helper.style.fontSize = ++fontSize + 'px'
  }

  return {
    fontSize: fontSize,
    width: helper.offsetWidth,
    height: helper.offsetHeight
  }
}

export {
  getSizeType,
  getFontSizeHelper,
  getFitSize,
  getVerFitSize,
  getFullHeightFitSize
}
