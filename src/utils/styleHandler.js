/**
 * <AUTHOR>
 * @description 生成style对象或字符串
 */

export function formatBackground (background = {}, object = false) {
  if (background.show === false) return ''

  let backgroundStyle, backgroundObjectStyle
  switch (background.type) {
    case 'pure':
      object ? backgroundObjectStyle = {
        backgroundColor: background.pure
      }
        : backgroundStyle = {
          'background-color': background.pure
        }
      break
    case 'gradient': {
      const {
        gradient = {}
      } = background
      object
        ? backgroundObjectStyle = {
          backgroundImage: `linear-gradient(${gradient.deg}deg, ${gradient.start}, ${gradient.end})`
        }
        : backgroundStyle = {
          'background-image': `linear-gradient(${gradient.deg}deg, ${gradient.start}, ${gradient.end})`
        }
      break
    }
    case 'image': {
      const { image = {} } = background
      const { changeColor: imageChangeColor = {} } = image
      object
        ? backgroundObjectStyle = {
          backgroundSize: image.size,
          backgroundPosition: `${image.positionX} ${image.positionY}`,
          backgroundRepeat: image.repeat
        }
        : backgroundStyle = {
          'background-size': image.size,
          'background-position': `${image.positionX} ${image.positionY}`,
          'background-repeat': image.repeat
        }
      const imageList = [`url(${image.url})`]
      if (imageChangeColor && imageChangeColor.show) {
        imageList.push(formatGradientValue(imageChangeColor.data))
        object
          ? backgroundObjectStyle.mixBlendMode = imageChangeColor.mixMode
          : backgroundStyle['mix-blend-mode'] = imageChangeColor.mixMode
      }
      object
        ? backgroundObjectStyle.backgroundImage = imageList.join(',')
        : backgroundStyle['background-image'] = imageList.join(',')

      break
    }
  }
  return object ? backgroundObjectStyle
    : backgroundStyle ? formatStyle(backgroundStyle) : ''
}

export function formatBorder (border = {}, object = false) {
  if (border.show === false) {
    return ''
  }

  const borderStyle = {
    'border-style': border.style,
    'border-color': border.color,
    'border-width': `${border.width}px`
  }
  const borderObjectStyle = {
    borderStyle: border.style,
    borderColor: border.color,
    borderWidth: `${border.width}px`
  }
  return object ? borderObjectStyle : formatStyle(borderStyle)
}

export function formatPadding (padding = {}, object = false) {
  if (Object.keys(padding).length) {
    return !object
      ? `padding: ${padding.top}px ${padding.right}px ${padding.bottom}px ${padding.left}px;`
      : {
        paddingTop: object.top + 'px',
        paddingLeft: object.left + 'px',
        paddingRight: object.right + 'px',
        paddingBottom: object.bottom + 'px'
      }
  }
}

/**
 * @description handler
 * @param boxStyle
 * @return {string}
 */
const formatStyle = (boxStyle = {}) => {
  return Object.keys(boxStyle).map(key => {
    const value = boxStyle[key]
    if (value !== undefined) {
      return key + ': ' + boxStyle[key]
    } else {
      return ''
    }
  }).join(';') + ';'
}

/**
 * @description handler
 * @param.gradient
 * @return {string}
 */
const formatGradientValue = (gradient = {}) => {
  return gradient.type === 'radial-gradient'
    ? `radial-gradient(${gradient.shape}, ${gradient.start} ${gradient.startScale}%, ${gradient.end} ${gradient.endScale}%)`
    : `linear-gradient(${gradient.deg}deg, ${gradient.start} ${gradient.startScale}%, ${gradient.end} ${gradient.endScale}%)`
}
