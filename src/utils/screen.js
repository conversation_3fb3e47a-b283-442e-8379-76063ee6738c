/*
 * @Description: 这是***页面
 * @Date: 2023-04-11 14:25:42
 * @Author: chen<PERSON><PERSON>
 * @LastEditors: chenxingyu
 */
import { replaceUrl, findImageUrl } from './base'

/**
 * 大屏数据处理图片url
 * @param {*} screenData
 */
export const screenDataImageReplaceUrl = (screenData) => {
  Object.keys(screenData).forEach(key => {
    if (key !== 'filter' && key !== 'layers') {
      const item = screenData[key]
      findImageUrl(item, replaceUrl)
    }
  })
}
