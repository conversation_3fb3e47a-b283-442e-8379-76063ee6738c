/* eslint-disable */
import CodeMirror from 'codemirror'

// 语言高亮
import 'codemirror/mode/javascript/javascript'
import 'codemirror/mode/pug/pug'
import 'codemirror/mode/css/css'
import 'codemirror/mode/sass/sass'
import 'codemirror/mode/stylus/stylus'
import 'codemirror/mode/coffeescript/coffeescript'
import 'codemirror/mode/htmlmixed/htmlmixed';
// 代码收缩
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/fold/indent-fold.js';
import 'codemirror/addon/fold/comment-fold.js';
// 标签匹配与编辑配置
import 'codemirror/addon/edit/closetag'
import 'codemirror/addon/edit/closebrackets'
import 'codemirror/addon/edit/matchtags'
import 'codemirror/addon/edit/matchbrackets'
import 'codemirror/addon/edit/continuelist'
import 'codemirror/addon/edit/trailingspace'
// 智能提示
import 'codemirror/addon/hint/javascript-hint'
import 'codemirror/addon/hint/html-hint'
import 'codemirror/addon/hint/css-hint'
import 'codemirror/addon/hint/show-hint'
import 'codemirror/addon/hint/anyword-hint'
import 'codemirror/addon/hint/show-hint.css';
// 语法检查
import 'codemirror/addon/lint/css-lint'
import 'codemirror/addon/lint/html-lint'
import 'codemirror/addon/lint/javascript-lint.js'
import 'codemirror/addon/lint/coffeescript-lint'
import 'codemirror/addon/lint/lint.js'
// 标签匹配与编辑配置
import 'codemirror/addon/edit/closetag'
import 'codemirror/addon/edit/closebrackets'
import 'codemirror/addon/edit/matchtags'
import 'codemirror/addon/edit/matchbrackets'
import 'codemirror/addon/edit/continuelist'
import 'codemirror/addon/edit/trailingspace'
// 搜索
import 'codemirror/addon/search/search'
import 'codemirror/addon/search/searchcursor'
import 'codemirror/addon/dialog/dialog'
import 'codemirror/addon/search/jump-to-line'
import 'codemirror/addon/search/matchesonscrollbar'
import 'codemirror/addon/search/match-highlighter'
// 快捷键
import 'codemirror/keymap/sublime'
// 其它
import 'codemirror/addon/comment/comment'
import 'codemirror/addon/selection/active-line'
import 'codemirror/addon/scroll/scrollpastend'
import 'codemirror/addon/comment/continuecomment'
import 'codemirror/addon/runmode/colorize'

// 导入全局hint
import { JSHINT } from 'jshint'
import { CSSLint } from 'csslint'
import { HTMLHint } from 'htmlhint'
window.JSHINT = JSHINT
window.CSSLint = CSSLint
window.HTMLHint = HTMLHint

import emmet from '@emmetio/codemirror-plugin';

emmet(CodeMirror);

/**
 * Configure editor features and options
 * 配置编辑器功能及选项
 * @param {String} mode 语言
 */
function codemirrorConfig (mode = '', currentTemp='') {
  const codeOptions = {
    lineNumbers: true, // 是否在编辑器左侧显示行号
    line: true,
    tabSize: 2, // tab缩进数
    mode: '', // 语言
    matchTags: { bothTags: true }, // 匹配标签
    lineWiseCopyCut: true,
    indentWithTabs: true,
    indentUnit: 2,
    lineWrapping: true, // 超出换行
    autofocus: true,
    keyMap: 'sublime',
    styleActiveLine: true,
    scrollPastEnd: true, // 在编辑器底部插入一个编辑器同等高度的空白
    continueComments: true,
    lint: true, // 代码规范检测
    selfContain: true,
    showReplace: false, // 是否显示replace
    showTrailingSpace: true, // 无意义的空格会添加下滑红线
    theme: 'default', // 编辑器样式的主题
    foldGutter: true,
    gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
    autoCloseTags: true,
    autoCloseBrackets: true,
    matchBrackets : true,//括号匹配
    readOnly: mode === 'HTML' && currentTemp === 'ECharts' ? true : false,
    highlightSelectionMatches: {
      showToken: true,
      annotateScrollbar: true,
    },
    hintOptions: {
      completeSingle: false,
      alignWithWord: false,
    },

    extraKeys: {
      "Ctrl-Q": function(cm){ cm.foldCode(cm.getCursor()); },
      'Tab': 'emmetExpandAbbreviation',
      'Esc': 'emmetResetAbbreviation',
      'Enter': 'emmetInsertLineBreak'
    }
  }
  return codeOptions

}
export default codemirrorConfig
