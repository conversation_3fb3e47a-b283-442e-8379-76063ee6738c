export default class Rect {
  constructor (x, y, width, height) {
    if (Array.isArray(x)) {
      if (!x.length) {
        throw new Error('数组为空，无法构造矩形！');
      }
      const rectArr = x.map(rect => new Rect(rect.x, rect.y, rect.w, rect.h));
      const r0 = rectArr[0];
      let xmin = r0.x;
      let ymin = r0.y;
      let xmax = r0.x1;
      let ymax = r0.y1;
      for (let i = 1, len = rectArr.length; i < len; i++) {
        const ri = rectArr[i];
        xmin = Math.min(ri.x, xmin);
        ymin = Math.min(ri.y, ymin);
        xmax = Math.max(ri.x1, xmax);
        ymax = Math.max(ri.y1, ymax);
      }

      this.x = xmin;
      this.y = ymin;
      this.width = Math.abs(xmax - xmin);
      this.height = Math.abs(ymax - ymin);
      this.x1 = xmax;
      this.y1 = ymax;
    } else {
      if (x == null || y == null || width == null || height == null) {
        throw new Error('参数不足，无法构造矩形！')
      }
      this.x = x;
      this.y = y;
      this.width = Math.abs(width);
      this.height = Math.abs(height);
      this.x1 = this.x + this.width;
      this.y1 = this.y + this.height;
    }
  }
}
