export function getSvgOptionsByContent (string = '') {
  const options = []
  const rxp = /\[\[.*?\]\]/g
  const namesArr = string.match(rxp)

  let itemLen, fileName

  namesArr.forEach(item => {
    itemLen = item.length
    fileName = item.slice(2, itemLen - 2)
    options.push({
      value: fileName,
      label: fileName
    })
  })

  return options
}

export function formatSvg (string = '', componentId) {
  // 换行符替换
  string = string.replace(/\n/g, ' ')
  const svgNameOptions = getSvgOptionsByContent(string)
  let content = `<svg id="insert-custom-svg-${componentId}" xmlns="http://www.w3.org/2000/svg" style="width:0;height:0;visibility:hidden;">`

  const rxp = /\[\[.*?\]\]/g
  let svgArr = string.split(rxp)

  if (!svgArr) {
    svgArr = []
  }

  // 删除最后一个空值
  svgArr.pop()

  let itemSvg, itemWidth, itemHeight, itemPath, itemName
  const wRxp = /width=".*?"/
  const hRxp = /height=".*?"/
  const pathRxp = /\<path.*?\/\>/

  for (const i in svgArr) {
    const wArr = svgArr[i].match(wRxp)
    const hArr = svgArr[i].match(hRxp)
    const pathArr = svgArr[i].match(pathRxp)

    if (wArr && wArr.length) {
      itemWidth = Number(wArr[0].split('=')[1].replace(/\s/g, '').replace(/"/g, ''))
    } else {
      itemWidth = 20
    }

    if (hArr && hArr.length) {
      itemHeight = Number(hArr[0].split('=')[1].replace(/\s/g, '').replace(/"/g, ''))
    } else {
      itemHeight = 20
    }

    if (pathArr && pathArr.length) {
      itemPath = pathArr[0]
    } else {
      itemPath = ''
    }

    itemName = svgNameOptions[i].value

    itemSvg = `<symbol viewBox="0 0 ${itemWidth} ${itemHeight}" id="icon-${itemName}">
            ${itemPath}
          </symbol>`
    content += itemSvg
  }

  content += '</svg>'

  return content
}

export function insertSvgToHtml (string = '') {
//   const customSvg = document.getElementById(`insert-custom-svg-${componentId}`)
//   if(customSvg){
//     document.body.removeChild(customSvg)
//   }
  document.body.insertAdjacentHTML('beforeend', string)
}

export function formatIconFontSvg (string = '', componentId) {
  const svgNameOptions = getSvgOptionsByIconFontContent(string)
  let content = `<svg id="insert-custom-svg-${componentId}" xmlns="http://www.w3.org/2000/svg" style="width:0;height:0;visibility:hidden;">`

  const rxp = /d=".*?"/g
  let svgArr = string.match(rxp)
  let itemName, itemSvg

  if (!svgArr) {
    svgArr = []
  }

  // 匹配到的第一项为 id 删除
  svgArr.shift()

  for (const i in svgArr) {
    itemName = svgNameOptions[i].value

    itemSvg = `<symbol viewBox="0 0 1024 700" id="icon-${itemName}">
      <path ${svgArr[i]} />
      </symbol>`
    content += itemSvg
  }

  content += '</svg>'

  return content
}

export function getSvgOptionsByIconFontContent (string = '') {
  const options = []
  const rxp = /glyph-name=".*?"/g
  const namesArr = string.match(rxp)

  let itemLen, fileName

  namesArr.forEach(item => {
    itemLen = item.length
    fileName = item.slice(12, itemLen - 1)
    options.push({
      value: fileName,
      label: fileName
    })
  })

  return options
}
