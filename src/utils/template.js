import echartsIcon from '@/assets/img/tempIcon/echarts.png'
// import vueIcon from '@/assets/img/tempIcon/vue.png'
// import elementIcon from '@/assets/img/tempIcon/element.svg'
const publicPath = process.env.BASE_URL
const dom = {
  name: 'dom',
  instanceCode: {
    HTML: {
      language: 'html',
      content: '',
      resources: []
    },
    CSS: {
      language: 'css',
      content: '',
      resources: []
    },
    JavaScript: {
      language: 'javascript',
      content: '// 可通过 vm.config vm.data 读取数据和配置项\n // <div ref="dom"></div> vm.refs 获取dom对象',
      resources: [{
        name: 'JQuery',
        url: publicPath + 'chart-common-lib/jquery-3.6.0.min.js',
        type: 'static'
      }
      ]
    }

  }
}
// const vue2 = {
//   name: 'Vue 2',
//   icon: vueIcon,
//   instanceCode: {
//     HTML: {
//       language: 'html',
//       content: `<div id="app">
//       {{ message }}
//       </div>`,
//       resources: []
//     },
//     CSS: {
//       language: 'css',
//       content: '',
//       resources: []
//     },
//     JavaScript: {
//       language: 'javascript',
//       content: `var app = new Vue({
//         el: '#app',
//         data: {
//             message: 'Hello Vue!'
//         }
//         })`,
//       resources: [{
//         name: 'Vue 2',
//         url: 'https://cdn.jsdelivr.net/npm/vue/dist/vue.js'
//       }]
//     }
//   }
// }
const eCharts = {
  name: 'ECharts',
  icon: echartsIcon,
  instanceCode: {
    HTML: {
      language: 'html',
      content: '',
      resources: []
    },
    CSS: {
      language: 'css',
      content: '',
      resources: []
    },
    JavaScript: {
      language: 'javascript',
      content: '// 可通过 vm.config vm.data 读取数据和配置项',
      resources: [{
        name: 'ECharts',
        url: publicPath + 'chart-common-lib/echarts.min.js',
        type: 'static'
      }, {
        name: 'JQuery',
        url: publicPath + 'chart-common-lib/jquery-3.6.0.min.js',
        type: 'static'
      }]
    }
  }
}

// const elementUi = {
//   name: 'element-ui',
//   icon: elementIcon,
//   instanceCode: {
//     HTML: {
//       language: 'html',
//       content: `<div id="app">
// <el-button @click="visible = true">按钮</el-button>
// <el-dialog :visible.sync="visible" title="Hello world">
//   <p>欢迎使用 Element</p>
// </el-dialog>
// </div>`,
//       resources: []
//     },
//     CSS: {
//       language: 'css',
//       content: '',
//       resources: [{
//         name: 'element-ui',
//         url: 'https://unpkg.com/element-ui/lib/theme-chalk/index.css'
//       }]
//     },
//     JavaScript: {
//       language: 'javascript',
//       content: `new Vue({
// el: '#app',
// data: function() {
//   return { visible: false }
// }
// })`,
//       resources: [{
//         name: 'Vue 2',
//         url: 'https://cdn.jsdelivr.net/npm/vue/dist/vue.js'
//       },
//       {
//         name: 'element-ui',
//         url: 'https://unpkg.com/element-ui/lib/index.js'
//       }
//       ]
//     }
//   }
// }

const templateList = [
  dom,
  // vue2,
  eCharts
  // elementUi
]
export default templateList
