/*
 * @Author: Wang<PERSON>ing
 * @Date: 2023-01-04 21:03:49
 * @LastEditors: chenxingyu
 * @LastEditTime: 2023-04-17 15:18:14
 * @FilePath: /seatom/src/utils/coeditSocket.js
 */
import { getFingerprintID } from '@/utils/base';
import VueSocket from 'vue-socket.io';
import ClientSocketIO from 'socket.io-client';
import Vue from 'vue'
import store from '@/store'

export default function createSocket () {
  return new Promise((resolve) => {
    getFingerprintID().then((uid) => {
      const option = {
        transports: ['websocket'],
        path: '/api/socket',
        reconnectionAttempts: 10,
        query: {
          uid
        }
      }

      const socket = new VueSocket({
        debug: false,
        connection: ClientSocketIO.connect('/editor', option)
      })

      const updateSocketStatus = (socketStatus) => {
        store.commit('editor/setState', {
          socketStatus: socketStatus
        })
      }
      socket.io.on('connect', () => {
        // console.log('连接成功')
        updateSocketStatus('connect')
        resolve(socket)
      })
      socket.io.on('connect_error', () => {
        // console.log('connect_error')
        updateSocketStatus('connect_error')
      })
      socket.io.on('connection', () => {
        // console.log('connection')
        updateSocketStatus('connection')
      })
      socket.io.on('reconnect', () => {
        // console.log('reconnect')
        updateSocketStatus('reconnect')
      })
      // 重连失败
      socket.io.on('reconnect_failed', (e) => {
        // console.info('reconnect_failed')
        updateSocketStatus('reconnect_failed')
      })

      // 重连错误
      socket.io.on('reconnect_error', (e) => {
        // console.info('reconnect_error')
        updateSocketStatus('reconnect_error')
      })
      Vue.prototype.$coeditSocket = socket
    })
  })
}
