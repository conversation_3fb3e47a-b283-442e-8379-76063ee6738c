import CallbackManager from '@/lib/CallbackManager';
import { keyMap } from '@/common/constants'
import { _ } from 'core-js';
import { handleDecrypt } from '@/utils/base';

// 条件计算
function caclulateCondition (condition, param, callbackState = {}) {
  if (!condition) return true;
  const { field, type, compare, expected, code } = condition;
  const val = param[field];
  if (type === 'field') {
    let dateVal, dateExpected;
    switch (compare) {
      // eslint-disable-next-line
      case '==': return val == expected;
      // eslint-disable-next-line
      case '!=': return val != expected;
      case '>': return val > expected;
      case '<': return val < expected;
      case '>=': return val >= expected;
      case '<=': return val <= expected;
      case '>(date)':
        dateVal = new Date(val);
        dateExpected = new Date(expected);
        return dateVal.getTime() > dateExpected.getTime();
      case '<(date)':
        dateVal = new Date(val);
        dateExpected = new Date(expected);
        return dateVal.getTime() < dateExpected.getTime();
      case '>=(date)':
        dateVal = new Date(val);
        dateExpected = new Date(expected);
        return dateVal.getTime() >= dateExpected.getTime();
      case '<=(date)':
        dateVal = new Date(val);
        dateExpected = new Date(expected);
        return dateVal.getTime() <= dateExpected.getTime();
      case 'contain': return val.toString().indexOf(expected) > -1;
      case 'notContain': return val.toString().indexOf(expected) === -1;
      case 'null': return val === '' || val == null;
      case 'notNull': return val !== '' && val != null;
      case 'include': return Array.isArray(val) ? val.includes(expected) : false;
      case 'exclude': return Array.isArray(val) ? !val.includes(expected) : false;
      default: return true;
    }
  } else {
    if (_.isEmpty(code)) return true;
    const resFunc = function (eventParam = {}) {
      // eslint-disable-next-line
      try {
        const newCode = `const { ${Object.keys(eventParam).join(',')} } = $$event_param$$;const callbackArgs = $$callback_state$$;` + code;
        // eslint-disable-next-line
        return new Function('$$event_param$$', '$$callback_state$$', newCode)(eventParam, callbackState);
      } catch (e) {
        throw e;
      }
    };
    return !!resFunc(param);
  }
}

const dataUtil = {
  // 返回字段映射后的 data
  mapData (data = [], fieldMapping = []) {
    data = _.cloneDeep(data);
    if (data.map) {
      return data.map(d => {
        const newd = d;
        for (const f of fieldMapping) {
          if (f.target && newd && typeof newd === 'object') {
            newd[f.source] = d[f.target];
          }
        }
        return newd;
      });
    }
    return []
  },
  // 返回过滤器过滤后的 data
  filterData (data = [], validFilters = [], callbackManager = new CallbackManager({})) {
    let newData = _.cloneDeep(data);
    try {
      const filterFuncs = validFilters.map(f => {
        // eslint-disable-next-line
        return new Function('data', 'callbackArgs', 'fieldSetting', handleDecrypt(f.content) || f.content);
      });
      for (let i = 0; i < filterFuncs.length; i++) {
        const fieldSetting = {};
        const systemParams = validFilters[i].systemParams || [];
        if (systemParams.length) {
          systemParams.forEach(item => {
            if (item.source === 'callback') {
              validFilters[i].callbackKeys.push(...item.value);
            }
            if (['field', 'callback', 'form'].includes(item.source)) {
              fieldSetting[item.key] = item.value;
            } else if (item.source === 'cascade') {
              const options = item.options;
              fieldSetting[item.key] = options.map(obj => obj.value);
            }
          })
        }
        const callbackArgs = callbackManager && callbackManager.pick(validFilters[i].callbackKeys);
        newData = filterFuncs[i](newData, callbackArgs, fieldSetting);
      }
    } catch (e) {
      newData = [];
    }
    return newData;
  },
  // 联动过滤数据
  linkAgeFilterData (data = [], param = {}, validFilters = []) {
    const filterFuncs = validFilters.map(f => {
      if (f.extraParam) {
        // eslint-disable-next-line
        return new Function('data', 'param', f.extraParam, f.content);
      }
      // eslint-disable-next-line
      return new Function('data', 'param', f.content);
    });
    let newData = _.cloneDeep(data);
    for (let i = 0; i < filterFuncs.length; i++) {
      // eslint-disable-next-line
      try {
        newData = filterFuncs[i](newData, param, validFilters[i].extraValue);
      } catch (e) {
        throw e;
      }
    }
    return newData;
  },
  // 条件判断
  conditionResult (e, param = {}, callbackManager = new CallbackManager({})) {
    if (!e) return true;
    const { conditions = [], conditionType } = e;
    if (conditionType === 'one') { // 满足一个条件
      for (let i = 0, l = conditions.length; i < l; i++) {
        const val = caclulateCondition(conditions[i], param);
        if (val) return true;
      }
      return false;
    } else {
      for (let i = 0, l = conditions.length; i < l; i++) {
        const val = caclulateCondition(conditions[i], param, callbackManager.state || {});
        if (!val) return false;
      }
      return true;
    }
  },
  // 获取数组对象的所有key
  getFieldFromData (data) {
    let keys = [];
    data.forEach(item => {
      const key = Object.keys(item)
      keys.push(...key)
    })
    keys = Array.from(new Set(keys));
    return keys
  },

  /**
   * 字段自动映射
   * @param {*} fieldMapping 组件映射结果
   * @param {*} data 组件数据
   * @returns
   */
  fieldAutoMapping (fieldMapping, data) {
    const datakeys = this.getFieldFromData(data);
    if (datakeys.length) {
      fieldMapping.forEach(item => {
        if (item.target) {
          const source = item.source;
          const target = item.target;
          const rowData = data[0];
          if (datakeys.includes(target)) {
            _.pull(datakeys, target)
            return
          }
          if (datakeys.includes(source)) {
            item.target = source;
            _.pull(datakeys, source)
            return
          }

          const keys = keyMap[source] || []; // 特征字段集合
          if (keys.length) {
            for (let i = 0; i < datakeys.length; i++) {
              const index = keys.findIndex(k => k === datakeys[i]);
              if (index > -1) {
                item.target = datakeys[i];
                datakeys.splice(i, 1);
                break;
              }
            }
          }

          if (item.target === target) { // 以上未匹配到字段
            for (let i = 0; i < datakeys.length; i++) {
              const dataType = typeof rowData[datakeys[i]];
              if (item.type === dataType) {
                item.target = datakeys[i];
                datakeys.splice(i, 1);
                break
              }
            }
          }
        }
      })
    }

    return fieldMapping
  },

  /**
   * 获取动态参数里订阅的回调参数
   * @param {*} dataResponse 数据源配置
   * @returns Object { origin: [], key: [] }
   */
  getVarParamObj (dataResponse) {
    if (dataResponse.sourceType !== 'api') {
      return []
    }
    const { api } = dataResponse.source;
    const func = api.data.func;
    const reg = /\$\{(.*?)\}/ig; // 匹配${xxx}
    let matchArr = func.match(reg); // 得到['${name}', ${obj.xxx}]集合
    if (!matchArr) {
      return []
    }
    matchArr = matchArr.map(str => {
      return str.replace(/[${}]/g, '').replace(/\.[\w\W]*/g, ''); // 去掉${}和.后缀
    })
    return matchArr
  },

  /**
   * 获取api数据动态参数
   */
  getApiParams (dataResponse, callbackManager) {
    if (dataResponse.sourceType !== 'api') {
      return {}
    }
    const { api } = dataResponse.source;
    let func = api.data.func;
    const reg = /\$\{(.*?)\}/ig; // 匹配${xxx}
    func = func.replace(reg, str => {
      const path = str.replace(/[${}]/g, '');
      const pKey = path.replace(/\.[\w\W]*/g, '');
      const obj = callbackManager.pick([pKey]);
      return _.get(obj, path)
    })
    let $var = {};
    try {
      // new Function里使用await：new AsyncFunction()
      // eslint-disable-next-line
      const AsyncFunction = eval('Object.getPrototypeOf(async function () {}).constructor');
      const code = `
        try { ${func} } catch (e) { console.warn(e) }
      `
      // eslint-disable-next-line
      $var = new AsyncFunction('callbackArgs', code)(callbackManager.state);
    } catch (e) {
      console.warn(e)
    }

    return $var
  }
};

export default dataUtil;
