export default async function runAnimation (element, animations = [], type = '') {
  const play = (animation) => new Promise(resolve => {
    element.classList.add('animate__animated', `animate__${animation.value}`)
    animation.duration && (element.style.animationDuration = ~~animation.duration / 1000 + 's')
    animation.delay && (element.style.animationDelay = ~~animation.delay / 1000 + 's')
    type ? (element.style.animationIterationCount = 'infinite') : element.style.animationIterationCount = '';
    const removeAnimation = () => {
      element.removeEventListener('animationend', removeAnimation)
      element.removeEventListener('animationcancel', removeAnimation)
      element.classList.remove(`animate__${animation.value}`)
      element.style.animationDelay = '';
      element.style.animationDuration = '';
      element.style.animationIterationCount = '';
      resolve()
    }
    // animationend 动画完成时触发， animationcancel 意外终止时触发
    element.addEventListener('animationend', removeAnimation) // 在动画结束的时候移除动画
    // element.addEventListener('animationcancel', removeAnimation) // 在意外终止时触发
  })

  for (let i = 0, len = animations.length; i < len; i++) {
    await play(animations[i]) // 使动画同步一个个实现
  }
}
