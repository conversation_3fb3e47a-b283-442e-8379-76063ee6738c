function customTemp (state) {
  const instanceCodeCopy = _.cloneDeep(state.instanceCode)
  if (state.currentTemp === 'ECharts') {
    instanceCodeCopy.HTML.content = `<div style="width: 100%; height: 100%" ref="${state.echartsId}"></div>`
  }
  const temp = {
    compConfig: {
      name: state.compConfig.type,
      version: hanlderVersion(state.compConfig.version),
      chartConfig: {
        cn_name: state.compConfig.name,
        type: state.compConfig.type,
        // eslint-disable-next-line
        icon: '${packageUrl}icon.png',
        show: true,
        width: state.compConfig.width,
        height: state.compConfig.height,
        children: null,
        parent: null,
        data: {
          source: state.dataCode,
          fields: []
        },
        config: state.configCode,
        callbacks: state.callbacks
      }
    },
    echartsCompId: state.echartsId,
    instanceCode: instanceCodeCopy,
    editorWidth: state.editorWidth,
    iframeWidth: state.iframeWidth,
    currentTemp: state.currentTemp
  }
  return temp
}
function hanlderVersion (version) {
  const verAry = version.split('.')
  verAry[2]++
  return verAry.join('.') || '4.0.0'
}
export { customTemp }
