/*
 * @Description: systemjs import
 * @Date: 2023-04-11 14:17:53
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
import { isPanel } from './base'

/**
 * 导入组件
 * @param {*} screenComps 大屏组件列表
 */
export const addImportComponents = (screenComps) => {
  const packages = {}
  for (const key in screenComps) {
    const com = screenComps[key]

    if (isPanel[com.comType]) {
      continue
    }
    packages[`${com.comName}@${com.version}`] = `${process.env.BASE_URL}public/packages/${com.comName}@${com.version}/${com.comName}.${com.version}.js`
  }
  window.System.addImportMap({
    imports: packages
  })
}
