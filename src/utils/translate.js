// import store from '@/store'

// 角度转弧度
// Math.PI = 180 度
function angleToRadian (angle) {
  return angle * Math.PI / 180
}

/**
 * 计算根据圆心旋转后的点的坐标
 * @param   {Object}  point  旋转前的点坐标
 * @param   {Object}  center 旋转中心
 * @param   {Number}  rotate 旋转的角度
 * @return  {Object}         旋转后的坐标
 * https://www.zhihu.com/question/67425734/answer/252724399 旋转矩阵公式
 */
export function calculateRotatedPointCoordinate (point, center, rotate) {
  /**
     * 旋转公式：
     *  点a(x, y)
     *  旋转中心c(x, y)
     *  旋转后点n(x, y)
     *  旋转角度θ                tan ??
     * nx = cosθ * (ax - cx) - sinθ * (ay - cy) + cx
     * ny = sinθ * (ax - cx) + cosθ * (ay - cy) + cy
     */

  return {
    x: (point.x - center.x) * Math.cos(angleToRadian(rotate)) - (point.y - center.y) * Math.sin(angleToRadian(rotate)) + center.x,
    y: (point.x - center.x) * Math.sin(angleToRadian(rotate)) + (point.y - center.y) * Math.cos(angleToRadian(rotate)) + center.y
  }
}

/**
 * 获取旋转后的点坐标（八个点之一）
 * @param  {Object} style  样式
 * @param  {Object} center 组件中心点
 * @param  {String} name   点名称
 * @return {Object}        旋转后的点坐标
 */
export function getRotatedPointCoordinate (style, center, name) {
  let point // point 是未旋转前的坐标
  switch (name) {
    case 't':
      point = {
        x: style.left + (style.width / 2),
        y: style.top
      }

      break
    case 'b':
      point = {
        x: style.left + (style.width / 2),
        y: style.top + style.height
      }

      break
    case 'l':
      point = {
        x: style.left,
        y: style.top + style.height / 2
      }

      break
    case 'r':
      point = {
        x: style.left + style.width,
        y: style.top + style.height / 2
      }

      break
    case 'lt':
      point = {
        x: style.left,
        y: style.top
      }

      break
    case 'rt':
      point = {
        x: style.left + style.width,
        y: style.top
      }

      break
    case 'lb':
      point = {
        x: style.left,
        y: style.top + style.height
      }

      break
    default: // rb
      point = {
        x: style.left + style.width,
        y: style.top + style.height
      }

      break
  }

  return calculateRotatedPointCoordinate(point, center, style.rotate)
}

// 求两点之间的中点坐标
export function getCenterPoint (p1, p2) {
  return {
    x: p1.x + ((p2.x - p1.x) / 2),
    y: p1.y + ((p2.y - p1.y) / 2)
  }
}

export function sin (rotate) {
  return Math.abs(Math.sin(angleToRadian(rotate)))
}

export function cos (rotate) {
  return Math.abs(Math.cos(angleToRadian(rotate)))
}

export function mod360 (deg) {
  return (deg + 360) % 360
}

// export function changeStyleWithScale(value) {
//     return value * parseInt(store.state.canvasStyleData.scale) / 100
// }

export function degTo360 (deg) {
  if (deg < 0) {
    while (true) {
      deg += 360;
      if (deg >= 0) {
        return deg;
      }
    }
  } else if (deg > 360) {
    while (true) {
      deg -= 360;
      if (deg <= 360) {
        return deg;
      }
    }
  }
  return deg;
}

// 获取一个组件旋转 rotate 后的样式
export function getComponentRotatedStyle (style) {
  style = { ...style }
  if (style.rotate !== 0) {
    const newWidth = style.width * cos(style.rotate) + style.height * sin(style.rotate)
    const diffX = (style.width - newWidth) / 2 // 旋转后范围变小是正值，变大是负值
    style.left += diffX
    style.right = style.left + newWidth

    const newHeight = style.height * cos(style.rotate) + style.width * sin(style.rotate)
    const diffY = (newHeight - style.height) / 2 // 始终是正
    style.top -= diffY
    style.bottom = style.top + newHeight

    style.width = newWidth
    style.height = newHeight
  } else {
    style.bottom = style.top + style.height
    style.right = style.left + style.width
  }

  return style
}
