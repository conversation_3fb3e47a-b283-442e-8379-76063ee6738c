import axios from 'axios'
import router from '../router/edit/index'
import Message from 'hz-message'
import store from '../store/index'
import { getQueryVariable } from './base'
import { screenConfig } from '@/api/user'
import Vue from 'vue'
// import QS from 'qs';

/*
 * 全局 axios 默认配置
 */
// axios.defaults.baseURL = process.env.VUE_APP_SERVER_URL
const updateCache = getQueryVariable('updateCache')
const href = window.location.href;
axios.defaults.baseURL = window.location.origin + process.env.BASE_URL
axios.defaults.timeout = 60000 * 6
axios.defaults.withCredentials = true
// 公共请求头
// get 请求头
axios.defaults.headers.get['X-Requested-With'] = 'XMLHttpRequest'
axios.defaults.headers.get.Accept = 'application/json'
// post 请求头
// axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
axios.defaults.headers.post['Content-Type'] = 'application/json'

// 添加请求前拦截器
axios.interceptors.request.use(function (config) {
  // console.log(config,'s---------')
  // Do something before request is sent
  const token = window.localStorage.getItem('token')
  const userId = window.localStorage.getItem('userId')

  if (token) {
    config.headers.systoken = token
  }
  if (userId) {
    config.headers.sysuserid = userId
  }
  if (Vue.prototype.$enableCache && href.includes('/preview/index.html')) {
    config.headers['enable-cache'] = true
  }
  if (Vue.prototype.$needAuth && href.includes('/preview/index.html') && (config.url === '/api/datastorage/getData')) {
    Vue.prototype.$authAccessToken && (config.headers.access_token = Vue.prototype.$authAccessToken);
  }
  if (updateCache === 'true') { // 需要更新redis缓存
    if (config.url === '/api/preview/screen' || config.url === '/api/preview/getScreensComs') {
      config.headers['Update-Cache'] = true;
    }
  }
  if (href.includes('/screen/share')) { // 发布页需要读取url链接上的userId
    const userId = getQueryVariable('userId');
    if (userId) {
      config.headers.sysuserid = userId
    }
  }
  return config
}, function (error) {
  // Do something with request error
  return Promise.reject(error)
})

let msgService = null;
// 添加响应拦截器
axios.interceptors.response.use(async function (response) {
  // Any status code that lie within the range of 2xx cause this function to trigger
  // Do something with response data
  if ([400, 401, 402, 444].includes(response.data.code)) {
    if (msgService) {
      msgService.close();
    }
    const copyData = { // 复制的错误信息
      url: response.config.url,
      request_time: new Date().toLocaleString(),
      trcid: 'undefined',
      stack: response.data.message,
      status: response.data.code
    }
    if (response.data.code !== 402) {
      msgService = Message.error(response.data.message, JSON.stringify(copyData));
    }
    if (response.data.code === 401) {
      const res = await screenConfig()
      if (res && res.data) {
        if (res.data.ucenter) {
          window.open('/api/user/dmcUcenter', '_self');
        }
      } else {
        router.push({
          path: '/login'
        })
      }
    } else if (response.data.code === 402) {
      // const workspaceId = window.localStorage.getItem('workspaceId')
      // router.push(`/workspace/${workspaceId}`)
      router.push('/noaccess')
    }
    return Promise.reject(response)
  } else if (response.data.code === 455) {
    if (!Object.keys(response.data.data).includes('authorizationDays')) {
      const storage = window.localStorage.getItem('workspaceId')
      storage ? router.push({ path: `/workspace/${storage}` }) : router.push({
        path: '/login'
      })
      store.commit('user/setOverdueVisible', true)
      return Promise.reject(response)
    }
  }
  return response
}, function (error) {
  // Any status codes that falls outside the range of 2xx cause this function to trigger
  // Do something with response error
  return Promise.reject(error)
})

function transformRequest (param) {
  return Object.keys(param).map(key => {
    const value = typeof param[key] === 'object' ? encodeURIComponent(JSON.stringify(param[key])) : param[key]
    return `${key}=${value}`
  }).join('&')
}

function request (config) {
  return new Promise((resolve, reject) => {
    axios.request(config)
      .then(res => {
        if (res) {
          resolve(res.data)
        }
      })
      .catch(err => {
        handleError(err)
        reject(err)
      })
  })
}

function handleError (err) {
  console.error(err)
}

export default {
  get (url, params = {}) {
    return request({
      url: url,
      method: 'get',
      params: params
    })
  },

  // post 请求可以带 queryParams，放到第三个参数
  post (url, body = {}, params = {}, fn) {
    return request({
      url: url,
      method: 'post',
      data: body,
      params: params,
      onUploadProgress: fn
      // transformRequest: [function (data, headers) {
      //   return QS.stringify(data)
      // }]
    })
  },

  delete (url, params) {
    return request({
      url: url,
      method: 'delete',
      params: params,
      paramsSerializer: function (params) {
        return transformRequest(params)
      }
    })
  },
  head (url, params) {},
  options (url, params) {},
  put (url, params) {},
  patch (url, params) {}
}
