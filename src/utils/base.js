import { v4 } from 'uuid';
import Fingerprint2 from 'fingerprintjs2'
import dataUtil from '@/utils/data'
import CryptoJS from 'crypto-js'
import html2canvas from 'html2canvas'
import { screenShot } from '@/api/workspace'
import VueSocket from 'vue-socket.io';
import ClientSocketIO from 'socket.io-client';

export function getQueryVariable (variable, str) {
  var query = str || (window.location.hash.split('?')[1] || '');
  var vars = query.split('&');
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=');
    if (pair[0] === variable) return pair[1];
  }
  return undefined;
}

export function uuid (prefix) {
  return prefix ? `${prefix}_${v4()}` : v4();
}

export function swap (arr, index1, index2) {
  arr[index1] = arr.splice(index2, 1, arr[index1])[0];
  return arr;
}

export function randomStr (n) {
  const str = 'abcdefghijklmnopqrstuvwxyz9876543210';
  let tmp = '';
  let i = 0;
  const l = str.length;
  for (i = 0; i < n; i++) {
    tmp += str.charAt(Math.floor(Math.random() * l));
  }
  return tmp;
}

export function replaceUrl (url, type, joint = false) {
  // if (type === 'jump') return;
  // let host = process.env.VUE_APP_SERVER_URL || '';
  let host = window.location.origin;
  const forceReplace = process.env.VUE_APP_FORCE_REPLACE
  const fuxiPath = ['/public/packages/', '/public/commonfiles/', '/public/personal/', '/public/private/', '/public/screenData/', '/public/system/']
  // let host = "http://************:7001/tools/hzksh/";
  if (type === 'imageServer') host = process.env.VUE_APP_IMG_SERVER_URL || host;
  host += process.env.BASE_URL;
  if (host.charAt(host.length - 1) === '/') host = host.substring(0, host.length - 1);
  if (joint) return `${host}${url}`;
  if (typeof (url) !== 'string' || (url.indexOf('public') === -1 && url.indexOf('tfs') === -1 && url.indexOf('api') === -1 && url.indexOf('qianliyan') === -1)) {
    return url
  }
  if (url.indexOf('http') === -1 && (url.indexOf('/public/') === 0 || url.indexOf('/tfs/') === 0 || url.indexOf('/api/') === 0 || url.indexOf('/qianliyan/') === 0)) {
    return `${host}${url}`
  } else {
    if (url.indexOf('/public/') > -1) {
      const newUrl = url.split('/public');
      const hasPath = fuxiPath.findIndex(path => url.indexOf(path) > -1) > -1
      const reg = new RegExp(/(\w+):\/\/([^/:]+)(:\d*)?/)
      if (!hasPath && forceReplace !== 'true' && newUrl[0].match(reg) && newUrl[0].match(reg)[2] !== host.match(reg)[2]) { // 第三方链接，不处理
        return url;
      }
      newUrl[0] = host
      url = newUrl.join('/public')
    } else if (url.indexOf('/tfs/') > -1) {
      const newUrl = url.split('/tfs');
      newUrl[0] = host
      url = newUrl.join('/tfs')
    } else if (url.indexOf('/qianliyan/') > -1) {
      const newUrl = url.split('/qianliyan');
      newUrl[0] = host
      url = newUrl.join('/qianliyan')
    } else if (url.indexOf('/api/') > -1) {
      const newUrl = url.split('/api');
      newUrl[0] = host
      url = newUrl.join('/api')
    }
    return url
  }
}

export function getFingerprintID () {
  return new Promise((resolve, reject) => {
    Fingerprint2.get(function (components) {
      const values = components.map(function (component, index) {
        if (index === 0) { // 把微信浏览器里UA的wifi或4G等网络替换成空,不然切换网络会ID不一样
          return component.value.replace(/\bNetType\/\w+\b/, '')
        }
        return component.value
      })
      // 生成最终id murmur
      const murmur = Fingerprint2.x64hash128(values.join(''), 31);
      resolve(murmur)
    })
  })
}

// 判断数据源映射是否匹配
export function chargeData (comp, data, screenFilters) {
  if (!comp || !data || !data.length) return true;
  const filters = comp.dataConfig.dataResponse.filters;
  const validFilters = filters.enable ? _.filter(filters.list, { enable: true }).map(({ id }) => screenFilters[id]) : [];
  data = dataUtil.filterData(data, validFilters);
  if (typeof data === 'undefined') return false; // 过滤器处理后的数据如果是undefined，认为不匹配
  const fields = comp.dataConfig.fields;
  const mapping = comp.dataConfig.fieldMapping.filter(map => {
    const field = fields.find(item => item.name === map.source);
    if (field && !field.optional) return true;
    else return false;
  }).map(item => (item.target || item.source));
  if (!mapping.length) return true;
  for (let i = 0, len = mapping.length; i < len; i++) {
    const flag = data.some(res => Object.prototype.hasOwnProperty.call(res, mapping[i]));
    if (!flag) {
      // 组件数据中没有包含此映射字段的值，视为出错，收集组件
      return false;
    }
  }
  return true;
}

// 阿拉伯数字转为中文
export function numberToChinese (num) {
  const chnNumChar = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const chnUnitSection = ['', '万', '亿', '万亿', '亿亿'];
  const chnUnitChar = ['', '十', '百', '千'];
  function sectionToChinese (section) {
    let strIns = ''; let chnStr = '';
    let unitPos = 0;
    let zero = true;
    while (section > 0) {
      const v = section % 10;
      if (v === 0) {
        if (!zero) {
          zero = true;
          chnStr = chnNumChar[v] + chnStr;
        }
      } else {
        zero = false;
        strIns = chnNumChar[v];
        strIns += chnUnitChar[unitPos];
        chnStr = strIns + chnStr;
      }
      unitPos++;
      section = Math.floor(section / 10);
    }
    return chnStr;
  }
  let unitPos = 0;
  let strIns = ''; let chnStr = '';
  let needZero = false;

  if (num === 0) {
    return chnNumChar[0];
  }

  while (num > 0) {
    const section = num % 10000;
    if (needZero) {
      chnStr = chnNumChar[0] + chnStr;
    }
    strIns = sectionToChinese(section);
    strIns += (section !== 0) ? chnUnitSection[unitPos] : chnUnitSection[0];
    chnStr = strIns + chnStr;
    needZero = (section < 1000) && (section > 0);
    num = Math.floor(num / 10000);
    unitPos++;
  }
  return chnStr;
}

export function waitTime (time) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve()
    }, time);
  })
}

/**
 * 根据key，value获取节点对象
 * @param {Array} nodes
 * @param {String} key
 * @param {String} value
 * @returns Object
 */
export function getNodeByParam (nodes, key, value) {
  if (!nodes || !key) return null;
  for (var i = 0, l = nodes.length; i < l; i++) {
    var node = nodes[i];
    if (node[key] === value) {
      return nodes[i];
    }
    var children = node.children;
    var tmp = getNodeByParam(children, key, value);
    if (tmp) return tmp;
  }
  return null;
}

// 递归查找分组下的所有组件
export function getGroupComs (node) {
  const coms = [];
  if (node.children && node.children.length) {
    node.children.forEach(child => {
      if (child.type === 'com') {
        coms.push(child);
      } else {
        coms.push(...getGroupComs(child));
      }
    });
    return coms
  } else {
    return [];
  }
}

/**
 * 判断是否是面板类组件
 * @param {String} comType
 * @returns Boolean
 */
export function isPanel (comType) {
  const arr = [
    'interaction-container-dynamicpanel',
    'interaction-container-newdynamicpanel',
    'interaction-container-flowlayoutpanel',
    'interaction-container-carousepanel',
    'interaction-container-affixPanel',
    'interaction-container-list-pitch',
    'interaction-container-roll-pitch',
    'interaction-container-loop-pitch',
    'interaction-container-referpanel',
    'interaction-container-mapShadowPanel',
    'interaction-container-popoverpanel',
    'interaction-container-fold-panel',
    'interaction-container-popup',
    'interaction-container-statusdialogpanel',
    'interaction-form-group'
  ]
  return arr.includes(comType)
}

/**
 * 判断是否是form表单类组件
 * @param {String} comType
 * @returns Boolean
 */
export function isFormComp (comType) {
  const arr = [
    'interaction-form-checkbox',
    'interaction-form-input',
    'interaction-form-select'
  ]
  return arr.includes(comType)
}

/**
 * 根据下钻配置 将级联数据处理成id，parentId关联的数据
 * @param {*} data 输入数据
 * @param {*} linkObj 联动配置
 * @returns Array
 */
export function transDrillData (data, linkObj) {
  const levels = linkObj.links.map(l => l.fieldValue).filter(d => !!d);
  const sums = linkObj.sums.filter(s => !!s);
  if (!levels.length) {
    return data
  }
  var result = []
  const drillField = '下钻字段'; // 新增的下钻字段名称
  data.forEach(item => {
    const path = []
    levels.forEach(level => {
      path.push(item[level])
      const obj = {
        ...item,
        _id: path.join('-'),
        _parentId: path.slice(0, -1).join('-'),
        [drillField]: item[level]
      }
      result.push(obj)
    })
  })

  var groupData = _.groupBy(result, '_id');
  const arr = []
  Object.values(groupData).forEach(list => {
    var obj = list.reduce((last, curr, i) => {
      if (i === 0) {
        last = { ...curr };
      }
      sums.forEach(key => {
        if (i === 0) {
          last[key] = 0;
        }
        last[key] += (+curr[key]);
      })
      return last
    }, {})
    arr.push(obj)
  })
  return arr
}

/**
 * 将base64转为formData，为上传做准备
 */
export function getFormData (data) {
  const formData = new FormData();
  const ext = data.split(',')[0].match(/:(.*?);/)[1].split('/')[1];
  data = dataURLtoFile(data, randomStr(6) + '.' + ext);
  formData.append('file', data);
  formData.append('filefolder', 'image-magic');
  return formData;
}

/**
 * 将base64转为file
 * @param {*} dataurl
 * @param {*} filename
 * @returns
 */
export function dataURLtoFile (dataurl, filename) {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}
// 拼接tree字符串
export function treeToJson (data, str, obj) {
  const newValue = str || ''
  for (const key in data) {
    str = newValue + '.' + key
    if (typeof data[key] === 'object') {
      treeToJson(data[key], str, obj);
    } else {
      obj[str] = data[key]
    }
  }
}

// 图片路径加密算法
export function encryptTilemapPath (path = '') {
  const key = CryptoJS.enc.Utf8.parse('F212492324A249CG'); // fuxistatic 转 F212492324A249CG
  const iv = CryptoJS.enc.Utf8.parse('ABCDEF1234123412');
  const srcs = CryptoJS.enc.Utf8.parse(path);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  const httpBasename = encrypted.ciphertext.toString();
  const realPath = 'tfs/' + httpBasename;
  return realPath;
}

// AES方式进行加密
export function Encrypt (word = '') {
  const key = CryptoJS.enc.Utf8.parse('F212492324A249CG');
  const iv = CryptoJS.enc.Utf8.parse('ABCDEF1234123412');
  const srcs = CryptoJS.enc.Utf8.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
  const aes = 'encrypt_' + encrypted.ciphertext.toString().toUpperCase()
  return aes;
}

// AES方式进行解密
export function Decrypt (word = '') {
  const key = CryptoJS.enc.Utf8.parse('F212492324A249CG');
  const iv = CryptoJS.enc.Utf8.parse('ABCDEF1234123412');
  const encryptedHexStr = CryptoJS.enc.Hex.parse(word);
  const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  const decrypt = CryptoJS.AES.decrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
  const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}

// 处理AES方式进行加密
export function handleDecrypt (word = '') {
  let value;
  if (word.includes('encrypt_')) {
    value = Decrypt(word.split('encrypt_')[1])
  } else {
    value = word
  }
  return value
}

// 获取压缩后的图片路径
export function fastLoadedImg (json, jsonType, dom) { // json是带有图片url的字符串或对象，jsonType是json类型：string，objectBg，objectPic，dom是带有width和height的对象
  // 获取当前页面缩放比例
  const wrap = document.querySelector('.screen-view-wrap');
  if (!wrap) return false;
  const matrix = window.getComputedStyle(wrap, null).transform;
  const scaleX = parseFloat(matrix.substring(7).split(',')[0]) || 1;
  const scaleY = parseFloat(matrix.substring(7).split(',')[3]) || 1;
  const width = parseInt((dom.width || dom.w) * scaleX);
  const height = parseInt((dom.height || dom.h) * scaleY);
  switch (jsonType) {
    case 'string': {
      return json.includes('/tfs/') ? `${json}?width=${width}&height=${height}` : json;
    }
    case 'objectBg': {
      if (json && json.image && json.image.url && json.image.url.includes('/tfs/')) {
        json.image.url = `${json.image.url}?width=${width}&height=${height}`;
      }
      return json;
    }
    case 'objectPic': {
      if (json && json.url && json.url.includes('/tfs/')) {
        json.url = `${json?.url}?width=${width}&height=${height}`;
      }
      return json;
    }
  }
}
export function findImageUrl (config, fn) { // 递归查找组件带图片路径的属性值，通过fn进行处理
  if (!config) return;
  for (const key in config) {
    if (typeof config[key] === 'string' && config[key].indexOf('/public/') > -1) {
      config[key] = fn(config[key])
    } else if (typeof config[key] === 'object' && !Array.isArray(config[key])) {
      findImageUrl(config[key], fn)
    } else if (Array.isArray(config[key])) {
      config[key].forEach(item => {
        findImageUrl(item, fn)
      })
    }
  }
}

// 处理背景对象
export function formatBackground (background = {}) {
  if (background.show === false) {
    return '';
  }
  let backgroundStyle;
  switch (background.type) {
    case 'pure':
      backgroundStyle = {
        'background-color': background.pure
      }
      break;
    case 'gradient': {
      const { gradient = {} } = background;
      backgroundStyle = { 'background-image': `linear-gradient(${gradient.deg}deg, ${gradient.start}, ${gradient.end})` };
      break
    };
    case 'image': {
      const {
        image = {}
      } = background;
      const {
        changeColor: imageChangeColor = {}
      } = image;
      backgroundStyle = {
        'background-size': image.size,
        'background-position': `${image.positionX} ${image.positionY}`,
        'background-repeat': image.repeat
      };
      const imageList = [`url(${image.url})`];
      if (imageChangeColor && imageChangeColor.show) {
        imageList.push(formatGradientValue(imageChangeColor.data))
        backgroundStyle['mix-blend-mode'] = imageChangeColor.mixMode;
      }
      backgroundStyle['background-image'] = imageList.join(',')
      break;
    }
  }
  return backgroundStyle;
}
function formatGradientValue (gradient = {}) {
  if (gradient.type === 'radial-gradient') {
    return `radial-gradient(${gradient.shape}, ${gradient.start} ${gradient.startScale}%, ${gradient.end} ${gradient.endScale}%)`
  } else {
    return `linear-gradient(${gradient.deg}deg, ${gradient.start} ${gradient.startScale}%, ${gradient.end} ${gradient.endScale}%)`
  }
}

// 获取http请求头
export const getHttpHeaders = () => {
  return {
    systoken: window.localStorage.getItem('token'),
    sysuserid: window.localStorage.getItem('userId')
  }
}

// 日期格式化
export const dateFormat = (time, format) => {
  var t = new Date(time);
  var tf = function (i) { return (i < 10 ? '0' : '') + i };
  return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
    switch (a) {
      case 'yyyy':
        return tf(t.getFullYear());
      case 'MM':
        return tf(t.getMonth() + 1);
      case 'mm':
        return tf(t.getMinutes());
      case 'dd':
        return tf(t.getDate());
      case 'HH':
        return tf(t.getHours());
      case 'ss':
        return tf(t.getSeconds());
    }
  })
}

/**
 * 取两个对象的交集，交集的value默认为第一项
 * @param {Object} obj1
 * @param {Object} obj2
 * @param {Array} keys  为两个对象中哪几个key的值也要相同
 */
export function findIntersection (obj1, obj2, keys = ['type']) {
  let intersection = null;

  if (typeof obj1 === 'object') {
    if (obj1 === null || Array.isArray(obj1)) {
      intersection = [];
    } else {
      intersection = {};
    }
  }

  for (const key in obj1) {
    if (Object.prototype.hasOwnProperty.call(obj2, key) && key !== 'conditionTemp') {
      if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
        if (keys.every(k => _.isEqual(obj1[key][k], obj2[key][k]))) {
          intersection[key] = findIntersection(obj1[key], obj2[key]);
        }
      } else if (typeof obj1[key] === typeof obj2[key]) {
        intersection[key] = obj1[key]
      }
    }
  }

  return intersection;
}
// 是否是移动端
export const isMobile = () => {
  return !!(window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|PlayBook|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))
}

// 表格数据转列表
export const tableToList = (table = {}) => {
  if (!table || typeof table !== 'object' || (!table.h && !table.b) || (!Array.isArray(table.h) && !Array.isArray(table.b))) {
    return table
  }

  const heads = table.h || []
  const bodys = table.b || []
  const list = []

  for (let index = 0; index < bodys.length; index++) {
    const body = bodys[index];

    const data = {}

    for (let j = 0; j < heads.length; j++) {
      const key = heads[j];

      if (body[j] && typeof body[j] === 'object' && Array.isArray(body[j].h) && Array.isArray(body[j].b)) {
        data[key] = tableToList(body[j])
      } else {
        data[key] = body[j]
      }
    }

    list.push(data)
  }
  return list
}

// 前端截屏
export const screenshot = (dom, cb, folderName = 'screenData/screenshot') => {
  html2canvas(
    dom,
    {
      scale: 1.5,
      backgroundColor: null
    }
  ).then(canvas => {
    const a = canvas.toDataURL('image/png');
    const file = base64ImgtoFile(a)
    const params = new FormData();
    params.append('file', file)
    params.append('filefolder', folderName)
    screenShot(params).then(res => {
      const screenShotUrl = `${res.data[0].url}`
      cb && cb(screenShotUrl)
    })
  })
}

export function base64ImgtoFile (dataurl, filename = 'file') {
  const arr = dataurl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const suffix = mime.split('/')[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], `${filename}.${suffix}`, {
    type: mime
  })
}

// 创建socket连接
export function createSocket (room) {
  const uid = uuid()
  const option = {
    transports: ['websocket'],
    path: '/api/socket',
    reconnectionAttempts: 10,
    query: {
      room,
      userId: localStorage.getItem('userId') || '',
      uid
    }
  }
  const socket = new VueSocket({
    debug: false,
    connection: ClientSocketIO.connect('/linkage', option)
  })
  return socket
}
