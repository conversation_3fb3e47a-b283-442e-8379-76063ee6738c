// const BASE_RGB_COLORS = [
//   [[0, 0, 0]], // 白色系
//   [[255, 255, 255]], // 黑色系
//   [[255, 0, 0], [116, 0, 0], [241, 0, 0]], // 红色系
//   [[255, 255, 0], [255, 128, 0], [228, 226, 121]], // 黄色系
//   [[0, 0, 255], [0, 255, 255], [14, 71, 146]], // 蓝色系
//   [[255, 126, 0]], // 橙色系
//   [[254, 210, 235], [242, 189, 217]], // 粉色系
//   [[0, 255, 0], [170, 210, 224], [63, 203, 239]], // 绿色系
//   [[55, 55, 55], [111, 111, 111]]
// ];
const COLOR_INTERVAL = 360 / 24;
const mainColorCache = {};
let helperCanvas;

// 使用HSL算法提取主色，主要用于需要用取到的主色做颜色对比，处理其他对象
function getMainColorByHSL (url, getWholeColor) {
  return new Promise((resolve, reject) => {
    if (url instanceof File) {
      url = window.URL.createObjectURL(url);
    }
    let mainColor = mainColorCache[url];
    if (mainColor) {
      resolve(mainColor);
      return;
    }
    getPxList(url).then(({ pxList }) => {
      const similarColorMap = {}
      pxList.filter(colorArr => colorArr[3] !== 0).forEach(colorArr => {
        const hsl = rgbToHsl.apply(colorArr, colorArr.slice(0, 3))
        const similarColor = getColorTypeByHue(hsl[0] * 360);
        if (!similarColorMap[similarColor]) {
          similarColorMap[similarColor] = {
            color: similarColor,
            number: 0,
            saturation: [],
            brightness: []
          };
        }
        const currnetSimlilarObj = similarColorMap[similarColor];
        currnetSimlilarObj.number++;
        currnetSimlilarObj.saturation.push(hsl[1]);
        currnetSimlilarObj.brightness.push(hsl[2]);
      });
      const similarColorList = Object.values(similarColorMap);
      similarColorList.sort((left, right) => left.number > right.number ? -1 : 1);
      const mainColorObj = similarColorList[0];
      const mainSaturationList = mainColorObj.saturation.map(item => parseFloat(item.toFixed(2)));
      const mainBrightnessList = mainColorObj.brightness.map(item => parseFloat(item.toFixed(2)));
      mainColor = {
        h: mainColorObj.color,
        s: getMaxByDuplicateCount(mainSaturationList),
        l: getMaxByDuplicateCount(mainBrightnessList)
      }
      mainColorCache[url] = mainColor;
      resolve(mainColor);
    });
  })
}

function getPxList (url, config) {
  config = config || {};
  if (!helperCanvas) {
    helperCanvas = document.createElement('canvas');
    helperCanvas.className = 'helper-canvas';
    helperCanvas.style.display = 'none'
    document.body.appendChild(helperCanvas);
  }
  return new Promise(resolve => {
    getImageInfo(url, image => {
      const { width, height } = image;
      // let interval = config.canvasWidth || 100;
      // height = height * interval / width;
      // width = interval;
      // if (pxLists[url]) {
      //   resolve({pxList: pxLists[url], image});
      // } else {
      helperCanvas.width = width;
      helperCanvas.height = height;
      const ctx = helperCanvas.getContext('2d');
      ctx.drawImage(image, 0, 0, width, height);
      const imageData = ctx.getImageData(0, 0, width, height);
      const pxList = combineRGBA(imageData.data);
      // pxLists[url] = pxList;
      resolve({ pxList, image });
      // }
    })
  })
}

function getImageInfo (url, cb) {
  const image = new Image();
  image.crossOrigin = '';
  image.src = url;
  image.onload = function () {
    cb && cb(image)
  };
}

function combineRGBA (pxData) {
  const pxLength = pxData.length;
  const pxList = [];
  for (let i = 0; i < pxLength; i += 4) {
    pxList.push([
      pxData[i],
      pxData[i + 1],
      pxData[i + 2],
      pxData[i + 3]
    ])
  }
  return pxList;
}

// 根据色相对颜色进行分类
function getColorTypeByHue (color) {
  return ~~(color / COLOR_INTERVAL) * COLOR_INTERVAL;
}

function getMaxByDuplicateCount (arr) {
  const duplicateCountMap = {};
  arr.forEach(key => {
    if (!duplicateCountMap[key]) {
      duplicateCountMap[key] = 0;
    }
    duplicateCountMap[key]++;
  })
  const sortArr = Object.keys(duplicateCountMap).sort((left, right) => duplicateCountMap[left] > duplicateCountMap[right] ? -1 : 1);
  return sortArr[0]
}

function changePxColor (rgbaColor, rect, alpha) {
  // let alpha = rgbaColor[3];
  rgbaColor[0] = rgbaColor[0] + rect[0];
  rgbaColor[1] = rgbaColor[1] + rect[1];
  rgbaColor[2] = rgbaColor[2] + rect[2];
  rgbaColor.push(alpha);
  return rgbaColor;
}

function changeColor (url, mainColor, destColor, cb) {
  const mainRgba = hslToRgb.apply(mainColor, [mainColor.h, parseFloat(mainColor.s), parseFloat(mainColor.l)]);
  // const destRgba = hex2Rgb.apply(destColor, [destColor]).slice(4, -1).split(",").map(item => parseInt(item));
  const destRgba = destColor.slice(5, -1).split(',').map(item => parseInt(item));
  // const destHsl = rgbToHsl.apply(destRgba, destRgba);
  // const h = destHsl[0];
  const rect = [destRgba[0] - mainRgba[0], destRgba[1] - mainRgba[1], destRgba[2] - mainRgba[2]];
  getPxList(url).then(({ pxList, image }) => {
    const colorChangedPxList = pxList.map(pxItem => {
      return changePxColor(pxItem, rect, destRgba[3]);
    })
    cb && cb(colorChangedPxList, image);
  })
}

// function hex2Rgb (hexColor) {
//   let sColor = hexColor.toLowerCase();
//   // 十六进制颜色值的正则表达式
//   let reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
//   // 如果是16进制颜色
//   if (sColor && reg.test(sColor)) {
//     if (sColor.length === 4) {
//       let sColorNew = '#';
//       for (let i = 1; i < 4; i += 1) {
//         sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
//       }
//       sColor = sColorNew;
//     }
//     // 处理六位的颜色值
//     let sColorChange = [];
//     for (let i = 1; i < 7; i += 2) {
//       sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)));
//     }
//     return 'RGB(' + sColorChange.join(',') + ')';
//   }
//   return sColor;
// }

function hslToRgb (h, s, l) {
  let r, g, b;
  h = h / 360;
  if (s === 0) {
    r = g = b = l; // achromatic
  } else {
    const hue2rgb = function hue2rgb (p, q, t) {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    }

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }
  return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
}
// 输入的h范围为[0,360],s,l为百分比形式的数值,范围是[0,100]
// 输出r,g,b范围为[0,255],可根据需求做相应调整

function rgbToHsl (r, g, b) {
  r = r / 255;
  g = g / 255;
  b = b / 255;
  const max = Math.max(r, g, b); const min = Math.min(r, g, b);
  let h; let s; const l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }
  return [h, s, l];
}

function createCanvasFromPxList (pxList, image) {
  const canvas = document.createElement('canvas');
  const { width, height } = image;
  const ext = image.src.substring(image.src.lastIndexOf('.') + 1).toLowerCase();
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');
  const imgData = ctx.createImageData(width, height);
  pxList.forEach((pxItem, index) => {
    index = index * 4;
    imgData.data[index + 0] = pxItem[0];
    imgData.data[index + 1] = pxItem[1];
    imgData.data[index + 2] = pxItem[2];
    imgData.data[index + 3] = pxItem[3];
  });
  ctx.putImageData(imgData, 0, 0);
  return canvas.toDataURL('image/' + ext);
}

export { getMainColorByHSL, changeColor, createCanvasFromPxList };
