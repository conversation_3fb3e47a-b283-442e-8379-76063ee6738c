import { postData } from '@/api/datastorage'
import dataUtil from '@/utils/data'
import { REQUEST_TIME_RANGE, REQUEST_MERGE_NUM } from '@/common/constants'
import { uuid } from '@/utils/base'

const getCompData = (function () {
  const time = REQUEST_TIME_RANGE;
  const maxNum = REQUEST_MERGE_NUM;
  const params = [];
  const instance = [];
  const requestMap = new Map();
  let timeout = null;
  return function (compParams = {}) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      if (['inherit', 'dialog'].includes(this.sourceType)) {
        this.dataLoaded = true
        resolve({ code: 200, success: true, data: this.itemData })
      } else if (this.sourceType === 'static') {
        this.dataLoaded = true
        resolve({ code: 200, success: true, data: this.comCfg.staticData })
      } else {
        clearTimeout(timeout);
        // 获取api数据源订阅的回调参数
        const strArr = this.getFormatParamArr();
        const newParams = this.callbackManager().pick(strArr);
        const _params = _.merge(_.pickBy(newParams, val => !_.isNil(val)), compParams);
        // 获取api数据源动态参数
        const _var = await dataUtil.getApiParams(this.dataResponse, this.callbackManager());
        if (!_.isEmpty(_var)) {
          _.merge(_params, { _var });
        }
        const requstData = { componentId: this.id, workspaceId: this.workspaceId, sourceType: this.sourceType, params: _params, uid: uuid() }
        params.push(requstData);

        // 保存组件上次请求到Map中，id => new Map()
        if (!requestMap.has(this.id)) {
          requestMap.set(this.id, new Map());
        }
        const currMap = requestMap.get(this.id);
        if (currMap.size) { // 如果存在上次请求，则resolve并删除
          currMap.forEach((callback, key) => {
            callback.resolve();
            currMap.delete(key);
          })
        }
        currMap.set(requstData.uid, { resolve, reject });

        this.compLoading = true;
        instance.push(this);
        if (params.length === maxNum) {
          const part = params.splice(0);
          const partInstance = instance.splice(0);
          const reqData = part.map(p => {
            const d = { ...p }
            delete d.uid
            return d
          })
          part.length && postData({}, reqData).then(res => {
            res.forEach((item, index) => {
              if (partInstance[index]) {
                const callbackMap = requestMap.get(partInstance[index].id);
                const callback = callbackMap.get(part[index].uid);
                if (callback) {
                  callback.resolve(item)
                  callbackMap.delete(part[index].uid)
                }
              }
            });
          }).catch(err => {
            console.error(err);
          })
        } else {
          timeout = setTimeout(() => {
            if (params.length) {
              const part = params.splice(0);
              const partInstance = instance.splice(0);
              const reqData = part.map(p => {
                const d = { ...p }
                delete d.uid
                return d
              })
              part.length && postData({}, reqData).then(res => {
                res.forEach((item, index) => {
                  const callbackMap = requestMap.get(partInstance[index].id);
                  const callback = callbackMap.get(part[index].uid);
                  if (callback) {
                    callback.resolve(item)
                    callbackMap.delete(part[index].uid)
                  }
                });
              }).catch(err => {
                console.error(err);
              })
            }
          }, time)
        }
      }
    })
  }
})();

export default getCompData;
