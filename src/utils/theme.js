const publicPath = process.env.BASE_URL;
const createLink = (() => {
  let $link = null;
  return () => {
    if ($link) {
      return $link;
    }
    $link = document.createElement('link');
    $link.rel = 'stylesheet';
    $link.type = 'text/css';
    document.head.appendChild($link);
    return $link;
  }
})();

const toggleTheme = (theme = 'dark') => {
  const $link = createLink();
  $link.href = publicPath + `themes/${theme}.css?${+new Date()}`;
  window.localStorage.setItem('theme', theme);
  return theme;
}

export default toggleTheme;
