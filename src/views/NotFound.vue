<template>
    <div class="theme-bgc">
        <div class="notfound-container">
            <template v-if="theme=='light'">
                <img v-if="path=='/noaccess'" src="../assets/img/noaccess_light.png" alt="">
                <img v-else src="../assets/img/notfound_light.png" alt="">
            </template>
            <template v-else>
                <img v-if="path=='/noaccess'" src="../assets/img/noaccess_dark.png" alt="">
                <img v-else src="../assets/img/notfound_dark.png" alt="">
            </template>
            <div class="reason">
                <div class="lasttime">
                    <img src="../assets/img/lasttime_light.png" alt="" v-if="theme=='light'">
                    <img src="../assets/img/lasttime_dark.png" alt="" v-else>
                    <span class="lasttime-number">{{ time }}</span>
                </div>
                <div class="text">
                    <p v-if="path=='/noaccess'">抱歉，当前您没有访问权限</p>
                    <p v-else>抱歉，您访问的页面不存在</p>
                    <p>5秒后将返回上一个页面</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
  data () {
    return {
      time: 5,
      timer: null,
      theme: localStorage.getItem('theme'),
      path: this.$route.path
    }
  },
  mounted () {
    this.startTime()
  },
  beforeDestroy () {
    clearTimeout(this.timer);
    this.timer = null
  },
  methods: {
    // 倒计时
    startTime () {
      this.timer = setTimeout(() => {
        this.time -= 1
        if (this.time !== 0) {
          this.startTime()
        } else {
          const path = window.localStorage.getItem('former_path')
          if (path && path !== 'undefined') {
            this.$router.push(path)
          } else {
            this.$router.push('/index')
          }
        }
      }, 1000);
    }
  }
}
</script>

<style lang="scss" scoped>
.notfound-container {
    width: 526px;
    height: 622px;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    .reason {
        width: 100%;
        height: 126px;
        display: flex;
        font-family: PingFang SC;
        justify-content: space-between;
        align-items: center;
        .text {
            color: var(--seatom-type-800);
            p:nth-child(1) {
                font-size: 30px;
                font-weight: 600;
            }
            p:nth-child(2) {
                font-size: 24px;
                font-weight: 400;
            }
        }
        .lasttime {
            position: relative;
            .lasttime-number {
                font-size: 56px;
                font-weight: 700;
                font-family: DIN;
                color: #0094FF;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
}
.theme-bgc {
    height: 100%;
    background-color: var(--seatom-mono-100);
}
</style>
