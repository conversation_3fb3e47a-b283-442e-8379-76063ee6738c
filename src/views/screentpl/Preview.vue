<template>
  <div class="create-screen">
    <!-- <div class="top-bar">
      <div class="right-bar"></div>
      <div class="left-bar"></div>
      <div class="return-btn">
        <div @click="backPage" class="return-text">
          <i class="el-icon-arrow-left"></i>
          取消创建
        </div>
      </div>
    </div> -->
    <div class="creator-wp">
      <div class="template-list">
        <template v-for="item in ScreenTpl">
          <div v-if="item.level" :key="item.id" class="template-item">
            <div class="template-image">
              <img :src="item.config.thumbnail" alt="暂无图片" class="preview-image">
              <div class="template-mask">
                  <el-button type="primary" @click="previewScreentpl(item.screenId)" class="create-btn">查看模版</el-button>
                  <el-button class="preview-btn" @click="exportScreen(item)">导出模版</el-button>
              </div>
            </div>
            <div class="template-info">
              <div class="template-name">
                 {{item.name}}
              </div>
              <div class="template-size">
                <p>{{item.config.width}}px x {{item.config.height}}px</p>
              </div>
            </div>
          </div>
        </template>
        <!-- <div class="template-item --blank">
        <div class="template-image">
            <el-button type="primary"  @click="openDialog" class="create-btn">+ 创建模版</el-button>
        </div>
        </div> -->
      </div>
    </div>
    <el-dialog
      title="创建大屏模版"
      :visible.sync="dialogVisible"
      width="450px"
      top="0"
      :before-close="closeCreate"
      :close-on-click-modal="false">
      <el-form :model="selectParams" :rules="rules" size="medium" label-width="100px" ref="form">
        <el-form-item label="模版名称：" prop="name" status-icon>
          <el-input v-model="selectParams.name" placeholder="请输入大屏模版名称"></el-input>
        </el-form-item>
        <el-form-item label="标签：">
          <div class="tag-wrap">
            <div class="tag-item" v-for="(item, idx) in selectParams.tag" :key="idx">
              <span class="tag">{{ item }}</span>
              <span class="el-icon-close" @click="delTag(idx)"></span>
            </div>
            <el-input v-if="showInput" v-model="tagVal" v-select class="tag-input" size="mini" @blur="inputBlur()"></el-input>
            <el-button type="primary" size="mini" icon="el-icon-plus" @click="addTag()">添加</el-button>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button class="preview-btn" @click="closeCreate">取 消</el-button>
        <el-button class="create-btn" type="primary" @click="createScreen">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getScreenTpl, createScreentpl, deleteScreentpl } from '@/api/screentpl';
import { openNewTab } from '@/utils/dom';
import { replaceUrl } from '@/utils/base';
export default {
  data () {
    return {
      dialogVisible: false,
      selectProject: [],
      ScreenTpl: [],
      selectParams: {
        projectId: 0,
        workspaceId: 0,
        level: 1,
        name: '',
        tag: []
      },
      rules: {
        name: [
          { required: true, message: '模板名称必填', trigger: 'change' }
        ]
      },
      showInput: false,
      tagVal: ''
    }
  },
  directives: {
    select: {
      inserted: function (el) {
        el.querySelector('input').select()
      }
    }
  },
  created () {
    this.getScreenTpl();
  },
  methods: {
    exportScreen (item) { // 导出模版
      this.$confirm('确认导出模版？', { title: '提示', type: 'info' }).then(() => {
        var a = document.createElement('a');
        a.download = '';
        a.href = replaceUrl('/api/screen/export?id=', null, true) + item.screenId;
        document.body.append(a);
        a.click();
        a.parentElement.removeChild(a);
      }).catch(() => {})
    },
    async  getScreenTpl () {
      const data = (await getScreenTpl()).data;
      this.ScreenTpl = data;
    },
    openDialog () {
      this.dialogVisible = true;
      this.selectParams = { type: 'pc', templateId: 1, ...this.selectParams }
    },
    closeCreate () {
      this.$refs.form.resetFields();
      this.selectParams.tag = [];
      this.dialogVisible = false;
    },
    createScreen () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const res = await createScreentpl(this.selectParams);
          if (res.success) {
            this.$router.push({ path: `/screen/edit/${res.data.screenId}` });
          } else {
            this.$message.error(res.message);
          }
        }
      })
    },
    editScreentpl (id) {
      this.$router.push({ path: `/screen/edit/${id}` });
    },
    deleteScreentpl (item) {
      this.$confirm('确认删除该模板？', { title: '提示', type: 'warning' }).then(async () => {
        const res = await deleteScreentpl({ id: item.id, screenId: item.screenId });
        if (res.success) {
          this.$message.success('删除成功');
          this.getScreenTpl();
        }
      })
    },
    addTag () {
      this.showInput = true;
    },
    delTag (index) {
      this.selectParams.tag.splice(index, 1);
    },
    inputBlur () {
      if (this.tagVal) {
        this.selectParams.tag.push(this.tagVal);
        this.tagVal = '';
      }
      this.showInput = false;
    },
    backPage () {
      this.$router.push('/')
    },
    previewScreentpl (screenId) {
      const token = localStorage.getItem('token');
      const userId = localStorage.getItem('userId');
      var url = `/screen/preview/${screenId}?token=${token}&userId=${userId}`;
      openNewTab(url)
    }
  }

}
</script>

<style lang="scss" scoped>
.create-screen {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #191c23;

  .top-bar {
    height: 50px;
    width: 100%;
    position: relative;

    .right-bar {
      position: absolute;
      left: 150px;
      top: 20px;
      height: 5px;
      width: 100%;
      border-top: 1px solid #2681ff;
      border-left: 2px solid #2681ff;
      background: rgba(55,126,255,.04);
      border-top-left-radius: 5px;
      transform: skewX(-45deg);
      box-shadow: 0 5px 28px 0 rgba(55,126,255,.28);
    }

    .left-bar {
      position: absolute;
      left: 0;
      top: 24px;
      height: 25px;
      width: 138px;
      border-right: 2px solid #2681ff;
      border-bottom: 1px solid #2681ff;
      transform: skewX(-45deg);
      border-bottom-right-radius: 5px;
      box-shadow: 0 5px 28px 0 rgba(55,126,255,.28);
    }

    .return-btn {
      position: absolute;
      left: -31px;
      top: 0;
      width: 180px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      background: #151b22;
      cursor: pointer;
      transform: skewX(-45deg);
      border-bottom-right-radius: 5px;
      font-weight: bolder;

      .return-text {
        display: inline-block;
        color: #fff;
        font-size: 14px;
        margin-left: 10px;
        transform: skewX(45deg);
        transition: .2s;
      }
    }
  }

  .creator-wp {
    height: calc(100% - 50px);
    margin-left: 84px;

    .template-list {
      padding-bottom: 100px;
      padding-top: 60px;
      height: 100%;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      color: #fff;

      .template-item {
        width: 258px;
        height: 184px;
        box-shadow: 0 0 10px -6px #000;
        border: 1px solid #3a4659;
        margin: 16px;
        transition: .2s;
        cursor: default;
        outline: 1px solid transparent;

        .template-image {
          width: 100%;
          height: 146px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: .2s;
          position: relative;

          .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            position: relative;
          }

          .template-mask {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            position: absolute;
            background: rgba(0,0,0,.5);
            transition: .2s;
            pointer-events: none;
            opacity: 0;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
          }
        }
        .template-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10px;
          height: 36px;
          background: #1d262e;
          transition: .2s;

          .template-name {
            font-size: 12px;
          }
          .template-size {
            color: #999;
            text-align: right;
            font-family: Arial,Helvetica,sans-serif;
          }
        }
      }
      .template-item:hover {
        outline: 1px solid #2681ff;
      }
      .template-item:hover .template-mask {
        pointer-events: all;
        opacity: 1;
      }
      .--blank {
        position: relative;
        outline:1px solid #2681ff;
        .template-image {
          box-shadow: inset 0 0 46px 0 rgba(136,215,255,.29);
        }
        .template-info {
          justify-content: center;
          font-size: 14px;
          border-top: 1px solid #2681ff;
        }
      }
    }
  }
  .preview-btn {
    margin-top: 10px;
    margin-left: 0;
    background-color: transparent;
    border: 1px solid #2681ff;
    color: #2681ff;
  }
  .preview-btn:hover {
    color: #fff;
    background: #409fff;
    border-color: #409fff;
  }
  ::v-deep .el-form-item__label {
    color: #fff;
  }
  .el-select {
    width: 100%;
  }

  .tag-wrap {
    margin-right: 10px;
    .tag-item {
      display: inline-block;
      background: #393b4a;
      color: #fafafa;
      line-height: 24px;
      font-size: 12px;
      border: none;
      padding: 2px 7px;
      margin-bottom: 6px;
      margin-right: 6px;
    }
    .tag-input {
      width: 70px;
      margin-right: 6px;
    }
  }
}
</style>
