<template>
  <div class="config-editor">
    <JsonEditor
      height="100%"
      v-model="configData"
      :mode="'code'"
      :modes="['code']"
      isTemp
      @json-change="val => staticDataChange(val)" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import JsonEditor from '@/components/editor/data-source/JsonEditor';
export default {
  data () {
    return {
      configData: {}
    }
  },

  components: {
    JsonEditor
  },

  created () {
    this.configData = _.cloneDeep(this.callbacks);
  },

  mounted () {
  },

  computed: {
    ...mapState('customcomp', ['callbacks'])
  },

  methods: {
    staticDataChange: _.debounce(function (json) {
      this.$store.commit('customcomp/handlecallbackCode', json)
      // this.$emit('config', this.config)
      this.$store.dispatch('customcomp/updateCustom', this.$route.params.customId).then(() => {
        // this.$message.success('保存成功！');
      })
    }, 1000)
  }

}
</script>

<style lang="scss" scoped>
.config-editor {
  width: 100%;
  height: 100%;
}
</style>
