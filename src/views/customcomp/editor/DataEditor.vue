<template>
  <div class="data-editor">
    <JsonEditor
      height="100%"
      v-model="jsonData"
      :mode="'code'"
      :modes="['code']"
      isTemp
      @json-change="val => staticDataChange(val)" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import JsonEditor from '@/components/editor/data-source/JsonEditor';
export default {
  data () {
    return {
      jsonData: []
    }
  },

  components: {
    JsonEditor
  },

  created () {
    this.jsonData = _.cloneDeep(this.dataCode);
  },

  mounted () {
  },

  watch: {
  },

  computed: {
    ...mapState('customcomp', ['dataCode'])
  },

  methods: {
    staticDataChange: _.debounce(function (json) {
      this.$store.commit('customcomp/handleDataCode', json)
      this.$emit('runCode')
      this.$store.dispatch('customcomp/updateCustom', this.$route.params.customId).then(() => {
        // this.$message.success('修改成功');
      })
    }, 1000)
  }

}
</script>

<style lang="scss" scoped>
.data-editor {
  width: 100%;
  height: 100%;
}

</style>
