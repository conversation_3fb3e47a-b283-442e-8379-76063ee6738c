<template>
  <div class="editor">
    <codemirror
      class="code"
      :style="fontStyle"
      :options="codeOptions"
      ref="codeArea"
      v-model="code"
      :value="code"></codemirror>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { codemirror } from 'vue-codemirror'
import cmConfig from '@/utils/codemirrorOptions'
import 'codemirror/lib/codemirror.css'
import emitter from '@/utils/bus';
export default {
  data () {
    return {
      code: '',
      codeOptions: {},
      modeStyleList: {
        HTML: 'text/html',
        CSS: 'css',
        JavaScript: 'text/javascript'
      },
      lintList: ['HTML', 'CSS', 'JavaScript'] // Current languages which has lint function
    }
  },

  components: {
    codemirror
  },

  props: {
    codeMode: String,
    showCodeArea: Boolean
  },

  created () {
  },

  mounted () {
    this.initEditor();
    // const cm = this.getCodeMirror();
    setTimeout(() => {
      if (this.showCodeArea) {
        const codeArea = this.$refs.codeArea
        codeArea.refresh()
        codeArea.codemirror.focus()
      }
    }, 3100)
    emitter.on('reset_code', this.initEditor)
  },

  computed: {
    ...mapState('customcomp', ['instanceCode',
      'instanceSetting', 'currentTemp']),
    fontStyle () {
      const { fontFamily, fontSize } = this.instanceSetting
      return {
        fontFamily,
        fontSize: `${fontSize}px`
      }
    }
  },

  watch: {
    showCodeArea (newState) {
      // Make the current display editor get focus
      if (newState) {
        const codeArea = this.$refs.codeArea
        codeArea.refresh()
        codeArea.codemirror.focus()
      }
    }
  },

  methods: {
    initEditor () {
      const instanceCode = this.instanceCode
      const codeMode = this.codeMode
      this.codeOptions = cmConfig(codeMode, this.currentTemp)
      const codeOptions = this.codeOptions
      codeOptions.mode = this.modeStyleList[codeMode]
      codeOptions.lint = this.getLintOpts(codeMode)
      this.code = instanceCode[codeMode].content
      this.watchCode = this.$watch(
        'code',
        _.debounce(function (code) {
          this.$store.commit('customcomp/handleInstanceCode', { codeMode, code });
          this.$emit('runCode')
          this.$store.dispatch('customcomp/updateCustom', this.$route.params.customId).then(() => {
            // this.$message.success('修改成功！');
          })
        }, 1000)
      )
    },
    getCodeMirror () {
      return this.$refs.codeArea.codemirror
    },
    getLintOpts (codeMode) {
      let lint = false
      if (this.lintList.includes(codeMode)) {
        switch (codeMode) {
          case 'JavaScript': {
            lint = {
              esversion: 2021
            }
            break
          }
          default: {
            lint = true
          }
        }
      }
      return this.instanceSetting.lint && lint
    }
  }

}
</script>

<style lang="scss" scoped>
.editor {
  width: 100%;
  height: calc(100% - 56px);
  ::v-deep .code {
    height: 100%;
    overflow: hidden;

    .CodeMirror {
      height: 100% !important;
      resize: none;
      outline: none;
      border: none;
      font-family: inherit;
      font-size: inherit;
      background-color: #191F28;
    }

    .CodeMirror-gutter {
      background: #29313A;
    }
    .CodeMirror-linenumber {
      color: none;
    }
  }
}

</style>
