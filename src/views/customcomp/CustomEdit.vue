<template>
  <div class="custom-edit">
    <div class="custom-edit-content">
      <div class="edit-header">
        <div class="custom-exit">
          <img src="../../assets/img/custom-exit.png" alt="" @click="jumpTo">
          伏羲组件编辑器<span class="comp-name">/{{compName}}</span>
        </div>
        <div class="temp-list">
          <!-- <div class="temp-item" @click="dialogVisible = true">选择模版</div> -->
          <div
            class="temp-item"
            v-for="item in templateData"
            :key="item.name"
            @click="changeTemp(item)"
            :class="{'active': currentTemp === item.name}">
            {{item.name}}
          </div>
        </div>
      </div>
      <div class="main-body">
        <div class="code-area" :style="{ width: `${editorWidth}px` }" v-if="!loading">
          <el-tabs class="config-tab" v-model="activeTab">
            <el-tab-pane label="代码" name="1">
              <div class="script-type">
                <span class="script-title">脚本类型:</span>
                <div
                  v-for="(item, index) in preprocessor"
                  :key="index"
                  class="preprocessor-item"
                  :class="{'active-item': currentTab === item}"
                  @click="handleCurPreprocessor(item)"
                  >
                  {{ item }}
                </div>
                <el-button type="plain" size="mini" @click="addResource">添加</el-button>
              </div>
              <!-- <div class="type-notes">

              </div> -->
              <CustomEditor
                v-for="(item, index) in preprocessor"
                :key="index"
                :codeMode="item"
                :showCodeArea="item === currentTab"
                v-show="item === currentTab"
                @runCode="runCode">
              </CustomEditor>
            </el-tab-pane>
            <el-tab-pane label="数据" name="2">
              <DataEditor @runCode="runCode"/>
            </el-tab-pane>
            <el-tab-pane label="配置项" name="3">
              <ConfigEditor />
            </el-tab-pane>
            <el-tab-pane label="回调" name="4">
              <CallbackEditor />
            </el-tab-pane>
          </el-tabs>
          <div>
          </div>
        </div>
        <div class="resize borbox" @mousedown="viewResize"></div>
        <div class="view-area" :class="{ 'disabled-events': disabledEvents }" :style="{ width: `${iframeWidth}px` }" v-if="!loading">
          <div class="release">
            <div class="comp-size">
              <div class="input-box">
                <label>组件宽度：</label>
                <el-input size="mini" class="size-input" v-model.number="width"/>
              </div>
              <div class="input-box">
                <label>组件高度：</label>
                <el-input size="mini" class="size-input" v-model.number="height"/>
              </div>
            </div>
            <div>
              <el-button size="mini" type="primary" @click="publish" icon="el-icon-s-promotion" :loading="publishBtn">发布</el-button>
            </div>
          </div>
          <div class="iframe-box">
            <div class="iframe-container" ref="iframeContainer" :style="{ width: width + 'px', height: height + 'px' }">
              <iframe class="viewIframe" ref="viewIframe" :srcdoc="srcdoc"/>
            </div>
          </div>
        </div>
      </div>
    </div>
    <seatom-loading v-if="loading"></seatom-loading>
    <el-dialog
      :title="`添加${addResourceType}资源`"
      :visible.sync="resourceDialog"
      top="0"
      width="800px"
      :close-on-click-modal="false">
      <div class="btnGroup mb10">
        <el-button
          type="plain"
          icon="el-icon-plus"
          size="mini"
          @click="addOneResource"
          >添加资源</el-button>
      </div>
      <el-table
        empty-text="暂无数据"
        :height="300"
        :data="resourceData"
        size="mini">
        <el-table-column label="名称" width="200">
          <template slot-scope="scope">
            <!-- <span>{{scope.row}}</span> -->
            <el-input
              v-model="resourceData[scope.$index].name"
              size="mini"
              :disabled="resourceData[scope.$index].type == 'static'"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="地址">
          <template slot-scope="scope">
            <!-- <span>{{scope.row}}</span> -->
            <el-input
              v-model="resourceData[scope.$index].url"
              :disabled="resourceData[scope.$index].type == 'static'"
              size="mini"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="50">
          <template slot-scope="scope">
            <el-button
              v-if="resourceData[scope.$index].type == 'add' "
              @click="deleteResource(scope)"
              type="text"
              icon="el-icon-delete"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="plain" @click="confirmAddResource" size="mini">确定</el-button>
        <el-button type="text" @click="resourceDialog = false" size="mini">取消</el-button>
      </span>

    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import CustomEditor from '@/views/customcomp/CustomEditor';
import DataEditor from '@/views/customcomp/editor/DataEditor';
import ConfigEditor from '@/views/customcomp/editor/ConfigEditor';
import CallbackEditor  from '@/views/customcomp/editor/CallbackEditor';
import templateList from '@/utils/template';
import emitter from '@/utils/bus';
import ConfigTree from '@/lib/ConfigTree';
import { infoCustom, publishCustom, createCustomScreenshot } from '@/api/custom';
import { getResourceList } from '@/api/workspace';
import { screenshot } from '@/utils/base';
export default {

  data () {
    return {
      activeTab: '1',
      disabledEvents: false,
      templateData: templateList,
      width: 0,
      height: 0,
      loading: false,
      initInstanceCode: {},
      dialogVisible: false,
      resourceDialog: false,
      srcdoc: '',
      publishBtn: false,
      compName: '',
      resourceData: [],
      addResourceType: ''
    }
  },

  components: {
    CustomEditor,
    DataEditor,
    ConfigEditor,
    CallbackEditor
  },

  created () {
    this.init();
  },

  mounted () {
    this.editWidthInit();
    window.onresize = () => {
      this.editWidthInit();
    }
  },

  computed: {
    ...mapState('customcomp', ['echartsId', 'editorWidth', 'currentTemp', 'dataCode', 'configCode', 'compConfig',
      'preprocessor', 'iframeWidth', 'currentTab', 'instanceCode', 'jsResources', 'custom_id']),
    size () {
      return { width: this.width, height: this.height };
    }
  },

  watch: {
    'size' (newSize) {
      if (!this.loading && this.$refs.viewIframe) {
        this.$store.commit('customcomp/setCompWH', newSize)
        this.$nextTick(() => {
          this.runCode();
        })
      }
    }
  },

  methods: {
    async init () {
      this.infoCustom(this.$route.params.customId);
      await getResourceList({'resourceType': 'js' })
    },
    async infoCustom (custom_id) {
      this.loading = true
      const res = await infoCustom({ id: custom_id });
      if (res && res.success && res.data) {
        this.compName = res.data.compConfig.chartConfig.cn_name
        this.initInstanceCode = res.data.instanceCode
        // this.$store.commit('customcomp/setInstanceCode', )
        this.$store.commit('customcomp/infoData', res.data)
        this.width = this.compConfig?.width
        this.height = this.compConfig?.height
        const initTemp = templateList.find(item => {
          return item.name === this.currentTemp
        })
        if (!this.instanceCode?.JavaScript?.resources?.length) {
          this.$store.commit('customcomp/setInstanceCode', _.cloneDeep(initTemp.instanceCode))
        }
        this.loading = false
        this.$nextTick(() => {
          this.runCode()
        })
      }
    },
    editWidthInit () {
      const { clientHeight: clientH, clientWidth: clientW } = document.body
      const avgW = (clientW - 4) / 2 // 编辑器和预览区域平分后的大小
      if (avgW % 1 !== 0) {
        const floorAvg = Math.floor(avgW)
        this.$store.commit('customcomp/handleEditorW', floorAvg);
        this.$store.commit('customcomp/handleIframeW', floorAvg + 1);
      } else {
        this.$store.commit('customcomp/handleEditorW', avgW);
        this.$store.commit('customcomp/handleIframeW', avgW);
      }
    },
     addResource () {
      console.log(this.currentTab)
      console.log(this.instanceCode[this.currentTab])
      this.addResourceType = this.currentTab;
      this.resourceData = (this.instanceCode[this.currentTab].resources || []).map(
        (r) => {
          return {
            ...r,
          }
        }
      )
      this.resourceDialog = true
    },
    addOneResource () {
      this.resourceData.push({
        url: '',
        name: '',
        type: 'add'
      })
    },
    deleteResource (e) {
      this.resourceData.splice(e.$index, 1)
    },
    confirmAddResource () {
      let resources = this.resourceData.map((item) => {
        return {
          ...item,
        }
      })
      this.$store.commit('customcomp/setCodeResource', {
        type: this.addResourceType,
        resources,
      })
      this.$store.dispatch('customcomp/updateCustom', this.$route.params.customId)
      this.cancelAddResource();
      this.runCode()
    },
    cancelAddResource () {
      this.resourceDialog = false
      this.addResourceType = ''
      this.resourceData = []
    },

    viewResize (e) {
      const starX = e.clientX
      const viewW = this.iframeWidth
      const editorW = this.editorWidth
      const wholeW = viewW + editorW
      const move = e => {
        this.disabledEvents = true
        const curX = e.clientX;
        const finW = editorW + (curX - starX)
        if (finW > 100 && wholeW - finW > 100) {
          this.$store.commit('customcomp/handleEditorW', finW);
          this.$store.commit('customcomp/handleIframeW', wholeW - finW);
        }
      }
      const up = e => {
        this.disabledEvents = false
        document.removeEventListener('mousemove', move);
        document.removeEventListener('mouseup', up);
      }
      document.addEventListener('mousemove', move);
      document.addEventListener('mouseup', up);
    },
    handleCurPreprocessor (item) {
      this.$store.commit('customcomp/handleCurrentTab', item)
    },
    createHtml (html, css, js, jsResources, cssResources) {
      let _cssResources = cssResources
      .map((item) => {
        return `<link href="${item.url}" rel="stylesheet">`
      })
      .join('\n')
      let _jsResources = jsResources
        .map((item) => {
          return `<script src="${item.url}"><\/script>`
        })
        .join('\n')
      let head = `
        <title>预览<\/title>
        <style type="text/css">
          * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            border: 0;
            outline: 0;
            font-size: 100%;
            vertical-align: baseline;
          }
          html,body { margin: 0; background:white }
          ${css}
        <\/style>
        ${_cssResources}
      `
      let body = `
        ${this.currentTemp=== 'ECharts' ? `<div id="`+ this.echartsId + `"></div>` : `<div>${html}</div>`}
        ${_jsResources}
        <script>
          try {
            (function foo(){
              document.body.firstElementChild.style.width = ${this.$refs.viewIframe.offsetWidth} + 'px';
              document.body.firstElementChild.style.height = ${this.$refs.viewIframe.offsetHeight} + 'px';
              this.vm = {};
              this.vm.refs = {};
              const domList = document.body.querySelectorAll("[ref]");
              Array.prototype.forEach.call(domList, item => {
                const ref = item.getAttribute('ref');
                if (Object.prototype.hasOwnProperty.call(this.vm.refs, ref)) {
                  if (Array.isArray(this.vm.refs[ref])) {
                    this.vm.refs[ref].push(item);
                  } else {
                    const arr = [];
                    arr.push(this.vm.refs[ref], item);
                    this.vm.refs[ref] = arr;
                  }
                } else {
                  this.vm.refs[ref] = item;
                }
              });
              this.vm.data = ${JSON.stringify(this.dataCode)}
              this.vm.config = ${JSON.stringify(this.handleConfig(this.configCode))};
              ${this.currentTemp=== 'ECharts' ?
             `this.container = document.getElementById('` + this.echartsId + `');
             this.myChart = echarts.init(this.container);
             this.option = null\n`
             + js +`\n
             if (!!option) {
               myChart.setOption(option);
             }`
             : js }
            })()
          } catch (err) {
            console.error('js代码运行出错')
            console.error(err)
          }
        <\/script>
      `
      return this.assembleHtml(head, body)
    },
    assembleHtml (head, body) {
      return `<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8" />
            ${head}
        </head>
        <body>
            ${body}
        </body>
        </html>`
    },
    runCode () {
      try {
        this.srcdoc = ''
        const html = this.instanceCode?.HTML?.content
        const css = this.instanceCode?.CSS?.content
        const js = this.instanceCode?.JavaScript?.content

        let _jsResources = this.instanceCode && this.instanceCode.JavaScript.resources.map((item) => {
          return { ...item }
        })
        let _cssResources = this.instanceCode && this.instanceCode.CSS.resources.map((item) => {
          return { ...item }
        })
        this.srcdoc = this.createHtml(html, css, js, _jsResources, _cssResources)
      }  catch (error) {
        console.log(error)
      }
    },
    changeTemp (temp) {
      if (temp.name === this.currentTemp) return
      this.$confirm('切换之后内容将会被清空,确定要清空吗？', '', {
        confirmButtonText: '确定(Enter)',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$store.commit('customcomp/handleCurrentTemp', temp.name)
        this.$store.commit('customcomp/setInstanceCode', _.cloneDeep(temp.instanceCode))
        emitter.emit('reset_code')
      }).catch(() => {})
      // console.log(temp)
      // this.dialogVisible = true

    },
    handleConfig (value) {
      const configTree = new ConfigTree({ children: value });
      return configTree.toObject();
    },
    updateConfig () {
      // this.loading = true
      this.$store.dispatch('customcomp/updateCustom', this.$route.params.customId).then(() => {
        // this.loading = false
        this.$message.success('保存成功！');
      })
    },
    async publish () {
      // this.loading = true
      this.publishBtn = true;
      // const { height, width } = this.$refs.iframeContainer.getBoundingClientRect();
      const comId = this.$route.params.customId;
      // console.log(this.$route)
      const dom = document.querySelector('.viewIframe').contentDocument.body
      this.$store.dispatch('customcomp/updateCustom', this.$route.params.customId).then(async () => {
        try {
          screenshot(dom, async url => {
            const data = {
              comId,
              comUrl: url
            }
            try {
              await createCustomScreenshot(data)
              const res = await publishCustom( { id: this.$route.params.customId } )
              if (res && res.success) {
                this.$message.success('发布成功');
              } else {
                this.$message.error('发布失败');
              }
              this.publishBtn = false;
            } catch (error) {
              this.publishBtn = false;
            }
          }, 'system/common/comicon')
        } catch (error) {
          this.publishBtn = false;
        }
      })
    },
    jumpTo () {
      this.$router.push({
        name: 'workspace/component',
        params: {
          workspaceId: parseInt(window.localStorage.getItem('workspaceId'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-edit {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #191F28;

  &-content {
    width: 100%;
    height: 100%;

    .edit-header {
      width: 100%;
      height: 70px;
      background: #191F28;
      box-shadow: 0px 1px 0px #000000;
      display: flex;
      align-items: center;
      padding-left: 16px;
      justify-content: space-between;
      padding-right: 56px;
      font-size: 24px;
      color: #FFFFFF;

      .custom-exit {
        display: flex;
        img {
          margin-right: 24px;
          cursor: pointer;
        }
      }

      .temp-list {
        display: flex;

        .temp-item {
          padding:14px 29px;
          color:#fff;
          border-radius: 8px;
          font-weight: 600;
          font-size: 14px;
          cursor: pointer;
          margin-left: 8px;
          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
          }
          &.active {
            background-color: rgba(255, 255, 255, 0.1);
            position: relative;
            &::before {
              content: '';
              display: block;
              position: absolute;
              height: 4px;
              width: 4px;
              border-radius: 50%;
              background-color: #fff;
              left: 50%;
              transform: translateX(-50%);
              bottom: 20%;
            }
          }
        }
      }
    }

    .main-body {
      height: calc(100% - 70px);
      display: flex;
      background-color: #29313A;

      .code-area {
        background-color: #191F28;
        .script-title {
          color: #BFC1C4;
          font-family: PingFang SC;
          font-style: normal;
          font-weight: normal;
          font-size: 12px;
          margin: 0 28px;

        }

        .script-type {
          height: 56px;
          width: 100%;
          background-color: rgba(21, 22, 24, 0.72);
          display: flex;
          align-items: center;

          .preprocessor-item {
            padding: 4px 16px;
            background: rgba(204, 219, 255, 0.1);
            border-radius: 16px;
            margin-right: 16px;
            cursor: pointer;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
          }
          .active-item {
            background: rgba(61, 133, 255, 0.3);
          }
          &::before {
            content: '';
            height: 100%;
            width: 38.8px;
            display: block;
            background-color: #29313A;
          }
        }

        .type-notes {
          height: 56px;
          width: 100%;
          background: rgba(21, 22, 24, 0.72);
          &::before {
            content: '';
            height: 100%;
            width: 38.8px;
            display: block;
            background-color: #29313A;
          }

        }
      }
      .resize {
        width: 4px;
        height: 100%;
        border: 2px solid #262626;
        cursor: w-resize;
        &:hover {
          border-color: yellow;
        }

      }
      .view-area {
        .release {
          height: 55px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 25px;
          // background-color: rgba(21, 22, 24);
          .comp-size {
            display: flex;
            .input-box {
              display: flex;
              align-items: center;
              label {
                flex-shrink: 0;
                font-family: 思源黑体Medium;
                font-style: normal;
                font-weight: normal;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.7);
              }
              .size-input {
                background: rgba(255, 255, 255, 0.05);;
                color: #fff;
                border: 1px solid transparent;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                transition: .2s;
                margin-right: 16px;
                width: 112px;
                border-radius: 4px;
              }
            }
          }
        }
        .iframe-box {
          display: flex;
          justify-content: center;
          align-items: center;
          height: calc(100% - 55px);
          width: 100%;
          overflow: auto;
          .iframe-container {
            border: 1px dashed red;
          }
          .viewIframe{
            width: 100%;
            height: 100%;
            html,body {
              height: 100%;
            }
          }
        }
        .basic-config {
          height: 208px;
          width: 100%;
          background-color: #191F28;
        }
      }
      .disabled-events {
        pointer-events: none;
      }
    }
  }

  .config-tab ::v-deep {
    > .el-tabs__header {
      margin: 0;
      .el-tabs__nav {
        width: 100%;
        background-color: #29313A;
      }
      .el-tabs__active-bar {
        bottom: unset;
        background-color: var(--seatom-sub-main-color);
      }
      .el-tabs__nav-scroll {
        overflow: auto;
      }
      .el-tabs__item {
        user-select: none;
        width: 160px;
        text-align: center;
        color: #bfbfbf;
        padding: 0;
        background: #2e343c;
        height: 55px;
        line-height: 55px;
        font-size: 16px;
        &.is-active {
          color: var(--seatom-sub-main-color);
          background-color: #22242b;
        }
      }
      .el-tabs__nav-wrap::after {
        display: none;
      }
    }
    > .el-tabs__content {
      height: calc(100vh - 125px);
      overflow-x: hidden;
      overflow-y: auto;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
  ::v-deep .el-dialog {
    .el-dialog__body {
      display: flex;
      height: 400px;
      overflow: auto;
      flex-wrap: wrap;
      flex-direction: column;
    }
    .dialog-temp-item {
      width: 230px;
      height: 70px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgb(36 36 36);
      float: left;
      margin-right: 10px;
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        background-color: rgba(36, 36, 36, 0.1);
      }

      .icon {
        width: 60px;
        height: 60px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        margin-left: 10px;
      }

      .name {
        font-weight: bold;
        margin-left: 20px;
        font-size: 18px;
      }
    }
  }
  ::v-deep {
    .el-input__inner {
    }
    .el-input__inner:hover {
      border-color: #409EFF;
    }
    .el-icon-s-promotion,.el-icon-loading {
      color: white !important;
    }
  }
  .comp-name {
    color: #3D85FF;
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    margin-left: 10px;
    display: flex;
    align-items: center;
  }
}
.mb10 {
  margin-bottom: 10px;
}

</style>
