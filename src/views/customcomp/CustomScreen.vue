<template>
  <div class="screen-view-wrap_box">
    <div class="iframe-container" ref="iframeContainer" :style="{ width: width + 'px', height: height + 'px' }">
      <iframe class="viewIframe" ref="viewIframe" :srcdoc="srcdoc"/>
    </div>
  </div>
</template>

<script>
import { infoCustom } from '@/api/custom';
import ConfigTree from '@/lib/ConfigTree';
import { uuid } from '@/utils/base';
export default {
  name: 'CustomPreview', // 截图专用

  data () {
    return {
      srcdoc: '',
      width: 0,
      height: 0,
      instanceCode: {},
      echartsId: uuid('echarts'),
      dataCode: []
    }
  },

  created () {
  },

  mounted () {
    this.init();
  },

  methods: {
    async init () {
      const res = await infoCustom({ id: this.$route.query.customId });
      if (res && res.success && res.data) {
        this.instanceCode = res.data.instanceCode
        this.width = res.data.compConfig.chartConfig.width
        this.height = res.data.compConfig.chartConfig.height
        this.currentTemp = res.data.currentTemp
        this.dataCode = res.data.compConfig.chartConfig.data.source
        this.$nextTick(() => {
          this.viewComp();
        })
      }
    },
    handleConfig (value) {
      const configTree = new ConfigTree({ children: value });
      return configTree.toObject();
    },
    createHtml (html, css, js, jsResources, cssResources) {
      const _cssResources = cssResources
        .map((item) => {
          return `<link href="${item.url}" rel="stylesheet">`
        })
        .join('\n')
      const _jsResources = jsResources
        .map((item) => {
          return `<script src="${item.url}"><\/script>`
        })
        .join('\n')
      const head = `
        <title>预览<\/title>
        <style type="text/css">
          html,body { margin: 0; background:white }
          ${css}
        <\/style>
        ${_cssResources}
      `
      const body = `
        ${this.currentTemp === 'ECharts' ? '<div id="' + this.echartsId + '"></div>' : html}
        ${_jsResources}
        <script>
          try {
            (function foo(){
              document.body.firstElementChild.style.width = ${this.$refs.viewIframe.offsetWidth} + 'px';
              document.body.firstElementChild.style.height = ${this.$refs.viewIframe.offsetHeight} + 'px';
              this.vm = {};
              this.vm.data = ${JSON.stringify(this.dataCode)}
              this.vm.config = ${JSON.stringify(this.handleConfig(this.configCode))};
              ${this.currentTemp === 'ECharts'
             ? 'this.container = document.getElementById(\'' + this.echartsId + `');
             this.myChart = echarts.init(this.container);
             this.option = null\n` +
             js + `\n
             if (!!option) {
               myChart.setOption(option);
             }`
             : js}
            })()
          } catch (err) {
            console.error('js代码运行出错')
            console.error(err)
          }
        <\/script>
      `
      return this.assembleHtml(head, body)
    },
    assembleHtml (head, body) {
      return `<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8" />
            ${head}
        </head>
        <body>
            ${body}
        </body>
        </html>`
    },
    viewComp () {
      this.srcdoc = ''
      const html = this.instanceCode?.HTML?.content
      const css = this.instanceCode?.CSS?.content
      const js = this.instanceCode?.JavaScript?.content
      const _jsResources = this.instanceCode && this.instanceCode.JavaScript.resources.map((item) => {
        return { ...item }
      })
      const _cssResources = this.instanceCode && this.instanceCode.CSS.resources.map((item) => {
        return { ...item }
      })
      this.srcdoc = this.createHtml(html, css, js, _jsResources, _cssResources)
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-view-wrap_box {
  position: relative;
  width: 100%;
  height: 100%;
}
.iframe-container {
  // border: 1px dashed red;
}
.viewIframe{
  width: 100%;
  height: 100%;
  html,body {
    height: 100%;
  }
}

</style>
