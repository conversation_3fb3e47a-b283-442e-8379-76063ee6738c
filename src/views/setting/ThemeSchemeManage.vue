<template>
  <div class="themescheme-manage">
    <div class="theme-head">
      <hz-icon class="icon" name="theme"></hz-icon>
      主题方案
      <el-button type="text" size="mini" @click="add">新增</el-button>
    </div>
    <div class="theme-content">
      <el-table class="mapping-t" :data="tableData" height="100%" size="mini">
        <el-table-column label="名称" prop="name" width="200" align="left"></el-table-column>
        <el-table-column label="颜色" prop="colorList" align="left">
          <template slot-scope="scope">
            <div class="color-list">
              <div class="color-item" :style="{'background-color': color}" v-for="(color, index) in scope.row.colorList" :key="index"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="editItem(scope.row)">修改</el-button>
            <el-button type="text" @click="deleteItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <seatom-loading v-if="loading"></seatom-loading>
    <ThemeScheme ref="scheme" @refresh="getThemeScheme" />
  </div>
</template>

<script>
import ThemeScheme from '@/components/theme/ThemeScheme';
import { themeScheme, deleteThemeScheme } from '@/api/theme';
export default {
  name: 'ThemeSchemeManage', // 主题方案管理
  components: {
    ThemeScheme
  },
  data () {
    return {
      show: false,
      tableData: [],
      loading: false
    }
  },
  created () {
    this.getThemeScheme();
  },
  methods: {
    async getThemeScheme () { // 获取主题方案
      this.loading = true;
      const res = await themeScheme();
      if (res && res.success) {
        this.tableData = res.data || [];
      }
      this.loading = false;
    },
    add () {
      this.$refs.scheme.showDialog();
    },
    editItem (item) {
      this.$refs.scheme.showEdit(item);
    },
    deleteItem (item) {
      this.$confirm('删除该方案将同步清空对应主题的方案，确认删除？', '提示', { type: 'warning' }).then(async () => {
        const data = {
          id: item.id
        }
        const res = await deleteThemeScheme(data);
        if (res && res.success) {
          this.$message.success('删除成功');
          this.getThemeScheme();
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.themescheme-manage {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #171b22;
    .theme-head {
      font-size: 14px;
      color: #fafafa;
      height: 45px;
      line-height: 45px;
      padding: 0 20px;
      .icon {
        margin-right: 5px;
      }
    }
    .theme-content {
      display: flex;
      flex: 1;
      padding: 0 10px 10px;
      overflow: hidden;
    }
}
.color-list {
  display: flex;
  .color-item {
    height: 14px;
    width: 30px;
  }
}
</style>
