<template>
  <div class="import-screen">
    <el-dialog :visible.sync="importShow" title="大屏模版导入" width="500px" top="0" :close-on-click-modal="false" :before-close="close">
      <el-form :model="form" :rules="rules" label-width="90px" size="mini" ref="form">
        <el-form-item label="选择文件：">
          <el-upload
            ref="upload"
            size="mini"
            class="upload-demo"
            drag
            action=""
            accept=".tgz"
            :auto-upload="false"
            :file-list="fileList"
            :on-change="handleChange">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">点击上传tgz文件，不能超过100M</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button size="mini" @click="close">取消</el-button>
        <el-button size="mini" type="primary" @click="submit" :loading="loading">导入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { importTpl } from '@/api/screen'
export default {
  name: 'ImportScreentpl', // 导入大屏
  props: {
    value: Boolean,
    manage: {
      type: Array,
      default () {
        return []
      }
    },
    active: { // 当前分组
      type: [Number, String],
      default: 0
    }
  },
  data () {
    return {
      fileList: [],
      form: {
        name: '',
        projectId: 0
      },
      rules: {
        name: [
          { required: true, message: '请输入大屏名称', trigger: 'change' },
          { max: 30, message: '最大输入30个字', trigger: 'change' }
        ],
        projectId: [
          { required: true, message: '请选择分组', trigger: 'change' }
        ]
      },
      percentage: 0,
      loading: false
    }
  },
  computed: {
    importShow: {
      get: function () {
        return this.value
      },
      set: function (val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    importShow: {
      handler: function (val) {
        if (val) {
          if (this.active === 0) {
            this.form.projectId = this.manage.length ? this.manage[0].id : '';
          } else {
            this.form.projectId = this.active;
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleChange (file, fileList) {
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]];
        const name = file.name;
        const firstName = name.split('.')[0];
        this.form.name = firstName;
      }
    },
    close () {
      this.fileList = [];
      this.importShow = false;
      this.$refs.form.resetFields();
    },
    submit () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          if (!this.fileList.length) {
            this.$message.error('请上传文件');
            return
          }
          this.loading = true;
          const data = new FormData();
          data.append('file', this.fileList[0].raw);
          data.append('workspaceId', 0);
          data.append('name', this.form.name);
          data.append('projectId', 0)
          const res = await importTpl(data, {}, progress => {
            this.percentage = Number(Math.floor(progress.loaded / progress.total * 100).toFixed(0));
          })
          if (res && res.success) {
            this.$message.success('导入成功');
            this.close();
          }
          this.loading = false;
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.import-screen {
  ::v-deep {
    .el-form-item__label {
      color: #fafafa;
      font-size: 12px;
    }
    .el-upload-dragger {
      width: 300px;
      height: 140px;
      border-radius: 0;
      background-color: #181b24;
      border: 1px solid #393b4a;
      .el-upload__text {
        font-size: 12px;
      }
    }
    .el-upload-dragger .el-icon-upload {
      font-size: 50px;
      margin: 20px 0 16px;
    }
  }
}
</style>
