<template>
  <div class="skeleton-manage">
    <div class="manage-left">
      <div class="folder-concent link-title padding-left-24"
      :class="{'select': item.id === activeFolder.id}"
      v-for="(item) in fileListImg[0].folderList"
      :key="item.id"
      @click="slelectFolder(item)">
        <div class="folder-name" >
          <i :class="[item.isOpen ? 'el-icon-folder-opened' : 'el-icon-folder']" style="margin:0 4px;"></i>
          <span class="folder-name-max-width" :title="item.name">{{item.name}}</span>
        </div>
        <span class="file-total"
        >{{item.list.length}}</span>
        <hz-icon class="del-folder-icon"
        name="trash"
        style="margin-left: 4px;height: 16px;width: 16px;"
        @click.native.stop="delFolder(item)"></hz-icon>
      </div>
    </div>
    <div class="manage-right">
        <img-preview-control
        resourceType="skeletonPicture"
        :folderId="activeFolder.id"
        :resourceLibraryCode="activeFolder.resourceLibraryCode"
        :list="activeFolder.list"
        :imgList="activeFolder.imgList"
        :selectFolderOptions="selectFolderOptions"
        @refreshResourceList="refreshResourceList"
        ></img-preview-control>
    </div>
  <!-- 删除文件前提示 -->
  <el-dialog
    title="提示"
    :visible.sync="dialogVisibleDel"
    width="30%"
    top="0"
    :close-on-click-modal="false">
    <div class="dialog-content-del-info">
      <div>删除文件夹后，该文件夹内文件会同步删除</div>
      <div>是否确认删除？</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisibleDel = false;">取 消</el-button>
      <el-button type="primary" @click="delFiles()">确 定</el-button>
    </span>
  </el-dialog>
  <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import ImgPreviewControl from '@/components/resources-control/ImgPreviewControl.vue';
import { getResourceList, delResourceFolder } from '@/api/workspace';
import { replaceUrl } from '@/utils/base';
export default {
  name: 'Skeleton', // 骨骼管理器
  components: {
    ImgPreviewControl
  },
  data () {
    return {
      loading: false,
      dialogVisibleDel: false,
      activeFolder: {
        id: 11,
        name: '1920*1080',
        isOpen: true,
        list: [],
        imgList: []
      },
      fileListImg: [
        {
          folderList: []
        }
      ],
      selectFolderOptions: [],
      delFolderList: []
    }
  },
  created () {
    this.getResourceListFun(false);
  },
  methods: {
    slelectFolder (item) {
      if (this.activeFolder.id !== item.id) {
        this.fileListImg[0].folderList.forEach(fdr => {
          fdr.isOpen = item.id === fdr.id;
        });
      }

      this.activeFolder = item;
    },
    // 刷新资源列表
    refreshResourceList (data) {
      this.getResourceListFun(true, 'skeletonPicture');
    },
    async getResourceListFun (isRefresh, resourceType = 'skeletonPicture') {
      this.loading = true;
      const res = await getResourceList({ resourceType: resourceType });
      this.loading = false;
      if (res.success) {
        if (res.data.length === 0) {
          this.$message.error('获取资源文件为空，请联系管理员');
        }
        this.fileListImg = this.getFileList(res.data, isRefresh);
        this.selectFolderOptions = [];
        this.selectFolderOptions = this.getSelectFolderOptions(res.data);
      }
    },
    // 数据映射
    getFileList (list, isRefresh) {
      list.forEach(item => {
        item.id = item.name;
        item.folderList.forEach((folder, index) => {
          folder.name = folder.resourceFolder;
          folder.isOpen = index === 0;
          if (folder.isOpen) {
            this.activeFolder = folder;
          }
          folder.isSelect = 'no';
          folder.imgList = folder.resourceList.map(it => {
            return replaceUrl(process.env.VUE_APP_SERVER_URL + it.resourceUrl);
          });
          folder.resourceList.forEach(img => {
            img.name = img.resourceName;
            img.isSelect = false;
            img.isShow = true;
            img.url = replaceUrl(process.env.VUE_APP_SERVER_URL + img.resourceUrl);
          });
          folder.list = folder.resourceList;
        });
      });
      return list;
    },
    // 获取上传文件夹列表
    getSelectFolderOptions (list) {
      const tempOptions = [];
      list.forEach(item => {
        const tempItem = {
          value: item.resourceLibraryCode,
          label: item.name,
          disabled: false,
          children: []
        };
        item.folderList.forEach(folder => {
          tempItem.children.push({
            value: folder.resourceFolder,
            label: folder.resourceFolder
          });
        });
        tempOptions.push(tempItem);
      });
      return tempOptions;
    },
    // 删除文件夹
    delFolder (item) {
      if (item.name === '默认文件夹') {
        this.$message.warn('默认文件夹不允许删除');
        return;
      }
      this.delFolderList = [item.id];
      this.dialogVisibleDel = true;
    },
    // 确定删除
    async delFiles () {
      this.loading = true;
      const res = await delResourceFolder(this.delFolderList);
      this.loading = false;
      // 删除成功
      if (res.success) {
        this.getResourceListFun(false);
        this.$message.success('操作成功');
        this.dialogVisibleDel = false;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.skeleton-manage {
  display: flex;
  // flex-direction: column;
  height: 100%;
  background: #171b22;
  overflow: hidden;
  overflow-y: auto;
  .manage-left {
    width: 240px;
    font-size: 14px;
    cursor: pointer;
    height: 100%;
    margin: 8px 0;
    .link-title {
      display: flex;
      align-items: center;
      line-height: 42px;
      padding-left: 22px;
      color: #898A8C;
      transition: color 0.2s;
      text-decoration: none;
      position: relative;
      .hz-icon {
        height: 20px;
        width: 20px;
        margin-right: 4px;
      }
      .block-16 {
        width: 16px;
        height: 16px;
      }
      &:hover {
        background: linear-gradient(0deg, #1F2430, #1F2430);
      }
      &.active {
        color: #fff;
        background: linear-gradient(0deg, #1F2430, #1F2430);
      }
      &.select {
        color: #fff;
        font-weight: bold;
        background: #3D85FF;
        box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25);
      }
    }
    .folder-concent {
      justify-content: space-between;
      .folder-name {
        cursor: pointer;
        margin-left: 4px;
        display: flex;
        align-items: center;
        font-size: 14px;
        width: 148px;
        .folder-name-max-width {
          max-width: 128px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .file-total {
        display: block;
        font-weight: bold;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        margin-right: 16px;
      }
      .del-folder-icon {
        display: none;
        margin-right: 12px;
      }
      &:hover {
        .del-folder-icon {
          display: block;
        }
        .file-total {
          display: none;
        }
      }
      .file-total-block {
        display: block !important;
      }
    }
    .padding-left-24 {
      padding-left: 24px;
    }
    .padding-left-54 {
      padding-left: 54px;
    }
    .margin-right-4 {
      margin-right: 4px;
    }
  }
  .manage-right {
    flex: 1;
    height: 100%;
    margin: 8px 0;
  }
  ::v-deep .dialog-content-del-info {
    padding: 0 16px;
  }
  ::v-deep .el-button--primary.is-plain {
    background: #1b3d6187;
    border-color: #4679af;
  }
  ::v-deep .el-button {
    padding: 6px 12px;
  }
}
</style>
