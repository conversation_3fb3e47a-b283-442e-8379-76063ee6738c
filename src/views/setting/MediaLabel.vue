<template>
  <div class='label-manage'>
    <div class='label-head'>
      <!-- <hz-icon class='icon' name='label'></hz-icon> -->
      组件标签
      <el-button type='text' style='text-align: right' size='mini' @click='goback()'>返回首页</el-button>
    </div>
    <div class='label-content'>
      <div class='comp-wrap'>
        <div class='sub-head'>
          <span>组件列表</span>
          <el-input
            v-model='searchText'
            clearable
            prefix-icon='el-icon-search'
            class='search-input'
            size='mini'
          ></el-input>
        </div>
        <div class='comp-list' ref='complist'>
          <CompList>
            <CompListItem
              class='gray-border'
              :class='{ active: comp.id === packageId }'
              v-for='comp in packageList'
              :key='comp.id'
              :title='comp.alias'
              :icon='comp.icon'
              :data='comp'
              @click='handleItemClick($event)'
            />
          </CompList>
        </div>
      </div>
      <div class='comp-label'>
        <div class='sub-head'>
          <span>设置标签</span>
          <el-button
            class='add-btn'
            type='text'
            icon='el-icon-plus'
            size='mini'
            @click='handleItemClick()'
            :disabled='!comType'
            v-if='this.listShow'
            >标签管理</el-button
          >
          <el-button
            v-else
            class='add-btn'
            type='text'
            icon='el-icon-plus'
            size='mini'
            @click='newLabel()'
            :disabled='!comType'
            >新增标签</el-button
          >
        </div>
        <div v-if='listShow' class='label-list-wrap'>
          <!-- <MediaListItem
            v-for='item in dataList'
            :key='item.id'
            :data='item'
            @editItem='editTheme'
            @delItem='delItem'
            @editName='editName'
            @setDefault='setDefault' /> -->
          <div style='width: 100%;'>
          <div  v-for='item in labelList' :key='item.categoryId'>
            <div class='labelValueTitle'>{{ item.categoryName + ':'}}</div>
            <div style='display: flex;'>
              <!-- <el-radio  v-for='it in item.tags' :key='it.id' v-model='radio1' label='1' border>{{it.name}}</el-radio> -->
              <div :class='{active: it.checked}' @click='updatas(item,it)' class='labelValue' v-for='it in item.tags' :key='it.id'><span style='margin:10px;'>{{it.name}}</span></div>
            </div>
          </div>
          </div>
          <div class='no-data' v-if='labelList.length === 0'>暂无标签</div>
        </div>
        <div v-else class='label-list-wrap'>
          <div class='label-title' v-if='newLabelIpt'>
            <input
              v-model='newLabelTitle'
              @keyup.enter='creatNewLabel(newLabelTitle)'
              type='text'
              class='labelInput el-input__inner'
              style='margin-top: 0px'
            />
            <!-- <i @click='addLabelContent(indexs)' class='addButton'></i> -->
          </div>
          <div
            class='set-content'
            v-for='(item, indexs) in labelList'
            :key='item.categoryId'
          >
            <div class='label-title'>
              {{ item.categoryName
              }}<i @click='addLabelContent(indexs)' class='addButton'></i>
                <i @click='deleteLabelTitle(item)' class='reduceButton'></i>
            </div>
            <!-- <input type='text'> -->
            <div class='label'>
              <span
                class='labelContent'
                v-for='(items, index) in item.tags'
                :key='items.id'
                >{{ items.name
                }}<i
                  class='el-icon-close el-input__icon'
                  @click='deleteLabelContent(items,index)'
                >
                </i>
              </span>
              <input
                class='labelInput el-input__inner'
                style='margin-top: 8px; margin-left: 12px;'
                v-if='inputShow === indexs'
                type='text'
                v-model='creatLabel'
                @keyup.enter='creat(item,indexs)'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <seatom-loading v-if='loading'></seatom-loading>
    <!-- 新增主题弹框 -->
    <!-- <ThemeAdd ref='add' :packageId='packageId' @refresh='getThemeList' /> -->
  </div>
</template>

<script>
import { mapState } from 'vuex';
import CompList from '@/components/editor/CompList';
import CompListItem from '@/components/editor/CompListItem';
import Message from 'hz-message';

// import ThemeAdd from '@/components/theme/ThemeAdd';
import {
  getlabelList,
  createLabel,
  update,
  deleteLabelContent
} from '@/api/theme';
// import { getFontList } from '@/api/workspace';
import { uuid } from '@/utils/base';
// import { log } from '@chenfengyuan/vue-qrcode';
export default {
  name: 'MediaLabel',
  components: {
    CompList,
    CompListItem
  },
  data () {
    return {
      comType: null,
      packageId: null,
      loading: false,
      scrollTop: 0,
      searchText: '',
      listShow: true,
      inputShow: null,
      creatLabel: '',
      newLabelIpt: false,
      newLabelTitle: '',
      labelList: [],
      compId: '',
      compName: '',
      oldId: ''
    };
  },
  computed: {
    ...mapState({
      compPackges: (state) => state.editor.compPackges,
      comScrollTop: (state) => state.comtheme.scrollTop
    }),
    complist () {
      return this.$refs.complist;
    },
    packageList () {
      if (this.searchText) {
        return this.compPackges.filter((item) =>
          item.alias.includes(this.searchText)
        );
      }
      return this.compPackges;
    }
  },
  beforeRouteEnter (to, from, next) {
    if (from.name === 'themeEdit') {
      next((vm) => {
        vm.comType = from.query.comType;
        vm.packageId = Number(from.query.packageId);
        vm.searchText = from.query.searchText || '';
      });
      return;
    }
    next();
  },
  beforeRouteLeave (to, from, next) {
    if (to.name === 'themeEdit') {
      this.$store.commit('comtheme/changeScrollTop', this.scrollTop);
    }
    next();
  },
  async created () {
    if (
      !window.cangjie ||
      (window.cangjie && window.cangjie.fonts.length === 0)
    ) {
      // this.addFontface();
    }
    await this.$store.dispatch('editor/getCompPackages');
    if (this.compPackges.length) {
      this.comType = this.comType || this.compPackges[0].name;
      this.packageId = this.packageId || this.compPackges[0].id;
      // this.getThemeList();
    }
  },
  mounted () {
    this.$nextTick(() => {
      window.addEventListener('scroll', this.handleScroll, true);
      this.$refs.complist.scrollTop = this.comScrollTop;
    });
  },
  methods: {
    newLabel () { this.newLabelIpt = true; },
    creatNewLabel () {
      if (this.newLabelTitle !== '') {
        this.labelList.push({
          categoryId: uuid(this.newLabelTitle),
          categoryName: this.newLabelTitle
        });
      }
      this.newLabelIpt = false;
      this.newLabelTitle = ''
      // this.creat(this.newLabelTitle)
    },
    addLabelContent (indexs) {
      this.inputShow = indexs;
    },
    async creat (param, indexs) {
      if (this.creatLabel !== '' && this.newLabelTitle !== '') {
        const categoryName = this.newLabelTitle;
        const params = {
          id: uuid(this.creatLabel),
          name: this.creatLabel,
          categoryId: uuid(categoryName),
          categoryName: categoryName
        };

        await createLabel(params);

        this.inputShow = null;
        this.creatLabel = '';
        this.newLabelTitle = '';
      } else {
        let flag = false
        if (this.creatLabel === '') {
          Message.error('标签名不能为空');
          return
        }
        this.labelList.forEach(item => {
          if (item.tags) {
            item.tags.forEach(it => {
              if (this.creatLabel === it.name) {
                flag = true
              }
            })
          }
        })
        if (flag) {
          Message.error('标签名不能重复命名');
          return
        }
        const params = {
          id: uuid(this.creatLabel),
          name: this.creatLabel,
          categoryId: param.categoryId,
          categoryName: param.categoryName
        };
        await createLabel(params);
        const response = await getlabelList()
        if (response && response.code === 200) {
          this.labelList = response.data;
        }
        this.inputShow = null;
        this.creatLabel = '';
        this.newLabelTitle = '';
      }
    },
    async updatas (item, it) {
      const span = item.tags.filter(value => {
        return value.checked === true
      })
      if (span.length !== 0) {
        this.oldId = span[0].id;
      } else {
        this.oldId = ''
      }
      const params = {
        compId: this.compName,
        compName: this.comType,
        id: it.id === this.oldId ? '' : it.id,
        originId: this.oldId
      }
      const res = await update(params);
      if (res.code === 200 && res.success) {
        item.tags.forEach(value => {
          value.checked = false
          it.checked = true
        })
        if (span.length !== 0 && span[0].id === it.id) {
          it.checked = false
        }
      }
    },
    handleItemClick (item) {
      if (item) {
        this.listShow = true;
        this.comType = item.name;
        this.packageId = item.id;
        this.getThemeList(item);
      } else {
        this.listShow = false;
        getlabelList();
        this.newLabelIpt = false;
      }
    },
    async deleteLabelContent (item, index) {
      const param = { id: item.id }
      const res = await deleteLabelContent(param)
      if (res && res.code === 200) {
        this.labelList.forEach(its => {
          const list = its.tags.filter((val) => val.id !== item.id)
          its.tags = list
        })
      }
    },
    async deleteLabelTitle (item) {
      const param = { categoryId: item.categoryId }
      const res = await deleteLabelContent(param)
      if (res && res.code === 200) {
        this.labelList = this.labelList.filter((val) => val.categoryId !== item.categoryId)
      }
    },
    handleScroll () {
      if (this.complist) {
        this.scrollTop = this.complist.scrollTop;
      }
    },
    goback () {
      this.$router.push('/index');
    },
    async getThemeList (item) {
      this.compName = item.type;
      const params = {
        compId: this.comType
      };
      this.loading = true;
      const res = await getlabelList(params);
      if (res && res.success) {
        this.labelList = res.data;
        // this.updata(item)
      }
      this.loading = false;
    }
  }
};
</script>

<style lang='scss' scoped>
.label-manage {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #171b22;
  .label-head {
    font-size: 14px;
    color: #fafafa;
    height: 45px;
    line-height: 45px;
    padding: 0 20px;
    .icon {
      margin-right: 5px;
    }
  }
  .sub-head {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    line-height: 30px;
    color: #bcc9d4;
    padding: 0 10px;
    background: var(--seatom-panel-title-bg);
    ::v-deep button i {
      color: #409eff;
    }
    .search-input {
      width: 150px;
      ::v-deep .el-input__inner {
        height: 24px;
        line-height: 24px;
      }
    }
  }
  .label-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    .comp-wrap {
      display: flex;
      flex-direction: column;
      width: 70%;
      margin-right: 3px;
      .comp-list {
        flex: 1;
        background: #0a0b0e;
        padding: 6px;
        overflow: auto;
        &::-webkit-scrollbar {
          display: block;
          width: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #434b55;
          border: 1px solid #434b55;
        }

        .gray-border {
          border: 1px solid #212326;
          &.active {
            border-color: #2681ff;
          }
        }
      }
    }
    .comp-label {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      .label-list-wrap {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        padding: 0px 0 30px;
        overflow: auto;
      }
    }
  }
  .no-data {
    width: 100%;
    font-size: 14px;
    color: #999;
    text-align: center;
    padding-top: 100px;
  }
  .title-set {
    width: 100%;
    font-size: 14px;
    color: #999;
    text-align: center;
  }
  .set-content {
    width: 100%;
  }
  .label-title {
    padding-left: 10px;
    width: 100%;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
    font-family: PingFang SC;
    line-height: 40px;
    color: rgba(255, 255, 255, 0.7);
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  }
  .addButton {
    cursor: pointer;
    position: sticky;
    top: 3px;
    left: 83%;
    padding: top;
    width: 16px;
    height: 16px;
    text-align: center;
    background-image: url(../../../src/assets/img/add.png);
    border-radius: 12.6667px;
    display: inline-block;
  }
  .reduceButton {
    position: sticky;
    top: 3px;
    left: 91%;
    padding: top;
    width: 16px;
    height: 18px;
    text-align: center;
    background-position: center;
    background-image: url(../../../src/assets/img/reduce.png);
    // border-radius: 12.6667px;
    display: inline-block;
    cursor: pointer;
  }
  .label {
    display: flex;
    margin-bottom: 20px;
  }
  .labelContent {
    display: flex;
    justify-content: space-around;
    background: rgba(204, 219, 255, 0.1);
    font-weight: 400;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
    margin-left: 8px;
    margin-top: 8px;
    padding: 4px 6px;
    line-height: 20px;
    border-radius: 16px;
    border: 1px solid rgba(76, 82, 95, 0.32);
    // width: 68px;
    height: 28px;
    .el-input__icon {
      width: 13px;
      height: 22px;
      line-height: 22px;
      cursor: pointer;
    }
  }
  .labelInput {
    margin-top: 8px;
    margin-left: -1px;
    width: 74px;
    height: 28px;
    background: rgba(204, 219, 255, 0.06);
    border-radius: 4px 4px 0px 0px;
  }
  .labelValueTitle {
    margin: 13px 10px 3px 13px;
    // width: 42px;
    height: 22px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(255, 255, 255, 0.5);
  }
  .labelValue {
    // width: 32px;
    margin: 10px;
    height: 20px;
    background: rgba(204, 219, 255, 0.1);
    border-radius: 4px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #FFFFFF;
    cursor: pointer;
  }
  .active {
    background: #3D85FF;
  }
}
</style>
