<template>
  <div class="setting-page">
    <div class="link-wrap">
      <el-button type="primary" @click="toPage()">上传模版</el-button>
      <ImportScreentpl v-model="importShow" />
    </div>
  </div>
</template>

<script>
import ImportScreentpl from '@/views/setting/ImportScreentpl.vue'
export default {
  name: 'Setting',
  components: {
    ImportScreentpl
  },
  data () {
    return {
      importShow: false
    }
  },
  methods: {
    toPage () {
      this.importShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.setting-page {
  height: 100%;
  padding: 30px 15px 0;
  background-color: #161a1f;
  .page-title {
    font-size: 30px;
    text-align: center;
    color: #fff;
    margin-bottom: 50px;
  }
  .link-wrap {
    text-align: center;
  }
}
</style>
