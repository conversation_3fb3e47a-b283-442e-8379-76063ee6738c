<template>
  <div class="setting-page">
    <div class="setting-header">
      <div class="header-left">
        <div>
          <!-- <hz-icon name="help-bold" class="icon-help-bold"></hz-icon> -->
          <div class="title-contanier">
            <hz-icon name="box-dian" class="icon-box-dian"></hz-icon>
            <span>伏羲后台</span>
          </div>
        </div>
        <ul class="header-nav">
          <li
            class="nav-item"
            v-for="item in tabList" :key="item"
            :class="{'nav-item-active': activeTab==item}"
            @click="activeTab=item"
          >
            {{ item }}
          </li>
        </ul>
      </div>
      <div class="header-right">
        <hz-icon name="help-bold" class="icon-nav-right"></hz-icon>
        <hz-icon name="notification" class="icon-nav-right"></hz-icon>
        <hz-icon name="nav-bold" class="icon-nav-right"></hz-icon>
        <div class="icon-shu"></div>
        <div class="icon-fount" @click="onReception">
          <hz-icon name="file-model" class="icon-nav-right icon-model"></hz-icon>
          <span>前台</span>
        </div>
        <div class="nav-user">
          <hz-icon name="nav-user" class="icon-nav-user"></hz-icon>
          <span>Admin</span>
        </div>
      </div>
    </div>
    <div class="setting-content">
      <SystemManagement v-if="activeTab=='系统管理'" />
      <div v-if="activeTab=='开发者中心'">
        <div class="page-title">管理页面</div>
        <div class="link-wrap">
          <el-button type="primary" v-for="item in list" :key="item.link" @click="toPage(item)">{{ item.name }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SystemManagement from './systemManagement.vue'

export default {
  name: 'Setting',
  data () {
    return {
      list: [
        { name: '主题配置', link: '/theme' },
        { name: '主题方案', link: '/setting/theme-scheme' },
        { name: '骨骼布局', link: '/setting/skeleton' },
        { name: '内置过滤器', link: '/setting/filter' },
        { name: '教程文档', link: '/setting/doc' },
        { name: '瓦片路径加密', link: '/setting/tools' },
        { name: '模版导入', link: '/setting/importTpl' },
        { name: '组件打包', link: '/setting/packCom' },
        { name: '组件发布', link: '/setting/publishCom' },
        { name: '媒体标签', link: '/setting/media-label' },
        { name: '语音唤醒词训练', link: 'http://**************:8848' }
      ],
      activeTab: '系统管理',
      tabList: ['开发者中心', '系统管理', '教程文档']
    }
  },
  components: { SystemManagement },
  methods: {
    toPage (item) {
      if (item.link && item.link.startsWith('http')) {
        window.open(item.link)
      } else {
        this.$router.push(item.link);
      }
    },
    onReception () {
      this.$router.push('/index')
    }
  }
}
</script>

<style lang="scss" scoped>

.setting-page {
  height: 100%;
  background-color: #13151A;
  .page-title {
    font-size: 30px;
    text-align: center;
    color: #fff;
    margin-bottom: 50px;
  }
  .link-wrap {
    text-align: center;
  }
}
.setting-header {
  height: 56px;
  background: #1F2430;
  box-shadow: 0px 0px 1px 0px rgba(4, 8, 16, 0.32), 0px 1px 3px 0px rgba(4, 8, 16, 0.32), 0px 4px 8px 0px rgba(4, 8, 16, 0.16);
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.icon-help-bold {
  width: 16px;
  height: 16px;
  color: #FFFFFF;
}
.icon-box-dian {
  width: 26px;
  height: 30px;
  vertical-align: middle;
  margin-right: 8px;
}
.title-contanier {
  height: 100%;
  font-size: 22px;
  font-weight: 600;
  color: #FFF;
  font-family: PingFang SC;
  line-height: 56px;
}
.header-nav {
  list-style: none;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.70);
  display: flex;
  margin-left: 28px;
  height: 100%;
  .nav-item {
    padding: 0 20px;
    line-height: 56px;
    cursor: pointer;
  }
  .nav-item-active {
    background: linear-gradient(to bottom, #3580FF00, #3580FF14, #3580FF66);
    color: #FFF;
    position: relative;
    &::before {
      position: absolute;
      content: "";
      width: 16px;
      height: 3px;
      background: #fff;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      bottom: 2px;
      left: 0;
      right: 0;
      margin: auto;
    }
  }
}
.setting-content {
  height: calc(100% - 56px);
}
.header-left {
  display: flex;
}
.header-right {
  margin-right: 16px;
  display: flex;
  align-items: center;
  width: 270px;
  .icon-nav-right {
    color: rgba(255, 255, 255, 1);
    width: 16px;
    height: 16px;
    margin-right: 16px;
    vertical-align: baseline;
  }
  .icon-shu {
    width: 2px;
    height: 12px;
    background-color: #fff;
  }
  .icon-fount {
    margin-left: 16px;
    width: 60px;
    height: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    color: #fff;
    border-radius: 8px;
    cursor: pointer;
    .icon-model {
      margin-right: 4px;
      margin-left: 8px;
    }
    &:hover {
      background-color: rgba(204, 219, 255, 0.06);
    }
  }
  .nav-user {
    width: 95px;
    height: 40px;
    border-radius: 8px;
    margin-left: 8px;
    display: flex;
    align-items: center;
    color: #FFF;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    &:hover {
      background: rgba(43, 121, 255, 0.24);
    }
    .icon-nav-user {
      width: 32px;
      height: 32px;
      margin-left: 12px;
      margin-right: 8px;
    }
  }
}

</style>
