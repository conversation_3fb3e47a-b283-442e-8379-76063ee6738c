<template>
  <div class="setting-tools">
    <div class="manage-left">
      <div class="folder-concent link-title padding-left-24"
      :class="{'select': item.id === activeFolder.id}"
      v-for="(item) in toolsList"
      :key="item.id"
      @click="slelectItem(item)">
        <div class="folder-name" >
          <span class="folder-name-max-width" :title="item.name">{{item.name}}</span>
        </div>
      </div>
    </div>
    <div class="manage-right">
      <div class="right-info">
        <div>输入路径需要是服务器 'tfs/tilemap/' 的子文件夹，如：'/chinamap/guangdong || /global'</div>
        <div class="info-list">
          <div style="width: 580px;margin-right: 8px;">
            <el-input
              placeholder="请输入路径"
              size="mini"
              v-model="pathUrl">
            </el-input>
          </div>
          <el-button type="light-blue" size="medium" @click="encryptionPath()">加密</el-button>
        </div>
        <div>加密后URL:</div>
        <div class="info-list">
          <div style="width: 580px;margin-right: 8px;">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4}"
              disabled
              v-model="encryptionUrl">
            </el-input>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
const CryptoJS = require('crypto-js');
export default {
  name: 'SettingTools',
  components: {
  },
  data () {
    return {
      toolsList: [
        {
          id: 'tools_1',
          name: '路径加密',
          list: []
        }
      ],
      activeFolder: {
        id: 'tools_1',
        name: '',
        list: []
      },
      pathUrl: '',
      // 加密后路径
      encryptionUrl: ''
    }
  },
  methods: {
    slelectItem (item) {
      this.activeFolder = item;
    },
    encryptionPath () {
      this.encryptionUrl = '';
      if (!this.pathUrl) return;
      this.encryptionUrl = this.encryptTilemapPath(this.pathUrl) + '_{z}_{x}_{y}.png';
      // console.log('加密路径', this.pathUrl, this.encryptionUrl);
    },
    // 静态地址加密
    encryptTilemapPath (path = '') {
      // fuxistatic 转 F212492324A249CG
      const key = CryptoJS.enc.Utf8.parse('F212492324A249CG');
      const iv = CryptoJS.enc.Utf8.parse('ABCDEF1234123412');
      const srcs = CryptoJS.enc.Utf8.parse(path);
      const encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });
      const httpBasename = encrypted.ciphertext.toString();
      const realPath = 'http://服务器ip或网址/tfs/tilemap/' + httpBasename;
      return realPath;
    }
  }
}
</script>
<style lang="scss" scoped>
.setting-tools {
  display: flex;
  // flex-direction: column;
  height: 100%;
  background: #171b22;
  overflow: hidden;
  overflow-y: auto;
  .manage-left {
    width: 240px;
    font-size: 14px;
    cursor: pointer;
    height: 100%;
    margin: 8px 0;
    .link-title {
      display: flex;
      align-items: center;
      line-height: 42px;
      padding-left: 22px;
      color: #898A8C;
      transition: color 0.2s;
      text-decoration: none;
      position: relative;
      .hz-icon {
        height: 20px;
        width: 20px;
        margin-right: 4px;
      }
      .block-16 {
        width: 16px;
        height: 16px;
      }
      &:hover {
        background: linear-gradient(0deg, #1F2430, #1F2430);
      }
      &.active {
        color: #fff;
        background: linear-gradient(0deg, #1F2430, #1F2430);
      }
      &.select {
        color: #fff;
        font-weight: bold;
        background: #3D85FF;
        box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25);
      }
    }
    .folder-concent {
      justify-content: space-between;
      .folder-name {
        cursor: pointer;
        margin-left: 4px;
        display: flex;
        align-items: center;
        font-size: 14px;
        width: 148px;
        .folder-name-max-width {
          max-width: 128px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .file-total {
        display: block;
        font-weight: bold;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        margin-right: 16px;
      }
      .del-folder-icon {
        display: none;
        margin-right: 12px;
      }
      &:hover {
        .del-folder-icon {
          display: block;
        }
        .file-total {
          display: none;
        }
      }
      .file-total-block {
        display: block !important;
      }
    }
    .padding-left-24 {
      padding-left: 24px;
    }
    .padding-left-54 {
      padding-left: 54px;
    }
    .margin-right-4 {
      margin-right: 4px;
    }
  }
  .manage-right {
    flex: 1;
    height: 100%;
    margin: 8px 0;
    font-size: 14px;
    color: #ffffffb8;
    .right-info {
      position: relative;
      margin: 0 32px;
      height: 100%;
      .info-list {
        position: relative;
        display: flex;
        height: 32px;
        line-height: 32px;
        margin-bottom: 16px;
      }
    }
  }
}
</style>
