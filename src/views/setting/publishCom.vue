<template>
  <div class="publish-com-wrapper" v-loading="loading"
    element-loading-text="正在发布组件，请等待"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)">
    <div class="btns">
      <el-button size="small" type="primary" @click="publishCom">检测组件变动并发布</el-button>
    </div>
  </div>
</template>
<script>
import http from '@/utils/http'
export default {
  name: 'publishCom',
  data () {
    return {
      loading: false
    }
  },
  methods: {
    publishCom () {
      this.loading = true
      http.post('/api/publishcom').then(res => {
        if (res.data && res.data.finish) {
          this.loading = false
        }
      }).catch(err => {
        console.error(err)
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.publish-com-wrapper {
  height: 100%;
  width: 100%;
  overflow: auto;
  padding-top: 100px;
  display: flex;
  background: #181A20;
  color: #fff;
  .btns {
    width: 200px;
    margin: 0 auto;
  }
}
</style>
