<template>
  <div class="setting-tutorial">
    <el-container>
      <el-aside class="workspace-tutorial-tree" width="290px">
        <TutorialList/>
      </el-aside>
      <el-main>
        <TutorialContent class="workspace-tutorial-content"/>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import TutorialList from '@/views/tutorial/TutorialList'
import TutorialContent from '@/views/tutorial/TutorialContent'

export default {
  name: 'SettingDoc',
  components: {
    TutorialList,
    TutorialContent
  },
  data () {
    return {}
  },
  mounted () {
    history.pushState(null, null, document.URL)
    window.addEventListener('popstate', this.goBack, false)
  },
  beforeDestroy () {
    window.removeEventListener('popstate', this.goBack, false)
  },
  methods: {
    goBack () {
      this.$confirm('确认离开页面吗？', '确认信息', {
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.$router.go(-1)
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.setting-tutorial {
  height: 100%;
  width: 100%;
  overflow: auto;
  padding-top: 10px;
  display: flex;
  background: #181A20;
  &-tree {
    height: 100%;
    overflow: auto;
  }
  &-content {
    margin-left: 32px;
    flex: 1;
    height: 100%;
  }
}
</style>
