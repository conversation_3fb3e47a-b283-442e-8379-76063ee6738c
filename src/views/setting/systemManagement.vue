<template>
    <div class="system-management">
        <div class="operate-tab">
            <div
                class="operate-tab-item"
                v-for="item in operateTab" :key="item.name"
                :class="{'operate-tab-active': activeOperate===item.name}"
                @click="activeOperate=item.name"
            >
                {{ item.name }}
            </div>
        </div>
        <div class="operate-container">
            <div class="title">操作日志</div>
            <div class="manage-show">
                <div class="form-input">
                    <el-form :inline="true" :model="formInput" class="demo-form-inline">
                    <el-form-item class="input-dark" label="操作内容">
                        <el-input  v-model="formInput.keyword" placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item class="input-dark" label="组织机构">
                        <el-cascader
                        v-model="formInput.group_id"
                        :options="groupData"
                        :props="{ checkStrictly: true, value: 'group_id', label: 'group_name', children: 'sub_group'}"
                        clearable></el-cascader>
                    </el-form-item>
                    <el-form-item class="input-dark" label="操作人">
                        <el-select v-model="formInput.user_id" placeholder="请选择">
                            <el-option
                            v-for="item in userData"
                            :key="item.user_id"
                            :label="item.name"
                            :value="item.user_id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item  class="input-dark" label="操作类型">
                        <el-select v-model="formInput.optype" placeholder="请选择">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="登录" value="登录"></el-option>
                            <el-option label="新建" value="新建"></el-option>
                            <el-option label="查询" value="查询"></el-option>
                            <el-option label="修改" value="修改"></el-option>
                            <el-option label="删除" value="删除"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item class="input-dark"  label="状态">
                        <el-select v-model="formInput.status" placeholder="请选择">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="成功" value="2"></el-option>
                            <el-option label="失败" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item class="input-dark"  label="时间">
                        <el-select v-model="formInput.date_type" placeholder="请选择">
                            <el-option label="今天" value="1"></el-option>
                            <el-option label="昨天" value="2"></el-option>
                            <el-option label="七天前" value="3"></el-option>
                            <el-option label="30天前" value="4"></el-option>
                            <el-option label="半年前" value="5"></el-option>
                            <el-option label="最近一年" value="6"></el-option>
                            <el-option label="自定义" value=""></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="自定义" v-if="formInput.date_type===''">
                        <el-date-picker
                        class="time-dark"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        v-model="formInput.time"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <div class="right-btn">
                        <el-form-item class="search-btn">
                        <el-button size="mini" @click="onSubmit">查询</el-button>
                    </el-form-item>
                    <el-form-item class="clear-btn">
                        <el-button size="mini" @click="onClear">清空</el-button>
                    </el-form-item>
                    </div>
                    </el-form>
                </div>
                <div class="table-container">
                    <el-table
                    :data="logData"
                    class="log-table"
                    >
                        <el-table-column
                            prop="c_time"
                            label="时间"
                            width="218"
                            >
                            <template slot-scope="scope">
                                {{ filterTime(scope.row.c_time) }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="user_name"
                            label="操作人"
                            width="122"
                            >
                        </el-table-column>
                        <el-table-column
                            prop="organization"
                            width="200"
                            label="组织机构">
                            <template slot-scope="scope">
                                {{ scope.row.organization + ": " + scope.row.organizationID }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="operateType"
                            label="操作类型"
                            width="163"
                            >
                        </el-table-column>
                        <el-table-column
                            prop="details"
                            label="操作内容"
                            class-name="details-col-w"
                            >
                        </el-table-column>
                        <el-table-column
                            prop="status"
                            label="状态"
                            width="103"
                            >
                            <template slot-scope="scope">
                                <div :class="`status-icon ${scope.row.status===2 ? 'success-status-icon' : 'fail-status-icon'}`"></div>
                                <span>{{ scope.row.status===2 ? "成功" : "失败" }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div>
                    <el-pagination
                        class="pagination"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="paging.page"
                        :page-sizes="[10, 20, 30, 40]"
                        :page-size="paging.size"
                        layout="total, prev, pager, next, jumper, sizes"
                        :total="total">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getUserbehaviorLog, getUserGroupCategory, getUserList } from '@/api/user'

export default {
  name: 'SystemManagement',
  data () {
    return {
      operateTab: [
        { name: '操作日志', icon: 'h' }
      ],
      activeOperate: '操作日志',
      formInput: {
        keyword: '',
        user_id: '',
        group_id: '',
        optype: '',
        status: '',
        time: '',
        date_type: '3'
      },
      paging: {
        page: 1,
        size: 10
      },
      logData: [],
      total: 0,
      groupData: [],
      userData: []
    }
  },
  watch: {
    'formInput.group_id' (val) {
      getUserList({
        group_id: val[val.length - 1]
      }).then(res => {
        this.userData = res.data
      })
    },
    formInput: {
      handler (val) {
        if (val.date_type !== '' || (val.date_type === '' && val.time && val.time.length)) {
          this.GetUserbehaviorLog()
        }
      },
      deep: true
    }
  },
  created () {
    this.GetUserbehaviorLog()
    this.GetUserGroupCategory()
  },
  methods: {
    handleSizeChange (val) {
      this.paging.size = val
      this.GetUserbehaviorLog()
    },
    handleCurrentChange (val) {
      this.paging.page = val
      this.GetUserbehaviorLog()
    },
    // 获取log数据
    /* eslint-disable */
    GetUserbehaviorLog () {
      const formInput = this.formInput
      const paging = this.paging
      let s_time = ''
      let e_time = ''
      let date_type = ''
      if (formInput.time.length) {
        s_time = formInput.time[0]
        e_time = formInput.time[1]
      }
      if (formInput.date_type !== '') {
        date_type = formInput.date_type - 0
      }
      getUserbehaviorLog({
        page_no: paging.page,
        page_size: paging.size,
        keyword: formInput.keyword, // 查询字段
        user_id: formInput.user_id, // 用户id
        group_id: formInput.group_id[formInput.group_id.length - 1], // 组织id
        optype: formInput.optype, // 类型
        status: formInput.status, // 返回是否成功
        s_time: s_time, // 开始时间
        e_time: e_time, // 结束时间
        date_type: date_type // 今天传1 昨天传2 7天前传3 30天前传4 半年前传5 最近一年传6 自定义传空然后传s_time和e_time
      }).then(res => {
        if (res.success) {
          this.total = res.data.total
          this.logData = res.data.data
        }
      })
    },
    /* eslint-disable */
    // 获取组织机构
    GetUserGroupCategory () {
      getUserGroupCategory().then(res => {
        const data = res.data.result
        let arr = []
        for (let i = 0; i < data.length; i++) {
          arr.push(data[i].sub_group[0])
        }
        arr = removeEmptyChildren(arr)
        this.groupData = arr
      })
    },
    onSubmit () {
      this.GetUserbehaviorLog()
    },
    onClear () {
      this.formInput = {
        keyword: '',
        user_id: '',
        group_id: '',
        optype: '',
        status: '',
        time: '',
        date_type: '3'
      }
    },
    filterTime (val) {
      const time = new Date(val)
      return time.toLocaleString()
    }
  }
}
function removeEmptyChildren (array) {
  const newArray = [];
  for (const obj of array) {
    if (obj.sub_group && Array.isArray(obj.sub_group)) {
      if (obj.sub_group.length === 0) {
        delete obj.sub_group;
      } else {
        obj.sub_group = removeEmptyChildren(obj.sub_group);
      }
    }
    newArray.push(obj);
  }
  return newArray;
}
</script>

<style lang="scss" scoped>
.system-management {
    height: 100%;
    // display: flex;
}
.operate-tab {
    display: flex;
    width: 194px;
    padding: 8px 8px 16px 8px;
    flex-direction: column;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.70);
    font-weight: 600;
    float: left;
    .operate-tab-item {
        padding: 0 16px;
        height: 48px;
        width: 100%;
        line-height: 48px;
        cursor: pointer;
    }
    .operate-tab-active {
        color: rgba(61, 133, 255, 1);
        border-radius: 8px;
        background: rgba(61, 133, 255, 0.15);
        position: relative;
        &::after {
            content: "";
            position: absolute;
            width: 2px;
            height: 8px;
            border-radius: 0px 3px 3px 0px;
            background: #3D85FF;
            left: 0;
            top: 0;
            bottom: 0;
            margin: auto;
        }
    }
}
.operate-container {
    flex: 1;
    color: rgba(255, 255, 255, 0.70);
    background-color: #191D25;
    display: flex;
    flex-direction: column;
    margin-left: 194px;
    height: 100%;
    overflow: auto;
    .title {
        font-size: 16px;
        height: 56px;
        padding: 0 24px;
        line-height: 56px;
        background-color: #13151A;
    }
    .manage-show {
        flex: 1;
        background-color: #13151A;
        margin: 16px;
        padding: 24px;
        // overflow: auto;
        .form-input .input-dark {
            ::v-deep .el-form-item {
                margin-right: 24px;
                margin-bottom: 12px;
            }
            ::v-deep .el-form-item__label {
                font-family: PingFang SC;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.50);
                width: 70px;
            }
            ::v-deep .el-input__inner {
                width: 208px;
                height: 31px;
            }
        }
        ::v-deep .el-table tr {
            background-color: #13151A !important;
        }
        ::v-deep .el-table th.el-table__cell {
            background-color: #191D25 !important;
        }
        .form-input .time-dark {
            ::v-deep .el-range-input {
                background-color: rgba(204, 219, 255, 0.16);
                color: rgba(255, 255, 255, 0.70);
            }
        }
    }
}
.search-btn {
    ::v-deep .el-button {
        color: #FFF;
        border: 1px solid rgba(204, 219, 255, 0.32);
        background-color: #13151A;
    }
}
.clear-btn {
    ::v-deep .el-button {
        color: #3D85FF;
        background-color: #13151A;
        border-color: transparent;
    }
}
.right-btn {
    display: inline-block;
    float: right;
}
.log-table {
    min-width: 1000px;
}
.table-container {
    width: 100%;
    overflow: auto;
}
.pagination {
    margin-top: 24px;
    float: right;
    ::v-deep .el-pager li {
        background-color: transparent;
        color: rgba(255, 255, 255, 0.70);
        border: 1px solid rgba(255, 255, 255, 0.70);
        width: 32px;
        height: 32px;
        border-radius: 4px;
        font-size: 14px;
        padding: 0;
        line-height: 32px;
        margin: 0 8px;
        &:hover {
            border-color: #3D85FF;
        }
    }
    ::v-deep .el-icon-more {
        border: 1px solid transparent !important;
    }
    ::v-deep .el-pager li.active {
        background-color: rgba(85, 161, 157, 0.10);
        border: 1px solid transparent;
    }
    ::v-deep .btn-prev, ::v-deep .btn-next {
        background-color: transparent;
        color: rgba(255, 255, 255, 0.70);
        border: 1px solid rgba(255, 255, 255, 0.70);
        width: 32px;
        height: 32px;
        border-radius: 4px;
        font-size: 14px;
        padding: 0;
        line-height: 32px;
        margin: 0 8px;
        &:hover {
            border-color: #3D85FF;
        }
    }
    ::v-deep .el-pagination__editor.el-input .el-input__inner {
        height: 32px;
    }
    ::v-deep .el-pagination__jump, ::v-deep .el-pagination__total {
        color: rgba(255, 255, 255, 1);
    }
    ::v-deep .el-input .el-input__inner {
        border-color: transparent !important;
        background-color: transparent;
        height: 32px;
    }
    ::v-deep .el-pagination__editor.el-input .el-input__inner {
        border: 1px solid rgba(204, 219, 255, 0.16) !important;
        background-color: rgba(204, 219, 255, 0.16);
    }
}
.status-icon {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #239545;
    display: inline-block;
    margin-right: 8px;
}
.success-status-icon {
    background-color: #239545;
}
.fail-status-icon {
    background-color: #FF475D;
}
::v-deep .el-picker-panel {
                background: rgba(204, 219, 255, 0.16) !important;
                color: #FFF;
            }
</style>
