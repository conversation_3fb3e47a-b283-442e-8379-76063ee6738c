<template>
  <div class="filter-manage">
    <div class="theme-head">
      <hz-icon class="icon" name="filter"></hz-icon>
      内置过滤器
      <el-button type="text" size="mini" @click="add">新增</el-button>
    </div>
    <div class="theme-content">
      <el-table class="mapping-t" :data="tableData" height="100%" size="mini">
        <el-table-column label="名称" width="200" prop="name"></el-table-column>
        <el-table-column label="描述" prop="description"></el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" @click="editItem(scope.row)">修改</el-button>
            <el-button type="text" @click="deleteItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <SeatomLoading v-if="loading" />
    <SystemFilterAdd ref="add" @refresh="getSystemFilter" />
  </div>
</template>

<script>
import SystemFilterAdd from '@/components/editor/data-source/SystemFilterAdd';
import { systemfilter, deleteSystemfilter } from '@/api/filter';
import { handleDecrypt } from '@/utils/base';
export default {
  name: 'SystemFilter', // 系统过滤器管理
  components: {
    SystemFilterAdd
  },
  data () {
    return {
      show: false,
      tableData: [],
      loading: true
    }
  },
  created () {
    this.getSystemFilter();
  },
  methods: {
    add () {
      this.$refs.add.showDialog();
    },
    editItem (item) {
      this.$refs.add.showEdit(item);
    },
    deleteItem (item) {
      this.$confirm('确认删除该过滤器？', '提示', { type: 'warning' }).then(async () => {
        const data = {
          id: item.id
        }
        const res = await deleteSystemfilter(data);
        if (res && res.success) {
          this.$message.success('删除成功');
          this.getSystemFilter();
        }
      }).catch(() => {})
    },
    async getSystemFilter () {
      this.loading = true;
      const res = await systemfilter();
      if (res && res.success) {
        this.tableData = res.data.filter(item => {
          if (item.type !== '4') {
            item.content = handleDecrypt(item.content)
            return true
          }
          return false
        });
      }
      this.loading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-manage {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #171b22;
    .theme-head {
      font-size: 14px;
      color: #fafafa;
      height: 45px;
      line-height: 45px;
      padding: 0 20px;
      .icon {
        margin-right: 5px;
      }
    }
    .theme-content {
      display: flex;
      flex: 1;
      padding: 0 10px 10px;
      overflow: hidden;
    }
}
</style>
