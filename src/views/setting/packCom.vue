<template>
  <div v-loading="loading" element-loading-text="正在打包组件，请稍等" class="packstyle">
    <el-form ref="form" :model="packdata" :rules="rules" label-width="70px" class="formbox">
      <el-form-item label="" label-width="0"  prop="serverIp">
        <el-input clearable size="small" v-model="packdata.serverIp" placeholder="请输入伏羲大屏访问ip"></el-input>
        <p>如果是https的访问地址，则输入https://ip</p>
        <p>如果是http+8888端口的访问地址，则输入http://ip:7001</p>
        <p>如果是特殊端口的访问地址，则询问一下开发人员</p>
      </el-form-item>
      <el-form-item label="" label-width="0" prop="enName">
        <el-select @change="changeCom" v-model="comobj" filterable placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.label"
            :label="item.value"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <p>组件列表</p>
      <div>
        <el-tag class="tag" type="success" v-for="item in packdata.enName" :key="item.label" @close="handleClose(item)" closable>{{item.value}}</el-tag>
      </div>
      <el-button @click="download" type="primary">生成组件包</el-button>
      <div><a :href="comherf">{{comherf}}</a></div>
    </el-form>
  </div>
</template>

<script>
import { packcom, packlist } from '@/api/common'
export default {
  name: 'packCom',
  data () {
    return {
      comherf: '',
      comobj: {},
      packdata: {
        serverIp: '',
        enName: []
      },
      rules: {
        serverIp: [
          { required: true, message: '请输入伏羲大屏访问地址', trigger: 'blur' }
        ],
        enName: [
          { required: true, message: '请选择组件名称', trigger: 'change' }
        ]
      },
      options: [],
      loading: false
    }
  },
  mounted () {
    this.packlist()
  },
  methods: {
    handleClose (item) {
      this.packdata.enName.splice(this.packdata.enName.indexOf(item), 1);
    },
    changeCom (item) {
      if (this.packdata.enName.indexOf(item) === -1) {
        this.packdata.enName.push(item)
      }
    },
    async packlist () {
      const res = await packlist()
      if (res && res.success) {
        // if (res.data.ispack) {
        //   this.loading = true
        // }
        const comoptions = JSON.parse(res.data.comjson)
        for (const key in comoptions) {
          const comvalue = comoptions[key].split('&&')[1]
          this.options.push({
            value: comvalue,
            label: key
          })
        }
      }
    },
    download () {
      if (this.packdata.enName.length === 0) {
        this.$message.error('请选择组件');
      }
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.loading = true
          const data = {
            serverIp: this.packdata.serverIp,
            enName: this.packdata.enName
          }
          const res = await packcom(data)
          if (res && res.success) {
            this.$message.success('打包成功');
            this.comherf = res.data.packurl
          } else {
            this.$message.error('打包失败');
          }
          this.loading = false;
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
 .packstyle{
    height: 100%;
    width: 100%;
    overflow: auto;
    padding-top: 10px;
    display: flex;
    background: #181A20;
    color: #fff;
 }
 .formbox {
   width: 500px;
   margin: 0 auto;
 }
 .tag{
   margin-right: 20px;
   margin-bottom: 20px;
 }
</style>
