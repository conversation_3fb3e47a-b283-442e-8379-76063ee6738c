<template>
    <div>
      <div class="app-loading" v-if="pageLoading">
        <div class="loading-container">
          <img class="loading-logo" src="/video/loading.webp" alt="">
        </div>
      </div>
      <div class="app" v-else>
        <div class="app-login">
          <div class="login-title"></div>
          <el-tabs v-model="activeName">
            <el-tab-pane label="本地登录" name="localLogin">
              <el-form ref="localLogin" :model="loginForm" :rules="LoginRules" label-width="70px">
                <el-form-item label="" label-width="0" prop="domain">
                  <el-input clearable size="small" v-model="loginForm.domain" placeholder="企业域*" @keyup.enter.native="submitForm(activeName)"></el-input>
                </el-form-item>
                <el-form-item label="" label-width="0" prop="username">
                  <el-input clearable size="small" v-model="loginForm.username" placeholder="用户名*" @keyup.enter.native="submitForm(activeName)"></el-input>
                </el-form-item>
                <el-form-item label="" label-width="0" prop="password">
                  <el-input show-password clearable size="small" type="password" v-model="loginForm.password" placeholder="密码*" @keyup.enter.native="submitForm(activeName)"></el-input>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
          <el-checkbox class="checked" v-model="checked">记住密码</el-checkbox>
          <el-button size="small" type="primary" @click="submitForm(activeName)" :loading="loginloading" v-if="!loginloading">登录</el-button>
          <el-button size="small" type="primary" :loading="!loginloading" v-if="loginloading">登录中</el-button>
        </div>
      </div>
    </div>
  </template>
<script>
import { dmcLogin, localLogin, getUserId, screenConfig } from '@/api/user';
const Base64 = require('js-base64').Base64;
export default {
  data () {
    return {
      loginloading: false,
      pageLoading: false,
      checked: false,
      activeName: 'localLogin',
      loginForm: {
        domain: '',
        username: '',
        password: ''
      },
      LoginRules: {
        domain: [
          { required: true, message: '请输入企业域', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getloginInfo()
  },
  methods: {
    async loginUcenter () {
      const res = await screenConfig()
      if (res && res.data) {
        if (res.data.ucenter) {
          window.open('/api/user/dmcUcenter', '_self');
        }
      }
    },
    async getloginInfo () {
      window.localStorage.removeItem('userId');
      window.localStorage.removeItem('token');
      window.localStorage.removeItem('name');
      const rememberPsw = window.localStorage.getItem('password');
      const rememberUser = window.localStorage.getItem('username');
      const rememberDomain = window.localStorage.getItem('domain');
      if (rememberPsw && rememberUser && rememberDomain) {
        this.checked = true
        this.loginForm.password = Base64.decode(rememberPsw)
        this.loginForm.username = rememberUser
        this.loginForm.domain = rememberDomain
      }
      const timeStap = this.$route.query.timestap
      const ecToken = this.$route.query.ec_token
      if (timeStap && ecToken) {
        this.pageLoading = true
        try {
          const res = await getUserId({
            timestap: timeStap,
            ec_token: ecToken
          })
          this.goWorkspace(res)
        } catch (error) {
          this.pageLoading = false
        }
      } else {
        // this.loginUcenter()
      }
    },
    submitForm (refName) {
      this.loginloading = true
      this.$refs[refName].validate((valid) => {
      // 如果校验条件都无误的情况下valid为true
        if (valid) {
          this.login()
        } else {
          this.loginloading = false
        }
      })
    },
    rememberPsw (checked) {
      if (checked) {
        window.localStorage.setItem('password', Base64.encode(this.loginForm.password));
        window.localStorage.setItem('username', this.loginForm.username)
        window.localStorage.setItem('domain', this.loginForm.domain)
      } else {
        window.localStorage.removeItem('password');
        window.localStorage.removeItem('domain')
      }
    },
    async login () {
      const body = {
        password: Base64.encode(Base64.encode(this.loginForm.password)),
        // 对于1.2.7不采取混淆
        // password: this.loginForm.password,
        username: this.loginForm.username,
        domain: this.loginForm.domain
      }
      try {
        let res
        if (this.activeName === 'dmcLogin') {
          res = await dmcLogin(body)
        } else {
          res = await localLogin(body)
          window.localStorage.setItem('localLogin', true)
        }
        this.loginloading = false
        this.goWorkspace(res);
      } catch (e) {
        this.loginloading = false
      }
    },
    async goWorkspace (res) {
      if (res && res.success) {
        const { userInfo: { data: { result: { user_info: { name } } } } } = res.data;
        const workspaceId = res.data.workspaceData.id
        const userId = res.data.workspaceData.userId
        const token = res.data.userInfo.data.result.access_token
        window.localStorage.setItem('userId', userId);
        window.localStorage.setItem('token', token);
        window.localStorage.setItem('name', name);
        window.localStorage.setItem('workspaceId', workspaceId)
        window.localStorage.setItem('username', this.loginForm.username) // 保存下用户的登录账号
        this.$store.commit('user/setUserId', userId);
        this.$store.commit('user/setName', name);
        this.rememberPsw(this.checked);
        this.$router.push({
          path: `/workspace/${workspaceId}`
        })
      } else {
        const errstr = res.message
        this.$message.error(errstr)
      }
    }
  }
}
</script>
  <style lang="scss" scoped>
  .app-loading {
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    // background: url('../assets/img/login-loading.png') no-repeat center / cover;
    background: black;
    .loading-container {
        color:  #18FEFE;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .loading-logo {
        width: 400px;
      }
  }
  .app {
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: url('../assets/img/login_background.png') no-repeat center / cover;
    &-login {
      width: 400px;
      height: 500px;
      position: absolute;
      left: 80%;
      top: 50%;
      transform: translate(-50%, -50%);
      .login-title {
        width: 100%;
        height: 96px;
        margin-bottom: 24px;
        background: url('../assets/img/login_title.png') no-repeat top / cover;
        background-position: 0px;
      }
      input::placeholder{
        color: rgba(255, 255, 255, 0.48);
      }
    }
    .el-button {
      width: 100%;
      height: 48px;
      background: radial-gradient(100% 469.69% at 0% 0%, #498CF7 0%, #15B3BD 100%);
      border-radius: 8px;
    }
    .checked{
      margin-bottom: 38px;
    }
    ::v-deep{
      .el-form-item {
        margin-bottom: 24px;
      }
      .el-input__inner {
        height: 48px;
        line-height: 48px;
        background: radial-gradient(100% 321.18% at 0% 0%, rgba(41, 212, 255, 0.16) 0%, rgba(41, 212, 255, 0.08) 100%);
        border-radius: 4px 4px 0px 0px;
        // border-bottom: 2px solid rgba(41, 212, 255, 0.08);
      }
      .el-tabs__header {
        margin-bottom: 24px;
      }
      .el-tabs__nav-wrap::after{
        display: none;
      }
      .el-tabs__active-bar{
        height: 5px;
        background-color: radial-gradient(1113.64% 21.21% at 50% 50.76%, #29D4FF 0%, rgba(41, 212, 255, 0) 100%);
        background: url('../assets/img/login_active.png') no-repeat center / contain;
      }
      .el-tabs__item{
        width: 112px;
        height: 48px;
        line-height: 48px;
        text-align: center;
        background: none;
      }
      .el-tabs__item.is-active{
        color: #FFFFFF;
      }
      .el-tabs__item:hover{
        color: #FFFFFF;
      }
      .el-checkbox__inner{
        border: 2px rgba(21, 179, 189, 1) solid;
        border-radius: 4px;
        background: none;
      }
      .el-checkbox__label{
        color: rgba(255, 255, 255, 0.48);;
      }
      .el-icon-loading {
        color: #FFFFFF;
      }
    }
  }
</style>
