<template>
  <div class="create-screen">
    <div class="top-bar">
      <div class="right-bar"></div>
      <div class="left-bar"></div>
      <div class="return-btn">
        <div @click="$router.go(-1)" class="return-text">
          <i class="el-icon-arrow-left"></i>
          取消创建
        </div>
      </div>
    </div>
    <div class="creator-wp">
      <div class="template-list">
        <template v-for="item in ScreenTpl">
          <div :key="item.id" v-if="!item.level" class="template-item --blank">
            <div class="template-image">
              <el-button type="primary" @click="openDialog(item)" class="create-btn">+ 创建大屏</el-button>
            </div>
            <div class="template-info">
              空白画板
            </div>
          </div>
          <div v-else :key="item.id" class="template-item">
            <div class="template-image">
              <img v-lazy="getIhumbmailUrl(item)" alt="暂无图片"  class="preview-image">
              <div class="template-mask">
                <el-button type="primary" @click="openDialog(item)" class="create-btn">创建项目</el-button>
                <el-button class="preview-btn" @click="preview(item)">查看模版</el-button>
              </div>
            </div>
            <div class="template-info">
              <div class="template-name">
                 {{item.name}}
              </div>
              <div class="template-size">
                <!-- <p>比例 16:9</p> -->
                <p>{{ item.config.width }}x{{item.config.height}}px</p>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <el-dialog
      title="创建数据大屏"
      :visible.sync="dialogVisible"
      width="500px"
      top="0"
      :close-on-click-modal="false">
      <el-form label-width="90px" label-position="right" size="mini">
        <el-form-item label="名称：" status-icon>
          <el-input v-model="selectParams.name" placeholder="请输入大屏名称"></el-input>
        </el-form-item>
        <el-form-item label="分组：" status-icon>
          <el-select v-model="selectParams.projectId"
          filterable
          placeholder="请选择大屏分组">
            <el-option
              v-for="item in selectProject"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="布局容器：" v-if="selectParams.type === 'mobile'">
          <div class="mobile-type">
            <div
              class="mobile-item"
              :class="{active: selectParams.screenType === item.screenType}"
              v-for="item in layoutOpt" :key="item.screenType"
              @click="selectLayout(item)">
              <div class="mobile-bg">
                <img :src="item.logo">
              </div>
              <div class="mobile-name">{{ item.title }}</div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="类型：" v-else>
          <el-select v-model="selectParams.screenType" :disabled="stDisabled">
            <el-option label="普通大屏" value="common"></el-option>
            <el-option label="场景大屏" value="scene"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="text" @click="dialogVisible = false">取 消</el-button>
        <el-button size="mini" type="plain" @click="createScreen" :loading="loading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getScreenTpl, getProject } from '@/api/workspace';
import { createScreen, screenCopy } from '@/api/screen';
import { openNewTab } from '@/utils/dom';
import { replaceUrl } from '@/utils/base';
export default {
  data () {
    return {
      replaceUrl,
      dialogVisible: false,
      selectProject: [],
      ScreenTpl: [],
      screenId: '',
      selectParams: {
        projectId: '',
        workspaceId: this.$route.query.workspaceId,
        name: '',
        level: 0,
        type: 'pc',
        templateId: 1,
        screenType: 'common'
      },
      layoutOpt: [
        { title: '自定义布局', logo: require('@/assets/img/freeLayout.png'), screenType: 'common' },
        { title: '多屏布局', logo: require('@/assets/img/multiScreen.png'), screenType: 'scene' }
      ],
      stDisabled: false, // 是否禁用大屏类型下拉框
      loading: false
    }
  },
  computed: {
    getIhumbmailUrl () {
      return function (item) {
        return replaceUrl(item.config.encryptThumbnail
          ? (process.env.VUE_APP_IMG_SERVER_URL ||
              process.env.VUE_APP_SERVER_URL) +
              item.config.encryptThumbnail +
              '?width=290&height=162'
          : item.thumbnail);
      };
    }
  },
  created () {
    const type = this.$route.query.type;
    if (type) {
      this.selectParams.type = type;
    }

    this.getScreenTpl();
    this.getManage();
  },
  methods: {
    async  getScreenTpl () {
      const data = (await getScreenTpl({ type: this.selectParams.type })).data;
      this.ScreenTpl = data;
    },
    async getManage () {
      const data = (await getProject({ workspaceId: this.$route.query.workspaceId })).data;
      this.selectProject = data;
    },
    openDialog (item) {
      this.dialogVisible = true;
      this.selectParams.projectId = '';
      this.selectParams.name = '';
      this.selectParams.level = item.level
      this.screenId = item.screenId
      if (item.screenType) {
        this.selectParams.screenType = item.screenType;
        this.stDisabled = true;
      } else {
        this.stDisabled = false;
      }
      if (!item.level) {
        this.selectParams.type = this.$route.query.type;
        return
      }
      this.selectParams.type = item.type
    },
    selectLayout (item) {
      this.selectParams.screenType = item.screenType;
    },
    async createScreen () {
      if (!this.check()) return false;
      if (this.selectParams.level) {
        const data = {
          id: this.screenId,
          ...this.selectParams,
          isCreateScreen: true
        }
        if (this.selectParams.type === 'mobile') {
          data.config = {
            width: 375,
            height: 667,
            scaleType: 'full_width'
          }
        }
        this.loading = true;
        const res = await screenCopy(data);
        if (res.success) {
          this.$router.push({ path: `/screen/edit/${res.data.id}` });
        } else {
          this.$message.error(res.message);
        }
        this.loading = false;
      } else {
        const data = {
          ...this.selectParams
        }
        if (this.selectParams.type === 'mobile') {
          data.config = {
            width: 375,
            height: 667,
            scaleType: 'full_width'
          }
        }
        this.loading = true;
        const res = await createScreen(data);
        if (res.success) {
          this.$router.push({ path: `/screen/edit/${res.data.id}` });
        } else {
          this.$message.error(res.message);
        }
        this.loading = false;
      }
    },
    preview (item) {
      let url;
      const token = localStorage.getItem('token');
      const userId = localStorage.getItem('userId');
      if (item.type === 'pc') {
        url = `/screen/preview/${item.screenId}?token=${token}&userId=${userId}`;
      } else {
        url = `/screen/mobile-preview/${item.screenId}?token=${token}&userId=${userId}`;
      }
      openNewTab(url);
    },
    check () {
      if (!this.selectParams.name.trim()) {
        this.$message.error('数据大屏名称不能为空');
        return false;
      }
      const reg = /^[\u4E00-\u9FA5A-Za-z0-9\-_]+$/;
      if (!reg.test(this.selectParams.name)) {
        this.$message.error('大屏名称不能输入特殊字符！');
        return
      }
      if (!this.selectParams.projectId) {
        this.$message.error('数据大屏分组不能为空');
        return false;
      }
      return true;
    }

  }

}
</script>

<style lang="scss" scoped>
.create-screen {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #191c23;

  .top-bar {
    height: 50px;
    width: 100%;
    position: relative;

    .right-bar {
      position: absolute;
      left: 150px;
      top: 20px;
      height: 5px;
      width: 100%;
      border-top: 1px solid #2681ff;
      border-left: 2px solid #2681ff;
      background: rgba(55,126,255,.04);
      border-top-left-radius: 5px;
      transform: skewX(-45deg);
      box-shadow: 0 5px 28px 0 rgba(55,126,255,.28);
    }

    .left-bar {
      position: absolute;
      left: 0;
      top: 24px;
      height: 25px;
      width: 138px;
      border-right: 2px solid #2681ff;
      border-bottom: 1px solid #2681ff;
      transform: skewX(-45deg);
      border-bottom-right-radius: 5px;
      box-shadow: 0 5px 28px 0 rgba(55,126,255,.28);
    }

    .return-btn {
      position: absolute;
      left: -31px;
      top: 0;
      width: 180px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      background: #151b22;
      cursor: pointer;
      transform: skewX(-45deg);
      border-bottom-right-radius: 5px;
      font-weight: bolder;

      .return-text {
        display: inline-block;
        color: #fff;
        font-size: 14px;
        margin-left: 10px;
        transform: skewX(45deg);
        transition: .2s;
      }
    }
  }

  .creator-wp {
    height: calc(100% - 50px);
    margin-left: 84px;

    .template-list {
      padding-bottom: 100px;
      padding-top: 60px;
      height: 100%;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      color: #fff;

      .template-item {
        width: 258px;
        height: 184px;
        box-shadow: 0 0 10px -6px #000;
        border: 1px solid #3a4659;
        margin: 16px;
        transition: .2s;
        cursor: default;
        outline: 1px solid transparent;

        .template-image {
          width: 100%;
          height: 146px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: .2s;
          position: relative;

          .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            position: relative;
          }

          .template-mask {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            position: absolute;
            background: rgba(0,0,0,.5);
            transition: .2s;
            pointer-events: none;
            opacity: 0;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
          }
        }
        .template-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10px;
          height: 36px;
          background: #1d262e;
          transition: .2s;

          .template-name {
            font-size: 12px;
          }
          .template-size {
            color: #999;
            text-align: right;
            font-family: Arial,Helvetica,sans-serif;
          }
        }
      }
      .template-item:hover {
        outline: 1px solid #2681ff;
      }
      .template-item:hover .template-mask {
        pointer-events: all;
        opacity: 1;
      }
      .--blank {
        position: relative;
        outline:1px solid #2681ff;
        .template-image {
          box-shadow: inset 0 0 46px 0 rgba(136,215,255,.29);
        }
        .template-info {
          justify-content: center;
          font-size: 14px;
          border-top: 1px solid #2681ff;
        }
      }
    }
  }
  .create-btn {
    width: 112px;
  }
  .preview-btn {
    margin-top: 10px;
    width: 112px;
    margin-left: 0;
    background-color: transparent;
    border: 1px solid #2681ff;
    color: #2681ff;
  }
  .preview-btn:hover {
    color: #fff;
    background: #409fff;
    border-color: #409fff;
  }
  ::v-deep .el-form-item__label {
    color: #fff;
  }
  .el-select {
    width: 100%;
  }
  .mobile-type {
    display: flex;
    align-items: center;
    .mobile-item {
      width: 76px;
      height: 108px;
      border: 1px solid #444A5B;
      margin-right: 20px;
      cursor: pointer;
      &.active {
        border-color: #1F71FF;
      }
      .mobile-bg {
        width: 46px;
        height: 62px;
        margin: 15px 15px 0;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .mobile-name {
        font-size: 12px;
        font-weight: normal;
        color: #fff;
        text-align: center;
      }
    }
  }
}
</style>
