<template>
  <div class="indicator-library-edit">
    <!-- 头部 -->
    <div class="seatom-header">
      <div class="header-return">
        <span @click="headerReturn" class="header-return-icon">
          <i class="el-icon-arrow-left"></i>
        </span>
        <div class="edit-name" v-if="!editable">
          <span class="name" :title="screenName">{{ screenName }}</span>
          <i
            class="icon el-icon-edit"
            v-if="loaded"
            @click="editable=true"
          ></i>
        </div>
        <el-input
          v-else
          v-focus
          size="mini"
          @blur="inputScreenBlur"
          v-model="screenName"
        ></el-input>
      </div>
      <div class="edit-top-btn">
        <div class="edit-btns-group">
          <div
            class="edit-btn"
            v-for="btn in editPanelState"
            :key="btn.type"
            @click.stop="handleEditBtnClick(btn)"
          >
            <button
              class="base-button"
              :title="btn.name"
              :class="{ select: btn.select }"
            >
              <hz-icon class="icon" :name="btn.icon"></hz-icon>
            </button>
            <span class="btn-name">{{ btn.name }}</span>
          </div>
        </div>
        <div class="drawer-actions">
          <div
            class="edit-btn"
            v-for="btn in drawerBtnState"
            :key="btn.type"
            @click.stop="handleDrawerBtnClick(btn)"
          >
            <button
              class="base-button"
              :title="btn.name"
              :class="{ select: btn.select }"
            >
              <hz-icon class="icon" :name="btn.icon"></hz-icon>
            </button>
            <span class="btn-name">{{ btn.name }}</span>
            <!-- 组件升级 || 基于骨骼布局-->
            <div
              class="seatom-popover-portal"
              v-if="
                btn.select && (btn.type === 'update' || btn.type === 'layout')
              "
            >
              <div class="update-panel-wp">
                <UpdatePanel v-if="btn.type === 'update'" />
              </div>
            </div>
          </div>
        </div>
        <div class="global-btns-group">
          <template v-for="btn in globalBtns">
            <div
              class="edit-btn"
              :key="btn.id"
              @click.stop="hanldeGlobalBtnClick(btn)"
            >
              <el-button :title="btn.name" type="primary" size="mini" class="btn-primary">
                <!-- <hz-icon class="icon" :name="btn.icon"></hz-icon> -->
                {{ btn.name }}
              </el-button>
            </div>
          </template>
        </div>
      </div>
    </div>
    <!-- 加载线 -->
    <div class="loading-indicator" :class="{ loading: !loaded }"></div>
    <!-- 底部内容 -->
    <div class="seatom-eidt-main">
      <!-- 图层列表 -->
      <div
        class="layer-panel-wp"
        :class="{ '--hide': !editPanelState.layer.select }"
      >
        <LayerManager v-if="loaded" />
      </div>
      <!-- 组件列表 -->
      <div
        class="component-panel-wp"
        :class="{ '--hide': !editPanelState.component.select }"
      >
        <CompPanel v-if="loaded" type="indicator" ref="compPanel" />
      </div>
      <div class="right-edit-main">
        <!-- 工具栏 -->
        <!-- <div class="toolbox-panel-wp">
          <ToolboxPanel v-if="loaded" />
        </div> -->
        <!-- 画布 -->
        <div class="editor-panel-wp" ref="panel">
          <CanvasMain v-if="loaded" data-code="1" />
        </div>
      </div>
      <!-- 抽屉按钮的遮罩 -->
      <!-- <div
        class="seatom-drawer__mask"
        @click="handleDrawerMaskClick"
        v-show="drawerMask"
      ></div> -->
      <!-- 右侧配置项 -->
      <div
        class="config-panel-wp"
        :class="{ '--hide': !editPanelState.config.select }"
      >
        <template v-if="editPanelState.config.select">
          <!-- 组件设置 -->
          <ConfigManager v-if="loaded && currentConfigType == 'com'" />
          <template v-else-if="loaded">
            <IndicatorSetting v-if="currentConfigType === 'screen' || platform === 'mobile'" />
          </template>
          <!-- 多个组件被框选的对齐方式 -->
          <!-- <AliginSetting v-if="loaded && currentConfigType === 'group' && platform === 'pc'" /> -->
        </template>
      </div>
    </div>
    <!-- 重命名弹框 -->
    <RenameDialog ref="rename" />
  </div>
</template>
<script>
import Vue from 'vue';
import ConfigNode from '@/components/editor/ConfigNode';
import contextmenu from '@/lib/v-contextmenu/src/index';
import '@/lib/v-contextmenu/src/styles/index.css';
import { mapState, mapGetters, mapActions } from 'vuex'
import { getCompCfg } from '@/api/component'
import { updateIndicator } from '@/api/workspace';
import CallbackManager from '@/lib/CallbackManager'
import Tree from '@/lib/Tree';
import { compact, bottom } from '@/utils/gridUtils'
import { isPanel, uuid, replaceUrl, getFormData } from '@/utils/base';
import canvasBus from '@/utils/canvasBus';
import emitter from '@/utils/bus';
import '@/components/plugins/shortcut';
import { getData } from '@/api/datastorage';
import { commonUpload } from '@/api/common';
import Rect from '@/utils/Rect';

Vue.use(contextmenu);
Vue.component(ConfigNode.name, ConfigNode);
export default {
  name: 'IndicatorLibraryEdit',
  components: {
    CompPanel: () => import('@/components/editor/CompPanel'),
    LayerManager: () => import('@/components/editor/LayerManager'),
    ConfigManager: () => import('@/components/editor/ConfigManager'),
    CanvasMain: () => import('@/components/editor/canvas/CanvasMain'),
    IndicatorSetting: () => import('@/components/editor/IndicatorSetting'),
    RenameDialog: () => import('@/components/editor/RenameDialog'),
    UpdatePanel: () => import('@/components/editor/UpdatePanel')
  },
  provide: function () {
    return {
      getLayerTree: () => this.layerTree,
      screenInfo: this.screenInfo,
      screenComs: () => {},
      callbackManager: () => this.callbackManager,
      getScale: () => 1
    }
  },
  data () {
    return {
      editable: false,
      loaded: false,
      screenName: '',
      platform: 'pc',
      layerTree: new Tree(),
      callbackManager: new CallbackManager()
    }
  },
  computed: {
    ...mapState({
      editPanelState: (state) => state.indicator.editPanelState,
      drawerBtnState: (state) => state.indicator.drawerBtnState,
      compPackages: (state) => state.indicator.compPackges,
      currentSelectId: (state) => state.editor.currentSelectId,
      screenInfo: (state) => state.editor.screenInfo,
      screenLayers: (state) => state.editor.screenLayers,
      menuPosition: (state) => state.editor.menuPosition,
      screenComs: (state) => state.editor.screenComs
    }),
    ...mapGetters('editor', ['currentConfigType', 'getComDataById', 'currentCom']),
    globalBtns () {
      return []
    }
  },
  watch: {
    screenLayers: {
      handler: function (layers) {
        this.layerTree = new Tree({ id: 'root', children: layers });
        if (this.currentCom) {
          this.layerTree.select(this.currentCom.id);
        }
      },
      immediate: true
    }
  },
  created () {
    const id = this.$route.params.id
    getCompCfg({
      id
    }).then(res => {
      if (res.success) {
        this.screenName = res.data.alias
        this.$store.commit('indicator/updateCurrentCom', res.data)
        this.initScreenAction({ screenId: res.data.config?.screens?.[0]?.id }).then(() => {
          this.loaded = true
          this.layerTree = new Tree({ id: 'root', children: this.screenLayers });
        })
      }
    })
    canvasBus.on('ctx_click', this.handleCtxClick);
  },
  mounted () {
    this.shortcut();
  },
  beforeDestroy () {
    this.$store.commit('editor/updateLoadState', false);
    this.$store.commit('editor/updateIndicatorId', '');
    this.$store.commit('indicator/setIndicatorData', null);
    this.$shortcut.unbind('⌘+c, ctrl+c');
    this.$shortcut.unbind('⌘+v, ctrl+v');
    this.$shortcut.unbind('backspace');
    canvasBus.off('ctx_click', this.handleCtxClick);
  },
  methods: {
    ...mapActions('indicator', ['initScreenAction']),
    async inputScreenBlur () {
      const id = this.$route.params.id
      const indicatorId = this.$route.query.indicatorId
      const indicatorCom = this.screenComs[id]
      if (indicatorCom.alias !== this.screenName) {
        if (this.screenName) {
          const res = await updateIndicator({ name: this.screenName }, { id: indicatorId })
          res.success && this.$message.success('修改成功');
        } else {
          this.screenName = indicatorCom.alias;
        }
      }
      this.editable = false
    },
    headerReturn () {
      const { parentId } = this.screenInfo
      if (parentId !== '1') {
        this.$router.push({
          path: `/screen/edit/${parentId}`
        });
      } else {
        this.$router.go(-1)
      }
    },
    handleEditBtnClick (btn) {
      this.$store.commit('indicator/updateEditSelect', {
        type: btn.type,
        value: !btn.select
      });
    },
    handleDrawerBtnClick (btn) {
      this.$store.commit('indicator/updateDrawerSelect', {
        type: btn.type,
        value: !btn.select
      });
    },
    hanldeGlobalBtnClick () {},
    findNodeOfGroup (node) {
      if (!node.children) return;
      node.children.forEach((item) => {
        if (item.children) {
          this.findNodeOfGroup(item);
        } else {
          if (!this.nodeOfGroup) this.nodeOfGroup = [];
          this.nodeOfGroup.push(item.data.id);
        }
      });
    },
    // 右键菜单点击事件
    handleCtxClick (key) {
      const { layerTree } = this;
      const selectedNodes = layerTree.getSelectedNodes();

      // 过滤数据，排除场景大屏可能会选到其他页面的图层，导致shift多选后删除、复制等操作错误
      // if (this.isSceneScreen) {
      //   selectedNodes = selectedNodes.filter((node) => {
      //     if (this.pageId) {
      //       return this.pageId === node.data.pageId
      //     } else if (this.sceneId) {
      //       return this.sceneId === node.data.sceneId && !node.data.pageId
      //     }
      //     return false
      //   })
      // }

      const selectedIds = selectedNodes.map((n) => n.data.id);
      let useToShowOrLock = selectedNodes.map((n) => {
        if (n.data.type !== 'group') return n.data.id;
        else {
          this.nodeOfGroup = [];
          this.findNodeOfGroup(n);
          return this.nodeOfGroup;
        }
      });
      /* 用于显示锁定图层数据 */
      useToShowOrLock = useToShowOrLock.flat();
      const soloShowCompList = Object.values(this.screenComs);

      let successCallback

      switch (key) {
        case 'top':
          layerTree.bringToTop(selectedIds);
          this.saveTree();

          successCallback = () => {
            this.$message.success('置顶成功');
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/moveScreenLayerByIds', {
                layerIds: selectedIds,
                action: 'bringToTop'
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }

          break;
        case 'bottom':
          layerTree.bringToBottom(selectedIds);
          this.saveTree();

          successCallback = () => {
            this.$message.success('置底成功');
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/moveScreenLayerByIds', {
                layerIds: selectedIds,
                action: 'bringToBottom'
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }

          break;
        case 'moveUp':
          layerTree.moveUp(selectedIds);
          this.saveTree();

          successCallback = () => {
            this.$message.success('上移成功');
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/moveScreenLayerByIds', {
                layerIds: selectedIds,
                action: 'moveUp'
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }

          break;
        case 'moveDown':
          layerTree.moveDown(selectedIds);
          this.saveTree();

          successCallback = () => {
            this.$message.success('下移成功');
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/moveScreenLayerByIds', {
                layerIds: selectedIds,
                action: 'moveDown'
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }

          break;
        case 'show':
          this.$store.dispatch(
            'editor/updateScreenCom',
            useToShowOrLock.map((id) => ({
              id,
              keyValPairs: [{ key: 'show', value: true }]
            }))
          );
          break;
        case 'hide':
          this.$store
            .dispatch(
              'editor/updateScreenCom',
              useToShowOrLock.map((id) => ({
                id,
                keyValPairs: [{ key: 'show', value: false }]
              }))
            )
            .then(() => {
              this.$message.success('隐藏成功');
            });
          break;
        case 'lock':
          this.$store
            .dispatch(
              'editor/updateScreenCom',
              useToShowOrLock.map((id) => ({
                id,
                keyValPairs: [{ key: 'attr.lock', value: true }]
              }))
            )
            .then(() => {
              this.$message.success('锁定成功');
            });
          break;
        case 'unLock':
          this.$store
            .dispatch(
              'editor/updateScreenCom',
              useToShowOrLock.map((id) => ({
                id,
                keyValPairs: [{ key: 'attr.lock', value: false }]
              }))
            )
            .then(() => {
              this.$message.success('解锁成功');
            });
          break;
        case 'group':
          this.group(selectedNodes, useToShowOrLock);
          break;
        case 'cancelGroup':
          this.cancelGroup(selectedNodes);
          break;
        case 'rename':
          this.$refs.rename.showDialog();
          break;
        case 'copy':
          this.copy();
          break;
        case 'paste':
          this.paste(!this.isMobilePlatform);
          break;
        case 'delete':
          this.delete(selectedIds, useToShowOrLock);
          break;
        case 'soloShow':
          this.$store.dispatch(
            'editor/updateScreenCom',
            soloShowCompList.map((comp) => {
              if (useToShowOrLock.includes(comp.id)) {
                return {
                  id: comp.id,
                  keyValPairs: [{ key: 'show', value: true }]
                };
              } else {
                return {
                  id: comp.id,
                  keyValPairs: [{ key: 'show', value: false }]
                };
              }
            })
          );
          break;
        case 'closeSoloShow':
          this.$store.dispatch(
            'editor/updateScreenCom',
            soloShowCompList.map((comp) => {
              return {
                id: comp.id,
                keyValPairs: [{ key: 'show', value: true }]
              };
            })
          );
          break;
      }
    },
    delete (selectedIds, compIds) {
      if (selectedIds.some(id => id.startsWith('interaction-container-modulepanel'))) { // 防止模块组件被删掉
        return;
      }
      if (_.isNil(selectedIds) || !selectedIds.length) return;
      const alias = compIds.map((id) => {
        const comData = this.getComDataById(id);
        return comData ? comData.alias : '';
      });
      const text = `删除后无法恢复，是否删除"${alias.join('，')}"共${
        compIds.length // selectedIds.length
      }个组件`;
      this.$confirm(text, '', {
        confirmButtonText: '确定(Enter)',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          this.layerTree.delete(selectedIds);
          this.saveTree();

          const successCallback = () => {
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/deleteScreenLayerByIds', {
                layerIds: selectedIds
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }

          this.$store
            .dispatch('editor/deleteComponent', compIds) // selectedIds)
            .then(() => {
              this.$message.success('删除成功');
            });
        })
        .catch(() => {});
    },
    async group (selectedNodes, useToShowOrLock) {
      const hasScene = selectedNodes.some((node) => {
        return !node.data.pageId
      })
      const hasScenePage = selectedNodes.some((node) => {
        return !!node.data.pageId
      })
      if (hasScene && hasScenePage) {
        this.$message.warn('不同页面组件不支持分组')
        return
      }

      /** 清除失效的模块组件 */
      const layers = this.layerTree.data.children;
      const clearModules = layers.filter(item => {
        if (item.id.startsWith('interaction-container-modulepanel')) {
          const com = this.getComDataById(item.id);
          if (com) {
            const groupId = com.config.groupId;
            const group = this.layerTree.getNodeById(groupId);
            if (!group) {
              return true
            }
          }
        }
        return false
      })
      if (clearModules.length) {
        const selectedIds = clearModules.map(item => item.id);
        this.layerTree.delete(selectedIds);
      }
      /** 清除失效的模块组件 ===end=== */

      const group = selectedNodes.map((item) => item.data);
      let allowGroup = true;
      group.forEach((item) => {
        if (
          (item.groupName && !item.children) ||
          new Set(useToShowOrLock).size !== useToShowOrLock.length
        ) { allowGroup = false; }
      });
      if (allowGroup) {
        let options;
        if (this.isMobilePlatform) { // 移动端分组特殊处理
          let coms = group.map(item => {
            if (item.type === 'com') {
              const com = this.getComDataById(item.id);
              return com
            }
            return this.getComDataById(item.comId)
          })

          coms = _.unionBy(coms, 'id'); // 根据id去重

          const layout = coms.map(item => {
            const attr = item.attr;
            return {
              x: attr.x,
              y: attr.y,
              w: attr.w,
              h: attr.h,
              i: item.id,
              static: attr.lock
            }
          })
          const compactLayout = compact(layout, true);

          const firstCom = coms[0];
          const gridHeight = _.ceil((bottom(compactLayout) * 15 + 10) / 15, 2);
          const moduleId = uuid('interaction-container-modulepanel');
          const comData = { // 创建分组前，先创建模块组件
            id: moduleId,
            name: 'interaction-container-modulepanel',
            version: '4.0.0',
            attr: {
              x: firstCom.attr.x,
              y: firstCom.attr.y,
              w: 24,
              h: gridHeight
            }
          }
          if (this.screenType === 'scene') {
            comData.sceneId = this.sceneId;
            comData.pageId = this.pageId;
          }
          const res = await this.$store.dispatch('editor/createScreenComp', comData);
          if (res.success) {
            const _groupId = `groups_${+new Date()}`;
            const newData = this.getComDataById(comData.id)
            const layer = {
              id: newData.id,
              type: newData.type
            }
            this.layerTree.addChild(layer);
            options = {
              id: _groupId,
              comId: newData.id
            }
            await this.$store.dispatch('editor/updateScreenCom', {
              id: newData.id,
              keyValPairs: [
                { key: 'config.groupId', value: _groupId }
              ]
            })
          }
        }
        const groupNode = this.layerTree.createGroup(group, options);
        this.saveTree();

        const successCallback = () => {
          this.$message.success('分组成功');
          this.recoverTree();
        }

        if (this.isSceneAndCoedit) {
          // 移动端屏不会进入该判断
          this.$store
            .dispatch('editor/createLayerGroups', {
              screenId: this.screenInfo.id,
              groups: [groupNode.toJson()]
            })
            .then(() => {
              successCallback()
            })
        } else {
          this.$store
            .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
            .then(() => {
              successCallback()
            });
        }
      } else {
        canvasBus.emit('select_node', null);
        this.layerTree.clearSelect();
        this.$message.warn('请选中图层后进行分组操作！');
      }
    },
    formatCopyrTree (nodes) {
      nodes.forEach((node) => {
        delete node.tree;
        delete node.parent;
        if (node.children) this.formatCopyrTree(node.children);
      });
    },
    /* 存储图层右键操作前的树结构 */
    saveTree () {
      const copyTree = _.cloneDeep(Array.from(this.layerTree.loadedNodes || []));
      copyTree.forEach((node) => {
        delete node.tree;
        delete node.parent;
        if (node.children && node.children.length !== 0) { this.formatCopyrTree(node.children); }
      });
      for (let i = copyTree.length - 1; i >= 0; i--) {
        if (
          copyTree[i].data.type === 'group' &&
          copyTree[i].children && copyTree[i].children.length === 0
        ) {
          this.layerTree.delete(copyTree[i].data.id);
        }
      }
      localStorage.setItem('originTree', JSON.stringify(copyTree));
    },
    /* 对图层右键操作后不改变原有树文件夹的展开收起情况 */
    recoverTree () {
      let originTree = localStorage.getItem('originTree');
      originTree = JSON.parse(originTree);
      let i = 0;
      let j = 0;
      while (i < originTree.length && j < this.layerTree.loadedNodes.length) {
        const tree = this.layerTree.loadedNodes;
        if (
          tree[j].data.id === originTree[i].data.id &&
          tree[j].data.type === 'group' &&
          originTree[i].collapse === false
        ) {
          tree[j].collapse = originTree[i].collapse;
          this.layerTree.collapseChildNodes(tree[j].data, tree[j].collapse);
          this.recursionChangeCollapse(
            tree[j].children,
            originTree[i].children
          );
          i++;
          j++;
        } else if (tree[j].data.id !== originTree[i].data.id) {
          j++;
        } else if (tree[j].data.id === originTree[i].data.id) {
          i++;
          j++;
        }
      }
    },
    recursionChangeCollapse (tree, originTree) {
      tree.forEach((node, index) => {
        node.collapse = originTree[index].collapse;
        this.layerTree.collapseChildNodes(node.data, node.collapse);
        if (node.children) {
          this.recursionChangeCollapse(
            node.children,
            originTree[index].children
          );
        }
      });
    },
    async cancelGroup (selectedNodes) {
      const nodes = selectedNodes.map((item) => item.data);
      const groups = nodes.filter(item => item.type === 'group');

      const clearModules = [];

      for (const group of groups) {
        await this.layerTree.cancelGroup(group);
        if (this.isMobilePlatform) {
          clearModules.push({ id: group.comId });
        }
      }

      if (clearModules.length) { // 清除没用的模块组件
        const selectedIds = clearModules.map(item => item.id);
        await this.layerTree.delete(selectedIds);
        await this.$store.dispatch('editor/deleteComponent', selectedIds);
      }
      this.saveTree();

      const successCallback = () => {
        this.$message.success('取消分组成功');
        this.recoverTree();
      }

      if (this.isSceneAndCoedit) {
        this.$store
          .dispatch('editor/cancelLayerGroups', {
            screenId: this.screenInfo.id,
            layerGroupIds: groups.map((item) => {
              return item.id
            })
          })
          .then(() => {
            successCallback()
          })
      } else {
        this.$store
          .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
          .then(() => {
            successCallback()
          });
      }
    },
    copy () {
      let copyIds = this.currentSelectId;
      if (_.isNil(copyIds)) {
        this.$message.error('请选择组件');
        return;
      }
      if (!_.isArray(copyIds)) {
        copyIds = [copyIds];
      }
      const originCom = copyIds
        .map((id) => this.getComDataById(id))
        .filter((d) => !_.isNil(d));
      const copyObj = {
        platform: this.platform,
        originCom
      };
      // 复制中包含分组的情况下存储tree结构
      const { layerTree } = this;
      const selectedNodes = layerTree.getSelectedNodes();
      if (selectedNodes.length) {
        let judgeCopyTree = false;
        for (let i = 0; i < selectedNodes.length; i++) {
          if (selectedNodes[i].data.type === 'group') {
            judgeCopyTree = true;
            break;
          }
        }

        // 判断是否存在分组
        if (judgeCopyTree) {
          // 处理移动端、并且有分组的复制
          if (this.isMobilePlatform) {
            // 找到分组对应的组件id 和 分组下的 组件id
            const getGroupAndChildComIds = (nodes, comIds = []) => {
              for (const node of nodes) {
                comIds.push(node.data.type === 'group' ? node.data.comId : node.data.id)

                if (node.children) {
                  getGroupAndChildComIds(node.children, comIds)
                }
              }
              return comIds
            }

            const comIds = getGroupAndChildComIds(selectedNodes)
            // 设置要复制的组件列表
            copyObj.originCom = comIds.map((id) => this.getComDataById(id)).filter((d) => !_.isNil(d));
          }

          const list = _.cloneDeep(selectedNodes);
          this.deleteParent(list);
          localStorage.setItem('copyTree', JSON.stringify(list));
        } else {
          localStorage.setItem('copyTree', JSON.stringify(''));
        }
      }

      localStorage.setItem('copyCom', JSON.stringify(copyObj));
      this.$message.success('复制成功');
      if (navigator.clipboard) {
        // 此处清空剪贴板内容，防止与组件复制冲突
        navigator.clipboard.writeText('');
        this.clipboardData = '';
      }
    },
    deleteParent (node) {
      node.map((item) => {
        if (item.parent) {
          delete item.parent;
          delete item.tree;
        }
        if (item.children) this.deleteParent(item.children);
      });
    },
    /* 递归修改复制的分组id */
    changeCopyTreeId (node, newComData) {
      if (!this.compIndex) {
        this.compIndex = 0;
      }
      node.map((item) => {
        if (item.data.type === 'group') {
          // 区分小屏移动端分组 和 大屏分组
          if (this.isMobilePlatform) {
            item.data.id = 'groups_' + newComData[this.compIndex].id
            item.data.comId = newComData[this.compIndex].id
            item.comId = newComData[this.compIndex].id
            this.compIndex++
          } else {
            const id = item.data.id.split('_');
            item.data.id = uuid(id[0]);
          }
        } else {
          item.data.id = newComData[this.compIndex++].id;
        }
        if (item.data.groupName) {
          item.groupName = item.data.groupName;
        }
        if (this.screenType === 'scene') {
          item.sceneId = this.sceneId;
          item.pageId = this.pageId;
        }
        item.id = item.data.id;
        item.type = item.data.type;
        delete item.collapse;
        delete item.depth;
        delete item.loaded;
        delete item.selected;
        if (item.children) {
          this.changeCopyTreeId(item.children, newComData);
        }
      });
    },
    shortcut () {
      this.$shortcut.bind('⌘+c, ctrl+c', { func: this.copy });
      this.$shortcut.bind('⌘+v, ctrl+v', {
        func: this.paste,
        params: false
      });
      this.$shortcut.bind('backspace', {
        func: this.handleCtxClick,
        params: 'delete'
      });
    },
    handleAfterCreate ({ id, name }) {
      if (name === 'data-text-normal') {
        // 文本组件
        this.$store
          .dispatch('editor/updateScreenCom', {
            id,
            keyValPairs: [
              {
                key: 'staticData',
                value: [{ text: this.clipboardData }]
              }
            ]
          })
          .then(async () => {
            try {
              const res = await getData({
                componentId: id,
                type: 'static',
                workspaceId: this.currWorkspaceId || this.screenInfo.workspaceId
              });
              this.$store.commit('editor/updateComData', {
                componentId: id,
                data: res.data
              });
              emitter.off('after_createCom', this.handleAfterCreate);
              this.$message.success('粘贴成功');
            } catch (e) {
              emitter.off('after_createCom', this.handleAfterCreate);
              console.warn(e);
            }
          });
      } else if (name === 'media-image-normal') {
        // 图片组件
        const formData = getFormData(this.clipboardData);
        commonUpload(formData)
          .then((res) => {
            if (res.success && res.data) {
              const imageUrl = replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.url);
              this.$store
                .dispatch('editor/updateScreenCom', {
                  id,
                  keyValPairs: [
                    {
                      key: 'config.url',
                      value: imageUrl
                    }
                  ]
                })
                .then(async () => {
                  // try {
                  //   const res = await getData({
                  //     componentId: id,
                  //     type: 'static',
                  //     workspaceId: this.currWorkspaceId || this.screenInfo.workspaceId
                  //   });
                  //   this.$store.commit('editor/updateComData', {
                  //     componentId: id,
                  //     data: res.data
                  //   });
                  // } catch (e) {
                  //   console.warn(e);
                  // }
                });
              emitter.off('after_createCom', this.handleAfterCreate);
              this.$message.success('粘贴成功');
            }
          })
          .catch((e) => {
            emitter.off('after_createCom', this.handleAfterCreate);
            console.warn(e);
          });
      }
    },
    createCom (type, isMouse) {
      const com = this.$refs.compPanel
        ? this.$refs.compPanel.findComByName(type)
        : null;
      let pos = null;
      if (com) {
        if (isMouse) {
          pos = {
            x: this.menuPosition.x,
            y: this.menuPosition.y
          };
        }
        this.$refs.compPanel.handleItemClick(com, pos);
        emitter.on('after_createCom', this.handleAfterCreate);
      }
    },
    async paste (isMouse) {
      if (navigator.clipboard) {
        try {
          const data = await navigator.clipboard.read();
          const type = data[0].types[data[0].types.length - 1];
          this.clipboardData = null;
          const reader = new FileReader();
          reader.onload = () => {
            this.clipboardData = reader.result;
            if (type === 'text/plain') {
              this.createCom('data-text-normal', isMouse);
            } else if (type === 'text/html') {
              // 将html类型内容转为字符串
              this.clipboardData = this.convertHtmlToString(reader.result);
              this.createCom('data-text-normal', isMouse);
            } else if (type === 'image/png') {
              this.createCom('media-image-normal', isMouse);
            }
          };
          if (type === 'text/html') {
            const content = await data[0].getType('text/html');
            reader.readAsText(content);
          } else if (type === 'text/plain') {
            const content = await data[0].getType('text/plain');
            reader.readAsText(content);
          } else if (type === 'image/png') {
            const content = await data[0].getType('image/png');
            reader.readAsDataURL(content);
          } else {
            console.warn('暂时处理不了' + type + '类型的内容！');
          }
        } catch (e) {
          this.pasteCom(isMouse);
        }
      } else {
        this.pasteCom(isMouse);
      }
    },
    async pasteCom (isMouse) {
      const copyObj = JSON.parse(localStorage.getItem('copyCom'));
      // 剪贴板为空，走组件粘贴逻辑
      if (_.isNil(copyObj)) {
        this.$message.error('请选择组件');
        return;
      }

      const platform = copyObj.platform;
      if (platform !== this.platform) {
        // pc端/移动端不能互相复制粘贴
        this.$message.error('pc端与移动端不能互相复制组件！');
        return;
      }

      const originCom = copyObj.originCom;
      if (!originCom.length) {
        this.$message.error('请选择组件');
        return;
      }
      // 如果是子面板 禁止复制面板类组件
      if (this.isDynamicScreen) {
        const index = originCom.findIndex(item => isPanel(item.comName));
        if (index > -1) {
          this.$message.error('子面板不允许复制面板类组件！');
          return
        }
      }

      let originRect = null
      let lastComBottom = 0

      if (!this.isMobilePlatform) {
        // 获取pc端位置
        originRect = new Rect(
          originCom.map((com) => ({
            x: com.attr.x,
            y: com.attr.y,
            w: com.attr.w,
            h: com.attr.h
          }))
        );
      } else {
        // 获取移动端排在最底组件y轴
        for (const key in this.screenComs) {
          const com = this.screenComs[key];
          lastComBottom = Math.max(lastComBottom, com.attr.y + com.attr.h)
        }
      }

      const copyData = originCom.map((com) => {
        let offsetX = 20;
        let offsetY = 20;
        if (isMouse && originRect) {
          offsetX = this.menuPosition.x - originRect.x;
          offsetY = this.menuPosition.y - originRect.y;
        }

        const comData = {
          originId: com.id,
          newData: {
            id: uuid(com.comName),
            name: com.comName,
            version: com.version,
            attr: this.isMobilePlatform ? {
              x: com.attr.x,
              y: lastComBottom
            } : {
              x: Math.floor(com.attr.x + offsetX),
              y: Math.floor(com.attr.y + offsetY)
            }
          }
        };
        if (this.screenType === 'scene') {
          comData.newData.pageId = this.pageId;
          comData.newData.sceneId = this.sceneId;
        }
        // 更新移动端关联图层分组id
        if (com.comType === 'interaction-container-modulepanel') {
          comData.newData.config = {
            groupId: 'groups_' + comData.newData.id
          }
        }
        return comData;
      });
      const res = await this.$store.dispatch(
        'editor/copyScreenComp',
        copyData
      );
      if (res) {
        await this.$store.dispatch('editor/getScreenFilters', {
          screenId: this.screenInfo.id
        });
        // 提取出复制成功的组件
        const newComData = copyData
          .map((cp) => this.getComDataById(cp.newData.id))
          .filter((d) => !_.isNil(d));
        if (newComData.length) {
          const haveGroup = localStorage.getItem('copyTree');
          let layers = []
          if (typeof JSON.parse(haveGroup) === 'string') {
            layers = newComData.map((d) => {
              const layer = {
                id: d.id,
                type: d.type
              };
              if (this.screenType === 'scene') {
                layer.pageId = this.pageId;
                layer.sceneId = this.sceneId;
              }
              return layer;
            })
            this.layerTree.addChild(layers);
          } else {
            const addChild = _.cloneDeep(JSON.parse(haveGroup));
            this.changeCopyTreeId(addChild, newComData);
            this.compIndex = 0;
            layers = addChild.map((d) => {
              const layer = {
                id: d.id,
                type: d.type
              };
              if (this.screenType === 'scene') {
                layer.pageId = this.pageId;
                layer.sceneId = this.sceneId;
              }
              if (d.data.type === 'group') {
                layer.groupName = d.data.groupName;

                // 获取分组的children数据，排除data字段，因为没用到，冗余
                const getChildren = (list = []) => {
                  const newChildren = []
                  for (const item of list) {
                    const newItem = {
                      ...item
                    }
                    delete newItem.data

                    if (newItem.children) {
                      newItem.children = getChildren(newItem.children)
                    }

                    newChildren.push(newItem)
                  }
                  return newChildren
                }

                layer.children = getChildren(d.children);

                // 处理移动端分组comId
                if (this.isMobilePlatform) {
                  layer.comId = d.comId
                }
              }
              return layer;
            })
            this.layerTree.addChild(layers);
          }
          this.saveTree();

          if (this.isSceneAndCoedit) {
            await this.$store
              .dispatch('editor/insertScreenLayers', {
                screenId: this.screenInfo.id,
                layers: layers
              })
          } else {
            await this.$store
              .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
          }

          // const ids = newComData.map(d => d.id);
          // this.layerTree.select(ids);
          // canvasBus.emit('select_node', ids);
          this.$message.success('粘贴成功');
          this.recoverTree();
        } else {
          this.$message.error('粘贴失败');
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/style/mixins";
.indicator-library-edit {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .edit-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 5px;
    margin-left: 20px;
    .base-button {
      display: inline-block;
      width: 32px;
      height: 28px;
      line-height: 1;
      white-space: nowrap;
      cursor: pointer;
      background: transparent;
      border: none;
      border-radius: 4px;
      color: #fff;
      text-align: center;
      outline: none;
      transition: 0.1s;
      position: relative;
      &.drop-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 32px;
        > i {
          font-size: 12px;
        }
      }
      > .icon {
        font-size: 16px;
      }
      &.select {
        background: var(--seatom-sub-main-color);
        .error-nums {
          right: -8px;
          top: -6px;
        }
      }
      &.abnormal > .icon {
        font-size: 19px;
      }
      .error-nums {
        position: absolute;
        right: 0px;
        top: -2px;
        font-size: 12px;
        width: 15px;
        height: 15px;
        line-height: 15px;
        border-radius: 50%;
        background: #ff475d;
        transform: scale(0.9);
      }
    }
    .btn-name {
      font-size: 12px;
      color: #fff;
      margin-top: 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .btn-primary {
      background: #1F71FF;
      border-color: #1F71FF;
    }
    .btn-icon {
      cursor: pointer;
      i {
        color: #fff;
      }
      line-height: 28px;
      font-size: 19px;
    }
  }
  .seatom-header {
    position: relative;
    height: 71px;
    z-index: 100;
    padding-right: 8px;
    display: flex;
    align-items: center;
    user-select: none;
    color: #a1aeb3;
    background: #1d1e1f;
    border-bottom: 1px solid #000;
    .header-return {
      height: 100%;
      width: 233px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &-icon {
        display: flex;
        width: 32px;
        font-size: 20px;
        justify-content: center;
        align-items: center;
        height: 100%;
        cursor: pointer;
        &:hover {
          background-color: #30333d;
          color: #4fb0ff;
        }
      }
      .edit-name {
        display: flex;
        align-items: center;
        width: 150px;
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        color: #fff;
        padding: 0 15px;
        .name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .icon {
          margin-left: 10px;
          cursor: pointer;
        }
      }
      ::v-deep .el-input {
        width: 90%;
        .el-input__inner {
          border-color: #409eff;
        }
      }
    }
    .edit-top-btn {
      flex: 1;
      height: 70px;
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .edit-btns-group {
        display: flex;
      }
      .drawer-actions {
        display: flex;
        flex: 0.8;
      }
      .global-btns-group {
        display: flex;
      }
    }
  }
  .loading-indicator {
    position: absolute;
    top: 70px;
    left: 0;
    width: 100%;
    height: 0;
    z-index: 101;
    transition: height 0.5s;
    background: linear-gradient(to right, #ff8754, #2681ff);
    @keyframes ladingAnimation {
      from {
        transform: translateX(-100%);
      }
      to {
        transform: translateX(150%);
      }
    }
    &.loading {
      height: 1px;
      animation: ladingAnimation 1s infinite ease-out;
    }
  }
  .seatom-popover-portal {
    position: relative;
    width: 100%;
    height: 0;
    .update-panel-wp {
      position: absolute;
      left: -50px;
      top: 5px;
    }
  }
  .seatom-drawer__mask {
    position: absolute;
    height: 100%;
    width: 100%;
  }
  .seatom-eidt-main {
    flex: 1;
    display: flex;
    flex-wrap: nowrap;
    position: relative;
    overflow: hidden;
    background: #313239;
    .layer-panel-wp {
      flex: none;
      position: relative;
      width: 200px;
      height: 100%;
      z-index: 5;
      background: #1d1f26;
      display: flex;
      flex-direction: column;
      transition: width 0.3s ease;
      border-right: 1px solid #000;
      overflow: hidden;
      &.--hide {
        width: 0;
      }
    }
    .component-panel-wp {
      flex: none;
      position: relative;
      width: 233px;
      height: 100%;
      z-index: 4;
      background: var(--seatom-panel-color);
      transition: width 0.3s ease;
      overflow: hidden;
      box-shadow: 1px 0 #000;
      &.--hide {
        width: 0;
      }
    }
    .source-panel-wp {
      position: absolute;
      width: 320px;
      height: 100%;
      background: var(--seatom-panel-color);
      z-index: 12;
      transition: width 0.3s ease;
      overflow: hidden;
      box-shadow: 1px 0 #000;
      &.--hide {
        width: 0;
      }
    }
    .right-edit-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      height: 100%;
      position: relative;
      .toolbox-panel-wp {
        height: 40px;
        transition: height 0.3s ease;
        border-bottom: 1px solid #000;
        z-index: 10;
        &.--hide {
          height: 0;
          overflow: hidden;
        }
      }
      .editor-panel-wp {
        width: 100%;
        height: 100%;
        flex: 1;
        display: flex;
        position: relative;
        overflow: hidden;
      }
    }
    .config-panel-wp {
      width: 332px;
      height: 100%;
      z-index: 90;
      background: #1c1f25;
      position: relative;
      transition: width 0.25s ease-in-out;
      // overflow: hidden;
      box-shadow: -1px 0 #000;
      &.--hide {
        width: 0;
      }
    }
    .filter-panel-wp {
      position: absolute;
      width: 500px;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 90;
      background: #1c1f25;
      box-shadow: -1px 0 #000;
      transition: transform 0.25s linear;
      overflow: hidden;
      &.--hide {
        transform: translateX(-500px);
      }
    }
    .permission-panel-wp {
      position: absolute;
      width: 400px;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 90;
      background: #1c1f25;
      box-shadow: -1px 0 #000;
      transition: transform 0.25s linear;
      overflow: hidden;
      &.--hide {
        transform: translateX(-400px);
      }
    }
    .publicDataManage-panel-wp {
      position: absolute;
      width: 510px;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 90;
      background: #1c1f25;
      box-shadow: -1px 0 #000;
      transition: transform 0.25s linear;
      overflow: hidden;
      &.--hide {
        transform: translateX(-510px);
      }
    }
  }
}
.mr-4 {
  margin-right: 4px;
}
</style>
<style lang="scss">

</style>
