<template>
  <div class="screen-container">
    <ViewScreen
      ref="screen"
      v-if="isScreenCreate"
      :screenId="screenId"
      :screenData="screenData"
      @loaded="handleScreenLoaded"
      @dataLoad="screenName"
      @progress="progress"
      :lazyload="true"
      :authObj="authObj"
    />
    <div v-if="!isAuth">无访问权限</div>
    <div class="qr-code-wrap" v-drag v-if="isConnect">
      <span class="close el-icon-close" @click.stop="isConnect = false"></span>
      <div class="qr-code" @click="openDialog">
        <VueQrcode :value="shareUrl" :options="options" />
      </div>
      <div class="title">设备实例扫码</div>
    </div>
    <!-- 终端交互 -->
    <TerminalCode v-if="isConnect" ref="terminal" :screenId="screenId" />
    <VoiceControl :screenId="screenId" :voiceConfig="voiceConfig" v-if="showVoiceControl" />
    <PasswordProtect v-if="showPwd" @confirm="handlePwdConfirm" />
  </div>
</template>

<script>
import VueSocket from 'vue-socket.io';
import { screenShareVerify, getscreenlinkAge } from '@/api/screen';
import { authPermission } from '@/api/common';
import { getFingerprintID, replaceUrl } from '@/utils/base';
import Vue from 'vue';
import monitor from '@/plugins/monitor';
const PasswordProtect = () => import('@/components/view/PasswordProtect');
const VueQrcode = () => import('@chenfengyuan/vue-qrcode');
const TerminalCode = () => import('@/components/editor/TerminalCode');
const VoiceControl = () => import('@/components/editor/voicecontrol');

Vue.prototype.$isControl = false;

export default {
  components: {
    VueQrcode,
    PasswordProtect,
    TerminalCode,
    VoiceControl
  },

  provide: function () {
    return {
      callbackFatherManager: () => {},
      permissionFatherMap: () => null
    };
  },

  data () {
    return {
      loading: false,
      screenId: null,
      screenData: null,
      isScreenCreate: false,
      isConnect: false,
      isAuth: true,
      showPwd: false,
      showLoading: false,
      authObj: {},
      options: {
        width: 65,
        height: 65,
        margin: 1
      },
      progressValue: 0,
      voiceConfig: {}
    };
  },

  computed: {
    shareUrl () {
      return `${window.location.origin}/screen/control/${this.screenId}`;
    },
    showVoiceControl () {
      return this.screenData && this.screenData.voiceControl
    }
  },

  async created () {
    const { screenId } = this.$route.params;
    const { token, userId } = this.$route.query;
    if (token && userId) {
      localStorage.setItem('token', token);
      localStorage.setItem('userId', userId);
    }

    if (screenId) {
      this.screenId = screenId;
    }

    // 获取大屏数据
    await this.getscreenlinkAge();
    this.getMainScreenData()
  },

  methods: {
    // 获取大屏数据
    async getMainScreenData () {
      const { screenId, shareToken } = this.$route.params;
      const { isShare } = this.$route.meta;
      const query = {
        screenId: screenId
      };

      if (isShare) {
        query.shareToken = shareToken
      }
      try {
        this.loading = true;

        this.screenData = await this.$store.dispatch('editor/getPreviewScreen', query);

        this.$store.commit('view/initScreenData', this.screenData);

        this.voiceConfig = this.screenData.voiceConfig || {};

        if (isShare) {
          this.initScreenShare()
          this.initMonitor()
        } else {
          this.createScreen(this.$route.query.loading);
        }

        this.addFontface()

        this.loading = false
      } catch (error) {
        this.loading = false;
        this.$message && this.$message.error(error.message);
      }
    },

    async initScreenShare () {
      const screenShare = this.screenData.screenShare
      const { loading, updateCache } = this.$route.query

      this.verifyParam = {
        screenId: screenShare.screenId
      };
      this.screenId = screenShare.screenId
      const enableCache = screenShare.enableCache
      const needAuth = screenShare.needAuth
      if (needAuth) { // 启用第三方用户体系接入
        Vue.prototype.$needAuth = true;
        await this.initNeedAuth();
      }
      if (enableCache) { // 启用数据缓存
        Vue.prototype.$enableCache = true;
      }
      if (screenShare.needPassword && updateCache !== 'true') {
        this.showPwd = true;
      } else {
        if (this.isAuth) {
          this.createScreen(loading);
        }
      }
    },

    // 埋点监控初始化
    initMonitor () {
      // 先在发布页测试
      // eslint-disable-next-line
      monitor.init({
        requestUrl: '/monitor/upLog',
        monitorId: process.env.VUE_APP_MONITOR_ID,
        userId: this.screenData.screenShare.userId || '',
        workspaceId: this.screenData.workspaceId || '',
        maxCacheLen: 200, // 最大队列长度
        isTrace: process.env.VUE_APP_MONITOR_IS_TRACE === 'true', // 是否执行采集上报动作，这个参数如果为false，什么都不会采集
        isTraceResourcePerformance: true // 是否采集静态资源性能
      });
    },

    initScreensocket () {
      const screenSocket = this.screenData.screenSocket;
      this.isConnect = screenSocket?.isConnect;
      setTimeout(() => {
        const { showCode } = this.$route.query;
        if (this.isConnect && showCode === '1') {
          this.openDialog();
        }
      }, 1000)
    },

    // 添加字体到docs
    addFontface () {
      if (!window.cangjie || (window.cangjie && window.cangjie.fonts.length === 0)) {
        const fonts = this.screenData.fonts || []
        // 加载自定义字体
        // const userId = localStorage.getItem('userId');
        let rule = '';
        // const res = await getFontList({ userId: userId });
        fonts.forEach(item => {
          rule += '@font-face {font-family:"' + item.fontName + '";src:url("' + replaceUrl(process.env.VUE_APP_SERVER_URL + item.fontUrl) + '");}'
        });
        const sty = document.createElement('style');
        sty.type = 'text/css';
        sty.innerHTML = rule;
        document.getElementsByTagName('head')[0].appendChild(sty);
        // 获取字体列表挂载至大屏中，供编辑器中字体设置使用
        window.cangjie = { fonts: [] };
        window.cangjie.fonts = fonts;
      }
    },
    // 发布页面对接第三方用户体系接入
    async initNeedAuth () {
      const { exchange_token: exchangeToken, domain } = this.$route.query;
      if (exchangeToken && domain) {
        const params = {
          exchangeToken,
          domain
        }
        try {
          const res = await authPermission(params)
          if (res && res.success) {
            const authRes = res.data
            if (authRes.code === 200) {
              const { access, access_token: accessToken, ...otherData } = authRes.data
              this.isAuth = access;
              Vue.prototype.$authAccessToken = accessToken;
              this.authObj = otherData;
            }
          }
        } catch (error) {
        }
      }
    },
    async createScreen (loading) {
      if (loading !== 'false') {
        this.showLoading = true;
        const mask = document.querySelector('#loading-mask');
        mask.style.display = 'block';
      }
      this.initScreensocket()

      if (this.isConnect) {
        getFingerprintID().then(uid => {
          Vue.use(new VueSocket({
            debug: false,
            connection: replaceUrl('/chat', null, true),
            // connection: 'http://192.168.4.90:5001/chat',
            options: {
              path: '/api/socket',
              query: {
                screenId: this.screenId,
                isControl: false,
                uid: uid
              }
            }
          }))
          this.$vueSocketIo.emitter.addListener('message', msg => {
            this.broadcastMessage(msg);
          }, this)
        });
      }
      this.isScreenCreate = true;
    },
    progress (e) {
      this.progressValue = e - 0;
    },
    handlePwdConfirm (pwd) {
      pwd = _.trim(pwd);
      if (pwd) {
        this.verifyParam.sharePassword = pwd;
        screenShareVerify(this.verifyParam).then((res) => {
          if (res.success && res.data) {
            this.showPwd = false;
            this.createScreen();
          } else {
            alert('密码错误！');
          }
        });
      }
    },
    async handleScreenLoaded () {
      this.showLoading = false;
      const mask = document.querySelector('#loading-mask');
      // top && top.postMessage('loaded', window.location.origin);
      mask.style.display = 'none';
      // 页面截屏
      if (_.isFunction(window.screenshotStart)) {
        this.$nextTick(() => {
          window.screenshotStart.call(null);
        });
      }
    },
    openDialog () {
      // 打开终端交互弹框
      this.$refs.terminal.showDialog();
    },
    screenName (screenName) {
      document.title = screenName;
    },
    broadcastMessage (msg) {
      const screen = this.$refs.screen;
      if (screen) {
        screen.receivedMessage(msg);
      }
    },
    async getscreenlinkAge () {
      const params = {
        screenId: this.screenId
      }
      try {
        const res = await getscreenlinkAge(params)
        if (res.success) {
          const Map = res.data.reduce((acc, item) => {
            item.linkAge.forEach(link => {
              if (link.immediate) {
                link.linkageConfig.forEach(config => {
                  const filteredComponentList = config.componentList.filter(component => component.sourceType === 'server');
                  filteredComponentList.forEach(component => {
                    acc[component.componentId] = acc[component.componentId] || [];
                    acc[component.componentId].push(component);
                  });
                });
              }
            });
            return acc;
          }, {});
          this.$store.commit('view/updateImmediateLink', Map);
        }
      } catch (e) {}
    }
  }
};
</script>

<style lang="scss" scoped>
.screen-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: none;
  .loading-icon {
    display: inline-block;
    width: 128px;
    height: 128px;
    background: url("../../assets/img/svg/loading.svg");
    animation: loading 2s infinite ease-in-out;
  }
  .qr-code-wrap {
    position: absolute;
    width: 106px;
    height: 126px;
    right: 20px;
    bottom: 20px;
    padding: 25px 15px 0;
    text-align: center;
    background: rgba(41, 41, 41, 0.74);
    box-shadow: 0px 48px 128px -16px rgba(4, 8, 16, 0.64),
      0px 16px 64px -16px rgba(4, 8, 16, 0.72), 0px 0px 1px rgba(4, 8, 16, 0.32);
    .close {
      position: absolute;
      top: 5px;
      right: 10px;
      cursor: pointer;
    }
    .qr-code {
      cursor: pointer;
      margin-bottom: 5px;
    }
    .title {
      font-size: 12px;
      color: #fff;
    }
  }
}
@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
