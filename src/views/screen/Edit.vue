<template>
  <div class="seatom-edit">
    <!-- 头部 -->
    <div class="seatom-header">
      <div class="header-return">
        <span @click="headerReturn" class="header-return-icon">
          <i class="el-icon-arrow-left"></i>
        </span>
        <div class="edit-name" v-if="!editable">
          <span class="name" :title="screenName">{{ screenName }}</span>
          <i
            class="icon el-icon-edit"
            v-if="loaded && !isDynamicScreen"
            @click="editName"
          ></i>
        </div>
        <el-input
          v-else
          v-focus
          size="mini"
          @blur="inputScreenBlur"
          v-model="screenName"
        ></el-input>
      </div>
      <div class="edit-top-btn">
        <div class="edit-btns-group">
          <div
            class="edit-btn"
            v-for="btn in editPanelState"
            :key="btn.type"
            @click.stop="handleEditBtnClick(btn)"
          >
            <button
              class="base-button"
              :title="btn.name"
              :class="{ select: btn.select }"
            >
              <hz-icon class="icon" :name="btn.icon"></hz-icon>
            </button>
            <span class="btn-name">{{ btn.name }}</span>
          </div>
        </div>
        <div class="drawer-actions">
          <div
            class="edit-btn"
            v-for="btn in drawerBtnState"
            :key="btn.type"
            @click.stop="handleDrawerBtnClick(btn)"
          >
            <button
              class="base-button"
              :title="btn.name"
              :class="{ select: btn.select, abnormal: btn.type === 'abnormal' }"
            >
              <hz-icon class="icon" :name="btn.icon"></hz-icon>
              <!-- <span
                class="error-nums"
                v-if="btn.type === 'abnormal' && allErrors"
                >{{ allErrors }}</span
              > -->
            </button>
            <span class="btn-name">{{ btn.name }}</span>
            <!-- 组件升级 || 基于骨骼布局-->
            <div
              class="seatom-popover-portal"
              v-if="
                btn.select && (btn.type === 'update' || btn.type === 'layout' || btn.type === 'childScreens')
              "
            >
              <div class="update-panel-wp">
                <UpdatePanel v-if="btn.type === 'update'" />
                <SkeletonLayoutPanel v-if="btn.type === 'layout'" />
                <ChildScreens v-if="btn.type === 'childScreens'" />
              </div>
            </div>
          </div>
        </div>
        <div class="global-btns-group">
          <template v-for="btn in filterGlobalBtns">
            <el-dropdown :key="btn.id" v-if="btn.id === 'preview'" placement="bottom">
              <div class="edit-btn" @click.stop="hanldeGlobalBtnClick(btn)">
                <button class="base-button" :title="btn.name">
                  <hz-icon class="icon" :name="btn.icon"></hz-icon>
                </button>
                <span class="btn-name">{{ btn.name }}</span>
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="hanldeGlobalBtnClick(btn, false)"><hz-icon class="icon mr-4" :name="btn.icon" />无加载动画预览</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <div
              class="edit-btn"
              :key="btn.id"
              v-else
              @click.stop="hanldeGlobalBtnClick(btn)"
            >
              <button class="base-button" :title="btn.name">
                <hz-icon class="icon" :name="btn.icon"></hz-icon>
              </button>
              <span class="btn-name">{{ btn.name }}</span>
            </div>
          </template>

        </div>
      </div>
    </div>
    <!-- 加载线 -->
    <div class="loading-indicator" :class="{ loading: !loaded }"></div>
    <!-- 底部内容 -->
    <div class="seatom-eidt-main">
      <!-- 图层列表 -->
      <div
        class="layer-panel-wp"
        :class="{ '--hide': !editPanelState.layer.select }"
      >
        <LayerManager v-if="loaded" />
      </div>
      <!-- 组件列表 -->
      <div
        class="component-panel-wp"
        :class="{ '--hide': !editPanelState.component.select }"
      >
        <CompPanel v-if="loaded" ref="compPanel" />
      </div>
      <!-- 资源 -->
      <div
        class="source-panel-wp"
        :class="{ '--hide': !editPanelState.tool.select }"
      >
        <!-- <SourceList v-if="editPanelState.tool.select" /> -->
        <IndicatorList v-if="editPanelState.tool.select"></IndicatorList>
      </div>
      <div class="right-edit-main">
        <!-- 工具栏 -->
        <div class="toolbox-panel-wp">
          <ToolboxPanel v-if="loaded" />
        </div>
        <!-- 画布 -->
        <div class="editor-panel-wp" ref="panel">
          <CanvasMain v-if="loaded" data-code="1" />
        </div>
      </div>
      <!-- 抽屉按钮的遮罩 -->
      <div
        class="seatom-drawer__mask"
        @click="handleDrawerMaskClick"
        v-show="drawerMask"
      ></div>
      <!-- 右侧配置项 -->
      <div
        class="config-panel-wp"
        :class="{ '--hide': !editPanelState.config.select }"
      >
        <template v-if="editPanelState.config.select">
          <!-- 组件设置 -->
          <ConfigManager v-if="loaded && currentConfigType == 'com'" @editIndicator="editIndicator" />
          <!-- 场景页面 -->
          <template v-else-if="screenType === 'scene'">
            <template v-if="platform === 'pc'">
              <ScenePage
                @editEnd="editEnd"
                @pageChange="pageChange"
                @sceneChange="sceneChange"
                ref="scenePageRef"
                v-if="loaded && currentConfigType === 'screen'"
              />
            </template>
            <template v-else-if="platform === 'mobile'">
              <MobileSetting v-if="loaded && currentConfigType === 'screen'" />
            </template>
          </template>
          <template v-else>
            <PageSetting v-if="loaded && (currentConfigType === 'screen' || platform === 'mobile')" />
          </template>
          <!-- 多个组件被框选的对齐方式 -->
          <AliginSetting v-if="loaded && currentConfigType === 'group' && platform === 'pc'" />
        </template>
      </div>
      <!-- 语音指令配置 -->
      <InstructConfig v-if="loaded && showVoiceConfig" />
      <!-- 过滤器配置 -->
      <div
        class="filter-panel-wp"
        :class="{ '--hide': !drawerBtnState.filter.select }"
      >
        <FilterPanel v-if="drawerBtnState.filter.select" />
      </div>
      <!-- 错误监测列表 -->
      <!-- <div
        class="error-monitor-wp"
        :class="{ '--hide': !drawerBtnState.abnormal.select }"
      >
        <ErrorMonitor v-if="drawerBtnState.abnormal.select" />
      </div> -->
      <!-- 权限配置 -->
      <div
        class="permission-panel-wp"
        :class="{ '--hide': !drawerBtnState.permission.select }">
        <CompPermission v-if="drawerBtnState.permission.select" />
      </div>
      <!-- 公共数据管理 -->
      <div
        class="publicDataManage-panel-wp"
        :class="{ '--hide': !drawerBtnState.publicDataManage.select }">
        <PublicDataManage v-if="drawerBtnState.publicDataManage.select" />
      </div>
    </div>
    <!-- 发布弹框 -->
    <PublishScreen ref="pub" />
    <!-- 从仪表盘导入弹框 -->
    <ImportDialog ref="ipt" />
    <!-- 重命名弹框 -->
    <RenameDialog ref="rename" />
    <!-- 生成模版 -->
    <CreateScreenTpl ref="tpl" />
    <!-- 失效组件提醒 -->
    <InvalidComp
      ref="invalid"
      :nodes="failNodes"
      :layerTree="layerTree"
      @refresh="refreshLayers"
    />
    <!-- 共享弹窗 -->
    <ScreenShare ref="screenshare" />
    <!-- 终端交互 -->
    <TerminalDialog ref="terminal" :screenId="screenInfo.id" />
    <seatom-loading v-if="!loaded || editLoading"></seatom-loading>
    <!-- 协同场景所有页面被占用创建新页面 -->
    <SceneSelect
      v-if="showSceneSelect"
      @cancelScene="cancelScene"
      @sceneConfirm="sceneConfirm"
    />
    <!-- 海致通用AI对话框组件 -->
    <!-- <hz-ai-dialog
      action_route="seatom.create"
      welcome_text="欢迎使用AI助手，请对您的模型进行描述，我会帮您生成相应的模型"
      loading_text="模型生成中…"
      theme="dark"
    >
    </hz-ai-dialog> -->
    <hintsDialog ref="hints" />
  </div>
</template>

<script>
import Vue from 'vue';
import ConfigNode from '@/components/editor/ConfigNode';
import contextmenu from '@/lib/v-contextmenu/src/index';
import Tree from '@/lib/Tree';
import CallbackManager from '@/lib/CallbackManager'
import createSocket from '@/utils/coeditSocket'
import { mapState, mapGetters } from 'vuex';
import { openNewTab } from '@/utils/dom';
import { isPanel, uuid, replaceUrl, getFormData, createSocket as createLinkSocket } from '@/utils/base';
import { compact, bottom } from '@/utils/gridUtils'
import { find } from 'lodash'
import canvasBus from '@/utils/canvasBus';
import emitter from '@/utils/bus';
import Rect from '@/utils/Rect';
import '@/lib/v-contextmenu/src/styles/index.css';
import '@/components/plugins/shortcut';
import { screenCopy } from '@/api/screen';
import { commonUpload } from '@/api/common';
import { getData } from '@/api/datastorage';
// import 'hz-ai-dialog/lib/bundle.js';
// import 'hz-ai-dialog/lib/index.css';

Vue.use(contextmenu);
Vue.component(ConfigNode.name, ConfigNode);

export default {
  name: 'Editor',

  provide: function () {
    return {
      getLayerTree: () => this.layerTree, // 通过函数实现响应式
      screenInfo: this.screenInfo,
      screenComs: () => {},
      callbackManager: () => this.callbackManager,
      getScale: () => 1
    };
  },
  beforeRouteEnter (to, from, next) {
    const { params = {} } = from
    next((vm) => {
      vm.currWorkspaceId = params.workspaceId
    })
  },
  components: {
    LayerManager: () => import('@/components/editor/LayerManager'),
    CompPanel: () => import('@/components/editor/CompPanel'),
    ConfigManager: () => import('@/components/editor/ConfigManager'),
    CanvasMain: () => import('@/components/editor/canvas/CanvasMain'),
    ToolboxPanel: () => import('@/components/editor/ToolboxPanel'),
    ScenePage: () => import('@/components/editor/scene/ScenePage'),
    MobileSetting: () => import('@/components/editor/scene/MobileSetting'),
    SceneSelect: () => import('@/components/editor/scene/SceneSelect'),
    PageSetting: () => import('@/components/editor/PageSetting'),
    FilterPanel: () => import('@/components/editor/FilterPanel'),
    PublishScreen: () => import('@/components/editor/PublishScreen'),
    ImportDialog: () => import('@/components/editor/ImportDialog'),
    RenameDialog: () => import('@/components/editor/RenameDialog'),
    CreateScreenTpl: () => import('@/components/editor/CreateScreenTpl'),
    UpdatePanel: () => import('@/components/editor/UpdatePanel'),
    SkeletonLayoutPanel: () => import('@/components/editor/SkeletonLayoutPanel'),
    InvalidComp: () => import('@/components/editor/InvalidComp'),
    ScreenShare: () => import('@/components/editor/ScreenShare'),
    PublicDataManage: () => import('@/components/editor/PublicDataManage'),
    TerminalDialog: () => import('@/components/editor/TerminalDialog'),
    CompPermission: () => import('@/components/editor/data-source/CompPermission'),
    // SourceList: () => import('@/components/editor/SourceList'),
    AliginSetting: () => import('@/components/editor/AliginSetting'),
    InstructConfig: () => import('@/components/editor/voicecontrol/InstructConfig'),
    IndicatorList: () => import('@/components/editor/IndicatorList.vue'),
    HintsDialog: () => import('@/components/editor/HintsDialog'),
    // ErrorMonitor: () => import('@/components/editor/ErrorMonitor'),
    ChildScreens: () => import('@/components/editor/ChildScreens')
  },

  data () {
    this.socket = null
    return {
      layerTree: new Tree(),
      replaceUrl,
      screenName: '',
      editable: false,
      failNodes: [], // 失效组件
      showDetailError: false,
      coeditInfo: {},
      optionalPages: [], // 场景大屏可选择的页面列表
      showSceneSelect: false,
      isReturn: false, // 返回标识
      isOnScene: false, // 标识当前处于场景页面中
      currWorkspaceId: '' // 当前用户workspaceId
    };
  },

  computed: {
    ...mapState({
      screenLayers: (state) => state.editor.screenLayers,
      editPanelState: (state) => state.editor.editPanelState,
      drawerBtnState: (state) => state.editor.drawerBtnState,
      // ctxMenuState: state => state.editor.ctxMenuState,
      loaded: (state) => state.editor.loaded,
      editLoading: (state) => state.editor.editLoading,
      currentSelectId: (state) => state.editor.currentSelectId,
      copySelectId: (state) => state.editor.copySelectId,
      screenInfo: (state) => state.editor.screenInfo,
      sceneId: (state) => state.editor.sceneId,
      pageId: (state) => state.editor.pageId,
      menuPosition: (state) => state.editor.menuPosition,
      errorCompIds: (state) => state.editor.errorCompIds,
      screenComs: (state) => state.editor.screenComs,
      childScreenComs: state => state.editor.childScreenComs,
      coeditData: state => state.editor.coeditData,
      showVoiceConfig: state => state.editor.showVoiceConfig,
      snapshotIndex: state => state.editor.snapshotIndex
    }),
    ...mapGetters('editor', [
      'currentCom',
      'currentConfigType',
      'getComDataById',
      'drawerMask',
      'dataMappingErrorComps',
      'errorComps',
      'isSocketConnect',
      'cursnapshotData'
    ]),
    allErrors () {
      return this.errorComps.length + this.dataMappingErrorComps.length;
    },
    isScreentpl () {
      // 是否为模板
      return this.screenInfo.isScreentpl;
    },
    isDynamicScreen () {
      // 是否为动态面板创建的大屏
      return this.screenInfo.isDynamicScreen;
    },
    islocalLogin () {
      // 是否为本地登录
      return !!window.localStorage.getItem('localLogin')
    },
    globalBtns () {
      const btns = [
        { id: 'share', name: '同步', icon: 'sync' },
        { id: 'terminal', name: '智能操控', icon: 'terminal' },
        { id: 'template', name: '生成模板', icon: 'page-template' },
        { id: 'preview', name: '预览', icon: 'entity-view' },
        { id: 'publish', name: '发布', icon: 'publish' },
        { id: 'import', name: '从仪表盘导入', icon: 'import' },
        { id: 'export', name: '导出', icon: 'export' }
      ]
      if (this.screenInfo.screenType === 'child') {
        return [
          { id: 'preview', name: '预览', icon: 'entity-view' }
        ]
      }
      if (!this.islocalLogin) {
        return btns
      }
      const excludes = ['import'];
      return btns.filter(item => !excludes.includes(item.id))
    },
    // 是否小屏编辑
    isMobilePlatform () {
      return this.platform === 'mobile'
    },
    filterGlobalBtns () {
      if (this.isMobilePlatform) {
        let noKey = [];
        // 移动端动态面板编辑页 不显示的按钮
        if (this.isDynamicScreen) {
          noKey = ['template', 'preview', 'publish', 'terminal'];
        } else if (!this.isScreentpl && !this.isDynamicScreen) {
          noKey = ['terminal'];
        } else {
          noKey = ['template', 'publish', 'terminal']; // 模板大屏不显示的按钮
        }
        return this.globalBtns.filter((item) => !noKey.includes(item.id));
      } else {
        if (!this.isScreentpl && !this.isDynamicScreen) {
          return this.globalBtns;
        } else {
          const noKey = ['template', 'publish', 'terminal']; // 模板大屏不显示的按钮
          return this.globalBtns.filter((item) => !noKey.includes(item.id));
        }
      }
    },
    screenType () {
      return this.screenInfo.screenType;
    },
    platform () {
      return this.screenInfo.type;
    },
    // 是否是场景大屏
    isSceneScreen () {
      return this.screenInfo.screenType === 'scene'
    },
    // 是否是场景大屏并且是协同编辑
    isSceneAndCoedit () {
      return !!this.screenInfo.coeditId && this.screenInfo.screenType === 'scene'
    }
  },

  watch: {
    $route: 'fetchData',
    currentConfigType: {
      handler: function (val) {
        if (val !== 'com') {
          this.$store.commit('editor/updateCurrentChildId', null);
        }
      },
      immediate: true
    },
    screenLayers: {
      handler: function (layers) {
        this.layerTree = new Tree({ id: 'root', children: layers });
        if (this.currentCom) {
          this.layerTree.select(this.currentCom.id);
        }
      },
      immediate: true
    }
  },

  created () {
    this.fetchData();
    canvasBus.on('ctx_click', this.handleCtxClick);
    this.browserReturnListener()
  },

  mounted () {
    this.shortcut();
    emitter.on('hints', this.handlerHints)
  },

  beforeDestroy () {
    window.removeEventListener('popstate', this.headerReturn, false);
    this.$store.commit('editor/updateLoadState', false);
    this.$store.commit('editor/clearErrorInfo');
    this.$shortcut.unbind('⌘+c, ctrl+c');
    this.$shortcut.unbind('⌘+v, ctrl+v');
    this.$shortcut.unbind('backspace');
    canvasBus.off('ctx_click', this.handleCtxClick);
    emitter.off('hints', this.handlerHints)

    if (this.socket && this.socket.io && this.socket.io.off) {
      this.socket.io.off('reconnecting')
      this.socket.io.off('reconnect')
      this.socket.io.off('reconnect_failed')
      this.socket.io.off('reconnect_error')
    }
  },

  beforeRouteLeave (to, from, next) {
    this.$store.commit('editor/updateDrawerSelect', {
      type: 'permission',
      value: false
    });
    this.$store.commit('editor/updateDrawerSelect', {
      type: 'publicDataManage',
      value: false
    });
    this.$store.commit('editor/updateEditPanelSelect', {
      type: 'tool',
      value: false
    });
    next();
  },

  methods: {
    // 进入指标编辑页
    editIndicator (componentId) {
      const { id, screenType, coeditId } = this.screenInfo
      if (coeditId) {
        const userId = localStorage.getItem('userId');
        const userName = localStorage.getItem('name');
        if (screenType === 'scene') {
          const exitOptions = {
            screenId: id,
            userId: userId,
            userName: userName
          }
          if (!this.isOnScene) {
          // 当前在页面中
            exitOptions.pageId = this.pageId
          } else {
          // 当前在场景中
            exitOptions.sceneId = this.sceneId
          }
          // 场景大屏页面退出编辑状态
          this.socket.io.emit('exitScenePage', exitOptions, res => {
            if (res.success) {
            // 退出页面成功 =》 场景大屏退出编辑状态
              this.socket.io.emit('exitScreen', {
                screenId: id,
                screenType: screenType,
                userId: userId,
                userName: userName
              }, (res) => {
                if (res.success) {
                // 退出成功
                  this.$router.push({
                    path: `/screen/indicator/edit/${componentId}`,
                    query: {}
                  })
                } else {
                }
              })
            } else {
            // 退出页面失败
            }
          })
        } else {
          // 普通协同大屏退出
          // 退出页面成功 =》 场景大屏退出编辑状态
          this.socket.io.emit('exitScreen', {
            screenId: id,
            screenType: screenType,
            userId: userId,
            userName: userName
          }, (res) => {
            if (res.success) {
              // 退出成功
              this.$router.push({
                path: `/screen/indicator/edit/${componentId}`,
                query: {}
              })
            }
          })
        }
      } else {
        this.$router.push({
          path: `/screen/indicator/edit/${componentId}`,
          query: {}
        })
      }
    },
    // 协同场景新增场景/页面完成
    editEnd () {
      const { id = '', coeditId = '' } = this.screenInfo
      if (!coeditId) return;
      const userId = localStorage.getItem('userId');
      const userName = localStorage.getItem('name');
      // 进入新页面
      this.socket.io.emit('joinScenePage', {
        screenId: id,
        userId: userId,
        userName: userName,
        sceneId: this.sceneId,
        pageId: this.pageId
      }, (res) => {
        if (res.success) {
        } else {
          this.$message.warn(res.message)
        }
      })
    },
    // 监听浏览器返回
    browserReturnListener () {
      window.addEventListener('popstate', this.headerReturn, false); ;
    },
    // 场景切换退出协同锁
    sceneChange () {
      const userId = localStorage.getItem('userId');
      const userName = localStorage.getItem('name');
      this.socket.io.emit('joinScenePage', {
        screenId: this.screenInfo.id,
        userId: userId,
        userName: userName,
        sceneId: this.sceneId
      }, res => {
        if (res.success) {
          this.isOnScene = true
        } else {
          // 退出页面失败

        }
      })
    },
    // 页面切换控制协同锁
    pageChange (page) {
      const nextPageId = page.pageId // 点击切换的页面id
      const { id = '' } = this.screenInfo
      const userId = localStorage.getItem('userId');
      const userName = localStorage.getItem('name');

      // 退出当前页面，进入下一页面
      this.socket.io.emit('joinScenePage', {
        screenId: id,
        userId: userId,
        userName: userName,
        sceneId: this.sceneId,
        pageId: nextPageId
      }, (res) => {
        if (res.success) {
          this.isOnScene = false
          // 进入下个屏成功
          this.$store.commit('editor/updatePageId', page.pageId);
        } else {
          // 进入下个屏失败
          this.$message.warn(res.message)
        }
      })
    },
    cancelScene () {
      const { id, screenType } = this.screenInfo
      const userId = localStorage.getItem('userId');
      const userName = localStorage.getItem('name');
      this.showSceneSelect = false
      this.socket.io.emit('exitScreen', {
        screenId: id,
        screenType: screenType,
        userId: userId,
        userName: userName
      }, (res) => {
        if (res.success) {
          // 退出成功
          this.$router.push({
            path: `/workspace/${this.currWorkspaceId || this.screenInfo.workspaceId}`
          });
        } else {
        }
      })
    },
    sceneConfirm (sceneId) {
      this.$store.commit('editor/updateSceneId', sceneId);
      this.$refs.scenePageRef && this.$refs.scenePageRef.addPage()
      this.showSceneSelect = false
    },
    // 后端校验是否允许进入页面
    enterScreenCheck () {
      const { id, screenType, isCollaborators } = this.screenInfo
      const userId = localStorage.getItem('userId');
      const userName = localStorage.getItem('name');
      if (!this.isSocketConnect && isCollaborators) {
        this.$message.error('创建协同连接失败，请稍后再试')
        window.history.back()
        return
      }

      // 协同大屏进入，后端校验状态
      this.socket.io.emit('joinScreen', {
        screenId: id,
        screenType: screenType,
        userId: userId,
        userName: userName
      }, (res) => {
        if (res.success) {
          // 成功则进入
        } else {
          if (screenType !== 'scene') {
            // 非场景大屏返回首页
            const { coeditUsers = [] } = res
            const coeditUsersObj = coeditUsers[0]
            const message = `${coeditUsersObj.userName}编辑中，不允许同时编辑同一页面`
            this.$message.warn(message)
            this.$router.push({
              path: `/workspace/${this.currWorkspaceId || this.screenInfo.workspaceId}`
            });
          }
        }
      })

      // 场景大屏协同需先判断当前页面占用情况
      if (screenType === 'scene') {
        this.socket.emitter.addListener('screenCoeditInfo', (coeditData = {}) => {
          this.$store.commit('editor/updateCoeditData', coeditData)
          if (this.isReturn || this.isOnScene) return
          const { sceneConfig = [], id = '' } = this.screenInfo
          const userId = localStorage.getItem('userId');
          const userName = localStorage.getItem('name');

          for (let i = 0; i < coeditData.coeditUsers.length; i++) {
            // 初始化时当前用户如果在页面内则不需要再次进入
            if (coeditData.coeditUsers[i].userId === userId) {
              return
            }
          }

          if (this.checkPageStatus({ sceneConfig, coeditData, id })) {
            // 还有剩余的，分配剩余页面
            const scene = this.optionalPages[0]
            this.socket.io.emit('joinScenePage', {
              screenId: id,
              userId: userId,
              userName: userName,
              sceneId: scene.sceneId,
              pageId: scene.pageId
            }, (res) => {
              if (res.success) {
                this.$store.commit('editor/updateSceneId', scene.sceneId);
                this.$store.commit('editor/updatePageId', scene.pageId);
              } else {
                this.$message.warn(res.message)
              }
            })
          } else {
          // 全部被占用
            this.showSceneSelect = true
          }
        }, this)
      }
    },
    getScenePageList (sceneConfig = []) {
      const scenePages = sceneConfig.map(scene => {
        return scene.pageList.map(item => {
          return {
            pageId: item.pageId,
            sceneId: scene.sceneId
          }
        })
      })
      return scenePages.flat()
    },
    // 校验场景大屏页面占用状态
    checkPageStatus ({ sceneConfig = [], coeditData = {}, id = '' }) {
      const currentCoeditObj = coeditData?.coeditUsers
      if (currentCoeditObj) {
        const scenePagesList = this.getScenePageList(sceneConfig)
        this.optionalPages = scenePagesList.filter(item => {
          return !find(currentCoeditObj, ['pageId', item.pageId])
        })
        return this.optionalPages.length !== 0
      }
    },
    // 判断当前是否存在全局socket实例
    handleSocketCheck () {
      // 协同大屏校验状态
      if (this.$coeditSocket) {
        this.socket = this.$coeditSocket
        this.handleSocketReconnect()
        this.enterScreenCheck()
      } else {
        createSocket().then(async () => {
          this.socket = this.$coeditSocket
          this.handleSocketReconnect()
          this.enterScreenCheck()
        })
      }
    },
    // 成功重新连接后激发
    handleSocketReconnect () {
      /* eslint-disable*/
      // 重连中
      this.socket.io.on('reconnecting', (e) => {
        // console.info('reconnecting', e);
      })

      // 成功重新连接后激发
      this.socket.io.on('reconnect', (e) => {
        // console.info('reconnect', e);
        this.enterScreenCheck()
      })

      // 重连失败
      this.socket.io.on('reconnect_failed', (e) => {
        // console.info('reconnect_failed', e);
      })

      // 重连错误
      this.socket.io.on('reconnect_error', (e) => {
        // console.info('reconnect_error', e);
      })
    },
    fetchData () {
      const params = {
        screenId: this.$route.params.screenId
      };
      this.$store.commit('editor/updateLoadState', false);
      this.$store.dispatch('editor/initScreenAction', params).then(() => {
        const { coeditId = '' } = this.screenInfo
        if (coeditId) {
          this.handleSocketCheck()
        }
        this.layerTree = new Tree({ id: 'root', children: this.screenLayers });
        this.$store.commit('editor/updateLoadState', true);
        this.screenName = this.screenInfo.name;
        // 失效组件提醒
        const nodesMap = _.pickBy(this.layerTree.nodesMap, node => (!!node.data.type && node.data.type !== 'group'));
        _.keys(nodesMap).forEach(id => {
          if (!this.getComDataById(id)) {
            this.failNodes.push(nodesMap[id].data);
          }
        });
        if (this.failNodes.length) {
          this.$refs.invalid && this.$refs.invalid.showDialog();
        }

        this.initCallbackManager();
        const socket = Vue.prototype.$socket = createLinkSocket(this.screenInfo.parentId || this.screenInfo.id)
        socket.emitter.addListener('linkage', e => {
          const { currId, type } = e
          if (currId === this.screenInfo.id) return true; // 自己发的事件，不处理
          if (type === 'update') {
            this.$store.dispatch('editor/getChildScreens')
            this.$store.dispatch('editor/getParentScreen')
          }
        }, this)
      });
    },
    handleEditBtnClick (btn) {
      if (btn.type === 'tool' && this.isMobilePlatform) {
        this.$message.warn("移动端大屏暂不支持指标库功能，请使用pc端大屏！")
        return false
      } 
      this.$store.commit('editor/updateEditPanelSelect', {
        type: btn.type,
        value: !btn.select
      });
    },
    handleDrawerBtnClick (btn) {
      const { screenType } = this.screenInfo
      const { coeditUsers = [] } = this.coeditData
      const { type } = btn
      if (screenType === 'scene' && coeditUsers.length > 1 && type === 'update') {
        // 协同场景大屏不允许批量更新,单人可以
        return this.$message.warn('多人编辑时，该功能暂时禁用')
      }
      this.$store.commit('editor/updateDrawerSelect', {
        type: btn.type,
        value: !btn.select
      });
    },
    handleDrawerMaskClick () {
      this.$store.commit('editor/updateDrawerSelectAllFalse');
    },
    hanldeGlobalBtnClick (btn, previewLoading = true) {
      let url;
      switch (btn.id) {
        case 'template':
          this.$refs.tpl.showTpl(this.screenInfo);
          break;
        case 'export':
          this.exportScreen();
          break;
        case 'publish':
          this.$refs.pub.showPublish(this.$route.params.screenId);
          break;
        case 'import':
          this.$refs.ipt.showImport(this.$route.params.screenId);
          break;
        case 'preview':
          if (this.isMobilePlatform) {
            url = `/screen/mobile-preview/${this.$route.params.screenId}?loading=${previewLoading}`;
          } else {
            url = `/preview/index.html#/${this.$route.params.screenId}?loading=${previewLoading}`;
          }
          openNewTab(url);
          break;
        case 'terminal':
          this.$refs.terminal.showDialog();
          break;
        case 'share':
          this.$refs.screenshare.showDialog();
          break;
        default:
          break;
      }
    },
    openPublicDataManage () {
      this.$refs.dataManage.showDialog();
    },
    findNodeOfGroup (node) {
      if (!node.children) return;
      node.children.forEach((item) => {
        if (item.children) {
          this.findNodeOfGroup(item);
        } else {
          if (!this.nodeOfGroup) this.nodeOfGroup = [];
          this.nodeOfGroup.push(item.data.id);
        }
      });
    },
    // 右键菜单点击事件
    handleCtxClick (key) {
      const { layerTree } = this;
      let selectedNodes = layerTree.getSelectedNodes();

      // 过滤数据，排除场景大屏可能会选到其他页面的图层，导致shift多选后删除、复制等操作错误
      if (this.isSceneScreen) {
        selectedNodes = selectedNodes.filter((node) => {
          if (this.pageId) {
            return this.pageId === node.data.pageId
          } else if (this.sceneId) {
            return this.sceneId === node.data.sceneId && !node.data.pageId
          }
          return false
        })
      }

      const selectedIds = selectedNodes.map((n) => n.data.id);
      let useToShowOrLock = selectedNodes.map((n) => {
        if (n.data.type !== 'group') return n.data.id;
        else {
          this.nodeOfGroup = [];
          this.findNodeOfGroup(n);
          return this.nodeOfGroup;
        }
      });
      /* 用于显示锁定图层数据 */
      useToShowOrLock = useToShowOrLock.flat();
      const soloShowCompList = Object.values(this.screenComs);

      let successCallback

      switch (key) {
        case 'top':
          layerTree.bringToTop(selectedIds);
          this.saveTree();

          successCallback = () => {
            this.$message.success('置顶成功');
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/moveScreenLayerByIds', {
                layerIds: selectedIds,
                action: 'bringToTop'
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }


          break;
        case 'bottom':
          layerTree.bringToBottom(selectedIds);
          this.saveTree();

          successCallback = () => {
            this.$message.success('置底成功');
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/moveScreenLayerByIds', {
                layerIds: selectedIds,
                action: 'bringToBottom'
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }

          break;
        case 'moveUp':
          layerTree.moveUp(selectedIds);
          this.saveTree();

          successCallback = () => {
            this.$message.success('上移成功');
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/moveScreenLayerByIds', {
                layerIds: selectedIds,
                action: 'moveUp'
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }

          break;
        case 'moveDown':
          layerTree.moveDown(selectedIds);
          this.saveTree();

          successCallback = () => {
            this.$message.success('下移成功');
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/moveScreenLayerByIds', {
                layerIds: selectedIds,
                action: 'moveDown'
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }

          break;
        case 'show':
          this.$store.dispatch(
            'editor/updateScreenCom',
            useToShowOrLock.map((id) => ({
              id,
              keyValPairs: [{ key: 'show', value: true }]
            }))
          );
          break;
        case 'hide':
          this.$store
            .dispatch(
              'editor/updateScreenCom',
              useToShowOrLock.map((id) => ({
                id,
                keyValPairs: [{ key: 'show', value: false }]
              }))
            )
            .then(() => {
              this.$message.success('隐藏成功');
            });
          break;
        case 'lock':
          this.$store
            .dispatch(
              'editor/updateScreenCom',
              useToShowOrLock.map((id) => ({
                id,
                keyValPairs: [{ key: 'attr.lock', value: true }]
              }))
            )
            .then(() => {
              this.$message.success('锁定成功');
            });
          break;
        case 'unLock':
          this.$store
            .dispatch(
              'editor/updateScreenCom',
              useToShowOrLock.map((id) => ({
                id,
                keyValPairs: [{ key: 'attr.lock', value: false }]
              }))
            )
            .then(() => {
              this.$message.success('解锁成功');
            });
          break;
        case 'group':
          this.group(selectedNodes, useToShowOrLock);
          break;
        case 'cancelGroup':
          this.cancelGroup(selectedNodes);
          break;
        case 'rename':
          this.$refs.rename.showDialog();
          break;
        case 'copy':
          this.copy();
          break;
        case 'paste':
          this.paste(this.isMobilePlatform ? false : true);
          break;
        case 'delete':
          this.delete(selectedIds, useToShowOrLock);
          break;
        case 'soloShow':
          this.$store.dispatch(
            'editor/updateScreenCom',
            soloShowCompList.map((comp) => {
              if (useToShowOrLock.includes(comp.id)) {
                return {
                  id: comp.id,
                  keyValPairs: [{ key: 'show', value: true }]
                };
              } else {
                return {
                  id: comp.id,
                  keyValPairs: [{ key: 'show', value: false }]
                };
              }
            })
          );
          break;
        case 'closeSoloShow':
          this.$store.dispatch(
            'editor/updateScreenCom',
            soloShowCompList.map((comp) => {
              return {
                id: comp.id,
                keyValPairs: [{ key: 'show', value: true }]
              };
            })
          );
          break;
      }
    },
    delete (selectedIds, compIds) {
      if (selectedIds.some(id => id.startsWith('interaction-container-modulepanel'))) { // 防止模块组件被删掉
        return;
      }
      if (_.isNil(selectedIds) || !selectedIds.length) return;
      const alias = compIds.map((id) => {
        const comData = this.getComDataById(id);
        return comData ? comData.alias : '';
      });
      const text = `删除后无法恢复，是否删除"${alias.join('，')}"共${
        compIds.length // selectedIds.length
      }个组件`;
      this.$confirm(text, '', {
        confirmButtonText: '确定(Enter)',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          const originLayers = _.cloneDeep(this.layerTree.data.children)
          this.layerTree.delete(selectedIds);
          this.saveTree();

          const successCallback = () => {
            this.recoverTree();
          }

          if (this.isSceneAndCoedit) {
            this.$store
              .dispatch('editor/deleteScreenLayerByIds', {
                layerIds: selectedIds
              })
              .then(() => {
                successCallback()
              })
          } else {
            this.$store
              .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
              .then(() => {
                successCallback()
              });
          }

          this.$store
            .dispatch('editor/deleteComponent', compIds) // selectedIds)
            .then(() => {
              this.recordSnapshot(compIds, originLayers)
                Vue.prototype.$socket && Vue.prototype.$socket.io.emit('linkage', {
                  room: this.screenInfo.parentId || this.screenInfo.id,
                  msg: {
                    type: 'update',
                    currId: this.screenInfo.id
                  }
                })
              this.$message.success('删除成功');
            });
        })
        .catch(() => {});
    },
    recordSnapshot (compIds, originLayers) {
      /**保存一下删除的初始状态 */
      // const updateParams = compIds.map(id => {
      //   return {
      //     id,
      //     keyValPairs: [
      //       { key: 'isDelete', value: false }
      //     ]
      //   }
      // })
      // this.$store.commit('editor/recordSnapshot', {
      //   type: 'delete',
      //   data: updateParams
      // })
      /** */
      const snapeshotParams = compIds.map(id => {
        return {
          id,
          keyValPairs: [
            { key: 'isDelete', value: false }
          ]
        }
      })
      this.$store.commit('editor/recordSnapshot', {
        type: 'delete',
        data: snapeshotParams,
        originLayers,
        layers: this.layerTree.data.children || []
      })
    },
    convertHtmlToString (html) {
      if (!html) return html;
      const htmlDom = document.createElement('html');
      htmlDom.innerHTML = html;
      return htmlDom.textContent || htmlDom.innerText || '';
    },
    handleAfterCreate ({ id, name }) {
      if (name === 'data-text-normal') {
        // 文本组件
        this.$store
          .dispatch('editor/updateScreenCom', {
            id,
            keyValPairs: [
              {
                key: 'staticData',
                value: [{ text: this.clipboardData }]
              }
            ]
          })
          .then(async () => {
            try {
              const res = await getData({
                componentId: id,
                type: 'static',
                workspaceId: this.currWorkspaceId || this.screenInfo.workspaceId
              });
              this.$store.commit('editor/updateComData', {
                componentId: id,
                data: res.data
              });
              emitter.off('after_createCom', this.handleAfterCreate);
              this.$message.success('粘贴成功');
            } catch (e) {
              emitter.off('after_createCom', this.handleAfterCreate);
              console.warn(e);
            }
          });
      } else if (name === 'media-image-normal') {
        // 图片组件
        const formData = getFormData(this.clipboardData);
        commonUpload(formData)
          .then((res) => {
            if (res.success && res.data) {
              const imageUrl = replaceUrl(process.env.VUE_APP_SERVER_URL + res.data.url);
              this.$store
                .dispatch('editor/updateScreenCom', {
                  id,
                  keyValPairs: [
                    {
                      key: 'config.url',
                      value: imageUrl
                    }
                  ]
                })
                .then(async () => {
                  // try {
                  //   const res = await getData({
                  //     componentId: id,
                  //     type: 'static',
                  //     workspaceId: this.currWorkspaceId || this.screenInfo.workspaceId
                  //   });
                  //   this.$store.commit('editor/updateComData', {
                  //     componentId: id,
                  //     data: res.data
                  //   });
                  // } catch (e) {
                  //   console.warn(e);
                  // }
                });
              emitter.off('after_createCom', this.handleAfterCreate);
              this.$message.success('粘贴成功');
            }
          })
          .catch((e) => {
            emitter.off('after_createCom', this.handleAfterCreate);
            console.warn(e);
          });
      }
    },
    createCom (type, isMouse) {
      const com = this.$refs.compPanel
        ? this.$refs.compPanel.findComByName(type)
        : null;
      let pos = null;
      if (com) {
        if (isMouse) {
          pos = {
            x: this.menuPosition.x,
            y: this.menuPosition.y
          };
        }
        this.$refs.compPanel.handleItemClick(com, pos);
        emitter.on('after_createCom', this.handleAfterCreate);
      }
    },
    async paste (isMouse) {
      if (navigator.clipboard) {
        try {
          const data = await navigator.clipboard.read();
          const type = data[0].types[data[0].types.length - 1];
          this.clipboardData = null;
          const reader = new FileReader();
          reader.onload = () => {
            this.clipboardData = reader.result;
            if (type === 'text/plain') {
              this.createCom('data-text-normal', isMouse);
            } else if (type === 'text/html') {
              // 将html类型内容转为字符串
              this.clipboardData = this.convertHtmlToString(reader.result);
              this.createCom('data-text-normal', isMouse);
            } else if (type === 'image/png') {
              this.createCom('media-image-normal', isMouse);
            }
          };
          if (type === 'text/html') {
            const content = await data[0].getType('text/html');
            reader.readAsText(content);
          } else if (type === 'text/plain') {
            const content = await data[0].getType('text/plain');
            reader.readAsText(content);
          } else if (type === 'image/png') {
            const content = await data[0].getType('image/png');
            reader.readAsDataURL(content);
          } else {
            console.warn('暂时处理不了' + type + '类型的内容！');
          }
        } catch (e) {
          this.pasteCom(isMouse);
        }
      } else {
        this.pasteCom(isMouse);
      }
    },
    async pasteCom (isMouse) {
      const copyObj = JSON.parse(localStorage.getItem('copyCom'));
      // 剪贴板为空，走组件粘贴逻辑
      if (_.isNil(copyObj)) {
        this.$message.error('请选择组件');
        return;
      }

      const platform = copyObj.platform;
      if (platform !== this.platform) {
        // pc端/移动端不能互相复制粘贴
        this.$message.error('pc端与移动端不能互相复制组件！');
        return;
      }

      const originCom = copyObj.originCom;
      if (!originCom.length) {
        this.$message.error('请选择组件');
        return;
      }
      // 如果是子面板 禁止复制面板类组件
      if (this.isDynamicScreen) {
        const index = originCom.findIndex(item => isPanel(item.comName));
        if (index > -1) {
          this.$message.error('子面板不允许复制面板类组件！');
          return
        }
      }

      let originRect = null
      let lastComBottom = 0

      if (!this.isMobilePlatform) {
        // 获取pc端位置
        originRect = new Rect(
          originCom.map((com) => ({
            x: com.attr.x,
            y: com.attr.y,
            w: com.attr.w,
            h: com.attr.h
          }))
        );
      } else {
        // 获取移动端排在最底组件y轴
        for (const key in this.screenComs) {
          const com = this.screenComs[key];
          lastComBottom = Math.max(lastComBottom, com.attr.y + com.attr.h)
        }
      }

      const copyData = originCom.map((com) => {
        let offsetX = 20;
        let offsetY = 20;
        if (isMouse && originRect) {
          offsetX = this.menuPosition.x - originRect.x;
          offsetY = this.menuPosition.y - originRect.y;
        }

        const comData = {
          originId: com.id,
          newData: {
            id: uuid(com.comName),
            name: com.comName,
            version: com.version,
            attr: this.isMobilePlatform ? {
              x: com.attr.x,
              y: lastComBottom
             } : {
              x: Math.floor(com.attr.x + offsetX),
              y: Math.floor(com.attr.y + offsetY)
            }
          }
        };
        if (this.screenType === 'scene') {
          comData.newData.pageId = this.pageId;
          comData.newData.sceneId = this.sceneId;
        }
        // 更新移动端关联图层分组id
        if (com.comType === 'interaction-container-modulepanel') {
          comData.newData.config = {
            groupId: 'groups_' + comData.newData.id
          }
        }
        return comData;
      });
      const res = await this.$store.dispatch(
        'editor/copyScreenComp',
        copyData
      );
      if (res) {
        await this.$store.dispatch('editor/getScreenFilters', {
          screenId: this.screenInfo.id
        });
        // 提取出复制成功的组件
        const newComData = copyData
          .map((cp) => this.getComDataById(cp.newData.id))
          .filter((d) => !_.isNil(d));
        if (newComData.length) {
          const haveGroup = localStorage.getItem('copyTree');
          let layers = []
          if (typeof JSON.parse(haveGroup) === 'string') {
            layers = newComData.map((d) => {
              const layer = {
                id: d.id,
                type: d.type
              };
              if (this.screenType === 'scene') {
                layer.pageId = this.pageId;
                layer.sceneId = this.sceneId;
              }
              return layer;
            })
            this.layerTree.addChild(layers);
          } else {
            const addChild = _.cloneDeep(JSON.parse(haveGroup));
            this.changeCopyTreeId(addChild, newComData);
            this.compIndex = 0;
            layers = addChild.map((d) => {
              const layer = {
                id: d.id,
                type: d.type
              };
              if (this.screenType === 'scene') {
                layer.pageId = this.pageId;
                layer.sceneId = this.sceneId;
              }
              if (d.data.type === 'group') {
                layer.groupName = d.data.groupName;

                // 获取分组的children数据，排除data字段，因为没用到，冗余
                const getChildren = (list = []) => {
                  const newChildren = []
                  for (const item of list) {

                    const newItem = {
                      ...item
                    }
                    delete newItem.data

                     if (newItem.children) {
                      newItem.children = getChildren(newItem.children)
                    }

                    newChildren.push(newItem)
                  }
                  return newChildren
                }

                layer.children = getChildren(d.children);

                // 处理移动端分组comId
                if (this.isMobilePlatform) {
                  layer.comId = d.comId
                }
              }
              return layer;
            })
            this.layerTree.addChild(layers);
          }
          this.saveTree();

          if (this.isSceneAndCoedit) {
            await this.$store
              .dispatch('editor/insertScreenLayers', {
                screenId: this.screenInfo.id,
                layers: layers
              })
          } else {
            await this.$store
              .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
          }

          // const ids = newComData.map(d => d.id);
          // this.layerTree.select(ids);
          // canvasBus.emit('select_node', ids);
          this.$message.success('粘贴成功');
          this.recoverTree();
        } else {
          this.$message.error('粘贴失败');
        }
      }
    },
    async group (selectedNodes, useToShowOrLock) {
      const hasScene = selectedNodes.some((node) => {
        return !node.data.pageId
      })
      const hasScenePage = selectedNodes.some((node) => {
        return !!node.data.pageId
      })
      if (hasScene && hasScenePage) {
        this.$message.warn('不同页面组件不支持分组')
        return
      }

      /** 清除失效的模块组件 */
      const layers = this.layerTree.data.children;
      const clearModules = layers.filter(item => {
        if (item.id.startsWith('interaction-container-modulepanel')) {
          const com = this.getComDataById(item.id);
          if (com) {
            const groupId = com.config.groupId;
            const group = this.layerTree.getNodeById(groupId);
            if (!group) {
              return true
            }
          }
        }
        return false
      })
      if (clearModules.length) {
        const selectedIds = clearModules.map(item => item.id);
        this.layerTree.delete(selectedIds);
      }
      /** 清除失效的模块组件 ===end=== */

      const group = selectedNodes.map((item) => item.data);
      let allowGroup = true;
      group.forEach((item) => {
        if (
          (item.groupName && !item.children) ||
          new Set(useToShowOrLock).size !== useToShowOrLock.length
        ) { allowGroup = false; }
      });
      if (allowGroup) {
        let options;
        if (this.isMobilePlatform) { // 移动端分组特殊处理
          let coms = group.map(item => {
            if (item.type === 'com') {
              const com = this.getComDataById(item.id);
              return com
            }
            return this.getComDataById(item.comId)
          })

          coms = _.unionBy(coms, 'id'); // 根据id去重

          const layout = coms.map(item => {
            const attr = item.attr;
            return {
              x: attr.x,
              y: attr.y,
              w: attr.w,
              h: attr.h,
              i: item.id,
              static: attr.lock
            }
          })
          const compactLayout = compact(layout, true);

          const firstCom = coms[0];
          const gridHeight = _.ceil((bottom(compactLayout) * 15 + 10) / 15, 2);
          const moduleId = uuid('interaction-container-modulepanel');
          const comData = { // 创建分组前，先创建模块组件
            id: moduleId,
            name: 'interaction-container-modulepanel',
            version: '4.0.0',
            attr: {
              x: firstCom.attr.x,
              y: firstCom.attr.y,
              w: 24,
              h: gridHeight
            }
          }
          if (this.screenType === 'scene') {
            comData.sceneId = this.sceneId;
            comData.pageId = this.pageId;
          }
          const res = await this.$store.dispatch('editor/createScreenComp', comData);
          if (res.success) {
            const _groupId = `groups_${+new Date()}`;
            const newData = this.getComDataById(comData.id)
            const layer = {
              id: newData.id,
              type: newData.type
            }
            this.layerTree.addChild(layer);
            options = {
              id: _groupId,
              comId: newData.id
            }
            await this.$store.dispatch('editor/updateScreenCom', {
              id: newData.id,
              keyValPairs: [
                { key: 'config.groupId', value: _groupId }
              ]
            })
          }
        }
        const groupNode = this.layerTree.createGroup(group, options);
        this.saveTree();

        const successCallback = () => {
          this.$message.success('分组成功');
          this.recoverTree();
        }

        if (this.isSceneAndCoedit) {
          // 移动端屏不会进入该判断
          this.$store
            .dispatch('editor/createLayerGroups', {
              screenId: this.screenInfo.id,
              groups: [groupNode.toJson()]
            })
            .then(() => {
              successCallback()
            })
        } else {
          this.$store
            .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
            .then(() => {
              successCallback()
            });
        }

      } else {
        canvasBus.emit('select_node', null);
        this.layerTree.clearSelect();
        this.$message.warn('请选中图层后进行分组操作！');
      }
    },
    formatCopyrTree (nodes) {
      nodes.forEach((node) => {
        delete node.tree;
        delete node.parent;
        if (node.children) this.formatCopyrTree(node.children);
      });
    },
    /* 存储图层右键操作前的树结构 */
    saveTree () {
      const copyTree = _.cloneDeep(Array.from(this.layerTree.loadedNodes || []));
      copyTree.forEach((node) => {
        delete node.tree;
        delete node.parent;
        if (node.children && node.children.length !== 0) { this.formatCopyrTree(node.children); }
      });
      for (let i = copyTree.length - 1; i >= 0; i--) {
        if (
          copyTree[i].data.type === 'group' &&
          copyTree[i].children && copyTree[i].children.length === 0
        ) {
          this.layerTree.delete(copyTree[i].data.id);
        }
      }
      localStorage.setItem('originTree', JSON.stringify(copyTree));
    },
    /* 对图层右键操作后不改变原有树文件夹的展开收起情况 */
    recoverTree () {
      let originTree = localStorage.getItem('originTree');
      originTree = JSON.parse(originTree);
      let i = 0;
      let j = 0;
      while (i < originTree.length && j < this.layerTree.loadedNodes.length) {
        const tree = this.layerTree.loadedNodes;
        if (
          tree[j].data.id === originTree[i].data.id &&
          tree[j].data.type === 'group' &&
          originTree[i].collapse === false
        ) {
          tree[j].collapse = originTree[i].collapse;
          this.layerTree.collapseChildNodes(tree[j].data, tree[j].collapse);
          this.recursionChangeCollapse(
            tree[j].children,
            originTree[i].children
          );
          i++;
          j++;
        } else if (tree[j].data.id !== originTree[i].data.id) {
          j++;
        } else if (tree[j].data.id === originTree[i].data.id) {
          i++;
          j++;
        }
      }
    },
    recursionChangeCollapse (tree, originTree) {
      tree.forEach((node, index) => {
        node.collapse = originTree[index].collapse;
        this.layerTree.collapseChildNodes(node.data, node.collapse);
        if (node.children) {
          this.recursionChangeCollapse(
            node.children,
            originTree[index].children
          );
        }
      });
    },
    async cancelGroup (selectedNodes) {
      const nodes = selectedNodes.map((item) => item.data);
      const groups = nodes.filter(item => item.type === 'group');

      const clearModules = [];

      for (const group of groups) {
        await this.layerTree.cancelGroup(group);
        if (this.isMobilePlatform) {
          clearModules.push({ id: group.comId });
        }
      }

      if (clearModules.length) { // 清除没用的模块组件
        const selectedIds = clearModules.map(item => item.id);
        await this.layerTree.delete(selectedIds);
        await this.$store.dispatch('editor/deleteComponent', selectedIds);
      }
      this.saveTree();

      const successCallback = () => {
        this.$message.success('取消分组成功');
        this.recoverTree();
      }

      if (this.isSceneAndCoedit) {
        this.$store
          .dispatch('editor/cancelLayerGroups', {
            screenId: this.screenInfo.id,
            layerGroupIds: groups.map((item) => {
              return item.id
            })
          })
          .then(() => {
            successCallback()
          })
      } else {
        this.$store
          .dispatch('editor/updateScreenLayers', this.layerTree.data.children)
          .then(() => {
            successCallback()
          });
      }
    },
    copy () {
      let copyIds = this.currentSelectId;
      if (_.isNil(copyIds)) {
        this.$message.error('请选择组件');
        return;
      }
      if (!_.isArray(copyIds)) {
        copyIds = [copyIds];
      }
      const originCom = copyIds
        .map((id) => this.getComDataById(id))
        .filter((d) => !_.isNil(d));
      const copyObj = {
        platform: this.platform,
        originCom
      };
      // 复制中包含分组的情况下存储tree结构
      const { layerTree } = this;
      const selectedNodes = layerTree.getSelectedNodes();
      if (selectedNodes.length) {
        let judgeCopyTree = false;
        for (let i = 0; i < selectedNodes.length; i++) {
          if (selectedNodes[i].data.type === 'group') {
            judgeCopyTree = true;
            break;
          }
        }

        // 判断是否存在分组
        if (judgeCopyTree) {
          // 处理移动端、并且有分组的复制
          if (this.isMobilePlatform) {
            // 找到分组对应的组件id 和 分组下的 组件id
            const getGroupAndChildComIds = (nodes, comIds = []) => {
              for (const node of nodes) {
                comIds.push(node.data.type === "group" ? node.data.comId : node.data.id)

                if (node.children) {
                  getGroupAndChildComIds(node.children, comIds)
                }
              }
              return comIds
            }

            const comIds = getGroupAndChildComIds(selectedNodes)
            // 设置要复制的组件列表
            copyObj.originCom = comIds.map((id) => this.getComDataById(id)).filter((d) => !_.isNil(d));
          }

          const list = _.cloneDeep(selectedNodes);
          this.deleteParent(list);
          localStorage.setItem('copyTree', JSON.stringify(list));
        } else {
          localStorage.setItem('copyTree', JSON.stringify(''));
        }
      }

      localStorage.setItem('copyCom', JSON.stringify(copyObj));
      this.$message.success('复制成功');
      if (navigator.clipboard) {
        // 此处清空剪贴板内容，防止与组件复制冲突
        navigator.clipboard.writeText('');
        this.clipboardData = '';
      }
    },
    deleteParent (node) {
      node.map((item) => {
        if (item.parent) {
          delete item.parent;
          delete item.tree;
        }
        if (item.children) this.deleteParent(item.children);
      });
    },
    /* 递归修改复制的分组id */
    changeCopyTreeId (node, newComData) {
      if (!this.compIndex) {
        this.compIndex = 0;
      }
      node.map((item) => {
        if (item.data.type === 'group') {
          // 区分小屏移动端分组 和 大屏分组
          if (this.isMobilePlatform) {
            item.data.id = 'groups_' + newComData[this.compIndex].id
            item.data.comId = newComData[this.compIndex].id
            item.comId = newComData[this.compIndex].id
            this.compIndex++
          } else {
            const id = item.data.id.split('_');
            item.data.id = uuid(id[0]);
          }
        } else {
          item.data.id = newComData[this.compIndex++].id;
        }
        if (item.data.groupName) {
          item.groupName = item.data.groupName;
        }
        if (this.screenType === 'scene') {
          item.sceneId = this.sceneId;
          item.pageId = this.pageId;
        }
        item.id = item.data.id;
        item.type = item.data.type;
        delete item.collapse;
        delete item.depth;
        delete item.loaded;
        delete item.selected;
        if (item.children) {
          this.changeCopyTreeId(item.children, newComData);
        }
      });
    },
    shortcut () {
      this.$shortcut.bind('⌘+c, ctrl+c', { func: this.copy });
      this.$shortcut.bind('⌘+v, ctrl+v', {
        func: this.paste,
        params: false
      });
      this.$shortcut.bind('backspace', {
        func: this.handleCtxClick,
        params: 'delete'
      });
    },
    editName () {
      this.editable = true;
    },
    async inputScreenBlur () {
      if (this.screenInfo.name !== this.screenName) {
        if (this.screenName) {
          this.$store.dispatch('editor/updateScreenInfo', [
            { key: 'name', value: this.screenName }
          ]);
          this.$message.success('修改成功');
        } else {
          this.screenName = this.screenInfo.name;
        }
      }
      this.editable = false;
    },
    exportScreen () {
      this.$confirm('确认导出大屏？', { title: '提示', type: 'info' })
        .then(() => {
          const userId = localStorage.getItem('userId');
          var a = document.createElement('a');
          a.download = '';
          a.href = replaceUrl('/api/screen/export?id=', null, true) + this.screenInfo.id + '&userId=' + userId + '&t=' + new Date().getTime();
          document.body.append(a);
          a.click();
          a.parentElement.removeChild(a);
        })
        .catch(() => {});
    },
    headerReturn () {
      this.isReturn = true
      if (this.screenInfo.isScreentpl) {
        // 编辑模板 返回模板列表
        this.$router.push({
          path: '/screentpl'
        });
        return;
      }
      // 面板编辑 直接关闭当前页面
      const { comp, screenId } = this.$route.query;
      const panels = ['pitch', 'dialog', 'dynamic', 'referPanel'];
      if (panels.includes(comp)) {
        const _top = window.opener;
        if (_top && !_top.closed) {
          _top.name = 'top_' + uuid();
          window.open('', _top.name);
          window.close();
        } else {
          window.name = '';
          this.$router.push({
            path: `/screen/edit/${screenId}`
          });
        }
        return;
      }

      const { id, screenType, coeditId } = this.screenInfo
      if (coeditId) {
        const userId = localStorage.getItem('userId');
        const userName = localStorage.getItem('name');
        if (screenType === 'scene') {
          let exitOptions = {
            screenId: id,
            userId: userId,
            userName: userName
          }
        if(!this.isOnScene) {
          // 当前在页面中
          exitOptions.pageId = this.pageId
        }else {
          // 当前在场景中
          exitOptions.sceneId = this.sceneId
        }
        // 场景大屏页面退出编辑状态
          this.socket.io.emit('exitScenePage', exitOptions, res => {
            if (res.success) {
            // 退出页面成功 =》 场景大屏退出编辑状态
              this.socket.io.emit('exitScreen', {
                screenId: id,
                screenType: screenType,
                userId: userId,
                userName: userName
              }, (res) => {
                if (res.success) {
                // 退出成功
                  this.$router.push({
                    path: `/workspace/${this.currWorkspaceId || this.screenInfo.workspaceId}`
                  });
                } else {
                }
              })
            } else {
            // 退出页面失败
            }
          })
        } else {
          // 普通协同大屏退出
          // 退出页面成功 =》 场景大屏退出编辑状态
          this.socket.io.emit('exitScreen', {
            screenId: id,
            screenType: screenType,
            userId: userId,
            userName: userName
          }, (res) => {
            if (res.success) {
              // 退出成功
              this.$router.push({
                path: `/workspace/${this.currWorkspaceId || this.screenInfo.workspaceId}`
              });
            } else {
            }
          })
        }
      } else if (this.screenInfo.screenType === 'child') {
        const _top = window.opener;
        if (_top && !_top.closed) {
          _top.name = 'top_' + uuid();
          window.open('', _top.name);
          window.close();
        } else {
          window.name = '';
          this.$router.push({
            path: `/screen/edit/${screenId}`
          });
        }
      } else {
        this.$router.push({
          path: `/workspace/${this.currWorkspaceId || this.screenInfo.workspaceId}`
        });
      }
    },
    generateTmplate () {
      // 生成模板
      this.$confirm('确认生成大屏模板？', { title: '提示', type: 'warning' })
        .then(async () => {
          const data = {
            id: this.screenInfo.id,
            name: this.screenInfo.name + 'Copy',
            workspaceId: this.currWorkspaceId || this.screenInfo.workspaceId,
            projectId: this.screenInfo.projectId,
            type: this.screenInfo.type,
            templateId: this.screenInfo.templateId,
            isScreentpl: true
          };
          const res = await screenCopy(data);
          if (res && res.success) {
            this.$message.success('已生成大屏模板');
          }
        })
        .catch(() => {});
    },
    initCallbackManager () {
      const { screenComs } = this
      const { params, query } = this.$route;
      const route = {
        ...params,
        ...query
      }
      const callbackManager = new CallbackManager();
      callbackManager.addCallbackKey('route', route);
      _.values(screenComs).forEach(com => {
        if (com.interactionConfig && com.interactionConfig.callbackParams) {
          com.interactionConfig.callbackParams.forEach(cb => {
            callbackManager.addCallbackKey(cb.variableName)
          })
        }
      })
      this.callbackManager = callbackManager;
    },
    refreshLayers () {
      this.failNodes = [];
      this.fetchData();
    },
    handlerHints (data) {
      let isShow = JSON.parse(localStorage.getItem('hintsShow')) ?? true
      if (isShow) {
        this.$refs.hints.showDialog(data);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/style/mixins";
.seatom-edit {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .edit-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 5px;
    margin-left: 20px;
    .base-button {
      display: inline-block;
      width: 32px;
      height: 28px;
      line-height: 1;
      white-space: nowrap;
      cursor: pointer;
      background: transparent;
      border: none;
      border-radius: 4px;
      color: #fff;
      text-align: center;
      outline: none;
      transition: 0.1s;
      position: relative;
      &.drop-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 32px;
        > i {
          font-size: 12px;
        }
      }
      > .icon {
        font-size: 16px;
      }
      &.select {
        background: var(--seatom-sub-main-color);
        .error-nums {
          right: -8px;
          top: -6px;
        }
      }
      &.abnormal > .icon {
        font-size: 19px;
      }
      .error-nums {
        position: absolute;
        right: 0px;
        top: -2px;
        font-size: 12px;
        width: 15px;
        height: 15px;
        line-height: 15px;
        border-radius: 50%;
        background: #ff475d;
        transform: scale(0.9);
      }
    }
    .btn-name {
      font-size: 12px;
      color: #fff;
      margin-top: 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .btn-icon {
      cursor: pointer;
      i {
        color: #fff;
      }
      line-height: 28px;
      font-size: 19px;
    }
  }
  .seatom-header {
    position: relative;
    height: 71px;
    z-index: 100;
    padding-right: 8px;
    display: flex;
    align-items: center;
    user-select: none;
    color: #a1aeb3;
    background: #1d1e1f;
    border-bottom: 1px solid #000;
    .header-return {
      height: 100%;
      width: 233px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &-icon {
        display: flex;
        width: 32px;
        font-size: 20px;
        justify-content: center;
        align-items: center;
        height: 100%;
        cursor: pointer;
        &:hover {
          background-color: #30333d;
          color: #4fb0ff;
        }
      }
      .edit-name {
        display: flex;
        align-items: center;
        width: 150px;
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        color: #fff;
        padding: 0 15px;
        .name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .icon {
          margin-left: 10px;
          cursor: pointer;
        }
      }
      ::v-deep .el-input {
        width: 90%;
        .el-input__inner {
          border-color: #409eff;
        }
      }
    }
    .edit-top-btn {
      flex: 1;
      height: 70px;
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .edit-btns-group {
        display: flex;
      }
      .drawer-actions {
        display: flex;
        flex: 0.8;
      }
      .global-btns-group {
        display: flex;
      }
    }
  }
  .loading-indicator {
    position: absolute;
    top: 70px;
    left: 0;
    width: 100%;
    height: 0;
    z-index: 101;
    transition: height 0.5s;
    background: linear-gradient(to right, #ff8754, #2681ff);
    @keyframes ladingAnimation {
      from {
        transform: translateX(-100%);
      }
      to {
        transform: translateX(150%);
      }
    }
    &.loading {
      height: 1px;
      animation: ladingAnimation 1s infinite ease-out;
    }
  }
  .seatom-popover-portal {
    position: relative;
    width: 100%;
    height: 0;
    .update-panel-wp {
      position: absolute;
      left: -50px;
      top: 5px;
    }
  }
  .seatom-drawer__mask {
    position: absolute;
    height: 100%;
    width: 100%;
  }
  .seatom-eidt-main {
    flex: 1;
    display: flex;
    flex-wrap: nowrap;
    position: relative;
    overflow: hidden;
    background: #313239;
    .layer-panel-wp {
      flex: none;
      position: relative;
      width: 200px;
      height: 100%;
      z-index: 5;
      background: #1d1f26;
      display: flex;
      flex-direction: column;
      transition: width 0.3s ease;
      border-right: 1px solid #000;
      overflow: hidden;
      &.--hide {
        width: 0;
      }
    }
    .component-panel-wp {
      flex: none;
      position: relative;
      width: 233px;
      height: 100%;
      z-index: 4;
      background: var(--seatom-panel-color);
      transition: width 0.3s ease;
      overflow: hidden;
      box-shadow: 1px 0 #000;
      &.--hide {
        width: 0;
      }
    }
    .source-panel-wp {
      position: absolute;
      width: 300px;
      height: 100%;
      background: var(--seatom-panel-color);
      z-index: 12;
      transition: width 0.3s ease;
      overflow: hidden;
      box-shadow: 1px 0 #000;
      &.--hide {
        width: 0;
      }
    }
    .right-edit-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      height: 100%;
      position: relative;
      .toolbox-panel-wp {
        height: 40px;
        transition: height 0.3s ease;
        border-bottom: 1px solid #000;
        z-index: 10;
        &.--hide {
          height: 0;
          overflow: hidden;
        }
      }
      .editor-panel-wp {
        width: 100%;
        height: 100%;
        flex: 1;
        display: flex;
        position: relative;
        overflow: hidden;
      }
    }
    .config-panel-wp {
      width: 332px;
      height: 100%;
      z-index: 90;
      background: #1c1f25;
      position: relative;
      transition: width 0.25s ease-in-out;
      // overflow: hidden;
      box-shadow: -1px 0 #000;
      &.--hide {
        width: 0;
      }
    }
    .filter-panel-wp {
      position: absolute;
      width: 500px;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 90;
      background: #1c1f25;
      box-shadow: -1px 0 #000;
      transition: transform 0.25s linear;
      overflow: hidden;
      &.--hide {
        transform: translateX(-500px);
      }
    }
    .permission-panel-wp {
      position: absolute;
      width: 400px;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 90;
      background: #1c1f25;
      box-shadow: -1px 0 #000;
      transition: transform 0.25s linear;
      overflow: hidden;
      &.--hide {
        transform: translateX(-400px);
      }
    }
    .publicDataManage-panel-wp {
      position: absolute;
      width: 510px;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 90;
      background: #1c1f25;
      box-shadow: -1px 0 #000;
      transition: transform 0.25s linear;
      overflow: hidden;
      &.--hide {
        transform: translateX(-510px);
      }
    }
    .error-monitor-wp {
      position: absolute;
      width: 350px;
      height: 100%;
      top: 0;
      left: 0;
      font-size: 12px;
      z-index: 90;
      background: #13151a;
      box-shadow: -1px 0 #000;
      transition: transform 0.25s ease-in-out;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      &.--hide {
        transform: translateX(-500px);
      }
    }
  }
}
.mr-4 {
  margin-right: 4px;
}
</style>
<style lang="scss">
.error-info-container {
  width: 600px;
  .el-message-box__message {
    padding: 20px 0;
    .error-info-box {
      display: flex;
      flex-direction: column;
      text-align: left;
      .error-info-header {
        height: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #f56c6c;
        .icon {
          cursor: pointer;
          transition: transform 0.3s ease-in-out;
        }
        .rotate {
          transform: rotate(180deg);
        }
      }
      .error-info-stack {
        overflow: auto;
        color: #f56c6c;
        height: 0;
        transition: all 0.3s ease-in-out;
        &.show {
          height: 300px;
        }
      }
    }
  }
}
.ai-footer .voice-input textarea {
  color: #fff;
}
</style>
