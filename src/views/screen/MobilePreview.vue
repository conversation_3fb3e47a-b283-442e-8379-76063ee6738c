<template>
  <div class="mobile-preview">
    <div class="preview-wrapper">
      <div class="screen-wrap">
        <div class="iframe-wp">
          <iframe :src="pageUrl" title="" class="mobile-page"></iframe>
        </div>
      </div>
      <div class="screen-config">
        <div class="mobile-config">
          <div class="qr-code">
            <vue-qrcode :value="shareUrl" :options="options"></vue-qrcode>
          </div>
          <div class="code-tips">手机扫码预览</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
export default {
  name: 'MobilePreview',
  components: {
    VueQrcode
  },
  data () {
    return {
      screenId: null,
      token: null,
      userId: null,
      loading: true,
      options: {
        width: 260,
        height: 260,
        margin: 1
      }
    }
  },
  computed: {
    pageUrl () {
      return `${process.env.SUB_PATH || '/'}screen/preview/${this.screenId}?token=${this.token}&userId=${this.userId}&loading=${this.loading}`
    },
    shareUrl () {
      return `${window.location.origin}${process.env.SUB_PATH || '/'}screen/preview/${this.screenId}?token=${this.token}&userId=${this.userId}&loading=${this.loading}`
    }
  },
  created () {
    const { screenId } = this.$route.params;
    const { loading } = this.$route.query;
    const token = localStorage.getItem('token');
    const userId = localStorage.getItem('userId');
    this.screenId = screenId;
    this.token = token;
    this.userId = userId;
    this.loading = loading;
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.mobile-preview {
  width: 100%;
  height: 100%;
  background: #313233;
  .preview-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    .screen-wrap {
      flex: 4;
      overflow: hidden;
      height: 100%;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .iframe-wp {
        font-size: 0;
        position: relative;
        background: #313237;
        border: 1px solid #3a4659;
        margin: 30px;
        .mobile-page {
          width: 375px;
          height: 667px;
        }
      }
    }
    .screen-config {
      flex: 6;
      overflow: hidden;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #bcc9d4;
      .mobile-config {
        width: 490px;
        height: 440px;
        padding: 20px;
        background: #3a3c3e;
        margin-left: 30px;
        box-shadow: 0 0 12px -9px #000;
        border-radius: 2px;
        position: relative;
        transition: .2s;
        .qr-code {
          margin: 30px 0 20px;
          display: flex;
          justify-content: center;
        }
        .code-tips {
          font-size: 14px;
          color: #fff;
          text-align: center;
        }
      }
    }
  }
}
</style>
