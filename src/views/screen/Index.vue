<template>
  <div class="screen-page">
    <router-view></router-view>
  </div>
</template>

<script>
import { getFontList } from '@/api/workspace';
import { replaceUrl } from '@/utils/base';
export default {
  created () {
    if (!window.cangjie || (window.cangjie && window.cangjie.fonts.length === 0)) {
      this.addFontface();
    }
  },
  methods: {
    async addFontface () {
      // 加载自定义字体
      const userId = localStorage.getItem('userId');
      let rule = '';
      const res = await getFontList({ userId: userId });
      if (res.success) {
        res.data.forEach(item => {
          rule += '@font-face {font-family:"' + item.fontName + '";src:url("' + replaceUrl(process.env.VUE_APP_SERVER_URL + item.fontUrl) + '");}'
        });
        const sty = document.createElement('style');
        sty.type = 'text/css';
        sty.innerHTML = rule;
        document.getElementsByTagName('head')[0].appendChild(sty);
        // 获取字体列表挂载至大屏中，供编辑器中字体设置使用
        window.cangjie = { fonts: [] };
        window.cangjie.fonts = res.data;
      } else {
        console.error('获取字体列表异常:', res.message);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-page {
  height: 100%;
}
</style>
