<template>
  <div
    class="screen-container"
    v-loading="warnLoading"
    element-loading-text="loading"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)">
    <ViewScreen
      :screenId="screenId"
      v-if="isScreenCreate"
      @loaded="handleScreenLoaded"
      @dataLoad="screenName"
    />
    <seatom-loading v-if="showLoading">
      <i class="loading-icon"></i>
    </seatom-loading>
    <PasswordProtect v-if="showPwd" @confirm="handlePwdConfirm" tips="请输入控制码"></PasswordProtect>
    <div class="control-warning" v-if="showWarning">
      尚未开启终端交互，请联系管理员！
    </div>
    <el-dialog :visible.sync="show" title="提示" :close-on-click-modal="false" :show-close="false" width="300px" top="-100px">
      <div class="tips">{{ warnTxt }}</div>
      <div slot="footer">
        <el-button type="default" size="mini" @click="cancelHandler">取消</el-button>
        <el-button type="primary" size="mini" @click="submitHandler">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { screensocketVerify, screensocket } from '@/api/screen';
import PasswordProtect from '@/components/view/PasswordProtect';
import Vue from 'vue';
import VueSocket from 'vue-socket.io';
import { getFingerprintID, replaceUrl } from '@/utils/base';
Vue.prototype.$isControl = true;

export default {

  components: {
    PasswordProtect
  },

  provide: function () {
    return {
      callbackFatherManager: () => {},
      permissionFatherMap: () => null
    }
  },

  data () {
    return {
      screenId: null,
      isScreenCreate: false,
      showLoading: false,
      showPwd: false,
      isConnect: false, // 是否开启终端控制
      showWarning: false,
      show: false,
      warnTxt: '',
      warnCode: null,
      warnLoading: false
    };
  },

  sockets: {
    connect () {
      // eslint-disable-next-line
      console.log('socket 连接成功=============================');
    }
  },

  created () {
    const { screenId } = this.$route.params;
    const { token, userId } = this.$route.query;
    if (token) {
      localStorage.setItem('token', token);
    }
    if (userId) {
      localStorage.setItem('userId', userId);
    }
    if (screenId) {
      this.screenId = screenId;
    }
    this.getScreensocket();
  },

  methods: {
    async getScreensocket () {
      const params = {
        screenId: this.screenId
      }
      const res = await screensocket(params);
      if (res && res.success) {
        const data = res.data;
        this.isConnect = data.isConnect;
        if (this.isConnect) {
          this.showPwd = true;
        } else {
          this.showWarning = true;
        }
      }
    },
    createScreen () {
      this.showLoading = true;
      this.isScreenCreate = true;
    },
    handleScreenLoaded () {
      this.showLoading = false;
      // 页面截屏
      if (_.isFunction(window.screenshotStart)) {
        this.$nextTick(() => {
          window.screenshotStart.call(null);
        })
      }
    },
    handlePwdConfirm (pwd) {
      pwd = _.trim(pwd);
      if (pwd) {
        const data = {
          screenId: this.screenId,
          password: pwd
        }
        screensocketVerify(data).then(res => {
          if (res.success && res.data) {
            this.showPwd = false;
            getFingerprintID().then(uid => {
              Vue.use(new VueSocket({
                debug: false,
                connection: replaceUrl('/chat', null, true),
                // connection: 'http://192.168.4.90:5001/chat',
                options: {
                  path: '/api/socket',
                  query: {
                    screenId: this.screenId,
                    isControl: true,
                    uid: uid
                  }
                }
              }))
              this.$vueSocketIo.emitter.addListener('message', this.messageHandler, this)
              this.createScreen();
            })
          } else {
            alert('密码错误！');
          }
        });
      }
    },
    messageHandler (msg) {
      const { type } = msg;
      if (type === 'message') {
        this.warnCode = msg.code;
        if (!this.warnLoading) {
          if (this.warnCode === 0) {
            this.warnTxt = '该大屏已经有人在控制，是否申请控制？';
            this.show = true;
          }
          if (this.warnCode === 1) {
            this.warnTxt = '有人申请控制大屏，是否同意？';
            this.show = true;
          }
        }
        if (this.warnCode === 2) {
          this.$message.success('对方已同意你的控制请求');
          this.warnLoading = false;
        }
        if (this.warnCode === 3) {
          this.$message.error('对方拒绝你的控制请求');
          this.isScreenCreate = false;
          this.showPwd = true;
          this.warnLoading = false;
          this.closeConnect();
        }
        if (this.warnCode === 4) {
          this.$message.info('当前连接人数太多，请稍后再试！');
          this.isScreenCreate = false;
          this.showPwd = true;
          this.warnLoading = false;
          this.showLoading = false;
          this.closeConnect();
        }
      }
    },
    cancelHandler () {
      if (this.warnCode === 0) {
        this.isScreenCreate = false;
        this.showPwd = true;
        this.closeConnect();
      }
      if (this.warnCode === 1) {
        this.$socket.emit('chat', { type: 'agree', agree: false });
      }
      this.show = false;
    },
    submitHandler () {
      if (this.warnCode === 0) {
        this.$socket.emit('chat', { type: 'apply', apply: true });
        this.warnLoading = true;
      }
      if (this.warnCode === 1) {
        this.$socket.emit('chat', { type: 'agree', agree: true });
        this.isScreenCreate = false;
        this.showPwd = true;
        this.closeConnect();
      }
      this.show = false;
    },
    closeConnect () {
      this.$socket.close();
    },
    screenName (screenName) {
      document.title = screenName
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: rgb(49, 50, 57);
}
.loading-icon {
  display: inline-block;
  width: 128px;
  height: 128px;
  background: url('../../assets/img/svg/loading.svg');
  animation: loading 2s infinite ease-in-out;
}
.control-warning {
  width: 330px;
  font-size: 16px;
  color: #fff;
  text-align: center;
  margin: 100px auto 0;
}
@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
