<template>
    <div
    class="login-loding"
    v-loading='loading'
    element-loading-background="rgba(0, 0, 0, 0.8)"
    >
    </div>
</template>

<script>
import { getWorkspace } from '@/api/workspace';
import { localLogin } from '@/api/user';
const Base64 = require('js-base64').Base64;
export default {
  data () {
    return {
      loading: true
    }
  },
  mounted () {
  },
  beforeRouteEnter: (to, from, next) => {
    const userId = window.localStorage.getItem('userId')
    const type = 0
    if (to.query.islogin === 'false') { // 不需要登陆直接进去
      next(vm => {
        vm.login()
      })
      return
    }
    if (userId) {
      getWorkspace({ userId, type }).then(res => {
        if (!res.success) {
          next('/login')
          return
        }
        if (!res.data || !res.data.length) {
          next('/login')
          return
        }
        const workspaceId = res.data[0].id
        next(`/workspace/${workspaceId}`)
      })
      return
    }
    next('/login')
  },
  methods: {
    async login () {
      const body = {
        password: Base64.encode(Base64.encode('local_admin')),
        username: 'local_admin',
        domain: 'local'
      }
      const res = await localLogin(body)
      if (res && res.success) {
        const { userInfo: { data: { result: { user_info: { name } } } } } = res.data;
        const workspaceId = res.data.workspaceData.id
        const userId = res.data.workspaceData.userId
        const token = res.data.userInfo.data.result.access_token
        window.localStorage.setItem('userId', userId);
        window.localStorage.setItem('token', token);
        window.localStorage.setItem('name', name);
        window.localStorage.setItem('workspaceId', workspaceId)
        this.$router.push({
          path: `/workspace/${workspaceId}`
        })
      } else {
        const errstr = res.message
        this.$message.error(errstr)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.login-loding{
   position: relative;
   width: 100%;
   height: 100%;
   background-color: rgba(23, 26, 31);
}
</style>
