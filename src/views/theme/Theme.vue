<template>
  <div class="theme-manage">
    <div class="theme-head">
      <hz-icon class="icon" name="theme"></hz-icon>
      组件主题
      <!-- <el-button type="text" style="text-align: right" size="mini" @click="goback()">返回首页</el-button> -->
    </div>
    <div class="theme-content">
      <div class="comp-wrap">
        <div class="sub-head">
          <span>组件列表</span>
          <el-input v-model="searchText" clearable prefix-icon="el-icon-search" class="search-input" size="mini"></el-input>
        </div>
        <div class="comp-list" ref="complist">
          <CompList>
            <CompListItem
              class="gray-border"
              :class="{'active': comp.id === packageId}"
              v-for="comp in packageList"
              :key="comp.id"
              :title="comp.alias"
              :icon="comp.icon"
              :data="comp"
              @click="handleItemClick($event)" />
          </CompList>
        </div>
      </div>
      <div class="comp-theme">
        <div class="sub-head">
          <span>主题列表</span>
          <el-button class="add-btn" type="text" icon="el-icon-plus" size="mini" @click="newTheme()" :disabled="!comType">新增主题</el-button>
        </div>
        <div class="theme-list-wrap">
          <ThemeListItem
            v-for="item in dataList"
            :key="item.id"
            :data="item"
            @editItem="editTheme"
            @delItem="delItem"
            @editName="editName"
            @setDefault="setDefault" />
          <div class="no-data" v-if="dataList.length === 0">暂无数据</div>
        </div>
      </div>
    </div>
    <seatom-loading v-if="loading"></seatom-loading>
    <!-- 新增主题弹框 -->
    <ThemeAdd ref="add" :packageId="packageId" @refresh="getThemeList" />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import CompList from '@/components/editor/CompList';
import CompListItem from '@/components/editor/CompListItem';
import ThemeListItem from '@/components/theme/ThemeListItem';
import ThemeAdd from '@/components/theme/ThemeAdd';
import { themeList, deleteTheme, updateTheme } from '@/api/theme';
import { getFontList } from '@/api/workspace';
import { replaceUrl } from '@/utils/base';
export default {
  name: 'theme',
  components: {
    CompList,
    CompListItem,
    ThemeListItem,
    ThemeAdd
  },
  data () {
    return {
      comType: null,
      packageId: null,
      dataList: [],
      loading: false,
      scrollTop: 0,
      searchText: ''
    }
  },
  computed: {
    ...mapState({
      compPackges: state => state.editor.compPackges,
      comScrollTop: state => state.comtheme.scrollTop
    }),
    complist () {
      return this.$refs.complist
    },
    packageList () {
      if (this.searchText) {
        return this.compPackges.filter(item => item.alias.includes(this.searchText));
      }
      return this.compPackges;
    }
  },
  beforeRouteEnter (to, from, next) {
    if (from.name === 'themeEdit') {
      next(vm => {
        vm.comType = from.query.comType;
        vm.packageId = Number(from.query.packageId);
        vm.searchText = from.query.searchText || '';
      });
      return
    }
    next()
  },
  beforeRouteLeave (to, from, next) {
    if (to.name === 'themeEdit') {
      this.$store.commit('comtheme/changeScrollTop', this.scrollTop);
    }
    next()
  },
  async created () {
    if (!window.cangjie || (window.cangjie && window.cangjie.fonts.length === 0)) {
      this.addFontface();
    }
    await this.$store.dispatch('editor/getCompPackages');
    if (this.compPackges.length) {
      this.comType = this.comType || this.compPackges[0].name;
      this.packageId = this.packageId || this.compPackges[0].id;
      this.getThemeList();
    }
  },
  mounted () {
    this.$nextTick(() => {
      window.addEventListener('scroll', this.handleScroll, true);
      this.$refs.complist.scrollTop = this.comScrollTop
    })
  },
  methods: {
    handleItemClick (item) {
      this.comType = item.name;
      this.packageId = item.id;
      this.getThemeList();
    },
    handleScroll () {
      if (this.complist) {
        this.scrollTop = this.complist.scrollTop
      }
    },
    goback () {
      this.$router.push('/index')
    },
    async getThemeList () {
      const params = {
        comType: this.comType
      }
      this.loading = true;
      const res = await themeList(params);
      if (res && res.success) {
        this.dataList = res.data;
      }
      this.loading = false;
    },
    delItem (item) { // 删除
      this.$confirm('确认删除该主题？', { title: '提示', type: 'warning' }).then(async () => {
        const data = {
          id: item.id
        }
        const res = await deleteTheme(data, {});
        if (res && res.success) {
          this.$message.success('删除成功');
          this.getThemeList();
        }
      })
    },
    newTheme () { // 新增主题
      this.$refs.add.showTheme();
    },
    editTheme (item) { // 编辑主题
      this.$router.push({
        path: '/theme-edit',
        query: {
          themeId: item.id,
          comType: item.comType,
          packageId: this.packageId,
          searchText: this.searchText
        }
      })
    },
    editName (item) {
      this.$refs.add.showEdit(item);
    },
    async setDefault (item) {
      const data = {
        isDefault: !item.isDefault,
        comType: item.comType
      }
      const res = await updateTheme(data, { id: item.id });
      if (res && res.success) {
        this.getThemeList();
      }
    },
    async addFontface () {
      // 加载自定义字体
      const userId = localStorage.getItem('userId');
      let rule = '';
      const res = await getFontList({ userId: userId });
      if (res.success) {
        res.data.forEach(item => {
          rule += '@font-face {font-family:"' + item.fontName + '";src:url("' + replaceUrl(process.env.VUE_APP_SERVER_URL + item.fontUrl) + '");}'
        });
        const sty = document.createElement('style');
        sty.type = 'text/css';
        sty.innerHTML = rule;
        document.getElementsByTagName('head')[0].appendChild(sty);
        // 获取字体列表挂载至大屏中，供编辑器中字体设置使用
        window.cangjie = { fonts: [] };
        window.cangjie.fonts = res.data;
      } else {
        console.error('获取字体列表异常:', res.message);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.theme-manage {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #171b22;
  .theme-head {
    font-size: 14px;
    color: #fafafa;
    height: 45px;
    line-height: 45px;
    padding: 0 20px;
    .icon {
      margin-right: 5px;
    }
  }
  .sub-head {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    line-height: 30px;
    color: #bcc9d4;
    padding: 0 10px;
    background: var(--seatom-panel-title-bg);
    ::v-deep button i {
      color: #409EFF;
    }
    .search-input {
      width: 100px;
      ::v-deep .el-input__inner {
        height: 24px;
        line-height: 24px;
      }
    }
  }
  .theme-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    .comp-wrap {
      display: flex;
      flex-direction: column;
      width: 200px;
      margin-right: 3px;
      .comp-list {
        flex: 1;
        background: #0a0b0e;
        padding: 6px;
        overflow: auto;
        &::-webkit-scrollbar {
          display: block;
          width: 4px;
        }

        &::-webkit-scrollbar-thumb{
          background: #434b55;
          border: 1px solid #434b55;
        }

        .gray-border {
          border: 1px solid #212326;
          &.active {
            border-color: #2681ff;
          }
        }
      }
    }
    .comp-theme {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      .theme-list-wrap {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        padding: 16px 0 30px;
        overflow: auto;
      }
    }
  }
  .no-data {
    width: 100%;
    font-size: 14px;
    color: #999;
    text-align: center;
    padding-top: 100px;
  }
}
</style>
