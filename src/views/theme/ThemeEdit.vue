<template>
  <div class="theme-manage">
    <div class="theme-head">
      <div class="left-wrap">
        <i class="icon el-icon-arrow-left" @click="back"></i>
        主题编辑
      </div>
      <div class="btn-wrap">
        <el-tooltip content="保存" placement="bottom">
          <button
            class="base-button" @click="save">
            <i class="el-icon-document"></i>
          </button>
        </el-tooltip>
      </div>
    </div>
    <div class="theme-content">
      <ThemeCanvas />
      <div class="config-wrap" v-if="comCtlConfigTree">
        <el-tabs class="config-tab">
          <el-tab-pane label="样式">
            <ConfigTree
              :treeData="comCtlConfigTree"
              :configObj="comCtlConfigObj"
              @change="handleConfigTreeChange">
            </ConfigTree>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import ThemeCanvas from '@/components/theme/ThemeCanvas';
import Tree from '@/lib/Tree';
import Vue from 'vue';
import ConfigNode from '@/components/editor/ConfigNode';
import { mapState, mapGetters } from 'vuex';
import { saveTheme } from '@/api/theme';
import { screenshot } from '@/utils/base';
Vue.component(ConfigNode.name, ConfigNode);
export default {
  name: 'ThemeEdit',
  provide: function () {
    return {
      getLayerTree: () => this.layerTree, // 通过函数实现响应式
      screenComs: () => {},
      callbackManager: () => {},
      screenInfo: {}
    };
  },
  components: {
    ThemeCanvas
  },
  data () {
    return {
      layerTree: new Tree(),
      loading: false
    }
  },
  computed: {
    ...mapState({
      screenLayers: state => state.comtheme.screenLayers
    }),
    ...mapGetters('comtheme', ['comCtlConfigTree', 'comCtlConfigObj'])
  },
  created () {
    this.packageId = this.$route.query.packageId;
    this.fetchData();
  },
  methods: {
    handleConfigTreeChange ({ path, value }) {
      this.$store.dispatch('comtheme/updateScreenComTheme', {
        keyValPairs: [
          { key: `config.${path}`, value: value }
        ]
      });
    },
    fetchData () {
      const params = {
        id: this.$route.query.themeId,
        packageId: this.$route.query.packageId
      };
      this.$store.dispatch('comtheme/initThemeInfo', params).then(() => {
        this.layerTree = new Tree({ id: 'root', children: this.screenLayers });
      });
    },
    back () {
      this.$router.go(-1);
    },
    async save () {
      const dom = document.getElementById('screenshoter')
      screenshot(dom, async url => {
        const data = {
          id: this.$route.query.themeId,
          packageId: this.$route.query.packageId,
          url: url
        }
        this.loading = true;
        const res = await saveTheme(data, {});
        if (res && res.success) {
          this.$message.success('保存成功');
        } else {
          this.$message.error('保存失败');
        }
        this.loading = false;
      }, 'comthemeshotList')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/style/mixins';
.theme-manage {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #171b22;
  .theme-head {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #fafafa;
    height: 45px;
    line-height: 45px;
    padding: 0 120px 0 20px;
    background: #171b22;
    z-index: 10;
    .icon {
      cursor: pointer;
      margin-right: 5px;
    }
    .btn-wrap {
      display: flex;
      align-items: center;
      .base-button {
        @include button;
      }
    }
  }
  .theme-content {
    display: flex;
    height: calc(100vh - 45px);
    background: var(--seatom-main-theme-bg);
    .config-wrap {
      position: relative;
      width: 330px;
      z-index: 10;
      background: #191c21;
    }
  }
  .config-tab ::v-deep {
    > .el-tabs__header {
      .el-tabs__nav {
        width: 100%;
      }
      .el-tabs__active-bar {
        display: none;
        // bottom: unset;
        // background-color: var(--seatom-sub-main-color);
      }
      .el-tabs__item {
        width: 100%;
        text-align: left;
        color: #bfbfbf;
        padding: 0 20px;
        background: #2e343c;
        &.is-active {
          color: var(--seatom-sub-main-color);
          background-color: #22242b;
        }
      }
      .el-tabs__nav-wrap::after {
        display: none;
      }
    }
    > .el-tabs__content {
      overflow: visible;
      > .el-tab-pane {
        height: calc(100vh - 100px);
        overflow-y: auto;
      }
    }
  }
}
</style>
