<template>
  <div class="screen-container" :style="containerStyle">
    <div class="screen" :style="screenStyle">
      <ViewThemeNode
        v-for="(item, index) in screenLayers"
        :key="item.id"
        :id="item.id"
        :type="item.type"
        :zIndex="screenLayers.length - index"
        @compLoaded="handleCompLoaded"
      >
      </ViewThemeNode>
    </div>
  </div>
</template>

<script>
import ViewThemeNode from '@/components/theme/ViewThemeNode';
import { mapState } from 'vuex';

export default {
  name: 'ThemePreview', // 主题预览 （截图使用）

  components: {
    ViewThemeNode
  },

  provide: function () {
    return {
      screenComs: () => {},
      callbackManager: () => {},
      screenInfo: {}
    }
  },

  data () {
    return {
      windowSize: {
        w: document.documentElement.clientWidth,
        h: document.documentElement.clientHeight
      },
      loadCount: 0,
      showPwd: false
    };
  },

  computed: {
    ...mapState({
      screenLayers: state => state.comtheme.screenLayers
    }),
    containerStyle () {
      const style = {
        overflow: 'auto'
      }
      return style;
    },
    screenStyle () {
      const { windowSize } = this;
      const style = {
        width: windowSize.w + 'px',
        height: windowSize.h + 'px'
      }
      return style;
    }
  },

  created () {
    const { themeId, packageId } = this.$route.query;
    this.initScreen(themeId, packageId);
  },

  methods: {
    initScreen (themeId, packageId) {
      const params = {
        id: themeId,
        packageId: packageId
      };
      this.$store.dispatch('comtheme/initThemeInfo', params).then(() => {
        // console.log(this.screenLayers)
      });
      window.addEventListener('resize', e => {
        this.windowSize.w = document.documentElement.clientWidth;
        this.windowSize.h = document.documentElement.clientHeight;
      });
    },
    handleCompLoaded () {
      this.$nextTick(() => {
        if (_.isFunction(window.screenshotStart)) {
          window.screenshotStart.call(null);
          // console.log('加载完成=======', _.isFunction(window.screenshotStart))
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #171b22;
  .screen {
    position: absolute;
    left: 0;
    top: 0;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform-origin: left top;
    overflow: hidden;
  }
}
</style>
