<template>
  <div class="tutorial-card-container">
    <div :class="['tutorial-card-main',active ? 'active' : '']"
         @mouseenter="active = true"
         @mouseleave="active = false"
         @click="selectCard"
         :style="cardStyle(item.coverStyle)"
    >
      <div class="tutorial-card-cover">
        <div class="cover-0" v-if="item.coverStyle === 0">
          <div class="content-wrapper" v-if="item.title.length > keywordIndex">
            <div class="cover-0-top">
              <div class="cover-0-top-left" :style="computedCoverStyle[0]">
                {{ keywords[0] }}
              </div>
              <div class="cover-0-top-right" :style="computedCoverStyle[1]">
                {{ keywords[1] }}
              </div>
            </div>
            <div class="cover-0-bottom" :style="computedCoverStyle[2]">
              {{ keywords[2] }}
            </div>
          </div>
          <div class="cover-rest-content" v-else>
            {{ item.title }}
          </div>
        </div>
        <div class="cover-1" v-else-if="item.coverStyle === 1">
          <div class="content-wrapper" v-if="item.title.length > keywordIndex">
            <div class="cover-1-top" :style="computedCoverStyle[0]">
              {{ keywords[0] }}
            </div>
            <div class="cover-1-bottom" :style="computedCoverStyle[1]">
              {{ keywords[1] }}
            </div>
          </div>
          <div class="cover-rest-content" v-else>
            {{ item.title }}
          </div>
        </div>
        <div class="cover-2" v-else-if="item.coverStyle === 2">
          <div class="content-wrapper" v-if="item.title.length > keywordIndex">
            <div class="cover-2-left" :style="computedCoverStyle[0]">
              {{ keywords[0] }}
            </div>
            <div class="cover-2-right" :style="computedCoverStyle[1]">
              {{ keywords[1] }}
            </div>
          </div>
          <div class="cover-rest-content" v-else>
            {{ item.title }}
          </div>
        </div>
        <div class="cover-3" v-else-if="item.coverStyle === 3">
          <div class="content-wrapper" v-if="item.title.length > keywordIndex">
            <div class="cover-3-top" :style="computedCoverStyle[0]">
              {{ keywords[0] }}
            </div>
            <div class="cover-3-bottom" :style="computedCoverStyle[1]">
              {{ keywords[1] }}
            </div>
          </div>
          <div class="cover-rest-content" v-else>
            {{ item.title }}
          </div>
        </div>
        <div class="cover-4" v-else-if="item.coverStyle === 4">
          <div class="cover-4-main" :style="computedCoverStyle">
            {{ item.title }}
          </div>
        </div>
        <div class="cover-5" v-else-if="item.coverStyle === 5">
          <div class="cover-5-main" :style="computedCoverStyle">
            {{ item.title }}
            <i class="cover-5-main-top"/>
            <i class="cover-5-main-left"/>
            <i class="cover-5-main-right"/>
            <i class="cover-5-main-bottom"/>
          </div>
        </div>
        <div class="cover-6" v-else-if="item.coverStyle === 6">
          <i class="cover-6-float-1"/>
          <i class="cover-6-float-2"/>
          <i class="cover-6-float-3"/>
          <i class="cover-6-float-4"/>
          <div class="content-wrapper" v-if="item.title.length > keywordIndex">
            <div class="cover-6-top" :style="computedCoverStyle[0]">
              {{ keywords[0] }}
            </div>
            <div class="cover-6-bottom" :style="computedCoverStyle[1]">
              {{ keywords[1] }}
            </div>
          </div>
          <div class="cover-rest-content" v-else>
            {{ item.title }}
          </div>
        </div>
        <div class="cover-7" v-else>
          <div class="content-wrapper" v-if="item.title.length > keywordIndex">
            <div class="cover-7-main">
              <div class="cover-7-main-top" :style="computedCoverStyle[0]">
                {{ keywords[0] }}
              </div>
              <div class="cover-7-main-middle" :style="computedCoverStyle[1]">
                {{ keywords[1] }}
              </div>
              <div class="cover-7-main-bottom" :style="computedCoverStyle[2]">
                {{ keywords[2] }}
              </div>
            </div>
          </div>
          <div class="cover-rest-content" v-else>
            {{ item.title }}
          </div>
        </div>
      </div>
      <hz-icon v-if="!selected && isAdmin" name="tutorial-unselect" class="tutorial-card-icon"/>
      <hz-icon v-if="selected && isAdmin" name="tutorial-selected" class="tutorial-card-icon selected"/>
      <div class="tutorial-card-hover" v-show="active">
        <div @click.stop="showTutorial($event)" v-if="item.content">
          <el-tooltip content="文档" placement="bottom" effect="light">
            <hz-icon class="tutorial-card-hover-icon form" name="tutorial-document"/>
          </el-tooltip>
        </div>
        <div @click.stop="showVideo($event)" v-if="item.videoPath">
          <el-tooltip content="媒体" placement="bottom" effect="light">
            <hz-icon class="tutorial-card-hover-icon media" name="tutorial-media"/>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="tutorial-card-footer" :style="{justifyContent:isAdmin?'flex-start':'center'}">
      <el-tooltip :content="item.title" placement="top">
        <div class="tutorial-card-footer-title" :style="{maxWidth:isAdmin?'200px':'100%'}">
          {{ item.title }}
        </div>
      </el-tooltip>
      <div type="text" class="edit-button" @click="editTutorial" v-if="isAdmin">
        <hz-icon name="tutorial-edit" class="edit-button-icon"/>
        编辑
      </div>
    </div>
  </div>
</template>

<script>
import { getFitSize } from '@/utils/fontsize';
import { mapState } from 'vuex';
import { replaceUrl } from '@/utils/base';
export default {
  name: 'TutorialCard',
  props: {
    index: {
      type: Number,
      default: 0
    },
    item: {
      type: Object,
      default: () => ({})
    },
    selectedList: {
      type: Array,
      default: () => []
    },
    isAdmin: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapState({
      theme: (state) => state.theme
    }),
    isDark () {
      return this.theme === 'dark'
    },
    previewData () {
      return data => data && replaceUrl(process.env.VUE_APP_SERVER_URL + data)
    },
    // eslint-disable-next-line
    computedCoverStyle() {
      switch (this.item.coverStyle) {
        case 0: {
          const temp = {
            0: getFitSize(this.keywords[0], this.keywords[2].length > 8 ? 200 : 110, 46, 1, 1),
            1: getFitSize(this.keywords[1], 100, 46, 1, 1),
            2: getFitSize(this.keywords[2], this.keywords[2].length > 8 ? 300 : 220, 46, 1, 1)
          }
          return {
            0: {
              fontSize: temp[0].fontSize + 'px',
              width: temp[0].fontSize * this.keywords[0] + 'px'
            },
            1: {
              fontSize: temp[1].fontSize + 'px',
              width: temp[1].fontSize * this.keywords[1] + 'px'
            },
            2: {
              fontSize: temp[2].fontSize + 'px',
              width: temp[2].fontSize * this.keywords[2] + 'px'
            }
          }
        }
        case 1: {
          const temp = {
            0: getFitSize(this.keywords[0], 220, 46, 1, 1),
            1: getFitSize(this.keywords[1], this.keywords[1].length > 8 ? 300 : 220, 46, 1, 1)
          }
          return {
            0: {
              fontSize: temp[0].fontSize + 'px',
              color: this.isDark ? '#FFFFFF' : '#865efa',
              width: temp[0].fontSize * this.keywords[0].length + 'px'
            },
            1: {
              fontSize: temp[1].fontSize + 'px',
              color: this.isDark ? '#FFFFFF' : '#a281ff',
              width: temp[1].fontSize * this.keywords[1].length + 'px'
            }
          }
        }
        case 2: {
          const temp = {
            0: getFitSize(this.keywords[0], 50, 100, 1, this.keywords[0].length),
            1: getFitSize(this.keywords[1], this.keywords[1].length > 12 ? 238 : 178, 60, 1, 2)
          }
          return {
            0: {
              fontSize: temp[0].fontSize + 'px',
              color: this.isDark ? '#FFFFFF' : '#ff8e8e',

              width: temp[0].fontSize + 'px'
            },
            1: {
              fontSize: temp[1].fontSize + 'px',
              color: this.isDark ? '#FFFFFF' : '#ff8e8e',
              width: temp[1].fontSize * Math.ceil(this.keywords[1].length / 2) + 'px'
            }
          }
        }
        case 3: {
          const temp = {
            0: getFitSize(this.keywords[0], this.keywords[0].length > 6 ? 300 : 220, 24, 1, 1),
            1: getFitSize(this.keywords[1], this.keywords[1].length > 8 ? 300 : 240, 24, 1, 1)
          }
          return {
            0: {
              color: this.isDark ? '#FFFFFF' : '#6775f9',
              fontSize: temp[0].fontSize + 'px',
              width: temp[0].width + 'px',
              height: temp[0].height + 'px'
            },
            1: {
              color: this.isDark ? '#FFFFFF' : '#ffffff',
              fontSize: temp[1].fontSize + 'px',
              width: temp[1].width + 'px',
              height: this.keywords[1].length > 8 ? '50px' : '36px'
            }
          }
        }
        case 4: {
          const temp = getFitSize(this.item.title, 240, this.item.title.length > 10 ? 56 : 28, 1, this.item.title.length > 10 ? 2 : 1)
          return {
            fontSize: temp.fontSize + 'px',
            width: this.item.title.length > 10
              ? Math.ceil(temp.fontSize * this.item.title.length / 2) + 'px'
              : temp.width + 'px',
            height: this.item.title.length > 10 ? '72px' : '36px'
          }
        }
        case 5: {
          const temp = getFitSize(this.item.title, 240, this.item.title.length > 10 ? 56 : 28, 1, this.item.title.length > 10 ? 2 : 1)
          return {
            fontSize: temp.fontSize + 'px',
            width: this.item.title.length > 10
              ? temp.fontSize * Math.ceil(this.item.title.length / 2) + 'px'
              : temp.width + 'px'
          }
        }
        case 6: {
          const temp = {
            0: getFitSize(this.keywords[0], 220, 32, 1, 1),
            1: getFitSize(this.keywords[1], 220, 46, 1, 1)
          }
          return {
            0: {
              fontSize: temp[0].fontSize + 'px',
              color: this.isDark ? '#FFFFFF' : '#0094ff',
              width: temp[0].fontSize * this.keywords[0].length + 'px'
            },
            1: {
              fontSize: temp[1].fontSize + 'px',
              color: this.isDark ? '#FFFFFF' : '#0094ff',
              width: temp[1].fontSize * this.keywords[1].length + 'px'
            }
          }
        }
        case 7: {
          const temp = {
            0: getFitSize(this.keywords[0], 240, 26, 1, 1),
            1: getFitSize(this.keywords[1], 240, 46, 1, 1),
            2: getFitSize(this.keywords[2], 240, 26, 1, 1)
          }
          return {
            0: {
              fontSize: temp[0].fontSize + 'px',
              color: this.isDark ? '#FFFFFF' : '#4098FF',
              width: temp[0].fontSize * this.keywords[0].length + 'px'
            },
            1: {
              fontSize: temp[1].fontSize + 'px',
              color: this.isDark ? '#FFFFFF' : '#4098FF',
              width: temp[1].fontSize * this.keywords[1].length + 'px'
            },
            2: {
              fontSize: temp[2].fontSize + 'px',
              color: this.isDark ? '#FFFFFF' : '#4098FF',
              width: temp[2].fontSize * this.keywords[2].length + 'px'
            }
          }
        }
      }
    },
    cardStyle () {
      return (type) => {
        return {
          background: this.isDark ? 'linear-gradient(0deg, #000000, #000000)' : this.mapColor[type],
          '--cover-7-middle': this.isDark ? '#000000' : this.mapColor[type],
          '--cover-5-main': this.isDark ? '#000000' : '#ffffff',
          '--cover-1-main': this.isDark ? '#004e66' : 'rgba(192, 171, 255, 0.37)',
          '--cover-3-main': this.isDark ? '#008bb7' : '#6775f9'
        }
      }
    }
  },
  data () {
    return {
      active: false,
      selected: false,
      rowNumber: 1,
      keywords: [],
      keywordIndex: 0,
      mapColor: {
        1: '#eeebff',
        2: '#ffefec',
        3: '#ebf0ff',
        4: '#f0fbff',
        5: '#f0fbff',
        6: '#eaf9ff',
        7: '#dee7f2'
      }
    }
  },
  watch: {
    selectedList: {
      handler (val) {
        this.selected = val.indexOf(this.item.id) !== -1
      },
      deep: true
    },
    item: {
      handler () {
        this.calculateTitle()
      },
      deep: true,
      immediate: true
    }
  },
  created () {
    // const validator = {
    //   0: '该封面的关键词仅支持标题中的词，不支持标题首或标题尾的词。',
    //   7: '该封面的关键词仅支持标题中的词，不支持标题首或标题尾的词。'
    //   1: '该封面的关键词仅支持以标题首位开始的词，不支持标题中间或标题尾的词。',
    //   2: '该封面的关键词仅支持以标题首位开始的词，不支持标题中间或标题尾的词。',
    //   3: '该封面的关键词仅支持标题结尾的词，不支持标题首或标题中的词。',
    //   6: '该封面的关键词仅支持标题结尾的词，不支持标题首或标题中的词。',
    // }
    // this.calculateTitle()
  },
  methods: {
    calculateTitle () {
      const { keyword, title, coverStyle } = this.item
      const temp = []
      this.keywordIndex = title.indexOf(keyword)
      switch (coverStyle) {
        case 0:
        case 7:
          temp.push(title.slice(0, this.keywordIndex))
          temp.push(keyword)
          temp.push(title.slice(this.keywordIndex + keyword.length))
          break
        case 1:
        case 2:
          temp.push(keyword)
          temp.push(title.slice(keyword.length))
          break
        case 3:
        case 6:
          this.keywordIndex = title.lastIndexOf(keyword)
          temp.push(title.slice(0, this.keywordIndex))
          temp.push(keyword)
          break
        case 4:
        case 5:
          this.rowNumber = title.length > 12 ? 2 : 1
          break
      }
      this.keywords = temp
    },
    selectCard () {
      if (!this.isAdmin) return
      this.selected = !this.selected
      this.$emit('selectEvent', { state: this.selected, id: this.item.id })
    },
    showTutorial () {
      const data = { state: true, item: _.cloneDeep(this.item) }
      this.$emit('showTutorialEvent', data)
    },
    showVideo () {
      const data = { state: true, item: _.cloneDeep(this.item) }
      this.$emit('showVideoEvent', data)
    },
    editTutorial () {
      if (!this.isAdmin) return
      const temp = _.cloneDeep(this.item)
      temp.content = temp.content.replaceAll('../', `${replaceUrl('/', '', true)}`)
      const data = { state: true, item: temp }
      this.$emit('editTutorial', data)
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin flex($justify:center,$align:center,$directions:row) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
  flex-direction: $directions;
}

@mixin fontSize($weight:500,$color:#ffffff) {
  font-weight: $weight;
  color: $color;
  line-height: 100%;
}

@mixin pseudo($height,$width,$zIndex:1,$bgColor:#ffffff) {
  position: absolute;
  display: block;
  content: '';
  z-index: $zIndex;
  height: $height;
  width: $width;
  background: $bgColor
}

@mixin contentWrapper() {
  width: 100%;
  height: 100%;
}
.tutorial-card-container {
  width: 354px;
  .tutorial-card-main {
    width: 100%;
    height: 224px;
    background: linear-gradient(0deg, #000000, #000000);
    border-radius: 10px;
    position: relative;
    z-index: 1;
    &.active {
      &::after {
        @include pseudo($width: 364px, $height: 234px, $zIndex: 0, $bgColor: transparent);
        top: 50%;
        left: 50%;
        border-radius: 14px;
        border: 2px solid #4886FF;
        transform: translate(-50%, -50%);
      }
    }
    .tutorial-card-cover {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      @include flex();
      @for $i from 0 through 7 {
        .cover-#{$i} {
          max-width: 90%;
          min-width: 70%;
          height: 75%;
          position: relative;
          @include flex();
        }
      }
      .cover-0 {
        &:after,
        &:before {
          @include pseudo($width: 40%, $height: 2px, $zIndex: 10);
        }
        &:before {
          top: 20px;
          left: -10px;
        }
        &:after {
          right: -10px;
          bottom: 20px;
          background: #00C2FF;
        }
        .content-wrapper {
          @include contentWrapper();
          @include flex($directions: column);
          .cover-0-top {
            width: 100%;
            @include flex($justify: space-around, $align: flex-end);
            line-height: 1.2;
            &-left {
              @include fontSize($weight: 400, $color: #00C2FF);
              @include flex($justify: flex-start, $align: flex-end);
            }
            &-right {
              height: 100%;
              @include fontSize();
              @include flex($justify: center, $align: flex-end);
            }
          }
          .cover-0-bottom {
            min-height: 40px;
            @include flex($justify: center, $align: center);
            @include fontSize($weight: 400, $color: #00C2FF);
            line-height: 1.5;
          }
        }
        .cover-rest-content {
          @include fontSize()
        }
      }
      .cover-1 {
        &:after {
          @include pseudo($height: 2px, $width: 40%, $zIndex: 1);
          bottom: 15px;
          left: 32px;
          background: var(--cover-1-main);
        }
        .content-wrapper {
          @include contentWrapper();
          @include flex($align: flex-start, $directions: column);
          .cover-1-top {
            @include fontSize();
            position: relative;
            line-height: 1.2;
            z-index: 2;
            &:after {
              @include pseudo($width: 46px, $height: 18px, $zIndex: 1);
              background: var(--cover-1-main);
              top: 0;
              right: -20px;
            }
          }
          .cover-1-bottom {
            @include flex($justify: flex-start, $align: center);
            @include fontSize($weight: 400, $color: #00C2FF);
            line-height: 1.5;
            min-height: 30px;
          }
        }
        .cover-rest-content {
          @include fontSize();
        }
      }
      .cover-2 {
        &:before {
          @include pseudo($width: 40%, $height: 2px, $zIndex: 1, $bgColor: #ffffff);
          top: 28px;
          left: 50%;
          transform: translateX(-50%);
        }
        .content-wrapper {
          @include contentWrapper();
          @include flex($justify: flex-start, $align: center);
          .cover-2-left {
            @include fontSize();
            font-family: BDZongYi-A001;
            text-align: center;
          }
          .cover-2-right {
            margin-left: 12px;
            @include fontSize($weight: 400, $color: #00C2FF);
            @include flex($justify: center, $align: center);
            flex-wrap: wrap;
            line-height: 1.2;
          }
        }
        .cover-rest-content {
          font-size: 32px;
          @include fontSize();
        }
      }
      .cover-3 {
        .content-wrapper {
          @include contentWrapper();
          @include flex($directions: column);
          .cover-3-top,
          .cover-3-bottom {
            @include fontSize($weight: 400);
            @include flex($align: center);
          }
          .cover-3-bottom {
            @include flex();
            margin-top: 10px;
            min-height: 32px;
            height: 60px;
            width: 100%;
            background: var(--cover-3-main);
            position: relative;
            z-index: 2;
            &:before {
              @include pseudo($width: 100%, $height: 100%, $bgColor: transparent, $zIndex: 1);
              border: 1px solid var(--cover-3-main);
              top: 5px;
              left: 5px;
            }
          }
        }
        .cover-rest-content {
          font-size: 36px;
          @include fontSize();
          @include flex();
          background: #008BB7;
          height: 48px;
          width: 100%;
          position: relative;
          &:before {
            @include pseudo($width: 100%, $height: 48px, $bgColor: transparent, $zIndex: 1);
            border: 1px solid #008BB7;
            top: 5px;
            left: 5px;
          }
        }
      }
      .cover-4 {
        width: 80%;
        height: 65%;
        border: 1px solid #00C2FF;
        @include flex();
        &:after {
          @include pseudo($width: 40%, $height: 2px, $bgColor: #00C2FF);
          bottom: 26px;
        }
        .cover-4-main {
          @include flex($justify: center, $align: center);
          @include fontSize($weight: 400, $color: #00C2FF);
          text-shadow: 0 0 0 rgb(-61, 135, 194), -1px -1px 0 rgb(-87, 109, 168), -2px -2px 0 rgb(-112, 84, 143), -3px -3px 0 rgb(-137, 59, 118), -4px -4px 0 rgb(-163, 33, 92), -5px -5px 0 rgb(-188, 8, 67), -6px -6px 0 rgb(-213, -17, 42), -7px -7px 6px rgba(0, 0, 0, 0.15), -7px -7px 1px rgba(0, 0, 0, 0.5), 0px 0px 6px rgba(0, 0, 0, .2);
          line-height: 1.4;
        }
      }
      .cover-5 {
        width: 75%;
        height: 65%;
        @include flex();
        &-main {
          @include flex();
          @include fontSize($color: #00C2FF, $weight: 500);
          width: 100%;
          border: 1px solid #ffffff;
          position: relative;
          background: var(--cover-5-main);
          padding: 20px 10px;
          box-sizing: content-box;
          line-height: 1.3;
          &:before {
            @include pseudo($zIndex: -1, $width: 100%, $height: 100%, $bgColor: #00C2FF);
            top: -8px;
            left: -8px;
          }
          &-top,
          &-left,
          &-right,
          &-bottom {
            display: block;
            position: absolute;
            z-index: 10;
            width: 8px;
            height: 8px;
            background: #ffffff;
            border: 1px solid #00C2FF;
          }
          &-top {
            top: -4px;
            left: -4px;
          }
          &-left {
            bottom: -4px;
            left: -4px;
          }
          &-right {
            top: -4px;
            right: -4px;
          }
          &-bottom {
            bottom: -4px;
            right: -4px;
          }
        }
      }
      .cover-6 {
        &:before {
          @include pseudo($width: 40%, $height: 2px, $bgColor: #00C2FF);
          top: 10px;
          left: 16px;
        }
        &:after {
          @include pseudo($width: 20px, $height: 2px, $bgColor: #ffffff);
          bottom: 12px;
          right: 100px;
        }
        @for $i from 1 through 4 {
          &-float-#{$i} {
            position: absolute;
            width: 20px;
            height: 2px;
            background: #00C2FF;
            opacity: .8;
            z-index: -1;
          }
        }
        &-float-1 {
          top: 55%;
          left: 30%;
        }
        &-float-2 {
          top: 65%;
          left: 40%;
        }
        &-float-3 {
          top: 75%;
          left: 57%;
        }
        &-float-4 {
          top: 85%;
          right: 20%;
        }
        .content-wrapper {
          @include flex($directions: column, $align: flex-start);
          @include contentWrapper();
          .cover-6-top {
            @include fontSize($color: #ffffff, $weight: 400);
            text-shadow: 2px 1px 1px #00C2FF;
            margin-left: .5em;
          }
          .cover-6-bottom {
            margin: 10px .5em 0 auto;
            @include fontSize($color: #ffffff, $weight: 400);
            text-shadow: 3px 0 2px #00C2FF;
          }
        }
        .cover-rest-content {
          background: #000;
          @include contentWrapper();
          @include flex();
          @include fontSize();
        }
      }
      .cover-7 {
        &:before {
          @include pseudo($width: 18px, $height: 16px, $bgColor: transparent);
          border-top: 2px solid #00C2FF;
          border-left: 2px solid #00C2FF;
          top: 0;
          left: -10px;
        }
        &:after {
          @include pseudo($width: 18px, $height: 16px, $bgColor: transparent);
          border-bottom: 2px solid #00C2FF;
          border-right: 2px solid #00C2FF;
          bottom: 0;
          right: -10px;
        }
        .content-wrapper {
          @include contentWrapper();
          .cover-7-main {
            @include flex($directions: column, $justify: center, $align: center);
            height: 100%;
            &-top {
              @include fontSize();
              @include flex();
              line-height: 1.2;
            }
            &-middle {
              @include fontSize();
              @include flex();
              text-shadow: -3px 0 1px #00C2FF;
              position: relative;
              line-height: 1.5;
              &:after {
                @include pseudo($width: 160px, $height: 6px, $bgColor: var(--cover-7-middle));
                top: 40%;
                left: 50%;
                transform: translate(-50%, -50%);
              }
            }
            &-bottom {
              line-height: 1.5;
              @include fontSize();
              @include flex();
            }
          }
        }
        .cover-rest-content {
          font-size: 32px;
          @include fontSize();
        }
      }
    }
    .tutorial-card-icon {
      font-size: 20px;
      position: absolute;
      right: 8px;
      top: 8px;
      color: white;
      &.selected {
        fill: #00C2FF
      }
    }
    .tutorial-card-hover {
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10;
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(18, 19, 20, 0.78);
      color: #ffffff;
      &-icon {
        font-size: 32px;
        &.media {
          margin-left: 20px;
        }
      }
    }
  }
  .tutorial-card-footer {
    margin: 10px auto 0 auto;
    width: 90%;
    display: flex;
    align-items: center;
    &-title {
      color: var(--seatom-type-700);
      font-family: PingFang SC;
      font-style: normal;
      font-weight: normal;
      font-size: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      user-select: none;
    }
    .edit-button {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: auto;
      border-radius: 4px;
      padding: 4px 8px;
      color: #ffffff;
      font-size: 14px;
      user-select: none;
      cursor: pointer;
      &-icon {
        font-size: 16px;
        margin-right: 2px;
      }
      &:hover {
        background: #3D84FF;
      }
    }
  }
}
</style>
