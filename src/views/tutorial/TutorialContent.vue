<template>
  <div class="tutorial-content-container">
    <div class="tutorial-content-header">
      <el-button
        v-if="isAdmin"
        type="primary"
        class="tutorial-content-header-create"
        @click="createTutorialVisible = true">
        <hz-icon name="icon-plus" class="block-16"/>
        新建
      </el-button>
      <el-button
        v-if="isAdmin"
        type="primary"
        class="tutorial-content-header-create is-plain"
        @click="deleteTutorial">
        <hz-icon name="trash" class="block-16"/>
        删除
      </el-button>
      <div class="tutorial-content-header-searchBox" :class="!isAdmin ? 'admin' : ''">
        <el-input
          placeholder="请输入搜索内容"
          size="mini"
          prefix-icon="el-icon-search"
          @input="searchChangeEvent"
          v-model="searchValue"
          class="input-theme"/>
      </div>
    </div>
    <div class="tutorial-content-tabs">
      <el-checkbox
        v-if="isAdmin"
        v-model="allSelected"
        class="tutorial-content-tabs-select"
      >
        全选
      </el-checkbox>
      <ul class="tutorial-content-tabs-label" :class="isAdmin ? 'admin' : ''">
        <li
          :class="['tutorial-content-tabs-label-item',tagIndex === index ? 'selected':'']"
          v-for="(label,index) in markList"
          :key="index"
          @click="changeTagName(index,label)"
        >
          {{ label }}
        </li>
      </ul>
    </div>
    <div class="tutorial-content-main">
      <TutorialCard
        v-for="(item,index) in tutorialList" :key="index"
        :index="index"
        :item="item"
        :is-admin="isAdmin"
        :selected-list="selectedList"
        class="tutorial-content-main-card"
        @selectEvent="selectEvent"
        @showTutorialEvent="showTutorialEvent"
        @showVideoEvent="showVideoEvent"
        @editTutorial="editTutorialEvent"
      />
    </div>
    <el-dialog
      v-if="isAdmin"
      title="新建文档"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      class="tutorial-content-dialog create-tutorial"
      :visible.sync="createTutorialVisible"
      fullscreen
      top="0"
      :before-close="handleClose">
      <div class="tutorial-content-dialog-title">
        <div class="title">教程标题 :</div>
        <el-input
          class="title-input"
          v-model="formData.title"
          :maxlength="computedMaxLength"
          show-word-limit
        />
      </div>
      <div class="tutorial-content-dialog-cover">
        <div class="title">教程封面 :</div>
        <div class="cover-wrapper">
          <img
            class="cover"
            v-for="(item,index) in coverList"
            :key="index"
            :src="item"
            :class="activeCoverIndex === index ? 'active' : ''"
            @click="selectCover(index)"
            alt=""
          >
        </div>
      </div>
      <div class="tutorial-content-dialog-keyword" v-if="(formData.coverStyle !== 4) && (formData.coverStyle !== 5)">
        <div class="title">
          <el-tooltip effect="dark" placement="top" :content="computedKeywordTip">
            <div class="tip">
              <i class="el-icon-question icon"/>
              关键词 :
            </div>
          </el-tooltip>
        </div>
        <div class="keyword-wrapper">
          <el-input
            class="keyword-input"
            placeholder="请输入关键词"
            v-model="formData.keyword"
            :maxlength="computedKeywordLength"
            @blur="validatorKeyword"
            show-word-limit
          />
          <span class="warning-tip" v-show="tipVisible">
            {{ tipContent }}
          </span>
        </div>
      </div>
      <div class="tutorial-content-dialog-editor">
        <div class="title">教程文档 :</div>
        <TEditor v-model="formData.content" class="editor" :tutorial-id="tutorialId"/>
      </div>
      <div class="tutorial-content-dialog-video">
        <div class="title">视频教程 :</div>
        <el-upload
          :class="['upload-demo',!formData.videoPath ? '' : 'isHidden']"
          drag
          ref="uploadVideo"
          accept="video/*"
          :headers="uploadHeaders"
          :on-change="addFile"
          :action="videoUploadUrl"
          :on-success="uploadSuccess"
          :on-error="uploadError"
          :on-remove="uploadRemove"
          :limit="1"
          :auto-upload="false"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>
            <div>视频小于500MB!</div>
          </div>
        </el-upload>
        <div class="video-wrapper" v-show="formData.videoPath">
          <video controls autopla :src="videoUrl(formData.videoPath)" class="video"/>
          <div class="video-action-wrapper">
            <el-button
              type="primary"
              class="delete"
              @click="replaceVideo">
              <hz-icon name="trash" class="block-16"/>
              替换
            </el-button>
            <el-button
              type="primary"
              class="delete"
              @click="deleteVideo">
              <hz-icon name="trash" class="block-16"/>
              删除
            </el-button>
          </div>
        </div>
      </div>
      <div class="tutorial-content-dialog-folder">
        <div class="title">选择文件夹 :</div>
        <el-cascader
          ref="folderOptions"
          class="options"
          v-model="folderValue"
          :options="folderList"
          :props="{ checkStrictly: true, label: 'name',value:'id',expandTrigger:'hover' }"
          @change="folderValueChange"
          clearable
        />
        <div class="createFolder" @click="$store.commit('tutorial/setCreateFolderVisible',true)">
          <hz-icon name="plus"/>
          新建文件夹
        </div>
      </div>
      <div class="tutorial-content-dialog-label">
        <div class="title">选择标签 :</div>
        <div class="tag-wrapper">
          <el-tag
            class="tag"
            :key="index"
            v-for="(tag,index) in formData.mark"
            closable
            effect="dark"
            :disable-transitions="false"
            @close="handleCloseTag(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-autocomplete
            ref="saveTagInput"
            class="input-new-tag"
            v-if="tagInputVisible"
            v-model="tagInputValue"
            placeholder="请输入内容"
            size="small"
            :fetch-suggestions="querySearch"
            :popper-class="'popper-class'"
            @select="handleInputConfirm"
            @keyup.enter.native="handleInputConfirm"
          />
          <el-button v-else class="button-new-tag" size="small" @click="showInput">
            <hz-icon name="plus"/>
            新增标签
          </el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="dialog-button" @click="handleClose">取 消</el-button>
        <el-button class="dialog-button dialog-button-right" type="primary" @click="confirmTutorial(tutorialId)">确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TutorialCard from '@/views/tutorial/TutorialCard'
import TEditor from '@/components/documents/TEditor'
import { tutorialDeleteFile } from '@/api/tutorial'
import { mapGetters } from 'vuex'
import { replaceUrl, getHttpHeaders } from '@/utils/base'

function debounce (fn, delay) {
  let timer
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => fn.apply(this, args), delay)
  }
}

const validator = {
  0: '该封面的关键词仅支持标题中的词，不支持标题首或标题尾的词。',
  1: '该封面的关键词仅支持以标题首位开始的词，不支持标题中间或标题尾的词。',
  2: '该封面的关键词仅支持以标题首位开始的词，不支持标题中间或标题尾的词。',
  3: '该封面的关键词仅支持标题结尾的词，不支持标题首或标题中的词。',
  6: '该封面的关键词仅支持标题结尾的词，不支持标题首或标题中的词。',
  7: '该封面的关键词仅支持标题中的词，不支持标题首或标题尾的词。'
}

export default {
  name: 'TutorialContent',
  components: {
    TutorialCard,
    TEditor
  },
  props: {
    isAdmin: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapGetters('tutorial', ['coverList', 'folderId', 'markList', 'title', 'marks', 'tutorialList', 'folderList']),
    ...mapGetters('user', ['userId']),
    videoUrl () {
      return data => data && replaceUrl(process.env.VUE_APP_SERVER_URL + data)
    },
    computedKeywordTip () {
      return validator[this.formData.coverStyle]
    },
    computedMaxLength () {
      return this.formData.coverStyle === 6 ? 10 : 20
    },
    computedKeywordLength () {
      switch (this.formData.coverStyle) {
        case 0:
          return 2
        case 1 :
          return 6
        case 2 :
          return 3
        case 6:
        case 7:
          return 4
        case 3:
          return 10
        default :
          return 20
      }
    }
  },
  watch: {
    folderId () {
      this.$store.dispatch('tutorial/getMarkList')
      this.$store.dispatch('tutorial/getTutorialList')
    },
    title () {
      this.$store.dispatch('tutorial/getTutorialList')
    },
    marks () {
      this.$store.dispatch('tutorial/getTutorialList')
    },
    allSelected (val) {
      val ? this.selectedAllDoc() : this.selectedList = []
    },
    tutorialList: {
      handler () {
        this.selectedList = []
        this.allSelected = false
      },
      deep: true
    },
    'formData.coverStyle' () {
      if (this.createTutorialVisible) { this.validatorKeyword() }
    },
    computedMaxLength () {
      if (this.createTutorialVisible) this.formData.title = this.formData.title.slice(0, this.computedMaxLength)
    },
    async createTutorialVisible (val) {
      if (val) {
        const condition = this.tutorialId || 'NEW_TUTORIAL_DRAFT'
        const storage = localStorage.getItem(condition)
        const draft = storage || await this.$store.dispatch('tutorial/getTutorialDraft', {
          userId: this.userId,
          docId: condition
        })
        if (draft) {
          this.$confirm('系统检测到有文档草稿,是否选择加载上一次保存的数据?',
            {
              title: '提示',
              type: 'info',
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            })
            .then(() => {
              this.formData.content = draft
            })
        }
      }
    },
    markList: {
      handler (val) {
        this.genSuggestions(val)
      },
      deep: true
    }
  },
  data () {
    return {
      createTutorialVisible: false,
      tagInputVisible: false, // 创建标签
      tagInputValue: '',
      selectedList: [], // 删除文件的ids数组
      allSelected: false,
      tagIndex: 0, // 选中标签项
      searchValue: '',
      videoUploadUrl: replaceUrl(process.env.VUE_APP_SERVER_URL + '/api/guide/upload'), // 视频上传地址
      uploadHeaders: getHttpHeaders(),
      activeCoverIndex: -1,
      uploadVideo: {
        state: false
      },
      tutorialId: '',
      folderValue: -1, // 所属文件夹id
      formData: {
        videoPath: '', // 视频地址
        coverStyle: -1, // 封面样式编号
        title: '', // 标题
        mark: [], // 标签
        folderId: '', // 文件夹
        content: '', // 文档内容
        keyword: '' // 关键词
      },
      tipVisible: false,
      tipContent: '',
      keywordMaLength: 0,
      suggestions: []
    }
  },
  created () {
    this.initData()
  },
  methods: {
    async initData () {
      await this.$store.dispatch('tutorial/getMarkList')
      await this.$store.dispatch('tutorial/getCoverList')
      await this.$store.dispatch('tutorial/getTutorialList')
    },
    changeTagName (index, label) {
      this.tagIndex = index
      this.$store.commit('tutorial/setMarks', label === '全部' ? '' : label)
    },
    // el-input 节流需要这样写,函数内部this需要用function传入
    searchChangeEvent: debounce(function (val) {
      this.$store.commit('tutorial/setTitle', val)
    }, 200),
    // 全选文档
    selectedAllDoc () {
      this.tutorialList.map(item => this.selectedList.push(item.id))
    },
    validatorKeyword () {
      const { keyword, title, coverStyle } = this.formData
      if (coverStyle !== 5 && (coverStyle !== 4)) {
        if (!keyword || !keyword.trim()) {
          this.$message.warn('请输入关键词')
          this.tipVisible = true
          return false
        }
        if (!title.includes(keyword)) {
          this.$message.warn('请输入标题中的词汇充当关键词。')
          this.tipVisible = true
          return false
        }
      }
      switch (coverStyle) {
        // 关键词必须是标题中间的词
        case 0:
        case 7:
          this.tipContent = '该封面的关键词仅支持标题中的词，不支持标题首或标题尾的词。'
          if (title.endsWith(keyword)) {
            this.$message.warn('该封面的关键词不支持以标题结尾的词，请将关键词替换为标题中的词。')
            this.tipVisible = true
            return false
          } else if (title.startsWith(keyword)) {
            this.$message.warn('该封面的关键词不支持以标题首位开始的词，请将关键词替换为标题中的词。')
            this.tipVisible = true
            return false
          }
          this.tipContent = ''
          this.tipVisible = false
          return true
        // 关键词必须是标题首位开始的词
        case 1:
        case 2:
          this.tipContent = '该封面的关键词仅支持以标题首位开始的词，不支持标题中或标题尾的词。'
          if (title.endsWith(keyword)) {
            this.$message.warn('该封面的关键词不支持以标题结尾的词，请将关键词替换以标题首位开始的词。')
            this.tipVisible = true
            return false
          } else if (!title.startsWith(keyword)) {
            this.$message.warn('该封面的关键词不支持标题中的词，请将关键词替换以标题首位开始的词。')
            this.tipVisible = true
            return false
          }
          this.tipContent = ''
          this.tipVisible = false
          return true
        // 关键词必须是标题结尾的词
        case 3:
        case 6:
          this.tipContent = '该封面的关键词仅支持标题结尾的词，不支持标题首或标题中的词。'
          if (!title.endsWith(keyword)) {
            this.$message.warn('该封面的关键词仅支持以标题结尾的词，请将关键词替换以标题结尾的词的词。')
            this.tipVisible = true
            return false
          } else if (title.startsWith(keyword)) {
            this.tipVisible = true
            this.$message.warn('该封面的关键词仅支持以标题结尾的词，请将关键词替换以标题结尾的词的词。')
            return false
          }
          this.tipContent = ''
          return true
        case 4:
        case 5:
          this.tipContent = ''
          this.tipVisible = false
          return true
      }
    },
    // 创建文档
    async confirmTutorial (id) {
      if (!this.formData.title?.trim() || !this.formData.title) {
        this.$message('请输入标题!')
        return
      }
      if (this.formData.coverStyle < 0) {
        this.$message('请选择封面图!')
        return
      }
      if (!this.formData.folderId) {
        this.$message('请选择所属文件夹!')
        return
      }
      if (!this.formData.videoPath && !this.formData.content) {
        if (this.formData.content?.trim() || !this.formData.content) {
          this.$message('视频和文档至少选择其一并填写有效文字!')
          return
        }
      }
      if (this.isUpload) {
        this.$message('视频正在上传中，请等待视频上传后再提交。')
        return
      }
      if (!this.validatorKeyword()) return
      id ? await this.$store.dispatch('tutorial/updateTutorial', this.formData)
        : await this.$store.dispatch('tutorial/createTutorial', this.formData)
      this.createTutorialVisible = false
      this.formData = {}
      this.activeCoverIndex = -1
      this.folderValue = 0
      this.tutorialId = ''
    },
    deleteTutorial () {
      if (!this.selectedList.length) return
      this.$confirm('确认删除文档？', { title: '提示', type: 'warning' })
        .then(async _ => {
          const data = { ids: this.selectedList }
          await this.$store.dispatch('tutorial/deleteTutorial', data)
        })
        .catch(_ => {})
    },
    handleClose () {
      this.$confirm('确认关闭？', { title: '提示', type: 'warning' })
        .then(async _ => {
          if (this.formData.content) {
            await this.$store.dispatch('tutorial/saveTutorialDraft', {
              userId: this.userId,
              docId: this.tutorialId || 'NEW_TUTORIAL_DRAFT',
              content: this.formData.content
            })
          }
          this.createTutorialVisible = false
          this.formData = {}
          this.folderValue = -1
          this.activeCoverIndex = -1
          this.tutorialId = ''
          this.tipVisible = false
          this.tipContent = ''
        })
        .catch(_ => {})
    },
    // 创建标签
    genSuggestions (arr) {
      const temp = _.cloneDeep(arr)
      this.suggestions = temp.reduce((acc, cur) => {
        if (cur !== '全部') {
          const target = { value: cur }
          acc.push(target)
        }
        return acc
      }, [])
    },
    querySearch (queryString, cb) {
      const suggestions = this.suggestions
      const results = queryString ? suggestions.filter(this.createFilter(queryString)) : suggestions
      cb(results)
    },
    createFilter (queryString) {
      return (suggestions) => {
        return (suggestions.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    handleCloseTag (tag) {
      this.formData.mark.splice(this.formData.mark.indexOf(tag), 1)
    },
    showInput () {
      this.tagInputVisible = true
      this.$nextTick(_ => this.$refs.saveTagInput.$refs.input.focus())
    },
    handleInputConfirm () {
      const tagInputValue = this.tagInputValue
      if (tagInputValue) {
        this.formData.mark.indexOf(tagInputValue) === -1 ? this.formData.mark.push(tagInputValue)
          : this.$message('请不要输入同一个标签名!')
      }
      this.tagInputVisible = false
      this.tagInputValue = ''
    },
    // 上传视频
    addFile (file, fileList) {
      if (!fileList.length) return
      this.isUpload = true
      const isLt500M = file.size / 1024 / 1024 < 500
      const temp = fileList.filter((item) => item.name === file.name).length
      if (temp > 1) {
        this.$message.error('文件添加重复')
        this.isUpload = false
        fileList.pop()
        return
      }
      if (['video/mp4', 'video/webm'].indexOf(file.raw.type) === -1) {
        this.$message.error('现在仅支持 .mp4 和 .webm 格式的视频文件上传!')
        this.isUpload = false
        fileList.pop()
        return
      }
      if (!isLt500M) {
        this.$message.error('上传视频大小不能超过500MB哦!')
        this.isUpload = false
        fileList.pop()
        return
      }
      this.$refs.uploadVideo.submit()
    },
    uploadSuccess (res) {
      if (this.formData.videoPath) {
        const data = { name: this.formData.videoPath.split('guide/')[1] }
        tutorialDeleteFile(data)
      }
      if (res && res.success) {
        this.$refs.uploadVideo.clearFiles()
        this.$message('上传视频成功!')
        this.isUpload = false
        this.formData.videoPath = res.data.url
      }
    },
    uploadError () {
      this.isUpload = false
      this.$message.warn('上传视频失败!')
    },
    uploadRemove () {
      this.isUpload = false
    },
    replaceVideo () {
      this.$refs.uploadVideo.$children[0].$refs.input.click()
    },
    deleteVideo () {
      this.$confirm('确认删除？', { title: '提示', type: 'warning' })
        .then(async _ => {
          const data = { name: this.formData.videoPath.split('guide/')[1] }
          const res = await tutorialDeleteFile(data)
          if (res && res.success) {
            this.$message('删除视频成功!')
            this.formData.videoPath = ''
          }
        })
        .catch(_ => {})
    },
    // 选择封面
    selectCover (index) {
      this.activeCoverIndex = index
      this.formData.coverStyle = index
    },
    folderValueChange (selectedArray) {
      this.formData.folderId = selectedArray[selectedArray.length - 1]
      this.$refs.folderOptions.dropDownVisible = false
    },
    // card事件处理
    selectEvent (data) {
      const { id, state } = data
      const temp = _.cloneDeep(this.selectedList)
      const index = temp.indexOf(id)
      state ? temp.push(id) : temp.splice(index, 1)
      this.selectedList = temp
    },
    showTutorialEvent (data) {
      const { item } = data
      const url = this.$router.resolve({
        path: '/tutorial/preview',
        query: {
          tutorialId: item.id,
          showMode: 'doc'
        }
      })
      window.open(url.href, '_blank')
    },
    closePreview () {
      this.formData = {}
      this.$router.push({ path: this.$route.path })
      this.tutorialVisible = false
    },
    showVideoEvent (data) {
      const { item } = data
      const url = this.$router.resolve({
        path: '/tutorial/preview',
        query: {
          tutorialId: item.id,
          showMode: 'video'
        }
      })
      window.open(url.href, '_blank')
    },
    editTutorialEvent (data) {
      const { state, item } = data
      this.tutorialId = item.id
      this.createTutorialVisible = state
      this.formData = item
      this.folderValue = item.folderId
      this.activeCoverIndex = item.coverStyle
    }
  }
}
</script>

<style lang="scss" scoped>
* {list-style: none;user-select: none;}
@mixin flex($justify:flex-start,$align:flex-start,$directions:row) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
  flex-direction: $directions;
}
.is-plain {
  background: transparent !important;
  border-color: #409EFF;
  color: #409EFF;
  &:hover {
    color: white;
  }
}
.tutorial-content-container {
  width: 100%;
  .tutorial-content-header {
    @include flex($align: center);
    &-create {
      @include flex($align: center, $justify: center);
      height: 32px;
    }
    &-searchBox {
      margin-left: auto;
      margin-right: 32px;
      width: 245px;
      &.admin {
        margin: 0;
      }
    }
  }
  .tutorial-content-tabs {
    margin-top: 14px;
    @include flex($align: flex-start);
    &-select {
      @include flex($align: center);
      color: rgba(255, 255, 255, 0.7);
      font-family: "思源黑体Medium";
      font-style: normal;
      font-weight: 600;
      height: 30px;
      margin-right: 20px;
      margin-top: 10px;
    }
    &-label {
      @include flex();
      color: var(--seatom-type-800);
      border-radius: 8px;
      font-family: "思源黑体Medium";
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      flex-wrap: wrap;
      width: 100%;
      //width: calc(100% - 50px);
      //height: 45px;
      //overflow: hidden;
      &-item {
        padding: 5px 10px;
        height: 30px;
        margin-right: 20px;
        margin-top: 10px;
        @include flex($justify: center, $align: center);
        user-select: none;
        cursor: pointer;
        border-radius: 6px;
        &.selected {
          color: var(--seatom-primary-900);
          background: var(--seatom-primary-a100);
        }
        &:hover {
          background: var(--seatom-mono-a100);
        }
      }
    }
    &-searchBox {
      margin-left: auto;
      margin-right: 32px;
      width: 245px;
      border: 1px solid;
    }
  }
  .tutorial-content-main {
    margin-top: 20px;
    margin-left: -26px;
    @include flex();
    flex-wrap: wrap;
    .tutorial-content-main-card {
      margin: 0 16px 32px 16px;
    }
  }
  .tutorial-content-dialog {
    &-belong, &-input {
      @include flex($align: center);
      margin-top: 20px;
      div {
        width: 8em;
      }
      .rest {
        margin-left: 10px;
        flex: 1;
      }
    }
    &-editor, &-title, &-label, &-folder, &-cover, &-keyword, &-video {
      width: 100%;
      margin-top: 20px;
      @include flex($align: flex-start);
      .title-input {
        width: 420px;
      }
      .editor {
        width: 1080px;
      }
      .title {
        height: 40px;
        width: 8em;
        flex-shrink: 0;
        flex-grow: 0;
        color: rgba(255, 255, 255, .7);
        @include flex($align: center);
      }
      .options {
        width: 420px;
      }
      .createFolder {
        margin-left: 20px;
        color: rgba(255, 255, 255, .7);
        cursor: pointer;
        flex-shrink: 0;
        height: 40px;
        @include flex($justify: center, $align: center);
      }
      .tag-wrapper {
        @include flex($justify: flex-start, $align: center);
        flex: 1;
        flex-flow: row wrap;
        height: 100%;
        width: 100%;
        max-width: 1080px;
        .tag {
          margin-top: 5px;
          margin-right: 10px;
          font-size: 14px;
          height: 32px;
        }
        .button-new-tag, .input-new-tag {
          height: 32px;
          width: 8em;
          margin-top: 5px;
          font-size: 14px;
          @include flex($justify: center, $align: center);
        }
      }
      .cover-wrapper {
        width: 100%;
        max-width: 1080px;
        @include flex();
        flex-flow: row wrap;
        .cover {
          user-select: none;
          width: 262px;
          height: 160px;
          margin: 0 6px 6px 0;
          &:hover, &.active {
            border: 1px solid #5291FF;
          }
        }
      }
      .keyword-wrapper {
        @include flex($justify: center, $align: flex-start, $directions: column);
        .keyword-input {
          width: 420px;
          height: 100%;
        }
        .warning-tip {
          margin-top: 8px;
          font-size: 12px;
          color: #FF475D;
          line-height: 1;
          height: 12px;
          margin-left: 16px;
        }
      }
    }
    &-video {
      position: relative;
      .video-wrapper {
        height: 228px;
        position: absolute;
        top: 0;
        left: 112px;
        z-index: 10;
        @include flex($justify: center, $align: flex-start);
        .video {
          height: 180px;
          width: 286px;
          position: relative;
        }
        .video-action-wrapper {
          @include flex();
          position: absolute;
          top: 40%;
          left: 50%;
          transform: translate(-50%, -50%);
          .delete, .replace {
            margin-left: 10px;
            font-size: 16px;
            height: 32px;
            width: 5em;
            @include flex($justify: center, $align: center)
          }
        }
      }
    }
    &-label {flex-wrap: wrap;}
  }
}

::v-deep .create-tutorial {
  .el-dialog.is-fullscreen {
    @include flex($justify: flex-start, $align: center, $directions: column);
    .el-dialog__header {
      width: 100%;
    }
    .el-dialog__body {
      @include flex($justify: center, $align: flex-start, $directions: column);
    }
    .el-dialog__footer {
      .dialog-footer {
        margin: 40px auto;
        @include flex($justify: center, $align: center);
        .dialog-button {
          width: 100px;
          height: 36px;
          @include flex($justify: center, $align: center);
        }
        .dialog-button-right {
          margin-left: 32px;
        }
      }
    }
  }
}

::v-deep .upload-demo {
  width: 286px;
  height: 228px;
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      background: transparent;
      width: 100%;
      border: 1px dashed #c4c3c357;
      border-radius: 4px;
      &:hover {
        border-color: #2681ff;
      }
    }
  }
  .el-upload-list {
    margin-bottom: 8px;
  }
  .el-upload-list__item {
    .el-upload-list__item-name, .el-progress__text {
      color: #ffffff !important;
    }
  }
  .el-upload-list__item:hover {
    background-color: #419eff14;
  }
  .el-upload-list--text {
    max-height: 200px;
    overflow: hidden;
    position: relative;
    z-index: 100;
  }
  &.isHidden {
    .el-upload {
      .el-upload-dragger {
        opacity: 0;
      }
    }
  }
}
::v-deep .el-input .el-input__count .el-input__count-inner {
  background: transparent;
}
</style>
<style lang="scss">
.el-autocomplete-suggestion.el-popper[x-placement^=top].popper-class {
  .popper__arrow {
    border-top-color: white;
    &::after {
      border-top-color: white;
    }
  }
}
.el-autocomplete-suggestion.el-popper[x-placement^=bottom].popper-class {
  .popper__arrow {
    border-top-color: white;
    &::after {
      border-top-color: white;
    }
  }
}
</style>
