<template>
  <div class="tutorial-preview"
       :data-title="tutorialInfo.title"
       v-web-title
  >
    <el-image-viewer
      v-if="showViewer"
      :on-close="closePreview"
      :url-list="imgList"
    />
    <div v-if="showMode === 'doc'" class="tutorial-preview-doc">
      <div class="tutorial-preview-doc-wrapper">
        <h1>{{ tutorialInfo.title }}</h1>
        <div class="update-time">更新时间 : {{ tutorialInfo.updatedAt }}</div>
        <div class="main" ref="tutorialMain"/>
      </div>
    </div>
    <div v-else class="tutorial-preview-video">
      <div class="tutorial-preview-video-wrapper">
        <h1>{{ tutorialInfo.title }}</h1>
        <video ref="videoBox" controls autopla :src="videoUrl(tutorialInfo.videoPath)" class="video"/>
      </div>
    </div>
  </div>
</template>

<script>
import { replaceUrl } from '@/utils/base'
import { mapGetters } from 'vuex'

export default {
  name: 'TutorialPreview',
  components: {
    'el-image-viewer': () => import('element-ui/packages/image/src/image-viewer')
  },
  data () {
    return {
      showMode: 'doc',
      showViewer: false,
      imgList: []
    }
  },
  computed: {
    ...mapGetters('tutorial', ['tutorialInfo']),
    videoUrl () {
      return data => data && replaceUrl(process.env.VUE_APP_SERVER_URL + data)
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    async init () {
      this.showMode = this.$route.query.showMode
      await this.$store.dispatch('tutorial/getTutorialById', { id: this.$route.query.tutorialId })
      // document.title = this.tutorialInfo.title
      if (this.showMode === 'doc') {
        this.$nextTick(() => {
          this.createShadowDom()
        })
      }
    },
    createShadowDom () {
      const mountPoint = document.createElement('div')
      const container = this.$refs.tutorialMain
      const style = document.createElement('style')
      style.textContent = '*{line-height:1.5;font-size:14px;}'
      container.attachShadow({ mode: 'open' })
      container.append(style)
      container.appendChild(mountPoint)
      const string = this.tutorialInfo.content.replaceAll('../', `${replaceUrl('/', null, true)}`)
      container.shadowRoot.innerHTML += string
      container.shadowRoot.addEventListener('click', this.previewEvent)
    },
    previewEvent (e) {
      if (e.target.nodeName === 'IMG') {
        this.showViewer = true
        this.imgList = [e.target.currentSrc]
      }
    },
    closePreview () {
      this.showViewer = false
      this.imgList = []
    }
  },
  beforeDestroy () {
    this.$refs.tutorialMain.shadowRoot.removeEventListener('click', this.previewEvent)
  }
}
</script>

<style lang="scss" scoped>
* {list-style: none;}
@mixin flex($justify:flex-start,$align:flex-start,$directions:row) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
  flex-direction: $directions;
}
h1 {
  font-size: 30px;
  color: #000000;
  margin: 56px 0 16px 0;
  font-family: 思源黑体Medium;
  font-style: normal;
  font-weight: bold;
}
.tutorial-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
  &-doc {
    width: 100%;
    height: 100%;
    background: #F1F1F3 !important;
    overflow: auto;
    &-wrapper {
      @include flex($justify: flex-start, $align: center, $directions: column);
      width: 100%;
      min-height: 100%;
      max-width: 1080px;
      background: #ffffff;
      padding: 0 100px 50px 100px;
      margin: 0 auto;
      h1, div {
        width: 100%;
        max-width: 1080px;
      }
      .update-time {
        color: rgba(21, 22, 24, 0.48);
      }
      .main {
        margin-top: 24px;
      }
    }
  }
  &-video {
    width: 100%;
    height: 100%;
    background: #F1F1F3 !important;
    overflow: auto;
    &-wrapper {
      width: 100%;
      min-height: 100%;
      max-width: 1080px;
      background: #ffffff;
      padding: 0 100px;
      margin: 0 auto;
      @include flex($justify: flex-start, $align: center, $directions: column);
      .video {
        margin-top: 20px;
        width: 100%;
        max-width: 1080px;
      }
    }
  }
}
</style>
