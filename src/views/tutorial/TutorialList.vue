<template>
  <div class="tutorial-list" ref="tutorialListContainer">
    <el-tree
      :data="copyFolderList"
      :expand-on-click-node="true"
      :props="defaultProps"
      :render-content="renderContent"
      class="tutorial-list-tree"
      node-key="id"
      ref="tutorialTree"
      :default-expanded-keys="expendArray"
      @node-expand="handleNodeExpand"
      @node-collapse="handleNodeCollapse"
      @node-drop="handleDrop"
      :draggable="isAdmin"
    />
    <el-dialog
      v-if="isAdmin"
      class="tutorial-content-dialog"
      title="新建文件夹"
      :visible.sync="createFolderVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :before-close="cancelAdd"
      :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
      width="30%"
      top="0">
      <div class="tutorial-content-dialog-belong">
        <div class="form-theme">
          所属文件夹
        </div>
        <el-cascader
          ref="folderOptions"
          class="rest input-theme"
          popper-class="poper-theme"
          v-model="folderValue"
          :options="folderList"
          :props="{ checkStrictly: true, label: 'name',value:'id' ,expandTrigger:'hover'}"
          @change="folderValueChange"
          clearable/>
      </div>
      <div class="tutorial-content-dialog-input">
        <div class="form-theme">
          文件夹名称
        </div>
        <el-input class="rest" v-model="addInputValue"/>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="dialog-button" @click="cancelAdd">取 消</el-button>
        <el-button class="dialog-button" type="primary" @click="confirmAdd">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="isAdmin"
      class="tutorial-content-dialog"
      title="重命名文件夹"
      :visible.sync="editDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="30%"
      top="0">
      <!--      <div class="tutorial-content-dialog-belong">-->
      <!--        <div>-->
      <!--          所属文件夹-->
      <!--        </div>-->
      <!--        <el-cascader-->
      <!--          class="rest"-->
      <!--          :options="treeData"-->
      <!--          :props="{ checkStrictly: true, label: 'name',value:'id' }"-->
      <!--          clearable/>-->
      <!--      </div>-->
      <div class="tutorial-content-dialog-input">
        <div>
          文件夹名称
        </div>
        <el-input class="rest" v-model="editInputValue"/>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="dialog-button" @click="cancelEdit">取 消</el-button>
        <el-button class="dialog-button" type="primary" @click="confirmEdit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TutorialList',
  props: {
    isAdmin: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      editDialogVisible: false,
      addInputValue: '',
      editInputValue: '',
      hoverId: -1,
      activeId: -1,
      folderValue: -1,
      parentID: -1,
      editId: -1,
      clickId: -1,
      copyFolderList: [],
      expendArray: [1]
    }
  },
  computed: {
    ...mapGetters('tutorial', ['folderId', 'title', 'marks', 'folderList', 'createFolderVisible'])
  },
  watch: {
    folderList: {
      handler (val) {
        this.resetTreeActive(this.$refs.tutorialTree.$el.childNodes)
        this.folderValue = -1
        this.parentID = -1
        this.editId = -1
        this.clickId = -1
        this.activeId = -1
        this.hoverId = -1
        this.copyFolderList = _.cloneDeep(val)
      },
      deep: true
    }
  },
  created () {
    this.$store.dispatch('tutorial/getFolderList')
  },
  methods: {
    handleNodeExpand (data) {
      let flag = false
      this.expendArray.some(item => {
        if (item === data.id) {
          flag = true
          return true
        }
      })
      if (!flag) this.expendArray.push(data.id)
    },
    handleNodeCollapse (data) {
      this.expendArray.some((item, i) => {
        if (item === data.id) {
          this.expendArray.splice(i, 1)
        }
      })
    },
    handleDrop (current, target, position) {
      this.$store.dispatch('tutorial/sortFolderList', {
        folderId: current.data.id,
        parentID: position === 'inner' ? target.data.id : target.data.parentID,
        treeList: this.copyFolderList
      })
    },
    renderContent (h, { node, data, store }) {
      if (this.isAdmin) {
        return (
          <span
            class={['custom-node', this.activeId === data.id ? 'active' : '']}
            on-mouseenter={() => this.mouseenterEvent(data, store)}
            on-mouseleave={() => this.mouseleaveEvent()}
            on-click={(e) => this.handleNodeClick(e, data)}>
            {data.name}
            <div class="custom-node-button-wrapper">
              <div class="custom-node-button"
                v-show={data.id !== 1 && (this.hoverId === data.id || this.activeId === data.id)}
                on-click={e => this.editFolder(e, node)}
              >
                <hz-icon name="tutorial-edit" class="icon block-16"/>
              </div>
              <div class="custom-node-button"
                v-show={data.id !== 1 && (this.hoverId === data.id || this.activeId === data.id)}
                on-click={e => this.deleteFolder(e, node)}
              >
                <hz-icon name="trash" class="icon block-16"/>
              </div>
              <div class="custom-node-button"
                v-show={data.id === 1}
                on-click={e => this.addFolder(e, data)}>
                <hz-icon name="icon-plus" class="icon block-16"/>
              </div>
            </div>
          </span>
        )
      } else {
        return (
          <span
            class={['custom-node', this.activeId === data.id ? 'active' : '']}
            on-mouseenter={() => this.mouseenterEvent(data, store)}
            on-mouseleave={() => this.mouseleaveEvent()}
            on-click={(e) => this.handleNodeClick(e, data)}>
            {data.name}
          </span>
        )
      }
    },
    resetTreeActive (root) {
      for (let i = 0; i < root.length; i++) {
        if (root[i].childNodes.length > 0) {
          root[i].childNodes.forEach(item => {
            if (item.className === 'el-tree-node__content') {
              item.parentNode.style.background = 'transparent'
            }
          })
          this.resetTreeActive(root[i].childNodes)
        }
      }
    },
    async handleNodeClick (e, data) {
      if (data.id === 1) this.$refs.tutorialTree.store.nodesMap[data.id].expanded = false
      this.activeId = -1
      const root = this.$refs.tutorialTree.$el.childNodes
      await this.resetTreeActive(root)
      this.$nextTick(() => {
        this.$nextTick(() => {
          if (e.target.className.includes('active')) {
            e.target.parentNode.parentNode.style.background = '#3D85FF'
          }
        })
      })
      let flag
      this.folderList.forEach(item => { if (item.id === data.id) flag = true })
      if (!flag && !data.children) this.activeId = data.id
      if (this.clickId !== data.id) {
        this.clickId = data.id
        this.$store.commit('tutorial/setFolderId', data.id)
      }
    },
    mouseenterEvent (leaf) {
      this.hoverId = leaf.id
    },
    mouseleaveEvent () {
      this.hoverId = -1
    },
    folderValueChange (val) {
      this.parentID = val[val.length - 1]
      this.$refs.folderOptions.dropDownVisible = false
    },
    addFolder (e) {
      e.stopPropagation()
      this.$store.commit('tutorial/setCreateFolderVisible', true)
    },
    deleteFolder (e, node) {
      e.stopPropagation()
      this.$confirm('确认删除？', { title: '提示', type: 'warning' })
        .then(async _ => {
          if (node.data?.children?.length > 0) {
            this.$message.error('该文件夹下有文件夹，无法删除，请清空文件夹后再尝试。')
            return
          }
          const data = { id: node.data.id, name: node.data.name }
          await this.$store.dispatch('tutorial/deleteFolder', data)
        })
        .catch(_ => {})
    },
    editFolder (e, node) {
      e.stopPropagation()
      this.editDialogVisible = true
      this.editInputValue = node.data.name
      this.editId = node.data.id
    },
    cancelEdit () {
      this.editId = -1
      this.editInputValue = ''
      this.editDialogVisible = false
    },
    async confirmEdit () {
      if (!this.editInputValue) {
        this.$message.warn('请输入文件夹名称')
        return
      }
      const data = { folderName: this.editInputValue, id: this.editId }
      await this.$store.dispatch('tutorial/renameFolder', data)
      this.cancelEdit()
    },
    cancelAdd () {
      this.parentID = -1
      this.folderValue = -1
      this.addInputValue = ''
      this.$store.commit('tutorial/setCreateFolderVisible', false)
    },
    async confirmAdd () {
      if (!this.parentID || this.parentID === -1) {
        this.$message.warn('请选择所属目录!')
        return
      }
      if (!this.addInputValue) {
        this.$message.warn('请填入文件夹名称')
        return
      }
      const data = { parentID: this.parentID, name: this.addInputValue }
      await this.$store.dispatch('tutorial/createFolder', data)
      this.cancelAdd()
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin flex($justify:flex-start,$align:flex-start,$directions:row) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
  flex-direction: $directions;
}
.tutorial-list {
  // width: 240px;
  .tutorial-list-tree {
    background: transparent !important;
  }
  .tutorial-content-dialog {
    &-belong, &-input {
      @include flex($align: center);
      margin-top: 20px;
      div {
        width: 8em;
        color: rgba(255, 255, 255, .7);
      }
      .rest {
        margin-left: 10px;
        flex: 1;
      }
    }
    .dialog-footer {
      @include flex($justify: flex-end, $align: center);
      .dialog-button {
        height: 28px;
        @include flex($justify: center, $align: center);
      }
    }
  }
}

::v-deep {
  .el-tree-node__expand-icon {
    color: var(--seatom-type-800);
  }

  .is-current {
    // background: var(--seatom-primary-a100) !important;

    &::after {
      content: "";
      display: block;
      height: 40px;
      width: 4px;
      background: var(--seatom-primary-900);
      position: absolute;
      left: 0;
      top: 0;
    }
  }
}

::v-deep .custom-node {
  display: flex;
  justify-content: flex-start;
  flex: 1;
  height: 100%;
  align-items: center;
  color: var(--seatom-type-800);
  font-size: 14px;
  &-button-wrapper {
    margin-left: auto;
    @include flex();
    color: white;
    margin-right: 10px;
    .custom-node-button {
      margin-right: 10px;
    }
  }
}

::v-deep .tutorial-list-tree {
  max-height: none !important;
}
::v-deep .tutorial-list-tree .el-icon-caret-right {
  display: block;
  font-size: 14px;
}

::v-deep .tutorial-list-tree .el-tree-node.is-current > .el-tree-node__content {
  background: transparent !important;
}

::v-deep .tutorial-list-tree .el-icon-caret-right:before {
  content: "\e6e0" !important;
}

// ::v-deep .tutorial-list-tree .el-tree-node.is-expanded > .el-tree-node__content > .custom-node {
//   color: white;
// }
// ::v-deep {
//   background-color: red !important;
// }
::v-deep .el-tree-node__content > .custom-node.active {
  color: white;
  font-family: 思源黑体Medium;
  font-style: normal;
  font-weight: bold;
  font-size: 14px;
}
</style>
