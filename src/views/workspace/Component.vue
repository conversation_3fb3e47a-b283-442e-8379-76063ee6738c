<template>
  <div class="workspace-component">
    <el-container class="seatom-main-container">
      <el-aside class="project-manage">
        <div class="project-manage-title">
          <div class="project-manage-title-header">
            <span>创建分组</span>
            <hz-icon @click="addProject" name="plus" style="font-size: 16px;"></hz-icon>
          </div>
          <div class="manage-main">
            <el-input v-if="isAdd" v-model.trim="addValue" v-focus size="mini" @blur="createProject" class="my-project project-input"></el-input>
            <div
              class="my-project"
              @click="showChecked(item, index)"
              v-for="(item, index) in selectData"
              :key="item.id"
              :class="{'project-checked-color': index === active}">
              <div class="project-name">
                <span>{{item.name}}</span>
              </div>
                <span class="screens-length">{{ item.comdata && item.comdata.length }}</span>
                <div class="edit-del">
                  <!-- <span class="edit mr10"><i class="el-icon-edit"></i></span> -->
                  <span @click="delFiles(item, index)" class="delete"><hz-icon name="trash"></hz-icon></span>
                </div>
            </div>
          </div>
        </div>
      </el-aside>
      <el-main>
        <div class="project-content-header">
          <div class="project-content-button">
            <el-button type="primary" @click="toCustomComp">
              <div class="add-text"><hz-icon name="plus" style="margin-right: 8px;"></hz-icon>
                <span>自定义</span>
              </div>
            </el-button>
            <el-button class="el-button--plain" type="plain"  @click="delCustom" icon="el-icon-delete">删除</el-button>
          </div>
          <div style="width: 245px;margin-right: 8px;">
            <el-input
              class="input-theme"
              placeholder="请输入搜索内容"
              size="mini"
              prefix-icon="el-icon-search"
              v-model="searchValue">
            </el-input>
          </div>
        </div>
        <div class="select-all" v-if="!!customList && !!customList.length">
          <!-- <hz-icon class="font-16" :name="'ico-select-' + selectAll" style="margin-right: 4px;"></hz-icon> -->
          <el-checkbox
           v-model="checkAll"
           :indeterminate="isIndeterminate"
           style="margin-right: 4px;"
           @change="handleCheckAllChange"></el-checkbox>
          <span style="font-size: 14px">全选</span>
        </div>
        <div class="main-screen">
          <div
            class="my-screen"
            v-for="custom in customList"
            :key="custom.id">
            <div class="img-border" :data-index="custom.customIcon">
              <el-image
                class="img-style"
                :src="custom.preview+`?width=290&height=162`">
                <div slot="placeholder" class="image-slot" style="line-height: 170px;text-align: center;color: #797979;">
                  加载中<span class="dot">...</span>
                </div>
                <div slot="error" class="image-slot" style="padding: 45px 0;text-align: center;color: #797979;">
                  <hz-icon name="icon-img-load-error" style="width: 61px;height: 52px;margin-bottom: 8px; color: red"></hz-icon>
                  <span style="display: block;">图片加载失败</span>
                </div>
              </el-image>
            </div>
            <div class="img-select" @click="selectCustom(custom)">
              <hz-icon class="hz-icon-18" :name="custom.isSelect ? 'select-circle-ok': 'select-circle-no'"></hz-icon>
            </div>
            <div class="img-bottom">
              <span class="custom-name">{{custom.name}}</span>
              <div class="edit-custom-button" @click="editCustom(custom.id)">
                <span class="edit"><hz-icon name="edit"></hz-icon></span>
                编辑
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog
      title="创建自定义组件"
      :visible.sync="dialogVisible"
      width="500px"
      top="0"
      :close-on-click-modal="false"
      :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'">
      <el-form label-width="90px" label-position="right" size="mini">
        <el-form-item label="中文名称：" status-icon>
          <el-input class="input-theme" v-model="name" placeholder="请输入自定义组件名称"></el-input>
        </el-form-item>
        <!-- <el-form-item label="组件id：" status-icon>
          <el-input v-model="type" placeholder="请输入组件id(唯一标示)"></el-input>
        </el-form-item> -->
        <el-form-item label="组件版本：" status-icon>
          <el-input class="input-theme" v-model="version" placeholder="例如：4.0.0"></el-input>
        </el-form-item>
        <el-form-item label="分组：" status-icon>
          <el-select class="input-theme" popper-class="poper-theme" style="width:100%" v-model="customcomprojectId" placeholder="请选择大屏分组">
            <el-option
              v-for="item in selectData"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="text" @click="dialogVisible = false">取 消</el-button>
        <el-button size="mini" type="plain" @click="createScreen">确 定</el-button>
      </span>
    </el-dialog>
    <!-- <el-dialog
      title="提示"
      :visible.sync="dialogVisibleDel"
      width="30%"
      top="0"
      :close-on-click-modal="false">
      <div class="dialog-content-del-info" style="color: #bfbfbf;">
        <div>删除文件夹后，该文件夹内文件会同步删除</div>
        <div>是否确认删除？</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleDel = false;">取 消</el-button>
        <el-button type="primary" @click="delFiles">确 定</el-button>
      </span>
    </el-dialog> -->

    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import { createCustom, createCustomProject, getCustomProject, deleteCustom, deleteCustomProject } from '@/api/custom';
import { uuid } from '@/utils/base';
export default {

  name: 'Component',

  data () {
    return {
      dialogVisible: false,
      name: '',
      type: uuid('custom'),
      version: '4.0.0',
      customcomprojectId: '',
      selectData: [],
      isAdd: false, // 是否添加输入框
      addValue: '',
      active: 0,
      searchValue: '',
      loading: false,
      checkAll: false,
      isIndeterminate: false
      // dialogVisibleDel: false
    }
  },

  created () {
    this.initCustomProject()
  },

  computed: {
    customList () {
      if (!this.selectData.length) return
      return this.selectData[this.active].comdata.map(item => {
        return {
          id: item.id,
          name: item.compConfig.chartConfig.cn_name,
          isSelect: false,
          preview: item.customIcon
        }
      }).filter(item => {
        return item.name.includes(this.searchValue.trim())
      })
    }
  },

  methods: {
    async initCustomProject () { // 获取自定义组件列表
      this.loading = true
      const res = await getCustomProject();
      if (res && res.success && res.data) {
        this.selectData = res.data
        this.loading = false
      }
    },
    toCustomComp () {
      this.dialogVisible = true;
    },
    addProject () {
      this.isAdd = true;
    },
    showChecked (item, index) {
      this.active = index
      this.checkAll = false
    },
    editCustom (id) {
      this.$router.push({ path: `/component/customcomp/${id}` })
    },
    handleCheckAllChange (val) {
      if (val) {
        this.customList.forEach(item => {
          item.isSelect = true
        })
      } else {
        this.customList.forEach(item => {
          item.isSelect = false
        })
      }
    },
    async createScreen () {
      if (!this.check()) return;
      const cmConfig = {
        compConfig: {
          version: this.version,
          name: `custom-all-${this.type}`,
          chartConfig: {
            type: `custom-all-${this.type}`,
            cn_name: this.name
          }
        }
      }
      const res = await createCustom({
        customcomprojectId: this.customcomprojectId,
        ...cmConfig
      })
      if (res && res.success && res.data) {
        this.$router.push({ path: `/component/customcomp/${res.data.id}` })
      }
    },
    async createProject () { // 创建自定义组件分组
      this.isAdd = false;
      if (this.addValue) {
        const res = await createCustomProject({ name: this.addValue });
        if (res && res.success && res.data) {
          this.initCustomProject();
          this.$message.success('创建分组成功');
        }
      }
      this.addValue = ''
    },
    delCustom () {
      const delList = this.customList.reduce((prev, cur) => {
        if (cur.isSelect) {
          prev.push(cur.id)
        }
        return prev
      }, [])
      if (delList.length) {
        this.$confirm('删除后可能无法恢复，是否删除?', '', {
          confirmButtonText: '确定(Enter)',
          cancelButtonText: '取消',
          type: 'warning',
          cancelButtonClass: 'poper-cancel',
          iconClass: 'message-warning',
          customClass: 'poper-theme',
          closeOnClickModal: false
        })
          .then(async () => {
            const res = await deleteCustom({ ids: delList });
            if (res && res.success && res.data) {
              this.initCustomProject();
              this.$message.success('删除成功');
            }
          })
          .catch(() => {});
      } else {
        this.$message.error('请选择删除组件');
      }
    },
    selectCustom (custom) {
      custom.isSelect = !custom.isSelect
      this.$forceUpdate();
      const allOk = this.customList.every(it => it.isSelect);
      const allNo = this.customList.every(it => !it.isSelect);
      if (allOk) {
        this.isIndeterminate = false;
        this.checkAll = true;
      } else if (allNo) {
        this.isIndeterminate = false;
        this.checkAll = false;
      } else {
        this.isIndeterminate = true;
      }
    },
    delFiles (item, index) {
      // await deleteCustomProject({ id:  })
      const text = '删除文件夹后，该文件夹内文件会同步删除,是否确认删除？'
      this.$confirm(text, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme',
        closeOnClickModal: false
      }).then(async () => {
        const res = await deleteCustomProject({ id: item.id });
        if (res && res.success && res.data) {
          this.selectData.splice(index, 1);
          this.active = index - 1;
          this.$message.success('删除成功');
        }
      }).catch(() => {})
    },
    check () {
      const versionRegex = /^\d+(.\d+){2}$/;
      if (!this.name.trim()) {
        this.$message.error('自定义组件名称不能为空');
        return false;
      }
      if (!this.version.trim()) {
        this.$message.error('自定义组件版本号不能为空');
        return false;
      }
      if (!versionRegex.test(this.version.trim())) {
        this.$message.error('版本号格式有误！');
        return false;
      }
      if (!this.customcomprojectId) {
        this.$message.error('分组不能为空');
        return false;
      }
      return true;
    }
  }

}
</script>

<style  lang="scss" scoped>
.workspace-component {
  .project-manage {
    min-width: 288px;
    max-width: 288px;
    position: sticky;
    top: 121px;// 96px;
    font-size: 14px;
    overflow-y: auto;
    height: calc(100vh - 125px);
    color: #fff;
    background-color: var(--seatom-background-100);
    color: var(--seatom-type-800);
    box-shadow: var(--seatom-container-c200);

    &::-webkit-scrollbar {
      display: block;
      width: 4px;
    }

    &::-webkit-scrollbar-thumb{
      background: #434b55;
      border: 1px solid #434b55;
    }

    &-title {
      position: sticky;
      top: 0;
      background: var(--seatom-background-100);
      z-index: 10;
      &-header {
        display: flex;
        justify-content: space-between;
        padding-right: 30px;
        height: 60px;
        padding-left: 24px;
        // border-bottom: 1px solid #27343e;
        align-items: center;
        font-family: 思源黑体Medium;
        font-style: normal;
        font-weight: bold;
        font-size: 14px;
      }

      &-bottom {
        transition: color .2s;
        cursor: pointer;
      }
    }

    .my-project {
      padding-left: 24px;
      line-height: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 30px;
      cursor: pointer;
    }
    .my-project:hover {
      background: var(--seatom-mono-a100);
      // border-radius: 0px 50px 50px 0px;
      position: relative;
    }

    .my-project:hover .edit-del{
      display: block;
      color: var(--seatom-type-800);
      font-size: 16px;
    }

    .my-project:hover .screens-length{
      display: none;
    }

    .my-project.project-checked-color {
      background-repeat: round;
      background: var(--seatom-primary-a100);
      color: var(--seatom-primary-900);
      font-weight: 600;
      &::after {
        content: "";
        display: block;
        height: 40px;
        width: 4px;
        background: var(--seatom-primary-900);
        position: absolute;
        left: 0;
      }
    }

    .edit-del {
      position: absolute;
      display: none;
      right: 30px;
      cursor: pointer;
    }
  }

  .project-content-header {
    padding-top: 16px;
    position: sticky;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--seatom-mono-a300);
    padding-bottom: 16px;
    // padding-top: 10px;
    height: 60px;
    background: var(--seatom-background-200);
    z-index: 9;
    top: 121px; // 96px;
    // min-width: 1024px;

    .project-content-button {
      display: flex;
    }
  }

  .select-all {
    height: 32px;
    line-height: 32px;
    display: flex;
    font-weight: 600;
    color: var(--seatom-type-800);
    cursor: pointer;
    width: 120px;
    align-items: center;
  }

  .main-screen {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    padding-top: 8px;
    user-select: none;
    padding-bottom: 50px;
    // padding-left: 4px;

    .my-screen {
      margin: 4px 12px;
      height: 226px;
      width: 286px;
      display: flex;
      flex-direction: column;
      // border: 2px solid rgba(204, 219, 255, 0.16);
      transition: .2s;
      box-sizing: border-box;
      border-radius: 16px;
      position: relative;
      // background-color: var(--seatom-background-400);

      .img-border {
        border-radius: 8px;
        border: 2px solid #5291ff00;
        display: flex;
        justify-content: center;
        align-items: center;
        &:hover {
          border: 2px solid #5291FF;
        }

        .el-image {
          height: 170px;
          width: 268px;
          border-radius: 8px;
          margin: 4px 0;
        }

        .img-style {
          background: var(--seatom-mono-400);
        }

      }

      .img-select {
        position: absolute;
        display: block;
        top: 6px;
        left: 234px;
        padding: 10px;
        cursor: pointer;
      }

      .img-bottom {
        // margin-top: 8px;
        padding: 0 10px;
        color: var(--seatom-type-800);
        display: flex;
        justify-content: space-between;

        .custom-name {
          margin-top: 4px;
          font-size: 14px;
        }

        .edit-custom-button {
          padding: 4px 6px;
          font-family: PingFang SC;
          font-style: normal;
          // font-weight: 600;
          font-size: 12px;
          border-radius: 4px;
          &:hover {
            background-color: var(--seatom-primary-a100);
            color: var(--seatom-primary-900);
          }
          i {
            color: #FFFFFF;
          }
        }

      }
    }

  }

  // .seatom-main-container ::v-deep .el-input__inner {
  //   background-color: rgba(29,38,46,.54);
  //   color: #fff;
  //   padding: 0 6px;
  //   border: 1px solid #2681ff;
  //   text-overflow: ellipsis;
  //   overflow: hidden;
  //   white-space: nowrap;
  // }

  .mr10 {
    margin-right: 10px;
  }

  .el-main {
    overflow: unset;
    padding-top: 0;
    padding: 0 32px;
  }

  .hz-icon-18 {
    width: 20px;
    height: 20px;
  }

  .font-16 {
    font-size: 16px;
  }

  ::v-deep .dialog-content-del-info {
    padding: 0 16px;
  }

  ::-webkit-input-placeholder {
    color: rgba(204, 219, 255, 0.15);
  }

  ::v-deep .el-button--plain  {
    // background: #1b3d6187;
    // border-color: #4679af;
    border-radius: 4px;
  }
  ::v-deep .el-button {
    padding: 8px 16px;
  }

}
</style>
