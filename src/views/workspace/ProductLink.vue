<!--
 * @Description: 产品列表链接
 * @Version: 2.0
 * @Author: julei
 * @Date: 2022-03-28 14:32:23
 * @LastEditors: julei
 * @LastEditTime: 2022-03-28 14:59:58
-->

<template>
  <div class="product-link-wrap">
    <div class="product-link-wrap-type" v-for="pro in productListNew" :key="pro.type">
      <div class="product-link-wrap-type-title">{{ pro.name }}</div>
      <div class="product-link-wrap-item-flex-wrap">
        <template v-for="item in pro.children">
          <div
            class="product-link-wrap-type-item"
            :key="item.name"
            @click="linkJump(item.url)"
            v-if="item.chat_has || item.group_has || item.own_has"
          >
            <div class="product-link-wrap-type-item-top">
              <img
                :src="item.imgUrl"
                alt=""
                width="24px"
                height="24px"
                class="imgRight"
              />
              <div>{{ item.name }}</div>
            </div>
            <div class="product-link-wrap-type-item-bottom">
              {{ item.pro_des }}
            </div>
            <div class="product-link-wrap-type-item-img"></div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  data () {
    return {}
  },
  props: {
    productListNew: {
      type: Array
    }
  },
  methods: {
    linkJump (url) {
      window.location.href = url;
    }
  }
};
</script>
<style lang="scss" scoped>
.product-link-wrap {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 30;
  background-image: var(--seatom-product-bg);
  background-color: var(--seatom-product-bgcolor);
  background-size: cover;
  background-repeat: no-repeat;
  box-shadow: 0px 48px 128px -16px rgba(4, 8, 16, 0.64),
    0px 16px 64px -16px rgba(4, 8, 16, 0.72);
  filter: drop-shadow(0px 0px 1px rgba(4, 8, 16, 0.32));
  padding: 25px 60px 32px 112px;
  overflow: auto;
  &-type {
    margin: 24px 0;
    &-title {
      margin: 8px 0;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
      color: var(--seatom-type-800);
    }
    &-item {
      position: relative;
      width: 256px;
      height: 76px;
      background: var(--seatom-product-item-bg);
      border-radius: 8px;
      margin-right: 40px;
      margin-bottom: 24px;
      display: flex;
      flex-direction: column;
      padding: 12px 0 12px 24px;
      border: 2px solid var(--seatom-product-border-color);
      cursor: pointer;
      &-top {
        color: var(--seatom-type-900);
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        display: flex;
      }
      &-bottom {
        margin-top: 6px;
        color: var(--seatom-type-800);
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        text-align: justify;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &-img {
        position: absolute;
        right: 8px;
        top: 6px;
        width: 73px;
        height: 60px;
        background-image: var(--seatom-product-ico);
        background-size: auto 100%;
        background-repeat: no-repeat;
        background-position: right;
      }
    }
    &-item:hover {
      border: 2px solid #3d85ff;
    }
  }
}
.imgRight {
  margin-right: 6px;
}
.product-link-wrap-item-flex-wrap {
  display: flex;
  flex-wrap: wrap;
}
</style>
