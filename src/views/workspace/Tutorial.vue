<template>
  <div class="workspace-tutorial">
    <el-container>
      <el-aside class="workspace-tutorial-tree" width="240px" :style="{top:'0px',height:'100vh'}">
        <TutorialList :is-admin="false"/>
      </el-aside>
      <el-main>
        <TutorialContent class="workspace-tutorial-content" :is-admin="false"/>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import TutorialList from '@/views/tutorial/TutorialList'
import TutorialContent from '@/views/tutorial/TutorialContent'

export default {
  name: 'Tutorial',
  components: {
    TutorialList,
    TutorialContent
  },
  data () {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.workspace-tutorial {
  &-tree {
    min-width: 288px;
    max-width: 288px;
    position: sticky;
    top: 121px;
    font-size: 14px;
    overflow-y: auto;
    height: calc(100vh - 125px);
    background-color: var(--seatom-background-100);
    color: var(--seatom-type-800);
    box-shadow: var(--seatom-container-c200);
    padding: 16px 0;

    &::-webkit-scrollbar {
      display: block;
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #434b55;
      border: 1px solid #434b55;
    }
  }
}
</style>
