<template>
  <div class="workspace-data">
    <div class="my-data">
      <div class="left-switch-list">
        <el-aside class="project-manage">
          <div class="project-manage-title">
            <div class="manage-main">
              <div
                class="my-project"
                v-for="item in selectData"
                :key="item.id"
                :class="{'project-checked-color': item.id === type}"
                @click="filterData(item)">
                <div class="project-name" style="display: flex; align-items: center;">
                  <hz-icon style="font-size: 20px; margin-right: 4px" :name="item.icon"></hz-icon>
                  <span>{{ item.name }}</span>
                </div>
                <!-- <span class="screens-length">{{ 5 }}</span> -->
              </div>
            </div>
          </div>
        </el-aside>
      </div>
      <div class="code-template-wp">
        <div class="source-manage">
          <div class="ds-part">
            <div class="data-title">
              <div class="title-add">
                <el-button type="primary" class="datav-btn-md datav-btn add-data" @click="createData">
                  <div class="add-text">+ 添加数据</div>
                </el-button>
                <div class="data-border"></div>
              </div>
            </div>
            <div class="data-main">
              <div class="main-storage" v-for="item in sourceList" :key="item.id">
                <div
                  class="storage-type"
                  :style="{
                  }"
                >
                  <div class="source">
                    <hz-icon :name="sourceMap[item.type].icon || ''" style="font-size: 24px"></hz-icon>
                    {{sourceMap[item.type].label || ''}}
                  </div>
                </div>
                <div class="storage-operate">
                  <div class="storage-preview" v-if="item.type == 'csv_file' || item.type === 'excel'" @click="previewItem(item)" title="预览">
                    <hz-icon name="entity-view" class="storage-icon"></hz-icon>
                  </div>
                  <div class="storage-edit" @click="eidtItem(item)" title="编辑">
                    <hz-icon name="edit" class="storage-icon"></hz-icon>
                  </div>
                  <div class="storage-delete" @click="delItem(item)" title="删除">
                    <hz-icon name="trash" class="storage-icon"></hz-icon>
                  </div>
                </div>
                <div class="storage-info" :title="item.name">
                  <div class="info-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 创建数据源 -->
          <CreateDataSource ref="data" :type="type" @update="getDatasourceList" />
          <!-- 预览数据 -->
          <PreviewData ref="preview" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CreateDataSource from '@/components/editor/data-source/CreateDataSource';
import PreviewData from '@/components/editor/my-data/PreviewData';
import { datastorageList, deleteStorage } from '@/api/datastorage';
import { mapState } from 'vuex';
export default {
  name: 'workspaceData', // 我的数据
  components: {
    CreateDataSource,
    PreviewData
  },
  data () {
    return {
      active: 'source',
      type: null,
      selectData: [
        {
          id: 'csv_file',
          name: 'CSV文件',
          isSelect: false,
          icon: 'data-csv'
        },
        {
          id: 'excel',
          name: 'Excel文件',
          isSelect: false,
          icon: 'data-excel'
        },
        {
          id: 'api',
          name: 'API数据',
          isSelect: false,
          icon: 'data-api'
        },
        {
          id: 'mysql',
          name: 'MySQL数据库',
          isSelect: false,
          icon: 'data-sql'
        },
        {
          id: 'postgresql',
          name: 'PostgreSQL数据库',
          isSelect: false,
          icon: 'data-sql'
        },
        {
          id: 'oracle',
          name: 'Oracle数据库',
          isSelect: false,
          icon: 'data-sql'
        },
        {
          id: 'mongodb',
          name: 'MongoDB数据库',
          isSelect: false,
          icon: 'data-sql'
        },
        {
          id: 'websocket',
          name: '实时数据',
          isSelect: false,
          icon: 'data-websocket'
        },
        {
          id: 'json',
          name: 'JSON文件',
          isSelect: false,
          icon: 'data-json'
        },
        {
          id: 'dmdb',
          name: '达梦数据库',
          isSelect: false,
          icon: 'data-sql'
        },
        {
          id: 'highgodb',
          name: '瀚高数据库',
          isSelect: false,
          icon: 'data-sql'
        }
      ],
      sourceList: [],
      sourceMap: {
        api: {
          label: 'OpenAPI',
          icon: 'source-api'
        },
        csv_file: {
          label: 'CSV',
          icon: 'source-csv'
        },
        excel: {
          label: 'Excel',
          icon: 'source-excel'
        },
        mysql: {
          label: 'MySQL',
          icon: 'source-type'
        },
        postgresql: {
          label: 'PostgreSQL',
          icon: 'source-type'
        },
        oracle: {
          label: 'Oracle',
          icon: 'source-type'
        },
        mongodb: {
          label: 'MongoDB',
          icon: 'source-type'
        },
        websocket: {
          label: '实时',
          icon: 'source-type'
        },
        json: {
          label: 'JSON',
          icon: 'source-json'
        },
        dmdb: {
          label: '达梦',
          icon: 'source-type'
        },
        highgodb: {
          label: 'HighgoDB',
          icon: 'source-type'
        }
      }
    };
  },
  computed: {
    ...mapState({
      screenInfo: state => state.editor.screenInfo
    })
  },
  created () {
    this.type = 'api';
    this.getDatasourceList();
  },
  methods: {
    selectTab (name) {
      this.active = name;
    },
    createData () {
      this.$refs.data.showDialog(this.type);
    },
    previewItem (item) { // 预览数据
      this.$refs.preview.previewData(item);
    },
    eidtItem (item) {
      this.$refs.data.showEdit(item);
    },
    delItem (item) {
      this.$confirm('确认删除该数据源？', {
        title: '提示',
        type: 'warning',
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme'
      }).then(async () => {
        const res = await deleteStorage({}, { id: item.id });
        if (res && res.success) {
          this.$message.success('删除成功');
          this.getDatasourceList();
        }
      }).catch(() => {})
    },
    async getDatasourceList () { // 获取数据源列表
      const data = {
        workspaceId: this.screenInfo.workspaceId || this.$route.params.workspaceId
      }
      if (this.type) {
        data.type = this.type;
      }
      const res = await datastorageList(data);
      if (res && res.success) {
        this.sourceList = res.data;
      }
    },
    filterData (data) {
      this.type = data.id;
      this.getDatasourceList();
    }
  }
};
</script>

<style lang="scss" scoped>
.workspace-data {
  .my-data {
    display: flex;
    .left-switch-list {
      // position: sticky;
      // top: 106px;
      // min-width: 240px;
      // max-width: 240px;
      // font-size: 14px;
      // height: 500px;
      // cursor: pointer;

      .project-manage {
        min-width: 288px;
        max-width: 288px;
        position: sticky;
        top: 121px; // 96px;
        font-size: 14px;
        overflow-y: auto;
        height: calc(100vh - 125px);
        background-color: var(--seatom-background-100);
        color: var(--seatom-type-800);
        padding: 16px 0;

        &-title {
          position: sticky;
          top: 0;
          background: (--seatom-background-100);
          z-index: 10;
        }

        .my-project {
          padding-left: 24px;
          line-height: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-right: 30px;
          cursor: pointer;
          color: var(--seatom-type-800);
        }
        .my-project:hover {
          background: var(--seatom-mono-a100);
          position: relative;
        }

        .my-project.project-checked-color {
          background-repeat: round;
          background: var(--seatom-primary-a100);
          color: var(--seatom-primary-900);
          font-weight: 600;
          &::after {
            content: "";
            display: block;
            height: 40px;
            width: 4px;
            background: var(--seatom-primary-900);
            position: absolute;
            left: 0;
          }
        }

      }
    }
    .code-template-wp {
      width: 100%;
      min-height: 400px;
      padding: 8px 0;
      .ds-part {
        flex: 1;
        padding-right: 50px;
        .data-title {
          position: sticky;
          top: 120px;
          width: 100%;
          max-width: 1440px;
          display: flex;
          flex-direction: column;
          padding-left: 55px;
          justify-content: center;
          height: 50px;
          z-index: 9;
          background-color: var(--seatom-background-200);
          .title-right {
            position: absolute;
            right: 0;
            display: flex;
            align-items: center;
            z-index: 1;
            .search-drop {
              cursor: pointer;
              font-size: 14px;
              margin-right: 10px;
              .el-dropdown-link {
                font-size: 12px;
                color: #bcc9d4;
              }
            }
          }
          .title-add {
            height: 32px;
            display: flex;
            .datav-btn {
              transform: skewX(-40deg);
              border-radius: 0;
              background: #2681ff;
              &.datav-btn-md {
                height: 32px;
                line-height: 32px;
                padding: 0 30px;
              }
              .add-text {
                display: block;
                transform: skewX(40deg);
              }
            }
            .data-border {
              border: 1px solid;
              border-color: var(--seatom-mono-a300) transparent transparent var(--seatom-mono-a300);
              flex: 1;
              transform: skewX(-40deg);
              margin-left: 20px;
            }
          }
        }
        .data-main {
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;
          padding: 8px 50px 30px 36px;
          .main-storage {
            display: flex;
            align-items: center;
            font-size: 14px;
            margin: 16px 0;
            position: relative;
            min-width: 50%;
            &:hover .storage-operate {
                transition: .5s all cubic-bezier(.65,.05,.36,1);
                left: 0px;
                opacity: 1;
            }
            .storage-type {
              position: absolute;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 35px;
              width: 165px;
              transform: skewX(-40deg);
              background-color: var(--seatom-primary-a300);
              background-size: contain;
              background-repeat: no-repeat;
              background-position: center center;

              .source {
                transform: skewX(40deg);
                color: var(--seatom-primary-900);
                font-weight: 600;
                font-size: 16px;
              }
            }
            .storage-operate {
              opacity: 0;
              transition: .5s all cubic-bezier(.65,.05,.36,1);
              position: absolute;
              left: 170px;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 35px;
              width: 165px;
              transform: skewX(-40deg);
              background-image: linear-gradient(-90deg,#12b3ff 0,#307cff 100%);
              .storage-preview, .storage-delete, .storage-edit {
                cursor: pointer;
                transform: skewX(40deg);
                margin-right: 15px;
                padding: 4px;
                .storage-icon {
                  font-size: 20px;
                  transition: color .2s;
                  color: #ffffff;
                }

                &:hover {
                  background: rgba(255, 255, 255, 0.12);
                  border-radius: 16px;
                }
              }
            }
            .storage-info {
              margin-left: 165px;
              margin-right: 20px;
              min-width: 320px;
              display: flex;
              flex-wrap: wrap;
              flex: 1;
              justify-content: space-between;
              line-height: 44px;
              padding: 0 35px;
              background: var(--seatom-storage-info-bg);
              // border: 1px solid #3a4659;
              border-left: 4px solid var(--seatom-primary-900);
              transform: skewX(-40deg);
              .info-name {
                color: var(--seatom-type-800);
                transform: skewX(40deg);
                width: 150px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
              .info-time {
                letter-spacing: 1.2px;
                font-size: 12px;
                color: #bcc9d4;
                transform: skewX(40deg);
              }
              &:hover {
                background: linear-gradient(90deg, rgba(43, 121, 255, 0.24) 3.04%, rgba(43, 121, 255, 0) 90.47%);
                .info-name {
                  color: var(--seatom-primary-900);
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
