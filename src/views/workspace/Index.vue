<template>
  <div class="workspace" ref="workspace">
    <div class="workspace-wrap" ref="workspaceWrap" @scroll="scrollDown">
      <div class="workspace-top">
        <div class="workspace-top-header" :class="{ 'scroll-none': fixed }">
          <!-- <div class="workspace-top-header-img" v-if="topProduct"><img :src="logoImg" alt=""></div> -->
          <!-- <div
            class="workspace-top-header-product"
            :class="{'show-product-link' : showProductLink}"
            style="top: 9px; left: 135px"
            @click="showProductLink = !showProductLink"
            v-if="topProduct && checkProductList">
            所有产品<i class="arrow" :class="showProductLink ? 'el-icon-arrow-up show-product-link' : 'el-icon-arrow-down'" style="margin-left: 8.5px"></i>
          </div> -->
          <hz-product-list local="" ref="productList" :theme="theme" class="workspace-top-header-product" plain="" v-if="!isLoginLocal">
            <div slot="manage" class="manage-box">
              <div class="icon">
                <img src="../../assets/img/permission-manage.png" width="20" height="20" :class="{darkIcon:theme==='dark'}">
              </div>
              <div class="divider" :class="[theme==='dark'?'divider_dark':'divider_light']" v-if="topProduct"></div>
              <div v-if="topProduct" class="logo">
                <img :src="logoImg" alt="" height="32">
              </div>
            </div>
          </hz-product-list>
          <div class="workspace-top-header-user">
            <div class="workspace-top-header-user-license" @click="licenseVisible = true">
              <hz-icon style="font-size: 18px" name="abnormal-info"></hz-icon>
              {{ computedAuthorizationDays }}
            </div>
            <!-- <hz-user
              label="label"
              product="seatom"
              avatar="b"
              level="level"
              id="hzUser">
            </hz-user> -->
            <div class="operation-icon">
              <el-tooltip class="item" effect="dark" content="后台" placement="bottom"  v-if="roleType === 1">
                <hz-icon class="pointer mr-26" @click="ToSetting" name="other-management"></hz-icon>
              </el-tooltip>
              <hz-icon class="pointer mr-15" @click="jumpTo" name="nav-course" v-if="roleType !== 3"></hz-icon>
              <hz-icon style="font-size: 18px" class="pointer mr-15" @click="changeTheme" :name="theme === 'dark' ? 'light-icon' : 'dark-icon'"></hz-icon>
              <div class="notice-box" @click.stop="showNotice">
                <hz-icon name="notice" class="pointer"></hz-icon>
                <span class="notice-indicator" v-if="hasUnreadNotice"></span>
              </div>
              <hz-icon class="pointer ml-15" @click="logout" name="logout" v-if="isLoginLocal"></hz-icon>
            </div>
            <div class="baffle"></div>
            <div v-if="isLoginLocal" style="display:flex;align-items:center">
              <div class="workspace-top-header-user-head">
                <hz-icon name="user-icon"></hz-icon>
              </div>
              <div class="workspace-top-header-user-text">{{ userName }}</div>
            </div>
            <!-- <hz-user
              v-else
              :label="baseInfoData.contact || baseInfoData.name"
              product="seatom"
              :avatar="baseInfoData.avatar_url"
              :level="baseInfoData.level"
              id="hzUser">
            </hz-user> -->
            <el-dropdown placement="bottom" v-else>
              <div class="user-trigger">
                <img :src="baseInfoData.avatar_url" alt="" srcset="" style="width:32px;height:32px;margin-right:4px;border-radius:100%">
                <span style="color: var(--user-btn-text, #FFFFFF);font-weight: 400;font-size: 12px;white-space: nowrap;">{{baseInfoData.contact || baseInfoData.name}}</span>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="user-dropdown-menu">
                  <div class="user-dropdown-container">
                    <div class="user-dropdown-top">
                      <img :src="baseInfoData.avatar_url" style="height: 56px;width: 56px;border-radius: 100%;">
                      <div style="display: flex;flex-direction: column;align-items: flex-start;width: max-content;" class="top-text">
                        <span style="color: rgba(21, 22, 24, 0.92);font-weight: 600;font-size: 20px;line-height: 28px;margin: 0;margin-bottom: 4px" class="name">{{baseInfoData.contact || baseInfoData.name}}</span>
                        <span style="" class="role">{{baseInfoData.level}}</span>
                      </div>
                    </div>
                    <div class="user-dropdown-body">
                      <div @click="clickCenter">
                        <hz-icon name="user-info" style="font-size: 16px"></hz-icon>
                        <span class="text">用户中心</span>
                      </div>
                      <span class="line"></span>
                      <div @click="logout">
                        <hz-icon name="logout" style="font-size: 16px"></hz-icon>
                        <span class="text">退出</span>
                      </div>
                    </div>
                  </div>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <!-- <hz-user
              label="label"
              product="seatom"
              avatar="b"
              level="level"
              id="hzUser">
            </hz-user> -->
          </div>
        </div>
        <div class="workspace-top-nav">
          <div class="workspace-top-nav-img">
            <!-- <video width="100%" autoplay="autoplay" loop="loop" muted="muted">
              <source
                src="../../assets/video/videoplayback .mp4"
                type="video/mp4"
              />
            </video> -->
          </div>
          <div class="workspace-top-nav-text"></div>
          <!-- <div class="workspace-top-header-product" :class="{'show-product-link' : showProductLink}"
           style="top: 58px; left: 376px" @click="showProductLink = !showProductLink"
           v-if="!topProduct && checkProductList"
           >
            所有产品<i class="arrow" :class="showProductLink ? 'el-icon-arrow-up show-product-link' : 'el-icon-arrow-down'" style="margin-left: 8.5px"></i>
          </div> -->
        </div>
      </div>
      <div class="notice-dropdown-list" v-if="showNoticeList">
        <header class="notice-header">通知列表</header>
        <main class="notice-list">
          <el-scrollbar style="height: 100%" v-if="noticeList.length">
            <li
              class="list-item"
              :class="[item.isRead ? 'read' : 'unread']"
              v-for="item in noticeList"
              :key="item.id"
              @click="handleNotice(item)"
            >
              <div class="notice-body">
                <!-- {'el-icon-share': item.type === 'usershare'} -->
                <i :class="[(item.type === 'usershare') ? 'el-icon-share': 'el-icon-user-solid' , item.type]"></i>
                <span class="notice-content">{{ item.text }}</span>
                <span class="notice-state">{{ getNoticeState(item.isRead) }}</span>
              </div>
              <span class="notice-delete" @click.stop="delMessage(item)"><i class="el-icon-close"></i></span>
            </li>
          </el-scrollbar>
          <div class="empty-info" v-else>
            <span class="empty-text">暂无消息</span>
          </div>
        </main>
      </div>
      <div
        class="workspace-menu"
        :class="{ 'gray-bg scroll-animation': fixed }"
        ref="menu"
      >
        <!-- <el-menu
          :default-active="$route.path"
          background-color="transparent"
          mode="horizontal"
          active-text-color="#b9c2cc"
          text-color="#b9c2cc"
          router>
          <el-menu-item :index="`/workspace/${workspaceId}/`"><i class="el-icon-full-screen"></i>我的可视化</el-menu-item>
          <el-menu-item :index="`/workspace/${workspaceId}/data`"><i class="el-icon-data-line"></i>我的数据</el-menu-item>
          <el-menu-item :index="`/workspace/${workspaceId}/resources`"><i class="el-icon-data-line"></i>我的资源</el-menu-item>
          <el-menu-item :index="`/workspace/${workspaceId}/component`"><i class="el-icon-user"></i>我的组件</el-menu-item>
          <el-menu-item :index="`/workspace/${workspaceId}/help`"><i class="el-icon-setting"></i>教程</el-menu-item>
        </el-menu> -->
        <div class="nav-main">
          <canvas
            id="canvas-2761"
            style="position: absolute; z-index: -1; left: 0px"
          ></canvas>
          <span class="nav-span" v-for="(item, i) in navMain" :key="item.route">
            <a
              class="nav-link"
              :class="{
                'nav-active': $route.meta.routeName === item.route,
                'not-active': $route.meta.routeName !== item.route,
              }"
              @click="toggle(i, item.route)"
            ><hz-icon class="mr-8 nav-icon" :name="item.icon"></hz-icon
            >{{ item.label }}</a
            >
          </span>
        </div>
      </div>
      <!-- <div class="workspace-shadow-box">
        <div class="workspace-shadow"></div>
      </div> -->
      <div class="workspace-main scroll-height">
        <router-view ref="Screen" @scrollTops="scrollTops" @changeActiveManage="changeActiveManage"></router-view>
      </div>
      <ProductLink v-if="showProductLink" :style="productListTop" :productListNew="productListNew"></ProductLink>
    </div>
    <el-dialog :visible.sync="manageShow" title="选择分组" width="400px" top="0" :before-close="close">
      <el-form :model="form" :rules="rules" label-width="90px" size="mini" ref="form">
        <el-form-item label="分组：" prop="projectId">
          <el-select v-model="form.projectId">
            <el-option v-for="opt in manage" :value="opt.id" :label="opt.name" :key="opt.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button size="mini" @click="close">取消</el-button>
        <el-button size="mini" type="primary" @click="confirm" :loading="loading">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      class="overdue-dialog"
      :visible.sync="overdueVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :top="'0'"
    >
      <div class="overdue-container">
        <img
          class="overdue-cover"
          :src="require('@/assets/img/overdue.png')"
          alt=""
        >
        <div class="overdue-info">
          <div class="overdue-info__header">订阅伏羲大屏</div>
          <div class="overdue-info__desc">
            <p>
              用户您好，伏羲大屏是付费平台，您的使用已于“{{ authorizationInfo.expdate }}”到期，您需要继续购买授权，才能将其用于个人或商业用途。
            </p>
            <p>
              若对我们的产品满意并希望继续使用，您可以联系管理员进行续费来延长您的大屏使用时间，否则，您将无法继续使用本平台功能。
            </p>
          </div>
          <div class="overdue-info__upload">
            <el-upload
            class="upload-demo"
            ref="upload-license"
            action=""
            :http-request="upload"
            :on-change="handleChange"
            :show-file-list="false"
            :limit="1"
            >
              <el-button size="small" type="primary">上传授权文件</el-button>
            </el-upload>
            <el-button size="small" type="success" @click="getLicenseCode()">获取授权码</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      class="license-dialog"
      :visible.sync="licenseVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :top="'0'"
    >
      <div class="license-container">
        <img
          class="license-cover"
          :src="require('@/assets/img/overdue.png')"
          alt=""
        >
        <div class="license-info">
          <div class="license-info__header">订阅伏羲大屏</div>
          <div class="license-info__desc">
            <p>
              用户您好，伏羲大屏是付费平台，您需要购买授权，才能将其用于个人或商业用途。截止到目前您还有{{ authorizationInfo.authorizationDays }}天的使用权限，这期间您可以尽情使用本产品。若对我们的产品满意并选择在{{ authorizationInfo.authorizationDays }}天后继续使用。
            </p>
            <p>
              若对我们的产品满意并希望继续使用，您可以联系管理员进行续费来延长您的大屏使用时间，否则，您将在{{ authorizationInfo.authorizationDays }}天后无法继续使用本平台功能。
            </p>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="licenseUploadShow" class="progress-dialog" custom-class="poper-theme" title="正在验证" width="400px" top="0">
      <el-progress :percentage="percentage" :color="'#409eff'" style="margin-top: 15px;"></el-progress>
      <div class="info">正在验证授权，大约剩{{remainTime}}s</div>
      <div slot="footer">
        <el-button size="mini" @click="cancelLicense">取消授权</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import canvasnav from '@/lib/CanvasNav.js'
import { navMain } from '@/common/constants'
import emitter from '@/utils/bus'
import { mapState, mapActions } from 'vuex'
import { getShareScreen, getProductList, uploadLicence, getLicenseCode, getDmcUserInfo, jumpUcenter } from '@/api/workspace'
import { screenConfig } from '@/api/user';
import ProductLink from '@/views/workspace/ProductLink'
import toggleTheme from '../../utils/theme'
import 'hz-product-list/lib/bundle.js'
import 'hz-product-list/lib/index.css'
import 'hz-user'
export default {
  components: {
    ProductLink
  },
  data () {
    return {
      fixed: false,
      navMain: [],
      active: 0,
      showNoticeList: false,
      licenseVisible: false,
      licenseUploadShow: false,
      indexMap: {
        '/': 0,
        '/data': 1,
        '/resources': 2,
        '/component': 3,
        '/doc': 4
      },
      currentNotice: null,
      shareInfo: {},
      manageShow: false,
      loading: false,
      form: {
        projectId: ''
      },
      rules: {
        projectId: [
          { required: true, message: '请选择分组', trigger: 'change' }
        ]
      },
      userName: '',
      showProductLink: false,
      topProduct: false,
      productList: [],
      productListTop: {
        top: 128 + 'px'
      },
      productListNew: [
        {
          type: 'data_manage',
          name: '大数据治理与服务',
          children: []
        },
        {
          type: 'data_apply',
          name: '大数据应用与可视化',
          children: []
        },
        {
          type: 'actual_app',
          name: '业务实战应用',
          children: []
        }
      ],
      percentage: 0,
      remainTime: 0,
      messageType: '', // 消息类型
      modeType: '',
      baseInfoData: {},
      userType: [
        {
          icon: 'account-superadmin',
          text: '超级管理员'
        },
        {
          icon: 'account-person',
          text: '普通用户'
        },
        {
          icon: 'account-group',
          text: '组织管理员'
        }
      ],
      isLoginLocal: false
    }
  },
  computed: {
    ...mapState({
      // userName: (state) => state.user.name,
      theme: (state) => state.theme,
      userId: (state) => state.user.id,
      roleType: (state) => state.user.roleType,
      noticeList: (state) => state.editor.noticeList,
      screenInfo: (state) => state.editor.screenInfo,
      manage: (state) => state.editor.manage,
      authorizationInfo: state => state.user.authorizationInfo,
      overdueVisible: state => state.user.overdueVisible
    }),
    hasUnreadNotice () {
      return this.noticeList.find((item) => item.isRead === false)
    },
    computedAuthorizationDays () {
      const days = this.authorizationInfo.authorizationDays || JSON.parse(window.localStorage.getItem('authorizationInfo')).authorizationDays
      return days < 0 ? `授权已过期${Math.abs(parseInt(days))}天` : `剩余${days}天`
    },
    checkProductList () {
      const tempProductList = [];
      this.productListNew.forEach(item => {
        tempProductList.push(...item.children);
      })
      if (tempProductList.length === 0 || (tempProductList.length === 1 && tempProductList[0].pro_name === 'seatom')) {
        return false;
      }
      return true;
    },
    logoImg () {
      return this.theme === 'dark' ? require('../../assets/img/nav_dark.png') : require('../../assets/img/nav_light.png')
    }
  },
  watch: {
    '$route.meta.routeName': {
      handler (val) {
        window._toggle(this.indexMap[val])
      }
    }
  },
  created () {
    this.navMain = navMain
    if (this.roleType === 3) {
      this.navMain = this.navMain.slice(0, 1)
    }
    this.userName = window.localStorage.getItem('name') || ''
    this.getMessage({
      userId: this.userId
    })
    this.getProductLinkList();
    this.isLoginLocal = window.localStorage.getItem('localLogin')
    if (!this.isLoginLocal) {
      this.getUserInfo();
    }
  },
  mounted () {
    this.$refs.workspaceWrap.addEventListener('scroll', this.linkPositionFun);
    this.$refs.workspaceWrap.addEventListener('click', this.linkHideFun);
    this.$refs.workspace.addEventListener('scroll', this.scrollEvent)
    this.$el.onclick = () => {
      this.showNoticeList = false
    }
    canvasnav();
  },
  methods: {
    async scrollDown () {
      if (this.modeType === 'share' || this.$route.meta.routeName !== '/') return
      const container = document.querySelector('.workspace-wrap');
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (scrollTop + clientHeight >= scrollHeight) {
        this.$refs.Screen.load()
      }
    },
    ...mapActions('editor', {
      // getShareScreen: 'getShareScreen',
      moveShareScreen: 'moveShareScreen',
      updateShareScreen: 'updateShareScreen',
      getMessage: 'getMessage',
      updateMessage: 'updateMessage',
      deleteMessage: 'deleteMessage'
    }),
    delMessage (message) {
      this.deleteMessage({
        id: message.id
      })
    },
    async getLicenseCode () {
      const res = await getLicenseCode();
      try {
        if (res && res.success) {
          const code = (res.data && res.data.code) || {};
          this.$alert(`授权码为：${code}，请联系管理员获取授权文件。`, '授权码', {
            confirmButtonText: '复制授权码',
            callback: action => {
              if (action === 'confirm') {
                const textarea = document.createElement('textarea');
                textarea.style.position = 'absolute';
                textarea.style.opacity = 0;
                textarea.value = code;
                document.body.appendChild(textarea);
                textarea.focus();
                textarea.select();
                document.execCommand('copy');
                textarea.remove();
                this.$message.success('授权码已复制！');
              }
            }
          }).catch(err => {
            this.$message.error(err);
          });
        } else {
          this.$message.error('授权码获取失败！');
        }
      } catch (e) {
        this.$message.error('授权码获取失败！');
      }
    },
    async upload (file) {
      const data = new FormData();
      data.append('file', file.file);
      try {
        let start = Date.now(); let end = Date.now();
        const res = await uploadLicence(data, {}, progress => {
          this.percentage = Number(Math.floor(progress.loaded / progress.total * 100).toFixed(0));
          end = Date.now();
          const minus = end - start;
          start = end;
          const speed = progress.loaded / minus;
          this.remainTime = Math.max(1, Number(Math.floor((progress.total - progress.loaded) / speed / 1000)));
        });
        end = Date.now();
        setTimeout(() => {
          this.$refs['upload-license'].clearFiles(); // 清空上传文件列表，否则后续不发请求
          this.licenseUploadShow = false;
          this.percentage = 0;
          this.remainTime = 0;
          if (res && res.success) {
            this.$message.success('授权成功！');
            this.$store.commit('user/setOverdueVisible', false);
            window.location.reload();
          } else {
            this.$message.error('授权失败！');
          }
        }, Math.max(1000, end - start));
      } catch (e) {
        this.$refs['upload-license'].clearFiles();
        this.licenseUploadShow = false;
        this.percentage = 0;
        this.remainTime = 0;
        this.$message.error('授权失败！');
      }
    },
    handleChange (file, fileList) {
      if (fileList.length) {
        this.licenseUploadShow = true;
      } else {
        this.licenseUploadShow = false;
      }
    },
    cancelLicense () {
      this.$refs['upload-license'].abort();
    },
    changeActiveManage (val) {
      if (val === 0 || val === -1) {
        // 过滤掉全部应用和共享给我的
        this.form.projectId = this.manage.length ? this.manage[0].id : ''
      } else {
        this.form.projectId = val
      }
    },
    scrollTops (bol, type) {
      this.modeType = type
      if (bol) {
        this.$refs.workspaceWrap.scrollTop = 0
      }
    },
    close () {
      this.manageShow = false
    },
    confirm () {
      const notice = this.currentNotice
      const detail = this.shareInfo
      // this.loading = true
      this.$refs.form.validate(async valid => {
        if (valid) {
          let moveParams = {}
          if (this.messageType === 'usershare') {
            moveParams = {
              screenId: detail.screenId,
              workspaceId: parseInt(window.localStorage.getItem('workspaceId')),
              projectId: this.form.projectId,
              name: detail.screenName
            }
          } else if (this.messageType === 'screenCoedit') {
            // 目前协同消息流程暂不允许用户选择分组，默认未分组，这里留个口
          }
          this.moveShareScreen(moveParams).then(res => {
            this.updateShareScreen({
              data: {
                isAccept: true
              },
              params: {
                id: notice.content.usershare.usershareId
              }
            })
            this.updateMessage({
              data: {
                isRead: true
              },
              params: {
                id: notice.id
              }
            })
            this.loading = false
            this.close()
            emitter.emit('reload')
          }).catch(err => {
            this.loading = false
            this.close()
            console.error(err)
          })
        }
      })
    },
    async handleNotice (notice) {
      this.messageType = notice.type
      let message = ''
      const title = '通知'
      if (notice.type === 'usershare') {
        // 大屏同步消息
        const id = notice.content.usershare.usershareId
        let detail = await getShareScreen({ id })
        detail = detail.data[0]
        if (notice.isRead) {
          if (detail.isAccept) {
            message = '你已接受此项目'
          } else {
            message = '你已拒绝此项目'
          }
          this.$alert(message, title, {
            type: 'info',
            customClass: 'notice-info'
          })
        } else {
          message = `<font style="color: #409EFF;">${detail.userName}</font>给你同步了一个可视化项目<font style="color: #409EFF;">${detail.screenName}</font>`
          this.$confirm(message, title, {
            confirmButtonText: '接受',
            cancelButtonText: '拒绝',
            type: 'info',
            distinguishCancelAndClose: true,
            customClass: 'notice-info',
            dangerouslyUseHTMLString: true,
            callback: (action, instance) => {
              if (action === 'confirm') {
                this.currentNotice = notice
                this.shareInfo = detail
                this.manageShow = true
              } else if (action === 'cancel') {
                this.updateShareScreen({
                  data: {
                    isAccept: false
                  },
                  params: {
                    id
                  }
                })
                this.updateMessage({
                  data: {
                    isRead: true
                  },
                  params: {
                    id: notice.id
                  }
                })
              }
            }
          })
        }
      } else if (notice.type === 'screenCoedit') {
        const screenCoedit = notice?.content?.screenCoedit ?? {}
        const roleMap = { collaborators: '协作者', viewers: '查看者' }
        const actionMap = { add: '添加', update: '修改', remove: '移除' }
        const action = actionMap[screenCoedit.action] ?? ''
        const role = roleMap[screenCoedit.coeditRole] ?? ''
        if (action === 'remove') {
          message = `<font style="color: #409EFF;">${screenCoedit.createUserName}</font>将你从可视化项目 <font style="color: #409EFF;">${screenCoedit.coeditScreenName}</font>中移除`
        } else {
          message = `<font style="color: #409EFF;">${screenCoedit.createUserName}</font>将你${action}为可视化项目 <font style="color: #409EFF;">${screenCoedit.coeditScreenName}</font> 的${role}`
        }
        // 大屏协同消息
        if (notice.isRead) {
          this.$alert(message, title, {
            type: 'info',
            customClass: 'notice-info',
            dangerouslyUseHTMLString: true
          })
        } else {
          this.$confirm(message, title, {
            confirmButtonText: '我知道了',
            type: 'info',
            showCancelButton: false,
            customClass: 'notice-info',
            dangerouslyUseHTMLString: true,
            beforeClose: async (action, instance, done) => {
              if (action === 'confirm') {
              // 更新消息状态
                this.updateMessage({
                  data: {
                    isRead: true,
                    type: notice.type
                  },
                  params: {
                    id: notice.id
                  }
                })
                done()
              } else {
                done()
              }
            }
          }).catch(() => {})
        }
      }
    },
    showNotice (e) {
      this.showNoticeList = true
    },
    getNoticeState (state) {
      let text = '未读'
      if (state) text = '已读'
      return text
    },
    scrollEvent (e) {
      const scrollTop = this.$refs.workspace.scrollTop
      if (scrollTop > 190) {
        this.fixed = true
      } else {
        this.fixed = false
      }
    },
    linkPositionFun (e) {
      const workspaceWrap = e.target;
      if (workspaceWrap && workspaceWrap.scrollTop >= 112) {
        this.topProduct = true;
        this.productListTop = { top: 56 + 'px' };
      } else if (workspaceWrap && workspaceWrap.scrollTop < 112) {
        this.topProduct = false;
        this.productListTop = { top: 128 - workspaceWrap.scrollTop + 'px' };
      }
    },
    linkHideFun (e) {
      if (this.showProductLink && (typeof e.target.className === 'string' && !e.target.className.includes('workspace-top-header-product') &&
      !e.target.className.includes('arrow') && !e.target.className.includes('product-link-wrap'))) {
        this.showProductLink = false;
      }
    },
    async getProductLinkList () {
      const res = await getProductList();
      if (res && res.success) {
        this.productList = res.data;
        this.$refs.productList.setData(res.data)
        this.productList.forEach(item => {
          this.productListNew.forEach(it => {
            if (item.product_category === it.type) {
              if (item.pro_name !== 'ucenter' && (item.chat_has || item.group_has || item.has_modules)) {
                try {
                  item.imgUrl = require('../../assets/img/linkIcon/' + item.pro_name + '.png');
                } catch (e) {
                  item.imgUrl = require('../../assets/img/linkIcon/default-pro.png');
                }
                it.children.push(item);
              }
            }
          })
        })
      }
    },
    toggle (index, route) {
      if (this.$route.meta.routeName === route) {
        return
      }
      window._toggle(index)
      this.active = index
      this.$router.push(
        `/workspace/${parseInt(this.$route.params.workspaceId)}${route}`
      )
    },
    async  logout () {
      const localLogin = window.localStorage.getItem('localLogin') || null
      window.localStorage.removeItem('userId')
      window.localStorage.removeItem('token')
      window.localStorage.removeItem('name')
      window.localStorage.removeItem('copyCom')
      window.localStorage.removeItem('localLogin')
      window.localStorage.removeItem('username')
      if (localLogin) {
        this.$router.push('/loginlocal')
        return
      }
      const res = await screenConfig()
      this.$store.commit('user/setUserId', null)
      if (res && res.data) {
        if (res.data.ucenter) {
          window.open(`${res.data.dmcAddress.address}/api/ucenter/account/logout?pro_name=seatom`, '_self');
        } else {
          this.$router.push('/login')
        }
      }
    },
    changeTheme () {
      const theme = window.localStorage.getItem('theme')
      if (theme === 'light') {
        toggleTheme('dark')
        this.$store.commit('changeTheme', 'dark')
      } else {
        toggleTheme('light')
        this.$store.commit('changeTheme', 'light')
      }
    },
    async getUserInfo () {
      const param = {
        user_id: this.userId,
        dmc_request: 1,
        sys_pro_name: 'seatom',
        router: '首页'
      }
      const res = await getDmcUserInfo(param)
      if (res && res.success) {
        if (res.data.data && res.data.data.status === '1') {
          this.$router.push('/login')
        }
        this.baseInfoData = res.data.data.result
        window.localStorage.setItem('username', this.baseInfoData.username) // 保存下用户的登录账号
        this.baseInfoData.level = this.userType[this.baseInfoData.role - 1].text
      }
    },
    jumpTo () {
      const url = window.location.origin + (process.env.BASE_URL || '/') + 'tutorial'
      window.open(url, '_blank')
    },
    ToSetting () {
      this.$router.push('/setting')
    },
    clickCenter () {
      const param = {
        pro_name: 'ucenter',
        dmc_request: 1,
        sys_pro_name: 'seatom',
        router: '首页'
      }
      jumpUcenter(param).then(res => {
        if (res && res.success) {
          window.location.href = res.data.data.result
        }
      })
    }
  },
  beforeDestroy () {
    this.$refs.workspace.removeEventListener('scroll', this.scrollEvent);
    this.$refs.workspaceWrap.removeEventListener('scroll', this.linkPositionFun);
    this.$refs.workspaceWrap.removeEventListener('click', this.linkHideFun);
  }
}
</script>

<style lang="scss" scoped>
.workspace {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: var(--seatom-background-200);
  overflow: hidden;
  ::-webkit-scrollbar {
    background: var(--seatom-mono-200) !important;
  }

  ::-webkit-scrollbar-thumb{
    background-color: var(--seatom-mono-a500) !important;
    border: unset !important;
  }
  .workspace-wrap {
    height: 100%;
    overflow: auto;
    overflow-x: hidden;
  }
  &-top {
    position: sticky;
    top: -165px;
    z-index: 9;// 22;

    &-header {
      position: fixed;
      top: 0;
      padding: 0 10px;
      z-index: 999;
      width: 100%;
      height: 56px;
      line-height: 32px;
      display: flex;
      // justify-content: flex-end;
      background: linear-gradient(270deg, rgba(43, 65, 103, 0.16) 0.94%, rgba(43, 65, 103, 0) 98.8%);;
      &-img {
        margin: 5px 6px;
      }
      &-product {
        // position: absolute;
        // z-index: 999;
        // display: flex;
        // align-items: center;
        // padding: 8px 12px;
        // margin-top: 9px;
        // width: 108px;
        // height: 38px;
        // background: var(--seatom-mono-a200);
        // color: var(--seatom-type-800);
        // border-radius: 8px;
        // font-family: 'PingFang SC';
        // font-style: normal;
        // font-weight: 600;
        // font-size: 16px;
        // line-height: 32px;
        // cursor: pointer;
        margin-left: 10px;
        .arrow {
          color: var(--seatom-type-800);
        }
      }
      &-product:hover {
        color: #FFFFFF;
        .arrow {
          color: #FFFFFF;
        }
      }
      &-user {
        height: 100%;
        display: flex;
        align-items: center;
        position: absolute;
        right: 10px;
        &-license {
          color: var(--seatom-type-900);
          font-size: 14px;
          margin-right: 20px;
          cursor: default;
        }

        &-head {
          display: flex;
          height: 24px;
          width: 24px;
          background: var(--seatom-mono-a300);
          border-radius: 24px;
          font-size: 16px;
          color: var(--seatom-type-900);
          justify-content: center;
          align-items: center;
          margin-right: 6px;
        }

        &-text {
          font-family: PingFang SC;
          font-style: normal;
          font-weight: normal;
          font-size: 12px;
          color: var(--seatom-type-900);
          max-width: 200px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          user-select: none;
        }
        .baffle {
          height: 8px;
          width: 2px;
          background: var(--seatom-mono-a300);
          margin: 0 18px;
        }
        .operation-icon {
          margin-left: 34px;
          font-size: 16px;
          color: var(--seatom-type-900);
          .notice-box {
            display: inline-block;
            position: relative;
            .notice-indicator {
              position: absolute;
              right: 0;
              top: 5px;
              background-color: red;
              width: 5px;
              height: 5px;
              border-radius: 50%;
            }
          }
        }
      }
    }

    &-nav {
      display: flex;
      position: absolute;
      top: 0;
      flex-direction: column;
      background: var(--seatom-background-200);
      height: 285px; // 220px; // 290px;
      transition: 0.5s height cubic-bezier(0.65, 0.05, 0.36, 1);
      width: 100%;
      &-img {
        width: 100%;
        height: 290px;
        background-color: white;
        background: var(--seatom-main-bg) no-repeat top / cover;
        transition: 0.5s opacity cubic-bezier(0.4, 0, 1, 1);
        // -webkit-mask-image: linear-gradient(
        //     89.91deg,
        //     rgba(25, 29, 37, 0) 0.1%,
        //     rgba(22, 25, 31, 0.56) 65.69%,
        //     #13151a 99.94%
        // );
      }

      &-text {
        background: var(--seatom-main-text) no-repeat top /
          cover;
        transition: 0.5s opacity cubic-bezier(0.4, 0, 1, 1);
        height: 137px;
        width: 590px;
        position: absolute;
        top: 52px;
        left: 47px;
      }
    }
  }

  &-menu {
    z-index: 20;
    display: flex;
    top: 56px;
    position: sticky;
    width: 10000px;
    margin-top: 220px;
    min-width: 1024px;
    user-select: none;
    align-items: center;
    // transform: translateX(-280px);
    transition: 0.2s;
  }

  .notice-dropdown-list {
    font-size: 16px;
    color: var(--seatom-type-900);
    line-height: 32px;
    width: 340px;
    height: 240px;
    position: absolute;
    border-radius: 5px;
    right: 70px;
    top: 56px;
    z-index: 1000;
    // background-color: #1f2430;
    background: var(--seatom-background-300);
    box-shadow: var(--seatom-container-c300);
    .notice-header {
      color: var(--seatom-type-900);
      font-size: 14px;
      padding: 5px 10px;
      border-bottom: 1px solid var(--seatom-mono-a100);
    }
    .notice-list {
      height: 197px;
      .list-item {
        list-style: none;
        display: flex;
        padding: 5px 10px;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        cursor: pointer;
        &:hover {
          background-color: rgba(61, 133, 255, 0.1);
        }
        .usershare,
        .screenCoedit {
          font-size: 16px;
          color: #409eff;
        }
        .notice-body {
          display: flex;
          align-items: center;
        }
        .notice-state {
          line-height: 20px;
          transform: scale(0.8);
          padding: 0 5px;
          border: 1px solid;
          border-radius: 3px;
        }
        &.read {
          .notice-state {
            color: #67c23a;
            border-color: #67c23a;
          }
          .notice-body {
            filter: brightness(50%);
          }
        }
        &.unread {
          .notice-state {
            color: #e6a23c;
            border-color: #e6a23c;
          }
        }
        .notice-content {
          max-width: 240px;
          margin-left: 10px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .empty-info {
        min-height: 60px;
        position: relative;
        text-align: center;
        .empty-text {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }

  // .gray-bg {
  //   background-color: rgb(23, 27, 34);
  //   background: #1F2430;
  // }
  .scroll-icon {
    height: 60px;
    width: 280px;
    // background: url('../../assets/img/scroll_icon.png') no-repeat center / contain;
  }
  .scroll-none {
    display: none;
  }
  .scroll-height {
    min-height: calc(100vh - 120px);
  }
  .scroll-animation {
    transform: translateX(0);
    transition: 0.2s;
  }

  &-shadow {
    background: linear-gradient(180deg, transparent, #171b22);
    height: 30px;
    position: relative;
    width: 100%;
    z-index: 1;
    top: -24px;
  }
}
.workspace-shadow-box {
  position: sticky;
  top: 92px;
  z-index: 10;
}
.nav-main {
  span:first-of-type {
    margin-left: 65px;
    .nav-active::after {
      left: 30px;
    }
  }
}

.not-active {
  background: var(--seatom-mono-a100);
  border-top-left-radius: 50px;
  border-top-right-radius: 50px;
}

.nav-main .nav-span .nav-link {
  text-decoration: none !important;
  display: block;
  color: var(--seatom-type-800);
  width: auto;
  min-width: 140px;
  line-height: 64px;
  font-size: 18px;
  text-align: left;
  cursor: pointer;
  padding: 0 40px;
  position: relative;
  font-weight: 700;

  .nav-icon {
    color: var(--seatom-primary-900);
  }
}
.nav-main .nav-span .nav-active {
  font-family: 思源黑体Medium;
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  color: #FFFFFF;

  .nav-icon {
    color: #FFF;
  }

  &::after {
    content: "";
    display: block;
    height: 6px;
    width: 132px;
    position: absolute;
    left: 3px;
    bottom: 0px;
    // background-color: var(--seatom-primary-a400);
    background: var(--seatom-menu-dark) no-repeat center /
      contain;
  }
}
.datav-icon {
  display: inline-block;
  vertical-align: middle;
}
.datav-font {
  font-family: "datav-font" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.nav-main .nav-icon {
  padding-right: 5px;
  font-size: 20px;
}
.nav-main {
  z-index: 20;
  display: flex;
  // top: 200px;
  position: relative;
  width: 100%;
  min-width: 1024px;
  height: 64px;
}
.pointer {
  cursor: pointer;
}
.mr-8 {
  margin-right: 8px;
}
.mr-15 {
  margin-right: 15px;
}
.ml-15 {
  margin-left: 15px;
}
.overdue-dialog {
  z-index: 999;
  background: rgba(0, 0, 0, .5);
  ::v-deep .el-dialog,
  ::v-deep .el-dialog__body,
  ::v-deep .el-dialog__header {
    padding: 0;
    margin: 0;
  }
  ::v-deep .el-dialog {
    width: 1176px;
  }
  .overdue-container {
    height: 600px;
    width: 1176px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .overdue-cover {
      height: 600px;
      width: 576px;
      display: block;
      user-select: none;
    }
    .overdue-info {
      height: 100%;
      width: 600px;
      padding: 30px;
      user-select: none;
      position: relative;
      &__header {
        margin-top: 35px;
        color: #FFFFFF;
        font-weight: 600;
        font-size: 20px;
      }
      &__desc {
        margin-top: 30px;
        overflow: hidden;
        p {
          margin-top: 20px;
          text-align: justify;
          text-justify: inter-ideograph;
          text-indent: 2em;
          color: white;
        }
      }
      &__upload {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 30px;
        display: flex;
        gap: 28px;
        justify-content: center;
      }
    }
  }
}
.license-dialog {
  z-index: 999;
  background: rgba(0, 0, 0, .5);
  ::v-deep .el-dialog,
  ::v-deep .el-dialog__body,
  ::v-deep .el-dialog__header {
    padding: 0;
    margin: 0;
  }
  ::v-deep .el-dialog {
    width: 1176px;
  }
  .license-container {
    height: 600px;
    width: 1176px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .license-cover {
      height: 600px;
      width: 576px;
      display: block;
      user-select: none;
    }
    .license-info {
      height: 100%;
      width: 600px;
      padding: 30px;
      user-select: none;
      &__header {
        margin-top: 35px;
        color: #FFFFFF;
        font-weight: 600;
        font-size: 20px;
      }
      &__desc {
        margin-top: 30px;
        overflow: hidden;
        p {
          margin-top: 20px;
          text-align: justify;
          text-justify: inter-ideograph;
          text-indent: 2em;
          color: white;
        }
      }
    }
  }
}
.progress-dialog {
  .info {
    margin-top: 16px;
  }
}
.show-product-link {
  color: #FFFFFF;
}
.darkIcon {
  filter: invert(1);
}
.manage-box {
  height: 32px;
  display: flex;
  gap: 16px;
  align-items: center;
  align-content: center;
}
.icon {
  height: 32px;
  width: 32px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon:hover{
  background-color: var(--seatom-primary-a300);
}
.divider {
  width: 1px;
  height: 16px;
}
.divider_dark {
  background-color: rgba(255,255,255,0.24);
}
.divider_light {
  background-color: rgba(15, 34, 67, 0.11);
}
.logo{
  display: flex;
}
</style>
<style lang="scss">
.notice-info {
  .el-message-box__status {
    color: #67C23A;
  }
}
.user-trigger {
  border-radius: 8px;
  padding: 4px 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.user-trigger:hover{
  background: rgba(43, 121, 255, 0.24);
}
.user-dropdown-menu{
  background: rgba(255, 255, 255, 0.64);
  box-shadow: 0px 8px 24px rgba(15, 34, 67, 0.16);
  backdrop-filter: blur(12px);
  border-radius: 8px;
  border: 0px;
  overflow: hidden;
  .user-dropdown-container{
    padding: 6px 16px;
    .user-dropdown-top{
    display:flex;
    align-items:center;
    padding:0px 16px;
    justify-content:space-between;
    .top-text{
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: max-content;
      margin-left: 24px;
      .name{
        color: rgba(21, 22, 24, 0.92);
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
        margin: 0;
        margin-bottom: 4px
      }
      .role{
        border: 1px solid #1F71FF;
        border-radius: 6px;
        padding: 2px 6px;
        color: #1F71FF;
        font-weight: 600;
        font-size: 12px;
        line-height: 20px;
        white-space: nowrap;
      }
    }
    }
    .user-dropdown-body{
    display: flex;
    flex-direction: column;
    margin-top: 16px;
    div{
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 16px;
      cursor: pointer;
    }
    div:hover{
      background: rgba(15, 34, 67, 0.05);
      border-radius: 4px;
    }
    .text{
      box-sizing: border-box;
      color: rgba(21, 22, 24, 0.72);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    .line{
      height: 1px;
      background: #0F22431C;
      margin: 8px 0;
    }
    }
  }
  .popper__arrow{
    display: none;
  }
}

</style>
