<template>
  <div class="seatom-main" id="seatom-main">
    <el-container class="seatom-main-container">
        <el-aside class="project-manage" ref="manage">
          <template v-if="roleType !== 3">
            <div
              @click="showCoeditToMe"
              class="share-to-me my-project"
              :class="{'project-checked-color': active == coeditSreens.id}">
              <span>共享给我的</span>
              <span class="screens-length">{{coeditSreens.screens&&coeditSreens.screens.length}}</span>
            </div>
            <el-divider></el-divider>
          </template>
          <div class="project-manage-title">
            <div class="project-manage-title-header">
              <span>我的分组</span>
              <hz-icon @click="addManage" name="plus" style="font-size: 16px;"></hz-icon>
            </div>
          </div>
          <div
            @click="showAll"
            class="project-manage-title-bottom my-project"
            :class="{'project-checked-color': active == ''}">
            <span style="display: flex; align-items: center;"><hz-icon style="font-size: 20px; margin-right: 4px" name="screen-all"/>全部应用</span>
            <span class="screens-length">{{allTotalScreens}}</span>
          </div>
          <div class="manage-main">
            <el-input v-if="isAdd" v-model.trim="addValue" v-focus size="mini" @blur="addGroup()" class="my-project project-input"></el-input>
            <div
              @click="showChecked(item)"
              v-for="(item, index) in manage"
              :key="item.id"
              class="my-project"
              :ref="`curCheck${item.id}`"
              @dragenter.prevent="handleDragEnter($event, $refs[`curCheck${item.id}`])"
              @dragleave="handleDragLeave($event, $refs[`curCheck${item.id}`])"
              @dragover.prevent="handleDragOver($event, $refs[`curCheck${item.id}`])"
              @drop="handleDrop($event, item)"
              :class="{'project-checked-color': active == item.id, 'custom-project': item.type, 'drag-bg': isDrag }">
              <div class="project-name">
                <span v-if="item.isEdit"><hz-icon style="font-size: 20px; margin-right: 4px" name="screen-item" />{{item.type?item.name : '未分组'}}</span>
                <el-input v-focus @blur="inputBlur(item)" v-model.trim="item.inputText" size="mini" class="project-input" v-else></el-input>
              </div>
              <span class="screens-length">{{item.totalScreens}}</span>
              <div v-if="item.type" class="edit-del">
                <span class="edit mr10" @click="editGroupName(item)"><hz-icon :class="{'edit-color': !item.isEdit}" name="edit"></hz-icon></span>
                <span class="delete" @click="del(item, index, false)"><hz-icon name="trash"></hz-icon></span>
              </div>
            </div>
          </div>
        </el-aside>

        <el-main>
          <div class="create-entry" v-if="roleType !== 3">
            <div class="create-title">选择下面的方式进行创建</div>
            <div class="create-list">
              <div
                class="create-item"
                :class="[theme === 'dark' ? 'create-item-bg-dark' : 'create-item-bg-light']"
                v-for="item in screenOpt"
                :key="item.type"
                @click="createScreen(item)">
                <div class="item-logo">
                  <img :src="item.logo">
                </div>
                <div class="item-name">{{ item.title }}</div>
                <div class="tag" v-if="item.type === 'ai'">new</div>
              </div>
            </div>
          </div>
          <div class="project-content-header">
            <div class="project-content-header-title">
              <h2 class="mr-8">{{screens.name == '项目' ? '未分组' : screens.name}}</h2>
              <span class="r-screens-length mr-24">{{nowScreensLength}}</span>
              <div style="width: 245px;margin-right: 8px;">
                <el-input
                  class="input-theme"
                  placeholder="请输入搜索内容"
                  size="mini"
                  prefix-icon="el-icon-search"
                  v-model="searchValue"
                  @input="searchScreens()"
                  >
                </el-input>
              </div>
              <!-- <div style="margin-left: 20px;" class="project-content-header-search">
              <el-radio-group v-model="radio">
                <el-radio label="">全部</el-radio>
                <el-radio label="mobile">移动端</el-radio>
                <el-radio label="pc">pc端</el-radio>
              </el-radio-group>
            </div> -->
            </div>
            <!-- <div class="project-content-header-search">
              <el-select class="screen-select" v-model="resType" placeholder="请选择" popper-class="poper-theme">
                <el-option label="1920*1080" value="1920*1080"></el-option>
                <el-option label="375*667" value="375*667"></el-option>
              </el-select>
            </div> -->
            <!-- <div class="project-content-header-search">
              <el-select class="screen-select" v-model="radio" placeholder="请选择" popper-class="poper-theme">
                <el-option label="全部平台" value=""></el-option>
                <el-option label="移动端" value="mobile"></el-option>
                <el-option label="PC端" value="pc"></el-option>
              </el-select>
            </div> -->
            <div class="project-content-header-search">
              <el-select style="width: 110px" class="screen-select" v-model="radio" placeholder="请选择" popper-class="poper-theme">
                <el-option label="全部平台" value=""></el-option>
                <el-option label="移动端" value="mobile"></el-option>
                <el-option label="PC端" value="pc"></el-option>
              </el-select>
              <el-select class="screen-select" v-model="sortType" placeholder="请选择" popper-class="poper-theme">
                <el-option label="按创建时间排序" value="createdAt"></el-option>
                <el-option label="按修改时间排序" value="updatedAt"></el-option>
              </el-select>
            </div>
          </div>
          <div class="project-second-header">
            <el-checkbox  v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
            <el-button :class="{ 'noCheck': checkedScreen.length === 0 }" v-if="roleType !== 3" type="primary" :disabled="checkedScreen.length === 0" @click="Batchexport()" >
              <div class="add-text"><hz-icon name="upload" style="margin-right: 8px;"></hz-icon>导出</div>
            </el-button>
            <el-button style="border-radius: 3px;" :class="{ 'noCheckDel': checkedScreen.length === 0 }" :disabled="checkedScreen.length === 0"  class="el-button--plain" type="primary" @click="Batchdel()">
              <div class="add-text"><hz-icon name="trash" style="margin-right: 8px;"></hz-icon>
                <span>删除</span>
              </div>
            </el-button>
          </div>
          <div ref="scrollTop" class="main-screen" v-if="allSreens.screens.length && mode === 'selfish'" @scroll="load">
            <div class="my-screen" v-for="(screen, index) in allSreens.screens" :key="screen.id">
              <div class="screen" @dragstart="handleDragStart($event, screen)" @dragend="handleDragEnd">
                <div class="terminal-icon" v-if="screen.isConnect"></div>
                <el-checkbox-group v-model="checkedScreen" @change="handleCheckedScreenChange">
                      <el-checkbox v-if="!screen.coeditId" :label="screen.id" :key="screen.id"></el-checkbox>
                </el-checkbox-group>
                <div class="screen-info">
                  <div class="screen-img" :class="{'img-filter': !screen.thumbnail}">
                    <img v-if="screen.thumbnail" v-lazy="getIhumbmailUrl(screen)">
                  </div>
                  <div class="screen-edit">
                    <div class="screen-button">
                      <div class="screen-btn" @click="goScreenEdit(screen)" v-if="checkBtnStatus(screen,{}) && roleType !== 3"><hz-icon name="edit" class="mr-8"></hz-icon>编辑</div>
                      <div class="main-button">
                        <el-tooltip :visible-arrow="false" popper-class="icon-tooltip" v-if="active!== -1" content="拖拽开始移动" placement="bottom">
                          <span class="pointer" draggable="true"><hz-icon name="move"></hz-icon></span>
                        </el-tooltip>
                        <el-tooltip content="导出" v-if="roleType !== 3" placement="bottom" :visible-arrow="false" popper-class="icon-tooltip">
                          <span class="pointer" @click="exportScreen(screen)"><hz-icon name="move-to"></hz-icon></span>
                        </el-tooltip>
                        <el-tooltip :content="roleType !== 3 ? '预览' : '查看'" :visible-arrow="false" placement="bottom" popper-class="icon-tooltip">
                          <span class="pointer"  @click="preview(screen)"><hz-icon name="entity-view"></hz-icon></span>
                        </el-tooltip>
                        <el-tooltip :visible-arrow="false" popper-class="icon-tooltip" content="发布" placement="bottom" v-if="checkBtnStatus(screen,{ pblBtn: true }) && roleType !== 3">
                          <span class="publish pointer" @click="publishScreen(screen)">
                            <hz-icon name="publish"></hz-icon>
                          </span>
                        </el-tooltip>
                        <el-tooltip :visible-arrow="false" popper-class="icon-tooltip" content="同步" placement="bottom" v-if="roleType !== 3">
                          <span class="mr6 pointer" @click="syncScreen(screen)"><hz-icon name="sync"></hz-icon></span>
                        </el-tooltip>
                        <el-tooltip :visible-arrow="false" popper-class="icon-tooltip" content="共享" placement="bottom" v-if="checkBtnStatus(screen,{ shareBtn: true }) && roleType !== 3">
                          <span class="mr6 pointer" @click="shareScreen(screen)"><hz-icon name="share"></hz-icon></span>
                        </el-tooltip>
                        <template v-if="roleType !== 3">
                          <el-popover
                            placement="bottom-end"
                            popper-class="more-action-popover"
                            :visible-arrow="false"
                            trigger="hover">
                              <div class="more-action-container">
                                <span @click="copyScreen(screen)">复制</span>
                                <span @click="del(screen, index, true)" class="button-span" v-if="checkBtnStatus(screen,{ deleteBtn: true })">删除</span>
                              </div>
                              <span class="pointer" slot="reference" ><hz-icon name="other"></hz-icon></span>
                          </el-popover>
                        </template>
                        <el-tooltip :visible-arrow="false" popper-class="icon-tooltip" content="删除" placement="bottom" v-else>
                          <span class="mr6 pointer" @click="del(screen, index, true)"><hz-icon name="trash"></hz-icon></span>
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                  <div class="screen-coedit" v-if="screen.coeditInfo">
                    <hz-icon
                      style="font-size: 18px"
                      class="pointer mr-26"
                      name="coediter">
                    </hz-icon>
                      {{ handleCoediterNum(screen) }}
                  </div>
                </div>
                <div class="screen-main">
                  <div class="screen-name-input">
                    <div class="edit-name">
                      <input @focus="inputScreenFocus(screen)" @blur="inputScreenBlur(screen)" class="input" type="text" :title="screen.name" v-model="screen.name">
                    </div>
                    <div class="screen-status">
                      <span class="name-icon" :class="{ 'publish': screen.isPublic }"></span>
                      <span>{{screen.isPublic ? '已发布' : '未发布'}}</span>
                    </div>
                  </div>
                  <div class="publish-info">
                    <div class="screen-size">{{ screen.width }}x{{ screen.heiht }}</div>
                    <div class="update-time">{{screen.updatedAt | formatDate}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div ref="scrollTop" class="main-screen" v-if="allSreens.screens.length && mode === 'share'" @scroll="load">
            <div class="my-screen" v-for="(screen, index) in shareWithMe" :key="screen.id">
              <div class="screen" @dragstart="handleDragStart($event, screen)" @dragend="handleDragEnd">
                <div class="terminal-icon" v-if="screen.isConnect"></div>
                <el-checkbox-group v-model="checkedScreen" @change="handleCheckedScreenChange">
                      <el-checkbox v-if="!screen.coeditId" :label="screen.id" :key="screen.id"></el-checkbox>
                </el-checkbox-group>
                <div class="screen-info">
                  <div class="screen-img" :class="{'img-filter': !screen.thumbnail}">
                    <img v-if="screen.thumbnail" v-lazy="getIhumbmailUrl(screen)">
                  </div>
                  <div class="screen-edit">
                    <div class="screen-button">
                      <div class="screen-btn"  @click="goScreenEdit(screen)" v-if="checkBtnStatus(screen,{})"><hz-icon name="edit" class="mr-8"></hz-icon>编辑</div>
                      <div class="main-button">
                        <el-tooltip :visible-arrow="false" popper-class="icon-tooltip" v-if="active!== -1" content="拖拽开始移动" placement="bottom">
                          <span class="pointer" draggable="true"><hz-icon name="move"></hz-icon></span>
                        </el-tooltip>
                        <el-tooltip content="导出" placement="bottom" :visible-arrow="false" popper-class="icon-tooltip">
                          <span class="pointer" @click="exportScreen(screen)"><hz-icon name="move-to"></hz-icon></span>
                        </el-tooltip>
                        <el-tooltip content="预览" :visible-arrow="false" placement="bottom" popper-class="icon-tooltip">
                          <span class="pointer"  @click="preview(screen)"><hz-icon name="entity-view"></hz-icon></span>
                        </el-tooltip>
                        <el-tooltip :visible-arrow="false" popper-class="icon-tooltip" content="发布" placement="bottom" v-if="checkBtnStatus(screen,{ pblBtn: true })">
                          <span class="publish pointer" @click="publishScreen(screen)">
                            <hz-icon name="publish"></hz-icon>
                          </span>
                        </el-tooltip>
                        <el-tooltip :visible-arrow="false" popper-class="icon-tooltip" content="同步" placement="bottom">
                          <span class="mr6 pointer" @click="syncScreen(screen)"><hz-icon name="sync"></hz-icon></span>
                        </el-tooltip>
                        <el-tooltip :visible-arrow="false" popper-class="icon-tooltip" content="共享" placement="bottom" v-if="checkBtnStatus(screen,{ shareBtn: true })">
                          <span class="mr6 pointer" @click="shareScreen(screen)"><hz-icon name="share"></hz-icon></span>
                        </el-tooltip>
                        <template v-if="true">
                          <el-popover
                            placement="bottom-end"
                            popper-class="more-action-popover"
                            :visible-arrow="false"
                            trigger="hover">
                              <div class="more-action-container">
                                <span @click="copyScreen(screen)">复制</span>
                                <span @click="del(screen, index, true)" class="button-span" v-if="checkBtnStatus(screen,{ deleteBtn: true })">删除</span>
                              </div>
                              <span class="pointer" slot="reference" ><hz-icon name="other"></hz-icon></span>
                          </el-popover>
                        </template>
                      </div>
                    </div>
                  </div>
                  <div class="screen-coedit" v-if="screen.coeditInfo">
                    <hz-icon
                      style="font-size: 18px"
                      class="pointer mr-26"
                      name="coediter">
                    </hz-icon>
                      {{ handleCoediterNum(screen) }}
                  </div>
                </div>
                <div class="screen-main">
                  <div class="screen-name-input">
                    <div class="edit-name">
                      <!-- <hz-icon name="edit"></hz-icon> -->
                      <input @focus="inputScreenFocus(screen)" @blur="inputScreenBlur(screen)" class="input" type="text" :title="screen.name" v-model="screen.name">
                    </div>
                    <div class="screen-status">
                      <span class="name-icon" :class="{ 'publish': screen.isPublic }"></span>
                      <span>{{screen.isPublic ? '已发布' : '未发布'}}</span>
                    </div>
                  </div>
                  <div class="publish-info">
                    <div class="screen-size">{{ screen.width }}x{{ screen.heiht }}</div>
                    <div class="update-time">{{screen.updatedAt | formatDate}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <div v-if="!allSreens.screens.length">
          <NoDataContainer></NoDataContainer>
        </div>
        </el-main>
    </el-container>
    <!-- 导入大屏 -->
    <ImportScreen v-model="importShow" @refresh="refreshData" :manage="manage" :active="active" />
    <!-- 大屏发布 -->
    <PublishScreen ref="pub" @success="handlePublishScreenSucc" @close="handlePublishScreenClose" />
    <!-- 大屏同步=> 原共享功能更名为同步功能，逻辑不变 -->
    <ScreenShare ref="screensync" />
    <!-- 大屏共享 -->
    <ApplicationShare
      @refreshData='refreshData'
      @cancelShare="showScreenshare=false"
      v-if="showScreenshare"
      :currScreenInfo="currScreenInfo"
      />
    <!-- AI生成 -->
    <ai-dialog ref="aiDialog"></ai-dialog>
    <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import { getProject, createProject, updateProject, delProject } from '@/api/workspace'
import { updateScreen, deleteScreen, screenCopy, checkToken, coeditScreenMove, paging } from '@/api/screen'
import { sharedToMe } from '@/api/screencoedit'
import ImportScreen from '@/components/editor/ImportScreen'
import PublishScreen from '@/components/editor/PublishScreen'
import ScreenShare from '@/components/editor/ScreenShare.vue';
import ApplicationShare from '@/components/editor/ApplicationShare.vue';
import NoDataContainer from '@/components/editor/NoDataContainer.vue';
import { openNewTab } from '@/utils/dom';
import { replaceUrl, randomStr } from '@/utils/base';
import emitter from '@/utils/bus';
import { mapState, mapGetters } from 'vuex';
import createSocket from '@/utils/coeditSocket'

export default {
  name: 'my-visualization',
  components: {
    ImportScreen,
    PublishScreen,
    ScreenShare,
    ApplicationShare,
    NoDataContainer,
    AiDialog: () => import('@/components/editor/AIDialog')
  },
  props: {
  },
  data () {
    return {
      replaceUrl: replaceUrl,
      allSreens: {
        name: '全部应用',
        id: 0,
        screens: []
      }, // 所有应用
      coeditSreens: {
        name: '共享给我的',
        id: -1,
        screens: []
      }, // 所有应用
      unGroupId: '',
      manage: [], // 左侧目录
      screens: {}, // 当前展示的大屏项目
      active: localStorage.getItem('projectId') ? localStorage.getItem('projectId') : '', // 当前active的
      isAdd: false, // 是否添加输入框
      addValue: '',
      searchValue: localStorage.getItem('searchValue'),
      curScreenName: '',
      importShow: false,
      sortType: localStorage.getItem('sortType') ? localStorage.getItem('sortType') : 'createdAt',
      loading: false,
      dragImg: require('@/assets/img/drag.png'),
      isDrag: false,
      loaded: true, // 控制组件重新加载
      showScreenshare: false,
      currScreenInfo: {},
      socket: null,
      allScreenCoeditList: {}, // 所有正在编辑的协同列表
      projectId: localStorage.getItem('projectId') ? localStorage.getItem('projectId') : '',
      workspaceId: null,
      page: 1,
      radio: localStorage.getItem('radio') ? localStorage.getItem('radio') : '',
      allTotalScreens: 0,
      scroolDisabled: true,
      nowScreensLength: 0,
      mode: 'selfish',
      shareWithMe: [],
      dataLock: false,
      convertToStatic: false,
      checkedScreen: [],
      isIndeterminate: true,
      checkAll: false
    }
  },
  directives: {
    focus: {
      inserted: function (el) {
        el.querySelector('input').focus()
      }
    }
  },
  computed: {
    ...mapState({
      theme: (state) => state.theme
    }),
    ...mapGetters({
      isSocketConnect: 'editor/isSocketConnect'
    }),
    screenOpt () {
      return [
        { title: 'PC应用', logo: this.theme === 'dark' ? require('@/assets/img/pc-screen-dark.png') : require('@/assets/img/pc-screen-light.png'), type: 'pc' },
        { title: '移动端应用', logo: this.theme === 'dark' ? require('@/assets/img/mobile-screen-dark.png') : require('@/assets/img/mobile-screen-light.png'), type: 'mobile' },
        // { title: 'AI生成', logo: this.theme === 'dark' ? require('@/assets/img/ai-dark.png') : require('@/assets/img/ai-light.png'), type: 'ai' },
        { title: '导入应用', logo: this.theme === 'dark' ? require('@/assets/img/import-screen-dark.png') : require('@/assets/img/import-screen-light.png'), type: 'import' }
      ]
    },
    getIhumbmailUrl () {
      return function (screen) {
        return replaceUrl(screen.encryptThumbnail
          ? (process.env.VUE_APP_IMG_SERVER_URL ||
              process.env.VUE_APP_SERVER_URL) +
              screen.encryptThumbnail +
              '?width=290&height=162'
          : screen.thumbnail);
      };
    },
    roleType () {
      return this.$store.state.user.roleType
    }
  },
  watch: {
    sortType (newValue) {
      if (this.mode === 'share') {
        this.sort();
      } else {
        this.$emit('scrollTops', true)
        localStorage.setItem('sortType', this.sortType)
        this.page = 1
        this.scroolDisabled = true
        this.getManage()
      }
    },
    radio () {
      if (this.mode === 'share') {
        this.searchScreens()
      } else {
        this.$emit('scrollTops', true)
        localStorage.setItem('radio', this.radio)
        this.page = 1
        this.scroolDisabled = true
        this.getManage()
      }
    },
    searchValue () {
      if (this.mode === 'selfish') {
        this.$emit('scrollTops', true)
        localStorage.setItem('searchValue', this.searchValue)
        this.scroolDisabled = true
        this.page = 1
      }
    },
    active: {
      handler (newVal) {
        this.$emit('changeActiveManage', newVal);
      },
      immediate: true
    }
  },
  async created () {
    this.getList()
    this.getManage();
    this.screens = this.allSreens;
    this.initCheckToken();
    emitter.on('reload', () => {
      this.refreshData();
    });

    const coeditRes = await sharedToMe()
    if (coeditRes.success) {
      this.coeditSreens.screens = coeditRes.data
    }
    this.$emit('scrollTops', true)
  },
  mounted () {
    this.handleCoeditSocket()
    this.img = new Image();
    this.img.src = this.dragImg;
  },
  methods: {
    handleCheckedScreenChange (value) {
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.allSreens.screens.length;
      // this.isIndeterminate = checkedCount > 0 && checkedCount < this.allSreens.screens.length;
    },
    handleCheckAllChange (val) {
      const checkedScreenArr = []
      this.allSreens.screens.forEach(it => {
        if (!it.coeditId) {
          checkedScreenArr.push(it.id)
        }
      })
      this.checkedScreen = val ? checkedScreenArr : []
      this.isIndeterminate = false
    },
    // 我的可视化创建socket实例
    handleCoeditSocket () {
      if (this.$coeditSocket) {
        this.socket = this.$coeditSocket
        this.socket.emitter.addListener('allScreenCoeditList', (data) => {
          this.allScreenCoeditList = data
        }, this)
      } else {
        createSocket().then(async () => {
          this.socket = this.$coeditSocket
          this.socket.emitter.addListener('allScreenCoeditList', (data) => {
            this.allScreenCoeditList = data
          }, this)
        })
      }
    },
    // 计算当前屏协同人数
    handleCoediterNum (screen = {}) {
      const { coeditInfo = {} } = screen
      if (Object.keys(coeditInfo) === 0) {
        return null
      } else {
        const { coeditUsers = [] } = coeditInfo
        // 算上创建者自己
        return coeditUsers.length + 1
      }
    },
    // 判断按钮权限
    checkBtnStatus (screen = {}, { shareBtn = false, pblBtn = false, deleteBtn = false }) {
      const userId = localStorage.getItem('userId')
      const { coeditInfo } = screen
      if (!coeditInfo) {
        // 不是协同大屏
        return true
      }
      const { createUserId, coeditUsers } = coeditInfo

      if (shareBtn) {
        // 控制共享按钮
        if (createUserId === userId) {
        // 创建者才可以共享
          return true
        } else {
          return false
        }
      }

      if (createUserId === userId) {
        // 创建者
        return true
      } else {
        // 获取当前用户在该协同大屏的角色
        const roleInScreen = coeditUsers.find(item => {
          return item.coeditUserId === userId
        })
        if (roleInScreen) {
          if (roleInScreen.coeditRole === 'viewers') {
            return false
          } else {
            // 协作者
            if (deleteBtn) {
              return false
            }
            return true
          }
        }
      }
    },
    initCheckToken () {
      checkToken({})
    },
    async getList (isDel = false) {
      const res = await getProject({ workspaceId: parseInt(this.$route.params.workspaceId) });
      if (res.success) {
        this.allTotalScreens = 0
        this.manage = res.data.map(item => {
          if (item.type === 0) this.unGroupId = item.id
          this.allTotalScreens += item.totalScreens
          return { isEdit: true, inputText: '', ...item }
        })
        const totl = res.data.filter(it => {
          return it.id === parseInt(this.projectId)
        })
        if (!totl[0]) {
          let allTotl = 0
          res.data.forEach(it => {
            allTotl += it.totalScreens
          })
          this.nowScreensLength = allTotl
        } else {
          this.nowScreensLength = totl[0].totalScreens
        }
      }
    },
    async getManage (isDel = false) {
      // const params = {
      //   projectId: this.projectId,
      //   workspaceId: parseInt(this.$route.params.workspaceId),
      //   query: this.searchValue,
      //   platform: this.radio,
      //   sortField: this.sortType,
      //   pageSize: 10,
      //   page: 1
      // }
      // const items = await paging(params)
      this.pagingInterface().then((res) => {
        if (res.success) {
          this.allSreens.screens = res.data
          this.screens = this.allSreens
        }
      })
    },
    addManage () {
      this.isAdd = true;
    },
    async showAll (event) {
      this.loading = true
      this.mode = 'selfish'
      this.$emit('scrollTops', true, this.mode)
      // this.$emit('scroll', this.mode)
      this.active = this.allSreens.id
      this.checkAll = false
      this.checkedScreen = []
      this.scroolDisabled = true
      this.projectId = ''
      this.page = 1
      this.searchValue = ''
      this.screens.name = '全部应用'
      this.allSreens.screens = [];
      localStorage.setItem('projectId', this.projectId)
      const restotl = await getProject({ workspaceId: parseInt(this.$route.params.workspaceId) });
      if (restotl.success) {
        let allTotl = 0
        restotl.data.forEach(it => {
          allTotl += it.totalScreens
        })
        this.nowScreensLength = allTotl
      }
      // const params = {
      //   workspaceId: parseInt(this.$route.params.workspaceId),
      //   projectId: this.projectId,
      //   query: this.searchValue,
      //   platform: this.radio,
      //   sortField: this.sortType,
      //   pageSize: 10,
      //   page: 1
      // }
      // const res = await paging(params)
      this.pagingInterface().then((res) => {
        if (res.success) {
          this.allSreens.screens = res.data
          this.loading = false
        }
      })
    },
    // 共享给我的
    async showCoeditToMe () {
      this.mode = 'share'
      this.$emit('scrollTops', true, this.mode)
      // this.$emit('scrollTops', true)
      this.radio = ''
      this.searchValue = ''
      this.sortType = 'createdAt'
      this.checkAll = false
      this.checkedScreen = []
      localStorage.setItem('radio', '')
      localStorage.setItem('searchValue', '')
      localStorage.setItem('sortType', 'createdAt')
      localStorage.setItem('sortType', 'createdAt')
      localStorage.setItem('projectId', '')
      this.active = this.coeditSreens.id
      this.screens = this.coeditSreens;
      const res = await sharedToMe()
      if (res.success) {
        this.allSreens.screens = res.data
        this.shareWithMe = res.data
        this.nowScreensLength = res.data.length
        this.sort()
      } else {
        this.$message.error(res.message);
      }
    },
    async showChecked (item) {
      this.mode = 'selfish'
      this.$emit('scroll', this.mode)
      this.allSreens.screens = [];
      this.page = 1
      this.scroolDisabled = true
      this.checkAll = false
      this.checkedScreen = []
      // this.isIndeterminate = false
      this.projectId = item.id
      this.screens.name = '全部应用'
      this.workspaceId = item.workspaceId
      this.$store.commit('editor/updateManage', this.manage);
      this.$emit('changeActiveManage', this.active);
      this.$emit('scrollTops', true, this.mode)
      if (this.projectId !== Number(localStorage.getItem('projectId'))) {
        this.searchValue = ''
      }
      localStorage.setItem('projectId', this.projectId)
      const projectId = this.projectId;
      if (projectId) {
        const project = this.manage.find(item => item.id === +projectId);
        if (project) {
          this.active = project.id;
          // this.screens = _.cloneDeep(project);
          // this.screens = project;
        }
      }
      this.loading = true
      // const params = {
      //   projectId: item.id,
      //   workspaceId: parseInt(this.$route.params.workspaceId),
      //   query: this.searchValue,
      //   platform: this.radio,
      //   sortField: this.sortType,
      //   pageSize: 10,
      //   page: 1
      // }
      const restotl = await getProject({ workspaceId: parseInt(this.$route.params.workspaceId) });
      if (restotl.success) {
        const totl = restotl.data.filter(it => {
          return it.id === parseInt(this.projectId)
        })
        this.nowScreensLength = totl[0].totalScreens
      }
      // const res = await paging(params)
      this.pagingInterface().then((res) => {
        if (res.success) {
          const allSreens = this.allSreens.screens; // allScreens 改为 projects ,workspace->project->screen->component
          const data = res.data.filter(item => {
            return !item.isScreentpl
          })
          const screens = data
          allSreens.push(...screens)
          const projectId = this.projectId;
          if (projectId) {
            const project = this.manage.find(item => item.id === +projectId);
            if (project) {
              this.active = project.id;
            }
          }
          // this.sort();
          this.loading = false;
        }
        this.$store.commit('editor/updateManage', this.manage);
        this.$emit('changeActiveManage', this.active);
        this.$emit('scrollTops', true, this.mode)
        // this.sort();
        this.loading = false;
      })
      // this.active = item.id;
      // this.screens = _.cloneDeep(item);
      // this.sort();
      // this.$router.push({
      //   path: this.$route.path,
      //   query: {
      //     projectId: item.id
      //   }
      // })
    },
    searchScreens () {
      if (this.mode === 'selfish') {
        this.getManage()
      } else if (this.mode === 'share') {
        if (this.searchValue === '' && this.radio === '') {
          this.shareWithMe = this.allSreens.screens
        } else {
          this.shareWithMe = []
          for (let i = 0; i < this.allSreens.screens.length; i++) {
            const scr = this.allSreens.screens[i];
            if (scr.type === this.radio && scr.name.indexOf(this.searchValue) !== -1) {
              this.shareWithMe.push(scr)
            } else if (this.radio === '' && scr.name.indexOf(this.searchValue) !== -1) {
              this.shareWithMe.push(scr)
            }
          }
        }
        localStorage.setItem('radio', '')
        localStorage.setItem('searchValue', '')
        localStorage.setItem('sortType', 'createdAt')
        localStorage.setItem('sortType', 'createdAt')
        localStorage.setItem('projectId', '')
      }
    },
    load () {
      this.page = this.page + 1
      this.loading = true
      // const params = {
      //   query: this.searchValue,
      //   projectId: this.projectId,
      //   workspaceId: parseInt(this.$route.params.workspaceId),
      //   platform: this.radio,
      //   sortField: this.sortType,
      //   pageSize: 10,
      //   page: this.page
      // }
      if (!this.scroolDisabled) {
        this.loading = false
        return
      }
      if (!this.dataLock) {
        this.dataLock = true;
        // const res = await paging(params)
        this.pagingInterface().then((res) => {
          if (res.success) {
            this.dataLock = false;
            if (res.data.length === 0) { this.scroolDisabled = false }
            const loadedArr = []
            res.data.forEach(element => {
              this.allSreens.screens.push(element)
              loadedArr.push(element.id)
              if (this.checkAll) {
                this.checkedScreen.push(element.id)
              }
            })
            this.screens = this.allSreens
          }
        }).catch(() => {
        })
      }
      this.loading = false
    },
    async Batchexport () {
      let str = ''
      const h = this.$createElement
      this.$msgbox({
        title: '提示',
        type: 'info',
        showCancelButton: true,
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme',
        message: h('div', null, [
          h('p', {
            style: {
              marginBottom: '8px'
            }
          }, '确认导出大屏？')
        ])
      }).then(() => {
        if (this.checkedScreen.length > 10) {
          this.$message.error('批量导出最多支持十个大屏')
          return
        }
        str = JSON.stringify(this.checkedScreen)
        var a = document.createElement('a');
        a.download = '';
        a.href = replaceUrl('/api/screen/batchExport?screenIds=' + str)
        document.body.append(a);
        a.click();
        a.parentElement.removeChild(a);
      }).catch(() => {})
    },
    async Batchdel () {
      const text = '所选大屏删除后无法恢复，确认删除'
      this.$confirm(text, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme',
        closeOnClickModal: false
      }).then(async () => {
        const res = await deleteScreen({ screens: this.checkedScreen })
        if (res.success) {
          // this.screens.screens = this.screens.screens.filter((item) => !this.checkedScreen.some(it => it === item.id))
          // this.nowScreensLength = this.screens.screens.length
          this.$emit('scrollTops', true, this.mode)
          this.page = 1
          this.scroolDisabled = true
          this.getList()
          this.getManage()
          this.checkedScreen = []
          this.$message({
            html: true,
            text: '删除成功',
            duration: 1500,
            showClose: true,
            type: 'success'
          });
        }
      }).catch(() => {})
    },
    async pagingInterface () {
      const params = {
        query: this.searchValue,
        projectId: this.projectId,
        workspaceId: parseInt(this.$route.params.workspaceId),
        platform: this.radio,
        sortField: this.sortType,
        pageSize: 10,
        page: this.page
      }
      const res = await paging(params)
      if (res.success) return res
    },
    editGroupName (item) {
      item.isEdit = false;
    },
    del (item, index, isScreen) {
      const text = isScreen ? `${item.name} 删除后无法恢复，确认删除？` : `${item.name} 分组删除后，分组下的大屏将被移入未分组当中，确定删除？`
      this.$confirm(text, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme',
        closeOnClickModal: false
      }).then(async () => {
        if (isScreen) {
          const res = await deleteScreen({ screens: item.id });
          if (res.success) {
            this.screens.screens.splice(index, 1);
            this.refreshData();
          }
        } else {
          const res = await delProject({ id: item.id, workspaceId: parseInt(this.$route.params.workspaceId) });
          if (res.success) {
            this.manage.splice(index, 1);
            this.getList(true);
          }
        }
      }).catch(() => {})
    },
    goScreenEdit (screenData) {
      const { coeditId, coeditInfo = {}, id, screenType } = screenData
      const userId = localStorage.getItem('userId')

      // 协同者进入大屏编辑判断socket连接状态
      if (
        !!coeditId &&
        !this.isSocketConnect &&
        coeditInfo &&
        coeditInfo.createUserId !== userId
      ) {
        this.$message.error('创建协同连接失败，请稍后再试')
        return
      }

      if (screenType !== 'scene') {
        if (Object.keys(coeditInfo).length !== 0) {
        // 普通大屏、移动端 前端校验状态
          const currentCoeditScreen = `screen-editor-${id}`
          if (this.allScreenCoeditList[currentCoeditScreen]) {
            const matchItem = this.allScreenCoeditList[currentCoeditScreen]
            const coeditUsersObj = matchItem[0]
            const message = `${coeditUsersObj.userName}编辑中，不允许同时编辑同一页面`
            this.$message.warn(message)
            return
          }
        }
      }
      this.$router.push({ path: `/screen/edit/${screenData.id}` })
    },
    goScreenCreate () {
      this.$router.push({
        path: '/screen/create',
        query: {
          workspaceId: parseInt(this.$route.params.workspaceId),
          type: 'pc',
          screenType: 'common'
        }
      })
    },
    createScreen (item) { // 新建可视化
      if (item.type === 'import') {
        this.importScreen();
        return
      }
      if (item.type === 'ai') {
        this.$refs.aiDialog.show(this.unGroupId)
        return
      }
      const type = (item.type === 'pc') ? 'pc' : 'mobile';
      const screenType = 'common';
      this.$router.push({
        path: '/screen/create',
        query: {
          workspaceId: parseInt(this.$route.params.workspaceId),
          type,
          screenType
        }
      })
    },
    async inputBlur (item) { // 我的分组input 框弹起的事件
      item.isEdit = true;
      if (item.inputText && item.inputText !== item.name) {
        const res = await updateProject({ name: item.inputText, id: item.id });
        if (res.success) {
          item.name = item.inputText;
          this.screens.name = item.inputText;
        } else {
          this.$message.error(res.message);
        }
      }
    },
    inputScreenFocus (screen) {
      this.curScreenName = screen.name;
    },
    async inputScreenBlur (screen) { // 大屏input 框弹起的事件
      if (this.curScreenName && this.curScreenName !== screen.name) {
        await updateScreen({ name: screen.name, workspaceId: parseInt(this.$route.params.workspaceId) }, { id: screen.id });
      }
    },
    async addGroup () {
      this.isAdd = false;
      const params = {
        workspaceId: this.$route.params.workspaceId,
        name: this.addValue
      }
      if (this.addValue) {
        const res = await createProject(params);
        if (res.success) {
          this.getList();
        } else {
          this.$message.error(res.message);
        }
      }
      this.addValue = ''
    },
    // 显示发布弹窗
    publishScreen (screen) {
      this.$store.commit('editor/setSelectedManageScreen', screen)
      this.$refs.pub.showPublish(screen.id || '');
    },
    // 发布成功
    handlePublishScreenSucc (screenProps = {}) {
      this.$store.commit('editor/updateSelectedManageScreenProps', screenProps)
    },
    // 发布关闭
    handlePublishScreenClose () {
      this.$store.commit('editor/setSelectedManageScreen', null)
    },
    // 同步大屏
    syncScreen (screen) {
      this.$refs.screensync.showDialog(screen);
    },
    // 共享大屏
    shareScreen (screen) {
      this.showScreenshare = true
      this.currScreenInfo = screen
    },
    preview (screen) {
      let url;
      const userId = localStorage.getItem('userId');
      if (screen.type === 'pc') {
        url = `/screen/preview/${screen.id}?loading=true&userId=${userId}`;
      } else {
        url = `/screen/mobile-preview/${screen.id}?loading=true&userId=${userId}`;
      }
      openNewTab(url);
    },
    importScreen () {
      this.importShow = true;
    },
    async refreshData () {
      this.showScreenshare = false
      this.loading = true
      this.page = 1
      this.scroolDisabled = true
      await this.getList();
      await this.getManage();
      if (this.active !== 0) {
        const checked = this.manage.find(item => item.id === this.active);
        if (checked) {
          this.showChecked(checked);
        }
      } else {
        this.screens = this.allSreens;
      }
      this.loading = false
    },
    copyScreen (screen) { // 拷贝大屏
      this.$confirm('确认复制大屏？', {
        title: '提示',
        type: 'info',
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme'
      }).then(async () => {
        const data = {
          id: screen.id,
          name: screen.name + '_' + randomStr(4),
          workspaceId: this.$route.params.workspaceId,
          projectId: screen.projectId,
          type: screen.type,
          templateId: screen.templateId
        }
        this.loading = true;
        try {
          const res = await screenCopy(data, {});
          if (res && res.success) {
            this.loading = false;
            this.$message.success('复制成功');
            this.refreshData();
          }
        } catch (e) {
          this.loading = false;
        }
      })
    },
    sort () {
      this.shareWithMe = this.allSreens.screens.sort((a, b) => {
        if (this.sortType === 'create') {
          const createA = Date.parse(a.createdAt);
          const createB = Date.parse(b.createdAt);
          if (createA < createB) {
            return 1;
          }
          if (createA > createB) {
            return -1;
          }
          return 0;
        } else {
          const updatedA = Date.parse(a.updatedAt);
          const updatedB = Date.parse(b.updatedAt);
          if (updatedA < updatedB) {
            return 1;
          }
          if (updatedA > updatedB) {
            return -1;
          }
          return 0;
        }
      })
    },
    platformType () {
      // this.shareWithMe = this.shareWithMe.filter(item => {
      //   if (this.radio === '') return item
      //   return item.type === this.radio
      // })
      // if (this.radio === '') {
      //   this.shareWithMe = this.allSreens.screens
      // }
    },
    exportScreen (item) { // 导出大屏
      const h = this.$createElement
      this.$msgbox({
        title: '提示',
        type: 'info',
        showCancelButton: true,
        cancelButtonClass: 'poper-cancel',
        iconClass: 'message-warning',
        customClass: 'poper-theme',
        message: h('div', null, [
          h('p', {
            style: {
              marginBottom: '8px'
            }
          }, '确认导出大屏？')
          // h('el-checkbox', {
          //   props: {
          //     checked: this.convertToStatic
          //   },
          //   on: {
          //     change: (val) => {
          //       this.convertToStatic = val
          //     }
          //   }
          // }, '将组件数据源转成静态数据')
        ])
      }).then(() => {
        const userId = localStorage.getItem('userId');
        var a = document.createElement('a');
        a.download = '';
        a.href = replaceUrl('/api/screen/export?id=', null, true) + item.id + '&userId=' + userId + '&convertToStatic=' + this.convertToStatic + '&t=' + new Date().getTime();
        document.body.append(a);
        a.click();
        a.parentElement.removeChild(a);
      }).catch(() => {})
    },
    handleDragStart (e, screen) {
      this.isDrag = true
      const { coeditId, id } = screen
      // 协同大屏的移动和普通大屏移动分开
      if (!coeditId) {
        // 普通大屏
        e.dataTransfer.setData('screenId', id);
        e.dataTransfer.setData('screenType', 'common');
      } else {
        // 协同大屏
        e.dataTransfer.setData('screenType', 'coedit');
        e.dataTransfer.setData('coeditId', coeditId);
      }
      e.dataTransfer.setDragImage(this.img, -10, 25);
      e.dataTransfer.effectAllowed = 'all';
    },
    handleDragEnd () {
      this.isDrag = false
    },
    handleDragEnter (e, dom) {
      dom[0].classList.remove('drag-bg')
    },
    handleDragOver (e, dom) {
      e.dataTransfer.dropEffect = 'move';
      dom[0].classList.remove('drag-bg')
    },
    handleDragLeave (e, dom) {
      dom[0].classList.add('drag-bg')
    },
    handleDrop (e, item) {
      const screenId = e.dataTransfer.getData('screenId');
      const screenType = e.dataTransfer.getData('screenType');
      if (screenType === 'common') {
        this.$confirm(`确认移动到${item.name}文件夹下？`, { title: '提示', type: 'info' }).then(async () => {
          await updateScreen({ projectId: item.id }, { id: screenId });
          this.refreshData();
        }).catch(() => {})
      } else if (screenType === 'coedit') {
        const coeditId = e.dataTransfer.getData('coeditId');
        this.$confirm(`确认移动到${item.name}文件夹下？`, { title: '提示', type: 'info' }).then(async () => {
          try {
            const res = await coeditScreenMove({ projectId: item.id, coeditId: Number(coeditId) }, null);
            if (res.success) {
              this.refreshData();
            }
          } catch (error) {
            console.error(error)
          }
        }).catch(() => {})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.seatom-main {
  ::v-deep {
    .el-divider--horizontal {
      margin: 10px 0 4px 24px;
      background-color: var(--seatom-mono-a300);
      width: 240px;
    }
  }
  ::v-deep {
    .el-button--plain {
      border-radius: 1
    }
  }
  .share-to-me {
    font-weight: 600;
    margin-top: 10px;
  }
  .project-manage {
    min-width: 288px;
    max-width: 288px;
    position: sticky;
    top: 121px; // 96px;
    font-size: 14px;
    overflow-y: auto;
    height: calc(100vh - 121px);
    background-color: var(--seatom-background-100);
    color: var(--seatom-type-800);
    box-shadow: var(--seatom-container-c200);

    &::-webkit-scrollbar {
      display: block;
      width: 4px;
    }

    &::-webkit-scrollbar-thumb{
      background: #434b55;
      border: 1px solid #434b55;
    }

    &-title {
      position: sticky;
      top: 0;
      background: var(--seatom-background-100);
      z-index: 10;
      &-header {
        display: flex;
        justify-content: space-between;
        padding-right: 30px;
        height: 60px;
        padding-left: 24px;
        font-weight: 600;
        // border-bottom: 1px solid #27343e;
        align-items: center;
      }

      &-bottom {
        transition: color .2s;
        font-weight: 600;
      }
    }

    .manage-main {
      .my-project {
        padding-left: 40px;
      }
    }

    .my-project {
      padding-left: 24px;
      line-height: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 24px;
      cursor: pointer;
    }
    .my-project:hover {
      background: var(--seatom-mono-a100);
      // border-radius: 0px 50px 50px 0px;
      position: relative;
    }

    .my-project:hover .edit-del{
      display: block;
      color: var(--seatom-type-800);
      font-size: 16px;
    }

    .custom-project:hover .screens-length{
      display: none;
    }

    .my-project.project-checked-color {
      // background-image: url('../../assets/img/checked_color.png');
      background-repeat: round;
      background: var(--seatom-primary-a100);
      color: var(--seatom-primary-900);
      // font-weight: 600;
      &::after {
        content: "";
        display: block;
        height: 40px;
        width: 4px;
        background: var(--seatom-primary-900);
        position: absolute;
        left: 0;
      }
      .screens-length {
        color: var(--seatom-primary-900);
      }
    }

    .edit-del {
      position: absolute;
      display: none;
      right: 30px;
      cursor: pointer;
    }

    .el-icon-plus {
      cursor: pointer;
    }
  }

  .create-entry {
    .create-title {
      font-size: 14px;
      color: var(--seatom-type-800);
      font-weight: bold;
      margin-top: 16px;
    }
    .create-list {
      display: flex;
      align-items: center;
      padding: 24px 0 24px 0;
      .create-item {
        width: 245px;
        height: 86px;
        display: flex;
        align-items: center;
        // padding-left: 8px;
        background: var(--seatom-background-300);
        border: 1px solid var(--seatom-mono-a300);
        border-radius: 8px;
        margin-right: 32px;
        position: relative;
        cursor: pointer;
        .item-logo {
          width: 120px;
          height: 82px;
          // margin-right: 12px;
          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        .item-name {
          font-size: 18px;
          font-weight: 500;
          color: var(--seatom-type-900);
        }
        .tag {
          border-radius: 0px 5px 0px 8px;
          background: linear-gradient(136deg, #00C2FF 0%, #2B19FF 100%);
          position: absolute;
          right: 0;
          top: 0;
          color: #fff;
          font-size: 12px;
          padding: 4px 8px;
          line-height: 1;
        }
      }
      .create-item-bg-light {
        background-color: var(--seatom-background-100);
      }
      .create-item-bg-dark {
        background-color: var(--seatom-background-300);
      }
    }
  }

  .project-content-header {
    position: sticky;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--seatom-mono-a300);
    padding-bottom: 5px;
    // padding-top: 10px;
    height: 60px;
    background: var(--seatom-background-200);
    z-index: 2;
    top: 121px;// 96px;
    // min-width: 1024px;

    &-title {
      display: flex;
      align-items: center;
      padding: 5px 0;

      .el-input {
        width: 245px;
      }
    }

    &-search {
      display: flex;
      align-items: center;

      .el-dropdown-link {
        color: #bcc9d4;
        min-width: 100px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .screen-temp {
        width: 90%;
      }
    }

    &-title h2 {
      max-width: 200px;
      font-size: 14px;
      margin-right: 6px;
      font-family: PingFang SC;
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      color: var(--seatom-type-800);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    &-title .import-btn {
      position: absolute;
      right: 0;
    }
  }
  .noCheck {
    color: rgba(255, 255, 255, 0.5);
    background-color: rgba(64,158,255, 0.5);
    border-color: rgba(64,158,255, 0.5);
    // cursor: not-allowed;
  }
  .noCheckDel {
    color: rgba(255, 255, 255, 0.5);
    background-color: rgba(19,22,26, 0.001);
    border-color: rgba(64,158,255, 0.5);
  }
  .project-second-header {
    position: sticky;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // border-bottom: 1px solid var(--seatom-mono-a300);
    padding-bottom: 5px;
    // padding-top: 10px;
    height: 60px;
    background: var(--seatom-background-200);
    z-index: 2;
    top: 181px;// 96px;
    // min-width: 1024px;
  }
  .project-second-header .el-button--primary {
    padding: 8px 15px;
  }
  .project-second-header .el-checkbox {
    margin-right: 25px;
  }

  .main-screen {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    padding-top: 8px;
    user-select: none;
    // padding-bottom: 50px;
    // padding-left: 4px;
    .my-screen {
      // margin: 8px 12px;
      margin: 8px 16px 8px 0;

      .screen {
        position: relative;
        display: flex;
        flex-direction: column;
        height: 226px;
        width: 294px;
        background-color: var(--seatom-background-100);
        border: 1px solid var(--seatom-mono-a300);
        transition: .2s;
        box-sizing: border-box;
        border-radius: 16px;
        overflow: hidden;

        .terminal-icon {
          position: absolute;
          width: 46px;
          height: 46px;
          right: 0;
          top: 0;
          background: url('../../assets/img/terminal.png') no-repeat;
        }

        .screen-info {
          width: 100%;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;

          .screen-img {
            display: flex;
            justify-content: center;
            align-items: center;
            width: inherit;
            height: 166px;
            background-size: 100% 100%;
            background: var(--seatom-screen-bg) no-repeat center / contain;
            border-radius: 16px 16px 0 0;
            overflow: hidden;
          }
          .img-filter {
            // filter: grayscale(1); // 加个灰色的滤镜
            // opacity: 0.78;
            width: 246px;
          }

          .screen-coedit {
            padding: 0 8px;
            position: absolute;
            bottom: 0;
            width: 100%;
            line-height: 22px;
            height: 22px;
            font-weight: 400;
            font-size: 14px;
            color: #fff;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.1) 100%);
          }

          .screen-edit {
            position: absolute;
            width: 100%;
            height: 100%;
            background: rgba(21,22,24,0.8);
            transition: opacity .2s;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            border-radius: 16px 16px 0 0;

            .main-button {
              display: flex;
              justify-content: space-between;
              font-size: 19px;
              padding-top: 15px;
              align-items: center;
              color: #fff;
              margin-bottom: 10px;
              width: 100%;
            }
          }
        }

        .screen-main {
          width: 100%;
          height: 60px;
          padding: 8px 16px;
          background: var(--seatom-background-300);
          display: flex;
          flex-direction: column;
          color: var(--seatom-type-800);
          font-size: 14px;
          border-radius: 0 0 16px 16px;

          .screen-name-input {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            .edit-name {
              flex: 1;
              margin-right: 15px;
              input {
                color: var(--seatom-type-800);
                background: 0 0;
                line-height: 20px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                border: 1px solid transparent;
                width: 100%;
                font-weight: 600;
                font-size: 16px;
                font-family: PingFang SC;
                font-style: normal;
              }
            }
            .screen-status {
              .name-icon {
                margin-right: 5px;
                width: 8px;
                height: 8px;
                border-radius: 5px;
                display: inline-block;
                background-color: #FF9431;
                &.publish {
                  background-color: #00BBC2;
                }
              }
            }
          }

          .publish-info {
            display: flex;
            justify-content: space-between;
            color: var(--seatom-type-800);
            text-align: right;
            font-size: 14px;
            font-weight: 400;
            .screen-size {
              font-size: 10px;
              color: var(--seatom-type-700);
            }
            .update-time {
              font-family: PingFang SC;
              font-style: normal;
              font-weight: normal;
              font-size: 10px;
              color: var(--seatom-type-700);
            }
          }
        }
      }

      .screen:hover .screen-info .screen-edit {
        opacity: 1;
        pointer-events: all;
      }
    }
  }

  .project-name {
    width: 140px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
  }

  // .seatom-main-container ::v-deep .el-input__inner {
  //   background-color: rgba(29,38,46,.54);
  //   color: #fff;
  //   padding: 0 6px;
  //   border: 1px solid #2681ff;
  //   text-overflow: ellipsis;
  //   overflow: hidden;
  //   white-space: nowrap;
  // }

  .mr10 {
    margin-right: 10px;
  }

  .mt30 {
    margin-top: 30px;
  }

  .mr6 {
    margin-right: 6px;
  }

  .color-BCC9D4 {
    font-size: 14px;
    letter-spacing: 1px;
    color: #bcc9d4;
  }

  .color-2483FF {
    padding: 0 2px;
    color: #2681ff;
  }
  .el-main {
    overflow: unset;
    padding-top: 0;
    padding: 0 36px;
  }
  .pointer {
    cursor: pointer;
  }
  .screen-select {
    width: 154px;
    ::v-deep {
      .el-input__inner {
        border: none;
        color: var(--seatom-type-900);
        background-color: transparent;
        font-size: 14px;
        font-family: PingFang SC;
        font-style: normal;
        font-weight: 400;
      }
      .el-select__caret {
        color: var(--seatom-type-900);
      }
    }
  }

  .screens-length{
    height: 20px;
    width: 26px;
    border-radius: 8px;
    // background-color: rgba(92, 156, 255, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--seatom-type-800);
    font-size: 12px;
    font-weight: 400;
  }

  .r-screens-length {
    height: 20px;
    width: 26px;
    border-radius: 8px;
    background-color: rgba(92, 156, 255, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--seatom-primary-900);
    font-size: 12px;
    font-weight: 700;
  }

  .screen-button {
    width: 244px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .screen-btn {
    width: 178px;
    height: 39px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #1F71FF;
    font-family: 思源黑体Medium;
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    color: #FFFFFF;
    cursor: pointer;
    border-radius: 8px;
  }
  .import-btn {
    color: #FFFFFF;
    background: transparent;
    border: 1px solid rgba(204, 219, 255, 0.32);
  }
  .edit-color {
    color: #2979FF;
  }
  .mr-8 {
    margin-right: 8px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mr-24 {
    margin-right: 24px;
  }
  .drag-bg {
    background-color: rgba(0, 186, 255, 0.1);
  }
  .edit-name {
    display: flex;
    // justify-items: center;
    align-items: center;
  }
}
.more-action-container {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  border-radius: 8px;
  line-height: 32px;
  & > span {
    text-align: center;
    border-radius: 6px;
    &:hover {
      background-color: var(--seatom-mono-a100);
    }
  }
}
.el-checkbox-group {
  position: absolute;
  left: 270px;
  top: 4px;
  height: 19px;
}
</style>
