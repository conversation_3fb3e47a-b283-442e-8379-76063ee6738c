<template>
  <div class="my-resources">
  <el-container class="seatom-main-resources">
    <el-aside class="project-manage-resources">
    <div class="resources-list">
      <a class="link-title"
      :class="{'active': resourcesList[0].id === selectActive.id}"
      @click="selectRes(resourcesList[0])">
        <i class="el-icon-arrow-right margin-right-4"></i>
        <hz-icon :name="resourcesList[0].icon"></hz-icon>
        <span>{{resourcesList[0].title}}</span>
      </a>
      <!-- 图片管理 -->
      <a class="link-title"
      :class="{'active': resourcesList[1].id === selectActive.id}"
      @click="selectRes(resourcesList[1])">
        <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !resourcesList[1].isOpen}"></i>
        <hz-icon :name="resourcesList[1].icon"></hz-icon>
        <span style="width: 186px;">{{resourcesList[1].title}}</span>
        <hz-icon name="icon-plus" class="block-16" @click.native="addFolder('picture')"></hz-icon>
      </a>
      <!-- 图片管理 文件夹 -->
      <div v-show="resourcesList[1].isOpen" v-for="(item, index) in fileListImg" :key="'res-picture-' + index">
        <a class="link-title padding-left-38"
        @click="item.isOpen = !item.isOpen">
          <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !item.isOpen}"></i>
          <span :title="item.name">{{item.name}}</span>
        </a>
        <div class="folder-concent link-title padding-left-54"
        :class="[{'folder-concent-Open': item.isOpen}, {'select': it.isOpen}]"
        v-for="(it, folderIndex) in item.folderList"
        :key="it.id"
        @click="slelectFolder(it, 'picture')">
          <div class="folder-name" >
            <hz-icon :name="it.isOpen ? 'folder-open' : 'folder-close'" style="margin:0 4px;"></hz-icon>
            <span class="folder-name-max-width" :title="it.name">{{it.name}}</span>
          </div>
          <span class="file-total"
          :class="{'file-total-block': it.name == '背景' || it.name == '边框' || it.name == '默认文件夹'}"
          >{{it.list | fileListTotal}}</span>
          <hz-icon class="del-folder-icon"
          v-if="(it.name !== '背景' && it.name !== '边框' && it.name !== '默认文件夹' && item.resourceLibraryCode !== 'system') || isAdmin"
          name="edit"
          style="margin-left: 4px;height: 16px;width: 16px;margin-right: 4px;"
          @click.native.stop="editName(it, folderIndex, index, 'img')"></hz-icon>
          <hz-icon class="del-folder-icon"
          v-if="(it.name !== '背景' && it.name !== '边框' && it.name !== '默认文件夹' && item.resourceLibraryCode !== 'system') || isAdmin"
          name="trash"
          style="margin-left: 4px;height: 16px;width: 16px;"
          @click.native.stop="delFolder(it, 'picture')"></hz-icon>
        </div>
      </div>
      <!-- 视频管理 -->
      <a class="link-title"
      :class="{'active': resourcesList[2].id === selectActive.id}"
      @click="selectRes(resourcesList[2])">
        <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !resourcesList[2].isOpen}"></i>
        <hz-icon :name="resourcesList[2].icon"></hz-icon>
        <span style="width: 186px;">{{resourcesList[2].title}}</span>
        <hz-icon name="icon-plus" class="block-16" @click.native="addFolder('video')"></hz-icon>
      </a>
      <!-- 视频管理 文件夹 -->
      <div v-show="resourcesList[2].isOpen" v-for="(item, index) in fileListVideo" :key="'res-video-' + index">
        <a class="link-title padding-left-38"
        @click="item.isOpen = !item.isOpen">
          <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !item.isOpen}"></i>
          <span :title="item.name">{{item.name}}</span>
        </a>
        <div class="folder-concent link-title padding-left-54"
        :class="[{'folder-concent-Open': item.isOpen}, {'select': it.isOpen}]"
        v-for="(it, folderIndex) in item.folderList"
        :key="it.id"
        @click="slelectFolder(it, 'video')">
          <div class="folder-name" >
            <hz-icon :name="it.isOpen ? 'folder-open' : 'folder-close'" style="margin:0 4px;"></hz-icon>
            <span class="folder-name-max-width" :title="it.name">{{it.name}}</span>
          </div>
          <span class="file-total"
          :class="{'file-total-block': it.name == '背景' || it.name == '边框' || it.name == '默认文件夹'}"
          >{{it.list | fileListTotal}}</span>
          <hz-icon class="del-folder-icon"
          v-if="(it.name !== '背景' && it.name !== '边框' && it.name !== '默认文件夹' && item.resourceLibraryCode !== 'system') || isAdmin"
          name="edit"
          style="margin-left: 4px;height: 16px;width: 16px;margin-right: 4px;"
          @click.native.stop="editName(it, folderIndex, index, 'video')"></hz-icon>
          <hz-icon class="del-folder-icon"
          v-if="(it.name !== '背景' && it.name !== '边框' && it.name !== '默认文件夹' && item.resourceLibraryCode !== 'system') || isAdmin"
          name="trash"
          style="margin-left: 4px;height: 16px;width: 16px;"
          @click.native.stop="delFolder(it, 'video')"></hz-icon>
        </div>
      </div>
      <!-- 图标管理 -->
      <a class="link-title"
      :class="{'active': resourcesList[4].id === selectActive.id}"
      @click="selectRes(resourcesList[4])">
        <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !resourcesList[4].isOpen}"></i>
        <hz-icon :name="resourcesList[4].icon"></hz-icon>
        <span style="width: 186px;">{{resourcesList[4].title}}</span>
        <hz-icon name="icon-plus" class="block-16" @click.native="addFolder('icon')"></hz-icon>
      </a>
      <!-- 图标管理 文件夹 -->
      <div v-show="resourcesList[4].isOpen" v-for="(item, index) in fileListIcon" :key="'res-icon-' + index">
        <a class="link-title padding-left-38"
        @click="item.isOpen = !item.isOpen">
          <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !item.isOpen}"></i>
          <span :title="item.name">{{item.name}}</span>
        </a>
        <div class="folder-concent link-title padding-left-54"
        :class="[{'folder-concent-Open': item.isOpen}, {'select': it.isOpen}]"
        v-for="(it, folderIndex) in item.folderList"
        :key="it.id"
        @click="slelectFolder(it, 'icon')">
          <div class="folder-name" >
            <hz-icon :name="it.isOpen ? 'folder-open' : 'folder-close'" style="margin:0 4px;"></hz-icon>
            <span class="folder-name-max-width" :title="it.name">{{it.name}}</span>
          </div>
          <span class="file-total"
          :class="{'file-total-block': it.name == '背景' || it.name == '边框' || it.name == '默认文件夹'}"
          >{{it.list | fileListTotal}}</span>
          <hz-icon class="del-folder-icon"
          v-if="(it.name !== '背景' && it.name !== '边框' && it.name !== '默认文件夹' && item.resourceLibraryCode !== 'system') || isAdmin"
          name="edit"
          style="margin-left: 4px;height: 16px;width: 16px;margin-right: 4px;"
          @click.native.stop="editName(it, folderIndex, index, 'icon')"></hz-icon>
          <hz-icon class="del-folder-icon"
          v-if="(it.name !== '背景' && it.name !== '边框' && it.name !== '默认文件夹' && item.resourceLibraryCode !== 'system') || isAdmin"
          name="trash"
          style="margin-left: 4px;height: 16px;width: 16px;"
          @click.native.stop="delFolder(it, 'icon')"></hz-icon>
        </div>
      </div>
      <!-- 文件管理 start-->
      <a class="link-title"
      :class="{'active': resourcesList[3].id === selectActive.id}"
      @click="selectRes(resourcesList[3])">
        <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !resourcesList[3].isOpen}"></i>
        <hz-icon :name="resourcesList[3].icon"></hz-icon>
        <span style="width: 186px;">{{resourcesList[3].title}}</span>
        <hz-icon name="icon-plus" class="block-16" @click.native="addFolder('docfile')"></hz-icon>
      </a>
      <!-- 文件管理 文件夹 -->
      <div v-show="resourcesList[3].isOpen" v-for="(item, index) in fileListFile" :key="'res-docfile-' + index">
        <a class="link-title padding-left-38"
        @click="item.isOpen = !item.isOpen">
          <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !item.isOpen}"></i>
          <span :title="item.name">{{item.name}}</span>
        </a>
        <div class="folder-concent link-title padding-left-54"
        :class="[{'folder-concent-Open': item.isOpen}, {'select': it.isOpen}]"
        v-for="(it, folderIndex) in item.folderList"
        :key="it.id"
        @click="slelectFolder(it, 'docfile')">
          <div class="folder-name" >
            <hz-icon :name="it.isOpen ? 'folder-open' : 'folder-close'" style="margin:0 4px;"></hz-icon>
            <span class="folder-name-max-width" :title="it.name">{{it.name}}</span>
          </div>
          <span class="file-total"
          :class="{'file-total-block': it.name == '背景' || it.name == '边框' || it.name == '默认文件夹'}"
          >{{it.list | fileListTotal}}</span>
          <hz-icon class="del-folder-icon"
          v-if="(it.name !== '背景' && it.name !== '边框' && it.name !== '默认文件夹' && item.resourceLibraryCode !== 'system') || isAdmin"
          name="edit"
          style="margin-left: 4px;height: 16px;width: 16px;margin-right: 4px;"
          @click.native.stop="editName(it, folderIndex, index, 'docfile')"></hz-icon>
          <hz-icon class="del-folder-icon"
          v-if="(it.name !== '背景' && it.name !== '边框' && it.name !== '默认文件夹' && item.resourceLibraryCode !== 'system') || isAdmin"
          name="trash"
          style="margin-left: 4px;height: 16px;width: 16px;"
          @click.native.stop="delFolder(it, 'docfile')"></hz-icon>
        </div>
      </div>
      <!-- 文件管理 end -->
      <!-- 指标管理 -->
      <a class="link-title"
      :class="{'active': resourcesList[5].id === selectActive.id}"
      @click="selectRes(resourcesList[5])">
        <i class="el-icon-arrow-down arrow-down margin-right-4" :class="{'arrow-rotate': !resourcesList[5].isOpen}"></i>
        <hz-icon :name="resourcesList[5].icon"></hz-icon>
        <span style="width: 186px;">{{resourcesList[5].title}}</span>
        <!-- <hz-icon name="icon-plus" class="block-16" @click.native="addFolder('icon')"></hz-icon> -->
      </a>
      <!-- 指标管理 文件夹 -->
    </div>
    </el-aside>
    <div class="resources-tabls">
      <div class="tabls-content" v-if="selectActive.id === 'res_font'">
        <font-control></font-control>
      </div>
      <div class="tabls-content" v-if="selectActive.id === 'picture' || selectActive.id === 'video' || selectActive.id === 'docfile' || selectActive.id === 'icon' || selectActive.id === 'indicator'">
        <img-preview-control
        :resourceType="selectActive.id"
        :folderId="selectFolderId"
        :resourceLibraryCode="selectResourceLibraryCode"
        :list="list"
        :imgList="imgList"
        :selectFolderOptions="selectFolderOptions"
        @refreshResourceList="refreshResourceList"
        @checkAdmin="checkAdmin"
        ></img-preview-control>
      </div>
    </div>
  </el-container>
  <!-- 删除文件前提示 -->
  <el-dialog
    title="提示"
    :visible.sync="dialogVisibleDel"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    width="30%"
    top="0"
    :close-on-click-modal="false">
    <div class="dialog-content-del-info">
      <div>删除文件夹后，该文件夹内文件会同步删除</div>
      <div>是否确认删除？</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="dialogVisibleDel = false;">取 消</el-button>
      <el-button size="mini" type="primary" @click="delFiles()">确 定</el-button>
    </span>
  </el-dialog>
  <!-- 弹窗 新建文件夹 -->
  <el-dialog
    title="新建文件夹"
    :visible.sync="dialogVisibleAddFolder"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    width="30%"
    top="0"
    class="upload-file-add-folder"
    :append-to-body="true"
    :close-on-click-modal="false">
    <div class="dialog-content dialog-content-add-folder">
      <div class="content-list" style="margin-bottom: 8px;">
        <div class="content-name form-theme">所属文件夹</div>
        <div class="content-input">
          <el-select
            class="input-theme"
            popper-class="poper-theme"
            v-model="newFolderInfo.folder"
            filterable
            placeholder="请选择"
            size="mini"
            >
            <el-option
              v-for="(item, index) in selectFolderOptions"
              :key="item.value + index"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
              >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="content-list">
        <div class="content-name form-theme">文件夹名称</div>
        <div class="content-input">
          <el-input
            class="input-theme"
            v-model="newFolderInfo.name"
            size="mini"
            >
          </el-input>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="dialogVisibleAddFolder = false;">取 消</el-button>
      <el-button size="mini" type="primary" @click="submitNewFolder()">确 定</el-button>
    </span>
  </el-dialog>
  <!-- 弹窗 修改文件名称 -->
  <el-dialog
    title="修改名称"
    :visible.sync="dialogVisibleEditName"
    width="30%"
    :custom-class="$route.name.includes('edit') ? '' : 'poper-theme'"
    top="0"
    class="upload-file-add-folder"
    :append-to-body="true"
    :close-on-click-modal="false">
    <div class="dialog-content dialog-content-add-folder">
      <div class="content-list" style="margin-bottom: 8px;">
        <div class="content-name form-theme">原名称</div>
        <div class="content-input">
          {{editFolderInfo.originName}}
        </div>
      </div>
      <div class="content-list">
        <div class="content-name form-theme">新名称</div>
        <div class="content-input">
          <el-input
            class="input-theme"
            v-model="editFolderInfo.newName"
            size="mini"
            >
          </el-input>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="text" @click="dialogVisibleEditName = false;">取 消</el-button>
      <el-button size="mini" type="primary" @click="submitNewName()">确 定</el-button>
    </span>
  </el-dialog>
  <seatom-loading v-if="loading"></seatom-loading>
  </div>
</template>

<script>
import FontControl from '@/components/resources-control/FontControl.vue';
import ImgPreviewControl from '@/components/resources-control/ImgPreviewControl.vue';
import { getResourceList, delResourceFolder, ResourceCreateFolder, editResourceFolderName, getIndicators } from '@/api/workspace';
import { replaceUrl } from '@/utils/base';
export default {
  name: 'workspaceResources',
  components: {
    FontControl,
    ImgPreviewControl
  },
  data () {
    return {
      loading: false,
      // 是否有超级管理权限
      isAdmin: false,
      resourcesList: [{
        title: '字体管理',
        name: 'font',
        id: 'res_font',
        isOpen: false,
        icon: 'resources-font'
      },
      {
        title: '图片管理',
        name: 'img',
        id: 'picture',
        isOpen: false,
        icon: 'resources-img'
      },
      {
        title: '视频管理',
        name: 'video',
        id: 'video',
        isOpen: false,
        icon: 'resources-video'
      },
      {
        title: '文件管理',
        name: 'docfile',
        id: 'docfile',
        isOpen: false,
        icon: 'resources-docfile'
      },
      {
        title: '图标管理',
        name: 'icon',
        id: 'icon',
        isOpen: false,
        icon: 'resources-icon'
      },
      {
        title: '指标管理',
        name: 'indicator',
        id: 'indicator',
        isOpen: false,
        icon: 'resources-indicator'
      }
      ],
      selectActive: {
        title: '字体管理',
        name: 'font',
        id: 'res_font',
        isOpen: false,
        icon: 'icon-font',
        resourceLibraryCode: 'personal'
      },
      // 修改文件夹名称
      dialogVisibleEditName: false,
      editFolderInfo: {
        originName: '',
        newName: '',
        id: '',
        index: 0,
        folderIndex: 0,
        folderType: 'img'
      },
      // 删除前弹窗提示
      dialogVisibleDel: false,
      delFolderList: [],
      // 图片资源 图片列表
      fileListImg: [],
      // 视频资源 视频列表
      fileListVideo: [],
      // 文件资源 文件列表
      fileListFile: [],
      // 图标资源 图标列表
      fileListIcon: [],
      // 指标资源 指标列表
      fileListIndicator: [],
      userId: '',
      // 图片信息列表
      list: [],
      // 图片预览列表
      imgList: [],
      selectFolderId: -1,
      // 所选库
      selectResourceLibraryCode: 'private',
      // 上传文件夹目录
      selectFolderOptions: [],
      // 新建文件夹 弹窗
      dialogVisibleAddFolder: false,
      newFolderInfo: {
        folder: '',
        name: ''
      },
      selectFolderType: ''
    };
  },
  created () {
    const activeItemId = localStorage.getItem('resourcesActiveItemId');
    this.userId = localStorage.getItem('userId');
    if (activeItemId) {
      this.resourcesList.forEach(item => {
        if (item.id === activeItemId) {
          if (activeItemId === 'picture') {
            item.isOpen = true;
            this.getResourceListFun(false, 'picture');
          } else if (activeItemId === 'video') {
            item.isOpen = true;
            this.getResourceListFun(false, 'video');
          } else if (activeItemId === 'docfile') {
            item.isOpen = true;
            this.getResourceListFun(false, 'docfile');
          } else if (activeItemId === 'icon') {
            item.isOpen = true;
            this.getResourceListFun(false, 'icon')
          } else if (activeItemId === 'indicator') {
            item.isOpen = true;
            this.getIndicatorList()
          }
          this.selectActive = item;
        }
      });
    }
  },
  methods: {
    checkAdmin () {
      this.isAdmin = true;
      this.selectFolderOptions.forEach((item) => {
        item.disabled = this.isAdmin ? false : item.resourceLibraryCode === 'system';
      });
    },
    // 刷新资源列表
    refreshResourceList (data) {
      if (data === 'indicator') {
        this.getIndicatorList()
      } else {
        this.getResourceListFun(true, data);
      }
    },
    async selectRes (item) {
      if (item.id === this.selectActive.id) {
        item.isOpen = !item.isOpen;
      } else {
        // const folderId = localStorage.getItem('resourcesSelectFolderId');
        if (item.id === 'picture') {
          if (this.fileListImg.length === 0) {
            await this.getResourceListFun(false, 'picture');
          }
          this.fileListImg.forEach(item => {
            item.folderList.forEach(fdr => {
              // fdr.isOpen = fdr.id.toString() === folderId;
              if (fdr.isOpen) {
                this.list = fdr.list;
                this.imgList = fdr.imgList;
                this.selectFolderId = fdr.id;
                this.selectResourceLibraryCode = fdr.resourceLibraryCode;
              }
            });
          });
        } else if (item.id === 'video') {
          if (this.fileListVideo.length === 0) {
            await this.getResourceListFun(false, 'video');
          }
          this.fileListVideo.forEach(item => {
            item.folderList.forEach(fdr => {
              // fdr.isOpen = fdr.id.toString() === folderId;
              if (fdr.isOpen) {
                this.list = fdr.list;
                this.imgList = fdr.imgList;
                this.selectFolderId = fdr.id;
                this.selectResourceLibraryCode = fdr.resourceLibraryCode;
              }
            });
          });
        } else if (item.id === 'docfile') {
          if (this.fileListFile.length === 0) {
            await this.getResourceListFun(false, 'docfile');
          }
          this.fileListFile.forEach(item => {
            item.folderList.forEach(fdr => {
              // fdr.isOpen = fdr.id.toString() === folderId;
              if (fdr.isOpen) {
                this.list = fdr.list;
                this.imgList = fdr.imgList;
                this.selectFolderId = fdr.id;
                this.selectResourceLibraryCode = fdr.resourceLibraryCode;
              }
            });
          });
        } else if (item.id === 'icon') {
          if (this.fileListIcon.length === 0) {
            await this.getResourceListFun(false, 'icon')
          }
          this.fileListIcon.forEach(item => {
            item.folderList.forEach(fdr => {
              if (fdr.isOpen) {
                this.list = fdr.list;
                this.imgList = fdr.imgList;
                this.selectFolderId = fdr.id;
                this.selectResourceLibraryCode = fdr.resourceLibraryCode;
              }
            })
          })
        } else if (item.id === 'indicator') {
          // if (this.fileListIndicator.length === 0){
          //   this.fileListIndicator = this.getIndicatorList()
          // }
          // console.log('@@@@@filemethod')
          // this.fileListIndicator.forEach(el => {
          //   this.list = el.list
          //   this.imgList = el.imgList
          // })
          // console.log(this.list,this.imgList,'????')
          this.getIndicatorList()
        }
        if (item.id !== 'picture') {
          this.fileListImg.forEach(item => {
            item.folderList.forEach(fdr => {
              fdr.isOpen = false;
            });
          });
        }
        if (item.id !== 'video') {
          this.fileListVideo.forEach(item => {
            item.folderList.forEach(fdr => {
              fdr.isOpen = false;
            });
          });
        }
        if (item.id !== 'docfile') {
          this.fileListFile.forEach(item => {
            item.folderList.forEach(fdr => {
              fdr.isOpen = false;
            });
          });
        }
        if (item.id !== 'icon') {
          this.fileListIcon.forEach(item => {
            item.folderList.forEach(fdr => {
              fdr.isOpen = false;
            });
          });
        }
        if (item.id === 'picture' || item.id === 'video' || item.id === 'docfile' || item.id === 'icon') {
          this.setFolderState(false, item.id);
        }
        item.isOpen = true;
      }
      this.selectActive = item;
      localStorage.setItem('resourcesActiveItemId', this.selectActive.id);
    },

    async getResourceListFun (isRefresh, resourceType) {
      this.loading = true;
      const res = await getResourceList({ resourceType: resourceType });
      this.loading = false;
      if (res.success) {
        if (res.data.length === 0) {
          this.$message.error('获取资源文件为空，请联系管理员');
        }
        if (resourceType === 'picture') {
          this.fileListImg = this.getFileList(res.data, isRefresh);
        } else if (resourceType === 'video') {
          this.fileListVideo = this.getFileList(res.data, isRefresh);
        } else if (resourceType === 'docfile') {
          this.fileListFile = this.getFileList(res.data, isRefresh);
        } else if (resourceType === 'icon') {
          this.fileListIcon = this.getFileList(res.data, isRefresh)
        }
        this.selectFolderOptions = [];
        this.selectFolderOptions = this.getSelectFolderOptions(res.data);
        this.setFolderState(isRefresh, resourceType);
      }
    },
    // 刷新后 设置文件夹打开状态
    setFolderState (isRefresh, resourceType) {
      if (resourceType === 'picture') {
        this.setSelectFolder(this.fileListImg, isRefresh, resourceType);
      } else if (resourceType === 'video') {
        this.setSelectFolder(this.fileListVideo, isRefresh, resourceType);
      } else if (resourceType === 'docfile') {
        this.setSelectFolder(this.fileListFile, isRefresh, resourceType);
      } else if (resourceType === 'icon') {
        this.setSelectFolder(this.fileListIcon, isRefresh, resourceType);
      }
    },

    // 默认选中
    setSelectFolder (list, isRefresh, resourceType) {
      list.forEach(item => {
        if (item.name === '私有库' || (resourceType === 'docfile' && item.name === '公共库')) {
          item.folderList.forEach((fdr, index) => {
            if (!isRefresh) {
              if (index === 0) {
                this.slelectFolder(fdr, resourceType, isRefresh);
              }
            } else {
              if (fdr.isOpen) {
                this.slelectFolder(fdr, resourceType, isRefresh);
              }
            }
          });
        } else {
          if (isRefresh) {
            item.folderList.forEach((fdr) => {
              if (fdr.isOpen) {
                this.slelectFolder(fdr, resourceType, isRefresh);
              }
            });
          }
        }
      });
    },

    // 数据映射
    getFileList (list, isRefresh) {
      const resourceLibrary = localStorage.getItem('resourceSelectFolderLibrary');
      const folderId = localStorage.getItem('resourcesSelectFolderId');
      list.forEach((item, i, ary) => {
        item.id = item.name;
        if (isRefresh) {
          item.isOpen = item.name === resourceLibrary;
        } else {
          if (ary.length === 1) {
            item.isOpen = item.name === '公共库';
          } else {
            item.isOpen = item.name === '私有库';
          }
        }
        item.folderList.forEach(folder => {
          folder.name = folder.resourceFolder;
          if (isRefresh) {
            folder.isOpen = folder.id.toString() === folderId;
          } else {
            folder.isOpen = false;
          }
          folder.isSelect = 'no';
          folder.imgList = folder.resourceList.map(it => {
            return replaceUrl(process.env.VUE_APP_SERVER_URL + it.resourceUrl);
          });
          folder.resourceList.forEach(img => {
            img.name = img.resourceName;
            img.isSelect = false;
            img.isShow = true;
            img.url = replaceUrl(process.env.VUE_APP_SERVER_URL + img.resourceUrl);
            // 判断是否有预览缩略图
            if (img.ecryptUrl) {
              // 走图片服务器
              // if (process.env.VUE_APP_IMG_SERVER_URL) {
              //   img.ecryptUrl = process.env.VUE_APP_IMG_SERVER_URL + img.ecryptUrl + '?width=268&height=170';
              // } else {
              //   img.ecryptUrl = process.env.VUE_APP_SERVER_URL + img.ecryptUrl + '?width=268&height=170';
              // }
              img.ecryptUrl = folder.resourceType === 'icon' ? replaceUrl(img.resourceUrl) : replaceUrl(img.ecryptUrl + '?width=268&height=170')
            } else {
              img.ecryptUrl = img.url;
            }
          });
          folder.list = folder.resourceList;
        });
      });
      return list;
    },
    // 获取上传文件夹列表
    getSelectFolderOptions (list) {
      const tempOptions = [];
      list.forEach(item => {
        const tempItem = {
          value: item.resourceLibraryCode,
          label: item.name,
          // idr: item.id,
          disabled: this.isAdmin ? false : item.resourceLibraryCode === 'system',
          children: []
        };
        item.folderList.forEach(folder => {
          tempItem.children.push({
            value: folder.resourceFolder,
            label: folder.resourceFolder
            // idr: folder.id
          });
        });
        tempOptions.push(tempItem);
      });
      return tempOptions;
    },
    // 新建文件夹
    addFolder (type) {
      this.newFolderInfo.folder = type === 'docfile' ? 'personal' : 'private';
      this.newFolderInfo.name = '';
      this.selectFolderType = type;
      this.dialogVisibleAddFolder = true;
    },
    // 提交新建文件夹
    submitNewFolder () {
      const systemSet = new Set();
      const personalSet = new Set();
      const privateSet = new Set();
      if (this.selectFolderOptions.length > 1) {
        const systemChildren = this.selectFolderOptions.filter(item => item.value === 'system')[0]?.children;
        const personalChildren = this.selectFolderOptions.filter(item => item.value === 'personal')[0]?.children;
        const privateChildren = this.selectFolderOptions.filter(item => item.value === 'private')[0]?.children;
        systemChildren && systemChildren.forEach((item) => {
          systemSet.add(item.value);
        });
        personalChildren && personalChildren.forEach((item) => {
          personalSet.add(item.value);
        });
        privateChildren && privateChildren.forEach((item) => {
          privateSet.add(item.value);
        });
        const systemSetLength = systemSet.size;
        const personalSetLength = personalSet.size;
        const privateSetLength = privateSet.size;
        if (this.newFolderInfo.folder === 'system') {
          systemSet.add(this.newFolderInfo.name);
          if (systemSetLength !== systemSet.size) {
          } else {
            this.$message({
              text: '文件夹名称已存在',
              type: 'warn'
            });
            return;
          };
        } else if (this.newFolderInfo.folder === 'personal') {
          personalSet.add(this.newFolderInfo.name);
          if (personalSetLength !== personalSet.size) {
          } else {
            this.$message({
              text: '文件夹名称已存在',
              type: 'warn'
            });
            return;
          }
        } else if (this.newFolderInfo.folder === 'private') {
          privateSet.add(this.newFolderInfo.name)
          if (privateSetLength !== privateSet.size) {
          } else {
            this.$message({
              text: '文件夹名称已存在',
              type: 'warn'
            });
            return;
          }
        }
      } else if (this.selectFolderOptions.length === 1) {
        const personalChildren = this.selectFolderOptions.filter(item => item.value === 'personal')[0].children;
        personalChildren.forEach((item) => {
          personalSet.add(item.value);
        });
        const personalSetLength = personalSet.size;
        if (this.newFolderInfo.folder === 'personal') {
          personalSet.add(this.newFolderInfo.name);
          if (personalSetLength !== personalSet.size) {
          } else {
            this.$message({
              text: '文件夹名称已存在',
              type: 'warn'
            });
            return;
          }
        }
      }
      if (this.newFolderInfo.folder === '') {
        this.$message({
          text: '所属文件夹不能为空',
          type: 'warn'
        });
        return;
      }
      if (this.newFolderInfo.name === '') {
        this.$message({
          text: '文件夹名称不能为空',
          type: 'warn'
        });
        return;
      }
      this.loading = true;
      ResourceCreateFolder({
        userId: this.userId,
        resourceType: this.selectFolderType,
        resourceLibrary: this.getResourceLibrary(this.newFolderInfo.folder, this.selectFolderType),
        resourceLibraryCode: this.newFolderInfo.folder,
        resourceFolder: this.newFolderInfo.name
      }).then(res => {
        this.loading = false;
        if (res.success) {
          this.getResourceListFun(true, this.selectFolderType);
          this.dialogVisibleAddFolder = false;
        }
      });
    },
    // 此处后端需要 库文件夹名字，无法直接获取
    getResourceLibrary (strCode, type) {
      let tempName = '';
      if (type === 'picture') {
        this.fileListImg.forEach(item => {
          if (item.resourceLibraryCode === strCode) {
            tempName = item.name;
          }
        });
      } else if (type === 'video') {
        this.fileListVideo.forEach(item => {
          if (item.resourceLibraryCode === strCode) {
            tempName = item.name;
          }
        });
      } else if (type === 'docfile') {
        this.fileListFile.forEach(item => {
          if (item.resourceLibraryCode === strCode) {
            tempName = item.name;
          }
        });
      } else if (type === 'icon') {
        this.fileListIcon.forEach(item => {
          if (item.resourceLibraryCode === strCode) {
            tempName = item.name;
          }
        })
      }
      return tempName;
    },

    slelectFolder (item, type, isRefresh = false) {
      this.resourcesList.forEach(resitem => {
        if (resitem.id === type) {
          this.selectActive = resitem;
        }
      });
      if (!isRefresh) {
        item.isOpen = true;
        this.fileListImg.forEach(folder => {
          folder.folderList.forEach(it => {
            if (it.id !== item.id) {
              it.isOpen = false;
            }
          });
        });
        this.fileListVideo.forEach(folder => {
          folder.folderList.forEach(it => {
            if (it.id !== item.id) {
              it.isOpen = false;
            }
          });
        });
        this.fileListFile.forEach(folder => {
          folder.folderList.forEach(it => {
            if (it.id !== item.id) {
              it.isOpen = false;
            }
          });
        });
        this.fileListIcon.forEach(folder => {
          folder.folderList.forEach(it => {
            if (it.id !== item.id) {
              it.isOpen = false;
            }
          });
        });
        this.selectFolderOptions = [];
        if (type === 'video') {
          this.selectFolderOptions = this.getSelectFolderOptions(this.fileListVideo);
        } else if (type === 'picture') {
          this.selectFolderOptions = this.getSelectFolderOptions(this.fileListImg);
        } else if (type === 'docfile') {
          this.selectFolderOptions = this.getSelectFolderOptions(this.fileListFile);
        } else if (type === 'icon') {
          this.selectFolderOptions = this.getSelectFolderOptions(this.fileListIcon);
        }
      }
      this.list = item.list;
      this.imgList = item.imgList;
      this.selectFolderId = item.id;
      this.selectResourceLibraryCode = item.resourceLibraryCode;
      if (!isRefresh) {
        localStorage.setItem('resourcesSelectFolderId', item.id);
        localStorage.setItem('resourceSelectFolderLibrary', item.resourceLibrary);
      }
    },
    // 删除文件夹
    delFolder (item, type) {
      this.delFolderList = [item.id];
      this.selectFolderType = type;
      this.dialogVisibleDel = true;
    },
    // 确定删除
    async delFiles () {
      this.loading = true;
      const res = await delResourceFolder(this.delFolderList);
      this.loading = false;
      // 删除成功
      if (res.success) {
        const folderId = localStorage.getItem('resourcesSelectFolderId');
        if (this.delFolderList[0].toString() === folderId) {
          this.getResourceListFun(false, this.selectFolderType);
        } else {
          this.getResourceListFun(true, this.selectFolderType);
        }
        this.$message.success('操作成功');
        this.dialogVisibleDel = false;
      }
    },
    // 修改文件夹名称
    editName (item, folderIndex, index, folderType) {
      this.editFolderInfo.resourceLibraryCode = item.resourceLibraryCode
      this.editFolderInfo.originName = item.name;
      this.editFolderInfo.resourceId = item.id;
      this.editFolderInfo.newName = '';
      this.editFolderInfo.folderType = folderType;
      this.dialogVisibleEditName = true;
      this.editFolderInfo.folderIndex = folderIndex;
      this.editFolderInfo.index = index;
    },
    // 修改文件夹名称
    async submitNewName () {
      // 修改文件夹名称去重
      const systemSet = new Set();
      const personalSet = new Set();
      const privateSet = new Set();
      const systemChildren = this.selectFolderOptions.filter(item => item.value === 'system')[0]?.children;
      const personalChildren = this.selectFolderOptions.filter(item => item.value === 'personal')[0]?.children;
      const privateChildren = this.selectFolderOptions.filter(item => item.value === 'private')[0]?.children;
      systemChildren && systemChildren.forEach((item) => {
        systemSet.add(item.value);
      });
      personalChildren && personalChildren.forEach((item) => {
        personalSet.add(item.value);
      });
      privateChildren && privateChildren.forEach((item) => {
        privateSet.add(item.value);
      });
      const systemSetLength = systemSet.size;
      const personalSetLength = personalSet.size;
      const privateSetLength = privateSet.size;
      if (this.editFolderInfo.resourceLibraryCode === 'system') {
        systemSet.add(this.editFolderInfo.newName);
        if (systemSetLength !== systemSet.size) {
        } else {
          this.$message({
            text: '文件夹名称已存在',
            type: 'warn'
          });
          return;
        };
      } else if (this.editFolderInfo.resourceLibraryCode === 'personal') {
        personalSet.add(this.editFolderInfo.newName);
        if (personalSetLength !== personalSet.size) {
        } else {
          this.$message({
            text: '文件夹名称已存在',
            type: 'warn'
          });
          return;
        };
      } else if (this.editFolderInfo.resourceLibraryCode === 'private') {
        privateSet.add(this.editFolderInfo.newName)
        if (privateSetLength !== privateSet.size) {
        } else {
          this.$message({
            text: '文件夹名称已存在',
            type: 'warn'
          });
          return;
        }
      }
      if (this.editFolderInfo.newName === '') {
        this.$message({
          text: '文件夹名称不能为空',
          type: 'warn'
        });
        return;
      }
      const param = {
        id: this.editFolderInfo.resourceId
      }
      const data = {
        resourceFolder: this.editFolderInfo.newName
      }
      this.loading = true;
      const res = await editResourceFolderName(data, param);
      this.loading = false;
      if (res.success) {
        if (this.editFolderInfo.folderType === 'img') {
          this.fileListImg[this.editFolderInfo.index].folderList[this.editFolderInfo.folderIndex].name = this.editFolderInfo.newName;
        } else if (this.editFolderInfo.folderType === 'video') {
          this.fileListVideo[this.editFolderInfo.index].folderList[this.editFolderInfo.folderIndex].name = this.editFolderInfo.newName;
        } else if (this.editFolderInfo.folderType === 'docfile') {
          this.fileListFile[this.editFolderInfo.index].folderList[this.editFolderInfo.folderIndex].name = this.editFolderInfo.newName;
        } else if (this.editFolderInfo.folderType === 'icon') {
          this.fileListIcon[this.editFolderInfo.index].folderList[this.editFolderInfo.folderIndex].name = this.editFolderInfo.newName;
        }
        this.dialogVisibleEditName = false;
      }
    },
    // 获取指标库列表
    async getIndicatorList () {
      this.list = []
      this.imgList = []
      getIndicators({ userId: this.userId }).then(res => {
        res.data.forEach((el) => {
          this.list.push({
            isShow: true,
            createAt: el.createdAt,
            name: el.name,
            isSelect: false,
            ecryptUrl: el.config.backgroundImage,
            backgroundColor: el.config.backgroundColor,
            componentId: el.componentId,
            id: el.id
          })
          this.imgList.push(el.config.backgroundImage)
        })
      })
      // await this.getResourceListFun(false,'indicator')
      // let list = JSON.parse(JSON.stringify(mockData))
      // let imgList = list.map(it => {
      //   return replaceUrl(process.env.VUE_APP_SERVER_URL + it.resourceUrl);
      // })
      // list.forEach(img => {
      //   img.name = img.resourceName
      //   img.isSelect = false
      //   img.isShow = true
      //   img.createAt = '2023-08-01'
      //   img.url = replaceUrl(process.env.VUE_APP_SERVER_URL + img.resourceUrl);
      //   img.ecryptUrl = img.url
      // })
      // return [{list,imgList}]
    }
  }
}
</script>

<style lang="scss" scoped>
.my-resources {
  // display: flex;
  // position: relative;
  // height: calc(100vh - 300px);
  // color: #000;
  .resources-list {
    // width: 240px;
    font-size: 14px;
    cursor: pointer;
    margin-top: 8px;
    .link-title {
      display: flex;
      align-items: center;
      line-height: 40px;
      padding-left: 24px;
      // color: #898A8C;
      // color: rgba(255, 255, 255, 0.7);
      transition: color 0.2s;
      text-decoration: none;
      position: relative;
      .hz-icon {
        height: 20px;
        width: 20px;
        margin-right: 4px;
        color: rgba(21, 22, 24, 0.72);
      }
      .el-icon-arrow-right, .el-icon-arrow-down {
        color: var(--seatom-type-800);
      }
      .block-16 {
        width: 16px;
        height: 16px;
      }
      &:hover {
        // background: linear-gradient(0deg, #1F2430, #1F2430);
        background: var(--seatom-mono-a100);
      }
      &.active {
        // color: #fff;
        // background: linear-gradient(0deg, #1F2430, #1F2430);
        background-repeat: round;
        background: var(--seatom-primary-a100);
        color: var(--seatom-primary-900);
        font-weight: 600;
      }
      &.select {
        background-repeat: round;
        background: var(--seatom-primary-a100);
        color: var(--seatom-primary-900);
        font-weight: 600;
        &::after {
          content: "";
          display: block;
          height: 40px;
          width: 4px;
          background: var(--seatom-primary-900);
          position: absolute;
          left: 0;
        }
      }
    }
    .folder-concent {
      max-height: 0px;
      overflow: hidden;
      transition: max-height .3s;
      justify-content: space-between;
      .folder-name {
        cursor: pointer;
        margin-left: 4px;
        display: flex;
        align-items: center;
        font-size: 14px;
        width: 186px;
        .folder-name-max-width {
          max-width: 108px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .file-total {
        display: block;
        font-weight: bold;
        font-size: 12px;
        // color: rgba(255, 255, 255, 0.7);
        margin-right: 28px;
      }
      .del-folder-icon {
        display: none;
        margin-right: 12px;
      }
      &:hover {
        .del-folder-icon {
          display: block;
        }
        .file-total {
          display: none;
        }
      }
      .file-total-block {
        display: block !important;
      }
    }
    .folder-concent-Open {
      max-height: 900px;
    }
    .padding-left-38 {
      padding-left: 38px;
    }
    .padding-left-54 {
      padding-left: 54px;
    }
    .margin-right-4 {
      margin-right: 4px;
    }
  }

  .resources-tabls {
    flex: 1;
    height: 100%;
    .tabls-content {
      height: 100%;
      .hint {
        line-height: 120px;
        text-align: center;
        color: #ffffff88;
        font-size: 18px;
      }
    }
  }
  .arrow-down {
    transition: transform .3s;
  }
  .arrow-rotate {
    transform: rotate(-90deg);
    transition: transform .3s;
  }
  .icon-placeholder {
    display: block;
    width: 16px;
    margin-right: 4px;
  }
  ::v-deep .dialog-content-del-info {
    padding: 0 16px;
  }
  ::v-deep .el-button {
    padding: 6px 12px;
  }
  .project-manage-resources {
    min-width: 288px;
    max-width: 288px;
    position: sticky;
    top: 120px;
    font-size: 14px;
    overflow-y: auto;
    height: calc(100vh - 120px);
    // background-color: pink;
    // color: #fff;
    background-color: var(--seatom-background-100);
    color: var(--seatom-type-800);
    box-shadow: var(--seatom-container-c200);
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
    }

    // &::-webkit-scrollbar-thumb{
    //   background: #434b55;
    //   border: 1px solid #434b55;
    // }

    &-title {
      position: sticky;
      top: 0;
      background: #171b22;
      z-index: 10;
      &-header {
        display: flex;
        justify-content: space-between;
        padding-right: 30px;
        height: 60px;
        padding-left: 24px;
        // border-bottom: 1px solid #27343e;
        align-items: center;
      }

      &-bottom {
        transition: color .2s;
        cursor: pointer;
      }
    }
  }
}
</style>
