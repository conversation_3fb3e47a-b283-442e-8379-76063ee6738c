<template>
  <div class="BaseRecorder">
    <div class="BaseRecorder-record">
      <el-button @click="startRecorder()">开始录音</el-button>
      <el-button @click="resumeRecorder()">继续录音</el-button>
      <el-button @click="pauseRecorder()">暂停录音</el-button>
      <el-button @click="stopRecorder()">结束录音</el-button>
    </div>
    <div class="BaseRecorder-play">
      <el-button @click="playRecorder()">录音播放</el-button>
      <!-- <el-button @click="pausePlayRecorder()">暂停录音播放</el-button>
      <el-button @click="resumePlayRecorder()">恢复录音播放</el-button>
      <el-button @click="stopPlayRecorder()">停止录音播放</el-button> -->
    </div>
    <div class="BaseRecorder-download">
      <el-button @click="downWAV()">转换WAV</el-button>
      <el-button @click="downPCM()">下载PCM</el-button>
    </div>
    <div class="BaseRecorder-destroy">
      <el-button type="error" @click="destroyRecorder()">销毁录音</el-button>
    </div>
    <div class="BaseRecorder-text">
      <div style="font-size: 20px; color: red">
        {{recordText}}
      </div>
    </div>
  </div>
</template>

<script>
import Recorder from 'js-audio-recorder'
import { speechText } from '@/api/common'
import { MIN_RECORD_VOL } from '@/common/constants'
export default {
  name: 'record',
  data () {
    return {
      recorder: null,
      recordText: '',
      tap: false
    }
  },
  mounted () {
    this.init();
  },
  methods: {
    init () {
      this.recorder = new Recorder({
        // 采样位数，支持 8 或 16，默认是16
        sampleBits: 16,
        // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值
        sampleRate: 48000,
        // 声道，支持 1 或 2， 默认是1
        numChannels: 1,
        // 是否边录边转换，默认是false
        compiling: false
      })
      Recorder.getPermission().then(() => {
        // console.log('给权限了');
      }, (error) => {
        // console.log(`${error.name} : ${error.message}`);
      });
      const startTime = null
      // this.recorder.onprogress = (params) => {
      //   // console.log('录音时长(秒)', params.duration);
      //   // console.log('录音大小(字节)', params.fileSize);
      //   console.log('录音音量百分比(%)', params.vol);
      //   if (params.vol > MIN_RECORD_VOL) {
      //     startTime = +new Date();
      //   }
      //   if (startTime && params.vol < MIN_RECORD_VOL && params.duration > 3) {
      //     const end = +new Date();
      //     if (end - startTime > 1000 * 3) {
      //       console.log('停止')
      //       this.stopRecorder();
      //       this.downWAV();
      //     }
      //   }
      // }
    },
    // 开始录音
    startRecorder () {
      this.recorder.start().then(
        () => {
          // this.drawRecord()
          // console.log('开始录音')
        },
        error => {
          // 出错了
          // console.log(`${error.name} : ${error.message}`)
        }
      )
    },
    // 继续录音
    resumeRecorder () {
      this.recorder.resume()
    },
    // 暂停录音
    pauseRecorder () {
      this.recorder.pause()
    },
    // 结束录音
    stopRecorder () {
      this.recorder.stop()
    },
    // 录音播放
    playRecorder () {
      this.recorder.play()
    },
    // 销毁录音
    destroyRecorder () {
      this.recorder.destroy().then(function () {
        this.recorder = null
      })
    },
    downPCM () {
      // 这里传参进去的时文件名
      this.recorder.downloadPCM('新文件')
    },
    async downWAV () {
      const file = this.recorder.getWAVBlob();
      const size = file.size;
      const baseStr = await this.blobToBase64(file)
      const base64 = baseStr.split(',')[1]
      const data = {
        audio_base64: base64
      }
      const ccc = await speechText(data)
      this.recordText = ccc.result.text
      // console.log('ccc', ccc)
      // console.log('file:', file, 'size:', size)
    },
    blobToBase64 (blob) {
      return new Promise((res, rej) => {
        const fileReader = new FileReader();
        fileReader.onload = (e) => {
          // console.log(e)
          res(e.target.result)
        }
        fileReader.readAsDataURL(blob)
        fileReader.onerror = () => {
          rej(new Error('ccc'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.BaseRecorder {
  & > div {
    margin: 20px 0;
  }
}
</style>
